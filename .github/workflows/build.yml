name: Build & Testing
on:
  workflow_dispatch:
    inputs:
      env:
        type: choice
        description: Select the environment
        default: "Preview"
        options:
          - Preview
          - Beta
          - Prod

  pull_request:
    types:
      - opened
      - synchronize
      - reopened

jobs:
  vendor-prepare:
    name: Composer Install
    runs-on: ubuntu-24.04
    env:
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}

    outputs:
      test_unit_supported: ${{ steps.check_testunit.outputs.supported }}
      env: ${{ steps.env.outputs.name }}

    steps:
      - name: Setup Twingate
        uses: maherapi/github-action@enhanced
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }}
          enable-verification: "true"
          expected-outbound-ips: ${{ secrets.TWINGATE_OUTBOUND_IPS }}
          verification-timeout-seconds: ${{ vars.ACTION_WAIT_AFTER_TWINGATE }}

      - uses: actions/checkout@v4
        # if: steps.env.outputs.name == 'Preview'
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Resolve env name
        id: env
        run: |
          ENV_NAME="${{ github.event.inputs.env }}"
          echo "name=${ENV_NAME:-Preview}" >> $GITHUB_OUTPUT

      - name: Check if test unit supported in the repo
        # if: steps.env.outputs.name == 'Preview'
        id: check_testunit
        run: |
          if [ -f ./unit-test/ci-unit-test.sh ]; then
            echo "supported=true" >> $GITHUB_OUTPUT
          else
            echo "supported=false" >> $GITHUB_OUTPUT
          fi

      - name: Debug Outputs
        run: |
          echo "Test Unit Supported: ${{ steps.check_testunit.outputs.supported }}"
          echo "Environment: ${{ steps.env.outputs.name }}"

      - name: SETUP PHP VERSION
        if: steps.check_testunit.outputs.supported == 'true' && steps.env.outputs.name == 'Preview'
        # if: steps.env.outputs.name == 'Preview'
        uses: shivammathur/setup-php@c541c155eee45413f5b09a52248675b1a2575231
        with:
          php-version: "8.3"
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.jit=function, opcache.jit_buffer_size=128M, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1
          coverage: none

      - name: Build Vendor Folder
        if: steps.check_testunit.outputs.supported == 'true' && steps.env.outputs.name == 'Preview'
        # if: steps.env.outputs.name == 'Preview'
        run: |
          REDIS="redis-unit"
                  
          echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin

          if ! docker ps --format '{{.Names}}' | grep -Eq "^${REDIS}\$" ; then
            docker run --name $REDIS -p 6378:6379 -d redis
          fi

          composer config --global github-oauth.github.com ${GITHUB_TOKEN}
          composer config --global --auth http-basic.core-repo-package.salla.group token ${REPMAN_TOKEN}
          mv .env.example .env
          composer install

      - name: Cache Vendor Folder
        uses: actions/cache/save@v3
        if: steps.check_testunit.outputs.supported == 'true' && steps.env.outputs.name == 'Preview'
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          enableCrossOsArchive: true

  build:
    needs: vendor-prepare
    name: Docker Build

    runs-on: ubuntu-24.04

    timeout-minutes: 20

    env:
      BUILD_USER_SSH_PRIVATE_KEY: ${{ secrets.BUILD_USER_SSH_PRIVATE_KEY }}
      BUILD_USER_SSH_PUBLIC_KEY: ${{ secrets.BUILD_USER_SSH_PUBLIC_KEY }}
      BUILD_USER_SSH_KNOWN_HOSTS: ${{ secrets.BUILD_USER_SSH_KNOWN_HOSTS }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}

    steps:
      - name: Setup Twingate
        uses: maherapi/github-action@enhanced
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }}
          enable-verification: "true"
          expected-outbound-ips: ${{ secrets.TWINGATE_OUTBOUND_IPS }}
          verification-timeout-seconds: ${{ vars.ACTION_WAIT_AFTER_TWINGATE }}

      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          repository: "SallaApp/.CI"

      - name: Get Hash
        id: hash
        uses: ./utils/hash
        with:
          input_string: ${{ github.head_ref || github.ref_name }}

      - uses: rlespinasse/github-slug-action@v3.x
      - uses: FranzDiebold/github-env-vars-action@v2
      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive
          ref: "${{ env.CI_ACTION_REF_NAME }}"

      - name: SETUP PHP VERSION
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.3"
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.jit=function, opcache.jit_buffer_size=128M, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1
          coverage: none

      - name: Configure AWS credentials - Dev
        uses: aws-actions/configure-aws-credentials@v1
        if: needs.vendor-prepare.outputs.env == 'Preview'
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure AWS credentials - Prod
        uses: aws-actions/configure-aws-credentials@v1
        if: needs.vendor-prepare.outputs.env == 'Beta' || needs.vendor-prepare.outputs.env == 'Prod'
        with:
          aws-access-key-id: ${{ secrets.ECR_ACCESS_KEY_PRD }}
          aws-secret-access-key: ${{ secrets.ECR_SECRET_KEY_PRD }}
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Retrieve cached vendor folder
        if: needs.vendor-prepare.outputs.test_unit_supported == 'true' && needs.vendor-prepare.outputs.env == 'Preview'
        uses: actions/cache@v3
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          fail-on-cache-miss: true

      - name: get last release number
        id: release
        uses: pozetroninc/github-action-get-latest-release@master
        with:
          repository: ${{ github.repository }}
          token: ${{ secrets.GITHUB_TOKEN }}
          excludes: prerelease, draft

      # Here we set the ecr_repo, image_name and tag depending on which environment trigger this job
      # For production we have release event, for dev we have pull_request event and for beta_version we have repository_dispatch
      # for build_beta we set the tag to be the run_number which is incremental unique number
      - name: Set env
        run: |
          if [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Prod' ]]; then
            echo "TAG_NAME=${{ env.CI_ACTION_REF_NAME }}" >> "$GITHUB_ENV"
          elif [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Preview' ]]; then
            echo "TAG_NAME=stage-${{ env.CI_ACTION_REF_NAME_SLUG }}" >> "$GITHUB_ENV"
          elif [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Beta' ]]; then
            echo "TAG_NAME=${{ steps.release.outputs.release }}-beta-${{ github.run_number }}" >> "$GITHUB_ENV"
          fi

          source $GITHUB_ENV
          if [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Prod' || '${{ needs.vendor-prepare.outputs.env }}' == 'Beta' ]]; then
            echo "IMAGE_NAME= ${{ secrets.ECR_REGISTRY_PRD }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):$TAG_NAME" >> "$GITHUB_ENV"
          elif [[ $GITHUB_EVENT_NAME == 'pull_request' ]]; then
            echo "IMAGE_NAME= ${{ secrets.ECR_REGISTRY }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):$TAG_NAME" >> "$GITHUB_ENV"
          fi

      - name: Send Telegram Notification Docker Build
        if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown

          message: |
            ⏳  🐳  Start building Docker image for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Composer Install
        run: |
          echo "php version"
          composer config --global github-oauth.github.com ${GITHUB_TOKEN}
          composer config --global --auth http-basic.core-repo-package.salla.group token ${REPMAN_TOKEN}
          mv .env.example .env
          cat composer.json
          composer install --prefer-dist --no-interaction
          php artisan key:generate --ansi

      - name: Get Latest Release
        id: composer_latest_release
        uses: actions/github-script@v6
        continue-on-error: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPOSITORY: ${{ github.repository }}
          OWNER: ${{ env.GITHUB_REPOSITORY_OWNER_PART }}
          REPO: ${{ env.GITHUB_REPOSITORY_NAME_PART }}
          EXCLUDES: ${{ inputs.excludes || '' }}
        with:
          script: |
            const token = process.env.GITHUB_TOKEN;
            const repository = process.env.REPOSITORY;
            let owner = process.env.OWNER;
            let repo = process.env.REPO;
            let excludes = process.env.EXCLUDES.trim().split(",");

            if (repository) {
                [owner, repo] = repository.split("/");
            }

            const releases = await github.rest.repos.listReleases({
                owner: owner,
                repo: repo
            });

            let filteredReleases = releases.data;

            if (excludes.includes('prerelease')) {
                filteredReleases = filteredReleases.filter(x => !x.prerelease);
            }
            if (excludes.includes('draft')) {
                filteredReleases = filteredReleases.filter(x => !x.draft);
            }

            const rel = filteredReleases[${{ needs.vendor-prepare.outputs.env == 'Preview' && '0' || '1' }}]
            if (rel) {
                core.setOutput('release', rel.tag_name);
                core.setOutput('id', String(rel.id));
                core.setOutput('description', String(rel.body || ''));
            } else {
                core.setFailed("No valid releases");
            }

      - name: Get Latest Release Run Id
        id: get_latest_release_run_id
        continue-on-error: true
        run: |
          # Fetch the latest workflow run for the specified branch and workflow name
          run_id=$(gh api -X GET "repos/${{ github.repository }}/actions/runs?branch=${{ steps.composer_latest_release.outputs.release }}" \
            --jq '.workflow_runs | map(select(.name == "${{ github.workflow }}")) | .[0].id')

          # Print the run ID for debugging
          echo "Latest Release run ID: $run_id"

          # Set the run ID as an output for use in subsequent steps
          echo "release_run_id=$run_id" >> $GITHUB_OUTPUT

      - name: Downlaod and Extract Latest Release composer.lock
        if: steps.get_latest_release_run_id.outputs.release_run_id != ''
        continue-on-error: true
        run: |
          gh run download ${{ steps.get_latest_release_run_id.outputs.release_run_id }} \
          --name meta-files \
          --dir ./downloaded-artifact
          ls -la downloaded-artifact || echo "No meta-files directory found"
          if [ -f downloaded-artifact/composer.lock ]; then
          mv downloaded-artifact/composer.lock composer.lock.latest
          echo "files_exist=true" >> $GITHUB_ENV
          else
          echo "No meta-files.tar.gz found"
          echo "files_exist=false" >> $GITHUB_ENV
          fi

      - name: Get Updated Packages Versions
        id: get_updated_packages
        uses: actions/github-script@v4
        if: steps.get_latest_release_run_id.outputs.release_run_id != ''
        continue-on-error: true
        with:
          script: |
            const fs = require('fs');

            const oldLockPath = 'composer.lock.latest';
            const newLockPath = 'composer.lock';

            let oldLock, newLock;

            try {
                const oldData = fs.readFileSync(oldLockPath, 'utf8');
                oldLock = JSON.parse(oldData);
            } catch (err) {
                console.error(`Error reading or parsing ${oldLockPath}:`, err);
            }

            try {
                const newData = fs.readFileSync(newLockPath, 'utf8');
                newLock = JSON.parse(newData);
            } catch (err) {
                console.error(`Error reading or parsing ${newLockPath}:`, err);
                process.exit(1);
            }

            const oldPackages = {};
            const newPackages = {};

            oldLock.packages.forEach(pkg => {
                oldPackages[pkg.name] = {
                  version: pkg.version,
                  sourceLink: pkg.support?.source
                }
            });

            newLock.packages.forEach(pkg => {
                newPackages[pkg.name] = {
                  version: pkg.version,
                  sourceLink: pkg.support?.source
                }
            });

            const sallaChanges = [];
            const otherChanges = [];

            for (const pkgName in newPackages) {
                const oldVersion = oldPackages[pkgName]?.version, newVersion = newPackages[pkgName]?.version
                if (oldVersion && oldVersion !== newVersion) {
                    const change = {
                        package: pkgName,
                        oldVersion,
                        newVersion,
                        oldSourceLink: oldPackages[pkgName]?.sourceLink || '',
                        newSourceLink: newPackages[pkgName]?.sourceLink || '',
                    };
                    if (pkgName.startsWith('salla/')) {
                        sallaChanges.push(change);
                    } else {
                        otherChanges.push(change);
                    }
                }
            }

            // Format the changes into a markdown string
            let releaseNotes = '## Package Version Changes\n\n';
            if (sallaChanges.length > 0) {
                releaseNotes += '### Salla Packages\n';
                sallaChanges.forEach(change => {
                    releaseNotes += `- **${change.package}**: [${change.oldVersion}](${change.oldSourceLink}) → [${change.newVersion}](${change.newSourceLink})\n`;
                });
                releaseNotes += '\n';
            }
            if (otherChanges.length > 0) {
                releaseNotes += '### Other Packages\n';
                otherChanges.forEach(change => {
                    releaseNotes += `- **${change.package}**: [${change.oldVersion}](${change.oldSourceLink}) → [${change.newVersion}](${change.newSourceLink})\n`;
                });
            }

            // Set the release notes as an output
            core.setOutput('release_notes', releaseNotes);
            console.log(releaseNotes);

            if(otherChanges.length < 1 && sallaChanges.length < 1) {
              releaseNotes += `No changes detected`;
            }
            return releaseNotes;

      - name: Update Release Notes
        uses: actions/github-script@v6
        continue-on-error: true
        if: steps.get_updated_packages.outputs.release_notes != '' && (needs.vendor-prepare.outputs.env == 'Prod')
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const releaseNotes = `${{ steps.get_updated_packages.outputs.release_notes }}`;

            // Get the latest release
            const { data: releases } = await github.rest.repos.listReleases({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 1
            });

            const latestRelease = releases.find(release => release.tag_name === '${{ steps.release.outputs.release }}');

            // Update the release notes
            await github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: latestRelease.id,
              body: latestRelease.body + '\n\n' + releaseNotes
            });

      - name: Add composer.lock to artifacts
        continue-on-error: true
        if: needs.vendor-prepare.outputs.env == 'Prod'
        uses: actions/upload-artifact@v4
        with:
          name: meta-files
          path: composer.lock

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Build Docker image
        run: |
          # prefix is "${PROJECT}" if production, else if beta "${PROJECT}/beta/BRANCH_HASH"

          PROJECT=$(jq -r '.projects.${{ env.GITHUB_REPOSITORY_NAME_PART_SLUG_CS }}' assets.json)
          RELEASE_TAG=${{ env.TAG_NAME }}
          HASH_NAME="${{ steps.hash.outputs.hash }}"
          if [ ${{ needs.vendor-prepare.outputs.env }} == 'Beta' ]; then
            ASSET_URL="${{ vars.ASSETS_BETA_HOST }}/${{ vars.ASSETS_BETA_PATH }}/${PROJECT}/${HASH_NAME}"
          elif [ ${{ needs.vendor-prepare.outputs.env }} == 'Prod' ]; then
            ASSET_URL="${{ vars.ASSETS_PROD_HOST }}/${{ vars.ASSETS_PROD_PATH }}/${PROJECT}"
          elif [ ${{ needs.vendor-prepare.outputs.env }} == 'Preview' ]; then
            ASSET_URL="${{ vars.ASSETS_DEV_HOST }}/${{ vars.ASSETS_DEV_PATH }}/${PROJECT}/${HASH_NAME}"
          fi

          echo  "$(date)" > storage/.commit_date
          echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin

          docker build \
              --platform linux/arm64 \
              --build-arg TARGETARCH=arm64 \
              --build-arg GITHUB_USERNAME=${GITHUB_USERNAME} \
              --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
              --build-arg BUILD_USER_SSH_PRIVATE_KEY=${BUILD_USER_SSH_PRIVATE_KEY} \
              --build-arg BUILD_USER_SSH_PUBLIC_KEY=${BUILD_USER_SSH_PUBLIC_KEY} \
              --build-arg BUILD_USER_SSH_KNOWN_HOSTS=${BUILD_USER_SSH_KNOWN_HOSTS} \
              --build-arg REPMAN_TOKEN=${REPMAN_TOKEN} \
              --build-arg ASSET_URL=${ASSET_URL} \
              --build-arg RELEASE_TAG=${RELEASE_TAG} \
              -t $IMAGE_NAME . --file Dockerfile

      - name: Push Docker Image
        run: |
          docker push $IMAGE_NAME

      - name: Deploy App
        if: needs.vendor-prepare.outputs.env == 'Preview'
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: deploy.yaml
          ref: "${{  env.CI_ACTION_REF_NAME }}"

      - name: Push Docker Image as latest tag
        if: github.event_name == 'release'
        run: |
          docker tag $IMAGE_NAME ${{ secrets.ECR_REGISTRY_PRD }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):latest
          docker push ${{ secrets.ECR_REGISTRY_PRD }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):latest

      - name: Sent Telegram Notification Failer
        if: (failure()) && (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            ☹️💀  Uh Oh, something went wrong during building docker image for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Sent Telegram Notification Canceled
        if: (cancelled()) && (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            🫤  Hmmm, building docker image has been canceled for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Send Telegram Notification Docker Build
        if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            🔥  🐳  Finished the New Docker image for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: store beta image
        if: (needs.vendor-prepare.outputs.env == 'Beta')
        run: |
          curl "https://github-bot-magic.vercel.app/general-kv?command=set&key=${{ env.CI_REPOSITORY_SLUG }}-${{ env.CI_ACTION_REF_NAME_SLUG }}&value=${{ env.TAG_NAME }}" -H 'x-bot-salla-key: ${{ secrets.SALLA_GITHUB_WEBHOOK_LOCK_SECRET }}'

      - name: Create Sentry release
        continue-on-error: true
        if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
          SENTRY_URL: ${{ secrets.SENTRY_URL }}
        with:
          version: ${{ env.TAG_NAME }}
          environment: production
          ignore_missing: true

      - name: Unlock Merge if Prod
        if: always() && needs.vendor-prepare.outputs.env == 'Prod'
        run: |
          curl "https://github-bot-magic.vercel.app/kv?command=unlock" -H 'x-bot-salla-key: ${{ secrets.SALLA_GITHUB_WEBHOOK_LOCK_SECRET }}'

      - name: Send Telegram Release Command
        if: needs.vendor-prepare.outputs.env == 'Prod'
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            /release ${{ vars.RELEASE_PROJECT_NAME }} -n ${{ vars.RELEASE_NAMESPACE }} -v ${{ env.CI_ACTION_REF_NAME }} --all
      - name: Exec Telegram Release Command
        if: needs.vendor-prepare.outputs.env == 'Prod'
        run: |
          curl --location 'https://telegram-bot.salla.group/api/webhook-v2.js' -H 'X-Telegram-Bot-Api-Secret-Token: ${{ secrets.TELEGRAM_BOT_SECRET }}' -H 'Content-Type: application/json' -H 'CF-Access-Client-Id: ${{ secrets.CLOUDFLARE_ACCESS_ID }}' -H 'CF-Access-Client-Secret: ${{ secrets.CLOUDFLARE_ACCESS_TOKEN }}' --data '{"message":{"from":{"username":"${{ secrets.RELEASE_USERNAME }}"},"chat":{"id":${{ secrets.RELEASE_CHAT_ID }}},"text": "/release ${{ vars.RELEASE_PROJECT_NAME }} -n ${{ vars.RELEASE_NAMESPACE }} -v ${{ env.CI_ACTION_REF_NAME }} --all "}}'

  test:
    name: Test PHP ${{ matrix.team }} Team
    runs-on: ubuntu-24.04
    needs: vendor-prepare
    if: needs.vendor-prepare.outputs.test_unit_supported == 'true' && needs.vendor-prepare.outputs.env == 'Preview'

    strategy:
      fail-fast: false
      matrix:
        team:
          [
            "checkout",
            "theme",
            "product",
            "marketing",
            "api",
            "journey",
            "order-general",
            "order-console",
            "order-controllers",
            "order-requests",
            "order-middlewares",
            "order-entities",
            "order-helpers",
            "order-jobs",
            "order-services",
            "order-transformers",
            "shipping-general",
            "shipping-controllers",
            "shipping-jobs",
            "shipping-utilities",
          ]

    timeout-minutes: 40
    env:
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}

    steps:
      - name: Setup Twingate
        uses: maherapi/github-action@enhanced
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }}
          enable-verification: "true"
          expected-outbound-ips: ${{ secrets.TWINGATE_OUTBOUND_IPS }}
          verification-timeout-seconds: ${{ vars.ACTION_WAIT_AFTER_TWINGATE }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Retrieve Vached Vendor Folder
        uses: actions/cache@v3
        id: cache_vender
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          fail-on-cache-miss: true

      - name: Setup PHP Version
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.3"
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1,opcache.jit=function, opcache.jit_buffer_size=128M, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1, pcov.enabled=1, pcov.directory=., memory_limit=-1
          coverage: "pcov"
          debug: true

      - name: Build Vendor Folder
        if: ${{ !steps.cache_vender.outputs.cache-hit }}
        env:
          DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
        run: |
          ./unit-test/ci-unit-test-prepare.sh

      - name: Run Test
        id: comment_step
        env:
          APP_KEY: "base64:Kd+sit6vWbH228WJsOuqE7Da1dmi1EnZMLLlhRa+Dc8="
          DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
          COMMIT_UUID: ${{ env.CI_PR_SHA || github.sha }}
          CODACY_API_TOKEN: ${{ secrets.CODACY_API_TOKEN }}
        run: |
          composer run-script post-install-cmd && ./unit-test/ci-unit-test.sh "${{ matrix.team }}-team"

      - name: Test Log
        run: |
          cat test-log.txt
          HTML_URL=$(curl --location --request GET "https://api.github.com/repos/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}" --header 'Authorization: bearer ${{ env.GITHUB_TOKEN }}' | jq -r '.html_url')
          printf "\n\nFull logs : $HTML_URL" >> report.txt

      - name: Get Test Result
        id: result
        run: |
          cat failed-logs.txt | wc -l | awk '{print $1}'
          result=$(cat failed-logs.txt | wc -l | awk '{print $1}' )
          echo "::set-output name=result::$result"

      - name: Comeent To PR
        if: "${{ steps.result.outputs.result != 1 }}"
        uses: JoseThen/comment-pr@v1.2.0
        with:
          file_path: "report.txt"
          GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}

      - name: Check Test Result
        run: |
          if  [ "${{ steps.result.outputs.result }}" != 1 ]; then
            exit 1
          else
            exit 0
          fi;

  unit-test-ready:
    needs: [test]
    runs-on: ubuntu-24.04
    name: "Testing Ready"
    steps:
      - name: All tests passed
        run: echo "All matrix jobs succeeded"
