<?php

namespace Modules\MarketPlace\Listeners;


use <PERSON><PERSON>\Core\Traits\WithStageLogger;
use <PERSON><PERSON>\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallEvent;
use <PERSON><PERSON>\WebhookServer\Events\WebhookCallFailedEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallSucceededEvent;

class MarketPlaceWebhookListener
{
    use WithStageLogger;

    /**
     * @param WebhookCallEvent $event
     */
    public function handle($event)
    {
        $message = 'success';
        $extraData = [];
        if (!($event instanceof WebhookCallSucceededEvent)) {
            $message = 'fail';
            $extraData = [
                'error_type' => $event->errorType,
                'error' => $event->errorMessage ? truncateMessage($event->errorMessage) : null,
                'attempt' => $event->attempt,
            ];
        }

        $logData = [
            'status'  => $message === 'success' ? 'done' : 'failed',
            'event'   => class_basename($event),
            'webhook' => [
                'url'      => $event->webhookUrl,
                'details'  => !empty($event->response) && !empty($event->response->getBody()) ? truncateMessage(json_encode($event->response->getBody()->getContents())) : null,
                'headers'  => json_encode($event->headers),
                'payload'  => truncateMessage(json_encode($event->payload)),
                'meta'     => truncateMessage(json_encode($event->meta)),
                'tags'     => truncateMessage(json_encode($event->tags)),
            ],
            'event_name' => is_array($event->payload) ? $event->payload['event_name'] ?? null : null,
            'store_id' => is_array($event->payload) ? $event->payload['store'] ?? null : null,
            'app_id' => is_array($event->payload) ? $event->payload['app'] ?? null : null,
            ...$extraData
        ];
        \Salla\Logger\Facades\Logger::message('webhook', $message, $logData);
    }
}
