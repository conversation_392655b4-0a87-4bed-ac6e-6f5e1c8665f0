<?php

namespace Modules\MarketPlace\Enum;

use Salla\Core\Enum\Enum;

class InstalledThemeStatus extends Enum
{
    public const ACTIVE      = 'active';
    public const INACTIVE    = 'inactive';
    public const READY_STORE = 'ready_store';

    public static $labels = [];

    /**
     * @return void
     */
    protected static function boot()
    {
        static::$labels = [
            self::ACTIVE      => __('marketplace::installed_theme_status.' . self::ACTIVE),
            self::INACTIVE    => __('marketplace::installed_theme_status.' . self::INACTIVE),
            self::READY_STORE => __('marketplace::installed_theme_status.' . self::READY_STORE),
        ];
    }
}