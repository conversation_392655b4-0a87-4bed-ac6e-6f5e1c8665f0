<?php


namespace Modules\MarketPlace\Enum;


use <PERSON><PERSON>\Core\Enum\Enum;

class SallaProductAddonType extends Enum
{
    public const STORE_BRANCHES_LIMIT_ADDON = 'StoreBranchesLimitAddon';
    public const STORE_USERS_LIMIT_ADDON    = 'StoreUsersLimitAddon';
    public const APPS                       = 'APPS';
    public const SERVICE                    = 'SERVICE';
    public const INFLUENCER_SERVICE         = 'INFLUENCER_SERVICE';

    /**
     * @return string[]
     */
    public static function getPartnerTypes()
    {
        return [
            self::APPS,
            self::SERVICE,
            self::INFLUENCER_SERVICE,
        ];
    }

    public static function getPartnerServiceTypes()
    {
        return [
            self::SERVICE,
            self::INFLUENCER_SERVICE,
        ];
    }

    public static function isExternalPriceServices($type)
    {
        return in_array($type, [
            self::SERVICE,
            self::INFLUENCER_SERVICE,
        ]);
    }
}
