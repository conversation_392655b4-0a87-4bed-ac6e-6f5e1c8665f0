<template>
  <div class="loader-wrapper loader-wrapper--center loader-wrapper--min-height" v-if="!data">
    <span class="loader loader--small"></span>
  </div>
  <div v-else class="marketplace-section">
    <a v-if="false" :href="!isVerfied ? '/entity/verify' : null" class="alert-box alert-box--white mb-30"
      :class="[`${!isVerfied ? 'pending' : 'completed'}`]">
      <i></i>
      <article>
        <h6>توثيق المتجر كجمعية خيرية
          <span>({{ isVerfied ? 'مكتمل' : 'غير مكتمل' }})</span>
        </h6>
        <p v-if="!isVerfied" class="text-muted text-muted-small">
          احصل على اشتراك مجاني مدى الحياة.
        </p>
        <p v-else class="text-muted text-muted-small">
          أكمل خطوة توثيق المتجر كجمعية خيرية لتحصل علي اشتراك مجاني دائم.
        </p>
      </article>
    </a>
    <plans :data="data" :storeEntity="storeEntity" :isVerfied="isVerfied" :onBoardingPlansPage="onBoardingPlansPage" />
    <p v-if="data.is_ios" class="mt-20">
      اشتراكك في أي من باقات سلة يعني موافقتك على 
      <a href="https://salla.com/terms/" class="mt-20" target="_blank"><strong>الشروط والأحكام</strong> </a>
      و
      <a href="https://salla.com/privacy/" class="mt-20" target="_blank"><strong>سياسة الخصوصية</strong> </a>
      المتبعة في سلة. في حال قمت بالاشتراك عبر منصة أخرى، يمكنك إدارة اشتراكك من خلال تلك المنصة.
    </p>
  </div>
</template>

<script>

import { onBeforeMount, onMounted, ref } from 'vue';
import axios from 'axios';

export default {
  setup() {
    const data = ref(null),
      storeEntity = ref(null),
      isVerfied = ref(null),
      onBoardingPlansPage = ref(false);
      axios.defaults.headers.common = {...axios.defaults.headers.common, ...window.marketplaceInitialData.headers};

    onBeforeMount(() => {
      const urlParams = new URLSearchParams(window.location.search)
      if (!urlParams.has("context")) {
        return
      }

      if (urlParams.get("context") === "onboarding") {
        return onBoardingPlansPage.value= true
      }
    })

    onMounted(async () => {
      const plansUtm = JSON.parse(localStorage.getItem('plans_utm'));

      let url = `/marketplace/store-plan${onBoardingPlansPage.value ? '?context=onboarding' : ''}`;

      if (plansUtm && (plansUtm.utm_source || plansUtm.utm_campaign)) {
        const params = new URLSearchParams();

        if (plansUtm.utm_source) {
          params.append('utm_source', plansUtm.utm_source);
        }

        if (plansUtm.utm_campaign) {
          params.append('utm_campaign', plansUtm.utm_campaign);
        }

        const separator = url.includes('?') ? '&' : '?';
        url += separator + params.toString();
      }

      await axios.get(url)
        .then(response => {
          data.value = response.data.data; // Registring the data for public use.
        // store entity and store verfied to use it for store type charity.
          storeEntity.value = response.data.data.store_entity;
          isVerfied.value = response.data.data.has_verified_documents;
        })
    });

    return {
      data,
      storeEntity,
      isVerfied,
      onBoardingPlansPage
    }
  }
}
</script>

