<template>
<div :class="{'p-30': onBoardingPlansPage }">
  <div class="rec-flex center mb-50" v-if="isRecommendSpecial">
    <button aria-expanded="false" id="show_other_plans" data-parent="#accordion" href="#collapse_other_plans" aria-controls="collapse_other_plans" data-toggle="collapse" class="btn btn--outlined primary btn-rounded px-20" @click="isOpen = !isOpen">
      <i class="position-left font-14"  :class="[isOpen ? 'sicon-minus' : 'sicon-add']"></i>
      <span>هل تود استعراض باقات سلة الأخرى؟</span>
    </button>
  </div>

  <div :id="`collapse_other_plans`"  :aria-labelledby="`show_other_plans`"  :aria-expanded="!isRecommendSpecial" :class="{'collapse': isRecommendSpecial}" >

    <div v-if="storeEntity != 'charity' && (data.current_plan.type === 'plus' || data.current_plan.type === 'basic')"
         :class="{'grid-block': true, 'grid-block--col-2': !onBoardingPlansPage && data.plans.length <= 2,
      'grid-block--col-3 max-w-none': onBoardingPlansPage || data.plans.length > 2, 'grid-block--plans': true,
               'grid-block--gap-20': true, 'addons-list--upgrade': data.current_plan.type === 'plus', 'reverse': data.is_ios && data.current_plan.type === 'basic'}">
    <div v-for="plan in data.plans" :key="plan.id" class="salla-addon salla-addon--plan"
         :class="{'has-ribbon has-ribbon--lg has-ribbon--with-icon has-ribbon--darker salla-addon--recommended': plan.recommended.value && data.current_plan.type === 'basic',
         'notSorted': onBoardingPlansPage, 'upgrade': plan.type === 'team' && data.current_plan.type === 'plus',
         'salla-addon--recommended': plan.temporary_display}"
         data-ribbon-title="الأفضل">
      <div class="rec-flex rec-flex-col wide full-height z-10" :class="{'p-30': data.current_plan.type === 'plus'}" v-if="plan.type === 'plus' || data.current_plan.type !== 'plus'">
        <div class="salla-addon__thumb text-center">
          <img :src="plan.logo" :alt="plan.name">
          <small v-if="data.current_plan.type !== 'basic'" class="badge badge--grey"> {{plan.default_price.recurrence}} </small>
        </div>

        <!-- Plan Descriptions -->
        <div class="salla-addon__title-wrapper" :class="{'p-0 pb-25': data.current_plan.type === 'plus'}">
          <h2>{{plan.name}}</h2>
          <p class="d-block mb-10 text-center">{{plan.short_description}}</p>
          <div v-if="data.current_plan.type === 'basic'">
            <span v-if="plan.default_price.value" class="product-price">
              {{plan.default_price.value}}
                ر.س
              / {{plan.default_price.recurrence}}
            </span>
            <span v-else class="product-price">
              مجانا
            </span>
          </div>
          <div class="align-center wide" v-if="data.current_plan.type === 'plus'">
            <button class="btn btn--outlined btn-rounded primary" data-toggle="modal" data-target="#plansFeatures">استكشف مزايا الباقة</button>
          </div>
        </div>

        <!-- Plan Benefits -->
        <div class="salla-addon__info" v-if="data.current_plan.type === 'basic'">
          <ul class="rec-list rec-list--vertical rec-list--numeric checked-list light lg">
            <li v-for="(feature, index) in plan.product_features" 
                :key="index" 
                :class="{'hidden': (plan.type === 'team' && index > 4) || (plan.type === 'plus' && index > 3)}">
              <p class="salla-addon__info-title font-16">{{feature.title}}</p>
            </li>
            <li class="plus-item" v-if="(plan.type === 'team' && plan.product_features.length > 5) || (plan.type === 'plus' && plan.product_features.length > 4)">
              <p class="salla-addon__info-title font-16">
                {{ plan.type === 'team' ? plan.product_features.length - 5 : plan.product_features.length - 3 }}
                مزايا اخرى
              </p>
            </li>
          </ul>
        </div>

        <!-- Show this section if the merchant instalment is false -->
        <div v-if="!data.current_plan.installment.paused" :style="{'margin-top': onBoardingPlansPage ? 'auto' : ''}">

          <!-- Testimony Section -->
          <testimonies 
            v-if="data.current_plan.type === 'basic' && plan.type !== 'basic'" 
            :plan="plan"
            :currentPlan="data.current_plan.type"
          />

          <!-- Renewal Section -->
          <div class="py-25" v-if="data.current_plan.type === 'plus'">
            <div class="salla-addon__duedate" v-if="data.current_plan.type !== 'basic'">
              <div class="salla-addon__duedate__head" >
                تاريخ التجديد: 
                <span class="text-default">{{plan.renewal.date}}</span>
              </div>
              <div class="salla-addon__duedate__body">
                <b v-if="plan.renewal.remaining" class="d-block font-30 lh-1 mb-10" :class="plan.renewal.remaining.withinReminderPeriod ? 'text-danger' : 'text-gold' ">
                  {{plan.renewal.remaining.value > 0 ? plan.renewal.remaining.value : null}}<span class="fix-align ml-5">{{plan.renewal.remaining.type}}</span>
                </b>
                <small class="d-block text-muted font-11">الايام المتبقية</small>
              </div>
            </div>
          </div>

          <!-- Subscriptions &  Subscription Button-->
          <div class="salla-addon__options">
            <template v-if="plan.type !== 'basic'">
              <h6 v-if="!data.is_ios" class="mt-0 mb-10">اختر المدة:</h6>

              <!-- Subscriptions list for the Website -->
              <ul class="rec-list rec-list--vertical" v-if="!data.is_ios">
                <li class="tooltip-toggle top right" v-for="subscription in plan.subscriptions" :key="subscription.id">
                  <radio-button
                  :id="`${subscription.product_price_id}_${subscription.duration}`"
                  :disabled="!subscription.can_renew"
                  name="subscription" 
                  @change=" 
                  selectedDuration.productPriceId = $event;
                  selectedDuration.type = subscription.duration; 
                  selectedDuration.total = subscription.price.discounted ? subscription.price.discounted : subscription.price.regular
                  selectedDuration.id = subscription.id" 
                  :value="subscription.product_price_id">
                    {{subscription.title}} 
                    <span class="previous text-muted font-13" v-if="!data.is_ios && subscription.price.regular !== subscription.price.discounted && subscription.price.discounted !== 0"> 
                      {{subscription.price.regular}} ر.س 
                    </span>
                    <b class="mr-20"> {{subscription.price.discounted}} <span class="text-muted">ر.س</span> </b>
                    <span v-if="data.current_plan.type === 'basic' && subscription.price.discount.value > 0" class="badge badge--danger badge--small mr-15">خصم {{subscription.price.discount.percentage}}%</span>
                    <div class="salla-addon__gift" v-if="data.current_plan.type === 'basic' && subscription.gifts">
                      <i class="sicon-gift  font-14"></i>
                      <span class="font-11 light text-default wide"  v-html="subscription.gifts"></span>
                    </div>
                  </radio-button>
                  <div v-if="!subscription.can_renew" class="tooltip-content tooltip-content--small">لديك اشتراك فعال على هذة المدة</div>
                </li>
              </ul>

              <!-- Subscriptions list for the Mobile App -->
              <ul class="rec-list rec-list--vertical mt-20" v-else> 
                <li class="font-16 align-center wide mb-10" v-if="plan.subscriptions.length">
                  {{plan.subscriptions[0].title}} 
                    <span class="previous text-muted font-13" v-if="!data.is_ios && plan.subscriptions[0].price.regular !== plan.subscriptions[0].price.discounted && plan.subscriptions[0].price.discounted !== 0"> 
                      {{plan.subscriptions[0].price.regular}} ر.س 
                    </span>
                  <b> {{plan.subscriptions[0].price.discounted}} <span class="text-muted">ر.س</span> </b>
                </li>
              </ul>
            </template>

            <!-- Subscriptions Button -->
            <a v-if="plan.type !== 'basic'"
              class="btn btn-tiffany btn-xlg font-15 wide"
              :class="{'disabled btn--outlined primary': selectedDuration.id !== plan.id && !data.is_ios || storeEntity === 'charity' }"
              :key="selectedDuration.productPriceId"
              :data-product_price_id="selectedDuration.productPriceId"
              data-inline-loader
              @click="subscribeToPlan(data.current_plan.type === 'plus' ? 'begin_checkout_renew' : 'begin_checkout_upgrade',
                                  data.current_plan.type === 'plus' ? null : plan.type,
                                  !data.is_ios ? selectedDuration.productPriceId : plan.subscriptions.length ? plan.subscriptions[0].product_price_id : null,
                                  plan,
                                  data.current_plan.type === 'plus' ? 'renewal' : 'upgrade')">
              <span v-if="data.current_plan.type === 'basic'"> اشترك الان</span>
              <span v-else>تجديد الاشتراك</span>
            </a>
            <a v-else @click="navigateToMain" class="btn btn-tiffany btn-xlg font-15 wide">
              ابدء رحلتك الان
            </a>
          </div>
        </div>
        <div v-else class="salla-addon__options align-center">
          <b class="text-danger font-16 text-bold display-block mb-15">تعذّر سداد الدفعات المستحقة</b>
          <span class="font-15 display-block mb-20">تم إيقاف اشتراكك في باقة سلة برو منذ</span>
          <b class="text-bold font-25 display-block mb-20">
            {{ data.current_plan.installment.days }}
            يوم
          </b>
          <a class="btn btn-tiffany btn-xlg font-15 wide"
            @click="installmentHandler(data.current_plan.installment.id)"
            data-inline-loader>
            سداد الدفعات
          </a>
        </div>
      </div>

      <salla-upgrade
      :plans="data.plans" 
      :plan="plan" 
      :addToCartURL="data.buy_base_url" 
      :storeEntity="storeEntity" 
      v-if="data.current_plan.type === 'plus' && plan.type !== 'plus'"
      @trackEvent="trackEvent"/>
    </div>
  </div>

  <salla-pro-for-charity :plans="data.plans" :onBoardingPlansPage="onBoardingPlansPage" v-if="data.current_plan.type === 'basic' && storeEntity === 'charity' && !isVerfied"/>

  <salla-pro
    v-if="(data.current_plan.type === 'team' || data.current_plan.type === 'special')" 
    :plans="data.plans" 
    :currentPlan="data.current_plan" 
    :addToCartURL="data.buy_base_url" 
    :storeEntity="storeEntity" 
    :isVerfied="isVerfied"
    :has_lifetime_subscription="data.has_lifetime_subscription"
    :isIos="data.is_ios"
    :isDemoStore="data.is_demo_store"
    @installmentHandler="installmentHandler"
    @trackEvent="trackEvent"/>

    <addons 
      v-if="data.current_plan.type !== 'basic' && !data.is_ios && !data.current_plan.installment.paused"
      :addons="data.addons" 
      :isDemo="data.is_demo_store"
      :addToCartURL="data.buy_base_url" />

    <subscriptions-gift v-if="data.current_plan.type !== 'basic' && !data.current_plan.installment.paused" />

  <packages-benefits :data="data" :isShowBtn="true" isRecommendSpecial="true" class="mt-50" v-if="data.current_plan.type !== 'special' && data.current_plan.type === 'basic'" />
  <packages-benefits-modal :data="data" class="mt-50" v-else-if="data.current_plan.type !== 'special' && data.current_plan.type !== 'basic'" :isIos="data.is_ios"/>

</div>
<compare-with-special-modal 
  :plan="selectedOptions[3]" 
  @subscribeToSelected="trackEvent(...selectedOptions)"
  @subscribeToSpecial="trackEvent(...$event)"
>
</compare-with-special-modal>
</div>
</template>
<script>

import { ref, onMounted, computed } from 'vue';
import { useGtm } from "vue-gtm";
import { cart } from '../composables/cart';
import { installment } from '../composables/installment';

export default {
  props: ['data', 'storeEntity', 'isVerfied', 'onBoardingPlansPage'],
  setup (props) {
    const selectedDuration = ref({}),
          selectedOptions = ref([]),
          gtm = useGtm(),
          total = ref(null),
          isRecommendSpecial = ref(window.marketplaceInitialData.upToSpecial),
          isOpen = ref(false),
          state = ref(null);

    const checked = ref(false)
    // This will alow us to track the event on cart payments
    const jQueryInti = () => {
      let successHandler = laravel.ajax.successHandler
      laravel.ajax.successHandler = (res) => {
        hideLoading()
        successHandler(res)
        // Fire Google Tag Manager and Hotjar Event opening the cart
        total.value = selectedDuration.value.total
        trackEvent('begin_payment');

        if (!res.sections && !res.redirect) {
          // Fire Google Tag Manager and Hotjar Event on payment
          state.value = 'success'
          trackEvent('end_payment');
        }
      }
    };

    // Cart Handler for upgrade or renew
    const cartHandler = (duration, plan, source) => {
      source = props.onBoardingPlansPage ? 'onboarding' : source;
      if (props.data.is_ios && props.data.is_test_store) {
        duration ? cart(props.data.buy_base_url, plan.id,duration,source, props.data.is_ios) : null;
        return false;
      }

      if (!props.data.is_ios && props.data.has_app_store_auto_renewal) {
        swal({
          title: 'تحكم باشتراكك من إعدادات الجهاز',
          text: 'لادارة اشتراكك الرجاء الغاء التجديد التلقائي من اعدادات ابل',
          type: 'warning',
          showConfirmButton: true,
          confirmButtonText: "موافق"
        });
        return false;
      }

      else if (props.data.current_plan.source === 'apple_store' && !props.data.is_ios) {
        swal({
          title: 'تحكم باشتراكك من إعدادات الجهاز',
          text: 'اشتراكك الحالي مدفوع عبر متجر تطبيقات أبل. لإدارة الاشتراك، توجه إلى “الإعدادات” في جهاز أبل واضغط على اسمك ٫م اختر “الاشتراكات”.',
          type: 'warning',
          showConfirmButton: true,
          confirmButtonText: "موافق"
        });
        return false;
      }

      else if (props.data.current_plan.source === 'dashboard' && props.data.is_ios && (plan.type === 'plus' || (plan.type === 'team' && props.data.current_plan.type === 'team'))) {
        
        let title,
            message; 

        //case one if the remaining days are more than 30 days
        if (props.data.current_plan.remaining_days > 30 || plan.renewal.is_auto_renewal) {
          title = 'تحكم بباقتك عبر المتصفّح';
          message = 'لإدارة باقتك بشكل أفضل، توجه إلى لوحة تحكم متجرك في متصفّح الويب.'
        }
        // case two if the remaining days are less than 30 days and the period is Yearly or the remaining days are lees than 7 and the period is Monthly
         else if (props.data.current_plan.remaining_days <= 30 && props.data.current_plan.period === 'yearly' || 
                    props.data.current_plan.remaining_days <= 7 && props.data.current_plan.period === 'monthly') 
        {
          title = 'جدّد اشتراكك عبر المتصفّح';
          message = 'لتفادي توقف مزايا الباقة، جدِّد الاشتراك عبر زيارة لوحة تحكم متجرك في متصفّح الويب.'
        } else {
          title = 'تحكم بباقتك عبر المتصفّح';
          message = 'لإدارة باقتك بشكل أفضل، توجه إلى لوحة تحكم متجرك في متصفّح الويب.'
        }

        swal({
          title: title,
          text: message,
          type: 'warning',
          showConfirmButton: true,
          confirmButtonText: "موافق"
        });
        return false;
      } 
      
      else if (props.data.current_plan.source === 'dashboard' && props.data.is_ios && plan.type === 'team' && props.data.current_plan.type === 'plus' || props.data.is_ios && !props.data.current_plan.has_paid_plan) {
        swal({
          title: 'قم بترقية باقتك عبر المتصفّح',
          text: 'لترقية باقة متجرك الحالية، توجه للوحة تحكم متجرك في متصفّح الويب.',
          type: 'warning',
          showConfirmButton: true,
          confirmButtonText: "موافق"
        });
        return false;
      }

      else if (props.data.current_plan.source === 'apple_store' && props.data.is_ios && (plan.renewal.is_auto_renewal || props.data.current_plan.remaining_days > 7) && (plan.type === 'plus' || plan.type === 'team')) {
        swal({
          title: 'تحكم باشتراكك من إعدادات الجهاز',
          text: 'لإدارة باقة متجرك الحالية، توجه إلى “الإعدادات” في جهاز أبل واضغط على اسمك ثم اختر “الاشتراكات”.',
          type: 'warning',
          showConfirmButton: true,
          confirmButtonText: "موافق"
        });
        return false;
      }

      else if (props.data.current_plan.source === 'apple_store' && props.data.is_ios && props.data.current_plan.remaining_days <= 7 && !plan.renewal.is_auto_renewal && (plan.type === 'plus' || plan.type === 'team') && plan.is_current) {
        swal({
          title: 'جدد اشتراكك من إعدادات الجهاز',
          text: 'لتفادي توقف مزايا الباقة، توجه إلى إعدادات جهاز أبل واضغط على اسمك ثم اختر الاشتراكات.',
          type: 'warning',
          showConfirmButton: true,
          confirmButtonText: "موافق"
        });
        return false;
      }

        else if (props.data.current_plan.source === 'apple_store' && props.data.is_ios && props.data.current_plan.type === 'plus' && !plan.is_current) {
         swal({
          title: 'قم بترقية باقتك من إعدادات الجهاز',
          text: 'لترقية باقة متجرك الحالية، توجه إلى إعدادات جهاز أبل واضغط على اسمك ثم اختر “الاشتراكات”.',
          type: "warning",
          showCancelButton: false,
          confirmButtonText: "موافق",
          closeOnConfirm: true,
        }).then( () => {
          Salla.event.dispatchMobileEvent("market-place:mange-apple-subscriptions")
        });
        return false;
      }

      // if() -  to open compare modal

      duration ? cart(props.data.buy_base_url, plan.id,duration,source, props.data.is_ios) : null;
    }

    const subscribeToPlan = function (event, upgradedPlan = null, duration = null, plan,upgrade_or_renew){
      if(isRecommendSpecial.value){
        selectedOptions.value = arguments;
          $("#special_compare_modal").modal("show");
        }else{
          trackEvent(...arguments)
        }
    }

    // Event traking via Google tag manager and Hotjar
    const trackEvent = function(event, upgradedPlan = null, duration = null, plan, upgrade_or_renew){
      let source =  (props.data.source + "-"+ upgrade_or_renew);
      $("#special_compare_modal").modal("hide");
      plan && cartHandler(duration, plan,source)
      
      hj('event', event);
      gtm.trackEvent({
        event: event,
        duration: !duration ? null : selectedDuration.value.type == 1 ? 'monthly' : selectedDuration.value.type == 12 ? '1year' : '2year',
        currentPlan: props.data.current_plan.type,
        upgradeTo: upgradedPlan,
        total: duration ? duration.total : total.value,
        state: state.value
      });
    };

    const scrollToBenefits = (type) => {
      $(`a[href="#plan_${type}"]`).tab('show');
      // $('#plansFeatures').modal('show');
      $([document.documentElement, document.body]).animate({
        scrollTop: $("#packages_benefits_wrapper").offset().top
      }, 200);
    };

    // Installment Requeset
    const installmentHandler = (installmentId) => {
      const payload = {
        invoices: [...props.data.current_plan.installment.invoices],
        source: 'late-warning'
      };

      installment(installmentId, payload)
    }

    onMounted ( () => {
      jQueryInti();
      window.headers = { ...window.headers, ...window.marketplaceInitialData.headers };
      const params = new URL(window.location.href).searchParams;
      let product_price_id = params.get('product_price_id'),
          product_id = params.get('product_id');

      product_price_id ? $.ajax({
        url: `${props.data.buy_base_url}/${product_id}?without_old_items=1`,
        dataType: 'json',
        type: 'POST',
        data: {
          product_price_id: product_price_id,
          source: props.data.source,
          nonconfirm: true,
        },
        async: false,
        success: (resp) => {
          laravel.ajax.successHandler(resp)
        },
      }) : null;
    });

    const navigateToMain = () => {
      window.parent.location.href = "/";
    }

    return {
      selectedDuration,
      selectedOptions,
      checked,
      jQueryInti,
      trackEvent,
      subscribeToPlan,
      scrollToBenefits,
      isRecommendSpecial,
      installmentHandler,
      isOpen,
      navigateToMain
    }
  }
}
</script>
<style scoped>
  .text-bold {
    font-weight: bold;
  }
</style>
