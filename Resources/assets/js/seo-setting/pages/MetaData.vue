<template>
  <div>
    <page-header v-if="isLegacy" current-page="تحسينات الصفحة الرئيسية" />
    <AlertPlan v-show="!feature_enable"  text="هذه الميزة متوفرة فقط فى باقة سلة سبيشال, سلة برو"/>
    <div v-show="!loading" class="panel panel-default">

    <div class="panel-heading">
      <h6 class="panel-title">&nbsp;
        <i class="sicon-browser-alt"></i>
        تحسين محركات البحث SEO
      </h6>
    </div>
    <div class="panel-body">
      <lingual-field
          placeholder="أضف عنوانًا للصفحة الرئيسية لمتجرك"
          icon="sicon-home"
          type="input"
          class="ling-field"
          name="title"
          :input-attrs="{maxlength:70, cols:30, rows:1}"
          :translations="translations"
          :languages="$lang.languages"
          @value-updated="seoUpdatedInputs"
      >
        <template v-slot:before-input>
          <label>عنوان الصفحة الرئيسية (Homepage title)</label>
        </template>

      </lingual-field>
      <lingual-field
          placeholder="اكتب وصفًا جذابًا وفريدًا لمتجرك"
          class="ling-field"
          :input-attrs="{maxlength:300, cols:30, rows:4}"
          icon="sicon-content"
          type="textarea"
          name="description"
          :translations="translations"
          :languages="$lang.languages"
          @value-updated="seoUpdatedInputs"
      >
        <template v-slot:before-input>
          <label>وصف الصفحة الرئيسية (Meta Description)</label>
        </template>

      </lingual-field>
      <lingual-field
          placeholder="استهدف الكلمات المفتاحيه المناسبه لمنتجك"
          class="ling-field"
          :input-attrs="{maxlength:150, cols:30, rows:4}"
          icon="sicon-store2"
          type="textarea"
          name="keywords"
          :translations="translations"
          :languages="$lang.languages"
          @value-updated="seoUpdatedInputs"
      >
        <template v-slot:before-input>
          <label>الكلمات الافتتاحية (Keywords)</label>
        </template>

      </lingual-field>
      <div class="col-lg-6">
        <div class="form-group">
          <label>طريقة عرض روابط صفحات المتجر</label>
          <ul class="rec-list rec-list--vertical">
            <li class="mb-10">
              <div class="rec-checkbox rec-checkbox--default">
                <input type="radio"  v-model="url_selected" name="isSeoProductLinkEnabled" value="0" :checked="!enhanceSeoData.friendly_urls_status" id="isSeoProductLinkEnabled0">
                <label for="isSeoProductLinkEnabled0">
                <span>
                رابط مختصر
                <a class="text-muted text-muted-smaller" :href="enhanceSeoData.short_url">
                ({{enhanceSeoData.short_url}})
                </a>
                </span>
                </label>
              </div>
            </li>
            <li>
              <div class="rec-checkbox rec-checkbox--default">
                <input  v-model="url_selected" type="radio" :disabled="plan=='basic' || !feature_enable" name="isSeoProductLinkEnabled" value="1" id="isSeoProductLinkEnabled1" :checked="enhanceSeoData.friendly_urls_status">
                <label for="isSeoProductLinkEnabled1">
                <span>
                رابط بإسم الصفحة
                <a class="text-muted text-muted-smaller" :disabled="plan=='basic' || !feature_enable" :href="enhanceSeoData.friendly_url">({{enhanceSeoData.friendly_url}})</a>
                </span>
                </label>
              </div>
            </li>
            <li>
              <div class="rec-checkbox rec-checkbox--default">
                <input  v-model="url_selected" type="radio" :disabled="plan=='basic' || !feature_enable" name="isSeoProductLinkEnabled" value="2" id="isSeoProductLinkEnabled2" :checked="enhanceSeoData.new_urls_status">
                <label for="isSeoProductLinkEnabled2">
                <span>
                رابط بإسم الصفحة بالإنجليزي
                <a class="text-muted text-muted-smaller" :disabled="plan=='basic' || !feature_enable" :href="enhanceSeoData.new_url">({{enhanceSeoData.new_url}})</a>
                </span>
                </label>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <button :disabled="plan=='basic' || !feature_enable" @click="updateSeo" class="btn btn-tiffany btn-full">
        تحديث
      </button>

    </div>
  </div>
    <div v-show="loading" class="text-center">
      <span class="loader loader--wide"></span>
    </div>
  </div>
</template>

<script>
import LingualField from "@salla.sa/languages/components/LingualField";
import PageHeader from "../components/PageHeader";
import {requestData,postSeoEnhancement} from "../utils/configToken";
import AlertPlan from "../components/AlertPlan";
export default {
  name: "MetaData",
  components:{
    AlertPlan,
    PageHeader,
    LingualField
  },
  data(){
    return{
      baseApiUrl:window.apiUrl,
      enhanceSeoData:{
        title:'',
        description:'',
        keywords:'',
        friendly_url:'',
        friendly_urls_status:null,
        short_url:''
      },
      translations:null,
      url_selected:'1',
      loading:false,
      plan:initialData.plan || 'basic',
      feature_enable:true,
      isLegacy: window.sallaLegacy,
    }
  },
  methods:{
    async getUrl() {
      this.loading=true
      const token = await getAsyncFreshToken(false)
      await requestData(`${this.baseApiUrl}/settings/seo/meta-data`,token)
          .then(res => {
           const {title,description,keywords,friendly_url,friendly_urls_status,short_url,translations,feature_enabled}=res.data.data
           this.enhanceSeoData={...this.enhanceSeoData,title,description,keywords,friendly_url,friendly_urls_status,short_url}
            this.feature_enable=feature_enabled
            this.translations=translations
            this.url_selected= friendly_urls_status ? '1':'0'
          })
          .catch((error) => {
            if(error.response && error.response.status === 401){
              swal({
                type:'error',
                text:'ليس لديك صلاحيات'
              })
            }
          }).finally((res)=>this.loading=false)

    },
    seoUpdatedInputs(event){
      this.translations=event.translations
    },

    filterObjectWithoutAr(object){
      return Object.keys(object)
          .filter(key => key !== 'ar')
          .reduce((obj, key) => {
            obj[key] = object[key];
            return obj;
          }, {});
    },
    async updateSeo(){
      let translatedContent ={...this.translations,isSeoProductLinkEnabled:this.url_selected }
      const filterObject = translatedContent
      const token = await getAsyncFreshToken(false)
      await postSeoEnhancement(`${this.baseApiUrl}/settings/seo/meta-data`,{
        ...translatedContent.ar,
        ...filterObject,
      } ,token)
          .then((res)=>{
            swal({
              type:'success',
              text:'تم التحديث بنجاح'
            })
          })
          .catch((error)=>{
            if (error.response.data.errors) {
              for (const field in error.response.data.errors) {
                const [lang, fieldName] = field.split('.');
                const errorMessage = error.response.data.errors[field][0];

                switch (fieldName) {
                  case 'description':
                  case 'keywords':
                  case 'title':
                    swal({
                      type: 'error',
                      text: errorMessage
                    });
                    break;
                  default:
                    swal({
                      type: 'error',
                      text: errorMessage
                    });
                    break;
                }
              }
            } else {
              swal({
                type:'error',
                text:'حدث خطآ'
              })
            }

          })
    }

  },
  
  async mounted(){
    await this.getUrl()
    this.plan = initialData.plan || 'basic';
    window.parent?.postMessage(
      {
        event: 'breadcrumb',
        data: [
          { label: 'التسويق' },
          { label: 'تحسين محركات البحث', route: '/marketing/seo' },
          { label: 'تحسينات الصفحة الرئيسية', route: '/marketing/seo' },
        ],
      },
      '*'
    )
  }
}
</script>

<style lang="scss" scoped>

</style>
