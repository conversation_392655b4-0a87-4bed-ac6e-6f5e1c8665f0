@php use Modules\MarketPlace\Presenter\Cart\CartItemPriceOptionsPresenter; @endphp
@php
if(isset($cart)) {
    $firstCartItem = $cartItems->first();
    $firstTimeCost = 0 ;
        if ($firstCartItem
            && $firstCartItem->product->type == \Modules\MarketPlace\Enum\SallaProductType::MOBILE_APPS
            && $firstCartItem->value) {
                $itemValue = json_decode($firstCartItem->value);
                $itemValueAccount = $itemValue->account ?? null;
                if ($itemValueAccount == 'salla') {
                    $appStoresAccountsProduct = \Modules\MarketPlace\Entities\SallaProducts::where('type', 'mobile_apps')->where('action_method', \Modules\MarketPlace\Enum\SallaProductActionMethod::CREATE_APP_STORES_ACCOUNTS_REQUEST)->first();
                    $firstTimeCost = $appStoresAccountsProduct->productPrices->values()->get(0)->getPrice()->getTaxedPriceAsFloat();
                }
        }
}

$installmentActive = isset($is_installment_cart) || $installment['active'];
@endphp

@if(!empty($with_header) || isset($is_installment_cart))
  <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal"></button>
      <h6 class="modal-title">
          <i class="sicon-full-wallet"></i>&nbsp;
          @if(isset($is_installment_cart))
              {{ __t('marketplace::cart.dashboard.header.complete payment') }}
          @elseif(!empty($source) && $source == \Modules\MarketPlace\Enum\SallaCartSource::EXPERT)
              {{ __t('marketplace::cart.dashboard.header.request service') }}
          @elseif(!empty($source) && $source == \Modules\MarketPlace\Enum\SallaCartSource::INFLUENCER)
              {{ __t('marketplace::cart.dashboard.header.create campaign') }}
          @else
              {{ __t('marketplace::cart.dashboard.header.complete payment') }}
          @endif
      </h6>
      @if(in_array('apple_pay', $payment_methods->get('methods')))
      <div id="apple_pay_warning" class="payment-alt wide">
        <section  class="alert-box alert-box--warning wide my-20">
            <i class="sicon-warning"></i>
            <article>
                <p>لا يدعم المتصفح الذي تستخدمه ميزة الدفع عبر Apple Pay</p>
                <p>
                    <span>
                        يرجى ستخدام متصفح سفاري (Safari) لإتمام عملية الدفع باستخدام Apple Pay بسهولة. استخدم Safari
                    </span>
                    <span
                        role="button"
                        tabindex="0"
                        id="safari_clipboard"
                        onclick="copy('#safari_clipboard')"
                        data-clipboard-text="{{ url()->current() }}"
                        class="copy-referral-link font-13 text-default">
                        نسخ الرابط
                        <i class="sicon-swap-fill flip-x text-tiffany mr-5 font-14"></i>
                        <i class="sicon-check text-tiffany mr-5 font-14 hidden"></i>
                    </span>
                </p>
            </article>
        </section>
      </div>
      @endif
  </div>
@endif

<form class="form form--light payment-detail ajax"
      autocomplete="on"
      data-filter-before-submit="beforeSubmitPayment"
      id="div_cart_form"
>
    <div class="@if(!isset($is_installment_cart) && ($is_create_mobile_app || $is_new_app_cart)) pt-20 @else modal-body @endif">
        {{-- payment summary --- --}}
        @if(isset($is_installment_cart))
            @include('marketplace::dashboard.cart.partials.items_installment')
        @elseif(!empty($source) && $source == \Modules\MarketPlace\Enum\SallaCartSource::EXPERT)
            @include('marketplace::dashboard.cart.partials.items_expert_service')
        @elseif(!empty($source) && $source == \Modules\MarketPlace\Enum\SallaCartSource::INFLUENCER)
            @include('marketplace::dashboard.cart.partials.items_influencer_service')
        @elseif(!empty($source) && $source == \Modules\MarketPlace\Enum\SallaCartSource::APP)
            @include('marketplace::dashboard.cart.partials.items_app')
        @elseif($hasBundledProduct) 
            @include('marketplace::dashboard.cart.partials.items_bundle')
        @else
            @include('marketplace::dashboard.cart.partials.items')
        @endif

        <div style="display: none;" id="div_cart_extra_info">
            @if(!isset($is_installment_cart))
            <input type="hidden" name="source" value="{{ $source }}"/>
            <input type="hidden" name="from" value="{{ $from }}"/>
            @endif
            <input type="hidden" id="payment_method" name="payment_method"
                   value="{{ !isset($is_installment_cart) && $total_summery['total']->getAmount() <= 0 ? 'free' : 'credit_card' }}" />
        </div>

        {{-- payment totals --- --}}
        @if(!$installmentActive || isset($is_installment_cart))
        <ul class="rec-list rec-list--vertical payment-totals"
            @if(feature('install-app-method-v2')->isHaveFeature())
                style="border-top: 0px;"
            @endif
        >
            <li id="payment_subtotal" class="payment-subtotal">
                <div>
                    @if(!isset($is_installment_cart) && !empty($source) && $source == \Modules\MarketPlace\Enum\SallaCartSource::APP)
                        إجمالي المبلغ
                    @else
                        الإجمالي
                    @endif
                </div>
                <div>
                    <div class="rec-price-wrapper">
                        @php
                            $subTotal = isset($is_installment_cart) ? $totalWithoutTax : $total_summery['sub_total']->getAmount();
                            if (!isset($is_installment_cart) && $firstTimeCost) {
                                $subTotal -= $firstTimeCost;
                            }
                        @endphp
                        <b>{{ $subTotal }} ر.س </b>
                    </div>
                </div>
            </li>
            @if (!isset($is_installment_cart) && $firstTimeCost)
                <li id="payment_tax">
                    <div>رسوم التأسيس</div>
                    <div>
                        <div class="rec-price-wrapper">
                            <b>+ {{$firstTimeCost}} </b>
                        </div>
                    </div>
                </li>
            @endif
            <li id="payment_tax">
                <div>{{ isset($is_installment_cart) ?
                __t('marketplace::cart_summary.tax', ['percentage' => $taxPercent]) :
                $total_summery['tax']->getLabel() }}</div>
                <div>
                    <div class="rec-price-wrapper">
                        <b>+{{ isset($is_installment_cart) ? $taxAmount : $total_summery['tax']->getAmount() }}</b>
                    </div>
                </div>
            </li>


            @if(!isset($is_installment_cart) && !empty($total_summery['chnage_plan_cost']) && $total_summery['chnage_plan_cost']->getAmount() > 0)
                <li id="payment_tax">
                    <div>
                        {{ $total_summery['chnage_plan_cost']->getLabel() }}

                        @if ($total_summery['chnage_plan_cost']->getWalletCredit() > 0)
                            <p>
                                <small style="font-size: 11px;color: #9c9c9c;">المبلغ المسترجع للمحفظة ({{ $total_summery['chnage_plan_cost']->getWalletCredit() . '+' }})</small>
                            </p>
                        @endif
                    </div>
                    <div>
                        <div class="rec-price-wrapper">
                            <b>{{ $total_summery['chnage_plan_cost']->getSign() . $total_summery['chnage_plan_cost']->getAmount() }}</b>
                        </div>
                    </div>
                </li>
            @endif

            @if(!$installmentActive)
                @if(!empty($total_summery['loyalty_point']) && $total_summery['loyalty_point']->getAmount() > 0)
                    <li id="payment_loyal_points">
                        <div>
                            <div class="rec-checkbox rec-checkbox--primary large">
                                <input type="checkbox"
                                       id="loyalty_point"
                                       name="payment_method_extra[{{ \Modules\MarketPlace\Presenter\SummaryItems\LoyaltyPoint::TITLE }}]"
                                       data-action="{{ route('cp.marketplace.cart.payment_method', ['cart' => $cart]) }}"
                                       data-method="post"
                                       data-param="payment_method[{{ \Modules\MarketPlace\Presenter\SummaryItems\LoyaltyPoint::TITLE }}]"
                                       data-on-value="active"
                                       data-off-value="inactive"
                                       data-value="{{ $total_summery['loyalty_point']->getAmount() }}"
                                       data-source="{{ (!empty($source) ? $source : '') }}"
                                       class="ajax"
                                       @if($total_summery['loyalty_point']->isAvailable())
                                           checked
                                       @endif
                                />
                                <label for="loyalty_point">
                                    {{ $total_summery['loyalty_point']->getLabel() }}
                                    @php $layality_amount = store()->getLoyaltyPointWalletBalance() - ($total_summery['loyalty_point']->isAvailable()  ? $total_summery['loyalty_point']->getAmount()  : 0); @endphp
                                    @if($layality_amount > 0 )(<b>{{ $layality_amount }}</b>)@endif
                                    <p class="text-danger"> {{ __('marketplace::cart.messages.alert.loyalty_point_note') }} </p>
                                </label>
                            </div>
                        </div>
                        <div>
                            <div class="rec-price-wrapper">
                                <b class="{{ $total_summery['loyalty_point']->isAvailable() ? 'text-danger' : '' }}">{{ $total_summery['loyalty_point']->isAvailable() ? '-'.$total_summery['loyalty_point']->getAmount() : 0 }}</b>
                            </div>
                        </div>
                    </li>
                @endif
                @if(!empty($total_summery['credit']) && $total_summery['credit']->getAmount() > 0)
                    <li id="payment_wallet_credit">
                        <div>
                            <div class="rec-checkbox rec-checkbox--primary">
                                <input type="checkbox"
                                       id="credit"
                                       name="payment_method_extra[{{ \Modules\MarketPlace\Presenter\SummaryItems\Credit::TITLE }}]"
                                       data-action="{{ route('cp.marketplace.cart.payment_method', ['cart' => $cart]) }}"
                                       data-method="post"
                                       data-param="payment_method[{{ \Modules\MarketPlace\Presenter\SummaryItems\Credit::TITLE }}]"
                                       data-on-value="active"
                                       data-off-value="inactive"
                                       data-value="{{ $total_summery['credit']->getAmount() }}"
                                       data-source="{{ (!empty($source) ? $source : '') }}"
                                       data-is_update="{{ $is_update }}"
                                       class="ajax"
                                       @if($total_summery['credit']->isAvailable())
                                       checked
                                    @endif
                                />

                                <label for="credit">
                                    {{ $total_summery['credit']->getLabel() }}
                                    @php $credit_amount = store()->balanceFloat - ($total_summery['credit']->isAvailable()  ? $total_summery['credit']->getAmount()  : 0); @endphp
                                    @if($credit_amount > 0 )(<b>{{ $credit_amount }}</b>)@endif
                                </label>
                            </div>
                        </div>
                        <div>
                            <div class="rec-price-wrapper">
                                <b class="{{ $total_summery['credit']->isAvailable() ? 'text-danger' : '' }}">{{ $total_summery['credit']->isAvailable() ? '-'.salla_format_number($total_summery['credit']->getAmount()) : 0 }}</b>
                            </div>
                        </div>
                    </li>
                @endif
                @if($with_coupon)
                    <li id="div_cart_coupon" class="payment-coupon">
                        <div>
                            <div style="display: flex; align-items: center; justify-content: space-between; flex-direction: row;">
                                <div class="rec-checkbox rec-checkbox--primary">
                                    <input type="checkbox"
                                           id="cart_checkbox_coupon"
                                           @if(!empty($cart->coupon_code) || !empty($coupon_code))
                                               checked
                                            @endif
                                    />
                                    <label for="cart_checkbox_coupon">
                                        {{ __t('marketplace::cart.dashboard.form.have coupon') }}
                                    </label>
                                </div>

                                @if (!empty($cart->coupon_code) && $total_summery['coupon_discount']->isAvailable() && $total_summery['coupon_discount']->getAmount() > 0)
                                    <div>
                                        <div class="rec-price-wrapper">
                                            <b class="text-danger">{{ $total_summery['coupon_discount']->getSign() . $total_summery['coupon_discount']->getAmount() }}</b>
                                        </div>
                                    </div>
                                @elseif(!empty($cart->coupon_code) && $total_summery['gift_card']->isAvailable() && $total_summery['gift_card']->getAmount() > 0)
                                    <div>
                                        <div class="rec-price-wrapper">
                                            <b class="text-danger">{{ $total_summery['gift_card']->getSign() . $total_summery['gift_card']->getAmount() }}</b>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div id="coupon_code"
                             @if(empty($cart->coupon_code) && empty($coupon_code))
                                 style="display: none;"
                                @endif
                        >
                            {{-- add the class coupon-field--added if coupon confirmed and added to diasble the field and add delete coupon btn --}}
                            <div class="form-group coupon-field @if(!empty($cart->coupon_code)) coupon-field--added @endif">
                                <input type="text"
                                       id="cart_input_coupon"
                                       class="form-control numeric"
                                       value="{{ optional($cart)->coupon_code ?: $coupon_code }}"
                                       placeholder="{{ __t('marketplace::cart.dashboard.form.enter coupon') }}"
                                        {{-- onkeyup="parseArabicNumbers('coupon_code')"--}}
                                />
                                <button id="cart_button_coupon" type="button" class="btn btn-tiffany coupon-submit" data-hide-loader>
                                    @if(!empty($cart->coupon_code))
                                        <i class="sicon-check"></i>
                                        {{ __t('marketplace::cart.dashboard.form.coupon added') }}
                                    @else
                                        تطبيق
                                    @endif
                                </button>
                                @if(!empty($cart->coupon_code))
                                    {{-- {{ __t('marketplace::cart.dashboard.button.remove coupon') }} --}}
                                    <a href="{{ route('cp.marketplace.cart.coupon.remove', ['cart' => $cart, 'source' => (!empty($source) ? $source : ''), 'is_update' => $is_update]) }}"
                                       class="ajax btn-danger coupon-delete"
                                       data-type="GET">
                                        حذف
                                    </a>
                                @endif
                            </div>
                        </div>
                    </li>
                @endif
            @endif
            <li id="payment_total" class="payment-total-price">
                <div>
                    <h4 style="font-weight: bold;">
                        {{ __t('marketplace::cart.dashboard.form.total') }}
                    </h4>
                </div>
                <div>
                    <div class="rec-price-wrapper">
                        <b id="cart_b_payment_total" style="font-weight: bold;">
                            {{ isset($is_installment_cart) ? $total : $total_summery['total']->getAmount() }}
                        </b>
                        <small style="font-weight: bold;">ر.س</small>
                    </div>
                </div>
            </li>
        </ul>
        @endif
        @if(isset($is_installment_cart) ? $total > 0 : $total_summery['total']->getAmount($source) > 0)
            {{-- Installment Payment  --}}
            @if(!isset($is_installment_cart) && $installment['show'])
                @include('marketplace::dashboard.cart.partials.installment_details',
                    ['cart' => $cart, 'installment' => $installment])
            @endif

            <div class="pt-20 @if(!isset($is_installment_cart) && $is_create_mobile_app) 'px-20' @else px-20 @endif">
                <h6 class="mb-10 mt-0">الدفع بواسطة</h6>
                <div class="grid-block grid-block--col-s-4 grid-block--gap-20 p-0 mb-20">
                    @foreach($payment_methods->get('methods') as $payment_method)
                        {{-- @dd($payment_method); --}}
                        @include("marketplace::dashboard.payment.methods.$payment_method")
                    @endforeach
                </div>
                <div id="div_payment_cc" class="payment-default">
                    @widget(Modules\MarketPlace\Widgets\Dashboard\CreditCardFormWidget::class, ['source' => (!empty($source) ? $source : ''), 'is_installment_payment' => $installmentActive])
                </div>
                @if(!$installmentActive && in_array('bank', $payment_methods->get('methods')))
                     
                    @include('marketplace::dashboard.cart.partials.payment_primary')
                @endif
            </div>
        @endif
    </div>
    <div class="@if(!isset($is_installment_cart) && ($is_create_mobile_app || $is_new_app_cart)) '' @else modal-footer @endif mt-20">
		<div class="row row-narrow">
			<div class="col-md-12">
				<button  id="submit_form_btn_container"  type="button" data-cart-submit class="btn btn-tiffany rec-fvm btn-wide @if(!isset($is_installment_cart) && $is_create_mobile_app) font-16 text-bold @else '' @endif" data-inline-loader>تأكيد الدفع</button>
            </div>
            {{--<div class="col-md-4 col-xs-6">--}}
            {{--</div>--}}
		</div>
    </div>
</form>

@include('marketplace::dashboard.cart.js.js_index')
<script>

    let isSnapchat = /snapchat/i.test(navigator.userAgent)

    if (!window.ApplePaySession || !ApplePaySession.canMakePayments() || isSnapchat || {{ json_encode(!in_array('apple_pay', $payment_methods->get('methods'))) }}) {
        $('#apple_pay_warning').removeClass('payment-alt');
        $('#payment_method_credit_card').prop('checked', true);
        $('#payment_method_credit_card').trigger('change');
    }

  function copy(id) {
    navigator.clipboard.writeText(window.location.href)


      $(`${id} .sicon-check`).removeClass('hidden')

      setTimeout(() => {
        $(`${id} .sicon-check`).addClass('hidden')
        e.clearSelection()
      }, 1500)
  }
</script>