<?php
/** @var  \Salla\Core\Enum\StoreDocumentStatus $document_status */
$document_status = store()->getStoreDocumentAlertStatus();
?>

@if (request()->routeIs('cp.store_document.index'))
    <div class="alert-box alert-box--white {{ \Salla\Core\Enum\StoreDocumentStatus::getStatusClasses()[$document_status]}}">
        <i></i>
        <article>
            <h6>{{ trans('Breadcrumbs::labels.store_document') }}
                <span>({{\Salla\Core\Enum\StoreDocumentStatus::getLabel($document_status)}})</span>
            </h6>
            <p class="text-muted text-muted-small">
                {{\Salla\Core\Enum\StoreDocumentStatus::getDescription($document_status)}}
            </p>

{{--            @if(! store()->isStoreDocumented() && store()->plan == 'basic')--}}
{{--                <p>تتطلب هذه الخدمة رسوم إدارية رمزية بقيمة (10 ريال) من قبل ابشر و وزارة التجارة، لكن سيتم إعفاء--}}
{{--                    المتاجر عنها لفترة مؤقتة، فسارع بتوثيق متجرك!</p>--}}
{{--            @endif--}}
        </article>
    </div>
@else
    <a @iframe id="entityLink" @endiframe  @legacy href="{{ auth()->user()->role== 'user' ? route("cp.store_document.index") : '' }}" @endlegacy
       class="alert-box alert-box--white {{ \Salla\Core\Enum\StoreDocumentStatus::getStatusClasses()[$document_status]}}"
    >
        <i></i>
        <article>
            <h6>{{ trans('Breadcrumbs::labels.store_document') }}
                <span>({{\Salla\Core\Enum\StoreDocumentStatus::getLabel($document_status)}})</span>
            </h6>
            <p class="text-muted text-muted-small">
                {{\Salla\Core\Enum\StoreDocumentStatus::getDescription($document_status)}}
            </p>
            <span style="color: #f55157;"> فقط مالك المتجر يستطيع الدخول لتوثيق المتجر.</span>
        </article>
    </a>
@endif

<script>
    // Entity verification iframe navigation
    $("#entityLink").click((e) => {
        e.preventDefault();
        window.parent?.postMessage({event: "navigateTo", path: '/entity/verifications'}, "*");
    })
</script>
