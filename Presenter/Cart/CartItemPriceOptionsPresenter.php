<?php

namespace Modules\MarketPlace\Presenter\Cart;

use Exception;
use Illuminate\Support\Collection;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductActionMethod;
use Modules\MarketPlace\Enum\SallaProductType;
use Mo<PERSON>les\MarketPlace\Presenter\Dashboard\SallaAIDescriptionOptionsPresenter;
use Modules\MarketPlace\Presenter\Dashboard\SallaPackagePresenter;
use Modules\MarketPlace\Presenter\Dashboard\SallaSubscriptionPresenter;
use Modules\MarketPlace\Repositories\SallaProductRepository;
use Modules\MarketPlace\Scopes\SallaProductPriceCheckStoreScope;
use Salla\Core\Enum\Plan;
use Salla\Core\Presenters\BasePresenter;

class CartItemPriceOptionsPresenter extends BasePresenter
{
    public ?string $icon = null;

    public ?string $title = null;

    public ?string $subtitle = null;

    public ?string $promotional = null;

    /**
     * To indicate of there is "options" available on the object or not.
     */
    public bool $exist = false;

    /**
     * to indicate to FE what is the type of salla product. For now, it
     * will return either "plan" or "sms".
     */
    public string $type;

    public array $options = [];

    protected SallaProducts $sallaProduct;

    /** @var Collection<SallaCartItem> */
    protected Collection $sallaCartItems;

    /**
     * @throws Exception
     */
    public function __construct(Collection $sallaCartItems)
    {
        $this->sallaCartItems = $sallaCartItems;
        $this->sallaProduct = $sallaCartItems->first()->product;
        // checking on cartItems count to skip price options. Currently, we
        // only present price options only when the count of items is equal to 1.
         if ( (!SallaProductType::IsMOBILE_APPS($this->sallaProduct->type)) && $this->sallaCartItems->count() > 1) {
             return $this;
         }

        if (!SallaProductType::isItemPriceOptionsType($this->sallaProduct->type)) {
            return $this;
        }

        $this->initialize();
    }

    /**
     * @throws Exception
     */
    protected function initialize()
    {
        $this->icon = $this->getIcon();
        $this->title = $this->getTitle();
        $this->subtitle = $this->getSubtitle();
        $this->type = $this->getType();
        $this->options = $this->getOptions();
        $this->promotional = $this->getPromotional();

        if (count($this->options) > 0) {
            $this->exist = true;
        }
    }

    protected function getIcon(): ?string
    {
        if ($this->hasPriceOptionsPerPeriod()) {
            return $this->sallaProduct->avatar;
        }
        if ($this->getType() === SallaProductType::SMS) {
            return asset('images/marketplace/sms.svg');
        }

        return null;
    }

    protected function getOptions(): array
    {
        if ($this->getType() === SallaProductType::PLAN) {
            return $this->pluckPlans();
        }

        if ($this->getType() === SallaProductType::MOBILE_APPS && $this->sallaProduct->action_method != SallaProductActionMethod::UPDATE_MOBILE_REQUEST) {
            return $this->pluckMobileAppPlans();
        }

        if ($this->getType() === SallaProductType::SMS) {
            return $this->pluckSmsPackages();
        }

        if ($this->isAIProduct()) {
            return $this->pluckAiPrices();
        }

        return [];
    }

    private function hasPriceOptionsPerPeriod(): bool
    {
        return in_array($this->sallaProduct->type, [SallaProductType::MOBILE_APPS, SallaProductType::PLAN]);
    }

    private function isAIProduct(): bool
    {
        return $this->getType() === SallaProductType::AI_DESCRIPTION;
    }

    protected function isSelected(SallaProductPrice $sallaProductPrice): bool
    {
        return $this->sallaCartItems->first()->product_price_id === $sallaProductPrice->id;
    }

    /**
     * @throws Exception
     */
    protected function getTitle(): string
    {
        if ($this->hasPriceOptionsPerPeriod() || $this->isAIProduct()
            ) {
            return $this->sallaProduct->name;
        }

        if (SallaProductType::IsSMS($this->sallaProduct->type)) {
            // this is written statically because I need a generic title that matches all
            // SMS packages without specifying SMS qty.
            return 'رصيد SMS';
        }

        throw new Exception("I don't know how to deal with ".$this->sallaProduct->type." type");
    }

    protected function getPromotional(): ?string
    {
        if ($this->isAIProduct()) {
            return $this->sallaProduct->promotional;
        }

        return null;
    }

    protected function getSubtitle(): string
    {
        if ($this->isAIProduct()) {
            return $this->sallaProduct->name;
        }

        return $this->hasPriceOptionsPerPeriod() ? 'مدة الاشتراك' : 'الرصيد المطلوب';
    }

    protected function getType(): string
    {
        $type = $this->sallaProduct->type;

        if (is_string($type)) {
            return $type;
        }

        throw new Exception("I don't know how to deal with ".$this->sallaProduct->type." type");
    }

    /**
     * @throws Exception
     */
    protected function pluckPlans(): array
    {
        $plan = $this->sallaProduct;
        $currentSubscriptionPlan = store()->planInfo;
        $planCampaign = $this->sallaProduct->productPrices()->whereHas('utm', function ($query) {
            $query->where('utm_expire_at', '>', now()->format('Y-m-d'));
        })->exists();

        if (!$planCampaign) {
            $plan = app(SallaProductRepository::class)
                ->byTypeValue($this->sallaProduct->type_value)
                ->notSandbox()
                ->first();
        }

        $plan?->load([
            'productPrices' => function ($query) use ($currentSubscriptionPlan, $plan) {
                $shouldFilter = resolve(SallaProductRepository::class)->shouldApplyPeriodFilter($currentSubscriptionPlan, $plan);

                if ($shouldFilter) {
                    $query->where('period', '>', $currentSubscriptionPlan->period)
                        ->when($this->sallaProduct->type_value === Plan::PRO, function ($query) use ($currentSubscriptionPlan) {
                            $query->where('product_id', $currentSubscriptionPlan->product_id);
                        })
                        ->when($this->sallaProduct->type_value === Plan::PLUS && $currentSubscriptionPlan->type_value === Plan::PRO, function ($query) {
                            $query->where('product_id', '!=', $this->sallaProduct->id);
                        });
                }
            },
            'page'
        ]);

        return $plan
            ->getRelation('productPrices')
            ->map(function ($sallaProductPrice) use ($plan) {
                return SallaSubscriptionPresenter::make(
                    $plan,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice)
                );
            })
            ->toArray();
    }

    /**
     * @throws Exception
     */
    protected function pluckMobileAppPlans(): array
    {
        $plan = app(SallaProductRepository::class)
                    ->byType(SallaProductType::MOBILE_APPS)
                    ->notSandbox()
                    ->with(['productPrices' => fn($q) => $q->orderBy('price', 'DESC'), 'page'])->first();

        return $plan
            ->getRelation('productPrices')
            ->map(function ($sallaProductPrice) use ($plan) {
                return SallaSubscriptionPresenter::make(
                    $plan,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice));
            })
            ->toArray();
    }

    protected function pluckSmsPackages(): array
    {
        return app(SallaProductRepository::class)
            ->smsPackages()
            ->get()
            ->map(function (SallaProducts $smsPackage) {
                $sallaProductPrice = $smsPackage->getRelation('productPrices')->first();

                return SallaPackagePresenter::make(
                    $smsPackage,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice));
            })
            ->toArray();
    }

    protected function pluckAiPrices(): array
    {
        $aiDescription = app(SallaProductRepository::class)
            ->where('type', SallaProductType::AI_DESCRIPTION)
            ->with(['productPrices' => fn($q) => $q->orderBy('price', 'ASC')])
            ->first();

        return $aiDescription
            ->getRelation('productPrices')
            ->map(function ($sallaProductPrice) use ($aiDescription) {
                return SallaAIDescriptionOptionsPresenter::make(
                    $aiDescription,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice));
            })
            ->toArray();
    }
}
