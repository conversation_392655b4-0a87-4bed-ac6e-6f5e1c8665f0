<?php

namespace Modules\MarketPlace\Presenter\Cart;

use Exception;
use Illuminate\Support\Collection;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductActionMethod;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Presenter\Dashboard\SallaPackagePresenter;
use Modules\MarketPlace\Presenter\Dashboard\SallaSubscriptionPresenter;
use Modules\MarketPlace\Repositories\SallaProductRepository;
use Salla\Core\Enum\Plan;
use Salla\Core\Presenters\BasePresenter;

class CartItemPriceOptionsApiPresenter extends BasePresenter
{
    public ?string $icon = null;

    public ?string $title = null;

    public ?string $subtitle = null;

    /**
     * To indicate of there is "options" available on the object or not.
     */
    public bool $exist = false;

    /**
     * to indicate to FE what is the type of salla product. For now, it
     * will return either "plan" or "sms".
     */
    public string $type;

    public array $options = [];

    protected SallaProducts $sallaProduct;

    /** @var SallaCartItem */
    protected $sallaCartItem;

    /**
     * @throws Exception
     */
    public function __construct(SallaCartItem $sallaCartItem)
    {
        $this->sallaCartItem = $sallaCartItem;
        $this->sallaProduct = $sallaCartItem->product;
        // checking on cartItems count to skip price options. Currently, we
        // only present price options only when the count of items is equal to 1.
        if (!SallaProductType::IsMOBILE_APPS($this->sallaProduct->type)) {
            return $this;
        }

        if (!in_array($this->sallaProduct->type, [SallaProductType::MOBILE_APPS, SallaProductType::PLAN, SallaProductType::SMS])) {
            return $this;
        }

        $this->icon = $this->getIcon();
        $this->title = $this->getTitle();
        $this->subtitle = $this->getSubtitle();
        $this->type = $this->getType();
        $this->options = $this->getOptions();
    }

    protected function getIcon(): ?string
    {
        if ($this->hasPriceOptionsPerPeriod()) {
            return $this->sallaProduct->avatar;
        }
        if ($this->getType() === SallaProductType::SMS) {
            return asset('images/marketplace/sms.svg');
        }

        return null;
    }

    protected function getOptions()
    {
        if ($this->getType() === SallaProductType::PLAN) {
            return $this->pluckPlans();
        }

        if ($this->getType() === SallaProductType::MOBILE_APPS && $this->sallaProduct->action_method != SallaProductActionMethod::UPDATE_MOBILE_REQUEST) {
            return $this->pluckMobileAppPlans();
        }

        if ($this->getType() === SallaProductType::SMS) {
            return $this->pluckSmsPackages();
        }

        return [];
    }

    private function hasPriceOptionsPerPeriod(): bool
    {
        return in_array($this->sallaProduct->type, [SallaProductType::MOBILE_APPS, SallaProductType::PLAN]);
    }

    protected function isSelected(SallaProductPrice $sallaProductPrice): bool
    {
        return $this->sallaCartItem->product_price_id === $sallaProductPrice->id;
    }

    /**
     * @throws Exception
     */
    protected function getTitle(): string
    {
        if ($this->hasPriceOptionsPerPeriod()
        ) {
            return $this->sallaProduct->name;
        }

        if (SallaProductType::IsSMS($this->sallaProduct->type)) {
            // this is written statically because I need a generic title that matches all
            // SMS packages without specifying SMS qty.
            return 'رصيد SMS';
        }

        return '';
    }

    protected function getSubtitle(): string
    {
        return $this->hasPriceOptionsPerPeriod() ? 'مدة الاشتراك' : 'الرصيد المطلوب';
    }

    protected function getType(): string
    {
        $type = $this->sallaProduct->type;

        if (is_string($type)) {
            return $type;
        }

        return '';
    }

    /**
     * @throws Exception
     */
    protected function pluckPlans()
    {
        $currentSubscriptionPlan = store()->planInfo;
        $plan          = $this->sallaProduct;
        $planCampaign  = $this->sallaProduct->productPrices()->whereHas('utm', function ($query) {
            $query->where('utm_expire_at', '>', now()->format('Y-m-d'));
        })->exists();

        if (!$planCampaign) {
            $plan = app(SallaProductRepository::class)
                ->byTypeValue($this->sallaProduct->type_value)
                ->notSandbox()
                ->first();
        }

        $plan?->load([
            'productPrices' => function ($query) use ($currentSubscriptionPlan, $plan) {
                if (resolve(SallaProductRepository::class)->shouldApplyPeriodFilter($currentSubscriptionPlan, $plan)) {
                    $query->where('period', '>', $currentSubscriptionPlan->period)
                        ->when($this->sallaProduct->type_value === Plan::PRO, function ($query) use ($currentSubscriptionPlan) {
                            $query->where('product_id', $currentSubscriptionPlan->product_id);
                        })
                        ->when($this->sallaProduct->type_value === Plan::PLUS && $currentSubscriptionPlan->type_value === Plan::PRO, function ($query) {
                            $query->where('product_id', '!=', $this->sallaProduct->id);
                        });
                }

                $query->orderBy('price', 'DESC');
            },
            'page',
        ]);

        return $plan
            ->getRelation('productPrices')
            ->map(function ($sallaProductPrice) use ($plan) {
                return SallaSubscriptionPresenter::make(
                    $plan,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice));
            });
    }

    /**
     * @throws Exception
     */
    protected function pluckMobileAppPlans()
    {
        $plan = app(SallaProductRepository::class)
            ->byType(SallaProductType::MOBILE_APPS)
            ->notSandbox()
            ->with(['productPrices' => fn($q) => $q->orderBy('price', 'DESC'), 'page'])->first();

        return $plan
            ->getRelation('productPrices')
            ->map(function ($sallaProductPrice) use ($plan) {
                return SallaSubscriptionPresenter::make(
                    $plan,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice));
            });
    }

    protected function pluckSmsPackages()
    {
        return app(SallaProductRepository::class)
            ->smsPackages()
            ->get()
            ->map(function (SallaProducts $smsPackage) {
                $sallaProductPrice = $smsPackage->getRelation('productPrices')->first();

                return SallaPackagePresenter::make(
                    $smsPackage,
                    $sallaProductPrice,
                    $this->isSelected($sallaProductPrice));
            });
    }

    public function transformData()
    {
        $prices = $this->getOptions();

        if (is_array($prices)) {
            return [];
        }

        return [
            'title' => $this->getTitle(),
            'type' => $this->getType(),
            'icon' => $this->getIcon(),
            'prices' => $prices->map(function ($subscription) {
                return [
                    'id' => $subscription->product_price_id,
                    'title' => $subscription->title,
                    'duration' => $subscription->duration,
                    'can_renew' => $subscription->can_renew,
                    'selected' => $subscription->selected,
                    'price' => [
                        'amount' => $subscription->price->regular,
                        'currency' => 'ر.س',
                        'discount' => [
                            'amount' => $subscription->price->discounted,
                            'percentage' => store()->plan === 'basic' && $subscription->price->discount->value > 0 ?
                                $subscription->price->discount->percentage : '',
                        ]
                    ],
                    'gifts' => $subscription->gifts && store()->plan === 'basic' ? $subscription->gifts : '',
                ];
            })->toArray(),
        ];
    }

}