<?php

namespace Modules\MarketPlace\Presenter\Dashboard;

use Illuminate\Support\Optional;
use Modules\MarketPlace\Entities\SallaInstallment;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Repositories\SallaProductRepository;
use Salla\Core\Enum\Plan;
use Salla\Core\Presenters\BasePresenter;

class PlanPresenter extends BasePresenter
{
    public bool $is_current;

    public int $id;

    public string $name;

    public ?string $short_description;

    public ?string $description;

    public ?string $logo;
    public ?string $promotional;
    public ?bool $temporary_display;

    public object $default_price;

    public array $features = [];

    public array $product_features = [];

    public array $subscriptions = [];

    public ?object $renewal;

    public string $type;

    public ?string $color;

    public object $recommended;

    public object $upgrade_details;

    protected SallaProducts $plan;

    /* @var SallaSubscriptions|Optional $currentSubscription */
    protected $currentSubscription;

    public function __construct(SallaProducts $plan, SallaInstallment $lateInstallment = null)
    {
        $this->plan = $plan;
        $this->currentSubscription = $lateInstallment ? $lateInstallment->subscription : store()->planInfo ?? optional();

        $this->initialize();
    }

    protected function initialize(): void
    {
        $this->is_current = $this->currentSubscription->type_value === $this->plan->type_value;
        
        $this->id = optimus()->encode($this->plan->id);
        $this->type = $this->plan->type_value;
        $this->name = $this->plan->name;
        $this->short_description = $this->plan->short_description;
        $this->description = $this->plan->description;
        $this->logo = $this->plan->avatar;
        $this->promotional = $this->plan->promotional;
        $this->temporary_display = (bool)$this->plan->temporary_display;

        $this->features = $this->features();
        $this->product_features = $this->productFeatures();
        $this->subscriptions = $this->subscriptions();
        $this->default_price = $this->defaultPrice();
        $this->renewal = $this->renewalData();
        $this->color = $this->plan->color;
        $this->recommended = $this->getRecommendations();
        $this->upgrade_details = $this->getUpgradeDetails();
    }

    protected function defaultPrice(): object
    {
        if ($this->currentSubscription->period) {
            $value = $this->currentSubscription->sallaProduct->{$this->getColumnName()};
        } else {
            $value = $this->plan->getPrice()->getTaxedPriceAsFloat();
        }

        return (object)[
            'value' => (int)$value,
            'recurrence' => __tc('salla_product.subscription_by_period', $this->currentSubscription->period ?? 1),
        ];
    }

    protected function productFeatures(): array
    {
        $page = $this->plan->page ?? null;

        if (! $page) {
            return [];
        }

        $features = [];
        if (!$this->is_current) {
            $features = $page
                ->features
                ->map(function($feature) {
                    return (object)[
                        'title' => $feature['title'],
                        'description' => $feature['description'],
                        'icon' => $feature['icon'],
                    ];
                })
                ->values()
                ->toArray();
        }

        return $features;
    }

    protected function features(): array
    {
        return $this->plan->features->map->only('title', 'description', 'icon', 'icon_color', 'icon_background')->toArray();
    }

    protected function subscriptions(): array
    {
        $storeCurrentPeriod = 1;

        // to get the minimum subscription that he can renew/buy based on his current subscription period.
        if (store()->isPaid()) {
            $storeCurrentPeriod = (int)$this->currentSubscription->period;
        }

        $result = $this->plan
            ->productPrices
            ->where('period', '>=', $storeCurrentPeriod)
            ->filter(function(SallaProductPrice $productPrice) {
                return !mobileAgent()->isIos() || (mobileAgent()->isIos() && $productPrice->period == 1);
            })->map(function(SallaProductPrice $productPrice) {
                return SallaSubscriptionPresenter::make($this->plan, $productPrice, false, $this->is_current ? $this->currentSubscription : null);
            })->toArray();
        
        return sizeof($result) > 1 ? $result : array_values($result);
    }

    protected function renewalData(): object
    {
        if (! $this->is_current) {
            return new \stdClass();
        }

        $remainingDays = $this->currentSubscription->remainingDays();

        return (object)[
            'remaining' => (object)[
                'value' => $remainingDays,
                'type' => __tc('salla_product.subscription_remaining_days', $remainingDays),
                'withinReminderPeriod' => $this->currentSubscription->withinReminderPeriod(),
            ],
            'date' => $this->currentSubscription->date('end_date'),
            'is_auto_renewal' => $this->currentSubscription->isAutoRenewal()
        ];
    }

    protected function getRecommendations(): object
    {
        $value = false;
        $text = null;

        if ($this->plan->is_special) {
            $value = true;
            $text = $this->plan->special_text;
        }

        return (object)[
            'value' => $value,
            'text' => $text,
        ];
    }

    /**
     * Determine the column name based on plan period.
     */
    protected function getColumnName(): string
    {
        if ($this->currentSubscription->period == 1) {
            $column = 'price';
        } elseif ($this->currentSubscription->period == 12) {
            $column = 'annual_price';
        } else {
            $column = 'bi_annual_price';
        }

        return $column;
    }

    protected function getUpgradeDetails(): object
    {
        $productId = null;
        $productPriceId = null;

        if ($this->is_current && $this->plan->level == 2) {
            $product = app(SallaProductRepository::class)
                ->getUpgradePlan(2, (int)$this->currentSubscription->period);

            $productId = optimus()->encode($product->id);
            $productPriceId = optimus()->encode($product->getRelation('productPrices')->first()->id);
        }

        return (object)[
            'product_id' => $productId,
            'product_price_id' => $productPriceId,
        ];
    }
}