<?php

namespace Modules\MarketPlace\Presenter\Dashboard;

use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductType;
use Salla\Core\Enum\Plan;
use Salla\Core\Presenters\BasePresenter;

class SallaProductPriceObjectPresenter extends BasePresenter
{
    public float $regular;

    public float $discounted;

    public object $discount;

    protected SallaProducts $product;

    protected SallaProductPrice $price;

    public function __construct(SallaProducts $product, SallaProductPrice $price)
    {
        $this->product = $product;
        $this->price = $price;

        $this->initialize();
    }

    protected function initialize()
    {
        // has active special plan.
        if (store()->hasPlan(Plan::SPECIAL) && $this->product->type === 'plan') {
            $this->regular = $this->price->getPrice()->getTaxedPriceAsFloat();
            $this->discounted = $this->regular;
            $this->discount = $this->getDiscount();
            return;
        }

        // getTaxedPriceAsFloat() the returned price is already discounted when $price->period
        // is equal to 12. I don't know why!
        $discounted = $this->price->getPrice()->getTaxedPriceAsFloat();
        $currentPeriodRegularPrice = $this->getRegularPriceBasedOnBasePeriod($discounted);
        $discountValue = abs((int)($currentPeriodRegularPrice - $discounted));

        if (empty($this->product->price) || $this->product->price == 0) {
            $currentPeriodRegularPrice = $discounted = $this->price->getPrice()->getTaxedPriceAsFloat();
            $discountValue = 0;
        }

        $this->regular = $currentPeriodRegularPrice;
        $this->discounted = $discounted;
        $this->discount = $this->getDiscount($discountValue, $currentPeriodRegularPrice);
    }

    protected function getDiscount(int $discountValue = 0, float $currentPeriodRegularPrice = 1): object
    {
        return (object)[
            'value' => $discountValue,
            'percentage' => !empty($currentPeriodRegularPrice) ?
                (int)(($discountValue / $currentPeriodRegularPrice) * 100)
                : 0,
        ];
    }

    /**
     * In case of a "plan" type, I have consider the regular price is based on
     * the price of one month subscription * the number obtained from the "period" column.
     *
     * In any type other than "plan", I will return the default price which is usually
     * obtained from the salla_products_pricing table.
     */
    protected function getRegularPriceBasedOnBasePeriod(float $default): float
    {
        if ($this->product->type === SallaProductType::PLAN) {
            return (float)$this->product->price * (int)$this->price->period;
        }
        if ($this->product->type === SallaProductType::MOBILE_APPS) {
            return money($this->price->price, null, $this->price, withTaxIncluded: false)->getTaxedPriceAsFloat();
        }

        return $default;
    }
}
