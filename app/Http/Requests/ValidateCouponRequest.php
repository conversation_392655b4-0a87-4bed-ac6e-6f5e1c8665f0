<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use App\Models\Coupon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Marketing\Enum\CouponDiscountTypes;
use Modules\Marketing\Scopes\ExcludeNonSallaCouponsScope;
use Modules\StoreApp\Enum\AppliedIn;
use Salla\Paymetns\Models\PaymentMethod;
use Salla\Core\Traits\Helper\HasDecodeOptimus;
use Illuminate\Validation\ValidationException;
use Modules\MarketPlace\Enum\SallaProductPlanType;

class ValidateCouponRequest extends FormRequest
{
    use HasDecodeOptimus;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $existingRecord = $this->request;
        $types = [
            CouponDiscountTypes::FIXED,
            CouponDiscountTypes::PERCENT,
            'fixed',
            'Fixed',
            'percentage',
            'Percentage',
            'f',
            'p'
        ];

        $pTypes = [
            CouponDiscountTypes::PERCENT,
            'Percentage',
            'percentage',
            'p',
        ];

        $rules  = [
            'code' => [
                'nullable',
                'filled',
                'max:255',
                Rule::requiredIf(function () {
                    if ($this->get('is_group')) {
                        return false;
                    }

                    return true;
                }),
            ],
            'name' => ['sometimes', 'string', 'max:255'],
            'type' => ['required', Rule::in($types)],
            'amount' => 'required',
            'show_maximum_amount' => [
                'sometimes',
                'boolean',
                Rule::requiredIf(\in_array($this->get('type'), $pTypes)),
            ],
            'maximum_amount' => [
                'sometimes',
                'nullable',
                'numeric',
                Rule::requiredIf(\in_array($this->get('type'), $pTypes)),
                Rule::requiredIf($this->get('show_maximum_amount') == 1),
                'min:1',
                'max:999999',
            ],
            'marketing_show_maximum_amount' => 'nullable|required_if:marketing_active,1|boolean',
            'marketing_maximum_amount' => [
                'sometimes',
                'nullable',
                'numeric',
                Rule::requiredIf($this->get('marketing_show_maximum_amount') == 1 || $this->get('marketing_show_maximum_amount') == true),
                'min:1',
                'max:999999',
            ],
            'marketing_name'      => 'nullable|required_if:marketing_active,1,true',
            'marketing_type'      => 'nullable|required_if:marketing_active,1,true|in:P,p,F,f,Fixed,fixed,Percentage,percentage',
            'marketing_amount'    => [
                'nullable',
                'required_if:marketing_active,1,true',
                'numeric',
                'min:0',
                function ($attribute, $value, $fail) {
                    $type = $this->get('marketing_type');
                    $percentageTypes = ['P', 'p', 'Percentage', 'percentage'];
                    if (in_array($type, $percentageTypes) && $value > 100) {
                        $fail('نسبة العمولة يجب أن لا تتجاوز 100%');
                    }
                }
            ],
            'free_shipping'       => 'boolean|nullable',
            'payment_methods'           => ['nullable', 'array'],
            'payment_methods.*'         => ['nullable', 'string'],
            'applied_in'          => [
                'sometimes',
                'string',
                Rule::in(AppliedIn::APPLIEDINARRAY),
            ],
            'payment_method' => [
                'string',
                'nullable',
                Rule::in(
                    PaymentMethod::query()->select('slug')->where('status', 'enabled')
                        ->whereNotIn('slug', ['free'])->pluck('slug')->push('all')->toArray()
                )
            ],
            'group_name' => 'string|nullable|required_if:is_group,1',
            'group_coupons_count' => [
                'nullable',
                'min:1',
                Rule::requiredIf(function () {
                    if ($this->get('is_group') && $this->get('is_imported') =="0" && !$this->get('coupon_id')) {
                        return true;
                    }

                    return false;
                }),
                'integer',
                function ($attribute, $value, $fail) {
                    $hasSpecialPlan = store()->hasPlan(SallaProductPlanType::SPECIAL);

                    if ($hasSpecialPlan && $value > 100000) {
                        $fail('اقصى عدد لكل مجموعة هو 100000 كوبون');
                    } elseif (!$hasSpecialPlan && $value > 3000) {
                        $fail('اقصى عدد لكل مجموعة هو 3000 كوبون');
                    }
                },
            ],
            'is_apply_with_offer' => 'required|in:0,1',
        ];

        if ($this->get('coupon_id')) {
            $coupon = Coupon::withoutGlobalScope(ExcludeNonSallaCouponsScope::class)->find($this->get('coupon_id'));
            // If editing an existing coupon, apply additional rules
            $existingStartDate = Carbon::parse($coupon->start_date)->format('Y-m-d');
            $existingExpiryDate = Carbon::parse($coupon->expiry_date)->format('Y-m-d');


            $rules['start_date'] = [
                function ($attribute, $value, $fail) use ($existingExpiryDate) {
                $value = Carbon::parse($value)->format('Y-m-d');
                    if ($value == $existingExpiryDate && strtotime($value) > strtotime($existingExpiryDate)) {
                        $fail(__('coupon.messages.start_date.invalid'));
                    }
                },
            ];

            $rules['expiry_date'] = [
                function ($attribute, $value, $fail) use ($existingStartDate, $existingExpiryDate) {
                    $value = Carbon::parse($value)->format('Y-m-d');
                    if ($value !== $existingExpiryDate && strtotime($value) < strtotime($existingStartDate)) {
                        $fail(__('coupon.messages.start_date.before_expiry_msg'));
                    }
                },
                function ($attribute, $value, $fail) use ($existingStartDate, $existingExpiryDate) {
                    $start_date = Carbon::parse($this->get('start_date'))->format('Y-m-d');
                    $value = Carbon::parse($value)->format('Y-m-d');
                    if ($value < $start_date) {
                        $fail('تاريخ انتهاء الكوبون يجب ان يكون بعد تاريخ بداية الكوبون');
                    }
                },
            ];

        } else {
            // If creating a new coupon (not editing), apply these rules
            $rules['start_date'] = [
                'after_or_equal:' . date('Y-m-d H:i:s'),
            ];
            $rules['expiry_date'] = [
                'bail', 'after_or_equal:' . date('Y-m-d H:i:s'),
                function ($attribute, $value, $fail) {
                    if (!empty($this->get('start_date')) && $value < $this->get('start_date')) {
                        $fail(__('coupon.messages.start_date.before_expiry_msg'));
                    }
                },
            ];
        }

        if (isFromApi()) {
            $rules['code'] = [
                Rule::requiredIf(empty($this->get('is_group', null)) && empty($this->get('coupon_group_code', null))),
                'regex:/^' . preg_quote($this->get('type_coupon') === 'affiliate' ? 'F-' : '', '/') . '/',
                'max:20',
            ];

            $rules['group_suffix'] = [
                'required_if:is_group,1,true',
                'max:5',
                'regex:/^(?=.*[A-Za-z])[A-Za-z0-9]+$/',
            ];

            $rules['free_shipping'] = [
                'required',
                'boolean',
            ];

            $rules['amount'] = [
                'required',
                'numeric',
                Rule::when($this->get('free_shipping') == true, [
                    'min:0',
                    'max:999999',
                ]),
                Rule::when($this->get('free_shipping') == false, [
                    'min:1',
                    'max:999999',
                    function ($attribute, $value, $fail) use ($pTypes) {
                        if (in_array($this->get('type'), $pTypes) && $value > 100) {
                            $fail('The amount must not exceed 100 for this type.');
                        }
                    }
                ])
            ];

            $rules['maximum_amount'] =  [
                Rule::requiredIf(\in_array($this->get('type'), $pTypes)),
                Rule::requiredIf($this->get('show_maximum_amount') == true),
                function($attribute, $value, $fail) use ($pTypes) {
                    if(!in_array($this->get('type'), $pTypes) && !empty($value)) {
                        return $fail(__('marketing::coupon.dashboard_api.error.maximum_amount_not_required'));
                    }
                },
                'sometimes',
                'nullable',
                'numeric',
                'min:1',
                'max:999999',
            ];

            $rules['minimum_amount'] =  [
                'sometimes',
                'nullable',
                'numeric',
                'min:0',
                'max:999999',
            ];


            $rules['show_maximum_amount'] = [
                function($attribute, $value, $fail) use ($pTypes) {
                    if(!in_array($this->get('type'), $pTypes) && !empty($value)) {
                        return $fail(__('marketing::coupon.dashboard_api.error.show_maximum_amount_not_required'));
                    }
                }
            ];

            $rules['expiry_date']   = [
                request()->attributes->get('is_internal_api') ? 'sometimes' : 'required',
                'date',
                'after:' . date('Y-m-d'),
                function ($attribute, $value, $fail) {
                    if (! empty($this->get('start_date')
                        && $value < $this->get('start_date'))) {
                        return $fail(__('coupon.messages.start_date.before_expiry_msg'));
                    }
                },
            ];

            $rules['start_date']  = [
                request()->attributes->get('is_internal_api')
                ? ($this->get('expiry_date') ? 'before_or_equal:' . $this->get('expiry_date') : 'date')
                : 'after_or_equal:' . date('Y-m-d'),
            ];

            $rules['usage_limit'] = [
                'sometimes',
                'nullable',
                'integer',
                'min:1',
                'max:9999999999',
            ];

            $rules['usage_limit_per_user'] = [
                'sometimes',
                'nullable',
                'integer',
                'min:1',
                'max:9999999999',
            ];


            $rules['is_apply_with_offer'] = [
                'nullable',
                'boolean',
            ];

            //the follwoing keys are optinoal in api update only
            if($this->routeIs('api.v2.coupons.update'))
            {
                $rules['code'][] = 'sometimes';
                $rules['is_apply_with_offer'][] = 'sometimes';
                $rules['free_shipping'][] = 'sometimes';
                $rules['start_date'][] = 'sometimes';
                $rules['expiry_date'][] = 'sometimes';
                $rules['type'][] = 'sometimes';
                $rules['amount'] = [Rule::requiredIf(!empty($this->get('type')))];
            }
        }

        return $rules;
    }

    public function attributes()
    {
        return [
            'code'                      => 'رمز الكود',
            'coupon_group_code'         => 'رمز الكود',
            'group_suffix'              => 'لاحقة المجموعة', //ask for translation
            'type'                      => 'نوع الخصم',
            'amount'                    => 'كمية الخصم',
            'marketing_name'            => 'اسم المسوق',
            'marketing_type'            => 'نوع العمولة للمسوّق',
            'marketing_amount'          => 'نسبة العمولة',
            'applied_in'                => 'تطبيق الكوبون',
            'payment_method'            => 'طريقة الدفع',
            'group_name'                => 'اسم المجموعة',
            'group_coupons_count'       => 'عدد الكوبونات',
            'free_shipping'             => 'تطبيق الشحن',
            'exclude_sale_products'     => 'استثناء المنتجات المخفضة',
            'maximum_amount'            => 'المبلغ الأقصى للتخفيض',
            'marketing_maximum_amount'  => 'المبلغ الأقصى للعمولة',
            'expiry_date'               => __('coupon.expiry_date'),
            'start_date'                => __('coupon.start_date'),
        ];
    }

    public function messages()
    {
        $amount_message = $this->get('type') == 'P'? 'نسبة الخصم مطلوبة' : 'قيمة الخصم مطلوبة';

        return [
            'code.required'                => 'كود الكوبون مطلوب',
            'code.max'                     => 'كود الكوبون يجب ان لا يتجاوز 20 حرفاً',
            'coupon_group_code.required'   => 'كود الكوبون مطلوب',
            'coupon_group_code.max'        => 'كود الكوبون يجب ان لا يتجاوز 20 حرفاً',
            'coupon_group_code.regex'      => 'كود الكوبون يجب أن يحتوي حروف إنجليزية أو أرقام وبدون مسافات أو ـ -',
            'type.required'                => 'نوع الكوبون مطلوب',
            'amount.required'              => $amount_message,
            'maximum_amount.required'      => 'لابد أن يكون الحد الأقصى للتخفيض أكبر من صفر لإظهاره',
            'maximum_amount.min'           => 'لابد أن يكون المبلغ الأقصى للتخفيض أكبر من صفر حال إدخاله',
            'marketing_maximum_amount.required'=> 'لابد أن يكون المبلغ الأقصى للعمولة أكبر من صفر لإظهاره',
            'marketing_maximum_amount.min' => 'لابد أن يكون المبلغ الأقصى للعمولة أكبر من صفر حال إدخاله',
            'marketing_name.required_if'   => 'يرجى اضافة اسم المسوق للكوبون',
            'marketing_type.required_if'   => ' يرجى اضافة نوع كوبون التسويق',
            'marketing_amount.required_if' => ' يرجى اضافة نوع سعر التسويق',
            'applied_in.in' => 'يرجى التأكد من البيانات المدخلة',
            'group_suffix.required'     => 'كود مجموعة الكوبون مطلوب',
            'group_suffix.max'          => 'كود الكوبون يجب ان لا يتجاوز 5 حرفاً',
            'group_suffix.alpha_num'    => 'كود الكوبون يجب ان يحتوي حروف انجليزية وبدون مسافات او ـ -',
            'group_suffix.regex'        => 'كود الكوبون يجب ان يحتوي حروف انجليزية وبدون مسافات او ـ -',
            'group_suffix.unique'       => 'لقد قمت بإضافة مقطع بداية كوبونات بنفس القيمة من قبل',
            'group_name.required_if'    => 'اسم المجموعة مطلوب',
            'group_coupons_count.required_if' => 'عدد كوبونات المجموعة مطلوب',
            'group_coupons_count.max'         => 'اقصى عدد لكل مجموعة هو 3000 كوبون',
            'expiry_date.after_or_equal'      => __('coupon.messages.expiry_date_after'),
            'start_date.after_or_equal'       => __('coupon.messages.start_date.after_or_equal'),
            'free_shipping.boolean'           => 'يجب أن تكون قيمة حقل تطبيق الشحن إما نعم او لا',
            'is_apply_with_offer'             => 'يجب ان تكون القيمة true او false'
        ];
    }

    //throw code validation error if empty payload is recieved
    //TODO: make this as trait and use generic message
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (empty($this->validated()) && isApi()) {
                throw ValidationException::withMessages([
                    'code' => __('validation.required', ['attribute' => 'رمز الكود']),
                ]);
            }
        });
    }

    protected function prepareForValidation()
    {
        //if empty payload, let the request remain empty
        if(empty($this->all()) && isApi()){
            return;
        }

        //type is optinal in api update
        if(!$this->routeIs('api.v2.coupons.update'))
        {
            $type = $this->request->get('type');
            switch ($type) {
                case 'percentage':
                case 'Percentage':
                    $type = CouponDiscountTypes::PERCENT;
                    break;
                case 'fixed':
                case 'Fixed':
                    $type = CouponDiscountTypes::FIXED;
                    break;
                case 'p':
                case 'P':
                    $type = CouponDiscountTypes::PERCENT;
                    break;
                case 'f':
                case 'F':
                    $type = CouponDiscountTypes::FIXED;
                    break;
                case 'p':
                case 'P':
                    $type = CouponDiscountTypes::PERCENT;
                    break;
                case 'f':
                case 'F':
                    $type = CouponDiscountTypes::FIXED;
                    break;
            }
            $this->merge(['type' => $type]);
        }

        if (!$this->filled('applied_in')) {
            $this->merge(['applied_in' => "all"]);
        }

        $show_maximum_amount = ($this->has('show_maximum_amount') and
            ($this->get('show_maximum_amount') === 'on' or $this->get('show_maximum_amount') === true)
        );

        $marketing_show_maximum_amount = ($this->has('marketing_show_maximum_amount') and
            ($this->get('marketing_show_maximum_amount') === 'on' or $this->get('marketing_show_maximum_amount') === true)
        );

        $this->request->set('show_maximum_amount', $show_maximum_amount);
        $this->request->set('marketing_show_maximum_amount', $marketing_show_maximum_amount);

        if ($this->is('admin/*')) {

            $productsInclude = $this->get('products_include');
            $productsExclude = $this->get('products_exclude');
            $excludeShipping = $this->get('free_shipping') ? $this->get('list_exclude_shipping') : null;

            $productsInclude = ($productsInclude && !empty($productsInclude)) ? implode(',', $productsInclude) : null;

            $productsExclude = ($productsExclude && !empty($productsExclude)) ? implode(',', $productsExclude) : null;


            $this->merge(['products_include' => $productsInclude]);
            $this->merge(['products_exclude' => $productsExclude]);

            if ($excludeShipping) {
                $shipping_ids = array();
                $excludeShipping =  $this->decodeFakeIds($excludeShipping);
                foreach ($excludeShipping as $shippingId) {
                    if ($shippingId <= 50)
                        $shippingId =  -1 * abs($shippingId);

                    $shipping_ids[] = $shippingId;
                }
                $this->merge(['list_exclude_shipping' =>  $shipping_ids]);
            }

            if(!$this->filled('show_maximum_amount') || empty($this->get('show_maximum_amount'))) {
                $this->merge(['show_maximum_amount' => false]);
            }

            if(!$this->filled('marketing_maximum_amount') || empty($this->get('marketing_maximum_amount'))) {
                $this->merge(['marketing_maximum_amount' => '']);
            }

            if(!$this->filled('marketing_info') || empty($this->get('marketing_info'))) {
                $this->merge(['marketing_info' => '']);
            }

            if(!$this->filled('marketing_show_maximum_amount') || empty($this->get('marketing_show_maximum_amount'))) {
                $this->merge(['marketing_show_maximum_amount' => false]);
            }
        }
    }
}
