<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Modules\Languages\Traits\HasRequestTranslations;
use Salla\Core\Enum\Plan;

class ValidateOrderInstructionsRequest extends FormRequest
{
    use HasRequestTranslations;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        if (store()->plan ==  Plan::BASIC){
            throw ValidationException::withMessages(['*' => 'This setting is not supported in your current plan.']);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return $this->langRules([
            'thank_title'                        => 'required|string',
            'order_instructions_payment_waiting' => 'required|string',
            'order_instructions_under_review'    => 'required|string',
            'order_instructions_completed'       => 'required|string',
        ]);
    }

    public function attributes()
    {
        return [
            'order_instructions_payment_waiting' => 'رسالة اكتمال الطلب قبل تأكيد الدفع',
            'order_instructions_under_review'    => 'رسالة اكتمال الطلب بعد تأكيد الدفع',
            'order_instructions_completed'       => 'رسالة اكتمال الطلب بعد التحويل إلى تم التنفيذ',
            'thank_title'                        => 'العنوان البارز',
        ];
    }
}
