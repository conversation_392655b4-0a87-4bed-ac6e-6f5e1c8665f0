<?php

namespace App\Http\Requests;

use App\Models\Page;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Languages\Traits\HasRequestTranslations;
use Salla\Core\Facades\Store;
use Salla\Core\Rules\MaxLengthWithoutHtmlRule;
use Salla\FeatureRules\Facades\FeaturesManager;

/**
 * @property string $page_id
 * @property string $title
 * @property string $content
 * @property string $type
 * @property int $category_id
 * @property int $products_id
 * @property int $order
 * @property string $custom_url
 * @property string $metadata_description
 * @property string $metadata_title
 * @property bool|null $show_in_footer
 */
class ValidatePageRequest extends FormRequest
{
    use HasRequestTranslations;

    protected $lang;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        if ($this->page_id) {
            $this['page_id'] = hasDecode($this->page_id);
        }

        $this['content'] = $this->get('content') ?: '';
        if ($this->type != 'html_code') {
            $this['content'] = replaceBadTags($this['content']);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return $this->langRules(
            [
                'title'                => 'required|min:2',
                'content'              => ['required', new MaxLengthWithoutHtmlRule(30000)],
                'metadata_description' => 'sometimes|nullable|max:320|regex:/^[\p{L}\p{N} ,-]+$/u',
                'metadata_title'       => 'sometimes|nullable|min:2|max:150|regex:/^[\p{L}\p{N} ,-]+$/u',
                'custom_url'           => 'sometimes|nullable|max:150|regex:/^[\p{L}\p{N} ,-]+$/u',
                'type'                 => [
                    'required',
                    'in:general,return_policy,privacy_policy,html_code',
                    function ($_, $value, $fail) {
                        if ($value == 'html_code' && !feature('theme-customization')->isHaveFeature()) {
                            $fail(feature('theme-customization')->getMessage());
                        }
                    },
                ],
            ]);
    }

    public function messages()
    {
        return [
            'metadata_description.regex' => 'وصف الصفحة يجب ان لاتحتوى على حروف خاصة مثل ـ-@ او سطر جديد ',
            'metadata_title.regex'       => 'عنوان الصفحة لمحركات البحث يجب ان لاتحتوى على حروف خاصة مثل ـ-@ او سطر جديد ',
        ];
    }

    public function readyData()
    {
        $isBasic = Store::getPlan() == 'basic';
        $custom_url = feature('seo-meta-tag')->isHaveFeature() ? $this->custom_url : null;

        $data    = [
            'title'       => $this->title,
            'slug'        => $custom_url ?: slugArabic($this->title),
            'content'     => $this->get('content'),
            'type'        => $isBasic ? 'general' : (in_array($this->type, ['', '-']) ? 'general' : $this->type),
            'products_id' => $isBasic ? null
                : ($this->type == 'landing_category' ? $this->category_id : $this->products_id),
            'show_in_footer' => $this->show_in_footer === null ? false : true,
        ];

        return array_merge($data, $this->getTrans());
    }

    public function getTrans(): array
    {
        $data  = [];
        $trans = $this->getTranslations();
        foreach ($trans as $langCode => $attrs) {
            $data[$langCode]            = $attrs;
            if(isset($data[$langCode]['content'])){
                $data[$langCode]['content'] = replaceBadTags($attrs['content']);
            }
            $data[$langCode]['slug']    = $attrs['custom_url'] ?? slugArabic($attrs['title']);
            unset($data[$langCode]['custom_url']);

            if(! feature('seo-meta-tag')->isHaveFeature()) {
                $data[$langCode]['slug'] = slugArabic($attrs['title']);
                unset($data[$langCode]['metadata_title'], $data[$langCode]['metadata_description']);
            }
        }

        return $data;
    }
}
