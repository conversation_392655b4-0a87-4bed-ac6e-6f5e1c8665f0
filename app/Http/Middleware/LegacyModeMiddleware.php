<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\ThemeCustomization\Actions\ForceNewDashboardAction;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class LegacyModeMiddleware
{
    // Routes that are different in the new dashboard
    protected $iframeRoutes = [
        'cp.short_url'            => 'url/{id}/{type}',
        'cp.order.product.search' => 'order/product_search',
        'app.stream' => 'app/stream',
        'cp.advertisements.create' => 'advertisements/create',
        'cp.advertisements.edit' => 'advertisements/{advertisement}/edit',
        'cp.advertisements.index' => 'advertisements',
        'cp.announcement.close' => 'announcement/{id}/close',
        'cp.announcement.show' => 'announcement/{id}',
        'cp.app.alerts' => 'app/alerts',
        'cp.app.contact-us' => 'app/contact-us',
        'cp.app.more' => 'app/more',
        'cp.app.store.location' => 'app/store/location/{location?}/{city?}/{country?}',
        'cp.bank-offers' => 'bank-offers',
        'cp.bank-offersindex' => 'bank-offers/{any?}',
        'cp.blog.articles.create' => 'blog/articles/create',
        'cp.blog.articles.edit' => 'blog/articles/{article}/edit',
        'cp.blog.articles.index' => 'blog/articles',
        'cp.blog.articles.search' => 'blog/search',
        'cp.blog.categories.index' => 'blog/categories',
        'cp.blog.image.index' => 'blog/{article}/image',
        'cp.blog.image.show' => 'blog/{article}/image/{image}',
        'cp.campaign' => 'campaign',
        'cp.campaign.create' => 'campaign/create',
        'cp.campaign.customers' => 'campaign/customers/{id}',
        'cp.campaign.edit' => 'campaign/edit/{id}',
        'cp.campaign.filters' => 'campaign/filters/{id}',
        'cp.campaign.stats' => 'campaign/stats/{id}',
        'cp.cashback-offers' => 'cashback-offers',
        'cp.cashback-offersindex' => 'cashback-offers/{any?}',
        'cp.cities' => 'cities/{country}',
        'cp.complaints.index' => 'complaints',
        'complaints.services.show' => '{complaint_number}/{user_code}',
        'complaints.index' => '/',
        'complaints.services.index' => '{complaint_service}',
        'complaints.services.authenticate.logout' => '{complaint_number}/{user_code}/logout',
        'complaints.services.print_refund_receipt' => '{complaint_number}/{user_code}/print_refund_receipt',
        'complaints.services.order' => '{complaint_number}/{user_code}/order',
        'complaints.services.refund' => '{complaint_number}/{user_code}/{id}/refund',
        'complaints.merchant.services.create' => '/merchant-services',
        'cp.conditional-offers' => 'conditional-offers',
        'cp.conditional-offersindex' => 'cashback-offers/{any?}',
        'cp.contacts.resend.active_code' => 'settings/contacts/mobile/resend_active_code',
        'cp.coupon.browse_orders' => 'coupons/coupon/browse/{coupon}/orders',
        'cp.coupon.create' => 'coupons/coupon/{id?}',
        'cp.coupon.export_group_coupons' => 'coupons/export/group/coupons/{coupon}',
        'cp.coupon.group_coupons_list' => 'coupons/group/{coupon}',
        'cp.coupon.list' => 'coupons/coupon_list',
        'cp.coupon.marketing_coupon' => 'coupons/marketing-coupon/{id}',
        'cp.coupons' => 'coupons',
        'cp.customers.cities' => 'customers/cities',
        'cp.customers.create' => 'customers/create',
        'cp.customers.filtered.export' => 'customers/filtered/export',
        'cp.customers.loyaltyPoints' => 'customers/loyalty_points/{customer}',
        'cp.dashboard.products.load-more' => 'dashboard/show_products/load-more/{next?}',
        'cp.dashboard.zoho.callback' => 'zoho-books/callback',
        'cp.development_store.coupon.remove' => 'store-dev/{cart}/coupon/remove',
        'cp.development_store.logout' => 'store-dev/logout',
        'cp.domain.index' => 'domain',
        'cp.help-center.panel.article' => 'help-center/panel/article/{article}',
        'cp.help-center.panel.list' => 'help-center/panel/list',
        'cp.help-center.panel.search' => 'help-center/panel/search',
        'cp.help-center.redirect' => 'help-center/redirect',
        'cp.help_center.show_article' => 'help_center/{id}',
        'cp.help_center.temp' => 'help_center',
        'cp.languages.editor' => 'languages/editor/{locale?}/{model?}',
        'cp.languages.logs' => 'languages/logs',
        'cp.languages.settings' => 'languages/settings',
        'cp.languages.summary' => 'languages/summary/{locale?}',
        'cp.loyalty-system.index' => 'loyalty-system',
        'cp.marketing.abandoned_carts.index' => 'marketing/abandoned_carts',
        'cp.marketing.abandoned_carts.rule.create' => 'marketing/abandoned_carts/rule/create',
        'cp.marketing.abandoned_carts.rule.delete' => 'marketing/abandoned_carts/{cart}/delete',
        'cp.marketing.abandoned_carts.rule.edit' => 'marketing/abandoned_carts/{cart}/edit',
        'cp.marketing.abandoned_carts.rule.index' => 'marketing/abandoned_carts/rule/index',
        'cp.marketing.abandoned_carts.show' => 'marketing/abandoned_carts/{cart}',
        'cp.marketing.affiliate.create' => 'marketing/affiliate/create',
        'cp.marketing.affiliate.edit' => 'marketing/affiliate/{affiliate}/edit',
        'cp.marketing.affiliate.index' => 'marketing/affiliate',
        'cp.marketing.affiliate.report' => 'marketing/affiliate/report/{affiliate}',
        'cp.marketing.affiliate.share' => 'marketing/affiliate/share/{affiliate}',
        'cp.marketing.calender' => '/marketing/calender',
        'cp.marketing.celebrity_marketing' => 'marketing/celebrity_marketing',
        'cp.marketing.customer-wallet.index' => 'marketing/customer-wallet',
        'cp.marketing.marketing_settings' => 'marketing/marketing_settings',
        'cp.marketing.marketing_tools' => 'marketing/marketing_tools',
        'cp.marketing.partnerships.index' => 'marketing/partnerships',
        'cp.marketing.partnerships.show' => 'marketing/partnerships/{partnership}',
        'cp.marketplace.' => 'marketplace/store-plan',
        'cp.marketplace.apps.delete-permanent' => 'apps/delete/{app}',
        'cp.marketplace.apps.on_boarding' => 'apps/{app}/on-boarding',
        'cp.marketplace.apps.on_boarding.store' => 'apps/{app}/on-boarding',
        'cp.marketplace.apps.review' => 'apps/{app}/review',
        'cp.marketplace.apps.uninstall_confirm' => 'apps/uninstall-confirm/{app}',
        'cp.marketplace.apps.upgrade-plan' => 'apps/upgrade-plan/{app}',
        'cp.marketplace.apps.install.authorize' => 'apps/authorize/{app_id}',
        'cp.marketplace.apps.install.select_plan' => 'apps/plan/{app_id}',
        'cp.marketplace.apps.uninstall' => 'apps/uninstall/{app}',
        'cp.marketplace.apps.perform-uninstall' => 'apps/uninstall/{app}',
        'cp.marketplace.apps.update-rating' => 'apps/rating/{app}/edit',
        'cp.marketplace.apps.settings' => 'apps/settings',
        'cp.marketplace.holding.approve' => 'marketplace/payments/approve-holding',
        'cp.marketplace.apps.access-request.reject' => 'apps/request/{app_access_request}/reject',
        'cp.marketplace.apps.delete-waiting-payment' => 'apps/{app}/waiting-payment',
        'cp.marketplace.apps.rating' => 'apps/rating/{app}',
        'cp.marketplace.cart' => 'marketplace/cart',
        'cp.marketplace.cart.delete' => 'marketplace/cart/delete/{cartItem}',
        'cp.marketplace.cart.add' => 'marketplace/cart/add/{product}',
        'cp.marketplace.cart.pay' => 'marketplace/cart/{cart}/pay',
        'cp.marketplace.cart.confirm' => 'marketplace/cart/{cart}/confirm',
        'cp.marketplace.cart.coupon.apply' => 'marketplace/cart/{cart}/coupon',
        'cp.marketplace.cart.coupon.remove' => 'marketplace/cart/{cart}/coupon/remove',
        'cp.marketplace.cart.payment_method' => 'marketplace/cart/{cart}/payment_method',
        'cp.marketplace.cart.update' => 'marketplace/cart/update/{cartItem}',
        'cp.marketplace.cart.update-feature' => 'marketplace/cart/update/{cartItem}/feature/{priceFeature}',
        'cp.marketplace.cart.update.option' => 'marketplace/cart/update/{product}/option',
        'cp.marketplace.credit-cards.index' => 'marketplace/credit-cards',
        'cp.marketplace.credit-cards.main' => 'marketplace/credit-cards/{credit_card}/main',
        'cp.marketplace.invoices' => 'marketplace/invoices',
        'cp.marketplace.invoices.create_cart' => 'marketplace/invoices/create_cart',
        'cp.marketplace.invoices.print' => 'marketplace/invoices/{invoice}/print',
        'cp.marketplace.invoices.restore' => 'marketplace/invoices/restore',
        'cp.marketplace.invoices.show' => 'marketplace/invoices/{invoice}',
        'cp.marketplace.payment.failed' => 'marketplace/payment/{cart}/failed',
        'cp.marketplace.payment.landing' => 'marketplace/payment/{cart}',
        'cp.marketplace.payment.success' => 'marketplace/payment/{cart}/success',
        'cp.marketplace.plans.upgrade' => 'marketplace/plans/upgrade/{source?}/{product_id?}',
        'cp.marketplace.product' => 'marketplace/product/{product}',
        'cp.marketplace.product.gifts' => 'marketplace/product/{product}/gifts/{productPriceId}',
        'cp.marketplace.product.policy' => 'marketplace/product/policy',
        'cp.marketplace.product.rating' => 'marketplace/product/{product}/rating',
        'cp.marketplace.holding.approve ' => 'marketplace/payments/approve-holding',
        'cp.marketplace.referrals.details' => 'marketplace/referrals/details',
        'cp.marketplace.referrals.loyalty_points' => 'marketplace/referrals/loyalty-points',
        'cp.marketplace.referrals.statistics' => 'marketplace/referrals/statistics',
        'cp.marketplace.themes.' => 'marketplace/themes/{fallbackPlaceholder}',
        'cp.marketplace.themes.index' => 'marketplace/themes',
        'cp.marketplace.themes.preview' => 'marketplace/themes/{theme_id}/preview',
        'cp.marketplace.themes.show.redirect' => 'marketplace/themes/direct/{theme_id}',
        'cp.marketplace.themes.subscriptions' => 'marketplace/themes/subscriptions',
        'cp.marketplace.themes.app.new' => '_app/themes',
        'cp.design.show' => 'themes/editor/{product}',
        'cp.message.get_sms_balance' => 'message/get_sms_balance',
        'cp.orders.settings.customize_invoice' => 'orders/customize_invoice',
        'cp.pages' => 'pages',
        'cp.pages.create' => 'pages/page/{id?}',
        'cp.payment' => 'payment',
        'cp.payment.bank.create' => 'payment/bank/create',
        'cp.payment.bank.edit' => 'payment/bank/{bank}/edit',
        'cp.payment.bank.index' => 'payment/bank',
        'cp.payment.cod.history.confirm' => 'payment/cod/history/{payment}/confirm',
        'cp.payment.cod.history.confirm_all' => 'payment/cod/history/confirm_all',
        'cp.payment.cod.history.export.index' => 'payment/cod/history/export',
        'cp.payment.cod.history.export.show' => 'payment/cod/history/{payment}/export',
        'cp.payment.cod.history.index' => 'payment/cod/history',
        'cp.payment.cod.history.print' => 'payment/cod/history/{payment}/print',
        'cp.payment.cod.history.show' => 'payment/cod/history/{payment}',
        'cp.payment.credit_notes.index' => 'payment/credit-notes',
        'cp.payment.credit_notes.show' => 'payment/credit-notes/{creditNote}',
        'cp.payment.gateways.comparison' => 'payment/gateways/comparison',
        'cp.payment.history.confirm' => 'payment/history/{payment}/confirm',
        'cp.payment.history.confirm_all' => 'payment/history/confirm_all',
        'cp.payment.history.export.index' => 'payment/history/export',
        'cp.payment.history.export.show' => 'payment/history/{payment}/export',
        'cp.payment.history.index' => 'payment/history',
        'cp.payment.history.print' => 'payment/history/{payment}/print',
        'cp.payment.history.show' => 'payment/history/{payment}',
        'cp.payment.invoice.index' => 'payment/invoices',
        'cp.payment.invoice.show' => 'payment/invoices/{invoice}',
        'cp.payment.methods.apple-pay.csr' => 'payment/methods/apple-pay/csr',
        'cp.payment.rules.create' => 'payment/rules/create',
        'cp.payment.rules.edit' => 'payment/rules/{rule}/edit',
        'cp.payment.rules.index' => 'payment/rules',
        'cp.payment.unbilled_transactions.index' => 'payment/unbilled-transactions',
        'cp.rating' => 'rating',
        'cp.settings.avatar' => 'settings/avatar',
        'cp.settings.external_services' => 'services/integration',
        'cp.settings.external_services.google_analytics' => 'services/integration/google-analytics/redirect',
        'cp.settings.external_services.google_redirect' => 'services/integration/google/redirect',
        'cp.settings.external_services.show_component' => 'services/integration/{component}',
        'cp.settings.force-restore-settings' => 'settings/force-restore-settings',
        'cp.settings.seo' => 'settings/seo',
        'cp.settings.seo.index' => 'settings/seo/meta_data',
        'cp.settings.show_component' => 'settings/component/{component}',
        'cp.settings.show_generate_epayment' => 'settings/show_generate_epayment',
        'cp.settings.show_generate_quick_prurchase' => 'settings/show_generate_quick_prurchase',
        'cp.settings.webhooks.get_form' => 'settings/webhooks/get_form/{id?}',
        'cp.shipping.activate.ajeek.refresh-branches' => 'shipping/companies/ajeek/refresh-branches',
        'cp.shipping.activate.barq.register' => 'shipping/companies/barq/register',
        'cp.shipping.activate.labaih.refresh-pickup-addresses' => 'shipping/companies/labaih/refresh-pickup-addresses',
        'cp.shipping.activate.saee.register' => 'shipping/companies/saee/register',
        'cp.shipping.company.rating' => 'shipping/company/{company}/rating',
        'cp.shipping.company.supported_cities' => 'shipping/company/supported_cities/{company}/{country}',
        'cp.shipping.customs.companies.create' => 'shipping/customs/companies/create',
        'cp.shipping.customs.companies.edit' => 'shipping/customs/companies/{company}/edit',
        'cp.shipping.customs.companies.show' => 'shipping/customs/companies/{company}',
        'cp.shipping.freeshipping' => 'shipping/freeshipping',
        'cp.orders.cash_on_delivery_settings' => 'orders/cash_on_delivery_settings',
        'cp.shipping.get_cities' => 'shipping/country/{country?}',
        'cp.shipping.get_shipping_companies' => 'shipping/rules/get_shipping_companies',
        'cp.shipping.packaging_orders' => 'shipping/packaging_orders',
        'cp.shipping.packaging_orders.show' => 'shipping/packaging_orders/{id}',
        'cp.shipping.private.company.details' => 'shipping/company/{company}',
        'cp.shipping.rules.active' => 'shipping/rules/active',
        'cp.shipping.rules.create' => 'shipping/rules/create',
        'cp.shipping.rules.de_active' => 'shipping/rules/de_active',
        'cp.shipping.rules.edit' => 'shipping/rules/{rule}/edit',
        'cp.shipping.settings.show' => 'shipping/settings/show',
        'cp.shipping.shipment_history' => 'shipping/shipment_history/{company?}/{type?}',
        'cp.shippingcustom-companies' => 'shipping/custom-companies',
        'cp.special_offers' => 'specialoffer',
        'cp.special_offers.create' => 'specialoffer/create',
        'cp.special_offers.edit' => 'specialoffer/edit/{offer}',
        'cp.special_offers.report' => 'specialoffer/report/{offer}',
        'cp.store.subscription.prepare-delete' => 'store/subscription/prepare-delete',
        'cp.store_document.index' => 'entity/verification',
        'cp.store_document.info' => 'entity/verification/info',
        'cp.store_document.verify' => 'entity/verification/verify',
        'cp.store_document.verify.info' => 'entity/verification/verify/info',
        'cp.store_taxes.create' => 'app/salla_store_tax/store_tax/{id?}',
        'cp.violation.show' => 'violation/{violation}',
        'cp.wallet.index' => 'wallet',
        'cp.wallet.subscription.export' => 'wallet/subscription/export',
        'cp.wallet.transactions.export' => 'wallet/transactions/export',
        'cp.wallet.transactions.index' => 'wallet/transactions',
        'cp.wallet.transactions.print' => 'wallet/transactions/{transaction}/print',
        'cp.wallet.transactions.recent' => 'wallet/transactions/recent',
        'cp.wallet.transactions.show' => 'wallet/transactions/{transaction}',
        'cp.wallet.recharge.success' => 'wallet/recharge/{transaction}/success',
        'cp.wallet.recharge.failure' => 'wallet/recharge/{transaction}/failure',
        'cp.whatsapp.chat.contacts' => 'services/whatsapp/contacts',
        'cp.whatsapp.chat.create' => 'services/whatsapp/chat/create',
        'cp.whatsapp.chat.edit' => 'services/whatsapp/chat/{chat}/edit',
        'cp.whatsapp.chat.index' => 'services/whatsapp/chat',
        'cp.whatsapp.chat.media' => 'services/whatsapp/chat/media/{type}/{id}',
        'cp.whatsapp.chat.show' => 'services/whatsapp/chat/{chat}',
        'cp.whatsapp.index' => 'services/whatsapp',
        'cp.whatsapp.order.info' => 'services/whatsapp/{customerId}/last-order',
        'customer.groups.edit' => 'customers/groups/groups/{id}',
        'dashboard.addons' => 'addons/{slug}/{optional?}',
        'demo.redirect' => 'demo/redirect',
        'impex.export' => 'export/products-template',
        'impex.export-product-hs-codes' => 'export/product-hs-codes',
        'impex.export-product-orders' => 'export/product-orders',
        'impex.export-product-prices' => 'export/product-prices',
        'impex.export-product-quantities' => 'export/product-quantities',
        'impex.export-product-seo' => 'export/product-seo',
        'impex.export-products' => 'export/products',
        'locales.list' => 'locales',
        'mahly.index' => 'mahally',
        'mahly.remaining.unlinked.categories' => 'mahly/categories/remaining',
        'marketing_integration.oauth.sweply.snapchat.auth' => 'marketing-integration/snapchat/oauth',
        'marketing_integration.webhooks.sweply' => 'marketing-integration/webhooks/sweply/{campaign}',
        'mobile_app.index' => 'mobile-app',
        'mobile_app.info' => 'mobile-app/app-info',
        's3_browser_based_uploads.credentials' => 's3_browser_based_uploads/credentials/{disk?}',
        'translations.character-count-progress' => 'translations/character-count-progress/{locale}',
        'translations.detailed-summaries' => 'translations/detailed-summaries/{locale}',
        'translations.logs' => 'translations/logs',
        'translations.logs-breakdown' => 'translations/logs-breakdown',
        'translations.model-references' => 'translations/model-references',
        'translations.models' => 'translations/models/{modelReference}/locales/{targetLocale}',
        'translations.progress' => 'translations/progress/{locale}',
        'update.app' => 'update-app',
        'update.skip' => 'update-skip',
        'products.sorting.get_categories' => '/products/sorting/categories',
        'products.sorting.index' => '/products/sorting/categories/{category}/product',
        'cp.products.sorting' => '/products/sorting',
        'marketing_integration.oauth.snapchat.callback' => '/marketing/events-manager',
        'cp.settings.external_services.mailchimp_marketing.oauth_callback' => 'services/integration/mailchimp/oauth/callback',
        //Redirect to internal page
        'cp.store_branch.stock.management' => 'branches/stock/management',
        'cp.whatsapp.index' => 'services/whatsapp',

    //        'cp.app.suggestions' => 'app/suggestions',
//        'cp.design' => 'design/{fallbackPlaceholder}',
//        'cp.design.fallback' => 'design',
//        'cp.design.show' => 'design/{product}',
//        'cp.features' => 'features',
//        'cp.features.login' => 'features/login',
//        'cp.logout' => 'logout',
//        'cp.marketplace.preview_theme' => 'preview_theme/{id}',

    ];

    // Routes that are identical in the new dashboard
    protected $hyperRoutes = [
        'cp.marketing.affiliate.index' => '/marketing/affiliate',
        'cp.shipping' => '/shipping',
        'cp.shipping.tickets' => '/shipping/tickets',
        'cp.shipping.tickets.show' => '/shipping/tickets/{id}',
        'cp.shipping.rules.index' => '/shipping/rules',
        'cp.marketplace.index' => 'marketplace',
        'cp.marketplace.apps.index' => '/apps',
        'cp.marketplace.apps.bulk_install' => '/apps/bulk-install',
        'cp.marketplace.apps.install' => 'apps/install/{app_id}',
        'cp.store_branch.stock.management' => '/products/branches/stock-management',
        'cp.development_store.index' => 'store-dev',
    ];

    protected $forceNewDashboardRoutes = [
        'cp.marketplace.themes*' => ForceNewDashboardAction::class,
        'cp.design*' => ForceNewDashboardAction::class,
    ];

    public function handle(Request $request, Closure $next)
    {
        $isLegacy = (int) $request->get('legacy', $request->cookie('legacy', 1));
        $request->attributes->set('legacy', $isLegacy);
        /**
         * @var ForceNewDashboardAction $forceNewDashboardAction
         */
        $forceNewDashboardAction = collect($this->forceNewDashboardRoutes)->first(fn($_, $routePattern) => $request->routeIs($routePattern));
        if ($forceNewDashboardAction && ($response = $forceNewDashboardAction::make()->run())) {
            return $response;
        }

        if (!$request->has('legacy')) {
            return $this->handleResponse($request, $next);
        }

        // Get the response from the next middleware/controller
        $response = $this->next($request, $next);

        // Only attach cookie if response supports cookies
        if (!$this->canAttachCookie($response)) {
            return $response;
        }
        
        if($request->attributes->get('legacy') == "1") {
            $response->cookie(
                'from_old_dashboard', 1, 60 * 24 * 7, // 7 days
                null, config('session.domain'), true, // secure
                false // httpOnly
            );
        }
        return $response->cookie(
            'legacy',
            $request->attributes->get('legacy'),
            60 * 24 * 7, // 7 days
            null,
            config('session.domain'),
            true, // secure
            false // httpOnly
        );
    }

    /**
     * Check if the response can accept cookies
     *
     * @param mixed $response
     * @return bool
     */
    protected function canAttachCookie($response): bool
    {
        return method_exists($response, 'cookie');
    }

    protected function handleResponse(Request $request, Closure $next)
    {
        $response = $this->next($request, $next);

        $mode = $request->get('mode', $request->header('s-mode', $request->server('HTTP_SEC_FETCH_DEST')));

        // Check if response is a redirect and handle iframe mode
        if (!($response instanceof RedirectResponse && $mode === "iframe")) {
            return $response;
        }

        $targetUrl = $response->getTargetUrl();

        // Parse the URL and its query parameters
        $parsedUrl = parse_url($targetUrl);
        $query = [];
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $query);
        }

        // Add mode=iframe to query parameters if not already present
        if (!isset($query['mode'])) {
            $query['mode'] = 'iframe';
        }

        // Rebuild the URL with the new query parameter
        $newUrl = $parsedUrl['path'];
        if (!empty($query)) {
            $newUrl .= '?' . http_build_query($query);
        }
        if (isset($parsedUrl['fragment'])) {
            $newUrl .= '#' . $parsedUrl['fragment'];
        }

        // Create new redirect response with modified URL
        return redirect($newUrl)->setStatusCode($response->getStatusCode());
    }


    /**
     * @param Request $request
     * @param Closure $next
     * @return \Illuminate\Http\Response|mixed
     */
    public function next(Request $request, Closure $next): mixed
    {
        if ($request->attributes->get('legacy') || $request->method() !== "GET") {
            return $next($request);
        }

        // contune serve iframe routes
        if (isset($this->iframeRoutes[Route::currentRouteName()]) || $request->server('HTTP_SEC_FETCH_DEST') === "iframe") {
            return $next($request);
        }

        if (isset($this->hyperRoutes[Route::currentRouteName()]) && $request->get('mode') === "iframe") {
            return $next($request);
        }

        if ($request->method() === "GET" && isset($this->hyperRoutes[Route::currentRouteName()]) && $request->get('mode') === "iframe") {
            return $next($request);
        }

        // handle get request they match old routes
        return response()->view('cp.layouts.new-dashboard');
    }
}
