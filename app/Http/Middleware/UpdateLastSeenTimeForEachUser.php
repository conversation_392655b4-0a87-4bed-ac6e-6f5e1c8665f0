<?php

namespace App\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Salla\LaravelOtp\Services\OtpService;

class UpdateLastSeenTimeForEachUser
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $this->updateCustomerLocation($request);

        return $next($request);
    }

    public function updateCustomerLocation($request)
    {
        if (!auth()->check()) {
            return;
        }

        if (
            !app(OtpService::class)->setReceipent(auth()->user())->sessionIsVerified() &&
            !request()->attributes->get('token_verified')
        ) {
            return;
        }

        if (is_null(auth()->user()->last_seen) || !Carbon::parse(auth()->user()->last_seen)->greaterThan(now()->subMinutes(30))) {
            auth()->user()->update([
                'last_seen' => Carbon::now(),
                'last_url' => $request->url(),
                'ip_address' => request()->ip(),
                'ip_city' => request()->header('cf-ipcity'),
                'ip_country' => request()->header('cf-ipcountry'),
            ]);
        }

        if (store()->hasStore()) {
            // Update the store's last seen time if it has not been updated in the last 30 minutes or the last seen is null
            if (is_null(store()->last_seen) || !Carbon::parse(store()->last_seen)->greaterThan(now()->subMinutes(30))) {
                store()->update(['last_seen' => now()]);
            }
        }
    }
}
