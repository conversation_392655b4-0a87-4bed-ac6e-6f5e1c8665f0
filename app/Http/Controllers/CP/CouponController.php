<?php

namespace App\Http\Controllers\CP;

use App\Excel\Exports\CouponExport;
use App\Excel\Exports\CouponExportFastExcel;
use App\Http\Requests\CouponReportRequest;
use App\Http\Requests\ValidateCouponRequest;
use App\Http\Requests\ValidateSearchCouponRequest;
use App\Models\Coupon;
use App\Scopes\CouponScope;
use App\Services\CouponService;
use Carbon\Carbon;
use Salla\Core\Enum\Plan;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Excel;
use Modules\Marketing\Actions\Coupon\ChangeCouponStatus;
use Modules\Marketing\Enum\CouponTypes;
use Modules\Marketing\Http\Requests\Dashboard\CouponsRequest;
use Modules\Marketing\Jobs\NotifyCouponsCompletedExport;
use Modules\Marketing\Repositories\CouponRepository;
use Modules\Marketing\Repositories\Criteria\CouponsFilterCriteria;
use Modules\Marketing\Scopes\ExcludeNonSallaCouponsScope;
use Modules\SpecialOffer\Actions\ExportCouponReportAction;
use Modules\SpecialOffer\Jobs\CouponExportToMailJob;
use Salla\FeatureRules\Facades\FeaturesManager;
use Symfony\Component\HttpFoundation\Response;
use \Validator;

class CouponController
{
    private $request;

    private $couponService;

    private $couponRepository;

    public function __construct(Request $request, CouponService $couponService, CouponRepository $couponRepository)
    {
        $this->request = $request;
        $this->couponService = $couponService;
        $this->couponRepository = $couponRepository;

        $this->couponRepository->pushCriteria(new CouponScope());
    }

    public function index(ValidateSearchCouponRequest $request)
    {
        abort_if(!\Auth::user()->checkPermission("coupon_browse"), Response::HTTP_FORBIDDEN);

        $this->couponRepository->pushCriteria(CouponsFilterCriteria::class);


        $response['coupons'] = $this->couponRepository->onlyRoot(false)->simplePaginate();

        $response['page_title'] = 'كوبونات التخفيض';

        //for filter
        $today = date('Y-m-d');
        $current_week = rangeWeek($today);

        $response['week_range']['current'] = [
            'start' => $current_week['start'],
            'end'   => $current_week['end'],
        ];

        $prev_week_date = \Carbon\Carbon::parse($today)->subWeek()->format('Y-m-d');
        $prev_week = rangeWeek($prev_week_date);
        $response['week_range']['prev'] = [
            'start' => $prev_week['start'],
            'end'   => $prev_week['end'],
        ];
        $response['filtering'] =  (request('filtering')) ? 1 : 0;

        return view(config('salla.views_path.cp') . '.coupons.index')->with($response);
    }

    public function loadCoupons()
    {
        $response['coupons_count'] = $this->couponRepository->onlyRoot()->count();

        $response['coupons'] = $this->couponRepository->onlyRoot()->paginate();

        return view(config('salla.views_path.cp') . '.coupons.list')->with($response);
    }

    public function create($id = 0)
    {
        $store_id = app('store')->getId();
        $response = $this->couponService->create($id, $store_id);

        $source = $this->request->input('type_coupon', null);
        
        if($source === 'mahly') {
            return view(config('salla.views_path.cp') . '.coupons.mahly_form')->with($response);
        }

        return view(config('salla.views_path.cp') . '.coupons.form')->with($response);
    }

    public function store(CouponsRequest $request)
    {
        $response = \DB::transaction(function () use ($request) {
            return $this->couponService->store($request);
        });


        return ajax()->success($response['total'])
            ->setData([
                'coupon_view' => (string) view('cp.coupons.partials.coupon_template_row', [
                    'coupon' => $response['coupon_info'],
                ]),
            ]);
    }

    public function disable()
    {
        $id = $this->request->input('id');
        $status = $this->request->input('status');

        ChangeCouponStatus::make()
            ->setCoupon(\Modules\Marketing\Entities\Coupon::query()->withoutGlobalScope(ExcludeNonSallaCouponsScope::class)->findOrFail($id))
            ->setStatus($status)
            ->run();

        return [
            'case'    => 'success',
            'message' => 'تم الحفظ',
        ];
    }

    public function delete($id)
    {
        abort_if(!\Auth::user()->checkPermission("coupon_delete"), Response::HTTP_FORBIDDEN);

        $store_id = app('store')->getId();

        $response = \DB::transaction(function () use ($id, $store_id) {
            return $this->couponService->delete($id, $store_id);
        });

        return ajax();
    }

    public function list()
    {
        $store_id = app('store')->getId();

        return $this->couponService->list($store_id, []);
    }

    public function report($id, CouponReportRequest $request)
    {
        if (store()->plan == Plan::BASIC) {
            abort(403, __('global.plan_permission'));
        }

        $store_id = app('store')->getId();
        $coupon = $this->couponService->getCoupon($store_id, $id);

        $params = $request->validated();

        $result = $this->couponRepository->report($coupon, $params, 'simplePaginate');
        $result['coupon'] = $coupon;

        $urlCouponMarketing = '';
        if (FeaturesManager::accessible('coupon-affiliate') && (! empty($coupon->couponMarketing))) {
            $urlCouponMarketing = $coupon->couponMarketing->url;
        }

        return app('ajax')->setData([
            'urlCouponMarketing' => $urlCouponMarketing,
            'content'            => (string) view(config('salla.views_path.cp') . '.coupons.partials.report_data', $result),
        ]);
    }

    public function export_report($id, CouponReportRequest $request)
    {
        $params = $request->validated();

        // TODO: Use a different approach
        if (mobileAgent()->is()) {
            // dispatch_now is deprecated
            dispatch_sync(new CouponExportToMailJob($id, $params));
            return ajax()->success(__t('ajax.coupons_report_sent_message'));
        }

        return ExportCouponReportAction::make([
            'couponId' => $id,
            'params'   => $params,
        ])->run()->download(sprintf('coupons-%s.xlsx', $id), Excel::XLSX);
    }

    public function getMarketingCoupon($id)
    {
        $coupon = $this->couponService->getCoupon(store()->id, $id);
        if (! FeaturesManager::accessible('coupon-affiliate') || (empty($coupon->couponMarketing))) {
            return app('ajax')->error(trans('coupon.messages.error.coupon marketing not exist'));
        }

        return app('ajax')
            ->runJavascript('$("#modal_coupon_marketing").modal("show");')
            ->redrawView('coupon_marketing_modal_div')
            ->view('cp.coupons.modal_coupon_marketing', [
                'coupon'                     => $coupon,
                'coupon_marketing'           => $coupon->couponMarketing,
                'url_coupon_marketing'       => $coupon->couponMarketing->current_url,
                'url_coupon_marketing_store' => $coupon->couponMarketing->url_store,
            ]);
    }

    public function export_coupons(Request $request)
    {
        $toDate = new \DateTime($request->to_date);

        $rules = [ 'filter_orders' => 'nullable|in:today,week,month,all,dates'];
        if ($request->filter_orders == 'dates'){
            $rules = array_merge($rules,array(
                'from_date' => 'required_if:filter_orders,dates|date|before_or_equal:'. $toDate->format('Y-m-d'),
                'to_date' => 'required_if:filter_orders,dates|date|before_or_equal:today',
            ));
        }
        $validator = Validator::make( $request->all(), $rules );

        if ($validator->fails()) {
            return [
                'case' => 'error',
                'message' => 'الرجاء اضافة تاريخ صحيح',
            ];
        }

        $validated = $validator->validated();

        $store_id = app('store')->getId();

        $coupons = Coupon::query()
            ->withoutGlobalScope(ExcludeNonSallaCouponsScope::class)
            ->where('coupon.store_id', $store_id)
            ->where('coupon.status', '!=', 'deleted')
            ->where('coupon.parent_id', 0)
            ->where('coupon.is_group',0)
            ->where(function ($query) {
                $query->whereNull('coupon.type_coupon')
                    ->orWhere('coupon.type_coupon', CouponTypes::MAHLY);
            })
            ->exportFilter($validated)
            ->count();

        if ($coupons){

            $file_name = 'temps/'.date('d-m-Y-H-i').'_'.generateRandomString(5).'_coupons.xlsx';
            $file_path =  storage_path('app/'.$file_name);

            dispatch(function () use ($store_id,$file_path,$validated) {
                (new CouponExportFastExcel(null,$store_id,$validated))->queue($file_path);
            })->chain([
                new NotifyCouponsCompletedExport(auth()->user()->email,$file_name),
            ])->allOnQueue('exports');


            return [
                'case' => 'success',
                'message' => 'سيتم إرسال ملف تصدير الكوبونات علي بريدك الإلكتروني بعد قليل',
            ];
        }

        return [
            'case' => 'error',
            'message' => 'لايوجد لديك كوبونات في الفترة المختارة',
        ];
    }

    public function export_group_coupons($id)
    {
        $store_id = app('store')->getId();
        $groupCoupon = $this->couponService->getGroupCoupon($id, $store_id);

        // for big group of coupons we run the export in the jobs
        if($groupCoupon->group_coupons_count > 3000) {

            $fileName = 'temps/'.'coupons_'.$groupCoupon->code.'_'.Carbon::now()->format('Y-m-d_H-m-s').'.xlsx';
            $file_path =  storage_path('app/'.$fileName);

            dispatch(function () use ($id,$store_id,$file_path) {
                (new CouponExportFastExcel($id,$store_id))->queue($file_path);
            })->chain([
                new NotifyCouponsCompletedExport(auth()->user()->email,$fileName),
            ])->allOnQueue('exports');

            return [
                'case' => 'success',
                'message' => 'سيتم إرسال ملف تصدير الكوبونات علي بريدك الإلكتروني بعد قليل',
                'count' => $groupCoupon->group_coupons_count
            ];
        }

        $title = str_replace('', '-', 'كوبونات مجموعة ' . $groupCoupon->group_name);

        if (request()->ajax()) {
            return response()->json([
                'case' => 'download',
                'download_url' =>
                    route('cp.coupon.export_group_coupons', ['coupon' => $id]) . '?' .
                    http_build_query($this->request->all())
                ,
            ]);
        }

        return (new CouponExport($groupCoupon, $store_id, $this->request->all()))
            ->download(sprintf($title . '-%s.xlsx', Carbon::now()
            ->format('Y-m-d_H-m-s')), Excel::XLSX)
        ;
    }

    public function browse(Coupon $coupon, CouponReportRequest $request) {
        $params = $request->validated();

        $result = $this->couponRepository->report($coupon, $params, 'simplePaginate');
        $result['coupon'] = $coupon;
        $result['page_title'] = sprintf('طلبات الكوبون %s', $coupon->code);

        return view(config('salla.views_path.cp') . '.coupons.browser_orders')->with($result);
    }
}
