<?php

namespace App\Http\Controllers\CP;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAgreementRequest;
use App\Http\Requests\ValidateStoreTaxNumberRequest;
use App\Http\Requests\ValidateUploadVatImageRequest;
use App\Libraries\UploaderLib;
use Modules\Languages\Http\Requests\Dashboard\TranslatableRequest;
use Modules\Product\Http\Requests\Dashboard\ValidateImageProduct;
use App\Http\Requests\ValidateOrderInstructionsRequest;
use App\Http\Requests\ValidateOrderSettingRequest;
use App\Services\StoreService;
use App\Services\StoreTaxService;

class StoreController extends Controller
{
    private $request;
    private $storeService;

    public function __construct(
        TranslatableRequest $request,
        StoreService $storeService,
        StoreTaxService $storeTaxService
    ) {
        parent::__construct();
        $this->request = $request;
        $this->storeService = $storeService;
        $this->storeTaxService = $storeTaxService;
    }

    public function create()
    {
        $store_id = \Auth::user()->store_id;
        $response = $this->storeService->create($store_id);
        $response['page_title'] = 'إعدادات المتجر';
        $response['list_store_tax'] = $this->storeTaxService->index($store_id);
        $response['countries'] = $this->storeTaxService->getCountries();

        return view(config('salla.views_path.cp').'.settings.settings')->with($response);
    }

    public function updateAll()
    {
        $store_id = store()->getId();
        $response = $this->storeService->updateAll($this->request, $store_id);

        return $response;
    }

    public function update()
    {
        $store_id = store()->getId();
        $response = $this->storeService->update($this->request, $store_id);
        if ($response['case'] == 'validator') {
            return redirect()->back()->withErrors($response['data']);
        } elseif ($response['case'] == 'success') {
            flash($response['msg']);

            return redirect()->back();
        } else {
            flash($response['msg'], 'danger');

            return redirect()->back();
        }
    }

    public function getSocial()
    {
        $store_id = store()->getId();
        $response = $this->storeService->getSocial($store_id);
        $response['page_title'] = 'الحسابات الاجتماعية';

        return view(config('salla.views_path.cp').'.settings.social')->with($response);
    }

    public function postSocial()
    {
        $store_id = store()->getId();
        $response = $this->storeService->postSocial($this->request, $store_id);
        flash($response['msg']);

        return redirect()->back();
    }

    public function updatebio()
    {
        $user = \Auth::user();
        $response = $this->storeService->updatebio($user);
        if ($response['case'] == 'success') {
            flash($response['msg']);
        } else {
            flash($response['msg'], 'danger');
        }

        return redirect()->back();
    }

    public function getStoreContent()
    {
        return $this->storeService->getStoreContent();
    }

    public function postSocialConnect()
    {
        $store_id = store()->getId();
        $response = $this->storeService->postSocialConnect($this->request, $store_id);
        flash($response['msg']);

        return redirect()->back();
    }

    public function showRating()
    {
        return view(config('salla.views_path.cp').'.rating.rating')->with([
            'page_title' => 'التقييمات',
        ]);
    }

    public function saveOrderSettings(ValidateOrderSettingRequest $request)
    {
        $store_id = store()->getId();

        return $this->storeService->saveOrderSettings($request, $store_id);
    }

    public function editAvatar()
    {
        return view(config('salla.views_path.cp').'.settings.avatar');
    }

    public function upload_avatar(ValidateImageProduct $request)
    {
        return $this->storeService->uploadPhoto($request);
    }

    public function delete_avatar()
    {
        $store_id = store()->getId();

        return $this->storeService->delete_avatar($store_id);
    }

    public function refreshSettings()
    {
        $store_id = store()->getId();

        return $this->storeService->refreshSettings($store_id);
    }

    public function uploadTaxImage(ValidateUploadVatImageRequest $request)
    {
        $response = app(UploaderLib::class)->uploadPublicFile($request->file('file_input'));
        return [
            'aws_file_url' => $response['url'],
            'file_size'    => $response['fileSize'],
        ];
    }
    public function saveStoreTaxSetting(ValidateStoreTaxNumberRequest $request)
    {
        $store_id = store()->getId();

        $response = $this->storeService->saveStoreSetting($store_id, $request);

        if ($this->request->ajax('ajax')) {
            if ($response['case'] == 'error') {
                return response()->json([
                    'error' => true, 'error_description' => $response['msg'], 'data' => '',
                ]);
            } else {
                return response()->json([
                    'error' => false, 'error_description' => $response['msg'], 'data' => $response['msg'],
                ]);
            }
        } else {
            if ($response['case'] == 'error') {
                flash($response['msg'], 'danger');
            } else {
                flash($response['msg']);
            }

            return redirect()->route('cp.settings');
        }
    }

    public function UpdateStoreTaxSetting()
    {
        $store_id = store()->getId();

        $response = $this->storeService->UpdateStoreTaxSetting($store_id, $this->request);

        if ($this->request->ajax('ajax')) {
            if ($response['case'] == 'error') {
                return response()->json([
                    'error' => true, 'error_description' => $response['msg'], 'data' => '',
                ]);
            } else {
                return response()->json([
                    'error' => false, 'error_description' => $response['msg'], 'data' => $response['msg'],
                ]);
            }
        } else {
            if ($response['case'] == 'error') {
                flash($response['msg'], 'danger');
            } else {
                flash($response['msg']);
            }

            return redirect()->route('cp.settings');
        }
    }


    public function showAgreement()
    {
        $data['mystore'] = \Auth::user()->store;

        return view(config('salla.views_path.cp').'.settings.partials.agreement_form')->with($data);
    }

    public function saveAgreement()
    {
        $this->storeService->saveAgreement($this->request);
        return ['case' => 'success', 'message' => 'تم الحفظ بنجاح'];
    }

    public function ShowOrderInstructions()
    {
        $data['translations'] = store()->getSetting('order_instruction', []);
        if (! isset($data['translations'][store()->langIsoCode])) {
            $data['translations'][store()->langIsoCode] = $data['translations'];
        }
        return view(config('salla.views_path.cp').'.settings.partials.order_instructions_form')->with($data);
    }

    public function saveOrderInstructions(ValidateOrderInstructionsRequest $request)
    {
        store()->setSetting('order_instruction', $request->validated());
        clearStoreCache();

        return app('ajax')->success('تم بالنجاح تحديث البيانات');
    }

    public function uploadFavicon(ValidateImageProduct $request)
    {
        return $this->storeService->uploadFaviconPhoto($request);
    }

    public function deleteFavicon()
    {
        return $this->storeService->deleteFavicon(app('store')->getCurrent()->id);
    }
}
