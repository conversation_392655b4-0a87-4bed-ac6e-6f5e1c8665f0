<?php

namespace App\Http\Controllers\CP;

use App\Http\Controllers\Controller;
use App\Http\Requests\ExportOrdersRequest;
use App\Http\Requests\ValidateCompanyOptionRequest;
use App\Http\Requests\ValidateSuspiciousAlertRequest;
use App\Http\Requests\ValidateOrderReadRequest;
use App\Jobs\FlushUnreadCountCacheJob;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Services\OrderService;
use App\Traits\DashboardOrderTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Customer\Entities\CustomerBlockReason;
use Modules\Customer\Transformers\CustomerBlockReasonTransformer;
use Modules\Impex\Features\OrderExportTemplatesFeature;
use Modules\Orders\Actions\OrderStatus\GetAllowedOrderStatus;
use Modules\Orders\Actions\Shipping\Api\GetShipmentStatus;
use Modules\Orders\Actions\Shipping\CreateShipment;
use Modules\Orders\Entities\OrderCustomStatus;
use Modules\Orders\Entities\OrderShipmentBranch;
use Modules\Orders\Enum\ShippingCompanyId;
use Modules\Orders\Jobs\ChangeOrdersStatusJob;
use Modules\Orders\Presenter\Order\OrderPaymentActionsPresenter;
use Modules\Orders\Repositories\OrderRepository;
use Modules\Orders\Repositories\OrderStatusRepository;
use Salla\Core\Traits\HasIncremental;
use Salla\Shipping\Entities\CompanyShippingApi;
use Salla\Shipping\Exceptions\ShippingApiException;
use Salla\Shipping\Widgets\Dashboard\ShippingCompanyOptionWidget;
use App\Http\Requests\MultiChangeStatusModalRequest;
use App\Http\Requests\ValidateOrderStatusRequest;

class OrderController extends Controller
{
    use HasIncremental, DashboardOrderTrait;
    private $request;
    private $orderService;

    public function __construct(Request $request, OrderService $orderService)
    {
        parent::__construct();
        $this->request = $request;
        $this->orderService = $orderService;

        $this->middleware('optimusids:branch,country,city,status,order_status,list_order_id,order_id');
        $this->middleware('feature:' . OrderExportTemplatesFeature::getName())->only('export');
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * @deprecated
     */
    public function index()
    {
        return redirect(route('cp.orders'));
    }

    public function info(Order $order)
    {
        $response = $this->orderService->info($order);

        if ($response['case'] == 'error') {
            flash($response['msg'], 'danger');
            return redirect()->route('cp.orders');
        }

        if ($this->userHasAccess($response['order'])) {
            if ($this->request->ajax()) {
                return response('Unauthorized.', 403);
            }
            return abort(403);
        }

        currency()->setCurrency($response['order']->getCurrency(), true);

        $response['page_title'] = '<a href=' . url('orders') . '>الطلبات  </a> / استعراض طلب ';
        $response['show_help_panel'] = store()->getSetting('orders::show-help-guide', 0);
        $response['list_permission_order_status'] = GetAllowedOrderStatus::getListEditPermissionOrderStatus();
        $response['payment_actions'] = (new OrderPaymentActionsPresenter())->setOrder($response['order']);

        $response = array_merge($response, $this->prepareGiftData($order));
        $response['shipment'] = $order->getShipments(null, true);
        $response['block_reasons'] = transformation(CustomerBlockReason::query()->orderBy('id', 'desc')->get(), new CustomerBlockReasonTransformer())->transform();
        return view(config('salla.views_path.cp') . '.orders.info')->with($response);
    }

    public function status(ValidateOrderStatusRequest $request)
    {
        $statusId = $request->order_status;
        $branchId = $request->branch_id;
        $id = $request->order_id;
        $note = $request->note;
        $listOrderId = $request->list_order_id;
        $filters = $request->all();

        if ((
            $request->isSelectAll() ||
            ($request->isPartialSelect() && $request->countSelectedOrders() > store()->getSetting('orders::partial-order-limit', 100))
        )) {
            dispatch(
                new ChangeOrdersStatusJob(
                    auth()->id(),
                    store()->getId(),
                    $filters,
                    $statusId,
                    $note,
                    $branchId,
                    $request->get('un_selecteds', [])
                )
            );

            return app('ajax')->runJavascript('location.reload();')->success('سيتم تغيير حالة الطلبات');
        }

        $idsArr = (isset($id) && $id != '') ? [$id] : ($listOrderId ?? []);

        $result = $this->orderService->status($idsArr, $statusId, $note, $branchId, $request->has('bulk'));

        if (is_array($result) && count($result) == 1 && $result = $result[0]) {
            return $result['case'] == 'error' ? ajax()->error($result['msg']) : ajax()->success($result['msg']);
        }

        return $result;
    }


    public function receiptInfo(Order $order)
    {
        $response = $this->orderService->info($order);

        if (($response['case'] ?? '') == 'error') {
            flash($response['message'] ?? '', 'danger');

            return back();
        }

        $response['page_title'] = '<a href=' . url('orders/order/' . $order->getRouteKey()) . "> طلب # $order->id </a> / استعراض الايصال ";
        return view(config('salla.views_path.cp') . '.orders.receipt')->with($response);
    }

    public function orderShipping(Order $order)
    {
        return CreateShipment::make()->setOrder($order)->run()->toArray();
    }

    public function export(ExportOrdersRequest $request)
    {
        return $this->orderService->export(store()->getCurrent()->id, $request);
    }

    public function MapOrderStatusPermission($_id)
    {
        $status = [
            '1'  => 24,
            '2'  => 25,
            '3'  => 26,
            '4'  => 27,
            '8'  => 28,
            '9'  => 29,
            '10' => 30,
            '5'  => 31,
            '7'  => 32,
        ];

        return $status[$_id];
    }

    public function getPrivateStatus(Order $order, ?OrderShipmentBranch $orderShipmentBranch = null)
    {
        //Here we know its one shipment, so we can call getSingleShipment
        $shipment = $order->getSingleShipment($orderShipmentBranch);

        /** @var \Salla\Shipping\Contracts\Api\ShipmentStatusContract $status */
        $status = GetShipmentStatus::make()
            ->setOrder($order)
            ->set('orderShipmentBranch', $shipment->getOrderShipmentBranch())
            ->setReference($shipment->getReference())
            ->run();

        if (method_exists($status, 'getCode') && $status->getCode() === ShippingApiException::ERROR_SERVICE_NOT_AVAILABLE) {
            return [
                'message' => 'الميزة غير متاحة',
            ];
        }

        return [
            'message' => $status->isSuccess() ? ($status->getNote() ?: $status->getMessage()) : $status->getMessage(),
            'tracking_link' => $status->isSuccess() ? $status->getTrackingLink() : null,
            'isOnDemand' => optional($shipment->getReference()->shippingCompany)->isOnDemandDelivery()
        ];
    }

    public function companyAdvanceOption(ValidateCompanyOptionRequest $request, Order $order, ?OrderShipmentBranch $orderShipmentBranch = null)
    {
        return \Widget::run(ShippingCompanyOptionWidget::class, [
            'order'                 => $order,
            'orderShipmentBranch'   => $orderShipmentBranch,
        ]);
    }

    public function reset_wait_payment(Order $order)
    {
        $now = Carbon::now();
        $order->update(['wait_payment_start_at' => $now]);

        $newTime = "<p class='badge btn-tiffany btn-rounded' style='padding:0 10px;line-height: 20px;'>ستنتهي مهلة الدفع بعد:"
            . $now->addHours(store()->getSetting('dashboard::checkout-url-age', 72))->diffForHumans()
            . "</p>";

        return app('ajax')
            ->runJavascript('$("#wait-payment-block").html("' . $newTime . '")');
    }

    public function multiOrdersChangeStatusModal(MultiChangeStatusModalRequest $request)
    {
        if($request->isAllSelect()) {
            $orders = collect([]);
        } else {
            $listOrderId = $request->list_order_id;
            
            // getting order with  status history.
            $orders = Order::with('status_history')->find($listOrderId);
        }
        
        //getting suspicious orders count
        $suspiciousCount = $orders->filter(fn($order) => $order->hasSuspiciousAlert())->count();

        //Check if SMSA Enabled to show its Product options
        $smsaApi = (new CompanyShippingApi())->getCompanyApi(store()->getId(), ShippingCompanyId::SMSA);
        $isSMSActivated = $smsaApi ? $smsaApi->isActivated() : false;
        $isShippable   = store()->getStatusBy(optimus()->decode($request->status_id, false))->is_shippable ?? false;

        $orderStatus =  app(OrderStatusRepository::class)->getOrderStatus(optimus()->decode($request->status_id));
        /** @var OrderCustomStatus|OrderStatus $status */
        $status = optional(store()->getStatusBy(encoder()->decode($request->status_id, false)));

        return app('ajax')
            ->view(config('salla.views_path.cp') . '.orders.change_status_mutli_modal', [
                'status_id'              => $request->status_id,
                'status_name'            => $request->status_name,
                'orders'                 => $orders,
                'suspiciousCount'        => $suspiciousCount,
                'showProductDetailsOption' => $isSMSActivated && $isShippable,
                'isRefendable'           => $status->isRefundableStatus(),
                'show_send_sms_option'   => !$status->isRestrictedPlan(store()->getPlan()),
                'enable_send_sms_option' => !empty($orderStatus->message),
                'un_selecteds' => $request->get('un_selecteds', []),
            ]);
    }

    public function markOrdersAsRead(ValidateOrderReadRequest $request)
    {
        config([
            'core-salla.safe_update_mode.eloquent.update' => true,
            'core-salla.safe_update_mode.eloquent.chunk'  => 1000,
        ]);

        /** @var OrderRepository|Order|\Modules\Orders\Entities\Order $repo */
        $repo =  app(OrderRepository::class);

        $repo->setCriteria();

        $repo->where('read', 0)
            ->when(!$request->select_all && $request->order_ids, function ($query) use ($request) {
                $query->whereIn('orders.id', $request->order_ids);
            })
            ->safeUpdate(['read' => 1]);

        // let's reduce the by the totals count
        FlushUnreadCountCacheJob::dispatch();

        cache()->forget(sprintf(config('salla.notification_cache_key'), auth()->id()));

        return ajax()->runJavascript('setTimeout(function() {location.reload();}, 500)')
            ->success('سوف يتم تحديث عدد الطلبات الغير مقروءءّ بعد لحظات');
    }

    public function hideHelpPanel()
    {
        $this->orderService->hideHelpPanel();

        return ajax()->success('success');
    }
    
    public function handleSuspiciousAlert(ValidateSuspiciousAlertRequest $request, int $orderId)
    {
        DB::beginTransaction();
        try {
            $response = $this->orderService->handleSuspiciousAlert($orderId, $request->get('operation'));

            if($response['status'] == 'error') {
                return ajax()->error($response['msg']);
            }
            
            $message =  $response['msg'];
            
            DB::commit();
            
            return ajax()->success($message);

        } catch (\Exception $e) {
            DB::rollBack();
            return ajax()->error($e->getMessage());
        }
    }
    
    public function checkSuspiciousPopup(Request $request)
    {
        $request->validate([
            'status' => ['required', 'integer']
        ]);
        
        $response = $this->orderService->checkSuspiciousPopup();
        
        return ajax()->info($response);
    }

    function userHasAccess($order): bool
    {
        $permissions = GetAllowedOrderStatus::getListBrowsePermissionOrderStatus();
        return (!(\Auth::user()->hasRole('reader') || \Auth::user()->role == 'reader')) &&
        (!\Auth::user()->checkPermission('order_browse') &&
            (array_key_exists($order->order_status_id, $permissions)) &&
            (!\Auth::user()->checkPermission($permissions[$order->order_status_id])) &&
            (!\Auth::user()->checkPermission('order_search')));
    }
}
