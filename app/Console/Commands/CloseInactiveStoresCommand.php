<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Salla\Core\Entities\Store;
use Salla\Logger\Traits\WithStageLogger;

class CloseInactiveStoresCommand extends Command
{
    use WithStageLogger;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'salla:close-inactive-stores';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set Any Store That Not Active for 30days as Closed For Maintenance';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $counter = 0;
        Store::query()
            ->select('stores.id', 'stores.status', 'stores.last_seen')
            ->where('plan', 'basic')
            ->where('stores.status', 'active')
            ->groupBy('stores.id')
            ->havingRaw('stores.last_seen < date_sub(now(), interval 1 MONTH)')
            ->each(function (Store $store) use (&$counter) {
                $store->update(['status' => 'idle']);
                store()
                    ->setCurrent($store)
                    ->setSetting('store::status', [
                        'status'  => 'inactive',
                        'title'   => store()->getSetting('idle_store::title', 'المتجر مغلق حالياً'),
                        'message' => store()->getSetting('idle_store::message', 'عذرا عزيزي العميل، المتجر مغلق حاليا، سنعاود العمل خلال فترة وجيزة'),
                    ]);
                $counter++;
            }, 100);
        $this->info(sprintf('Found & Closed (%s) InActive Stores.', $counter));
    }
}
