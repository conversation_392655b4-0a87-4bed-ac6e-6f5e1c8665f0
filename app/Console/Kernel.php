<?php

namespace App\Console;

use App\Console\Commands\CampaignSchedule;
use App\Console\Commands\GeneratePaymentCommand;
use App\Console\Commands\InCompleteBasicStoreFiles;
use App\Console\Commands\NotificationCampaignSchedule;
use App\Console\Commands\SeedStoreCustomersCommand;
use App\Console\Commands\Themes\MigrateStoreBlocksCommand;
use App\Console\Commands\UpdatePostalCodeOnCities;
use App\Console\Commands\UpdateSallaOrderCreditNote;
use App\Console\Commands\UpdateSallaOrderItemRefunded;
use App\Jobs\Intercom\CleanUpLeads;
use App\Jobs\Intercom\CleanUpUsers;
use App\Jobs\RunCommandAsJob;
use App\Jobs\SupportJobs\InsertRiskyCustomersToBlacklistJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Container\Container;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Modules\Marketing\Console\DefaultAbandonedCartReminderCommand;
use Modules\MarketingIntegration\Console\AddLonAndLatToGoogleTargetsTableCommand;
use Modules\MarketingIntegration\Console\AddMarketPlaceAppAndSnippetCommand;
use Modules\MarketingIntegration\Console\FillMissingInfoOnTransactionsCommand;
use Modules\MarketingIntegration\Console\RecalculateStatisticsCommand;
use Modules\MarketingIntegration\Console\RefundCampaignCommand as RefundSweplyCampaignCommand;
use Modules\MarketingIntegration\Console\RefundSweplyCommand;
use Modules\MarketingIntegration\Console\AddMarketingIntegrationRoutesPermissionsCommand;
use Modules\MarketingIntegration\Console\RefundCanceledCampaignCommand;
use Modules\MarketingIntegration\Console\UpdateMISnapchatSettings;
use Modules\MarketingIntegration\Console\CorrectionTranSweplyCommand;
use Modules\MarketingIntegration\Console\CorrectionRefundDuplicationCommand;
use Modules\MarketingIntegration\Console\RefundSkippedCampaignsCommand;
use Modules\MarketingIntegration\Console\UpdatePlatformStatus;
use Modules\Marketing\Console\AbandonedCartNotUpdatedCommand;
use Modules\Marketing\Console\RemoveOldCouponsFilesCommand;
use Modules\ThemeCustomization\Jobs\CleanDraftThemesJob;
use Salla\AWSDomainVerify\Console\Commands\ActivePendingDomains;
use Salla\AWSDomainVerify\Console\Commands\InsetDomainsInAWSTable;
use Salla\Core\Jobs\BulkPurgeCloudflareCacheJob;
use Salla\Core\Jobs\HeartBeatCheck;
use Salla\Paymetns\Console\Commands\FixRegisterDomainApplepayCommand;
use Salla\ScheduleMonitor\MonitorsSchedule;
use Salla\Shipping\Console\Commands\GenerateShippingCodInvoiceCommand;
use Modules\Wallet\Commands\HandleCustomerWalletExpiryCommand;
use Salla\Shipping\Console\Commands\GenerateStoresShippingPriceTiersCommand;
use Modules\StoreBranch\Console\MigrateToMultiCountryCommand;

class Kernel extends ConsoleKernel
{
    use MonitorsSchedule;

    const LOCKED_EXPIRED = 60;

    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\OrdersCancelation::class,
        GeneratePaymentCommand::class,
        CampaignSchedule::class,
        NotificationCampaignSchedule::class,
        \App\Console\Commands\MessagesSchedule::class,
        //\App\Console\Commands\CurrencyUpdate::class,
        \App\Console\Commands\UpdateOutProduct::class,
        \App\Console\Commands\SendCampaign::class,
        \App\Console\Commands\CheckMessageStatus::class,
//        \App\Console\Commands\SendEmailReminder::class,
        \App\Console\Commands\SendEmailReports::class,
        \App\Console\Commands\UpdateCartItemsPrice::class,
        \App\Console\Commands\UpdateCartItemOptionsPrice::class,
        \App\Console\Commands\AlertSmsBalance::class,
        UpdatePostalCodeOnCities::class,
        \App\Console\Commands\SalasaDailySyncInventoryCommand::class,
        \App\Console\Commands\NotifyRating::class,
        \App\Console\Commands\NotifyRatingForOrders::class,
        \App\Console\Commands\ClearCustomersProducts::class,
        \App\Console\Commands\StorageStationDailySyncInventoryCommand::class,
        \App\Console\Commands\SyncSalasaInventoryDailyCommand::class,
        \App\Console\Commands\DeleteExpiredInvitationEmployeesCommand::class,
        \App\Console\Commands\SendAppRenewEmailReminder::class,

        //marketplace
        \Modules\MarketPlace\Console\MarketplaceBusJobsCommand::class,
        \Modules\MarketPlace\Console\CheckSubscriptionsCommand::class,
        \Modules\MarketPlace\Console\CreateInvoiceCommand::class,
        \Modules\MarketPlace\Console\SendInstallmentPayPreReminderCommand::class,
        \Modules\MarketPlace\Console\PauseLateInstallmentSubscriptionCommand::class,
        \Modules\MarketPlace\Console\CancelLateInstallmentSubscriptionCommand::class,
        \Modules\MarketPlace\Console\AutoPaidInvoiceCommand::class,
        \Modules\MarketPlace\Console\AutoPaidInstallmentInvoiceCommand::class,
        \Modules\MarketPlace\Console\DeleteSallaInvoiceCommand::class,
        \Modules\MarketPlace\Console\CreditCardExpirationAlertCommand::class,
        \Modules\MarketPlace\Console\NotifyUnpaidSallaInvoiceCommand::class,
        \Modules\MarketPlace\Console\SallaSubscriptionRevenueChangeStatusCommand::class,
        \Modules\MarketPlace\Console\DeleteCreditCardCommand::class,
        \Modules\MarketPlace\Console\CleanSallaMarketplaceRequestCommand::class,
        \Modules\MarketPlace\Console\ChangeToLifetimeSubscriptionCommand::class,
        \Modules\MarketPlace\Console\CheckAppChangePriceCommand::class,
        \Modules\MarketPlace\Console\SendChurnUsersSurveyCommand::class,
        \Modules\MarketPlace\Console\SendWhatsappSubscriptionRenewReminderCommand::class,
        \Modules\MarketPlace\Console\CheckSallaSubscriptionPendingCommand::class,
        \Modules\MarketPlace\Console\SyncStorePlanActiveSubscriptionsCommand::class,
        \Modules\MarketPlace\Console\MarketplaceApp\CheckNeedRenewSubscriptionCommand::class,
        \Modules\MarketPlace\Console\MarketplaceApp\CheckCanReviewMarketplaceAppsCommand::class,
        \Modules\MarketPlace\Console\MarketplaceApp\CleanMarketplaceAppRequestCommand::class,
        \Modules\MarketPlace\Console\CreateSubscriptionReminderCommand::class,

        //StoreApp
        \Modules\StoreApp\Console\CheckAppHostOnSallaAccountCommand::class,

        \App\Console\Commands\AutoCompleteTransaction::class,
        \Modules\Impex\Console\Commands\ClearOrderStatusUpdateQueue::class,
        \Modules\Marketing\Console\AbandonedCartWebhookCommand::class,
        \App\Console\Commands\AddMultipleKeysCommand::class,

        //Marketing
        AbandonedCartNotUpdatedCommand::class,
        DefaultAbandonedCartReminderCommand::class,
        RemoveOldCouponsFilesCommand::class,
        AddLonAndLatToGoogleTargetsTableCommand::class,
        UpdatePlatformStatus::class,
        UpdateMISnapchatSettings::class,
        CorrectionTranSweplyCommand::class,
        CorrectionRefundDuplicationCommand::class,
        AddMarketPlaceAppAndSnippetCommand::class,
        RefundSweplyCampaignCommand::class,
        AddMarketingIntegrationRoutesPermissionsCommand::class,
        RefundSweplyCommand::class,
        RefundCanceledCampaignCommand::class,
        RecalculateStatisticsCommand::class,
        RefundSkippedCampaignsCommand::class,
        HandleCustomerWalletExpiryCommand::class,
        FillMissingInfoOnTransactionsCommand::class,

        //Payment
        \Modules\Payment\Console\GenerateSallaStoreTaxInvoiceCommand::class,
        FixRegisterDomainApplepayCommand::class,

        //LoyaltySystem
        \Modules\LoyaltySystem\Console\PointExpiryReminderCommand::class,
        \Modules\LoyaltySystem\Console\ApproveBuyFromStorePointsCommand::class,
        \Modules\LoyaltySystem\Console\ApproveBuyFromStorePointsForSpecificStoreCommand::class,
        \Modules\LoyaltySystem\Console\ApproveLoyaltyAffiliateLinkPointsCommand::class,
        \Modules\LoyaltySystem\Console\ProcessLoyaltyPointsTempCommand::class,
        \Modules\LoyaltySystem\Console\UpdateStoreLoyaltyCustomerPointWalletCommand::class,
        \Modules\LoyaltySystem\Console\AdjustPointsForSpecificStoreCommand::class,

        // Gift System
        \Modules\GiftSystem\Console\DeleteGiftAssetCommand::class,

        //App Invoice
        \Modules\MarketPlace\Console\GenerateMarketplaceAppInvoiceCommand::class,
        \Modules\MarketPlace\Console\GenerateMarketplaceAppInvoiceItemOrderCommand::class,
        \Modules\MarketPlace\Console\NotifyMarketplaceAppInvoiceCommand::class,

        //Shipping COD Invoices
        GenerateShippingCodInvoiceCommand::class,

        //Rutter API
        \Salla\RutterApi\Console\RetryImportRutterDataCommand::class,
        \Salla\RutterApi\Console\MarkRutterImportProcessAsCompleted::class,

        // Mahly
        \Modules\MahlyDash\Console\Commands\ActivateMahlyForLeakedStores::class,
        \Modules\MahlyDash\Console\Commands\DeactivateMahlyForLeakedStores::class,

        // for testing purpose only
        SeedStoreCustomersCommand::class,

        //Tier Pricing System
        GenerateStoresShippingPriceTiersCommand::class,

        UpdateSallaOrderCreditNote::class,

        InCompleteBasicStoreFiles::class,

        UpdateSallaOrderItemRefunded::class,

        // Themes
        MigrateStoreBlocksCommand::class,

        // Store Branch
        MigrateToMultiCountryCommand::class
    ];

    /**
     * @var Schedule
     */
    protected $schedule;

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     */
    protected function schedule(Schedule $schedule)
    {
        if (defined('APP_RUNNING_IN_SWOOLE')) {
            return;
        }

        $this->schedule = $schedule;

        // just for test import
        //TODO:: Distribute Commands into separate functions by types or sections for clarity.
        if (!app()->environment('development')) {

            $this->commandAsJob('notifications:orders')->everyMinute();
            $schedule->job(new InsertRiskyCustomersToBlacklistJob)
                ->dailyAt('01:00')
                ->timezone('Asia/Riyadh');

            // Generate Stores Payments
            $schedule->command('payment:generate')
                ->daily()
                ->at('00:00')
                ->timezone('Asia/Riyadh');

            $this->commandAsJob('payment:generate')
                ->daily()
                ->at('01:20')
                ->timezone('Asia/Riyadh');

            $schedule->command('payment:generate')
                ->wednesdays()
                ->at('02:00')
                ->timezone('Asia/Riyadh');

            // Generate Partner Payments
            $this->commandAsJob('payment:partner-generate')
                ->twiceMonthly(13, 28, '02:00');
            $this->commandAsJob('payment:partner-affiliate-generate')
                ->twiceMonthly(13, 28, '04:00');

            // Update Tamara Orders status
            $this->commandAsJob('payments:tamara:update')->everyThirtyMinutes();

            //marketplace
            $this->commandAsJob('marketplace:installment:cancel')->dailyAt('04:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:installment:pause-late-subscription')
                ->dailyAt('04:15')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:installment:pay-reminder')->dailyAt('13:00')->timezone('Asia/Riyadh');

            $schedule->command('marketplace:invoice:create')
                    ->daily()
                    ->at('00:15')
                    ->timezone('Asia/Riyadh');
            $schedule->command('marketplace:subscription:check')
                    ->daily()
                    ->at('03:45')
                    ->timezone('Asia/Riyadh');
            $schedule->command('marketplace:invoice:auto-paid')
                    ->daily()
                    ->at('04:45')
                    ->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:installment-invoice:auto-paid')->dailyAt('05:15')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:invoice:delete')->dailyAt('06:30')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:subscription-reminder:create')->dailyAt('07:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:sync-store-plan-subscriptions')->dailyAt('07:15')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:subscription:revenue-update')->dailyAt('07:45')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:invoice:notify')->dailyAt('08:15')->timezone('Asia/Riyadh');

            $this->commandAsJob('credit_card:finish_alert')->dailyAt('07:30')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:subscription:survey')->dailyAt('14:00')->timezone('Asia/Riyadh');
            //$this->commandAsJob('marketplace:subscription:renew-reminder')->dailyAt('14:15')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:check-app-change-price')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:check-pending-subscription')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:check-can-review-marketplace-app')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:check-need-renew-app-subscription')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:clean-marketplace-app-request')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('marketplace:calculate-partner-theme-statistics')->weekly()->timezone('Asia/Riyadh');

            //Marketplace invoice
            //$this->commandAsJob('marketplace:generate-app-invoice-order')->monthlyOn(1)->timezone('Asia/Riyadh');
            //$this->commandAsJob('marketplace:generate-app-invoice')->monthlyOn(1, '03:00')->timezone('Asia/Riyadh');
            //$this->commandAsJob('marketplace:notify-app-invoice')->dailyAt('00:00')->timezone('Asia/Riyadh');

            //StoreApp
            $this->commandAsJob('storeApp:app_host_on_salla_account:check')->dailyAt('01:45')->timezone('Asia/Riyadh');

            $this->commandAsJob('transactions:auto-complete-pending')->everyFiveMinutes()->withoutOverlapping();
            //$this->commandAsJob('currency:update')->twiceDaily(12, 21)->timezone('Asia/Riyadh');
            $this->commandAsJob('sms:send-scheduled')->everyFiveMinutes();

            $this->commandAsJob('send:campaign')
                ->everyFifteenMinutes()
                ->timezone('Asia/Riyadh')
                ->between('7:00', '22:00');

            $this->commandAsJob('schedule:campaign')
                ->everyThirtyMinutes()
                ->timezone('Asia/Riyadh')
                ->between('7:00', '22:00');

            // send notification campaigns
            $this->commandAsJob('schedule:notification-campaign')->everyFiveMinutes()->timezone('Asia/Riyadh');

            $this->commandAsJob('schedule:messages')
                ->everyFiveMinutes()
                ->timezone('Asia/Riyadh')
                ->between('7:00', '22:00');

            if (app()->isProduction()) {
                $schedule->job(new CleanUpLeads())->dailyAt('08:00');
                $schedule->job(new CleanUpUsers())->dailyAt('10:00');
            }

            $this->commandAsJob('paused_subscriptions:activate')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('rising_stores:mark')
                ->weekly()
                ->mondays()
                ->at('00:00')
                ->timezone('Asia/Riyadh');

            if (app()->isProduction()) {
                // domain:clear-unused-domains

                $this->commandAsJob('domain:check')->everyThirtyMinutes();
                $this->commandAsJob('domain:clean-username')->daily();
                //TODO: We will keep monitoring this command as it is part of domain:check commands.
                // If the checked records is reduced this command will be removed.
                //ON 28/11/2024 the records are 92426
                $this->commandAsJob('domain:syn:expire-date')->dailyAt('01:00');
                $this->commandAsJob('domain:clean-expired-domains')->dailyAt('02:30');
                $schedule->command('domain:update-zones')->dailyAt('05:00')->runInBackground();
                $this->commandAsJob('domain:remove-unused-hostname')->dailyAt('01:30');
                $this->commandAsJob('domain:remove-unused-zone')->twiceDailyAt(2,14, 30);
                $this->commandAsJob('domain:update-hostname-status')->twiceDaily(6, 18);

                // $this->commandAsJob('domain:relink-after-plan-renewal')->dailyAt('03:00');
                //$schedule->command('domain:sync-hostnames')->dailyAt('06:00')->runInBackground();
                //$schedule->command('domain:migrate-to-saas')->dailyAt('08:00')->runInBackground();
                //$this->commandAsJob('aws:inits-email-domains')->daily();
                // $this->commandAsJob('register:applepay-merchants-domains')->daily();
                //$this->commandAsJob('fix:register-domain-applepay')->hourly();
            }

            $this->commandAsJob('announcement:update')
                ->weekly()
                ->sundays()
                ->at('00:00')
                ->timezone('Asia/Riyadh');

            $this->commandAsJob('horizon:snapshot')->everyFiveMinutes();
            $schedule->job(new CleanDraftThemesJob(), 'themes')->dailyAt('03:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('themes:migrate-legacy')->dailyAt('03:00')->timezone('Asia/Riyadh');

            $this->commandAsJob('import:product:hs-codes')->everyTenMinutes();
            // import product quantities
            $this->commandAsJob('import:product:quantities')->everyFiveMinutes();
            // import product prices
            $this->commandAsJob('import:product:prices')->everyTenMinutes();
            // import product seo
            $this->commandAsJob('import:product:seo')->everyTenMinutes();

            $this->commandAsJob('impex:trash')->dailyAt('03:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('brands:impex-trash')->dailyAt('03:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('categories:impex-trash')->dailyAt('03:00')->timezone('Asia/Riyadh');
            // $this->commandAsJob('salla:queue-scale')->everyMinute();

            $this->commandAsJob('schedule:clear-order-status-update-queue')->daily();

            // Aws emails
            if (app()->isProduction()) {
                $this->commandAsJob('aws:inits-email-domains special')->hourly()->then(function () {
                    $this->commandAsJob('aws:active-domains');
                });
                $this->commandAsJob('aws:verify-domains')->hourly()->then(function () {
                    $this->commandAsJob('aws:verify-via-dns');
                });
            }

            $this->commandAsJob('cart:forget')->hourly()->timezone('Asia/Riyadh');
            $this->commandAsJob('cart:not-updated')->everyThreeHours()->timezone('Asia/Riyadh');
            $this->commandAsJob('cart:forget-webhook')->hourly()->timezone('Asia/Riyadh');
            $this->commandAsJob('cart:forget-webhook', ['--type' => 'update'])->hourly()->timezone('Asia/Riyadh');

            $this->commandAsJob('coupon:update-payment-methods')->hourly()->timezone('Asia/Riyadh');
            $this->commandAsJob('store:visitors')->monthlyOn(1);

            $this->commandAsJob('complaint:send-reminder-emails')->hourly();
            // $this->commandAsJob('group:re-assign-customers')->twiceDaily(6, 16);

            $this->commandAsJob('domain:move')->dailyAt('00:00');

            // 🚨 never uncomment this before ask me (Salah Alkhwlani)
            // $this->commandAsJob('salla:categories-update-products-count')->dailyAt('04:00')->timezone('Asia/Riyadh');


            $this->commandAsJob('send_app_renew_email:reminders')->dailyAt('00:00')->timezone('Asia/Riyadh');

            $this->commandAsJob('schedule:clearcustomersproducts')->daily();

            $this->commandAsJob('salla-store:abandoned-cart')
                ->everyThirtyMinutes()
                ->timezone('Asia/Riyadh');

            $this->monitor($this->schedule);

            // Start Of Shipping Commands
            $this->commandAsJob('schedule:salasa:daily-sync-inventory')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('schedule:sync-salasa-inventory-daily')->dailyAt('03:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('schedule:storage-station:daily-sync-inventory')->dailyAt('00:00')->timezone('Asia/Riyadh');

            $this->commandAsJob('shipping:generate-cod-invoice')->dailyAt('00:00')->timezone('Asia/Riyadh');
            $this->commandAsJob('shipping:auto-match-cod-invoices')->dailyAt('01:00')->timezone('Asia/Riyadh');
            
            //Sync Shipments
            $this->commandAsJob('orders:shipemants:sync-status-by-company')->everyThirtyMinutes()->timezone('Asia/Riyadh');
            //$this->commandAsJob('shipping:sync-delivered-cod-shipments')->twiceDaily(15, 22)->timezone('Asia/Riyadh');
            $this->commandAsJob('shipping:sync-delivered-cod-shipments')->hourly()->timezone('Asia/Riyadh');
            // Remove this command after migration is complete
            $this->commandAsJob('shipping:shipment-op-migrate')
                ->cron('0 1 9 7 *') // minute hour day month weekday
                ->timezone('Asia/Riyadh');
                
            // $this->commandAsJob('shipping:fill-order-shipment-status-history')->hourly()->timezone('Asia/Riyadh');
            //$this->commandAsJob('shipping:generate-stores-tiers')->monthlyOn(1,'00:00')->timezone('Asia/Riyadh');

            $schedule->command('shipping:feedback')->monthlyOn(1, '00:00')->timezone('Asia/Riyadh');
            $schedule->command('shipping:feedback')->monthlyOn(15, '00:00')->timezone('Asia/Riyadh');

            //$this->commandAsJob('shipping:fill-missing-shipment-statuses-mapping')->dailyAt('00:00')->timezone('Asia/Riyadh');
            // End of Shipping Commands
        }

        $schedule->command('impex:file:processing')->everyFiveMinutes();
        $this->commandAsJob('impex:import')->everyFiveMinutes();
        $this->commandAsJob('customers:import')->everyTenMinutes();
        $this->commandAsJob('brands:import')->everyTenMinutes();
        $this->commandAsJob('categories:import')->everyTenMinutes();

        $this->commandAsJob('salla:close-inactive-stores')
            ->dailyAt('04:00')
            ->timezone('Asia/Riyadh');

        $this->commandAsJob('money:update-rate')
            ->dailyAt('03:00')
            ->timezone('Asia/Riyadh');

        // heartbeat check
        $schedule->job(new HeartBeatCheck(), 'webhook')->everyTenMinutes();

        //Payment
        $this->commandAsJob('credit-card:clean')->weeklyOn(5)->withoutOverlapping(self::LOCKED_EXPIRED); //at friday
        $schedule->command('salla-store-tax-invoice:generate')->monthlyOn(1, '01:00')->timezone('Asia/Riyadh');
        $this->commandAsJob('salla-store-credit-note:generate')->monthlyOn(5)->withoutOverlapping(self::LOCKED_EXPIRED);

        //    $this->commandAsJob('orders:generate-invoices')->everyMinute();
        
        $this->commandAsJob('schedule:loyalty-system:approve-affiliate-link-points')->everyFifteenMinutes()->timezone('Asia/Riyadh');
        $this->commandAsJob('schedule:loyalty-system:approve-buy-from-store-points')->everyThirtyMinutes()->timezone('Asia/Riyadh');
        $this->commandAsJob('schedule:loyalty-system:update-customers-point-wallet')->dailyAt('00:00')->timezone('Asia/Riyadh');
        $this->commandAsJob('wallet:customer:deduct-expired')->dailyAt('00:00')->timezone('Asia/Riyadh');
        // Set this time because SMS messages are allowed to be sent between 10 AM to 10 PM only
        $this->commandAsJob('schedule:loyalty-system:point-expiry-reminder')->dailyAt('13:00')->timezone('Asia/Riyadh');

        // Gift System
        $this->commandAsJob(\Modules\GiftSystem\Console\DeleteGiftAssetCommand::class)
            ->fridays()
            ->at('09:00')
            ->timezone('Asia/Riyadh');

        //Delete Expire Inviation Employees
        $this->commandAsJob('salla:delete-expired-users-invitations')->dailyAt('03:00')->timezone('Asia/Riyadh');

        //Rutter Api
        $this->commandAsJob('rutter-api:mark-rutter-import-as-completed')->everyThreeHours();

        $this->commandAsJob('store:create-custom-status')->everyFiveMinutes();
        //$this->commandAsJob('es:index:orders')->hourly();

        $this->commandAsJob(\Modules\MahlyDash\Console\Commands\ActivateMahlyForLeakedStores::class)
            ->dailyAt('05:00')
            ->timezone('Asia/Riyadh');

        $this->commandAsJob(\Modules\MahlyDash\Console\Commands\DeactivateMahlyForLeakedStores::class)
            ->dailyAt('05:30')
            ->timezone('Asia/Riyadh');

        $this->commandAsJob('deactivate:expired-bank-offers')->dailyAt('01:00')->timezone('Asia/Riyadh');
        $this->commandAsJob('deactivate:expired-cashback-offers-banner')->dailyAt('01:00')->timezone('Asia/Riyadh');

        // https://laravel.com/docs/10.x/queues#pruning-batches
        // delete all finished batches older than 7 days
        // $this->schedule->commandAsJob('queue:prune-batches --hours=48 --unfinished=72')->daily();
        $this->commandAsJob('queue:prune-batches')->weekly();
        $schedule->job(new BulkPurgeCloudflareCacheJob)->everyFiveMinutes();
        $this->monitor($schedule);

        // Marketing
        $this->commandAsJob('coupon:remove-old-files')
            ->monthlyOn(3, '06:00')
            ->timezone('Asia/Riyadh');
        $this->commandAsJob('wallet:customer:deduct-expired')->hourly();
        // $this->commandAsJob('marketing-integration:refund-campaigns')->hourly();
        $this->commandAsJob('sweply:refund')->everyThirtyMinutes()->onOneServer();
        $this->commandAsJob('marketing-integration:delete-credit-notes')->everyThirtyMinutes();

        $this->commandAsJob('marketing-integration:add-routes-permissions')->everySixHours();

        $this->commandAsJob('sweply:refund:canceled')->everyThirtyMinutes()->onOneServer();
    }

    public function commandAsJob($command, array $parameters = [], string $queue = 'schedule')
    {
        if (class_exists($command)) {
            $command = Container::getInstance()->make($command)->getName();
        }

        return $this->schedule->job(new RunCommandAsJob($command, $parameters), $queue);
    }

    /**
     * Get the name of the cache store that should manage scheduling mutexes.
     *
     * @return string
     */
    protected function scheduleCache()
    {
        return config('cache.schedule');
    }

    /**
     * Register the Closure based commands for the application.
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');
        require base_path('routes/console.php');
    }
}
