<?php

namespace App\Excel\Exports;

use App\Models\Product;
use App\Models\ProductSkus;
use Illuminate\Database\Query\Builder;
use Modules\Orders\Enum\OrderStatus;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use Modules\Product\Repositories\ProductRepository;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;


class InventoryExport implements
    FromQuery,
    WithHeadings,
    WithMapping,
    WithEvents,
    WithCustomChunkSize
{
    use Exportable;


    /**
     * @var array
     */
    private $soldStatusArray;

    /**
     * @var array
     */
    protected $filters;

    /**
     * @var int
     */
    protected $store_id;

    /**
     * @var int
     */
    private $columnsCount;


    /**
     * InventoryExport constructor.
     * @param $store_id
     * @param array $filters
     */
    public function __construct($store_id, $filters = [])
    {
        $this->store_id = $store_id;

        $this->filters = $filters;

        $this->soldStatusArray = store()->getSetting('reports::order-statuses', OrderStatus::DefaultSoldStatuses());

        $this->columnsCount = count($this->soldStatusArray) + 6;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return array_merge([
            'الاسم',
            'sku',
            'السعر',
            'الكمية الإجمالية',
            'الكمية المتبقية',
            'الكمية المباعة',
        ], $this->getStatusColsTitles());
    }

    /**
     * @return array
     */
    protected function getStatusColsTitles(): array
    {
        $soldArr = [];
        $labels = OrderStatus::choices();
        foreach ($this->soldStatusArray as $soldStatus) {
            $soldArr[] = 'الكمية المباعة لحالة الطلب ' . $labels[$soldStatus];
        }

        return $soldArr;
    }


    /**
     * query used in export product inventory data
     * it is used for both product inventory filters export and general export
     * @return Builder
     */
    public function query()
    {
        return resolve(ProductRepository::class)->getInventory(null, '', true, true, $this->filters);
    }

    /**
     * @param Product $product
     *
     * @return array
     */
    public function map($product): array
    {
        $countPerStatus = $product->getCountsSoldPerStatusAttribute($this->soldStatusArray);
        $countSold = $countPerStatus['count_sold'] ?? '0';
        unset($countPerStatus['count_sold']);
        $statusCols = array_values($countPerStatus);

        // quantity and remaining quantity, unlimited_quantity
        $productRow = array_merge([
            optional($product)->name,
            optional($product)->sku,
            currency()->format($product->getPrice()),
            // total quantity الإجمالي
            $product->unlimited_quantity ?
                'كمية غير محدودة' :
                (string) ($product->quantity >= 0 && $countSold >= 0 ? $product->quantity + $countSold : '0'),
            // المتبقي
             $product->unlimited_quantity ? 'كمية غير محدودة' : ($product->quantity > 0 ? $product->quantity : '0'),
            $countSold,
        ], $statusCols);

        // if the product don't have options return $row
        if (!$product->isAdvance() || ($product->productSkus->isEmpty())) {
            return $productRow;
        }

        // if the product has options in case [product type==product], get the skus rows
        return $this->getProductSkusRow($product, $productRow);
    }


    /**
     * @param $product
     * @param $productRow
     * @return array
     */
    protected function getProductSkusRow($product, $productRow): array
    {
        // push skus rows to the parent productRow, so there will be a row for product followed by it's skus
        $rows[] = $productRow;

        // TODO:: need enhancement !!! nested loop
        /** @var ProductSkus $sku */
        foreach ($product->productSkus as $sku) {
            $statusCols = [];
            foreach ($this->soldStatusArray as $status) {
                $columnValue = $sku->{'sum_' . $status};
                $statusCols[] = $columnValue ?? '0';
            }

            $rows[] = array_merge([
                $product->name . (count($sku->productOptionsDetails) ? ' - ' . implode('/', $sku->productOptionsDetails->pluck('option_details_name')->toArray()) : ''),
                $sku->sku,
                currency()->format($sku->getPrice()),

                // total stock_quantity الإجمالي
                $sku->unlimited_quantity ?
                    'كمية غير محدودة' :
                    (string) ($sku->stock_quantity >= 0 && $sku->count_sold >= 0 ? $sku->stock_quantity + $sku->count_sold : '0'),

                // المتبقي
                $sku->unlimited_quantity ?
                    'كمية غير محدودة' :
                    ($sku->stock_quantity > 0 ? $sku->stock_quantity : '0'),


                $sku->count_sold ?? '0',
            ], $statusCols);
        }

        return $rows;
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->setRightToLeft(true);
                $lastCharacter = getNameExcelCellFromNumber($this->columnsCount);
                $cellRange = "A1:{$lastCharacter}1";


                $event->sheet->getDelegate()
                    ->getStyle($cellRange)
                    ->getFont()
                    ->setSize(14)
                    ->setBold(1)
                    ->setColor((new Color())->setRGB('FFFFFF'));
                $styleArray = [
                    'borders' => [
                        'outline' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['argb' => '#FFFFFF'],
                        ],
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ];
                $event->sheet->getStyle($cellRange)
                    ->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('5DD5C4');
                $event->sheet->getDelegate()->getStyle($cellRange)->applyFromArray($styleArray);
                $event->sheet->getDelegate()->getRowDimension(1)->setRowHeight(30);

                /* Freeze header row */
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
