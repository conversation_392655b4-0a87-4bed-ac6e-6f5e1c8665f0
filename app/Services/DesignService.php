<?php

namespace App\Services;

use App\Libraries\UploaderLib;
use App\Models\Page;
use App\Transformers\PageTransformer;
use App\Models\SallaSubscriptions;
use Illuminate\Database\Eloquent\Collection;
use Modules\Component\Entities\Block;
use Modules\Component\Entities\StoreBlock;
use Modules\Component\Enum\BlocksSlugs;
use Modules\Component\Enum\BlockType;
use Modules\Component\Transformers\Dashboard\StoreBlockAppBuilderTransformer;
use Modules\Product\Entities\Brand;
use Modules\Product\Entities\Category;
use Modules\Product\Repositories\CategoryRepository;
use Modules\Product\Transformers\BrandsTransformer;
use Modules\Product\Transformers\Dashboard\CategoryTransformer;
use Modules\ThemeCustomization\Enums\ThemeFeatures;

class DesignService
{
    private $uploader_lib;

    public function __construct(UploaderLib $uploader_lib)
    {
        $this->uploader_lib = $uploader_lib;
    }

    //this now for app builder only, not used in dashboard anymore
    public function getDesign(bool $isAppMaker = false): array
    {
        return [
            'store_url'                     => store()->getDomain(),
            'categories'                    => transformation(resolve(CategoryRepository::class)->fetchAllCategories(null, false, null, true, false, [], false), CategoryTransformer::class)->transform(),
            'pages'                         => transformation(resolve(Page::class)->findAll(), PageTransformer::class)->transform(),
            'blocks'                        => $this->getThemeBlocks($isAppMaker),
            'store_blocks'                  => transformation($this->getStoreBlocks(['app', 'all']), StoreBlockAppBuilderTransformer::class)->transform(),
            'brands'                        => transformation(Brand::all(), BrandsTransformer::class)->transform(),
            'builder_supported_block_types' => BlockType::builderSupportedBlockTypes(),
            'alerted_block_types'           => $this->getAlertedBlockTypes(),
            'blog_accessible'               => feature('store-blog')->isAccessibleForCustomers(),
            'classifications_supported_block_types' => BlockType::classificationsSupportedBlockTypes(),
            'classifications_blocks'        => $this->getClassificationsBlocks(),

        ];
    }

    protected function getThemesList(): Collection
    {
        return SallaSubscriptions::where('type', 'theme')
            ->whereNotIn('status', ['deleted', 'ended'])
            ->with('sallaProduct.single_image')
            ->get();
    }

    public function getThemeBlocks(bool $isAppMaker = false): \Illuminate\Support\Collection
    {
        //get custom components for current theme
        $blocks     = Block::query()->orderBy('order')->get();

        $components = optional(storeTheme()->getThemeModel())->customComponents() ?? [];
        $components = collect($components ?? [])
            ->map(function ($customComponent) use ($blocks) {
                $customComponent['id']   = $blocks->firstWhere('slug', BlocksSlugs::CUSTOM_COMPONENT)->id;
                $customComponent['slug'] = BlocksSlugs::CUSTOM_COMPONENT;
                $customComponent['name'] = $customComponent['title'];
                return (object) $customComponent;
            });
        if (!$isAppMaker) {
            $blocks = $blocks->filter(fn(Block $block) => $block->canAddNew());
        }

        return $components->merge($blocks->values());
    }

    protected function isThemeHasHomeBlocks(): bool
    {
        if (storeTheme()->isTwigTheme()) {
            return storeTheme()->getThemeModel()->hasHomeBlocks();
        }
        return !storeTheme()->isTheme('default');
    }

    public function getStoreBlocks(array $showIn = ['web', 'all']): Collection|array
    {
        return StoreBlock::query()
            ->with('block')
            ->whereIn('show_in', $showIn)
            ->forLanding(optional(request()->route('landing'))->getKey())
            ->where('is_classification', false)
            ->sort()
            ->get()
            ->filter(fn(StoreBlock $block) => $block->availableInTheme())
            ->values();
    }

    public function uploadPhoto($request)
    {
        $response = $this->uploader_lib->uploadDesignImage($request->product_image);

        return $response['url'];
    }

    public function getClassificationsBlocks(): \Illuminate\Support\Collection
    {
        return Block::query()
                    ->whereIn('slug',
                        array_merge(BlockType::classificationsSupportedBlockTypes(), BlockType::designClassificationsSupportedBlockTypes())
                    )->get();
    }

    private function getAlertedBlockTypes(): array
    {
        $builderSupportedBlockTypes = BlockType::builderSupportedBlockTypes();

        return array_values(array_filter($builderSupportedBlockTypes, fn(string $blockType) => !$this->isBlockSupported($blockType)));
    }

    private function isBlockSupported(string $slug): bool
    {
        return BlocksSlugs::IsMobileOnly($slug) ||
             (storeTheme()->isLegacyTheme() ?
               BlocksSlugs::IsLegacy($slug)
               : storeTheme()->hasFeature(ThemeFeatures::GetBlockFeatureName($slug) ?? $slug)
              && BlocksSlugs::IsAddable($slug));
    }
}
