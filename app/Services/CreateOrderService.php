<?php

namespace App\Services;

use App\Models\City;
use App\Models\Order;
use App\Models\Coupon;
use Salla\Money\Money;
use App\Models\Country;
use App\Models\Product;
use App\Models\OrderItems;
use App\Models\SkuOptions;
use App\Libraries\OrderLib;
use Illuminate\Http\Request;
use App\Models\CouponHistory;
use App\Libraries\UploaderLib;
use App\Models\OrderItemOptions;
use Illuminate\Support\Facades\DB;
use App\Libraries\ShippingFrontLib;
use Salla\Core\Enum\RequestHeaders;
use Salla\Core\Enum\WeightTypeEnum;
use Modules\Orders\Enum\OrderStatus;
use Modules\Product\Enum\ProductType;
use Salla\Core\Libraries\GenderApiLib;
use App\Traits\CreateOrderServiceTrait;
use Modules\Customer\Entities\Customer;
use Modules\Orders\Actions\DeleteOrder;
use Modules\Product\Enum\ProductStatus;
use Salla\Core\Facades\WeightConverter;
use Salla\Events\Dashboard\OrderPlaced;
use Salla\Paymetns\Enum\PaymentMethods;
use Salla\StoresCart\Enums\DiscountType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use Modules\Product\Entities\ProductSkus;
use Modules\Orders\Entities\OrderMetaData;
use Modules\Orders\Enum\OrderEnumProvider;
use Modules\Orders\Facades\CashOnDelivery;
use Modules\Orders\Jobs\UpdateOrderStatus;
use Modules\Product\Enum\ProductOptionType;
use Propaganistas\LaravelPhone\PhoneNumber;
use Modules\Customer\Entities\StoreCustomers;
use Illuminate\Validation\ValidationException;
use Salla\Core\Traits\Helper\HasDecodeOptimus;
use Modules\Orders\Actions\CreateCartFromOrder;
use Modules\Orders\Entities\OrderShipmentBranch;
use Modules\Orders\Entities\AddressShippingOrder;
use Modules\Product\Enum\ProductOptionDetailName;
use Modules\Product\Enum\ServiceProductFieldType;
use Modules\Product\Entities\ProductOptionDetails;
use Modules\Product\Enum\ProductQuantityLogSource;
use Modules\Orders\Listeners\OrderInvoiceListener;
use Modules\Orders\Actions\Items\ReduceItemQuantity;
use Modules\Orders\Actions\Items\CreateOrderSubItems;
use Modules\Orders\Actions\Items\RestoreItemQuantity;
use Modules\Orders\Actions\Items\UpdateOrderSubItems;
use Modules\Orders\Actions\ItemsStock\StoreItemStock;
use Modules\Orders\Presenter\CashOnDeliveryPresenter;
use Modules\Product\Repositories\SkuOptionRepository;
use Salla\Events\Dashboard\Order\OrderPaymentUpdated;
use Modules\SpecialOffer\Services\SpecialOfferService;
use Salla\Events\Dashboard\Order\OrderCustomerUpdated;
use Salla\Events\Dashboard\Order\OrderProductsUpdated;
use App\Http\Requests\ValidateAddProductToOrderRequest;
use Modules\Product\Actions\Product\CreateCustomProduct;
use Modules\Customer\Actions\Customer\FindOrCreateCustomer;
use Modules\LoyaltySystem\Actions\RefundOrderLoyaltyPoints;
use Modules\Product\Actions\OrderOptions\FetchOrderOptions;
use Modules\StoreBranch\Repositories\StoreBranchRepository;
use Salla\Events\Dashboard\Order\OrderCouponAppliedUpdated;
use Modules\Checkout\Actions\Payment\Order\UpdatePaymentLog;
use Modules\Orders\Actions\ItemsStock\UpdateOrderItemsStock;
use Modules\Orders\Listeners\UpdateOrderDigitalCodeListener;
use Modules\Orders\Actions\ItemsStock\RefreshOrderItemStocks;
use Modules\Orders\Actions\Items\ReduceOrRestoreOrderItemStock;
use Modules\Product\Repositories\ProductOptionDetailsRepository;
use Modules\Product\Transformers\Dashboard\BranchQuantityTransformer;
use Modules\Orders\Actions\ItemsStock\RefreshOrderItemStockWeightAction;
use Modules\Orders\Actions\CreateOrder\ShippingDetails\SaveOrderShippingDetails;
use Modules\Orders\Actions\Shipping\OrderShipmentBranch\CreateOrderShipmentForBranch;

//TODO:: Refactoring.
class CreateOrderService
{
    use HasDecodeOptimus, CreateOrderServiceTrait;

    /**
     * @var \Modules\Orders\Services\CouponService
     */
    protected $couponService;

    /**
     * @var \Modules\SpecialOffer\Services\SpecialOfferService
     */
    protected $specialOfferService;

    /**
     * @var Order
     */
    private $orderModel;
    private $customerModel;
    private $storeCustomersModel;
    private $cityModel;
    private $countryModel;
    private $productModel;
    private $uploader_lib;
    private $orderItemsModel;
    private $productOptionDetailsRepository;
    private $orderItemOptionsModel;
    private $couponHistoryModel;
    private $order_lib;
    private $orderService;
    private $shippingFrontLib;
    private $skuOptionsModel;

    /**
     * @var SkuOptionRepository
     */
    private $skuOptionRepository;

    private bool $dashController = false;

    public function __construct(
        Order                                  $orderModel,
        Customer                               $customerModel,
        StoreCustomers                         $storeCustomersModel,
        City                                   $cityModel,
        Country                                $countryModel,
        Product                                $productModel,
        UploaderLib                            $uploader_lib,
        OrderItems                             $orderItemsModel,
        ProductOptionDetailsRepository         $productOptionDetailsRepository,
        OrderItemOptions                       $orderItemOptionsModel,
        CouponHistory                          $couponHistoryModel,
        OrderLib                               $order_lib,
        OrderService                           $orderService,
        ShippingFrontLib                       $shippingFrontLib,
        SkuOptions                             $skuOptionsModel,
        \Modules\Orders\Services\CouponService $couponService,
        SpecialOfferService                    $specialOfferService,
        SkuOptionRepository                    $skuOptionRepository
    )
    {
        // TODO:: it's wrong to create instances for all models here in construction method.
        // We should only initiate here classes that we will use them in all (or at least in most) methods.
        $this->customerModel = $customerModel;
        $this->orderModel = $orderModel;
        $this->storeCustomersModel = $storeCustomersModel;
        $this->cityModel = $cityModel;
        $this->countryModel = $countryModel;
        $this->productModel = $productModel;
        $this->uploader_lib = $uploader_lib;
        $this->orderItemsModel = $orderItemsModel;
        $this->productOptionDetailsRepository = $productOptionDetailsRepository;
        $this->orderItemOptionsModel = $orderItemOptionsModel;
        $this->couponHistoryModel = $couponHistoryModel;
        $this->order_lib = $order_lib;
        $this->orderService = $orderService;
        $this->shippingFrontLib = $shippingFrontLib;
        $this->skuOptionsModel = $skuOptionsModel;
        $this->couponService = $couponService;
        $this->specialOfferService = $specialOfferService;
        $this->skuOptionRepository = $skuOptionRepository;
    }

    public function create()
    {
        $exchange_currency = currency()->getExchangeRate();
        $source = request()->header(RequestHeaders::SOURCE);
        $order = Order::query()->create([
            'draft' => -1,
            'store_id' => app('store')->getId(),
            'currency' => $exchange_currency->getBaseCurrency(),
            'exchange_currency' => $exchange_currency->getCounterCurrency()->getCode(),
            'exchange_currency_rate' => $exchange_currency->getConversionRatio(),
            'user_id' => auth()->id(),
            'source' => ! RequestHeaders::isNewDashboard() && ! blank($source) ? $source : 'dashboard',
            'shippable' => 0,
        ]);

        /* Trigger new  order created event */
        event(new OrderPlaced($order));

        return $order->getKey();
    }

    /**
     * @param Order $oldOrder
     * @param $is_not_created
     * @return Order
     */
    public function reorder(Order $oldOrder, Order $newOrder = null): Order
    {
        $source = request()->header(RequestHeaders::SOURCE);

        $data = [
            'created_at' => \Carbon\Carbon::now()->toDateTimeString(),
            'cod_cost' => 0,
            'city' => null,
            'country' => null,
            'location' => null,
            'geocode' => null,
            'shipping_details_id' => 0,
            'payment_method' => null,
            'epayment_status' => null,
            'epayment_gateway' => null,
            'epayment_id' => null,
            'epayment_fee' => 0.00,
            'receipt_image_path' => '',
            'store_bank_id' => 0,
            'amount' => 0,
            'total' => 0,
            'shipping_cost' => 0,
            'shipping_cost_discount' => 0,
            'shipping_street_number' => null,
            'shipping_block' => null,
            'shipping_company_id' => null,
            'shipping_company_app_id' => null,
            'shipping_private_details_id' => 0,
            'accepted_payment_methods' => null,
            'bank_account_owner' => null,
            'postal_code' => null,
            'discounted_amount' => 0,
            'total_discounted_amount' => 0,
            'shipping_with_tax' => tax()->isShippingTaxable(),
            'user_id' => auth()->id(),
            'source' => ! RequestHeaders::isNewDashboard() && ! blank($source) ? $source : 'dashboard',
            'active_order_invoice_id' => null,
            'cart_id' => null,
            'order_status_id' => 0,
            'order_custom_status_id' => null,
            'store_branch_id' => null,
            'shipping_number' => null,
            'shipment_policy_created_at' => null,
            'shipping_info' => null,
            'smsa_sto_shipping_number' => null,
            'settings' => null,
        ];

        if ($oldOrder->currency != 'SAR') {
            $exchangeCurrency = currency()->getExchangeRate();

            $data = array_merge($data, [
                'currency' => $exchangeCurrency->getBaseCurrency(),
                'exchange_currency' => $exchangeCurrency->getCounterCurrency()->getCode(),
                'exchange_currency_rate' => $exchangeCurrency->getConversionRatio(),
            ]);
        }

        if(!$newOrder) {
            $newOrder = $oldOrder->replicate()->fill(array_merge($data, [
                'draft' => -1,
                'reorder_from_id' => $oldOrder->id,
                'active_order_invoice_id' => null,
                'cart_id' => null,
            ]));

            $newOrder->save();

            $data = [];
        }

        $newOrder = $this->resetUndraftOrder(order: $newOrder, returnOrderObj: true, data: $data);

        $newOrder->setRelation('customer', $oldOrder->customer);

        $this->addProductToReorder($newOrder, $oldOrder);
        $this->reReceiverOrder($oldOrder, $newOrder->id);

        /* Trigger new  order created event */
        event(new OrderPlaced($newOrder));

        return $newOrder;
    }

    private function reReceiverOrder(Order $order, int $newOrderId)
    {
        $receiver = AddressShippingOrder::where('order_id', $order->id)->first();

        if ($receiver) {
            AddressShippingOrder::query()->updateOrCreate([
                'order_id' => $newOrderId,
            ], [
                'name' => $receiver->name,
                'phone_number' => $receiver->phone_number,
                'email' => $receiver->email,
                'notify' => (bool)$receiver->notify,
                'order_id' => $newOrderId
            ]);

        } else {
            AddressShippingOrder::query()->where('order_id', '=', $newOrderId)->safeDelete();
        }

        $this->updateCartOrder($order, 'shipping', null, true);
    }

    public function searchCustomer($query, $store_id)
    {
        $data = $this->customerModel->liveSearch($query, $store_id);
        $arr = [];
        foreach ($data as $key => $item) {
            if ($data[$key] instanceof Customer && $data[$key]->isCustomerAccountDeactivated()) {
                continue;
            }
            $arr[$key]['id'] = $item->id;
            $arr[$key]['name'] = $item->full_name;
            $arr[$key]['mobile'] = $item->mobile_code_country . $item->mobile;
        }

        return $arr;
    }

    public function showCustomerForm($order)
    {
        $customer_data = (0 != $order->customer_id) ? $this->customerModel->findOne($order->customer_id, app('store')->getId()) : null;
        if (0 != $order->customer_id && (!$customer_data || !$customer_data->currentStoreCustomer)) {
            abort(404);
        }
        $data['customer'] = $customer_data;
        $data['store_customer_id'] = ($customer_data) ? optimus()->encode($customer_data->currentStoreCustomer->id) : null;
        $data['order'] = $order;
        $data['countries'] = $this->countryModel->findAll(-1);

        return $data;
    }

    public function showCustomerInfo($customer_id)
    {
        /** @var Customer $customer */
        $customer = $this->customerModel->findOne($customer_id, app('store')->getId());

        if (!$customer || !$customer->currentStoreCustomer) {
            abort(404);
        }

        // if we have solid number let's setup TODO:: clean up
        if ($customer && $customer->getMobileInstance() && !store()->isDemoStore()) {
            $customer->store_customer_id = optimus()->encode($customer->currentStoreCustomer->id);
            $customer->country_code = $customer->getMobileInstance()->getCountryCode() ?: 'SA';
            $customer->mobile_code_country =  '+' . $customer->getMobileInstance()->getCountryCode() ?: '+966';
            $customer->mobile =   $customer->getMobileInstance()->getNationalNumber();
            $customer->mobileCustomer =  \libphonenumber\PhoneNumberUtil::getInstance()->format(
                $customer->getMobileInstance(),
                \libphonenumber\PhoneNumberFormat::E164
            );
            // $customer->country_code = $customer->getMobileInstance()->getCountry() ?: 'SA';
            // $customer->mobile_code_country =  '+' . $customer->getMobileInstance()->getPhoneNumberInstance()
            //     ->getCountryCode() ?: '+966';
            // $customer->mobile =   $customer->getMobileInstance()->getPhoneNumberInstance()->getNationalNumber();
            // $customer->mobileCustomer =   $customer->getMobileInstance()->formatE164();
        }

        if (feature()->isReleased('custom-customer-fields') && $customer) {
            $customer->customFieldsValues->map(function ($field) {
                $field->id = app('optimus')->encode($field->id);

                return $field;
            });
        }

        return $customer;
    }

    /**
     * @param \App\Models\Order $order
     * @param Request $request
     *
     * @return array
     */
    public function storeCustomerForm($order, $request)
    {
        currency()->setCurrency($order->getCurrency(), true);

        $customer = ($request->has('store_customer_id') && $request->filled('store_customer_id')) ?
            StoreCustomers::with('customer')->findOrFail(optimus()->decode($request->store_customer_id))->customer :
            FindOrCreateCustomer::make($request->all())->run();

        $oldCustomerId = $order->customer_id;

        $order->update(['customer_id' => $customer->getKey()]);

        if ($oldCustomerId != $customer->getKey()) {
            $this->recalculateShipping($order);
        }

        if ($request->input('addressee_enabled') && $request->filled('addressee_name') && $request->filled('addressee_mobile')) {
            AddressShippingOrder::query()->updateOrCreate([
                'order_id' => $order->getKey(),
            ], [
                'name' => $request->addressee_name,
                'phone_number' => $this->getPhoneNumber($request->input('addressee_mobile'), $request->input('addressee_country_code'))->formatE164(),
                'email' => $request->addressee_email,
                'notify' => $request->receiver_notify == 'true',
            ]);
        } else {
            AddressShippingOrder::query()->where('order_id', '=', $order->getKey())->safeDelete();
        }

        $this->updateCartOrder($order, 'shipping', null, true);

        $wasChanged = $order->wasChanged() && ((optional($customer->currentStoreCustomer)->wasChanged() ?? true) || ($oldCustomerId != $customer->getKey()));
        event((new OrderCustomerUpdated($order, $wasChanged ? sprintf('تم تحديث بيانات العميل (%s)', $customer->full_name) : ''))->setFireWebhook($wasChanged));

        $data['case'] = 'success';

        return $data;
    }

    public function updateOrderOptionsForm($order, $request)
    {
        $requestData = $request->all();

        if (empty($requestData['pk'])) {
            return ['case' => 'success'];
        }

        $orderOptionId = $requestData['pk'];
        $value = $requestData['value'];

        $orderItem = OrderItems::with('product.productOptions')->findOrFail($orderOptionId);

        // incase if the product is order option of type date
        // so it should not had an order item option
        if(!$orderItem->oItemOptions()->count()){
            return;
        }

        $product = $orderItem?->product;
        $productOption = $product?->productOptions?->first();
        $productId = $product?->getRouteKey();
        $payload = [
            'product' => [
                $productId => [
                    $productOption?->id => $value
                ]
            ]
        ];

        $request->merge($payload);
        $this->storeOrderOptionsForm($order, $request, $productId);
        return ['case' => 'success'];
    }

    public function storeOrderOptionsForm($order, $request, $orderOptionId)
    {
        $requestData = $request->all();
        $delegateTo = 'addItem';

        if (!empty($orderOptionId)) {
            $orderOptionId = optimus()->decode($orderOptionId);
            $delegateTo = 'updateItem';
        }

        if (empty($requestData['product'])) {
            return ['case' => 'success'];
        }

        foreach ($requestData['product'] as $productId => $optionDetails) {
            foreach ($optionDetails as $optionId => $value) {

                if (blank($value)) {
                    if ($orderOptionId) {
                        throw ValidationException::withMessages(['error' => 'لا يمكن ترك الحقل فارغاً.']);
                    }
                    continue;
                }

                $product = Product::withoutGlobalScope('excludeOrderOption')
                    ->with('productOptions.details')
                    ->where('id', optimus()->decode($productId))
                    ->first();

                $exchange_currency = currency()->getExchangeRate($order->getCurrency());
                $productPrice = !empty($product->price) ? $product->price : 0;
                $orderItemOption = $this->prepareOrderOption($product, $optionId, $value);
                $option = $product->productOptions->where('id', $optionId)->first();
                if (empty($option)) {
                    return [];
                }

                if (in_array($option->type, [ProductOptionType::CHECKBOX, ProductOptionType::RADIO])) {
                    $productPrice = $orderItemOption['p_option_details_price'];
                }

                $product_quantity = 1;
                DB::beginTransaction();
                $orderItem = $order->items()->updateOrCreate([
                    'order_id' => $order->id,
                    'product_id' => $orderOptionId,
                ], [
                    'order_id' => $order->getKey(),
                    'product_id' => $product->getKey(),
                    'name' => escape_emoji($product->name) ?: null,
                    'product_price' => $productPrice,
                    'quantity' => $product_quantity,
                    'notes' => '',
                    'total' => ($productPrice) * $product_quantity,
                    'product_skus_id' => null,
                    'product_sku_code' => null,
                    'currency' => !empty($product->currency) ? $product->currency : null,
                    'exchange_currency' => $exchange_currency->getCounterCurrency()->getCode(),
                    'exchange_currency_rate' => $exchange_currency->getConversionRatio(),
                    'cost_price' => !empty($product->cost_price) ? $product->cost_price : null,
                    'weight' => null
                ]);

                $orderItem->oItemOptions()->safeDelete();
                $orderItem->oItemOptions()->create($orderItemOption);
                $this->recalculateShipping($order->refresh());

                $this->updateCartOrder($order, $delegateTo, $orderItem, true);
                DB::commit();
            }
        }


        $data['case'] = 'success';

        return $data;
    }

    /**
     * @param $optionDetailId
     * @param $price
     * @param $option_id
     * @param $option_data
     */
    protected function prepareOptions($optionDetailId, $price, $option_id, $option_data)
    {
        return [
            'p_option_details_id' => $optionDetailId,
            'p_option_details_price' => $price,
            'product_option_id' => $option_id,
            'option_data' => $option_data,
        ];
    }

    public function prepareOrderOption($product, $optionId, $optionValue)
    {
        $option = $product->productOptions->where('id', $optionId)->first();
        if (empty($option)) {
            return [];
        }

        $data = ['product_id' => $product->getKey()];

        switch ($option->type) {
            case ServiceProductFieldType::RADIO:
                $data += $this->handleRadioType($option, $optionValue);
                break;

            case ServiceProductFieldType::CHECKBOX:
                $data += $this->handleCheckboxType($option, $optionValue);
                break;

            default:
                $data += $this->handleDefaultType($option, $optionValue);
                break;

        }

        return $data;
    }

    private function handleRadioType($option, $request_option)
    {
        /** @var  ProductOptionDetails $details */
        $details = $option->details()->where('id', optimus()->decode($request_option))->first();

        if (!$details) {
            return [];
        }

        $price = Money::amountFromSmallest($details->getPrice()->getBasePrice());
        return $this->prepareOptions($details->getKey(), $price, $option->id, null);
    }

    /**
     * @param $key
     * @param \Modules\Product\Entities\ProductOptions $option
     * @param $request_option
     */
    protected function handleCheckboxType($option, $request_option)
    {
        if (!\is_array($request_option)) {
            return [];
        }
        $decodecValue = array_map(function ($encodeId) {
            return optimus()->decode($encodeId);
        }, $request_option);


        $details = $option->details()->whereIn('id', $decodecValue)->get();

        if ($details->isEmpty()) {
            return [];
        }

        $price = $details->sum(function (ProductOptionDetails $productOptionDetail) {
            return Money::amountFromSmallest($productOptionDetail->getPrice()->getBasePrice());
        });

        return $this->prepareOptions(0, $price, $option->id, $details->pluck('id')->implode(','));
    }

    /**
     * @param $key
     * @param $option
     * @param $request_option
     */
    protected function handleDefaultType($option, $request_option)
    {
        return $this->prepareOptions(0, 0, $option->id, $request_option);
    }

    /**
     * This function will filter order options that have already been selected
     *  and add the availability based on selected products in orders
     * @param $orderId
     * @return array|mixed
     */
    public function getProcessOrderOptions($orderId, $orderOptionId = null)
    {
        $order = Order::with(['items.product.categories', 'items.oItemOptions'])
            ->whereHas('items.product', function ($q) {
                return $q->where('type', '!=', ProductType::ORDER_OPTION);
            })
            ->where('id', $orderId)
            ->first();

        if (empty($order?->items)) {
            return [];
        }

        $orderOptions = FetchOrderOptions::make(['isProductOptionDetailsProcessed' => true])->run();

        if ($orderOptionId) {
            $oItemOptions = $order->items->where('product_id', optimus()->decode($orderOptionId))->first()?->oItemOptions;

            $filteredOrderOptions = [];
            foreach ($orderOptions as $orderOption) {
                if ($orderOption['id'] == $orderOptionId) {
                    $orderOption['oItemOptions'] = $oItemOptions;
                    $filteredOrderOptions[] = $orderOption;
                }
            }
        } else {
            $selectedItems = $order?->items->pluck('product_id')->unique()->toArray() ?? [];
            $filteredOrderOptions = array_filter($orderOptions, function ($orderOption) use ($selectedItems) {
                return (!in_array(optimus()->decode($orderOption['id']), $selectedItems)) && ($orderOption['status']);
            });
        }

        return $this->processFilteredOrderOptions($order, $filteredOrderOptions);
    }

    private function processFilteredOrderOptions($order, $filteredOrderOptions)
    {
        foreach ($filteredOrderOptions as &$orderOption) {
            $orderOption['availability'] = ProductOptionType::FULLY_AVAILABLE;
            $extraAttributes = $orderOption['extra_attributes'];

            $oItemOptions = $orderOption['oItemOptions'] ?? [];
            $optionData = null;
            if (!empty($oItemOptions)) {
                $optionData = ($extraAttributes['field_type'] == 'radio')
                    ? optional($oItemOptions)->first()?->p_option_details_id
                    : optional($oItemOptions)->first()?->option_data;
            }
            $orderOption['extra_attributes']['option_data'] = $optionData;


            if ($orderOption['extra_attributes']['product_category'] == 'selected') {
                $availbility = false;
                $notAvailableProduct = [];
                $availableProduct = [];

                foreach ($order->items as $item) {
                    if ($item->product->type == ProductType::ORDER_OPTION) {
                        continue;
                    }

                    $productCategories = $item->product?->categories?->pluck('category_id')?->map(fn($categoryId) => optimus()->encode($categoryId))->toArray();

                    $selectedCategories = array_map(fn($selectedid) => $selectedid['id'] ?? $selectedid, $orderOption['selected_categories']);

                    $sameCategories = array_intersect($productCategories, $selectedCategories);

                    if (!empty($sameCategories) && !empty($productCategories)) {
                        $availbility = true;
                        $availableProduct[] = $item->product->id;
                    } else {
                        $orderOption['availability'] = ProductOptionType::PARTIALLY_AVAILABLE;
                        $notAvailableProduct[] = $item->product->name;
                    }
                }

                if (!$availbility) {
                    $orderOption['availability'] = ProductOptionType::NOT_AVAILABLE;
                }
                $orderOption['not_available_products'] = $notAvailableProduct;
                $orderOption['available_products'] = $availableProduct;
            }
        }
        return $filteredOrderOptions;
    }

    private function hasProductAssosiatedOrderOptions($order, $productId, $deleteOrderOption = false): bool
    {
        $orderItems = $this->orderItemsModel->with(['product', 'oItemOptions'])
            ->where('order_id', $order->id)
            ->get();

        if ($orderItems->isEmpty()) {
            return false;
        }

        $orderOrderOptions = (clone $orderItems)->filter(function ($item) {
            return $item->product?->type === ProductType::ORDER_OPTION;
        });
        if ($orderOrderOptions->isEmpty()) {
            return false;
        }

        $orderProducts = (clone $orderItems)->filter(function ($item) {
            return $item->product?->type !== ProductType::ORDER_OPTION;
        });

        $orderOptions = app(FetchOrderOptions::class)->run(['isProductOptionDetailsProcessed' => true]);

        $selectedItems = $orderOrderOptions->pluck('product_id')->unique()->toArray();

        $deleteAllOrderOptions = false;
        //if only one product is remaining and we have order options in order
        if ($orderProducts->count() == 1 && count($selectedItems)) {
            $deleteAllOrderOptions = true;
        }

        $filteredOrderOptions = array_filter($orderOptions, function ($orderOption) use ($selectedItems) {
            return (in_array(optimus()->decode($orderOption['id']), $selectedItems)) && ($orderOption['status']);
        });

        $filteredOrderOptions = $this->processFilteredOrderOptions($order, $filteredOrderOptions);
        $hasOrderOptions = false;
        foreach ($filteredOrderOptions as $orderOption) {

            if ($orderOption['extra_attributes']['product_category'] == 'selected' || $deleteAllOrderOptions) {

                //delete order option if last product in order or no other product assosiated with order option
                if ($deleteAllOrderOptions ||
                    (isset($orderOption['available_products'])
                        && in_array($productId, $orderOption['available_products'])
                        && count($orderOption['available_products']) == 1)) {

                    $hasOrderOptions = true;
                    $orderItm = $orderOrderOptions->where(
                        'product_id', optimus()->decode($orderOption['id'])
                    )->first();

                    if (empty($orderItm) || !$deleteOrderOption) {
                        continue;
                    }

                    $orderItm->oItemOptions()->delete();
                    $orderItm->delete();
                    $this->updateCartOrder($order->refresh(), 'itemDeleted', $orderItm, true);
                }
            }
        }
        return $hasOrderOptions;
    }

    public function orderItemHasOrderOptions(Order $order, $orderItemId)
    {
        $orderItm = $this->orderItemsModel->findOrFail(hasDecode($orderItemId));

        return $this->hasProductAssosiatedOrderOptions($order, $orderItm->product_id);
    }

    /**
     * TODO:: Make A Helper Function.
     * @param $phone
     * @param null $country_code
     * @return PhoneNumber
     */
    public function getPhoneNumber($phone, $country_code = null)
    {
        return phone(
            $phone,
            $country_code
        );
    }

    public function getGender($firstname)
    {
        return GenderApiLib::getGender($firstname);
    }

    public function showShippingForm($order)
    {
        //we load countries, cities relation with order
        $data = $this->getShippingAddressDetails($order);

        $data['shipment'] = $order->getShipmentForCreateOrder();

        return $data;
    }

    public function showShippingList($request, $order_id)
    {
        $param['country_id'] = $request->input('country_id');
        $param['city_id'] = $request->input('city_id');
        $param['city_name'] = $request->input('city_name');
        $param['address'] = $request->input('address');
        $param['geocode'] = $request->input('geocode');
        $param['postal_code'] = $request->input('postal_code');
        $param['order_id'] = $order_id;
        $param['order_shipment_branch_id'] = $request->input('order_shipment_branch_id') ?? null;
        $param['district_id'] = $request->input('district_id') ?? null;

        return $this->shippingFrontLib->getShipping($param);
    }

    /**
     * * This Method will be accessed when create order (in Draft) or update order with one shipment
     *
     * From now on we will save the shipment details in order or order-shipment-branch
     * if its legacy order (with no branch related) we will connect it with default according to a settings and use it
     * orders with feature of (manage products by branches) by default will have a branch
     *
     * so we need to know where to save shipping details
     *
     * @param Request $request
     * @param $order
     * @return array|string[]
     */
    public function saveShipping(Request $request, $order)
    {

        return SaveOrderShippingDetails::make($request->all())->setOrder($order)->run();
    }

    public function showPaymentForm($order)
    {
        $data['banks'] = store()->normalBanks()->get();
        $data['order'] = $order;
        // TODO:: It's Better To Make A Perseneter ..
        $data['availablePaymentMethods'] = store()->getAvailablePaymentMethods(false);

        $data['shipment'] = $order->getShipmentForCreateOrder();

        $data['cod'] = CashOnDelivery::forOrder($data['order'], $data['shipment'])
            ->getCashOnDelivery();

        return $data;
    }

    //TODO:: Reformat
    public function savePayment(Request $request, $order)
    {
        $payment_option = $request->input('payment_option');
        if ($payment_option == -1) {
            return ['case' => 'error', 'message' => 'هل تم الدفع ؟'];
        }

        if ($request->input('cod_cost', 0) < 0) {
            return ['case' => 'error', 'message' => 'حصل خطأ عمولة الدفع عند الاستلام ليست صحيح'];
        }
        $store_bank_id = 0;
        $accepted_payment_methods = $request->input('accepted_payment_methods');
        $isWaiting = $payment_option == 2;//1: Yes Is Paid.
        $payment_due_date = null;
        $payment_reminder = null;
        if (!$isWaiting) {//is Paid
            $payment_method = $request->input('payment_method');
            $store_bank_id = ($request->input('store_bank_id')) ? $request->input('store_bank_id') : 0;
            if (!isset($payment_method)) {
                return ['case' => 'error', 'message' => 'اختر خيار الدفع'];
            }
            $accepted_payment_methods = null;
        } else {//is waiting to pay
            if (!isset($accepted_payment_methods)) {
                return ['case' => 'error', 'message' => 'اختر وسيلة دفع واحدة على الاقل'];
            }
            $payment_method = 'waiting';
            $accepted_payment_methods = implode(',', $accepted_payment_methods);
            if ($accepted_payment_methods === "cod") {
                $payment_method = 'cod';
            }
        }

        currency()->setCurrency($order->getCurrency(), true);

        $oldMethod = $order->payment_method;
        //keep Old Order Status
        $status = $order->order_status_id;
        //if User Changed Order To Not Paid, Change Order status to payment_pending.
        if ($isWaiting) {
            $status = OrderStatus::PAYMENT_PENDING;
            //if User Change Order To Paid, and old status is waiting, change order status to under review.
        } elseif ($status == OrderStatus::PAYMENT_PENDING) {
            $status = OrderStatus::UNDER_REVIEW;
        }
        //TODO:: Consider Custom Status
        $item_order = [
            'payment_method' => $payment_method,
            'store_bank_id' => $store_bank_id,
            'accepted_payment_methods' => $accepted_payment_methods,
            'payment_due_date' => $payment_due_date,
            'payment_reminder' => $payment_reminder,
            'cod_cost' => $this->decimalFormat($this->getCodCost()), // cod_cost is decimal(7, 3), add number_format to prevent changes on every update
            'order_status_id' => $status,
            //if the status changed, make sure to update custom_status to null. no need for more queries to get custom status
            'order_custom_status_id' => $status != $order->order_status_id ? null : $order->order_custom_status_id,
        ];

        // If old payment is COD, reset the cart_id.
        if ($oldMethod === PaymentMethods::COD) {
            $item_order['cart_id'] = null;
        }

        $order->fill($item_order);
        $order->save();

        //=> if payment method changes
        $payment_updated = $order->wasChanged(array_keys($item_order));

        $this->reCalculateTotal($order, false, true);

        if ($isWaiting) {
            $this->createCartFromOrder($order);
            $this->updateCartOrder($order, 'total');
        }

        $msg = 'تم تعديل بيانات الدفع';
        if ($oldMethod != $payment_method) {
            $msg .= ' من: ' . PaymentMethods::getLabel($oldMethod)
                . ' إلى: ' . PaymentMethods::getLabel($payment_method);
        }


        event((new OrderPaymentUpdated($order, $msg))->setFireWebhook($payment_updated));


        if ($order->payment_method === PaymentMethods::BANK) {
            UpdatePaymentLog::make()->setOrder($order)->run();
        }

        return [
            'message' => '',
            'case' => 'success',
            'status' => $order->status->name ?? 'جديد',
        ];
    }

    public function searchProduct($query, $store_id, $request, $param = [])
    {
        $param['limit'] = 20;

        if ($request->has('group_products')) {
            $param['group_products'] = '1';
        }

        if ($request->has('landing_page_products')) {
            $param['landing_page_products'] = '1';
        }

        if ($request->has('all_status')) {
            $param['all_status'] = '1';
        }

        if ($request->has('with')) {
            $param['with'] = $request->with;
        }

        if ($request->has('limit')) {
            $param['limit'] = $request->limit;
        }

        if ($request->filled('order_id')) {
            $order = Order::find(hasDecode($request->get('order_id')));
            if (!empty($order) && !$order->isLockQuantity()) {
                $param['with_out_status'] = '1';
            }
        }

        $param['exclude_types'] = [];
        if (!isset($param['with_booking'])) {
            $param['exclude_types'] = [ProductType::BOOKING];
        }

        if ($request->has('without_donating_products')) {
            $param['without_donating_products'] = '1';
        }

        $data = $this->productModel->liveSearch($query, $store_id, 'knawat', null, $param);
        $arr = [];
        foreach ($data as $key => $product) {
            $arr[$key]['id'] = $product->prod_id;
            $arr[$key]['name'] = $product->name;
            $arr[$key]['price'] = $product->getProductHtmlPrice();
            $arr[$key]['image'] = $product->image;
            $arr[$key]['encode_id'] = hasEncode($product->prod_id, 4);
            $arr[$key]['has_options'] = $product->isAdvance();
            $arr[$key]['sku'] = $product->sku;
        }

        return $arr;
    }

    public function uploadPhoto($order_id, $request)
    {
        $response = $this->uploader_lib->uploadApiImage($request->product_image, 'low_resolution');
        $image = $response[0]['low_resolution']['url'];
        $this->orderModel->where('id', $order_id)->update(['receipt_image_path' => $image]);

        return $image;
    }

    public function removePhoto($order)
    {
        if (app('store')->isDemoStore() || app()->environment('local')) {
            return 'success';
        }

        \Storage::disk('s3')->delete([
            basename($order->receipt_image_path),
        ]);

        $order->update(['receipt_image_path' => '']);

        return 'success';
    }

    public function addProductToOrder($order, ValidateAddProductToOrderRequest $request): array
    {
        currency()->setCurrency($order->getCurrency(), true);

        $product_id = $request->product_id;
        $product_name = $request->product_name;
        $product_price = $request->product_price;
        $product_cost_price = $request->product_cost_price;
        $product_quantity = $request->product_quantity ?: 1;
        $p_option = $request->p_option;
        $product_fields = $request->product_fields;
        $branch_id = $request->branch_id;
        $productWeight = $request->product_weight;
        $productWeightType = $request->product_weight_type ?? WeightTypeEnum::DEFAULT;

        // update exist order
        if ($product_id) {
            /** @var Product $product */
            $product = $this->productModel->find($product_id);

            if (!$product) {
                return ['case' => 'error', 'message' => 'المنتج  لم يعد موجود'];
            }

            if (ProductType::isDonatingProduct($product->type)) {
                return ['case' => 'error', 'message' => 'المنتج نوعه "كفالة أو تبرع" ولايمكن إضافته من لوحة التحكم'];
            }

            if ($product->isAdvance() && $product->productOptions->isEmpty()) {
                return ['case' => 'error', 'message' => 'المنتج غير جاهز للبيع لعدم وجود خيارات'];
            }

            if (!$branch_id && $product->isManagedByBranches()) {
                return ['case' => 'error', 'message' => 'لطفا حدد فرع معين للمنتج ' . $product->name];
            }

            // Mapping the branch quantity format
            if ($branch_id) {
                $branches_quantity[] = ['branch_id' => $branch_id, 'quantity' => $product_quantity];

                // A special case, the shipping companies have a flag `on_demand_delivery` do not support multiple shipments for a single order.
                if ($reference = $order->getShipmentForCreateOrder()->getReference()) {
                    if ($reference->shippingCompany && $reference->shippingCompany->isOnDemandDelivery() && $reference->branch_id != $branch_id) {
                        return ['case' => 'error', 'message' => 'شركة الشحن لا تدعم تعدد الشحنات لأكثر من فرع'];
                    }
                }
            }

            // If the product is service & advance/ has skus
            // , we need to fetch only the advancable  details ids
            if ($product->isAdvance() && isset($product_fields[hasEncode($product_id)])) {
                $p_option = $this->formatAdvancableOptions($product_fields);
            }

            if ($order->isLockQuantity()) {
                $checkQuantity = $this->order_lib->checkProductQuantity($product, $product_quantity, $p_option, $branches_quantity ?? null);
                if ($checkQuantity['case'] == 'error') {
                    return $checkQuantity;
                }
            }
            /// handle product options
            $order_item = $this->addOrderItem($product, $order, $p_option, $product_fields, $product_quantity, $branch_id);
        } else {
            $order_item = $this->addCustomOrderItem($order, $product_name, $product_price, $product_cost_price, $product_quantity, $productWeight, $productWeightType);
        }

        /** the should change flag is intended to update the total event to create credit notes. */
        $this->recalculateShipping($order, null, false, fmod($order_item->product_price, 2) === 0.0);
        $this->updateCartOrder($order, 'addItem', $order_item, true);

        return ['case' => 'success', 'data' => $order_item];
    }

    /**
     * @param Order $newOrder
     * @param Order $oldOrder
     */
    public function addProductToReorder(Order $newOrder, Order $oldOrder)
    {
        $messageError = [];

        foreach ($oldOrder->items as $item) {
            /**
             * @var OrderItems $item
             */
            if ($item->itemStock->isNotEmpty()) {
                foreach ($item->itemStock as $item_stock) {
                    $messageError = array_merge($messageError,
                        $this->addOrderItemToOrder($newOrder, $oldOrder, $item, $item_stock->branch_id)
                    );
                }
            } else {
                $messageError = $this->addOrderItemToOrder($newOrder, $oldOrder, $item);
            }
        }

        $this->reCalculateTotal($newOrder);

        count($messageError) != 0 ? flash(implode("<br>", $messageError), 'danger') : '';
    }

    private function addOrderItemToOrder(Order $newOrder, Order $oldOrder, OrderItems $item, $branch_id = null)
    {
        $messageError = [];
        $optionIds = null;
        $product_fields = [];

        $product = $item->product;

        if (!$product) {
            $messageError[] = "المنتج  لم يعد موجود في الطلب";
            return $messageError;
        }

        if (in_array($product->type, [ProductType::DONATING, ProductType::BOOKING])) {
            $messageError[] = "المنتج $product->name لا يمكن تكراره";
            return $messageError;
        }

        if (in_array($product->status, [ProductStatus::DELETED, ProductStatus::HIDDEN])) {
            $messageError[] = sprintf('%s %s لم يعد موجود في الطلب', $product->type === ProductType::ORDER_OPTION ? 'الخيار' : 'المنتج', $product->name);
            return $messageError;
        }

        if ($product->type == ProductType::PRODUCT) {
            $optionIds = $item->oItemOptions->map(function ($option) {
                return $option->p_option_details_id;
            })
                ->unique()
                ->toArray();
        }

        if (in_array($product->type, [ProductType::SERVICE, ProductType::FOOD, ProductType::DIGITAL_CARD])) {
            $optionIds = $item->oItemOptions->map(function ($option) {
                return $option->p_option_details_id;
            })->reject(function ($p_option_details_id) {
                return $p_option_details_id == 0;
            })
                ->unique()
                ->toArray();

            $product_fields = $this->productFieldsFormat($item->oItemOptions);
        }

        // Mapping the branch quantity format
        if ($branch_id) {
            $branches_quantity[] = ['branch_id' => $branch_id, 'quantity' => $item->quantity];
        }

        if ($oldOrder->isLockQuantity()) {
            $checkQuantity = $this->order_lib->checkProductQuantity($product, $item->quantity, $optionIds, $branches_quantity ?? null);
            if ($checkQuantity['case'] == 'error') {
                $messageError[] = $checkQuantity['msg'];
                return $messageError;
            }
        }

        $order_item = $this->addOrderItem($product, $newOrder, $optionIds, $product_fields, $item->quantity, $branch_id, true);
        $this->updateCartOrder($newOrder, 'addItem', $order_item, true);

        return $messageError;
    }

    /**
     * @param OrderItemOptions $productOptions
     * @return array
     */
    private function productFieldsFormat($productOptions): array
    {
        $field = [];

        foreach ($productOptions as $option) {

            $type = $option->productOptions->type;
            $valueArray = [];

            if ($type == 'radio') {
                $value = $option->p_option_details_id . ':' . $option->p_option_details_price;
            } elseif ($type == 'checkbox') {
                $optionDetail = explode(",", $option->option_data);

                for ($i = 0; $i < count($optionDetail); $i++) {
                    $valueArray [] = ['option' => $optionDetail[$i] . ':' . $option->p_option_details_price];
                }
            } else {
                $value = $option->option_data;
            }

            $field [hasEncode($option->product_id)]['field'][] = [
                "product_id" => hasEncode($option->product_id),
                "id" => $option->product_option_id,
                "name" => $option->option_name,
                "value" => $type == 'checkbox' ? $valueArray : $value,
                "required" => $option->productOptions->required,
                "type" => $type
            ];
        }
        return $field;
    }

    /**
     * @param Product|\Modules\Product\Entities\Product $product
     * @param Order $order
     * @param null|array $pOption
     * @param array $product_fields
     *
     * @return OrderItems
     */
    public function addOrderItem($product, $order, $pOption = null, $product_fields = [], $product_quantity = 1, $branch_id = null, $from_reorder = false)
    {
        $options = [];
        $itemsFields = [];
        $productSku = null;
        if ($product->isAdvance() && (!empty($pOption))) {
            $sku = $this->skuOptionRepository->getVariantByOptions($pOption, $product);
            if (isset($sku)) {
                /** @var \App\Models\ProductSkus $productSku */
                $productSku = $sku->variant;
            }
        }

        $productPrice = Money::amountFromSmallest(($productSku ? $productSku->getPrice() : $product->getPrice())->getBasePrice());
        $productWeight = $productSku->weight ?? $product->weight;

        $costPrice = $productSku?->regular_price ? $productSku->regular_price : $product->cost_price;
        $costPrice = money_amount(money($costPrice,$order->getCurrency()));

        // If the product is " منتج جاهز او اكواد"
        if (! empty($pOption) && ProductType::canBeEditibaleFromOrderOptions($product->type)) {
            // needed to save the options for current products in order
            $options = $this->productOptionDetailsRepository->getProductOptionsByID($pOption);
        }

        // If the product is service or food
        // in case the item has fields -- منتج حسب الطلب -- we need to get the price of each fields.
        if (isset($product_fields[hasEncode($product->id)])) {
            $itemsFields = $this->getOrderItemFields($product_fields[hasEncode($product->id)]);
            if (!$product->active_advance) {
                $productPrice += array_sum(array_filter(array_column($itemsFields, 'p_option_details_price')));
            }
        }

        //We need HERE to get Money\Money instance without currency convert.
        $productPrice = money($productPrice);

        /**
         * -- product_price => the price of one unit without tax and with discount.
         * -- total = product_price * quantity
         * -- product_discount => the total discount of coupon and offers for this item.
         * -- tax => 5%
         * -- tax_amount => the tax amount for net product price (total - product_discount).
         */
        $exchange_currency = currency()->getExchangeRate($order->getCurrency());

        /**
         * we need to change the currency of product to order currency.
         */
        $productPrice = currency()->convert($productPrice, $order->getCurrency());

        $orderItem = (($from_reorder) ? $order->items : $order->items())->where('product_id', $product->getKey())
            ->when($productSku, function ($query, $product_sku) {
                return $query->where('product_skus_id', $product_sku->getKey());
            })
            ->first();

        /*
        * Excluding service products
        * to prevent merge order items when order added from dashboard
        */

        if ($orderItem && count($itemsFields) === 0) {
            $orderItem->quantity += $product_quantity;
            //comment this line
            $orderItem->total += Money::amountFromSmallest($productPrice);
            $orderItem->save();
        } else {
            /** @var OrderItems $orderItem */
            $orderItem = $order->items()->create([
                'order_id'               => $order->getKey(),
                'product_id'             => $product->getKey(),
                'name'                   => escape_emoji($product->name) ?: null,
                'product_price'          => Money::amountFromSmallest($productPrice),
                'quantity'               => $product_quantity,
                'notes'                  => '',
                'total'                  => Money::amountFromSmallest($productPrice) * $product_quantity,
                'product_skus_id'        => $productSku?->getKey(),
                'product_sku_code'       => $productSku?->sku ?: $product->sku,
                'currency'               => $exchange_currency->getBaseCurrency(),
                'exchange_currency'      => $exchange_currency->getCounterCurrency()->getCode(),
                'exchange_currency_rate' => $exchange_currency->getConversionRatio(),
                'cost_price'             => $costPrice,
                'weight'                 => $productWeight,
                'require_shipping'       => $product->require_shipping,
                'product_type'           => $product->type,
                'product_thumbnail'      => $product->thumbnail,
                'mpn'                    => $productSku ? $productSku->mpn : $product->mpnValue,
                'gtin'                   => $productSku ? $productSku->gtin : $product->gtinValue,
                'weight_type'            => $product->weight_type,
            ]);
        }

        $orderItem->setRelation('order', $order);
        $orderItem->setRelation('product', $product);

        // Add product that has " منتج جاهز او اكواد" type options
        if (ProductType::canBeEditibaleFromOrderOptions($product->type) && count($options) > 0) {
            //reset options
            $orderItem->oItemOptions()->safeDelete();

            foreach ($options as $key => $option) {
                $orderItemOption = $orderItem->oItemOptions()->create([
                    'product_id' => $product->getKey(),
                    'product_option_id' => $option->product_option_id,
                    'p_option_details_id' => $option->id,
                    'p_option_details_price' => Money::amountFromSmallest(
                        currency()->convert($option->getPrice($product)->getBasePrice(), $order->getCurrency())
                    ),
                    'option_name' => $option->pOption?->option_name,
                    'option_type' => $option->pOption?->type,
                ]);
                $this->saveOrderOptionsDetails($orderItemOption,$orderItem);
            }
        }

        // this for product with fields -- منتج حسب الطلب --
        if (count($itemsFields) > 0) {
            //reset options
            $orderItem->oItemOptions()->safeDelete();

            foreach ($itemsFields as $key => $option) {
                $orderItemOption = $orderItem->oItemOptions()->create([
                    'product_id' => $product->getKey(),
                    'p_option_details_id' => $option['p_option_details_id'],
                    'p_option_details_price' => Money::amountFromSmallest(
                        currency()->convert(
                            money()->getMoney($option['p_option_details_price'] ?: 0, $product->currency),
                            $order->getCurrency()
                        )
                    ),
                    'product_option_id' => $option['product_option_id'],
                    'option_data' => $option['option_data'],
                    'option_name' => $option['option_name'],
                    'option_type' => $option['option_type'],
                ]);
                $this->saveOrderOptionsDetails($orderItemOption,$orderItem);
            }

        }

        // Store sku and barcode for variant
        if (!empty($orderItem->product_skus_id) && $productSku) {
            $orderItem->sku()->create([
                'sku' => $productSku->sku,
                'barcode' => $productSku->barcode,
            ]);
        }

        // create order item for sub products
        if ($product->type == 'group_products') {
            CreateOrderSubItems::make()->setOrder($order)->setParentOrderItem($orderItem)->run();
        }

        if ($orderItem->product->isManagedByBranches() && $branch_id) {
            StoreItemStock::make()
                ->setOrderItem($orderItem)
                ->setBranchId($branch_id)
                ->setQuantity($product_quantity)
                ->run();
        }

        if (!$order->isTakenQuantity() && $order->isLockQuantity() && !OrderStatus::IsPriceQuote($order->order_status_id)) {
            $order->makeQuantityTaken();
        }

        // reduce quantity
        if ($order->isTakenQuantity()) {
            ReduceItemQuantity::make()->setItem($orderItem)
                ->setQuantity($product_quantity)
                ->setBranchId($branch_id)
                ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_REDUCE_QUANTITY_FROM_ORDER)
                ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_SKU_REDUCE_QUANTITY_FROM_ORDER)
                ->run();
        }
        //when user added digital code after finishing the order.
        //we handle the listener manually.
        (new UpdateOrderDigitalCodeListener)->handle(new OrderPlaced($order));

        $order = ($from_reorder) ? $order->load(['items']) : $order->refresh();

        $orderShipmentBranch = CreateOrderShipmentForBranch::make()
            ->setOrder($order)
            ->setOrderItem($orderItem)
            ->setBranchId($branch_id)
            ->run();

        //after addOrderItem
        $this->recalculateShipping($order, $orderShipmentBranch ? $orderShipmentBranch->branch_id : null);

        event((new OrderProductsUpdated($order, sprintf('تم إضافة منتج : %s', escape_emoji($product->name))))->setFireWebhook(true));

        return $orderItem;
    }

    /**
     * @param OrderItems $order_item
     * @param Product $product
     * @param null|array $p_option
     * @param array $product_fields
     *
     * @return OrderItems
     */
    public function updateOrderItem($order_item, $product, $p_option = null, $product_fields = [])
    {
        $options = [];
        $itemsFields = [];
        $plus_price = 0;
        /** @var ProductSkus|null $product_sku */
        $product_sku = null;
        if ($product->isAdvance() && $p_option) {
            $old_sku_id = $order_item->product_skus_id;
            $product_sku = $this->skuOptionRepository->getVariantByOptions($p_option, $product)->variant ?? null;
            // we need to restore the old sku quantity
            if ($product_sku && $product_sku->id !== $old_sku_id && $order_item->order->isTakenQuantity()) {
                RestoreItemQuantity::make()->setItem($order_item)
                    ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                    ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                    ->run();
            }
        }

        $product_price = Money::amountFromSmallest(($product_sku ? $product_sku->getPrice() : $product->getPrice())->getBasePrice());

        if (ProductType::canBeEditibaleFromOrderOptions($product->type) && null !== $p_option) {
            $options = $this->productOptionDetailsRepository->getProductOptionsByID($p_option);
        }

        // in case the item has fields -- منتج حسب الطلب -- we need to get the price of each fields.
        if (isset($product_fields[hasEncode($product->id)])) {
            $itemsFields = $this->getOrderItemFields($product_fields[hasEncode($product->id)]);
            if (!$product->isAdvance()) {
                $product_price += array_sum(array_filter(array_column($itemsFields, 'p_option_details_price')));
            }
        }

        $product_price = money($product_price, $product->currency);

        /**
         * -- product_price => the price of one unit without tax and with discount.
         * -- total = product_price * quantity
         * -- product_discount => the total discount of coupon and offers for this item.
         * -- tax => 5%
         * -- tax_amount => the tax amount for net product price (total - product_discount).
         */
        $exchange_currency = currency()->getExchangeRate($order_item->order->getCurrency());

        /**
         * we need to change the currency of product to order currency.
         */
        $product_price = currency()->convert($product_price, $order_item->order->getCurrency());

        $quantity = $order_item->quantity ?: 1;

        $itemData = [
            'product_id' => $product->getKey(),
            'product_price' => Money::amountFromSmallest($product_price),
            'quantity' => $quantity,
            'total' => $this->decimalFormat((Money::amountFromSmallest($product_price) * $quantity)),
            'product_skus_id' => $product_sku ? $product_sku->getKey() : null,
            'product_sku_code' => $product_sku ? $product_sku->sku : $product->sku,
            'currency' => $exchange_currency->getBaseCurrency(),
            'exchange_currency' => $exchange_currency->getCounterCurrency()->getCode(),
            'exchange_currency_rate' => $exchange_currency->getConversionRatio(),
        ];
        /* @var OrderItems $order_item */
        $order_item->update($itemData);
        $itemWasChanged = $order_item->wasChanged(array_keys($itemData));

        $order_item->refresh();

        //if there is sku & user changed the sku, make sure to update it.
        if ($product_sku && $order_item->sku && $order_item->sku->sku != $product_sku->sku) {
            $order_item->sku
                ->update([
                    'sku' => $product_sku->sku,
                    'barcode' => $product_sku->barcode,
                ]);
        }

        if ($options || $itemsFields) {
            $order_item->oItemOptions()->delete();
        }

        // this for products without fields
        foreach ($options as $key => $option) {
            $orderItemOption = $order_item->oItemOptions()->create([
                'product_id' => $product->getKey(),
                'product_option_id' => $option->product_option_id,
                'p_option_details_id' => $option->id,
                'p_option_details_price' => Money::amountFromSmallest(currency()->convert($option->getPrice($product)->getBasePrice(), $order_item->order->getCurrency())),
                'option_name' => $option->pOption?->option_name,
                'option_type' => $option->pOption?->type,
            ]);
            $this->saveOrderOptionsDetails($orderItemOption,$order_item);
        }

        // this for product with fields -- منتج حسب الطلب --
        foreach ($itemsFields as $key => $option) {
            $orderItemOption = $order_item->oItemOptions()->create([
                'product_id' => $product->getKey(),
                'p_option_details_id' => $option['p_option_details_id'],
                'p_option_details_price' => Money::amountFromSmallest(
                    currency()->convert(
                        money()->getMoney($option['p_option_details_price'] ?: 0, $product->currency),
                        $order_item->order->getCurrency()
                    )
                ),
                'p_option_details_price' => $option['p_option_details_price'],
                'product_option_id' => $option['product_option_id'],
                'option_data' => $option['option_data'],
                'option_name' => $option['option_name'],
                'option_type' => $option['option_type'],
            ]);
            $this->saveOrderOptionsDetails($orderItemOption,$order_item);
        }

        // reduce quantity if the product has skus and its quantity is updated
        if (isset($product_sku) && $product_sku->id !== $old_sku_id && $order_item->order->isTakenQuantity()) {
            ReduceItemQuantity::make()->setItem($order_item)
                ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_REDUCE_QUANTITY_FROM_ORDER)
                ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_SKU_REDUCE_QUANTITY_FROM_ORDER)
                ->run();
        }

        //After updateOrderItem
        $this->recalculateShipping($order_item->order->refresh());

        event(
            (new OrderProductsUpdated($order_item->order, sprintf('تم تعديل منتج : %s', $product->name)))
                ->setFireWebhook($itemWasChanged || (optional($order_item->oItemOptions())->wasChanged() ?? false))
        );

        return $order_item;
    }

    /**
     * @param Order $order
     * @param        $product_name
     * @param        $product_price
     * @param int $product_quantity
     * @param float $real_weight
     * @param string $weight_type
     *
     * @return OrderItems
     */
    public function addCustomOrderItem($order, $product_name, $product_price, $product_cost_price, $product_quantity = 1, $real_weight = 0.0, $weight_type = null, $sku = null)
    {
        /**
         * -- product_price => the price of one unit without tax and with discount.
         * -- total = product_price * quantity
         * -- product_discount => the total discount of coupon and offers for this item.
         * -- tax => 5%
         * -- tax_amount => the tax amount for net product price (total - product_discount).
         */
        $exchange_currency = currency()->getExchangeRate($order->getCurrency());

        /**
         * we need to change the currency of product to order currency.
         */
        $price = money_amount(money($product_price, $order->getCurrency()));
        $cost_price = money_amount(money($product_cost_price, $order->getCurrency()));

        /** @var Product $product */
        $product = CreateCustomProduct::make([
            'name'        => $product_name,
            'price'       => $price,
            'cost_price'  => $cost_price,
            'quantity'    => $product_quantity,
            'store_id'    => store()->getKey(),
            'status'      => ProductStatus::HIDDEN,
            'real_weight' => $real_weight,
            'weight_type' => $weight_type,
            'weight'      => $weight = WeightConverter::converterToDefault($real_weight, $weight_type),
            'currency'    => $order->currency,
            'sku'         => $sku ?? '',
            'with_tax'    => request()->input('with_tax', 1)
        ])->run();

        /** @var OrderItems $order_item */
        $order_item = $order->items()->create([
            'order_id'               => $order->getKey(),
            'product_id'             => $product->getKey(),
            'exchange_currency'      => $exchange_currency->getCounterCurrency()->getCode(),
            'exchange_currency_rate' => $exchange_currency->getConversionRatio(),
            'total'                  => $price * (intval($product_quantity) ?: 1),
            'currency'               => $exchange_currency->getBaseCurrency(),
            'product_price'          => $price,
            'cost_price'             => $cost_price,
            'quantity'               => $product_quantity,
            'weight'                 => $weight,
            'require_shipping'       => $product->require_shipping,
            'product_type'           => $product->type,
            'product_thumbnail'      => $product->thumbnail,
            'mpn'                    => $product->mpnValue,
            'gtin'                   => $product->gtinValue,
            'weight_type'            => $product->weight_type,
        ]);

        //After addCustomOrderItem
        $this->recalculateShipping($order);

        event((new OrderProductsUpdated($order_item->order, sprintf('تم إضافة منتج حسب الطلب : %s', $product_name)))->setFireWebhook(true));

        return $order_item;
    }

    /**
     * @param      $product_fields
     * @param bool $check_required
     *
     * @return array
     */
    public function getOrderItemFields($product_fields, $check_required = false)
    {
        if (!isset($product_fields['field'])) {
            return [];
        }

        $fields = $product_fields['field'];
        $insert_array = [];
        foreach ($fields as $key => $field) {
            $type = $field['type'];
            $value = $field['value'] ?? null;
            $name = $field['name'] ?? null;
            $id = $field['id'];
            $insert_array[$key]['p_option_details_id'] = 0;
            $insert_array[$key]['p_option_details_price'] = 0;
            $insert_array[$key]['product_option_id'] = $id;
            $insert_array[$key]['option_data'] = $value;
            switch ($type) {
                case 'radio':
                    $radio_arr = $this->splitRadioFieldValue($value);
                    $insert_array[$key]['p_option_details_id'] = $radio_arr[0];
                    $insert_array[$key]['p_option_details_price'] = $radio_arr[1];
                    $insert_array[$key]['product_option_id'] = $id;
                    $insert_array[$key]['option_data'] = null;
                    $insert_array[$key]['option_name'] = $name;
                    $insert_array[$key]['option_type'] = $type;
                    break;
                case 'checkbox':
                    $checkbox_arr = [0, 0];
                    if (isset($value) && $value != '') {
                        $checkbox_arr = $this->splitCheckboxFieldValue($value);
                    }
                    $insert_array[$key]['p_option_details_id'] = 0;
                    $insert_array[$key]['p_option_details_price'] = $checkbox_arr[1];
                    $insert_array[$key]['product_option_id'] = $id;
                    $insert_array[$key]['option_data'] = $checkbox_arr[0];
                    $insert_array[$key]['option_name'] = $name;
                    $insert_array[$key]['option_type'] = $type;
                    break;
                default:
                    $insert_array[$key]['p_option_details_id'] = 0;
                    $insert_array[$key]['p_option_details_price'] = 0;
                    $insert_array[$key]['product_option_id'] = $id;
                    $insert_array[$key]['option_data'] = $value;
                    $insert_array[$key]['option_name'] = $name;
                    $insert_array[$key]['option_type'] = $type;
                    break;
            }
            // if (1 == $field['required']) {
            //     if (!$value) {
            //         $required = true;
            //     }
            // }
        }

        // if ($check_required) {
        //     return [];
        // }

        return $insert_array;
    }

    public function splitRadioFieldValue($str)
    {
        if ($str) {
            return explode(':', $str);
        }

        return [0, 0];
    }

    public function splitCheckboxFieldValue($arr)
    {
        $return_arr = [];
        $option_details_ids = [];
        $price = 0;
        foreach ($arr as $val) {
            $option_arr = $this->splitRadioFieldValue($val['option']);
            $option_details_ids[] = $option_arr[0];
            $price = $price + (isset($option_arr[1]) && is_numeric($option_arr[1]) ? $option_arr[1] : 0);
        }
        $option_details_ids = implode(',', $option_details_ids);

        return [$option_details_ids, $price];
    }

    public function updateProduct($order, $request)
    {
        currency()->setCurrency($order->getCurrency(), true);
        $newQuantity = (int)($request->quantity ?: 1);

        if ($newQuantity <= 0) {
            return ['case' => 'error', 'message' => 'الكمية يجب أنت تكون أكبر من الصفر'];
        }

        if ($newQuantity > 9999) {
            return ['case' => 'error', 'message' => 'الكمية يجب أنت تكون اصغراو تساوي 9999'];
        }

        /** @var OrderItems $order_item */
        $order_item = OrderItems::query()->with(['product', 'itemStock'])->findOrFail($request->item_id);
        $productName = ($product = $order_item->getProduct()) ? $product->name : $order_item->name;
        $isSpecial = $order_item->product_id == 0;
        $oldOrderTotal = $order->total;


        if (is_null($order_item->product) && !$isSpecial) {
            return ['case' => 'error', 'message' => 'لايوجد منتج!!'];
        }

        $needed_quantity = $newQuantity - $order_item->quantity;

        // If the request has branches quantity
        // that's mean the product is managed by branches
        // so we need to fetch old quantity before updating
        // to check the quantity
        if ($request->branches_quantity && $order_item->itemStock) {
            // fetch old stock quantity
            $stock = $order_item->itemStock->keyBy('id')->toArray();

            $stockForDelete = [];

            // store only the new quantity values
            $branches_quantity = array_map(function ($item) use ($stock, &$stockForDelete) {
                if($item['quantity'] != null && (int)$item['quantity'] === 0 && isset($stock[$item['id']])) {
                    $stockForDelete []= $stock[$item['id']]['id'];
                }
                $item['quantity'] = (!empty($item['quantity']) ? $item['quantity'] : 1) -
                    (!empty($stock[$item['id']]['quantity']) ? $stock[$item['id']]['quantity'] : 0);

                return $item;
            }, $request->branches_quantity);

            if(count($stockForDelete) > 0) {
                $order_item->itemStock()->whereIn('id', $stockForDelete)->delete();
            }

            $branches_quantity = array_filter($branches_quantity, function ($item) {
                return $item['quantity'] != 0;
            });
        }

        if ($order->isLockQuantity() && !$order_item->product->isUnlimitedQuantity() && $needed_quantity > 0 && !$isSpecial) {
            $checkQuantity = $this->checkOrderItemQuantity($needed_quantity, $order, $order_item, $branches_quantity ?? null);

            if ($checkQuantity['case'] == 'error') {
                return $checkQuantity;
            }
        }

        $updatedAttributes = [
            'quantity' => $newQuantity,
            'total' => $this->decimalFormat(($order_item->product_price * $newQuantity)),
        ];

        $order_item->update($updatedAttributes);

        // If descreasing quantity is requested, refund item
        if ($needed_quantity < 0) {
            $order_item->refund(abs($needed_quantity));
        }

        $itemWasChanged = $order_item->wasChanged(array_keys($updatedAttributes));

        if ($needed_quantity && !$isSpecial && $order_item->product->type === ProductType::GROUP_PRODUCT) {
            UpdateOrderSubItems::make()->setParentOrderItem($order_item)->run();
        }

        if ($needed_quantity && !$isSpecial && !OrderStatus::IsPriceQuote($order->order_status_id) && ($order->isLockQuantity() || $order->isTakenQuantity())) {
            if ($needed_quantity > 0) {
                ReduceItemQuantity::make()->setItem($order_item)
                    ->setQuantity($needed_quantity)
                    ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_REDUCE_QUANTITY_FROM_ORDER)
                    ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_SKU_REDUCE_QUANTITY_FROM_ORDER)
                    ->setSkipReduceBranchQuantity(true)
                    ->run();
            } else { //user entered number less than existed in order items, so return it.

                //TODO:: Remove abs, because if it's -5 it will become +5, we should cover all scenarios before.
                RestoreItemQuantity::make()->setItem($order_item)->setQuantity(abs($needed_quantity))
                    ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                    ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                    ->setSkipReduceBranchQuantity(true)->run();
            }
        }

        // we need to update stock quantity per branches
        // if the store activate the stock management feature
        if ($order_item->product->isManagedByBranches() && $request->branches_quantity) {
            $branches_quantity = array_map(function ($item) {
                if ($item['quantity'] == 0) {
                    $item['quantity'] = 1;
                }

                return $item;
            }, $request->branches_quantity);
            // we need to reduce the stock per branch before
            // update the new values , to make comparing with old values
            ReduceOrRestoreOrderItemStock::make()->setItem($order_item)->setUpdatedItemStock($branches_quantity)->run();
            UpdateOrderItemsStock::make()->setOrderItem($order_item)->setBranchesStock($branches_quantity)->run();
        }

        //Update order shipment branches weight & remove who's with quantity 0
        if ($order->hasMultiShipments()) {
            RefreshOrderItemStocks::make([
                'order' => $order,
            ])->run();
        } else {
            //update total weight for branch
            RefreshOrderItemStockWeightAction::make([
                'order' => $order,
            ])->run();
        }

        //after updateProduct
        $this->recalculateShipping($order);

        $this->updateCartOrder($order, 'item', $order_item, true);

        event((new OrderProductsUpdated($order, sprintf('تم تعديل كمية المنتج : %s', $productName), $oldOrderTotal, $order_item))->setFireWebhook($itemWasChanged));

        return ['case' => 'success'];
    }

    /**
     * @param $product_quantity
     * @param \Modules\Orders\Entities\OrderItems $order_item
     * @return array
     */
    public function checkOrderItemQuantity($product_quantity, $order, $order_item, $branches_quantity = null)
    {
        $p_options = [];
        $order_item->load('oItemOptions');
        if ($order_item->oItemOptions) {
            foreach ($order_item->oItemOptions as $key => $o_option) {
                $p_options[$key] = $o_option->p_option_details_id;
            }
        }

        if ($order_item->product->type == ProductType::DIGITAL_CARD) {
            $temp = $this->order_lib->checkProductQuantityCodes($order_item->product, $product_quantity, $order);
            if ($temp['case'] == 'error') {
                return $temp;
            }
        }


        return $this->order_lib->checkProductQuantity($order_item->product, $product_quantity, $p_options, $branches_quantity);
    }

    public function removeProduct($order, $request)
    {
        /** @var Order $order */
        currency()->setCurrency($order->getCurrency(), true);

        $order_item_id = hasDecode($request->input('order_item_id'));

        /** @var OrderItems $orderItm */
        $orderItm = $this->orderItemsModel->findOrFail($order_item_id);
        $productName = ($product = $orderItm->getProduct()) ? $product->name : $orderItm->name;

        $this->hasProductAssosiatedOrderOptions($order, $orderItm->product_id, true);

        if (!OrderStatus::IsPriceQuote($order->order_status_id) && ($order->isLockQuantity() || $order->isTakenQuantity())) {
            RestoreItemQuantity::make()->setItem($orderItm)
                ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                ->run();
        }

        $orderItm->delete();

        //Update order shipment branches weight & remove who's with quantity 0
        if ($order->hasMultiShipments()) {
            RefreshOrderItemStocks::make([
                'order' => $order,
            ])->run();
        } else {
            //update total weight for branch
            RefreshOrderItemStockWeightAction::make([
                'order' => $order,
            ])->run();
        }

        //After removeProduct
        $order = $this->returnCustomerLoyaltyPoints($order, $request, $orderItm->product_id);

        $this->recalculateShipping($order->refresh());

        $this->updateCartOrder($order, 'itemDeleted', $orderItm, true);

        event((new OrderProductsUpdated($order, sprintf('تم حذف المنتج : %s', $productName)))->setFireWebhook(true));

        $data['case'] = 'success';

        return $data;
    }

    public function removeOrderOption($order, $request)
    {
        /** @var Order $order */
        currency()->setCurrency($order->getCurrency(), true);

        $order_item_id = optimus()->decode($request->input('order_item_id'));

        /** @var OrderItems $orderItm */
        $orderItm = $this->orderItemsModel->findOrFail($order_item_id);
        $productName = ($product = $orderItm->getProduct()) ? $product->name : $orderItm->name;

        $orderItm->oItemOptions()->delete();
        $orderItm->delete();
        $this->recalculateShipping($order);

        $this->updateCartOrder($order, 'itemDeleted', $orderItm, true);

        event((new OrderProductsUpdated($order, sprintf('تم حذف المنتج : %s', $productName)))->setFireWebhook(true));

        $data['case'] = 'success';

        return $data;
    }

    public function showProductOptions($order_id, $request)
    {
        $product_id = $request->input('product_id');

        $data['product'] = $this->productModel->where('id', $product_id)->with('productImages')->first();

        $data['product']->load([
            'productOptionsNotSplitter'         => function ($query) use ($data) {
                $query->when($data['product']->type == 'product',
                    fn($q) => $q->where('advance', true));
            },
            'productOptionsNotSplitter.details' => function ($query) use ($data) {
                $query->when(ProductType::IsCodes($data['product']->type), function (Builder $subQuery) {
                    $subQuery->whereHas('productSkus.digitalCodesActive',
                        fn(Builder $q) => $q->whereColumn('digital_codes.product_id', 'product_skus.product_id'))
                        ->with('productSkus.digitalCodesActive');
                });
            },
        ]);

        // Get Branches Quantities for product
        $data['branches'] = (feature()->isActive('manage-products-by-branches') && $data['product']->isManagedByBranches()) ?
            transformation(
                resolve(StoreBranchRepository::class)->getWithProductQuantities($product_id),
                BranchQuantityTransformer::class
            )->transform() : null;

        // Get Main(default) Branch Info
        if ($data['branches']) {
            $default_branch = array_filter($data['branches'], function ($branch) {
                return $branch['is_default'];
            });

            $data['default_branch'] = reset($default_branch);
        }

        return $data;
    }

    public function showOrderOptions($order, $request)
    {
        $order_item_id = hasDecode($request->input('order_item_id'));
        $data['item'] = $this->orderItemsModel->with(

            'product',
            'product.productOptions',
            'product.productOptions.details',
            'product.instagramData',
            'product.productImages',
            'oItemOptions'
        )->findOrFail($order_item_id);

        if (!$data['item']) {
            return null;
        }

        $current_order_options = [];
        foreach ($data['item']->oItemOptions as $o_item_option) {
            $current_order_options[$o_item_option->product_option_id] = ($o_item_option->p_option_details_id == 0) ? $o_item_option->option_data : $o_item_option->p_option_details_id;
        }
        $data['current_order_options'] = $current_order_options;

        return $data;
    }

    public function saveOrderOptions($order, $request)
    {
        $p_option = $request->input('p_option');
        $order_item_id = hasDecode($request->input('order_item_id'));
        $product_id = hasDecode($request->input('product_id'));
        $product_fields = $request->input('product_fields');
        /** @var OrderItems $order_item */
        $order_item = $this->orderItemsModel->findOrFail($order_item_id);
        $product = $this->productModel->find($product_id);

        // We need to fetch branches quantity
        // to check the new option quantity per selected branch
        if ($product->isManagedByBranches() && $order_item->itemStock->isNotEmpty()) {
            $branches_quantities = $order_item->itemStock->toArray();
        }

        if ($product->isAdvance() && isset($product_fields[hasEncode($product_id)])) {
            $p_option = $this->formatAdvancableOptions($product_fields);
        }

        $checkQuantity = $this->order_lib->checkProductQuantity($product, $order_item->quantity, $p_option, $branches_quantities ?? null);
        if ($checkQuantity['case'] == 'error') {
            return $checkQuantity;
        }

        $order_item = $this->updateOrderItem($order_item, $product, $p_option, $product_fields);
        $this->updateCartOrder($order_item->order, 'updateItemOption', $order_item, true);

        $data['case'] = 'success';

        return $data;
    }

    public function applyCoupon($order, Coupon $coupon)
    {
        /** @var $order Order|\Modules\Orders\Entities\Order */
        $order = $order->loadMissing(['items', 'items.product']);

        $paymentMethod = !$order->isWattingPyment() ? $order->paidVia() : null; //head($order->getAcceptedPaymentMethods())

        $couponPresenter = $this->couponService->getDiscountAmount($coupon->code, $order, $paymentMethod);

        if (!$couponPresenter->isSuccess()) {
            return $couponPresenter;
        }

        // let's remove the old coupon history
        if ($order->couponHistory) {
            $order->couponHistory->delete();
        }

        $order->coupon_code = $coupon->code;

        $this->reCalculateTotal($order->refresh(), false, true);

        $this->updateCartOrder($order, 'coupon');

        event((new OrderCouponAppliedUpdated($order, sprintf('تم إضافة كوبون : %s', $order->coupon_code)))->setFireWebhook(true));

        $order->updatePartialBy('coupon', $coupon->code);

        return $couponPresenter;
    }

    public function deleteCoupon($order)
    {
        if ($order->couponHistory) {

            $couponHistories = CouponHistory::where('order_id', $order->id);

            if ($couponHistories->count() > 1) {
                foreach ($couponHistories->get() as $couponHistory) {
                    $couponHistory->delete();
                }
            } else {
                $order->couponHistory->delete();
            }
        }

        $this->recalculateShipping($order);

        $this->updateCartOrder($order, 'coupon');

        if (isset($order->couponHistory) && $order->couponHistory->couponInfo && $order->couponHistory->couponInfo->code) {
            event((new OrderCouponAppliedUpdated($order, sprintf('تم حذف كوبون : %s', $order->couponHistory->couponInfo->code)))->setFireWebhook(true));
        }

        $order->updatePartialBy('coupon', '');

        return true;
    }

    public function updateItemPrice($order, $request)
    {
        $shipment = $order->getShipmentForCreateOrder();

        if ($order->payment_method == 'cod' && $order->codPaymentLog && $shipment->getShippingNumber()) {
            return response()->json([
                'case' => 'error',
                'message' => 'في حالة أردت تعديل بيانات المنتجات يرجى إلغاء البوليصة الحالية أولاً',
            ]);
        }

        if ($order->loyaltyPrize && $order->loyaltyPrize->loyalty_program_prize_key != 'FREE_PRODUCT') {
            return response()->json([
                'case' => 'error',
                'message' => __('order.loyalty_prize_update_alert'),
            ]);
        }

        $orderItemId = hasDecode($request->input('pk'));
        $newPrice = (float)$request->input('value', 0);

        if ($newPrice > 999_999_9) {
            return response()->json([
                'case' => 'error',
                'message' => 'الكمية يجب أنت تكون اصغراو تساوي 9,999,999',
            ]);
        }

        currency()->setCurrency($order->getCurrency(), true);
        // update order item price
        /** @var OrderItems $orderItem */
        $orderItem = $this->orderItemsModel->newQuery()->find($orderItemId);

        if (!!$order->loyaltyPrize && $order->loyaltyPrize->product_id == $orderItem->product_id) {
            return response()->json([
                'case' => 'error',
                'message' => __('order.loyalty_prize_product_update_alert'),
            ]);
        }
        if (!$orderItem) {
            return 'error';
        }
        $productName = ($product = $orderItem->getProduct()) ? $product->name : $orderItem->name;
        $newPrice = money(amount: $newPrice, currency: $orderItem->getCurrency(), taxable: $orderItem)->getBasePrice();
        $newPrice = money_amount($newPrice);
        $oldOrderTotal = $orderItem->order->total;
        $updatedAttributes = [
            'product_price' => $this->decimalFormat($newPrice),
            'total' => $this->decimalFormat(($orderItem->quantity * $newPrice)),
        ];

        $orderItem->update($updatedAttributes);
        $itemWasChanged = $orderItem->wasChanged(array_keys($updatedAttributes));

        // re calculate order amount
        //After updateItemPrice
        $this->recalculateShipping($orderItem->order);

        $this->updateCartOrder($orderItem->order, 'item', $orderItem, true);

        event(
            (new OrderProductsUpdated(
                $orderItem->order,
                sprintf('تم تعديل سعر المنتج : %s', $productName),
                $oldOrderTotal,
                $orderItem
            ))->setFireWebhook($itemWasChanged)
        );

        return 'success';
    }

    public function activateOrder($order_id)
    {
        /** @var Order $order */
        $order = $this->orderModel->newQuery()->where('id', $order_id)->with('customer', 'items')->first();

        $check_status = $this->checkOrderForActivation($order);
        if ($check_status['case'] === 'success') {
            //for multiShipments, remove unwanted branches(without order_item_stock)
            $this->cleanUnwantedBranches($order);

            // if order created by seller and waiting for the payment
            if (((int)$order->order_status_id) === OrderStatus::PAYMENT_PENDING && !$order->activeOrderCart) {
                $this->createCartFromOrder($order);
            }

            // Special case If The Payment Accepted Inclode One Cod
            // We Shill Set The Status To In Progress
            if ($order->isWattingPymentOnlyCodPayment()) {
                return $this->finishOrder($order, OrderStatus::IN_PROGRESS);
            }
            //- customer will add payment method
            if ($order->isWattingPyment()) {
                return $this->finishOrder($order, OrderStatus::PAYMENT_PENDING);
            }

            return $this->finishOrder($order, OrderStatus::UNDER_REVIEW);
        }

        return $check_status;
    }

    public function draftOrder($order_id)
    {
        $this->orderModel->where('id', $order_id)->update(['draft' => 1]);

        return ['case' => 'success', 'message' => 'تم حفظ الطلب كمسودة'];
    }

    public function deleteOrder($order_id)
    {
        $order = \Modules\Orders\Entities\Order::query()->findOrFail($order_id);

        /** @var \Salla\Core\Presenters\MessagePresenter $status */
        $status = DeleteOrder::make()->setOrder($order)->run();

        if (!$status->isSuccess()) {
            return [
                'case' => 'danger',
                'msg' => $status->getMessage(),
            ];
        }

        return ['case' => 'success', 'message' => 'تم حذف الطلب بنجاح'];
    }

    public function uploadOptionImage($request)
    {
        $image = $request->file('image_file');

        $validationRules = ($request->get('file_upload', false) && !ProductOptionDetailName::isImageType($image->getClientOriginalExtension())) ? 'file|mimes:doc,docx,xls,xlsx,csv,pdf,txt' : 'mimes:jpg,png,jpeg,gif';

        $validator = Validator::make($request->all(), [
            'image_file' => $validationRules
        ]);

        if ($validator->fails()) {
            return ['case' => 'error', 'message' => 'الملف الذي تم رفعه ليس صورة'];
        }

        $s3 = \Storage::disk('s3');
        $filePath = generateRandomString() . '.' . $image->getClientOriginalExtension();
        $s3->put($filePath, file_get_contents($image));
        $image_url = config('filesystems.disks.s3.url') . $filePath;

        $file_path = \Storage::disk('s3')->url($filePath);

        return ['case' => 'success', 'filePath' => $request->get('file_upload', false) ? $file_path : $image_url];
    }


    /**
     * @param Order $order
     * @param $status_id
     * @return array
     *
     */
    public function finishOrder($order, $status_id)
    {
        /** @var $createdAt \Carbon\Carbon */
        $createdAt = \Carbon\Carbon::now();
        $source = request()->header(RequestHeaders::SOURCE);
        $order->update([
            'draft' => 0,
            'created_at' => $createdAt->toDateTimeString(),
            'updated_at' => $createdAt->toDateTimeString(),
            'source' => ! RequestHeaders::isNewDashboard() && ! blank($source) ? $source : 'dashboard',
            'wait_payment_start_at' => $createdAt->addHours(store()->getSetting('dashboard::checkout-url-age', 72)),
            'shippable' => $order->isItemsRequireShipping(),
            'pickable'  => $order->hasPickupShipment(),
            'bullet_delivery' => $order->isBulletDelivery()
        ]);

        $updateOrderStatus = (new UpdateOrderStatus($order, $status_id))
                                ->setCustomStatusId(store()->getEquivalentCustomStatus($status_id))
                                ->setProvider(OrderEnumProvider::CreateOrderServiceFinishOrder)
                                ->setFireWebhook(false)
                                ->setForceNotfaction(((int)$status_id) === OrderStatus::PAYMENT_PENDING) 
                                // In Case Payment Pending Let's Force Notification To Send Payment Link
                                ->setUserId(auth()->id());

        $cantUpdateStatus = $updateOrderStatus->cantUpdateStatus();

        dispatch_sync($updateOrderStatus);

        if ($order->payment_method === PaymentMethods::BANK) {
            UpdatePaymentLog::make()->setOrder($order)->run();
        }

        $orderPlacedEvent = new OrderPlaced($order);

        event($orderPlacedEvent);

        if ($cantUpdateStatus) {
            OrderInvoiceListener::run($orderPlacedEvent);
        }

        return ['case' => 'success', 'message' => 'تم إنشاء الطلب'];
    }

    public function checkOrderForActivation($order)
    {
        if (! auth()->user()->checkPermission('order_create', ['orders.create.draft' => ['draft']])) {
            return ['case' => 'error', 'message' => 'لديك إمكانية إنشاء مسودة فقط'];
        }

        $check_customer = $order->customer;
        if (is_null($check_customer)) {
            return ['case' => 'error', 'message' => 'الرجاء اختيار عميل'];
        }

        $check_product = $order->items;
        if (count($check_product) == 0) {
            return ['case' => 'error', 'message' => 'الرجاء إضافة منتجات'];
        }

        $orderShipmentBranch = $order->firstOrderShipmentBranch();
        if (
            $orderShipmentBranch && empty($orderShipmentBranch->store_branch_id) &&
            (optional($orderShipmentBranch)->shipping_details_id == 0 && $order->enable_customer_shipping == 0) &&
            $order->isRequireShipping()
        ) {
            return ['case' => 'error', 'message' => 'الرجاء اختيار طريقة الشحن او الفرع'];
        }

        if (is_null($order->payment_method)) {
            return ['case' => 'error', 'message' => 'الرجاء اختيار وسيلة الدفع'];
        }

        if ($order->isAcceptCashOnDelivery()) {
            $shipmentResolver = $order->getShipmentForCreateOrder();
            /** @var CashOnDeliveryPresenter $checkCod */
            $checkCod = CashOnDelivery::forOrder($order, $shipmentResolver->getReference())
                ->getCashOnDelivery();

            if (!$checkCod->isAvailable()) {
                return ['case' => 'error', 'message' => $checkCod->getMessage()];
            }
        }

        $temp = $this->order_lib->checkOrderProductQuantityCodes($order);
        if ($temp['case'] == 'error') {
            return $temp;
        }

        return ['case' => 'success', 'message' => ''];
    }

    public function getLastTempOrder()
    {
        $lastTempOrder = DB::table('orders')
            ->where([
                'draft' => -1,
                'user_id' => auth()->id(),
                'order_status_id' => OrderStatus::UNKNOWN,
                'reorder_from_id' => null
            ])
            ->latest('id')
            ->first()
            ?->id;

        if ($lastTempOrder) {
            return $lastTempOrder;
        }

        return $this->create();
    }

    /**
     * @param Order $oldOrder
     * @return Order
     */
    public function getLastReorder(Order $oldOrder): Order
    {
        DB::beginTransaction();
        try {
            $newOrder = $oldOrder->lastReorder->count() > 0 ? $oldOrder->lastReorder[0] : null;
            $newOrder = $this->reorder($oldOrder, $newOrder);

            DB::commit();
            return $newOrder;

        } catch (\Exception $exception) {
            DB::rollBack();
            app('sentry')->captureException($exception);
            throw $exception;
        }
    }

    public function getCustomerAddress($order)
    {
        if ($order->customer_id == 0) {
            return;
        }
        $customer = $this->customerModel->find($order->customer_id);

        if (!$customer->canShowGlobalCustomerInfo()) {
            return;
        }

        $location = $customer->location;
        $city_id = -1;
        if ($customer->city) {
            $city_id = $this->cityModel->ByName($customer->city)->value('id') ?? $city_id;
        }
        $country_code = null;
        if ($customer->country) {
            $country_code = $this->countryModel->ByName($customer->country)->value('country_code');
        }
        $arr['location'] = $location;
        $arr['city_id'] = $city_id;
        $arr['country_code'] = $country_code;
        $arr['city_name'] = $customer->city;
        $arr['shipping_street_number'] = $customer->shipping_street_number;
        $arr['shipping_block'] = $customer->shipping_block;
        $arr['postal_code'] = $customer->postal_code;
        $arr['district_id'] = $customer->district_id;
        $arr['district_name'] = $customer->district_id && $customer->district ? $customer->district->name_ar : null;

        return $arr;
    }

    public function resetUndraftOrder($order, $returnOrderObj = false, $data = [])
    {
        if (!($order = is_int($order) ? $this->orderModel->where('id', $order)->first() : $order) || $order->draft != -1) {
            return;
        }

        if (!empty($data) || !$order->reorder_from_id) {
            $order->update($data);

            $items = $this->orderItemsModel->where('order_id', $order->id)->get();
            foreach ($items as $item) {
                $this->orderItemOptionsModel->where('order_item_id', $item->id)->safeDelete();

                if ($order->isTakenQuantity()) {
                    RestoreItemQuantity::make()->setItem($item)
                        ->set('source_for_product', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                        ->set('source_for_product_sku', ProductQuantityLogSource::DASHBOARD_PRODUCT_RESTORE_QUANTITY_FROM_ORDER)
                        ->run();
                }

                $this->orderItemsModel->where('id', $item->id)->delete();
            }

            if ($order->couponHistory) {
                $order->couponHistory->delete();
            }

            if ($order->conditionalOffersHistory->isNotEmpty()) {
                $order->conditionalOffersHistory->each->delete();
            }

            if ($order->specialOffersHistory->isNotEmpty()) {
                $order->specialOffersHistory->each->delete();
            }

            $this->couponHistoryModel->where('order_id', $order->id)->safeDelete();

            $order->itemStocksByOrder()->safeDelete();

            $order->orderShipmentBranches->each(fn(OrderShipmentBranch $orderShipmentBranch) => $orderShipmentBranch->forceDelete());
            $order->paymentTransactions()->safeDelete();
        }

        if ($returnOrderObj) {
            return $order->load(['items']);
        }
    }

    private function getCodCost()
    {
        $acceptedPaymentMethodsAsArray = request('accepted_payment_methods');
        $option = request('payment_option');
        $payment = request('payment_method');
        $codCost = request('cod_cost', 0);

        if ($option == 2 && in_array('cod', $acceptedPaymentMethodsAsArray)) {
            return $codCost;
        }

        if ($option == 1 && $payment == 'cod') {
            return $codCost;
        }

        return 0;
    }

    protected function createCartFromOrder($order)
    {
        if (count($order->items) !== 0 && !$order->isRequireShipping()) {
            $order->update([
                'shipping_details_id' => -2,
                'shipping_cost' => null,
                'shipping_cost_discount' => 0,
                'cod_cost' => 0,
                'store_branch_id' => null,
                'shipping_company_id' => null,
                'shipping_company_app_id' => null,
                'shipping_private_details_id' => 0,
            ]);

            $this->reCalculateTotal($order);
        }

        CreateCartFromOrder::make()->setOrder($order)->run();
    }

    protected function formatAdvancableOptions($product_fields)
    {
        $details = [];
        foreach (reset($product_fields)['field'] as $option) {
            if ($option['type'] === 'radio') {
                $details[] = $this->splitRadioFieldValue($option['value'])[0];
            }
        }

        return $details;
    }

    public function removeLoyaltyPrize(Order $order, $request): Order
    {
        $this->returnCustomerLoyaltyPoints($order, $request, null, true);
        $this->recalculateShipping($order);
        return $order;
    }

    /**
     * @param $model
     * @param $attributes
     * @return bool
     */
    protected function modelWasChanged($model, $attributes)
    {
        foreach (array_wrap($attributes) as $attribute) {
            if ($model->wasChanged($attribute)) {
                return true;
            }
        }

        return false;
    }

    public function updateItemWeight($item, $request)
    {
        currency()->setCurrency($item->order->getCurrency(), true);

        $productName = ($product = $item->getProduct()) ? $product->name : $item->name;
        $weight = $request->input('value', null);
        if ($weight) {
            $weight = WeightConverter::converterToDefault($weight, $item->product->weight_type);
        }

        $updatedAttributes = [
            'weight' => $weight,
        ];

        $item->update($updatedAttributes);
        $itemWasChanged = $item->wasChanged(array_keys($updatedAttributes));

        RefreshOrderItemStockWeightAction::make([
            'order' => $item->order,
            'order_item' => $item,
        ])->run();

        // re calculate order amount
        //After updateItemWeight
        $this->recalculateShipping($item->order);

        $this->updateCartOrder($item->order, 'item', $item, true);

        event((new OrderProductsUpdated($item->order, sprintf('تم تعديل وزن المنتج : %s', $productName)))->setFireWebhook($itemWasChanged));

        return 'success';
    }

    /**
     * @param $order
     */
    protected function cleanUnwantedBranches($order)
    {
        if (!$order->hasMultiShipments()) {
            return;
        }

        //Update order shipment branches weight & remove who's with quantity 0
        RefreshOrderItemStocks::make([
            'order' => $order,
        ])->run();

        //After remove unwanted branches
        $this->recalculateShipping($order);

        $this->updateCartOrder($order, null, null, true);
    }

    public function formDashController(): self
    {
        $this->dashController = true;

        return $this;
    }

    public function returnCustomerLoyaltyPoints($order, $request, $product_id = null, $force = false)
    {
        $oldOrder = $order;
        if (!$force && (!$order->loyaltyPrize || !($product_id == $order->loyaltyPrize->product_id))) return $order;
        if ($request->return_loyalty_point && $request->return_loyalty_point == "true") {
            RefundOrderLoyaltyPoints::setOrder($order)->set('force_refund_loyalty_prize_points', true)->run();
        }
        if ($order->loyaltyPrize) {
            $order->loyaltyPrize->delete();
        }
        $order->setRelation('loyaltyPrize', null);
        $this->reCalculateTotal($order);
        $this->updateCartOrder($order, 'item');
        if ($oldOrder->loyaltyPrize && $oldOrder->loyaltyPrize->loyalty_program_prize_key == 'FREE_SHIPPING') {
            $order->cart->update([
                'shipping_cost_discount' => $order->shipping_cost_discount,
                'shipping_cost' => $order->shipping_cost,
                'shipping_details_id' => $order->shipping_details_id ?? 0,
                'store_branch_id' => $order->store_branch_id,
            ]);
            $order->cart->discounts()
                ->where('cart_items_discount.type', DiscountType::LOYALTY_PRIZE)
                ->delete();
        }
        return $order;
    }

    private function saveOrderOptionsDetails($option, $orderItem)
    {
        $productOptionsDetails = collect([]);
        // because the checkbox store as array in option_data column
        // we need to get all product options using the ids
        if ($option->productOptions->type === ProductOptionType::CHECKBOX) {
            $productOptionsDetails = ProductOptionDetails::whereIn('id', explode(',', $option->option_data))->get();
        } elseif ($option->productOptions->type === ProductOptionType::RADIO) {
            // for radio type we have only one valued selected
            // so we will get it from has one relation
            $productOptionsDetails = collect([$option->productOptionDetails])->filter();
        }

        $productOptionsDetails = $productOptionsDetails->map(function ($details, $key) use ($orderItem) {
            return [
                'detail_name' => $details->option_details_name,
                'detail_price' => Money::amountFromSmallest($details->getPrice()),
                'detail_quantity' => $details->option_details_quantity,
                'detail_cost_price' => $details->option_details_cost_price,
                'status' => $details->status,
                'currency' => $orderItem->currency,
                'exchange_currency' => $orderItem->exchange_currency,
                'exchange_currency_rate' => $orderItem->exchange_currency_rate,
            ];
        });


        if (!count($productOptionsDetails) > 0) {
            return;
        }

        $option->orderItemOptionDetails()->createMany($productOptionsDetails->toArray());

    }
}
