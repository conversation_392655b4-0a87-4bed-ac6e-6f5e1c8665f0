<?php

namespace App\Services\Lean;

use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Cache;
use Psr\Http\Message\ResponseInterface;
use Salla\ApiResponse\ApiResponse;
use Salla\ApiResponse\Contracts\ApiClient;
use <PERSON>la\ApiResponse\Traits\HasLogger;
use Salla\Core\Entities\StoreDocument;
use Salla\Core\Enum\StoreDocumentsStatus;
use Salla\Logger\Facades\Logger;

class LeanVerificationService extends ApiClient
{
    use HasLogger;

    protected string $baseUrl;
    protected string $authBaseUrl;
    protected string $clientId;
    protected string $clientSecret;
    protected string $tokenCacheKey = 'lean_api_token';
    protected int $tokenTtl = 3599;

    public function __construct()
    {
        $this->initializeConfig();

        $this->client = new Client(array_merge([
            'headers' => ['Accept' => 'application/json'],
            'handler' => $this->getLoggerHandler(),
        ], parent::defaultGuzzleConfigOptions(true)));
    }

    public function verifyIban(string $iban, string $accountType = null): ApiResponse|string
    {
        try {
            $document = StoreDocument::withoutGlobalScope('approved')
                ->where('status', StoreDocumentsStatus::PENDING)
                ->first();

            if (!$document) {
                throw new \Exception('No pending document found for IBAN verification.');
            }

            $payload = $this->buildVerificationPayload($document, $iban, $accountType);

            return $this->sendRequest('/verifications/v2/iban', $payload, $this->buildHeaders($this->getAccessToken()));
        } catch (Exception $e) {
            Logger::message('debug', 'Lean verification failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    protected function getAccessToken(): string
    {
        return Cache::remember($this->tokenCacheKey, $this->tokenTtl, function () {
            $response = $this->client->post("{$this->authBaseUrl}/oauth2/token", [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'client_credentials',
                    'scope' => 'api',
                ],
            ]);

            $this->checkError($response);

            return json_decode($response->getBody(), true)['access_token'] ?? '';
        });
    }

    public function checkError(ResponseInterface $response): void
    {
        $body = (string) $response->getBody();
        $json = json_decode($body);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Json response not valid');
        }

        if (empty(get_object_vars($json))) {
            throw new \Exception('Request was unsuccessful.');
        }
    }

    protected function sendRequest(string $endpoint, array $data = [], array $headers = []): ApiResponse
    {
        $url = $this->baseUrl . $endpoint;

        try {
            return $this->post($url, $data, $headers);
        } catch (Exception $e) {
            Logger::message('error', 'Lean integration request failed', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    protected function buildVerificationPayload(StoreDocument $document, string $iban, ?string $accountType): array
    {
        $entity = $document->entity ?? $document->store->entity;

        $identifications = match ($entity) {
            'person' => $this->resolvePersonIdentifications($document, $accountType),
            'company', 'firm' => $this->resolveBusinessIdentifications($document),
            default => throw new \Exception("Unsupported entity type: {$entity}")
        };

        return [
            'iban' => $iban,
            'type' => $this->resolveEntityType($entity, $accountType),
            'identifications' => $identifications,
        ];
    }

    protected function resolveEntityType(string $entity, ?string $accountType = null): string
    {
        return match ($entity) {
            'person' => $accountType === 'commercial' ? 'FREELANCER' : 'PERSONAL',
            'company', 'firm' => 'BUSINESS',
            default => throw new \Exception("Unsupported entity type: {$entity}")
        };
    }

    protected function resolvePersonIdentifications(StoreDocument $document, ?string $accountType): array
    {
        $base = [
            [
                'type' => 'NATIONAL_ID',
                'value' => $document->owner_identity_number,
            ],
        ];

        if ($accountType === 'commercial') {
            $base[] = [
                'type' => 'FREELANCER_NUMBER',
                'value' => $document->freelance_number,
            ];
        }

        return $base;
    }

    protected function resolveBusinessIdentifications(StoreDocument $document): array
    {

        // sometimes lean accept the commercial register unified number as the commercial number, and vise versa!
        $unifiedNumber = $document->commercial_register_unified_number ?: $document->commercial_number;
        $commercialNumber = $document->commercial_number ?: $unifiedNumber;

        return array_filter([
            [
                'type' => 'COMMERCIAL_REGISTRATION',
                'value' => $commercialNumber,
            ],
            [
                'type' => 'UNIFIED_NUMBER',
                'value' => $unifiedNumber,
            ],
        ]);
    }

    protected function buildHeaders(string $token): array
    {
        return [
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/json',
            'Accept' => '*/*',
        ];
    }

    protected function initializeConfig(): void
    {
        $env = app()->environment('production') ? 'production' : 'sandbox';

        $config = config('services.lean');

        $this->authBaseUrl  = $config[$env === 'production' ? 'auth_base_url' : 'auth_sandbox_base_url'];
        $this->baseUrl      = $config['base_url'][$env] ?? '';
        $this->clientId     = $config[$env === 'production' ? 'client_id' : 'sandbox_client_id'];
        $this->clientSecret = $config[$env === 'production' ? 'client_secret' : 'sandbox_client_secret'];
    }
}
