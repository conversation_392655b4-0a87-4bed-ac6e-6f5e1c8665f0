<?php

namespace App\Services;

use App\Models\ShippingCompany;
use Illuminate\Support\Facades\DB;
use Salla\Core\Scopes\CurrentStoreScope;
use Modules\Orders\Enum\ShippingCompanyId;
use Salla\FeatureRules\Facades\FeaturesManager;
use Salla\Shipping\Entities\CompanyShippingApi;
use Salla\Shipping\Enums\CompanyServiceType;
use Salla\Shipping\Enums\ShippingCompanySection;
use Salla\Shipping\Services\DelegateAccessEligibility;

class ShippingService
{
    private $companyModel;

    private $uniquelyCompanyName = [
        'quick' => 'quick-shipping-company',
        'dhl' => 'dhl-shipping-company',
        'mycn' => 'my-cn',
        'esnad_express' => 'esnad-express',
        'safe_arrival' => 'safe-arrival',
        'saudi_post' => 'saudi-post',
        'storage_station' => 'storage-station',
    ];

    public function __construct(ShippingCompany $companyModel) 
    {
        $this->companyModel = $companyModel;
    }

    public function index()
    {
        $data['companyShippingList']        = $this->companyModel->findAll();
        $data['active_shipping_compnies']   = empty($this->get_active_shipping_methods()) ? true : false;
        $data['allowed_shipping']           = (\Auth::user()->store->allowed_shipping) ? explode(',', \Auth::user()->store->allowed_shipping) : [];
        $data['available_companies']        = $this->availableCompanies();

        return $data;
    }

    public function get_active_shipping_methods()
    {
        $shipping_companies_api = DB::table(DB::raw('shipping_companies, shipping_companies_api'))
            ->select(DB::raw('shipping_companies.id, shipping_companies.name'))
            ->where('shipping_companies.id', '=', DB::raw('shipping_companies_api.company_id'))
            ->where('shipping_companies_api.store_id', store()->getId());

        return $this->companyModel->select('shipping_companies.id', 'shipping_companies.name')
            ->where('shipping_companies.store_id', store()->getId())
            ->unionAll($shipping_companies_api)
            ->pluck('shipping_companies.name', 'shipping_companies.id')
            ->toArray();
    }

    public function availableCompanies()
    {
        $companies = $this->getAvailableShippingCompaniesSettings();
        $companiesIds = ShippingCompanyId::$labels;

        collect($companiesIds)
            ->filter(function ($shippingName) {
                // let's get released shipping company only ..
                return FeaturesManager::isReleased($this->uniquelyCompanyName[$shippingName] ?? $shippingName);
            })->each(static function ($shipping) use (&$companies, $companiesIds) {
                // let's assign the companies .. add
                $companies[] = array_flip($companiesIds)[$shipping];
            });

        if (FeaturesManager::isHaveFeature('road')) {
            $companies[] = ShippingCompanyId::ROAD;
        }

        return $companies;
    }

    public function getAvailableShippingCompaniesSettings(): array
    {
        return app('store')->getSetting('features::available-shipping-companies', [-3, -5, -6, -8, -7, -9]);
    }

    public function get_acitve_shipping_companies()
    {
        $customShippingCompanies = ShippingCompany::query()
            ->where('store_id', '=', store()->getId())
            ->where('status', '=', 1)
            ->get()
            ->map(function ($item, $key) {
                return [
                    'id'              => $item['id'],
                    'name'            => $item['name'],
                    'company_id'      => 0,
                    'logo'            => $item['logo'] ?? null,
                    'activation_type' => 'manual',
                    'slug'            => $item['slug'],
                    'app_id'          => !empty($item['portal_app_id']) ? $item['portal_app_id'] : null,
                ];
            })
            ->all();

        $response = app(DelegateAccessEligibility::class)->checkDelegatesDenyList();

        if (! $response->isSuccess()) {
            $customShippingCompanies = [];
        }

        $privateShippingCompanies = CompanyShippingApi::query()
            ->where('store_id', '=', store()->getId())
            ->where('status', '=', 1)
            ->with('company')
            ->whereHas('company',fn ($query) => $query->withoutGlobalScope(CurrentStoreScope::class))
            ->get()
            ->filter(fn($item) => $item->company)
            ->map(function ($item, $key) {
                return [
                    'id'              => $item['company']['id'],
                    'name'            => $item['company']['name'],
                    'company_id'      => $item['company']['id'],
                    'logo'            => $item['company']['logo'],
                    'activation_type' => 'api',
                    'slug'            => $item['company']['slug'],
                    'app_id'          => !empty($item['company']['portal_app_id']) ? $item['company']['portal_app_id'] : null,
                ];
            })
            ->unique('company_id')
            ->values()
            ->all();


        return array_filter(
            array_merge(
                $customShippingCompanies,
                $privateShippingCompanies
            )
        );
    }

    public function getActiveSallaCompanies()
    {
        return ShippingCompany::select(
                \DB::raw('distinct shipping_companies.id'),
                'shipping_companies.name',
                'shipping_companies.logo'
            )
            ->join(
                'shipping_companies_api', 
                'shipping_companies.id', '=', 'shipping_companies_api.company_id'
            )
            ->where('shipping_companies_api.store_id', store()->getId())
            ->where('shipping_companies.support_salla_policies', 1)
            ->get();
    }
}
