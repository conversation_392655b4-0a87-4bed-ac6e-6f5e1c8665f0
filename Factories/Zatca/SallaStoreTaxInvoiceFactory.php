<?php

namespace Modules\Payment\Factories\Zatca;

use Modules\Payment\Entities\SallaStoreTaxInvoice;

class SallaStoreTaxInvoiceFactory
{
    private const DEFAULTS = [
        'reference_invoice_id'   => null,
        'type'                   => 'Tax Invoice',
        'currency'               => 'SAR',
        'discount'               => 0,
        'shipping_cost'          => 0,
        'shipping_cost_discount' => 0,
        'cod_cost'               => 0,
    ];

    /**
     * Create invoice data array from a SallaStoreTaxInvoice object.
     *
     * @param SallaStoreTaxInvoice $taxInvoice The tax invoice entity.
     *
     * @return array The invoice data array.
     */
    public static function createInvoiceData(SallaStoreTaxInvoice $taxInvoice): array
    {

        return array_merge(self::DEFAULTS, [
            'store_id'       => $taxInvoice->store_id,
            'invoice_id'     => $taxInvoice->id,
            'invoice_number' => $taxInvoice->id,
            'order_id'       => $taxInvoice->id,
            'uuid'           => $taxInvoice->uuid,
            'customer'       => !empty($taxInvoice->store_meta_data) &&
                                (($taxInvoice->year <= 2025 && $taxInvoice->month >= 8) || $taxInvoice->year > 2025)
                                ? $taxInvoice->store_meta_data
                                : CustomerDataFactory::create(store: $taxInvoice->store),
            'currency'       => $taxInvoice->currency ?? self::DEFAULTS['currency'],
            'sub_total'      => $taxInvoice->sub_total,
            'discount'       => $taxInvoice->discount ?? self::DEFAULTS['discount'],
            'tax'            => $taxInvoice->tax,
            'tax_amount'     => $taxInvoice->tax_value,
            'total'          => $taxInvoice->total,
            'items'          => self::createInvoiceItems($taxInvoice),
        ]);
    }

    public static function createInvoiceItems(SallaStoreTaxInvoice $taxInvoice): array
    {
        return InvoiceItemsFactory::createItems(items: $taxInvoice->items);

    }
}

