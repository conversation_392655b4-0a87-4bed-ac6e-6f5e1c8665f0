<?php

namespace Modules\Payment\Factories\Zatca;

use Modules\Payment\Entities\SallaStoreCreditNote;

class SallaStoreCreditNoteInvoiceFactory
{
    private const DEFAULTS = [
        'reference_invoice_id'   => null,
        'type'                   => 'Credit Note',
        'item_type'              => 'product', // must be 'product' or 'service'
        'currency'               => 'SAR',
        'discount'               => 0,
        'shipping_cost'          => 0,
        'shipping_cost_discount' => 0,
        'cod_cost'               => 0,
    ];

    public static function createInvoiceData(SallaStoreCreditNote $creditNote): array
    {
        return array_merge(self::DEFAULTS,[
            'store_id'               => $creditNote->store_id,
            'invoice_id'             => $creditNote->id,
            'reference_invoice_id'   => $creditNote->invoices?->pluck('id')->implode(',') ?: self::DEFAULTS['reference_invoice_id'],
            'invoice_number'         => $creditNote->id,
            'order_id'               => $creditNote->id,
            'uuid'                   => $creditNote->uuid,
            'type'                   => self::DEFAULTS['type'],
            'customer'               => !empty($creditNote->store_meta_data) &&
                                            (($creditNote->year <= 2025 && $creditNote->month >= 8) || $creditNote->year > 2025)
                                            ? $creditNote->store_meta_data
                                            : CustomerDataFactory::create(store: $creditNote->store),
            'currency'               => $creditNote->currency ?? self::DEFAULTS['currency'],
            'sub_total'              => $creditNote->sub_total,
            'discount'               => $creditNote->discount,
            'tax'                    => $creditNote->tax,
            'tax_amount'             => $creditNote->tax_value,
            'total'                  => $creditNote->total,
            'items'                  => self::createInvoiceItems($creditNote),
        ]);

    }

    public static function createInvoiceItems(SallaStoreCreditNote $creditNote): array
    {
        return InvoiceItemsFactory::createItems(items: $creditNote->items);

    }
}
