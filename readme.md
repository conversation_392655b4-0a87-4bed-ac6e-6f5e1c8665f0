# Salla Project Configuration


- Add your store info in `StoresSeed` and your user info in `UserSeed`
- Run these commands
  `composer update` 

`php artisan migrate`
`php artisan db:seed`
`php artisan queue:work`


# Elixir Installation & Setup 

https://laravel.com/docs/5.3/elixir#installation

`gulp` run it at folder root.
  

# Attention
 
- Check `.env.example` with your `.env` and make sure they are the same settings .

- Before push any code ! 

```
composer php-cs:issues # Used for checking for any configured code style issues.

composer php-cs:fix # Used for fix any configured code style issues automatically.
```


- After pull any request code !
```
composer salla:update # Used for run composer update and db migration and clear all cache.
``` 


