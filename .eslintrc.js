/* eslint-disable no-undef */
module.exports = {
  env: {
    'jquery': true
  },
  globals: {
    'axios': 'readonly',
    'swal': 'readonly',
    'store': 'readonly',
    'getFreshToken': 'readonly',
    'laravel': 'readonly',
    'grecaptcha': 'readonly',
    'require': true,
  },
  extends: [
    'eslint:recommended',
    // "plugin:vue/vue3-recommended" // Use this if you are using Vue.js 3.x
    'plugin:vue/recommended',

    'prettier', // Make sure "prettier" is the last element in this list.
  ],
  rules: {
    'vue/no-unused-vars': ['error'],
    'no-console': ['error', { allow: ['warn', 'error'] }],
    'no-undef': 'warn',
  },
}
