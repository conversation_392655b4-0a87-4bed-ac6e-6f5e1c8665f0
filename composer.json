{"autoload": {"files": ["app/Functions/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/", "Salla\\": "vendor/salla/"}}, "autoload-dev": {"psr-4": {"Salla\\LaravelOtp\\Tests\\": "vendor/salla/laravel-otp/tests/", "Tests\\": "tests/"}}, "config": {"allow-plugins": {"ocramius/package-versions": false, "pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}, "preferred-install": "dist"}, "description": "The Laravel Framework", "extra": {"laravel": {"dont-discover": ["swayok/alternative-laravel-cache", "monospice/laravel-redis-sentinel-drivers", "nwidart/laravel-modules"]}}, "keywords": ["framework", "laravel", ""], "license": "MIT", "minimum-stability": "dev", "name": "laravel/laravel", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://core-repo-package.salla.group"}], "require": {"alkhwlani/xss-middleware": "^4.0", "anam/phantomjs-linux-x86-binary": "^2.1", "async-aws/illuminate-cache": "^1.1", "barryvdh/laravel-snappy": "^1.0.0", "bepsvpt/secure-headers": "^7.4", "berkayk/onesignal-laravel": "^2.0", "beyondcode/laravel-mailbox": "^3.1", "giggsey/libphonenumber-for-php": "^8.9", "globalcitizen/php-iban": "^2.6", "imdhemy/laravel-purchases": "^1.9.1", "kodeine/laravel-meta": "^2.2.1", "laravel-validation-rules/credit-card": "^1.5.0", "laravel/framework": "^10.0", "laravel/horizon": "^5.0", "laravel/legacy-factories": "^1.4", "laravel/ui": "^4.4", "lcobucci/jwt": "^4.0", "madorin/matex": "^1.0", "mailjet/laravel-mailjet": "^3.0.4", "mavinoo/laravel-batch": "2.3.6", "milon/barcode": "^10.0", "morrislaptop/laravel-queue-clear": "~1.0", "ory/hydra-client": "^2.0", "php": "^8.3", "phpoffice/phpspreadsheet": "1.18.0", "predis/predis": "~1.0", "rap2hpoutre/fast-excel": "^5.3", "roave/better-reflection": "6.18 as 5.0", "salla/accounting-services": "~1.0", "salla/advertisements-module": "~1.0", "salla/aha-ideas": "~1.0", "salla/announcement-module": "~1.0", "salla/audit-log": "~1.0", "salla/aws-domain-verify": "~1.0", "salla/blog-module": "~1.0", "salla/breadcrumbs": "~1.0", "salla/careem-shipping": "~1.0.1", "salla/changelog-module": "~1.0", "salla/checkout": "~1.0", "salla/communication": "~1.0", "salla/complaint": "~1.0", "salla/component": "~1.0", "salla/core": "~1.4", "salla/customer": "~1.1", "salla/daftra-service": "~1.0.0", "salla/dashboard-api": "~1.0", "salla/dashboard-settings-module": "~1.0", "salla/dashboard-tracking": "~2.0", "salla/domain-module": "~1.0", "salla/email-templates": "~1.0.7", "salla/encryption": "~0.0.32", "salla/error": "~1.0", "salla/events": "~1.0", "salla/exchange-authority-service": "~1.0", "salla/expert-module": "~1.0", "salla/external-services-module": "~2.0", "salla/feature-rules": "~1.0", "salla/form-builder-module": "~1.0", "salla/gamification-php-sdk": "~1.0", "salla/gift-system": "~0.1.0", "salla/google-tags": "~0.1.3", "salla/graphql-client": "~1.0", "salla/help-center-module": "~1.0", "salla/impex": "~1.0", "salla/instagram-api": "~1.0", "salla/languages-module": "~1.1", "salla/laravel-duplicate": "~1.0", "salla/laravel-geo": "~2.0.3", "salla/laravel-otp": "~0.1", "salla/laravel-s": "dev-PHP-8.x", "salla/laravel-s3-browser-based-uploads": "~1.0.0", "salla/laravel-schedule-monitor": "~1.0", "salla/logger": "~2.0", "salla/loyalty-system-module": "~1.0", "salla/mahlydash": "~1.9", "salla/mailchimp": "~1.0", "salla/marketing": "~1.0", "salla/marketing-integration": "~1.2", "salla/marketplace-module": "~1.0", "salla/migrations": "~1.0", "salla/money": "~1.0.0", "salla/offers": "~1.0", "salla/omnipay-bank-transfer": "~1.0.0", "salla/omnipay-noon": "~1.0.0", "salla/omnipay-stc": "~1.0.0", "salla/omnipay-tap": "~1.0", "salla/onesignal-module": "~1.0.0", "salla/orders-module": "~1.2", "salla/payment-module": "~1.0", "salla/paymetns": "~1.0", "salla/planhat-api": "~1.0", "salla/product-module": "~1.0", "salla/qoyod-service": "~1.0", "salla/report-module": "~1.0", "salla/rutterapi": "~1.0", "salla/salla-activities": "~1.0", "salla/saudi-post": "~1.0", "salla/searchable": "~0.1.0", "salla/security-core": "~4.2.5", "salla/settings-manager": "~1.0", "salla/shipping": "~1.6", "salla/shipping-services": "~1.0", "salla/sift": "~1.0.0", "salla/sitemap": "~1.0", "salla/sluggable": "~1.1", "salla/sms": "~1.0", "salla/special-offer-module": "~1.0", "salla/store-app-module": "~1.0.2", "salla/store-branch-module": "~1.0", "salla/store-menu-module": "~1.0", "salla/store-module": "~1.0", "salla/store-resthook": "~2.0", "salla/store-wallet": "~1.0", "salla/stores-cart": "~1.0", "salla/theme-customization-module": "~1.0", "salla/tokenable": "~1.0", "salla/users": "~1.0", "salla/violation-module": "~1.0", "salla/websocket": "~1.0", "salla/whatsapp-business": "~1.0", "salla/zoho-books": "~1.0", "socialiteproviders/instagram": "^4.0", "spatie/laravel-http-logger": "^1.10", "spatie/laravel-image-optimizer": "^1.7.1", "spatie/laravel-web-tinker": "^1.8.4", "spatie/laravel-webhook-client": "^3.2", "spatie/laravel-webhook-server": "^3.8", "symfony/console": "6.4.23", "symfony/css-selector": "^6.0", "symfony/translation": "^5.3.10", "torann/geoip": "^3.0.5", "tymon/jwt-auth": "^2.0", "vitalybaev/google-merchant-feed": "^2.0", "zschuessler/laravel-route-to-class": "^2.0"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.0", "brianium/paratest": "^7.0", "ergebnis/phpunit-slow-test-detector": "^2.0", "fakerphp/faker": "^1.9.1", "filp/whoops": "^2.0", "friendsofphp/php-cs-fixer": "^3.0", "hammerstone/airdrop": "^0.2.3", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "orchestra/testbench": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "phpunit/phpunit": "^10.0", "salla/devmate": "^1.0", "timacdonald/log-fake": "^2.1"}, "scripts": {"php-cs:fix": "vendor/bin/php-cs-fixer fix", "php-cs:issues": "vendor/bin/php-cs-fixer fix --diff --dry-run", "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-create-project-cmd": ["@php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "@composer salla-assets-build"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@composer salla-assets-build"], "salla-assets-build": ["php artisan salla:publish-assets --provider=\"dashboard\"", "php artisan module:publish orders"], "salla:assets-publish": ["@composer salla-assets-build", "npm run prod"], "salla:clear-cache": ["rm -rf bootstrap/cache/routes/* && rm -rf bootstrap/cache/*.php && rm -rf storage/framework/cache/* && mkdir -p storage/framework/cache/data/ && touch storage/framework/cache/data/.gitignore"], "salla:update": ["@composer salla:clear-cache", "@composer update", "@php artisan migrate", "@php artisan clear-compiled && php artisan cache:clear && php artisan config:clear && php artisan route:clear && php artisan view:clear"]}, "type": "project"}