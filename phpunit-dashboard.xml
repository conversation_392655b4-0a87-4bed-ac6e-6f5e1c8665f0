<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" backupStaticAttributes="false" bootstrap="bootstrap/autoload.php" colors="true" convertErrorsToExceptions="true" convertNoticesToExceptions="true" convertWarningsToExceptions="true" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
  <testsuites>
    <!-- specify which tests to be tested in <testsuites> -->
    <testsuite name="dashboard">
      <directory suffix="Test.php">./tests/Feature</directory>
      <!-- <directory suffix="Test.php">./tests/Unit</directory> -->
      <directory suffix="Test.php">./Modules/*/Tests</directory>
      <directory suffix="Test.php">./Modules/*/Tests</directory>
    </testsuite>
  </testsuites>
  <coverage processUncoveredFiles="false">
    <include>
      <directory suffix=".php">./app</directory>
      <directory suffix=".php">./Modules</directory>
    </include>
  </coverage>
  <php>
    <ini name="memory_limit" value="-1" />
    <server name="APP_ENV" value="testing"/>
    <server name="BCRYPT_ROUNDS" value="4"/>
    <server name="CACHE_DRIVER" value="array"/>
    <server name="MAIL_MAILER" value="array"/>
    <server name="QUEUE_CONNECTION" value="sync"/>
    <server name="SESSION_DRIVER" value="array"/>
    <server name="TELESCOPE_ENABLED" value="false"/>
  </php>
</phpunit>
