<?php

namespace Modules\MarketPlace\Tests\Unit\Entities;

use Modules\MarketPlace\Entities\Expert\SallaProductExpertService;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\MarketPlace\Entities\PartnerOrderDetail;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Tests\Triats\GenerateAffiliateDataTrait;
use Modules\ThemeCustomization\Entities\Theme;
use Neves\Testing\DatabaseTransactions;
use Tests\TestCase;

class SallaOrderItemsTest extends TestCase
{
    use GenerateAffiliateDataTrait, DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();

        if ( !\Schema::connection('mysql-partner')->hasColumn('companies', 'commissions')) {
            \DB::connection('mysql-partner')
                ->statement('ALTER TABLE companies ADD COLUMN commissions json NULL;');
        }
    }

    public function test_relation_get_app_from_product()
    {
        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
        ]);

        $item = SallaOrderItems::factory()->create([
            'product_id' => $product,
        ]);

        $app = SallaProductMarketplaceApp::factory()->create([
            'product_id' => $product->id,
        ]);

        $this->assertEquals($app->id, $item->app->id);
    }

    public function test_relation_get_theme_from_product()
    {
        $product = SallaProducts::factory()->create([
            'type' => SallaProductType::THEME,
        ]);

        $item = SallaOrderItems::factory()->create([
            'product_id' => $product,
        ]);

        $theme = Theme::factory()->create([
            'product_id' => $product->id,
        ]);

        $this->assertEquals($theme->id, $item->theme->id);
    }

    public function test_relation_get_export_service_from_product()
    {
        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::SERVICE,
        ]);

        $item = SallaOrderItems::factory()->create([
            'product_id' => $product,
        ]);

        $service = SallaProductExpertService::factory()->create([
            'product_id' => $product->id,
        ]);

        $this->assertEquals($service->id, $item->expretService->id);
    }

    public function test_scope_partner_affiliate_items_not_have_payments()
    {
        env('APP_ENV', 'development');

        $count = 5;
        $companyId = fake()->randomNumber(1, 100);
        $marketerCompanyId = fake()->randomNumber(1, 100);

        $this->generateAffiliateData(
            $companyId,
            $marketerCompanyId,
            $this->testStoreId,
            $count
        );

        $this->assertEquals($count, SallaOrderItems::PartnerAffiliateItemsNotHavePayments()->count());
    }

    /**
     * @dataProvider feeCalculationDataProvider
     */
    public function test_get_fees_calculation($commissionPercentage, $productTypeValue, $total, $taxValue, $expectedFee)
    {
        // Create a company using the factory method with given commission
        $company = PartnerCompany::factory()->withAppsCommissionPercentage($commissionPercentage)->create();

        // Create a product of type 'apps' with the provided type value
        $product = SallaProducts::factory()->create([
            'type'       => 'addon',
            'type_value' => $productTypeValue,
        ]);


        // Create an order item linked to the product and company
        $orderItem = SallaOrderItems::factory()->create([
            'product_id'        => $product->id,
            'developer_user_id' => $company->id, // Link to the company
            'total'             => $total, // Total amount
            'tax_value'         => $taxValue,  // Tax value
        ]);

        // Assert the fee calculation matches the expected fee
        $this->assertEquals($expectedFee, $orderItem->getFess());
    }

    /**
     * Data provider for test_get_fees_calculation
     *
     * @return array
     */
    public static function feeCalculationDataProvider(): array
    {
        return [
            // Scenario: Company A with 12% commission, total 1000, tax 100
            [
                12,                 // commissionPercentage
                SallaProductAddonType::APPS,       // productTypeValue
                1000,               // total
                100,                // taxValue
                round((1000 - 100) * 12 / 100, 2), // expectedFee
            ],
            // Scenario: Company B with 15% commission, total 2000, tax 200
            [
                17,                 // commissionPercentage
                SallaProductAddonType::APPS,    // productTypeValue
                2000,               // total
                200,                // taxValue
                round((2000 - 200) * 17 / 100, 2), // expectedFee
            ],
            // Scenario: Company C with no custom commission, total 2000, tax 200
            [
                null,                 // commissionPercentage
                SallaProductAddonType::APPS,    // productTypeValue
                2000,               // total
                200,                // taxValue
                round((2000 - 200) * 15 / 100, 2), // expectedFee
            ],
        ];
    }
}