<?php

namespace Modules\MarketPlace\Tests;

use App\Models\User;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Salla\Core\Enum\Plan;
use Illuminate\Support\Carbon;
use Salla\Core\Entities\Store;
use Salla\Paymetns\Entities\CreditCard;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\ThemeCustomization\Entities\Theme;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Enum\SallaSubscriptionType;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaSubscriptionRenew;
use Modules\MarketPlace\Console\CreateInvoiceCommand;
use Modules\MarketPlace\Entities\SallaProductFeedback;
use Modules\MarketPlace\Console\AutoPaidInvoiceCommand;
use Modules\MarketPlace\Enum\SallaProductFeedbacksEnum;
use Modules\MarketPlace\Console\CheckSubscriptionsCommand;
use Modules\MarketPlace\Console\DeleteSallaInvoiceCommand;
use Modules\MarketPlace\Console\NotifyUnpaidSallaInvoiceCommand;
use Modules\MarketPlace\Console\CreditCardExpirationAlertCommand;
use Modules\MarketPlace\Console\CheckSallaSubscriptionPendingCommand;
use Modules\MarketPlace\Entities\SallaOrders;
use Salla\Users\Models\Role;

trait TestHelperTrait
{
    /**
     * @runInSeparateProcess
     */
    protected function creditStore(Store $store): void
    {
        store()->setCurrent($store)->depositFloat(10000); // wallet charged
    }


    protected function addCreditToStoreSetting(Store $store): void
    {
        store()->setCurrent($store)->setSetting('payments::method.default','credit_card');

        CreditCard::factory()->create([
            'related_id'        => $store->owner->id,
            'related_type'      => 'user',
            'card'              => '520000XXXXXX1005',
            'card_holder_name'  => 'test',
            'expiration_year'   => now()->addYears(2)->format('Y'),
            'expiration_month'  => 12,
            'identifier_type'   => 1,
            'identifier'        => '1210d709-3382-4d0d-bd2d-acb17f806fe5',
            'type'              => 'mastercard',
            'provider'          => 'noon',
            'main'              => 1,
            'deleted_at'        => null,
            'status'            => 'active',
            'notify'            => 1,
        ]);
    }


    /**
     * @runInSeparateProcess
     */
    protected function runNotifyUnpaidSallaInvoiceCommand(?int $days = null, $fixedDate = false): void
    {
        !$fixedDate && $this->travelBack();
        $this->travel($days ?? 27)->days();
        $this->artisan(NotifyUnpaidSallaInvoiceCommand::class)->assertSuccessful();
    }

    /**
     * @runInSeparateProcess
     */
    protected function runAutoPaidCommand(?int $days = null, $fixedDate = false, $store_id = null): void
    {
        !$fixedDate && $this->travelBack();
        $this->travel($days ?? 30)->days(); // any day from 26 to 30

        if (null !== $store_id) {
            $this->artisan(AutoPaidInvoiceCommand::class, ['--store-id' => $store_id])
                ->assertSuccessful();
        }
        $this->artisan(AutoPaidInvoiceCommand::class)->assertSuccessful();
    }

    /**
     * @runInSeparateProcess
     */
    protected function runCreateInvoiceCommand(?int $days = null, $fixedDate = false): void
    {
        !$fixedDate && $this->travelBack();
        $this->travel($days ?? 24)->days();
        $this->artisan(CreateInvoiceCommand::class)->assertSuccessful();
    }

    /**
     * @runInSeparateProcess
     */
    public function runDeleteSallaInvoiceCommand(?int $days = null, $fixedDate = false): void
    {
        !$fixedDate && $this->travelBack();
        $this->travel($days ?? 31)->days();
        $this->artisan(DeleteSallaInvoiceCommand::class)->assertSuccessful();
    }

    /**
     * @runInSeparateProcess
     */
    public function runCheckSubscriptionsCommand(?int $days = null, $fixedDate = false): void
    {
        !$fixedDate && $this->travelBack();
        $this->travel($days ?? 0)->days();
        $this->artisan(CheckSubscriptionsCommand::class)->assertSuccessful();
    }

    protected function createSubscription(Store $store, int $period, Carbon $endDate, bool $isRecurring = true): SallaSubscriptions
    {
        $product = SallaProducts::query()->where('type_value', $store->plan)->first() ?:
            SallaProducts::factory()->create([
                'type' => SallaProductType::PLAN,
                'type_value' => $store->plan,
            ]);
        $productPrice = SallaProductPrice::query()
            ->where('product_id', $product->id)
            ->where('period', $period)
            ->first() ?: SallaProductPrice::factory()->create([
            'product_id'        => $product->id,
            'price'             => 100,
            'sale_price'        => null,
            'sale_end'          => null,
            'currency'          => 'SAR',
            'period'            => $period,
            'default_price'     => 1,
            'taxable'           => 1,
            'tax_included'      => 0,
            'deleted_at'        => null,
        ]);

        return SallaSubscriptions::factory()->create([
            'store_id' => $store->id,
            'product_id' => $product->id,
            'product_price_id' => $productPrice->id,
            'type' => SallaProductType::PLAN,
            'type_value' => $store->plan,
            'period' => $period,
            'order_id' => $isRecurring ? 123 : 0,
            'end_date' => $endDate->format('Y-m-d'),
            'start_date' => $endDate->subMonth()->format('Y-m-d'),
            'status' => SallaSubscriptionStatus::ACTIVE,
            'monthly_invoice_id' => null,
            'renew' => SallaSubscriptionRenew::AUTO,
            'subscription_type' => $isRecurring ? SallaSubscriptionType::RECURRING : SallaSubscriptionType::TRIAL,
        ]);
    }

    protected function createExpiringSubscription(Carbon $endDate, string $type, int $period = 1): SallaSubscriptions
    {
        $store = $this->createStoreWithPlan();
        $product = SallaProducts::query()->where('type', $type)->first();
        $productPrice = SallaProductPrice::query()
            ->where('product_id', $product->id)
            ->where('period', $period)
            ->first() ?: SallaProductPrice::factory()->create([
            'product_id'        => $product->id,
            'price'             => 100,
            'sale_price'        => null,
            'sale_end'          => null,
            'currency'          => 'SAR',
            'period'            => $period,
            'default_price'     => 1,
            'taxable'           => 1,
            'tax_included'      => 0,
            'deleted_at'        => null,
        ]);

        return SallaSubscriptions::factory()->create([
            'store_id' => $store->id,
            'product_id' => $product->id,
            'product_price_id' => $productPrice->id,
            'type' => $type == 'domain' ? SallaProductType::DOMAIN : SallaProductType::PLAN,
            'period' => $period,
            'end_date' => $endDate->format('Y-m-d'),
            'status' => SallaSubscriptionStatus::ACTIVE,
            'renew' => SallaSubscriptionRenew::MANUALLY,
        ]);
    }
    public function createStoreWithPlan(?string $plan = null): Store
    {
        $faker = \Faker\Factory::create();
        $store = Store::factory()->create(['plan' => $plan ?? Plan::PLUS]);

        $user = User::factory()->create([
            'store_id' => $store->id,
            'role' => 'user',
            'email' => $faker->companyEmail(),
            'status' => 'active',
        ]);

        $role = Role::query()->firstOrCreate([
            'name'       => 'user',
            'guard_name' => 'user',
        ]);

        \DB::table('model_has_roles')
            ->updateOrInsert([
                'model_type' => User::class,
                'model_id'   => $user->id,
                'role_id'    => $role->id,
                'store_id'   => $store->id,
                'is_owner'   => true,
            ], ['status' => 'active']);

        return $store;
    }

    public function createSallaProductWithFeedback(bool $withReply = true): array
    {
        $theme = Theme::factory()->create([
            'marketplace_theme_id' => rand(100, 999),
            'namespace'            => 'test',
        ]);

        $store = Store::factory()->create(['theme_id' => $theme->id, 'theme' => $theme->namespace]);
        store()->setCurrent($store);

        $user = User::factory()->create([
            'store_id' => $store->id,
            'role'   => 'user',
            'status' => 'active',
            'email_verified' => true,
            'enabled' => true,
        ]);
        auth()->loginUsingId($user->id);

        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::THEME,
            'type_value' => $theme->id,
            'hide'       => false,
        ]);
        $theme->update(['product_id' => $product->getKey()]);

        $feedback = SallaProductFeedback::factory()->create([
            'product_id' => $product->id,
            'store_id'   => $store->id,
            'comment'    => 'parent comment',
            'status'     => SallaProductFeedbacksEnum::PUBLISHED
        ]);

        //some test cases don't require replies
        $reply = null;
        if ($withReply) {
            $reply = SallaProductFeedback::factory()->create([
                'product_id' => $product->id,
                'comment'    => 'this is a reply',
                'status'     => SallaProductFeedbacksEnum::PUBLISHED,
                'parent_id'  => $feedback->id,
            ]);
        }

        return [
            'theme'    => $theme,
            'product'  => $product,
            'feedback' => $feedback,
            'reply'    => $reply ?? null,
            'user_id'  => $user->id,
            'user'     => $user,
        ];
    }

    /**
     * @runInSeparateProcess
     */
    public function runCheckSallaSubscriptionPendingCommand(?int $days = null, $fixedDate = false): void
    {
        !$fixedDate && $this->travelBack();
        $this->travel($days ?? 0)->days();
        $this->artisan(CheckSallaSubscriptionPendingCommand::class)->assertSuccessful();
    }

    /**
     * @runInSeparateProcess
     */
    protected function runCreditCardExpirationAlertCommand(?int $days = null): void
    {
        $this->artisan(CreditCardExpirationAlertCommand::class)->assertSuccessful();
    }

    public function initializeExpiredSpecialPlan()
    {
        $this->sallaOrder = SallaOrders::factory()->create([
            'store_id'          => $this->testStoreId,
            'user_id'           => $this->user->id,
            'amount'            => 24000.00,
            'total_discounted_amount' => 0.0,
            'tax'               => 0.15,
            'tax_value'         => 3600.00,
            'total'             => 0.0,
            'currency'          => 'SAR',
            'payment_method'    => 'wallet',
            'store_bank_id'     => 0,
            'city'              => $this->createCity()->id,
            'country'           => $this->createCountry()->id,
            'location'          => null,
            'geocode'           => null,
            'status'            => 'paid',
        ]);

        $this->sallaProduct = SallaProducts::factory()->create([
            'name'              => 'سلة سبيشال',
            'price'             => 20869.57,
            'annual_price'      => 20869.57,
            'currency'          => 'SAR',
            'description'       => 'description',
            'type'              => 'plan',
            'type_value'        => 'special',
            'action_method'     => 'add_plan',
            'level'             => 4,
            'status'            => 'active',
            'is_sandbox'        => 0,
            'require_shipping'  => 0,
            'taxable'           => 1,
            'form'              => 1,
            'is_special'        => 0,
            'deleted_at'        => null,
        ]);

        $this->sallaProductPrice = SallaProductPrice::factory()->create([
            'product_id'        => $this->sallaProduct->id,
            'price'             => 24000,
            'sale_price'        => null,
            'sale_end'          => null,
            'currency'          => 'SAR',
            'period'            => 12,
            'default_price'     => 1,
            'taxable'           => 1,
            'tax_included'      => 0,
            'deleted_at'        => null,
        ]);

        $this->sallaSubscription = SallaSubscriptions::create([
            'store_id'          => $this->testStoreId,
            'order_id'          => $this->sallaOrder->id,
            'product_id'        => $this->sallaProduct->id,
            'type'              => 'plan',
            'type_value'        => 'special',
            'period'            => 12,
            'start_date'        => today(new \DateTimeZone('Asia/Riyadh'))->subYear(),
            'end_date'          => today(new \DateTimeZone('Asia/Riyadh'))->subDay(),
            'status'            => 'expired',
            'quantity'          => 1,
            'product_price_id'  => $this->sallaProductPrice->id,
            'renew'             => 1,
            'amount'            => 24000.00,
            'credit_amount'     => 26466.00,
            'discount_amount'   => 0.00,
            'tax'               => 0.5,
            'tax_value'         => 3600.00,
            'total'             => 27600.00,
        ]);
    }

    private function initializeExpiredPlusPlan() {
        $this->sallaOrder = SallaOrders::factory()->create([
            'store_id'          => $this->testStoreId,
            'user_id'           => $this->user->id,
            'amount'            => 24000.00,
            'total_discounted_amount' => 0.0,
            'tax'               => 0.15,
            'tax_value'         => 3600.00,
            'total'             => 0.0,
            'currency'          => 'SAR',
            'payment_method'    => 'wallet',
            'store_bank_id'     => 0,
            'city'              => $this->createCity()->id,
            'country'           => $this->createCountry()->id,
            'location'          => null,
            'geocode'           => null,
            'status'            => 'paid',
        ]);

        $this->sallaProduct = SallaProducts::factory()->create([
            'name'              => 'سلة بلس',
            'price'             => 20869.57,
            'annual_price'      => 20869.57,
            'currency'          => 'SAR',
            'description'       => 'description',
            'type'              => SallaProductType::PLAN,
            'type_value'        => SallaProductPlanType::PLUS,
            'action_method'     => 'add_plan',
            'level'             => 4,
            'status'            => 'active',
            'is_sandbox'        => 0,
            'require_shipping'  => 0,
            'taxable'           => 0,
            'form'              => 1,
            'is_special'        => 0,
            'deleted_at'        => null,
        ]);

        $this->sallaProductPrice = SallaProductPrice::factory()->create([
            'product_id'        => $this->sallaProduct->id,
            'price'             => 990,
            'sale_price'        => null,
            'sale_end'          => null,
            'currency'          => 'SAR',
            'period'            => 12,
            'default_price'     => 1,
            'taxable'           => 1,
            'tax_included'      => 0,
            'deleted_at'        => null,
        ]);

        $this->sallaSubscription = SallaSubscriptions::create([
            'store_id'          => $this->testStoreId,
            'order_id'          => $this->sallaOrder->id,
            'product_id'        => $this->sallaProduct->id,
            'type'              => 'plan',
            'type_value'        => 'special',
            'period'            => 12,
            'start_date'        => today(new \DateTimeZone('Asia/Riyadh'))->subYear(),
            'end_date'          => today(new \DateTimeZone('Asia/Riyadh'))->subDay(),
            'status'            => SallaSubscriptionStatus::EXPIRED,
            'quantity'          => 1,
            'product_price_id'  => $this->sallaProductPrice->id,
            'renew'             => 1,
            'amount'            => 24000.00,
            'credit_amount'     => 26466.00,
            'discount_amount'   => 0.00,
            'tax'               => 0.5,
            'tax_value'         => 3600.00,
            'total'             => 27600.00,
        ]);
    }
}
