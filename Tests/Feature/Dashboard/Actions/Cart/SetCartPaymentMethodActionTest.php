<?php

namespace Feature\Dashboard\Actions\Cart;

use Modules\MarketPlace\Actions\Cart\SetCartPaymentMethodAction;
use Modules\MarketPlace\Entities\SallaCart;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Services\SallaCartService;
use Salla\Core\Entities\StoreDocument;

class SetCartPaymentMethodActionTest extends \Tests\TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        store()->loadFromId($this->testStoreId);
        store()->update(['plan' => 'basic']);
        $this->user = $this->createUser();
        auth()->logInUsingId($this->user->id);

        $this->sallaCart = SallaCart::factory()->create([
            'store_id' => $this->testStoreId,
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);

        store()->depositFloat(1000000);

        $sallaProduct = SallaProducts::factory()->create([
            'type'              => 'plan',
            'type_value'        => 'plus',
        ]);

        SallaCartItem::factory()->create([
            'store_id'      => $this->user->store_id,
            'user_id'       => $this->user->id,
            'product_id'    => $sallaProduct->id,
            'product_price' => $sallaProduct->price,
            'cart_id'       => $this->sallaCart->id,
            'period'        => 12,
        ]);
    }

    /**
     * @test
     */
    public function it_cannot_apply_credit_with_installment_payment()
    {
        SallaSubscriptions::factory()->create([
            'store_id' => $this->testStoreId,
            'type' => SallaProductType::PLAN,
            'status' =>SallaSubscriptionStatus::EXPIRED
        ]);

        StoreDocument::factory()->create([
            'store_id'                          => $this->testStoreId,
            'owner_identity_is_completed'       => 1,
            'manager_identity_is_completed'     => 1,
            'commercial_register_is_verified'   => 1,
        ]);

        store()->update(['main_account_id'   => 123, 'main_account_verified'   => 1,]);
        store()->epaymentRequests()->create(['status' => 'approved']);

        $this->sallaCart->update(['parameters' => ['is_installment_payment'=> true, 'is_installment_cart' => true]]);
        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);

        SetCartPaymentMethodAction::make(
            ['payment_method' =>
                ['credit' => 'active']
            ])->run();

        $this->assertFalse($cartService->getTotals()->getCredit()->isApplied());
    }
}
