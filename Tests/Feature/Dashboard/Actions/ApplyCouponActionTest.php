<?php

namespace Modules\MarketPlace\Tests\Feature\Dashboard\Actions;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Modules\MarketPlace\Actions\Cart\ApplyCouponAction;
use Modules\MarketPlace\Entities\SallaCart;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaCoupons;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaCartSource;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppDomainType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Enum\SallaCouponCodeEnum;
use Modules\MarketPlace\Enum\SallaCouponType;
use Modules\MarketPlace\Repositories\SallaCouponRepository;
use Modules\MarketPlace\Rules\Coupon\CouponExtraConditionsRule;
use Modules\MarketPlace\Services\SallaCartService;
use Modules\Product\Tests\Dashboard\Controllers\API\HasProductFaker;
use Salla\Core\Traits\Helper\HasDecodeOptimus;
use Salla\Core\Entities\StoreDocument;

class ApplyCouponActionTest extends \Tests\TestCase
{
    use HasProductFaker, HasDecodeOptimus;

    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->createUser();
        auth()->logInUsingId($this->user->id);
        $this->request = new Request;
        $this->sallaCart = SallaCart::factory()->create([
            'store_id' => $this->user->store_id,
            'user_id'  => $this->user->id,
            'status'   => 'active',
        ]);

        $this->sallaCoupon = SallaCoupons::factory()->create([
            'code'                  => 'salla30',
            'type'                  => 'F',
            'amount'                => 20,
            'status'                => 'active',
            'created_by'            => 1,
            'only_new_subscription' => true,
            'expiry_date'           => now()->addMonth()->format('Y-m-d'),
            'usage_limit'           => 100,
            'usage_limit_per_user'  => 1,
        ]);
    }

    /**
     * @test
     */
    public function validation_coupon_code_is_required()
    {
        $validator = Validator::make($this->request->all(), [
            'coupon_code' => 'required',
        ]);
        $this->assertValidations($validator, 'required', 'coupon_code');
    }

    /**
     * @test
     */
    public function coupon_code_is_expire()
    {
        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);

        $rule = new CouponExtraConditionsRule($cartService, null);

        $this->assertFalse($rule->fail(__t('marketplace::cart.messages.error.coupon code not correct or expired')));
    }

    /**
     * @test
     */
    public function not_allow_apply_coupon_to_store_have_any_plan()
    {
        $this->sallaSubscriptions = SallaSubscriptions::factory()->create([
            'store_id'   => $this->user->store_id,
            'type'       => 'plan',
            'type_value' => 'plus',
            'period'     => 'monthly',
            'start_date' => now()->format('Y-m-d'),
            'end_date'   => now()->addDays(30)->format('Y-m-d'),
            'status'     => 'active',
        ]);

        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);

        $rule = new CouponExtraConditionsRule($cartService, $this->sallaCoupon);

        $rule = $rule->passes('attribute', '');

        $this->assertEquals($rule, false);
    }

    /**
     * @test
     */
    public function it_reset_coupon_when_adding_to_cart()
    {
        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);
        $this->couponinfo = app(SallaCouponRepository::class)->checkByCode($this->sallaCoupon->code);

        $cartService->getCart()->update([
            'coupon_code' => $this->couponinfo->code,
            'coupon_id'   => $this->couponinfo->getKey(),
        ]);

        $this->assertDatabaseHas('salla_cart', [
            'id'          => $this->sallaCart->id,
            'store_id'    => $this->user->store_id,
            'user_id'     => $this->user->id,
            'coupon_code' => $this->couponinfo->code,
            'coupon_id'   => $this->couponinfo->getKey(),
            'status'      => 'active'
        ]);

        $cartService->resetCoupon();

        $this->assertDatabaseHas('salla_cart', [
            'id'          => $this->sallaCart->id,
            'coupon_code' => null,
            'coupon_id'   => null,
        ]);
    }

    public function test_it_cannot_apply_coupon_with_installment_payment()
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage(__t('marketplace::cart.messages.error.coupon_not_allowed_with_installment_payment'));

        store($this->testStoreId)->update(['plan' => 'basic']);
        $this->sallaCoupon->update([
            'only_new_subscription' => false,
            'minimum_amount'        => null,
        ]);

        SallaSubscriptions::factory()->create([
            'store_id' => $this->testStoreId,
            'type'     => SallaProductType::PLAN,
            'status'   => SallaSubscriptionStatus::EXPIRED
        ]);

        StoreDocument::factory()->create([
            'store_id'                        => $this->testStoreId,
            'owner_identity_is_completed'     => 1,
            'manager_identity_is_completed'   => 1,
            'commercial_register_is_verified' => 1,
        ]);

        store()->loadFromId($this->testStoreId);
        store()->update(['main_account_id' => 123, 'main_account_verified' => 1,]);
        store()->epaymentRequests()->create(['status' => 'approved']);

        $sallaProduct = SallaProducts::factory()->create([
            'type'       => 'plan',
            'type_value' => 'plus',
        ]);

        SallaCartItem::factory()->create([
            'store_id'      => $this->user->store_id,
            'user_id'       => $this->user->id,
            'product_id'    => $sallaProduct->id,
            'product_price' => $sallaProduct->price,
            'cart_id'       => $this->sallaCart->id,
            'period'        => 12,
        ]);

        $this->sallaCart->update(['parameters' => ['is_installment_payment' => true, 'is_installment_cart' => true]]);

        ApplyCouponAction::make(['coupon_code' => $this->sallaCoupon->code])->setCart($this->sallaCart)->run();
    }

    /**
     * @test
     */
    public function check_coupon_code_added_to_cart()
    {
        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);
        $this->couponinfo = app(SallaCouponRepository::class)->checkByCode($this->sallaCoupon->code);

        $cartService->getCart()->update([
            'coupon_code' => $this->couponinfo->code,
            'coupon_id'   => $this->couponinfo->getKey(),
        ]);

        $this->assertEquals($this->couponinfo->code, $cartService->getCouponCode());
    }

    public function test_applied_wrong_affiliate_coupon_code()
    {
        $this->expectException(ValidationException::class);

        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);

        $code = SallaCouponCodeEnum::PARTNER_MARKETING . fake()->regexify('[A-Za-z0-9]{20}');

        SallaCoupons::factory()->create([
            'code'                => $code,
            'type'                => 'F',
            'amount'              => 20,
            'status'              => 'active',
            'created_by'          => 1,
            'type_coupon'         => SallaCouponType::PARTNER_AFFILIATE,
            'portal_developer_id' => 21,
        ]);

        ApplyCouponAction::make([
            'cart'        => $cartService->getCart(),
            'coupon_code' => $code,
        ])->run();
    }

    public function test_check_coupon_code_of_plan_app_added_to_cart()
    {
        $this->expectException(ValidationException::class);

        $developer_id = 21;

        $this->sallaCart->update([
            'source' => SallaCartSource::APPS_V2,
        ]);

        $code = fake()->text(5);
        $cartService = app(SallaCartService::class)->setCart($this->sallaCart);

        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
        ]);

        SallaProductMarketplaceApp::factory()
            ->create([
                'product_id'        => $product->id,
                'status'            => SallaProductMarketplaceAppStatus::LIVE,
                'domain_type'       => SallaProductMarketplaceAppDomainType::APP,
                'type'              => SallaProductMarketplaceAppType::PUBLIC,
                'developer_id'      => optimus()->encode($developer_id),
                'developer_user_id' => $developer_id,
            ]);

        $price = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price'      => 100,
        ]);

        $price2 = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price'      => 200,
        ]);

        SallaCoupons::factory()->create([
            'code'                => $code,
            'type'                => 'F',
            'amount'              => 5,
            'status'              => 'active',
            'created_by'          => 1,
            'type_coupon'         => SallaCouponType::PARTNER,
            'portal_developer_id' => $developer_id,
            'expiry_date'         => now()->addYears(1),
            'include_product_ids' => '["' . $product->id . '-' . $price->id . '"]',
        ]);

        SallaCartItem::factory()->create([
            'store_id'         => $this->user->store_id,
            'user_id'          => $this->user->id,
            'product_id'       => $product->id,
            'product_price_id' => $price2->id,
            'product_price'    => $price2->price,
            'cart_id'          => $this->sallaCart->id,
            'period'           => 12,
        ]);

        ApplyCouponAction::make([
            'cart'        => $cartService->getCart(),
            'coupon_code' => $code,
        ])->run();
    }

    private function assertValidations($validator, string $rule, string $attribute): void
    {
        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has($attribute));
        $this->assertEquals($validator->errors()->get($attribute),
            [__t("validation.{$rule}", ['attribute' => Str::replace('_', ' ', $attribute)])]);
    }
}
