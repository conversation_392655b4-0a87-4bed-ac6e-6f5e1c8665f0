<?php

namespace Modules\MarketPlace\Tests\Feature\Emails;

use Modules\MarketPlace\Entities\SallaInvoiceItem;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaInvoiceStatus;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionRenew;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Salla\Core\Enum\Plan;
use Tests\TestCase;
use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Entities\SallaInvoice;
use Modules\MarketPlace\Notifications\Invoice\SallaInvoiceExpiredNotification;

class SallaInvoiceExpiredNotificationTest extends TestCase
{

    public function setUp(): void
    {
        parent::setUp();
        $this->store = $this->createStore();
        store()->setCurrent($this->store)->getCurrent();
        Notification::fake();
        feature()->release('multi-stores');
    }

    public function test_it_can_send_invoice_expire_notification(): void
    {
        $invoice = SallaInvoice::factory()->create([
            'store_id' => $this->store->id,
            'year'     => now()->format('Y'),
            'month'    => now()->format('m'),
            'total'    => 100,
            'status'   => SallaInvoiceStatus::PENDING,
            'due_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $subscription = SallaSubscriptions::factory()->create([
            'store_id'           => $this->store->id,
            'status'             => SallaSubscriptionStatus::ACTIVE,
            'type'               => SallaProductType::PLAN,
            'type_value'         => Plan::TEAM,
            'period'             => 1,
            'end_date'           => now()->subDays(2)->format('Y-m-d'),
            'start_date'         => now()->startOfMonth()->format('Y-m-d'),
            'renew'              => SallaSubscriptionRenew::AUTO,
            'monthly_invoice_id' => $invoice->id,
        ]);

        SallaInvoiceItem::factory()->create([
            'invoice_id'      => $invoice->id,
            'subscription_id' => $subscription->id,
        ]);

        $invoice = $invoice->fresh('items');

        $user = $invoice->store->owner;

        $user->notifyNow(New SallaInvoiceExpiredNotification($invoice->id));

        Notification::assertSentTo($user, SallaInvoiceExpiredNotification::class, function($notification) use ($user, $invoice) {
            $mailData = $notification->toMail($user);
            $this->assertEquals(__t('marketplace::email_invoice_expired.have_one_subscription.subject',[
                'username'      => $invoice->store->name,
                'service_name'  => optional($invoice->items[0]->subscription->sallaProduct)?->name ?? '',
                'service_type'  => __t('marketplace::salla_product_type.' . $invoice->items[0]->subscription->type),
            ]), $mailData->toArray()['subject']);
            $this->assertEquals([
                "<EMAIL>",
                "سلة",
            ], $mailData->from);
            return true;
        });

        $this->artisan('marketplace:invoice:delete')
            ->assertExitCode(0);

        $this->assertNotNull($invoice->fresh()->deleted_at);


    }

    public function test_it_can_send_invoice_expire_notification_when_have_many_items(): void
    {
        $invoice = SallaInvoice::factory()->create([
            'store_id' => $this->store->id,
            'year'     => now()->format('Y'),
            'month'    => now()->format('m'),
            'total'    => 100,
            'status'   => SallaInvoiceStatus::PENDING,
            'due_date' => now()->subDays(2)->format('Y-m-d'),
        ]);

        $subscriptions = SallaSubscriptions::factory(2)->create([
            'store_id'           => $this->store->id,
            'status'             => SallaSubscriptionStatus::ACTIVE,
            'type'               => SallaProductType::PLAN,
            'type_value'         => Plan::TEAM,
            'period'             => 1,
            'end_date'           => now()->subDays(2)->format('Y-m-d'),
            'start_date'         => now()->startOfMonth()->format('Y-m-d'),
            'renew'              => SallaSubscriptionRenew::AUTO,
            'monthly_invoice_id' => $invoice->id,
        ]);

        foreach ($subscriptions as $subscription) {
            SallaInvoiceItem::factory()->create([
                'invoice_id'      => $invoice->id,
                'subscription_id' => $subscription->id,
            ]);
        }

        $this->artisan('marketplace:invoice:delete')
            ->assertExitCode(0);

        $this->assertNotNull($invoice->fresh()->deleted_at);
    }

    public function test_it_can_send_salla_invoice_expired_notification_mail_with_correct_details(): void
    {
        $invoice = SallaInvoice::factory()->create([
            'store_id' => $this->store->id,
            'year'     => now()->format('Y'),
            'month'    => now()->format('m'),
            'status'   => SallaInvoiceStatus::PENDING,
            'due_date' => now()->subDays(2)->format('Y-m-d'),
        ]);
        SallaInvoiceItem::factory(2)->create([
            'invoice_id' => $invoice->id,
        ]);

        $user = $invoice->store->owner;

        $user->notifyNow(New SallaInvoiceExpiredNotification($invoice->id));

        Notification::assertSentTo($user, SallaInvoiceExpiredNotification::class, function($notification) use ($user, $invoice) {
            $mailData = $notification->toMail($user);
            $this->assertEquals( __t('marketplace::email_invoice_unpaid_notify.welcome', [
                'username' => $invoice->store->name,
            ]), $mailData->toArray()['subject']);
            $this->assertEquals([
                "<EMAIL>",
                "سلة",
            ], $mailData->from);
            return true;
        });
    }
}
