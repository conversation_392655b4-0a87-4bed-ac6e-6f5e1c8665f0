<?php

namespace Modules\MarketPlace\Tests\Feature\Commands;

use Carbon\Carbon;
use Modules\MarketPlace\Entities\SallaCart;
use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Entities\SallaInvoice;
use Modules\MarketPlace\Entities\SallaInvoiceItem;
use Modules\MarketPlace\Console\DeleteSallaInvoiceCommand;
use Modules\MarketPlace\Repositories\SallaInvoiceRepository;

class DeleteSallaInvoiceCommandTest extends \Tests\TestCase
{

    use CreateExceedDueDateInvoiceTrait;

    protected ?\Salla\Core\Entities\Store $store;

    public function setUp(): void
    {
        parent::setUp();
        $store = $this->createStore();
        $this->store = $store->fresh();
        feature()->release('multi-stores');
    }

    public function test_if_run_delete_invoice_if_due_date_execute()
    {
        $this->artisan(DeleteSallaInvoiceCommand::class)->assertSuccessful();
    }

    public function test_if_delete_invoice_if_due_date_execute()
    {
        Notification::fake();

        $this->createExceedDueDateInvoice($this->store);

        $repository = app(SallaInvoiceRepository::class);
        $invoiceCount = $repository->forDeleteSallaInvoiceCommand()->count();

        $this->artisan(DeleteSallaInvoiceCommand::class)->assertSuccessful();
        $deletedInvoiceCount = SallaInvoice::withTrashed()->whereNotNull('deleted_at')
            ->whereDate('deleted_at', Carbon::now())
            ->count();

        $this->assertEquals($invoiceCount, $deletedInvoiceCount);
    }
}
