<?php

namespace Modules\MarketPlace\Tests\Feature\Commands;

use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Tests\TestHelperTrait;
use Modules\MarketPlace\Notifications\Invoice\SallaInvoiceUnPaidNotifyNotification;

/**
 * @coversDefaultClass NotifyUnpaidSallaInvoiceCommand
 */
class NotifyUnpaidSallaInvoiceCommandTest extends \Tests\TestCase
{
    use TestHelperTrait;

    protected ?\Salla\Core\Entities\Store $store;

    protected function setUp(): void
    {
        parent::setUp();

        Notification::fake();
        $this->travelBack();
        $store = $this->createStore();
        $subscription = $this->createSubscription($store, 1, now());

        $this->store = $store->fresh();
        feature()->release('multi-stores');
    }

    /**
     * @test
     */
    public function test_it_sends_an_email_notification_when_auto_paid_command_faild_to_pay_pending_invoices()
    {
        // arrange
        $this->runCreateInvoiceCommand(0);
        $this->runAutoPaidCommand(0);

        // act
        $this->runNotifyUnpaidSallaInvoiceCommand(0);

        // assert
        Notification::assertSentOnDemand(
            SallaInvoiceUnPaidNotifyNotification::class,
            function ($notification, $channels, $notifiable) {
                return $notifiable->routes['mail'] === $this->store->owner->email;
            }
        );
        Notification::assertSentOnDemandTimes(SallaInvoiceUnPaidNotifyNotification::class, 1);
    }

    /**
     * @covers ::handle
     */
    // public function test_it_skips_email_notification_when_auto_paid_command_been_able_to_pay_the_invoice()
    // {
    //     // arrange
    //     $this->creditStore($this->store);
    //     $this->runCreateInvoiceCommand();
    //     $this->runAutoPaidCommand();

    //     // act
    //     $this->runNotifyUnpaidSallaInvoiceCommand();

    //     // assert
    //     Notification::assertSentOnDemandTimes(SallaInvoiceUnPaidNotifyNotification::class, 0);
    // }

    /**
     * @covers ::handle
     */
    // public function test_it_skips_email_notification_when_there_is_no_invoice()
    // {
    //     // arrange
    //     $this->runCreateInvoiceCommand(15);
    //     $this->runAutoPaidCommand(15);

    //     // act
    //     $this->runNotifyUnpaidSallaInvoiceCommand(15);

    //     // assert
    //     Notification::assertSentOnDemandTimes(SallaInvoiceUnPaidNotifyNotification::class, 0);
    // }
}
