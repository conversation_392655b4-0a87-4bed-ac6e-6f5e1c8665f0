<?php

namespace Dashboard\Console;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\MarketPlace\Entities\Partner\PartnerSetting;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppDomainType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppPlanType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Enum\SallaSubscriptionType;
use Tests\TestCase;

class ExpireFreeSubscriptionsOfAppsCommandTest extends TestCase
{
    use DatabaseTransactions;

    public function test_skip_salla_company()
    {
        $this->artisan("marketplace:expire-free-subscription-of-app --company=4")->assertSuccessful();
    }

    public function test_remove_subscription_of_companies()
    {
        [$company_1, $subscription_1] = $this->setApps();
        [$company_2, $subscription_2] = $this->setApps(true);

        $this->artisan("marketplace:expire-free-subscription-of-app --company=" . $company_1->id)->assertSuccessful();
        $this->artisan("marketplace:expire-free-subscription-of-app --company=" . $company_2->id)->assertSuccessful();

        $subscription_1->refresh();

        $this->assertNotEmpty($subscription_1->expired_at);
        $this->assertEquals(SallaSubscriptionStatus::EXPIRED, $subscription_1->status);

        $this->deleteSetting($company_1);
        $company_1->delete();

        $this->deleteSetting($company_2);
        $company_2->delete();
    }

    private function setApps($has_free_plan = false)
    {
        $company = PartnerCompany::factory()->create();
        if ($has_free_plan) {
            PartnerSetting::factory()->create([
                'bag'   => $company->id,
                'key'   => 'features::show_app_free_plan',
                'value' => 1,
            ]);
        }

        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
        ]);

        $price = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
        ]);

        SallaProductMarketplaceApp::factory()->create([
            'developer_user_id' => $company->id,
            'product_id'        => $product->id,
            'status'            => SallaProductMarketplaceAppStatus::LIVE,
            'domain_type'       => SallaProductMarketplaceAppDomainType::APP,
            'type'              => SallaProductMarketplaceAppType::PUBLIC,
            'plan_type'         => SallaProductMarketplaceAppPlanType::RECURRING,
            'is_salla_app'      => false,
        ]);

        $subscription = SallaSubscriptions::factory()->create([
            'product_id'        => $product->id,
            'product_price_id'  => $price->id,
            'type'              => SallaProductType::ADDON,
            'type_value'        => SallaProductAddonType::APPS,
            'status'            => SallaSubscriptionStatus::ACTIVE,
            'order_id'          => 0,
            'subscription_type' => SallaSubscriptionType::FREE,
            'expired_at'        => null,
        ]);

        return [
            $company,
            $subscription,
        ];
    }

    /**
     * @param $company
     * @return void
     */
    private function deleteSetting($company)
    {
        PartnerSetting::where('bag', $company->id)
            ->where('key', 'features::show_app_free_plan')
            ->delete();
    }
}