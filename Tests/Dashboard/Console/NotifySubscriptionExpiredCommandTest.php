<?php

namespace Modules\MarketPlace\Tests\Feature\Dashboard\Console;

use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Entities\SallaSubscriptionReminder;
use Modules\MarketPlace\Tests\Dashboard\Traits\HasSubscriptionReminderCreate;
use Modules\Store\Entities\SallaSubscriptions;
use Modules\Store\Enum\SallaOrderStatus;
use Modules\Store\Enum\SallaProductType;
use Tests\TestCase;

class NotifySubscriptionExpiredCommandTest extends TestCase
{
    use HasSubscriptionReminderCreate;

    protected $store;

    public function setUp(): void
    {
        parent::setUp();
        $this->store = $this->createStore();
        store()->setCurrent($this->store)->getCurrent();
        Notification::fake();
        feature()->release('multi-stores');
    }

    public function test_send_reminder_subscription_will_come_due_date(): void
    {
        $this->setupProductAndRelatedEntities();
        $subscriptions = $this->createSubscriptions(2, [
            'end_date' => now()->format('Y-m-d'),
        ]);
        foreach ($subscriptions as $subscription) {
            $subscriptionReminder = SallaSubscriptionReminder::factory()->create([
                'subscription_id' => $subscription->id,
                'due_date'        => now()->format('Y-m-d'),
            ]);
            $this->artisan('marketplace:subscription-reminder:create')
                ->assertExitCode(0);

            $this->assertNotNull($subscriptionReminder->fresh()->deleted_at);
        }

    }

    public function test_owner_store_deleted_when_reminder_subscription(): void
    {
        $this->store->owner()->delete();
        $this->setupProductAndRelatedEntities();
        $subscription = $this->createSubscriptions(1, [
            'end_date' => now()->format('Y-m-d'),
        ]);

        $subscriptionReminder = SallaSubscriptionReminder::factory()->create([
            'subscription_id' => $subscription[0]->id,
            'due_date'        => now()->format('Y-m-d'),
        ]);
        $this->artisan('marketplace:subscription-reminder:create')
            ->assertExitCode(0);

        $this->assertNotNull($subscriptionReminder->fresh()->deleted_at);
    }
}
