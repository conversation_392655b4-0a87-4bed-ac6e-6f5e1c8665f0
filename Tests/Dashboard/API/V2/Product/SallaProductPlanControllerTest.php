<?php

namespace Modules\MarketPlace\Tests\Dashboard\API\V2\Product;

use App\Models\StoreBankAccount;
use Illuminate\Support\Facades\Validator;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Mo<PERSON>les\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Http\Requests\Dashboard\Api\SallaProductPlanRequest;
use Salla\Core\Entities\StoreDocument;
use Salla\Core\Enum\StoreEntityEnum;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class SallaProductPlanControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->store = $this->createStore();
        $this->user = $this->createUser();
        store()->setCurrent($this->store)->getCurrent();
        auth()->loginUsingId($this->user->id);
        $this->token = $this->createToken();
        $this->withoutExceptionHandling();
    }

    public function test_request_type_validation_passes_for_valid_values(): void
    {
        $data = ['request_type' => 'onboarding'];

        $validator = Validator::make($data, (new SallaProductPlanRequest())->rules());

        $this->assertFalse($validator->fails());
    }

    public function test_request_type_validation_fails_when_missing(): void
    {
        $data = [];

        $validator = Validator::make($data, (new SallaProductPlanRequest())->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('request_type', $validator->errors()->toArray());
    }

    public function test_request_type_validation_fails_for_invalid_value(): void
    {
        $data = ['request_type' => 'invalid_value'];

        $validator = Validator::make($data, (new SallaProductPlanRequest())->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('request_type', $validator->errors()->toArray());
    }

    public function test_it_returns_product_plans_successfully(): void
    {
        $this->createPlan(SallaProductPlanType::PLUS);
        $this->createPlan(SallaProductPlanType::PLUS, 12);
        $this->createPlan(SallaProductPlanType::TEAM, 12);
        $this->createPlan(SallaProductPlanType::TEAM, 24);
        $this->createPlan(SallaProductPlanType::SPECIAL, 24);
        $this->store->update(['plan' => SallaProductPlanType::BASIC, 'entity' => StoreEntityEnum::PERSON]);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->token}"])->getJson(route('api.v2.marketplace.plans',[
            'request_type' => 'onboarding'
        ]));
        $response->assertStatus(Response::HTTP_OK);
    }

    public function test_it_returns_product_plans_successfully_with_filter_type_value(): void
    {
        $this->createPlan(SallaProductPlanType::PLUS);
        $this->createPlan(SallaProductPlanType::PLUS, 12);
        $this->createPlan(SallaProductPlanType::TEAM, 12);
        $this->createPlan(SallaProductPlanType::TEAM, 24);
        $this->createPlan(SallaProductPlanType::SPECIAL, 24);
        $this->store->update(['plan' => SallaProductPlanType::PLUS, 'entity' => StoreEntityEnum::PERSON]);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->token}"])->getJson(route('api.v2.marketplace.plans',[
            'request_type' => 'onboarding',
            'type_value' => SallaProductPlanType::TEAM,
        ]));
        $response->assertStatus(Response::HTTP_OK);
    }

    public function test_it_returns_product_plans_successfully_if_store_charity(): void
    {
        store()->epaymentRequests()->create(['status' => 'approved']);
        $this->createPlan(SallaProductPlanType::TEAM, 12);
        StoreDocument::factory()->create([
            'store_id'                          => $this->store->id,
            'owner_identity_is_completed'       => 1,
            'manager_identity_is_completed'     => 1,
            'commercial_register_is_verified'    => 1,
        ]);
        $storeBankAccount = StoreBankAccount::factory()->create(['store_id' => $this->store->id]);
        $this->store->update([
            'plan' => SallaProductPlanType::BASIC,
            'entity' => StoreEntityEnum::CHARITY,
            'main_account_id'   => $storeBankAccount->id,
            'main_account_verified' => 1
        ]);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->token}"])->getJson(route('api.v2.marketplace.plans',['request_type' => 'marketplace']));
        $response->assertStatus(Response::HTTP_OK);
    }

    public function test_it_returns_product_plans_based_on_utm_source(): void
    {
        store()->epaymentRequests()->create(['status' => 'approved']);
        $this->createPlan(SallaProductPlanType::TEAM, 12, true);
        StoreDocument::factory()->create([
            'store_id'                          => $this->store->id,
            'owner_identity_is_completed'       => 1,
            'manager_identity_is_completed'     => 1,
            'commercial_register_is_verified'    => 1,
        ]);
        $storeBankAccount = StoreBankAccount::factory()->create(['store_id' => $this->store->id]);
        $this->store->update([
            'plan' => SallaProductPlanType::BASIC,
            'entity' => StoreEntityEnum::PERSON,
            'main_account_id'   => $storeBankAccount->id,
            'main_account_verified' => 1
        ]);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->token}"])->getJson(route('api.v2.marketplace.plans',[
            'request_type' => 'marketplace',
            'utm_source'   => 'salla',
        ]));
        $response->assertStatus(Response::HTTP_OK);
    }
    public function test_it_returns_product_plans_based_on_utm_campaign(): void
    {
        store()->epaymentRequests()->create(['status' => 'approved']);
        $this->createPlan(SallaProductPlanType::TEAM, 12, true);
        StoreDocument::factory()->create([
            'store_id'                          => $this->store->id,
            'owner_identity_is_completed'       => 1,
            'manager_identity_is_completed'     => 1,
            'commercial_register_is_verified'    => 1,
        ]);
        $storeBankAccount = StoreBankAccount::factory()->create(['store_id' => $this->store->id]);
        $this->store->update([
            'plan' => SallaProductPlanType::BASIC,
            'entity' => StoreEntityEnum::PERSON,
            'main_account_id'   => $storeBankAccount->id,
            'main_account_verified' => 1
        ]);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->token}"])->getJson(route('api.v2.marketplace.plans',[
            'request_type' => 'marketplace',
            'utm_campaign' => 'salla',
        ]));
        $response->assertStatus(Response::HTTP_OK);
    }
    public function test_it_returns_product_plans_based_on_utm_source_and_campaign(): void
    {
        store()->epaymentRequests()->create(['status' => 'approved']);
        $this->createPlan(SallaProductPlanType::TEAM, 12, true);
        StoreDocument::factory()->create([
            'store_id'                          => $this->store->id,
            'owner_identity_is_completed'       => 1,
            'manager_identity_is_completed'     => 1,
            'commercial_register_is_verified'    => 1,
        ]);
        $storeBankAccount = StoreBankAccount::factory()->create(['store_id' => $this->store->id]);
        $this->store->update([
            'plan' => SallaProductPlanType::BASIC,
            'entity' => StoreEntityEnum::PERSON,
            'main_account_id'   => $storeBankAccount->id,
            'main_account_verified' => 1
        ]);

        $response = $this->withHeaders(['Authorization' => "Bearer {$this->token}"])->getJson(route('api.v2.marketplace.plans',[
            'request_type' => 'marketplace',
            'utm_source'   => 'salla',
            'utm_campaign' => 'salla',
        ]));
        $response->assertStatus(Response::HTTP_OK);
    }

    private function createPlan($typeValue, $period = 1, $addUtm = false): void
    {
        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::PLAN,
            'type_value' => $typeValue,
        ]);

        $productPrice = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'period'     => $period,
        ]);

        if ($addUtm) {
            $productPrice->utm()->updateOrCreate([], [
                'utm_source' => 'salla',
            ]);
        }

    }
}
