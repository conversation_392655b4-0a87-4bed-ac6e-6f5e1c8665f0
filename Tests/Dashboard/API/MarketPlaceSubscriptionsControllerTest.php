<?php

namespace Dashboard\API;

use App\Models\User;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Entities\SallaOrders;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\InstalledThemeStatus;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppDomainType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceType;
use Modules\MarketPlace\Enum\SallaSubscriptionType;
use Modules\Store\Enum\StorePattern;
use Tests\TestCase;
use App\Models\SallaSubscriptions;
use Mo<PERSON>les\MarketPlace\Enum\SallaProductType;
use Modules\ThemeCustomization\Entities\Theme;
use Salla\Core\Entities\Store;
use Salla\Settings\Facades\Settings;
use Symfony\Component\HttpFoundation\Response;

class MarketPlaceSubscriptionsControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->apiKey = 'testkey';

        Settings::set('salla::developers-portal.api-key', $this->apiKey);

        feature()->release('multi-stores');
    }


//    public function test_get_active_subscriptions_of_themes_for_developer()
//    {
//        $theme = $this->getTheme('active');
//
//        $this
//            ->json('GET', route('api.v2.marketplace.apps.subscriptions', [
//                'developer_company_id' => $theme->developer_user_id,
//            ]), [
//                'type' => SallaProductType::THEME,
//            ], [
//                'Api-Key' => $this->apiKey,
//            ])
//            ->assertJson([
//                'status'  => Response::HTTP_OK,
//                'success' => true,
//                'data'    => [
//                    [
//                        'theme_status' => [
//                            'name' => InstalledThemeStatus::ACTIVE,
//                        ],
//                    ],
//                ],
//            ]);
//    }
//
    public function test_get_inactive_subscriptions_of_themes_for_developer()
    {
        $theme = $this->getTheme('inactive');

        $this
            ->json('GET', route('api.v2.marketplace.apps.subscriptions', [
                'developer_company_id' => $theme->developer_user_id,
            ]), [
                'type' => SallaProductType::THEME,
            ], [
                'Api-Key' => $this->apiKey,
            ])
            ->assertOk();
    }

    public function test_get_subscriptions_of_themes_for_developer_ready_store()
    {
        $theme = $this->getTheme('inactive', true);

        $this
            ->json('GET', route('api.v2.marketplace.apps.subscriptions', [
                'developer_company_id' => $theme->developer_user_id,
            ]), [
                'type' => SallaProductType::THEME,
            ], [
                'Api-Key' => $this->apiKey,
            ])
            ->assertOk();
    }

    public function test_get_subscriptions_of_apps_for_developer()
    {
        $developerId = 1;
        $this->getApp($developerId);

        SallaSubscriptions::where('type', SallaProductType::ADDON)
            ->where('type_value', SallaProductAddonType::APPS)
            ->where(function ($query) {
                return $query->where('store_id', 0)
                    ->whereNull('store_id');
            })
            ->delete();

        $this
            ->json('GET', route('api.v2.marketplace.apps.subscriptions', [
                'developer_company_id' => $developerId,
            ]), [
                'type' => SallaProductMarketplaceType::APP,
            ], [
                'Api-Key'         => $this->apiKey,
                'Accept-Language' => 'en',
            ])
            ->assertJson([
                'status'  => Response::HTTP_OK,
                'success' => true,
            ]);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Model|Theme
     */
    private function getApp($developerId)
    {
        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
        ]);

        $price = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
        ]);

        SallaProductMarketplaceApp::factory()->create([
            'product_id'        => $product->id,
            'status'            => SallaProductMarketplaceAppStatus::LIVE,
            'domain_type'       => SallaProductMarketplaceAppDomainType::APP,
            'type'              => SallaProductMarketplaceAppType::PUBLIC,
            'developer_user_id' => $developerId,
        ]);

        $store = Store::factory()->create();

        User::factory()->create([
            'store_id' => $store->id,
            'role'     => 'user',
        ]);

        $order = SallaOrders::factory()->create([
            'store_id' => $store->id,
        ]);

        $orderItem = SallaOrderItems::factory()->create([
            'order_id' => $order->id,
        ]);

        SallaSubscriptions::create([
            'product_id'        => $product->id,
            'product_price_id'  => $price->id,
            'store_id'          => $store->id,
            'order_id'          => $order->id,
            'order_item_id'     => $orderItem->id,
            'type'              => SallaProductType::ADDON,
            'type_value'        => SallaProductAddonType::APPS,
            'status'            => 'active',
            'subscription_type' => SallaSubscriptionType::RECURRING,
            'period'            => 1,
        ]);
    }

    private function getTheme($status = 'active', $as_template = false)
    {
        $theme = Theme::factory()->create();
        $product = SallaProducts::find($theme->product_id);
        $product->update([
            'type'       => SallaProductType::THEME,
            'type_value' => $theme->id,
        ]);

        $store = Store::factory()->create([
            'theme_id' => $status == 'active' ? $theme->id : null,
            'pattern' => $as_template ? StorePattern::FIXED_TEMPLATE : StorePattern::NORMAL,
        ]);

        User::factory()->create([
            'store_id' => $store->id,
            'role'     => 'user',
        ]);

        SallaSubscriptions::create([
            'store_id'          => $store->id,
            'product_id'        => $theme->product_id,
            'type'              => SallaProductType::THEME,
            'type_value'        => $theme->id,
            'status'            => 'active',
            'order_id'          => 1,
            'is_template'       => 0,
            'subscription_type' => SallaSubscriptionType::ONCE
        ]);

        return $theme;
    }
}
