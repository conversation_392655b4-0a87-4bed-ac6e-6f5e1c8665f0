<?php

namespace Dashboard\API\Affiliate;

use Illuminate\Support\Facades\Storage;
use Modules\DashboardApi\Tests\DashboardApiTestCase;
use Modules\MarketPlace\Tests\Triats\GenerateAffiliateDataTrait;
use Salla\Settings\Facades\Settings;
use Symfony\Component\HttpFoundation\Response;

class AffiliateExportOrderControllerTest extends DashboardApiTestCase
{
    use GenerateAffiliateDataTrait;

    public function setUp(): void
    {
        parent::setUp();

        $this->apiKey = 'testkey';

        Settings::set('salla::developers-portal.api-key', $this->apiKey);

        Storage::fake('s3');
    }

    public function test_get_export_order_affiliate()
    {
        $companyId = fake()->randomNumber(1, 100);
        $marketerCompanyId = fake()->randomNumber(1, 100);

        $this->generateAffiliateData(
            $companyId,
            $marketerCompanyId,
            $this->testStoreId,
            5
        );

        $response = $this->withHeaders([
            'api-key' => $this->apiKey,
        ])
            ->getJson(route('api.v2.partner_affiliate.orders.export', [
                'partner_company_id' => $companyId,
            ]));

        $response->assertJson([
            'status' => Response::HTTP_OK,
        ])->assertJsonStructure([
            'data' => [
                'path',
            ],
        ]);
    }

}