<?php

namespace Dashboard\Jobs;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppDomainType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppPlanType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Enum\SallaProductPriceType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Mo<PERSON>les\MarketPlace\Enum\SallaSubscriptionType;
use Modules\MarketPlace\Jobs\App\ExpireFreeAppSubscriptionsJob;
use Tests\TestCase;

class ExpireFreeAppSubscriptionsJobTest extends TestCase
{
    use DatabaseTransactions;

    public function test_check_if_app_not_live_skip()
    {
        $app = SallaProductMarketplaceApp::factory()->create([
            'status' => SallaProductMarketplaceAppStatus::DEVELOPMENT,
        ]);

        $result = (new ExpireFreeAppSubscriptionsJob($app))->handle();

        $this->assertEmpty($result);
    }

    public function test_check_if_app_have_free_plan()
    {
        $version = 1;

        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
        ]);

        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'store_id'   => null,
            'price'      => 0,
            'type'       => SallaProductPriceType::Recurring,
            'version'    => $version,
        ]);

        $app = SallaProductMarketplaceApp::factory()->create([
            'product_id'     => $product->id,
            'domain_type'    => SallaProductMarketplaceAppDomainType::APP,
            'status'         => SallaProductMarketplaceAppStatus::LIVE,
            'type'           => SallaProductMarketplaceAppType::PUBLIC,
            'plan_type'      => SallaProductMarketplaceAppPlanType::RECURRING,
            'update_version' => $version,
        ]);

        $result = (new ExpireFreeAppSubscriptionsJob($app))->handle();

        $this->assertEmpty($result);
    }

    public function test_expired_subscription_successfully()
    {
        $version = 1;

        $product = SallaProducts::factory()->create([
            'type'       => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
        ]);

        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'store_id'   => null,
            'price'      => 0,
            'type'       => SallaProductPriceType::Recurring,
            'deleted_at' => now(),
        ]);

        $app = SallaProductMarketplaceApp::factory()->create([
            'product_id'     => $product->id,
            'domain_type'    => SallaProductMarketplaceAppDomainType::APP,
            'status'         => SallaProductMarketplaceAppStatus::LIVE,
            'type'           => SallaProductMarketplaceAppType::PUBLIC,
            'plan_type'      => SallaProductMarketplaceAppPlanType::RECURRING,
            'update_version' => $version,
        ]);

        $subscription = SallaSubscriptions::factory()->create([
            'product_id'        => $product->id,
            'type'              => SallaProductType::ADDON,
            'type_value'        => SallaProductAddonType::APPS,
            'status'            => SallaSubscriptionStatus::ACTIVE,
            'order_id'          => 0,
            'subscription_type' => SallaSubscriptionType::FREE,
            'expired_at'        => null,
        ]);

        (new ExpireFreeAppSubscriptionsJob($app))->handle();

        $subscription->refresh();

        $this->assertNotEmpty($subscription->expired_at);
        $this->assertEquals(SallaSubscriptionStatus::EXPIRED, $subscription->status);
    }
}