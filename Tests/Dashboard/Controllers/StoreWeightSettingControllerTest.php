<?php

namespace Modules\Settings\Tests\Dashboard\Controllers;

use Illuminate\Http\Request;
use Tests\TestCase;
use Modules\Product\Features\ProductDefaultWeightFeature;

class StoreWeightSettingControllerTest extends TestCase
{

    public function setUp(): void
    {
        parent::setUp();

        $this->store = $this->createStore();

        store()->setCurrent($this->store)->getCurrent();

        $this->user = $this->createUser();

        auth()->loginUsingId($this->user->id);

        $this->request = new Request;

    }

    public function test_store_value()
    {
        store()->plan = 'special';
        store()->save();
        store()->setSetting('features::'.ProductDefaultWeightFeature::getName(), 1);

        $response = $this->post(route('cp.settings.weight.value') , ['weight' => 0.02]);

        $response->assertStatus(200);
    }

    public function test_store_value_for_basic_plan()
    {
        store()->plan = 'basic';
        store()->save();

        $response = $this->post(route('cp.settings.weight.value') , ['weight' => 0.02]);

        $response->assertStatus(403);
    }
}
