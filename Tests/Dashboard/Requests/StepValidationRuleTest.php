<?php

namespace Modules\Settings\Tests\Dashboard\Requests;
use Modules\Settings\Rules\StepVaildationRule;
use <PERSON>la\Core\Entities\StoreDocument;
use Salla\Core\Enum\StoreDocumentsStatus;
use Tests\TestCase;

class StepValidationRuleTest extends TestCase
{

    public function setUp(): void
    {
        parent::setUp();

        $this->store = $this->createStore();

        store()->setCurrent($this->store)->getCurrent();

        $this->user = $this->createUser();

        auth()->loginUsingId($this->user->id);

        $this->seedTestData();

    }

    protected function seedTestData()
    {
        $this->actingAs(store()->owner);

        \Salla\Core\Entities\StoreDocument::factory()->create([
            'store_id' => $this->testStoreId,
            'status' => StoreDocumentsStatus::PENDING,
            'owner_identity_is_verified' => true,
            'manager_identity_is_verified' => true,
            'commercial_register_is_verified' => true,
            'commercial_number' => '5900152572',
            'commercial_register_unified_number' => '7043374382',
            'charity_certificate_image' => null,
        ]);
    }

    /** @test */
    public function it_passes_validation_when_conditions_are_met()
    {
        $rule = new StepVaildationRule('iban');
        $this->assertTrue($rule->passes('iban_number', '************************'));
    }

    /** @test */
    public function it_fails_validation_when_owner_identity_is_not_verified()
    {
        $document = StoreDocument::withoutGlobalScope('approved')->latest('id')->first();
        $document->update(['owner_identity_is_verified' => false]);

        $rule = new StepVaildationRule('manager');

        $this->assertFalse($rule->passes('iban_number', '************************'));
        $this->assertEquals('يرجى اكمال خطوة مالك المتجر اولاً', $rule->message());
    }

    /** @test */
    public function it_fails_validation_when_manager_identity_is_not_verified_for_charity()
    {
        $document = StoreDocument::withoutGlobalScope('approved')->latest('id')->first();
        $document->update(['manager_identity_is_verified' => false]);

        $rule = new StepVaildationRule('charity');
        $this->assertFalse($rule->passes('iban_number', '************************'));
        $this->assertEquals('يرجى اكمال خطوة مدير المتجر اولاً', $rule->message());
    }

    /** @test */
    public function it_fails_validation_when_commercial_register_is_not_verified()
    {
        $document = StoreDocument::withoutGlobalScope('approved')->latest('id')->first();
        $document->update(['commercial_register_is_verified' => false]);
        store()->update(['entity' => 'company']);

        $rule = new StepVaildationRule('vat');
        $this->assertFalse($rule->passes('iban_number', '************************'));
        $this->assertEquals('يرجى اكمال خطوة السجل التجاري اولاً', $rule->message());
    }

    /** @test */
    public function it_fails_validation_when_charity_certificate_image_is_not_provided()
    {
        store()->update(['entity' => 'charity']);

        $document = StoreDocument::withoutGlobalScope('approved')->latest('id')->first();
        $document->update(['charity_certificate_image' => null]);

        $rule = new StepVaildationRule('vat');
        $this->assertFalse($rule->passes('iban_number', '************************'));
        $this->assertEquals('يرجى اكمال خطوة شهادة الجمعية اولاً', $rule->message());
    }

    /** @test */
    public function it_fails_validation_when_manager_identity_is_locked()
    {
        // Assuming you have a method to lock the manager identity
        store()->verificationLogs()->create(['type' => 'manager_identity']);
        store()->verificationLogs()->create(['type' => 'manager_identity']);
        store()->verificationLogs()->create(['type' => 'manager_identity']);

        $rule = new StepVaildationRule('manager');
        $this->assertFalse($rule->passes('iban_number', '************************'));
        $this->assertEquals('يمكنك معاودة المحاولة بعد 24 ساعة', $rule->message());
    }

    /** @test */
    public function it_fails_validation_when_owner_identity_is_locked()
    {
        // Assuming you have a method to lock the owner identity
        store()->verificationLogs()->create(['type' => 'owner_identity']);
        store()->verificationLogs()->create(['type' => 'owner_identity']);
        store()->verificationLogs()->create(['type' => 'owner_identity']);

        $rule = new StepVaildationRule('owner');
        $this->assertFalse($rule->passes('iban_number', '************************'));
        $this->assertEquals('يمكنك معاودة المحاولة بعد 24 ساعة', $rule->message());
    }

    /** @test */
    public function it_fails_validation_when_commercial_is_locked()
    {

        $document = StoreDocument::withoutGlobalScope('approved')->latest('id')->first();
        $document->update(['commercial_register_is_verified' => false]);

        // Assuming you have a method to lock the iban
        store()->verificationLogs()->create(['type' => 'commercial_register']);
        store()->verificationLogs()->create(['type' => 'commercial_register']);
        store()->verificationLogs()->create(['type' => 'commercial_register']);

        $rule = new StepVaildationRule('commercial');
        $this->assertFalse($rule->passes('commercial', '************************'));
        $this->assertEquals('يمكنك معاودة المحاولة بعد 24 ساعة', $rule->message());
    }


    /** @test */
    public function it_fails_validation_when_personal_is_locked()
    {

        $document = StoreDocument::withoutGlobalScope('approved')->latest('id')->first();
        $document->update(['commercial_register_is_verified' => false]);

        // Assuming you have a method to lock the iban
        store()->verificationLogs()->create(['type' => 'manager_identity']);
        store()->verificationLogs()->create(['type' => 'manager_identity']);
        store()->verificationLogs()->create(['type' => 'manager_identity']);

        $rule = new StepVaildationRule('personal');
        $this->assertFalse($rule->passes('personal', '123456789'));
        $this->assertEquals('يمكنك معاودة المحاولة بعد 24 ساعة', $rule->message());
    }
}