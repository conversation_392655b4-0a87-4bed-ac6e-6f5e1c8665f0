<?php

namespace Modules\MarketPlace\Actions\Coupon;

use Illuminate\Support\Str;
use Salla\Core\Base\Action;
use Illuminate\Validation\Rule;
use Salla\Logger\Facades\Logger;
use Salla\Core\Entities\StoreActivity;
use Modules\MarketPlace\Enum\SallaCouponType;
use Modules\MarketPlace\Entities\SallaCoupons;
use Modules\MarketPlace\Enum\SallaCouponStatus;
use Modules\MarketPlace\Enum\SallaCouponCodeEnum;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Entities\SallaCouponStores;
use Modules\MarketPlace\Enum\SallaCouponConditionTypes;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Actions\ExtractStoresIdsFromFileSheet;
use Modules\MarketPlace\Enum\SallaCouponSubscriptionStatusOptions;

/**
 * Class StoreSallaCouponAction
 * @method self setFile($file)
 * @property  mixed file
 *
 * @package Modules\MarketPlace\Actions\Coupon
 */
class StoreSallaCouponAction extends Action
{
    /**
     * @var SallaCoupons $coupon
     */
    protected $coupon;

    /**
     * @return array|\ArrayAccess|mixed
     */
    public function rules()
    {
        $rules = [];
        //when called from nasa we ignore this validation rule
        if (!isNasa()) {
            $rules = [
                'developer_id'      => 'required|numeric',
                'stores'            => 'nullable|array',
                'external_products' => 'nullable|array',
            ];
        }

        $rules = array_merge($rules, [
            'code' => [
                'required', 'string', 'max:255', 'regex:/^[\p{Arabic}a-zA-Z0-9٠-٩\-ـ]+$/u',
                function ($attr, $val, $fail) {
                    // when called from partner portal
                    if (
                        !isNasa() && (
                            !Str::startsWith($val, SallaCouponCodeEnum::PARTNER) &&
                            !Str::startsWith($val, SallaCouponCodeEnum::PARTNER . '-') &&
                            !Str::startsWith($val, SallaCouponCodeEnum::PARTNER_MARKETING) &&
                            !in_array($val, settings()->get('affiliate_special_coupons', []))
                        )
                    ) {
                        $fail('يجب أن يبدأ الكود بـ SP-');
                    }

                    return true;
                },
                Rule::unique('salla_coupons', 'code')
                    ->whereNot('status', SallaCouponStatus::DELETED)
                    ->when(!empty($this->coupon), function ($query) {
                        return $query->ignore($this->coupon->id);
                    })
            ],

            'type'                 => ['required', Rule::in(['P', 'F']),],
            'amount'               => ['required', 'numeric',],
            'minimum_amount'       => ['nullable', 'numeric',],
            'maximum_amount'       => ['nullable', 'numeric',],
            'expiry_date'          => ['nullable', 'date'],
            'usage_limit'          => ['nullable', 'numeric',],
            'usage_limit_per_user' => ['nullable', 'numeric',],
            'status'               => ['nullable', Rule::in(SallaCouponStatus::toArray())],
            'store_id'             => ['nullable', 'numeric',],
            'apps'                 => ['nullable', 'array',],
            // extra validations
        ]);

        if (isNasa()) {

            return array_merge($rules, [
                'periods'          => 'nullable|array',
                'plan_type'        => 'nullable|array',
                'store_entity'     => 'nullable|array',
                'store_activities' => 'nullable|array',

                'periods.*'          => ['sometimes', 'numeric',], // 1=>monthly , 12 =>annualy,
                'plan_type.*'        => ['sometimes', Rule::in(SallaProductPlanType::toArray())],
                'store_activities.*' => ['sometimes', Rule::exists((new StoreActivity())->getTable(), 'id')->where('status', 1)],
                'store_entity.*'     => ['sometimes', Rule::in(\Salla\Core\Enum\StoreEntityEnum::toArray())],

                'from_creating_date' => 'nullable|sometimes|date',
                'to_creating_date'   => 'nullable|sometimes|date|after_or_equal:from_creating_date',
                'min_orders'         => [
                    'nullable', 'sometimes', 'numeric', 'min:0',
                    function ($attr, $val, $fail) {
                        $max_orders = $this->get('max_orders');
                        if ($max_orders && $val > $max_orders) {
                            $fail(trans('marketplace::salla_product_discount.errors.min_orders_must_be_less_or_equal_to_max_number'));
                        }
                    }],
                'max_orders'         => [
                    'nullable', 'sometimes', 'numeric', 'min:1',
                    function ($attr, $val, $fail) {
                        $min_orders = $this->get('min_orders');
                        if ($min_orders && $val < $min_orders) {
                            $fail(trans('marketplace::salla_product_discount.errors.max_orders_must_be_bigger_then_or_equal_to_min_orders'));
                        }
                    }],

                'min_subscriptions' => [
                    'nullable', 'sometimes', 'numeric', 'min:0',
                    function ($attr, $val, $fail) {
                        $max_subscriptions = $this->get('max_subscriptions');
                        if ($max_subscriptions && $val > $max_subscriptions) {
                            $fail(trans('marketplace::salla_coupon.errors.min_subscriptions_must_be_less_or_equal_to_max_number'));
                        }
                    }],
                'max_subscriptions' => [
                    'nullable', 'sometimes', 'numeric', 'min:1',
                    function ($attr, $val, $fail) {
                        $min_subscriptions = $this->get('min_subscriptions');
                        if ($min_subscriptions && $val < $min_subscriptions) {
                            $fail(trans('marketplace::salla_coupon.errors.max_subscriptions_must_be_bigger_then_or_equal_to_min_subscriptions'));
                        }
                    }],

                'stores_ids_file' => [
                    'nullable', 'required_if:condition_type,stores_ids_file', 'file', 'max:40480',
                    function ($attr, $val, $fail) {
                        $result = array($this->file->getClientOriginalExtension());
                        if (!in_array($result[0], ["csv", "xls", "xlsx"])) {
                            $fail(trans('marketplace::salla_product_discount.errors.file_extension_must_be_in_xls_xlsx_csv'));
                        }
                    }],
                'subscription_status_option' => [
                    function ($attr, $val, $fail) {
                        if($this->get('subscription_status','expired') && !$this->get('subscription_status_option',null)){
                            $fail(trans('marketplace::salla_coupon.errors.expiration_date_is_not_defined'));
                        }
                    }],
                'visible' => ['nullable', 'boolean'],
            ]);
        }


        return $rules;
    }

    /**
     *
     */
    protected function prepareForValidation()
    {
        $this->coupon = $this->get('coupon');

        if ($this->get('stores')) {
            $stores = [];
            foreach ($this->get('stores') as $store) {
                if ((int)$store) {
                    $stores[] = optimus()->decode($store);
                }
            }

            $this->set('stores', $stores);
        }

        if ($this->get('apps')) {
            $this->set('apps',
                SallaProductMarketplaceApp::whereIn('app_id', $this->get('apps'))
                    ->where('developer_id', $this->get('developer_id'))
                    ->get()
                    ->pluck('id')
                    ->toArray()
            );
        }
    }


    /**
     * @return SallaCoupons
     */
    public function handle()
    {
        $data = [
            'type_coupon'          => $this->getType(),
            'portal_developer_id'  => !isNasa() ? $this->get('developer_id') : null,
            'code'                 => $this->get('code'),
            'type'                 => $this->get('type'),
            'amount'               => $this->get('amount'),
            'minimum_amount'       => $this->get('minimum_amount') ?: 0,
            'maximum_amount'       => $this->get('maximum_amount', null),
            'expiry_date'          => $this->get('expiry_date', null),
            'usage_limit'          => $this->get('usage_limit', null),
            'usage_limit_per_user' => $this->get('usage_limit_per_user', null),
            'status'               => $this->get('status') ? $this->get('status') : SallaCouponStatus::ACTIVE,
            'store_id'             => $this->get('store_id', null),
            'apps'                 => $this->getApps(),
            'include_product_ids'  => $this->getProductIds(),
        ];

        //when called from nasa we must set the required fields
        if (isNasa()) {
            $data = array_merge($data, [
                'free_shipping'         => !in_array($this->get('free_shipping'), ['', '-', null]) ? $this->get('free_shipping') : 0,
                'exclude_sale_products' => $this->get('exclude_sale_products') > 0 ?: 0,
                'only_new_subscription' => $this->get('only_new_subscription') > 0 ?: 0,
                'exclude_product_ids'   => $this->get('exclude_product_ids', []),
                'created_by'            => auth()->user()->getKey(),
                'visible'               => $this->get('visible', false),
            ]);

            $extraData = [
                //extra conditions
                'plan_types'            => $this->getPlanType(),
                'periods'               => $this->getPeriods(),
                'store_entities'        => $this->get('store_entity', null),
                'store_activities'      => $this->get('store_activities', null),
                'min_orders'            => $this->get('min_orders', null),
                'max_orders'            => $this->get('max_orders', null),
                'min_subscriptions'     => $this->get('min_subscriptions', null),
                'max_subscriptions'     => $this->get('max_subscriptions', null),
                'min_creation_date'     => $this->get('from_creating_date', null),
                'max_creation_date'     => $this->get('to_creating_date', null),
                'subscription_statuses' => $this->getSubscriptionStatuses(),
                'extra'                 => $this->getExtraData()
            ];


            if ($this->get('condition_type', '') === SallaCouponConditionTypes::STORES_IDS_FILE) {
                $extraData = array_fill_keys(array_keys($extraData), null);
            }

            $data = array_merge($data, $extraData);
        }

        if ($this->coupon) {
            $this->coupon = SallaCoupons::updateOrCreate([
                'id' => $this->coupon->id,
            ], $data);
        } else {
            $this->coupon = SallaCoupons::create($data);
        }

        Logger::message('debug', 'marketplace::store-coupon-action', [
            'receive_data' => $this->all(),
            'save_data'    => $data,
        ]);

        // if from nasa and condition_type equal to conditions
        if (
            isNasa() &&
            $this->get('condition_type', '') === SallaCouponConditionTypes::CONDITIONS
        ) {
            $this->deleteSallaCouponStores();
        }

        // After create Or Update coupon data we want  to check if file uploaded Or stores activities
        // will store the [activities, stores] in a separated table if not empty
        $this->createOrUpdateSallaCouponSores();

        return $this->coupon->fresh(['couponStores']);
    }


    private function createOrUpdateSallaCouponSores()
    {
        // when not from nasa (developer portal it maybe set stores as array)
        if (!isNasa() && $this->get('stores')) {
            return $this->syncStoresIds($this->getStores());
        }


        if (
            isNasa()
            && is_file($this->file)
            && $this->get('condition_type') === SallaCouponConditionTypes::STORES_IDS_FILE
        ) {
            [$storesIds, $url] = ExtractStoresIdsFromFileSheet::make()
                ->set('file', $this->file)->run();

            return $this->syncStoresIds($storesIds, $url);
        }

        return null;
    }

    /**
     *
     */
    private function deleteSallaCouponStores(): void
    {
        if ($this->coupon->file_path) {
            $this->coupon->file_path = null;
            $this->coupon->save();
        }

        SallaCouponStores::where('coupon_id', $this->coupon->id)->safeDelete();
    }


    /**
     * @return array|\ArrayAccess|mixed|null
     */
    private function getStores()
    {
        $stores = $this->get('stores', null);
        if (empty($stores)) {
            return null;
        }

        return $stores;
    }

    /**
     * @return null
     */
    private function getApps()
    {
        $apps = $this->get('apps', null);
        if (empty($apps)) {
            return null;
        }

        return $apps;
    }

    private function getProductIds()
    {
        /**
         * if call this action from nasa
         */
        if (!empty($this->get('include_product_ids', []))) {
            return $this->get('include_product_ids', []);
        }

        /**
         * if send data from external system like affiliate
         * it will send data like application ids will map it to product ids of dashboard
         * it will send data like themes ids will map it to product ids of dashboard
         * it will send data like services ids will map it to product ids of dashboard
         * [[
         *   'id' => product id,
         *   'plans' => array of id of plans,
         * ]]
         */
        if (empty($this->get('external_products', []))) {
            return [];
        }

        return GetProductsOfExternalProductAction::make([
            'external_products' => $this->get('external_products'),
        ])->run();
    }

    public function attributes(): array
    {
        return [
            'from_creating_date' => 'تاريخ انشاء المتجر الأدنى',
            'to_creating_date'   => 'تاريخ انشاء المتجر الأكبر',
            'min_orders'         => 'عدد الطلبات - من',
            'max_orders'         => 'عدد الطلبات - الى',
            'min_subscriptions'  => 'عدد الاشتراكات - من',
            'max_subscriptions'  => 'عدد الاشتراكات - الى',
        ];
    }

    public function messages(): array
    {
        return [
            'stores_ids_file.required_if' => 'حقل الملف مطلوب عند اختيار الشروط من نوع ملف',
            '*.after_or_equal'            => 'حقل الى تاريخ يجب أن يكون أكبر او يساوي التاريخ الادنى',
            'stores_ids_file.file'        => 'نوع المف يجب أن يكون ملف اكسل او csv',
            'stores_ids_file.max'         => 'يجب الا يتعدى حجم الملف :max',
            '*.gte'                       => 'يجب أن يكون عدد الطلبات الأدنى أقل أو يساوي عدد الطلبات الأعلى',
            'code.regex'                  => trans('marketplace::salla_coupon.dashboard.messages.error.regex'),
        ];
    }

    /**
     * @param $storeIds
     * @return mixed
     */
    private function syncStoresIds($storeIds, $url = null)
    {
        //delete the old if found
        $this->deleteSallaCouponStores();

        if ($url) {
            $this->coupon->file_path = $url;
            $this->coupon->save();
        }

        $couponStores = [];
        foreach ($storeIds as $id) {
            $couponStores[] = [
                'coupon_id' => $this->coupon->id,
                'store_id'  => $id
            ];
        }

        return SallaCouponStores::insert($couponStores);
    }


    private function getSubscriptionStatuses(): array
    {
        $values = [];

        foreach ((array)$this->get('subscription_status', []) as $status) {
            $options = (array)$this->get('subscription_status_option', []);

            if (!empty($options) && isset(SallaCouponSubscriptionStatusOptions::$OPTIONS[$status])) {
                foreach ($options as $option) {
                    $values[] = $status . '|' . $option;
                }
            } else {
                $values[] = $status;
            }
        }

        return $values;
    }

    private function getPreviousPlans()
    {
        return $this->get('subscription_status') == 'expired' ? $this->get('previous_plans', null): null;
    }

    private function getExtraData()
    {
        $extra = $this->coupon?->extra;
        $this->addPreviousPlansToExtra($extra);
        $this->addStoreAgeToExtra($extra);

        return $extra;
    }

    private function addPreviousPlansToExtra(?array &$extra): void
    {
        $previousPlans = $this->getPreviousPlans();
        if ($previousPlans) {
            $extra['previous_plans'] = $previousPlans;
        }
    }

    private function addStoreAgeToExtra(?array &$extra): void
    {
        $storeAgeConditions = $this->get('store_age_conditions');
        $storeAgeDays = $this->get('store_age_days');

        if ($this->isValidStoreAgeData($storeAgeConditions, $storeAgeDays)) {
            $extra['store_age'] = [
                'conditions' => $storeAgeConditions,
                'days' => $storeAgeDays
            ];
        }
    }

    private function isValidStoreAgeData(string $conditions, $days): bool
    {
        return in_array($conditions, ['=', '<', '>', '<=', '>=']) && is_numeric($days);
    }

    private function getPlanType()
    {
        return $this->get('subscription_status') == 'grace_period' ? $this->get('plan_type', null): null;
    }

    private function getPeriods()
    {
        return $this->get('subscription_status') == 'grace_period' ? $this->get('periods', null): null;
    }

    /**
     * @return string|null
     */
    private function getType()
    {
        if(isNasa()) {
            return null;
        }

        return Str::startsWith($this->get('code'), SallaCouponCodeEnum::PARTNER_MARKETING) ?
            SallaCouponType::PARTNER_AFFILIATE :
            SallaCouponType::PARTNER;
    }
}
