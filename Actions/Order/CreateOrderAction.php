<?php

namespace Modules\MarketPlace\Actions\Order;

use App\Models\User;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderGiftCardAction;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderInstallmentAction;
use Modules\MarketPlace\Contract\SallaCartService;
use Modules\MarketPlace\Notifications\Installment\BatchSuccessPaidNotification;
use Salla\Core\Base\Action;
use Salla\Logger\Facades\Logger;
use Salla\Paymetns\Models\Invoice;
use Salla\Events\Salla\OrderPlaced;
use Modules\Store\Enum\StorePattern;
use Salla\Paymetns\Enum\PaymentMethods;
use Salla\Paymetns\Traits\HasRunWithLock;
use Modules\MarketPlace\Entities\SallaCart;
use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Actions\Actions\HandleProductBundledItems;
use Modules\MarketPlace\Entities\SallaOrders;
use Salla\Paymetns\Exception\PaymentException;
use Modules\MarketPlace\Entities\SallaInvoice;
use Modules\MarketPlace\Enum\SallaOrderStatus;
use Salla\Core\Traits\Actions\WithTransaction;
use Modules\MarketPlace\Enum\SallaOrderSource;
use Modules\MarketPlace\Enum\PaymentMethodType;
use Modules\MarketPlace\Enum\SallaInvoiceStatus;
use Modules\MarketPlace\Presenter\TotalPresenter;
use Modules\MarketPlace\Entities\SallaOrderPaymentMethod;
use Modules\MarketPlace\Presenter\CartPayment\PaymentPresenter;
use Modules\MarketPlace\Actions\Store\SetAppsForTransferStoreAction;
use Modules\MarketPlace\Actions\Store\CreateStoreFromTemplateAction;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderItemAction;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderCreditAction;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderCouponAction;
use Modules\MarketPlace\Actions\Order\Partials\CreatePlanHistoryAction;
use Modules\MarketPlace\Actions\Order\Partials\ReturnFreeProductAction;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderItemActionsAction;
use Modules\MarketPlace\Actions\Order\Partials\CreateOrderLoyaltyPointAction;
use Modules\MarketPlace\Notifications\ToSalla\OrderNeedConfirmationNotification;
use Modules\MarketPlace\Actions\Order\Partials\CreatePriceDiscountHistoryAction;
use Modules\MarketPlace\Notifications\Invoice\AffiliateInvoiceSuccessPaidNotification;
use Modules\MarketPlace\Notifications\Invoice\SallaInvoiceSuccessPaidNotification;
use Modules\Store\Actions\StoreTemplate\TransferStore\CompleteTransferStoreAction;
use Modules\MarketPlace\Enum\SallaInvoiceSource;

/**
 * Class CreateOrderAction.
 *
 * @property TotalPresenter total
 * @property Invoice invoice
 * @property PaymentPresenter payment
 * @property SallaOrders order
 * @property SallaCart cart
 * @property SallaCartService reference
 * @method self setTotal(TotalPresenter $presenter)
 * @method self setInvoice(Invoice $invoice)
 * @method self setCart(SallaCart $cart)
 * @method self setPayment(PaymentPresenter $presenter)
 * @method self setReference(SallaCartService $reference)
 * @method SallaOrders run($parameters = [])
 */
class CreateOrderAction extends Action
{
    use HasRunWithLock, WithTransaction;

    public function handle()
    {
        $this->delegateTo(CreateStoreFromTemplateAction::class);

        /**
         * @var User $user
         */
        $user = $this->getUser();

        $payment_method = $this->payment->getPaymentMethod() ?: $this->payment->getOrder()->getPaymentMethod();

        if ($payment_method === 'free') {
            $payment_method = 'wallet';
        }

        $this->debug(function () {
            return $this->total->toArray();
        });

        /**
         * set Price discount per item
         */
        $items = $this->setPriceDiscountPerItem();

        $this->saveLog('Start Create Order');

        $isInstallment = $this->reference && $this->reference->isInstallmentPayment();
        /*
         * @var SallaOrders $order
         */
        $this->order = SallaOrders::query()->create([
            'city'                    => $this->invoice->store->store_city,
            'country'                 => $this->invoice->store->store_country,
            'geocode'                 => $this->invoice->store->store_location,
            'user_id'                 => $user->id ?? auth()->id(),
            'store_id'                => $this->invoice->store_id,
            'amount'                  => $this->total->getSubTotal(),
            'tax'                     => $this->total->getTax(),
            'tax_value'               => $this->total->getTaxAmount(),
            'total_discounted_amount' => $this->total->getTotalDiscount(),
            // the credit is a cash so its a part of total
            'total'                   => $this->total->getSubTotal() - $this->total->getTotalDiscount() + $this->total->getTaxAmount(), // this is not the final total, it can be change in insied the actions
            'status'                  => \in_array($this->payment->getPaymentMethod(), PaymentMethodType::getPaymentAcceptDirect(), true) ? SallaOrderStatus::PAID : $this->payment->getOrder()->getOrderStatus(),
            'payment_method'          => $payment_method,
            'epayment_id'             => $this->invoice->transaction_reference ?? null,
            'epayment_fee'            => $this->invoice->fee ?? 0,
            'epayment_gateway'        => $this->invoice->gateway->slug ?? null,
            'epayment_status'         => $this->invoice->status ?? null,
            'source'                  => $this->getSource($this->invoice),
            'created_by'              => auth()->id(),
            'coupon_id'               => $this->total->getCoupon()->isSuccess() ? $this->total->getCoupon()->getCoupon()->getKey() : null,
            // save bank information
            'store_bank_id'           => $this->payment->getOrder()->getPaymentMethod() === PaymentMethods::BANK ? $this->payment->getOrder()->getBank()->getId() : 0,
            'receipt_image_path'      => $this->payment->getOrder()->getPaymentMethod() === PaymentMethods::BANK ? $this->payment->getOrder()->getBank()->getReceiptPath() : '',
            'is_installment'          => $isInstallment,
            'is_test'                 => (store()->is_test || store()->pattern != StorePattern::NORMAL),
        ]);


        $this->set('order', $this->order);
        $this->set('source', $this->source);

        $this->saveLog('Created Order');

        $this->capture();

        $this->order->paymentMethods()->create([
            'order_id'       => $this->order->getKey(),
            'type'           => SallaOrderPaymentMethod::TYPE_INVOICE,
            'reference_type' => SallaOrderPaymentMethod::REFERENCE_INVOICE,
            'reference_id'   => $this->invoice->getKey(),
            'amount'         => $this->total->getAmountInBaseCurrency(),
        ]);

        $this->markReferenceAsPurchased();

        CreateOrderItemAction::make()->setTotal($this->total)->setOrder($this->order)->run();

        CreateOrderInstallmentAction::make()->setOrder($this->order)->setReference($this->reference)->run();

        CreateOrderCouponAction::make()->setTotal($this->total)->setOrder($this->order)->run();

        CreateOrderGiftCardAction::make()->setTotal($this->total)->setOrder($this->order)->run();

        CreatePriceDiscountHistoryAction::make()->setOrder($this->order)->setItems($items)->run();

        CreateOrderLoyaltyPointAction::make()->setTotal($this->total)->setOrder($this->order)->run();

        CreatePlanHistoryAction::make()->setTotal($this->total)->setOrder($this->order)->run();

        CreateOrderCreditAction::make()->setTotal($this->total)->setOrder($this->order)->run();

        $createOrderItemAction = new CreateOrderItemActionsAction();
        $createOrderItemAction->setOrder($this->order)->setSource($this->source)->run();

        if ($createOrderItemAction->getErrorMessage()) {
            return PaymentPresenter::error($createOrderItemAction->getErrorMessage());
        }

        HandleProductBundledItems::make()->setOrder($this->order)->run();

         if ($this->order->installment) {
             $this->order->store->owner->notify(new BatchSuccessPaidNotification($this->order->installment->sallaInvoices()->orderBy('due_date')->first()));
         }

        $this->saveLog('Run Actions Related to Order Items');

        ReturnFreeProductAction::make()->setTotal($this->total)->run();

        $this->saveLog('Run Actions Related to Free Order Items');

        $this->debug(function () {
            return $this->total->toArray();
        });

        $this->sendEmailForOrderNeedConfirmation();

        // Clear Coupon User After Used
        if (!empty($user->coupon_at_registration)) {
            $user->update([
                'coupon_at_registration' => null,
            ]);
        }

        event(new OrderPlaced($this->order));

        SetAppsForTransferStoreAction::make([
            'order' => $this->order,
        ])->run();

        CompleteTransferStoreAction::make([
            'order' => $this->order,
        ])->run();

        $this->saveLog('Finish Create Salla Order with total ' . $this->order->total);

        $result = tap(PaymentPresenter::success(), function ($presenter) {
            $presenter->getOrder()->setOrderModel($this->order);
        });

        $this->saveLog('Finish Create Salla Order #' . $this->order->id);

        return $result;
    }

    private function getUser()
    {
        if ($this->invoice->reference instanceof SallaCart) {
            return $this->invoice->reference->user;
        }
        if ($this->invoice->reference instanceof SallaInvoice) {
            return $this->invoice->reference->store->owner;
        }

        return auth()->user();
    }

    /**
     * Determine the source of the order based on the invoice reference.
     *
     * This helps identify if the order was:
     * - Automatically paid and linked to an affiliate invoice.
     * - Manually paid from the dashboard but still linked to an affiliate.
     * - Or a regular system/dashboard order.
     */
    private function getSource(Invoice $invoice = null)
    {
        // If the invoice is linked to a SallaInvoice
        if ($invoice?->reference instanceof SallaInvoice) {
            return $invoice->reference->source === SallaInvoiceSource::AFFILIATE
                ? SallaOrderSource::AFFILIATE  // Affiliate payout
                : SallaOrderSource::SYSTEM;    // Regular system order
        }

        // If the invoice is linked to a SallaCart, and the related SallaInvoice is for affiliate
        if (
            $invoice?->reference instanceof SallaCart
            && $invoice->reference->sallaInvoice?->source === SallaInvoiceSource::AFFILIATE
        ) {
            return SallaOrderSource::AFFILIATE; // Manual affiliate invoice order via cart
        }

        // Default case: manual order from dashboard
        return SallaOrderSource::DASHBOARD;
    }

    /**
     * Try to capture and check the amount before commit db transactions.
     *
     * @throws \Salla\Paymetns\Exception\PaymentException
     */
    protected function capture(): void
    {
        /** @var \Omnipay\Common\Message\AbstractResponse $response */
        $response = app('payment')->fromInvoice($this->invoice)->capture($this->invoice->parameters)->send();

        if (abs($this->total->getAmountInBaseCurrency() - $response->getAmount()) > 0.5) {
            throw new PaymentException('اجمالي المنتجات في السلة غير متطابق مع المبلغ المدفوع');
        }

        if (!$response->isSuccessful()) {
            throw new PaymentException($response->getMessage());
        }

        $this->invoice->hasBeenConfirmed($this->order);
    }

    private function sendEmailForOrderNeedConfirmation()
    {
        if ($this->order->payment_method !== 'bank') {
            return;
        }

        if (!app('store')->isDemoStore()) {
            Notification::route('mail', '<EMAIL>')
                ->notify(new OrderNeedConfirmationNotification($this->order));
        }
    }

    protected function markReferenceAsPurchased()
    {
        $data = [
            'status'   => SallaInvoiceStatus::PAID,
            'order_id' => $this->order->getKey(),
        ];

        if ($this->invoice->reference instanceof SallaCart || $this->invoice->reference instanceof \App\Models\SallaCart) {
            $this->invoice->reference->update([
                'status'   => 'purchased',
                'order_id' => $this->order->getKey(),
            ]);

            // if the invoice paid manually
            $invoice = SallaInvoice::query()
                ->firstWhere('cart_id', $this->invoice->reference->getKey());
            $invoice && $invoice->update($data);

            return;
        }

        if ($this->invoice->reference instanceof SallaInvoice) {
            $this->invoice->reference->update($data);

            if($this->invoice->reference->source === SallaInvoiceSource::AFFILIATE) {
                $this->order->store?->owner?->notify(new AffiliateInvoiceSuccessPaidNotification(
                    $this->order,
                    $this->invoice->reference
                ));
                return;
            }

            Notification::route('mail', $this->order->store?->owner?->email)
                ->notify(new SallaInvoiceSuccessPaidNotification($this->invoice->reference));
        }
    }

    /**
     * some time store condition change so make sure that item has right discount
     * @return \Illuminate\Support\Collection|\Modules\MarketPlace\Entities\SallaCartItem[]
     */
    private function setPriceDiscountPerItem()
    {
        $items = collect();
        foreach ($this->total->getItems() as $item) {
            $item->discount_price = null;

            if (!$item->getProduct()->isSupportDiscountedPrice()) {
                $items->push($item);
                continue;
            }

            $discountPrice = $item->getProductPrice()->getConditionedPriceDiscount();
            if (!empty($discountPrice)) {
                $item->discount_price = $discountPrice;
            }

            $items->push($item);
        }

        return $items;
    }

    protected function getLockKey(): string
    {
        return 'create-salla-order-' . $this->invoice->getKey();
    }

    /**
     * run action after finish transaction
     */
    public function actionAfterTransaction(): void
    {
        $events = cache()->pull("salla_order_event_after_action_{$this->order->store_id}", []);
        if (empty($events)) {
            return;
        }

        Logger::message('debug', 'salla-order-after-transaction', [
            'store_id' => $this->order->store_id,
            'order_id' => $this->order->id,
            'events'   => json_encode($events),
        ]);

        foreach ($events as $event) {
            event(unserialize($event));
        }
    }

    /**
     * @param $message
     */
    private function saveLog($message)
    {
        Logger::message('debug', 'salla-order-create', [
            'store_id'       => $this->invoice->store->id,
            'invoice_id'     => $this->invoice->id,
            'invoice_type'   => (($this->invoice->reference instanceof SallaInvoice) ? 'invoice' : 'cart'),
            'order_id'       => optional($this->order)->getKey(),
            'total'          => optional($this->total)->getTotal(),
            'system_message' => $message,
        ]);
    }
}
