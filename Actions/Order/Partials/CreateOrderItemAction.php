<?php

namespace Modules\MarketPlace\Actions\Order\Partials;

use App\Models\CartItem;
use Modules\MarketPlace\Entities\SallaCartItemFeature;
use Modules\MarketPlace\Entities\SallaOrderItemFeature;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Entities\SallaOrders;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Presenter\TotalPresenter;
use Salla\Core\Base\Action;
use Salla\Events\Salla\Product\ProductPurchased;

/**
 * Class CreateOrderItemAction
 *
 * @package Modules\MarketPlace\Actions\Order\Partials
 * @method self setTotal(TotalPresenter $total)
 * @method self setOrder(SallaOrders $order)
 * @property TotalPresenter total
 * @property SallaOrders order
 */
class CreateOrderItemAction extends Action
{
    public function handle()
    {
        /** @var CartItem $item */
        foreach ($this->total->getItems() as $item) { //-- cart items
            /** @var \Salla\Money\Data\MoneyTaxable $tax_amount */
            $tax_amount = $this->total->getTaxPerItems()->get($item->getKey(), 0);
            $tax_amount = $tax_amount ? $tax_amount->getTaxAsFloat() : 0;

            $product_discount_item = $this->total->getDiscountPerItems()->get($item->getKey(), 0);
            $product_credit_item = $this->total->getCredit()->getCreditPerItems()->get($item->getKey(), 0);
            $total = $item->getPrice()->getBasePriceAsFloat();

            /** @var SallaOrderItems $orderItem */
            $orderItem = $this->order->orderItems()->create([
                'product_id'        => $item->getProduct()->getKey(),
                'product_price'     => $item->getPrice()->getBasePriceAsFloat() / $item->getQuantity(),
                'quantity'          => $item->getQuantity(),
                'product_discount'  => $product_discount_item,
                'coupon_discount'   => $this->total->getCoupon()->getDiscountPerItems()->get($item->getKey(), 0),
                'loyalty_discount'  => $this->total->getLoyaltyPoint()->getDiscountPerItems()->get($item->getKey(), 0),
                'credit_amount'     => $product_credit_item,
                'tax'               => $this->total->getTax(),
                'tax_value'         => $tax_amount,
                'total'             => $total + $tax_amount - $product_discount_item,
                'period'            => $item->getPeriod(),
                'product_price_id'  => $item->getProductPriceId(),
                'value'             => optional($item->subscription)->type === SallaProductType::DOMAIN ? optional($item->subscription)->type_value : $item->value,
                'feature_price'     => $item->getTotalPriceFeatures(), //this field included in product_price column
                'first_time_cost'   => $item->getFirstTimeCost(), //this field included in product_price column
                'refund_amount'     => $this->getRefundAmount($item),
                'developer_user_id' => $this->setDeveloperPartner($item),
                'domain'            => optional($item->subscription)->type === SallaProductType::DOMAIN ? optional($item->subscription)->type_value : null,
                'promotion_id'      => $item->getPromotionId(),
            ]);

            event(new ProductPurchased($orderItem->product, $orderItem->total));

            //Free Product
            foreach ($item->getFreeProducts() as $free_product) {
                $orderItem->freeProducts()->create([
                    'order_id'         => $this->order->id,
                    'free_id'          => $free_product->free_id,
                    'product_id'       => $free_product->product_id,
                    'product_price_id' => $free_product->product_price_id,
                ]);

                if ($free_product && $free_product->product) {
                    event(new ProductPurchased($free_product->product));
                }
            }

            //Features
            /** @var SallaCartItemFeature $feature */
            foreach ($item->getPriceFeatures() as $feature) {
                if(empty($feature->quantity)) {
                    continue;
                }

                SallaOrderItemFeature::create([
                    'order_id'      => $orderItem->order_id,
                    'order_item_id' => $orderItem->id,
                    'feature_id'    => $feature->feature_id,
                    'slug'          => !empty($feature->slug) ? $feature->slug : ($feature->feature->slug ?: null),
                    'price'         => $feature->price,
                    'quantity'      => $feature->quantity,
                    'total'         => ($feature->price * $feature->quantity),
                ]);
            }

        }//-- end foreach
    }

    /**
     * @param CartItem $item
     * @return int
     */
    private function getRefundAmount($item)
    {
        if (!$item->getProduct()->isApp()) {
            return 0;
        }

        $changePlanPresenter = $this->total->getRemainingAmountForCurrentPlan();
        if (!$changePlanPresenter->isSuccess() || !$changePlanPresenter->getRemainingAmount()) {
            return;
        }

        return $changePlanPresenter->getRemainingAmount();
    }

    /**
     * @param CartItem $item
     * @return null
     */
    private function setDeveloperPartner($item)
    {
        /**
         * @var SallaProducts $product
         */
        $product = $item->getProduct();

        if ($product->isApp() && $product->app) {
            return $product->app->getDeveloperUserId();
        }

        if ($product->isExpertService() && $product->expretService) {
            return $product->expretService->external_partner_agency_id ?? $product->expretService->partner_company_id;
        }

        if ($product->isTheme() && $product->theme) {
            return $product->theme->developer_user_id;
        }

        return null;
    }

}
