<?php

namespace Modules\MarketPlace\Actions\Subscription;


use Modules\MarketPlace\Entities\SallaInstallmentCreditNote;
use Modules\MarketPlace\Enum\SallaInstallmentStatus;
use Salla\Core\Base\Action;
use Modules\MarketPlace\Entities\SallaInstallment;
use Salla\Paymetns\Models\Invoice;
use Illuminate\Support\Collection;

/**
 * Class ExpiredInstallmentSubscriptionAction
 *
 * @package Modules\MarketPlace\Actions\Subscription
 * @property SallaInstallment installment
 */
class ExpiredInstallmentSubscriptionAction extends Action
{

    public function rules()
    {
        return [
            'installment' => [
                'required',
            ],
        ];
    }

    public function handle()
    {
        $paidInstallments = $this->installment->paidPayments()->orderBy('due_date');

        $totalRefundAmount = $this->calculateRefund($paidInstallments);

        if (!$totalRefundAmount) {
            return;
        }

        // update subscription
        ExpiredSubscriptionBaseOnTypeAction::make(['subscription' => $this->installment->subscription])->run();

        // refunding
        // Make it refunded directly and send this amount to store credit
        store()->loadFromId($this->installment->store_id);

        $this->refundInvoices($totalRefundAmount);

        // update installment
        $this->installment->update(['status' => SallaInstallmentStatus::EXPIRED]);

    }

    private function calculateRefund($paidInstallments)
    {
        $paidInstallments = $paidInstallments->count(); // paid batches count

        $installmentsCount = $this->installment->sallaInvoices()->count(); //total batches count

        if ($paidInstallments == $installmentsCount) {
            return 0;
        }

        $subscription = $this->installment->subscription;
        $totalPrice = $this->installment->sallaInvoices()->sum('total');
        $monthPrice = $totalPrice / $subscription->period;
        $installmentPrice = $totalPrice / $installmentsCount;

        return round(($paidInstallments * $installmentPrice) - ($paidInstallments * $monthPrice), 2);

    }

    private function refundInvoices($totalRefundAmount)
    {
        $invoices = Invoice::where('target_type', $this->installment->order->getMorphClass())
            ->where('target_id', $this->installment->order_id)
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($invoices as $invoice) {
            if($totalRefundAmount <= 0) {
                break;
            }

            $invoiceAmount = $this->getInvoiceAmount($invoice);

            $refundAmount = min($totalRefundAmount, $invoiceAmount);
            $this->refund($refundAmount, $invoice->id);
            $totalRefundAmount = $totalRefundAmount - $refundAmount;
        }
    }

    private function refund($refundAmount, $invoiceId)
    {
        $transaction = store()->depositFloat($refundAmount, [], true);

        $creditNote = SallaInstallmentCreditNote::query()->create([
            'store_id'         => $this->installment->store_id,
            'installment_id'   => $this->installment->id,
            'transaction_id'   => $transaction->getKey(),
            'invoice_id'       => $invoiceId,
        ]);

        $transaction->setSource($creditNote);
    }

    private function getInvoiceAmount($invoice)
    {
        $invoiceAmount = $invoice->amount;

        // in case of auto payment with wallet balance method the invoice will not hold the amount and amount will be 0
        if ($invoiceAmount == 0 && $invoice->reference_type == $this->installment->sallaInvoices()->first()->getMorphClass()) {
            $invoiceAmount = $this->installment->sallaInvoices()->where('id', $invoice->reference_id)->first()->total;
        }

        return $invoiceAmount;
    }
}
