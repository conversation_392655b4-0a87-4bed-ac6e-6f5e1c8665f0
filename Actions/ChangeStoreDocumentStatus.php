<?php

namespace Modules\Settings\Actions;

use Salla\Core\Base\Action;

class ChangeStoreDocumentStatus extends Action
{
    public function handle()
    {
        if(! store()->getKey()){
            return;
        }

        $document_status = $this->getBypassCheckMainAccount() ? 3 : store()->getStoreDocumentStatus();

        // reset verification logs for iban
        store()->verificationLogs()->where('type', 'iban')->delete();

        if ($document_status === store()->document_status) {
            return;
        }

        store()->update(['document_status' => $document_status]);

        clearStoreCache();
    }

    public function getBypassCheckMainAccount()
    {
       return  $this->attributes['bypassCheckMainAccount'] ?? false;
    }

}
