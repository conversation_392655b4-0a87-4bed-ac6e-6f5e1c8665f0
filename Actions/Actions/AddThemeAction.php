<?php

namespace Modules\MarketPlace\Actions\Actions;

use Modules\MarketPlace\Actions\Subscription\CreateSubscriptionAction;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Entities\SallaOrders;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionType;
use Modules\ThemeCustomization\Actions\Versions\CreateVersionAction;
use Modules\ThemeCustomization\Entities\ThemeCustomization;

/**
 * Class AddThemeAction
 *
 * @package Modules\MarketPlace\Actions\Actions
 * @property SallaProducts   product
 * @property SallaOrders     order
 * @property SallaOrderItems order_item
 */
class AddThemeAction extends BaseProductAction
{
    protected $productType = SallaProductType::THEME;

    public function handle()
    {
        $salla_subscription = app(SallaSubscriptions::class)->checkTheme($this->order->store, $this->product);
        if (! empty($salla_subscription)) {
            $this->debug('The theme already exists');

            return null;
        }
 
        $subscription = CreateSubscriptionAction::make([
            'order'      => $this->order,
            'order_item' => $this->order_item,
            'period'     => null,
            'start_date' => null,
            'end_date'   => null,
            'type_value' => null,
            'subscription_type' => SallaSubscriptionType::ONCE,
        ])->run();

        if ($subscription && $this->doesntHaveVersion()) {
            CreateVersionAction::make()->setThemeId($this->product->type_value)->run();
        }

        return $subscription;
    }

    private function doesntHaveVersion(): bool
    {
        return ThemeCustomization::query()
            ->where('theme', $this->product->type_value)
            ->where('store_id', $this->order->store_id)
            ->count() === 0;
    }
}
