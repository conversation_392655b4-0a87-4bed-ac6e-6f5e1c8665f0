<?php


namespace Modules\MarketPlace\Actions\MarketplaceApp\Privilage;


use Carbon\Carbon;
use Salla\Core\Base\Action;
use Salla\Logger\Facades\Logger;
use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Enum\SallaSubscriptionType;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Traits\MarketPlaceApp\HaveInstallApp;
use Modules\MarketPlace\Actions\MarketplaceApp\AddAppActions;
use Modules\MarketPlace\Entities\MarketPlaceAppAccessRequest;
use Modules\MarketPlace\Enum\MarketPlaceAppAccessRequestStatus;
use Modules\MarketPlace\Enum\SubscriptionWebhookPortalEventType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppPlanType;
use Modules\MarketPlace\Actions\MarketplaceApp\InstallSallaAppAction;
use Modules\MarketPlace\Actions\Subscription\CreateSubscriptionAction;
use Modules\MarketPlace\Notifications\MarketPlace\MarketPlaceAppRequestAccessNotification;
use Modules\MarketPlace\Notifications\MarketPlace\MarketPlaceAppRequestAccessBaseNotification;
use Modules\MarketPlace\Notifications\MarketPlace\MarketPlaceAppRequestAccessAcceptNotification;

class MarketPlaceAppAcceptRequestAction extends Action
{

    use HaveInstallApp;

    /**
     * @var MarketPlaceAppAccessRequest $app_access_request
     */
    private $app_access_request;

    /**
     * @return array
     */
    public function rules()
    {
        return [
            'app' => [
                'required',
//                function ($attribute, $value, $fail) {
//                    /**
//                     * @var SallaProductMarketplaceApp $value
//                     */
//                    $this->app_access_request = MarketPlaceAppAccessRequest::where('store_id', store()->getId())
//                        ->where('app_id', $value->id)
//                        ->first();
//
//                    //if exist before
//                    if (empty($this->app_access_request)) {
//                        return $fail(trans('marketplace::marketplace_store_apps.dashboard.messages.error.no access request exist before'));
//                    }
//                },
            ],
        ];
    }

    /**
     *
     */
    public function handle()
    {
        /**
         * @var SallaProductMarketplaceApp $app
         */
        $app = $this->get('app');

        Logger::message('debug', 'marketplace::webhook-app-install', [
            'action_message' => sprintf(
                "App %s (%s) Install by webhook on store %s (%s)",
                $app->id,
                $app->app_id,
                store()->getId(),
                store()->getRouteKey()
            ),
        ]);

        //service or shipping
        $app_installed = $app->getInstalledApp();

        $this->app_access_request = MarketPlaceAppAccessRequest::where('store_id', store()->getId())
            ->where('app_id', $app->id)
            ->first();

        //check if app install before and have version
        //special state if access request maby request multi time so need to escape it
        if (
            !empty($app_installed) &&
            !empty($app->update_version) &&
            empty($this->app_access_request) &&
            ($app_installed->update_version == $app->update_version)
        ) {
            Logger::message('debug', 'marketplace::webhook-app-install-same-version', [
                'action_message' => sprintf(
                    "App %s (%s) Installed on store %s (%s)",
                    $app->id,
                    $app->app_id,
                    store()->getId(),
                    store()->getRouteKey()
                ),
            ]);
            
            return;
        }

        /**
         * @var SallaSubscriptions $subscription
         */
        $subscription = $app->getSubscription();

        //check if store not demo and is paid it must have subscription
        //and not updating
        if (
            (empty($app_installed)) &&
            !store()->isDemoStorePartner() &&
            !empty($app->plan_type) &&
            SallaProductMarketplaceAppPlanType::isPaidType($app->plan_type) &&
            empty($subscription)
        ) {
            $message = sprintf(
                "App %s (%s) Paid and not have subscription on store %s (%s)",
                $app->id,
                $app->app_id,
                store()->getId(),
                store()->getRouteKey()
            );

            Logger::message('debug', 'marketplace::webhook-app-install-not-have-subscritpion', [
                'action_message' => $message,
            ]);

            //throw new \Exception($message);

            return;
        }

        //Access Request
        if (!empty($this->app_access_request)) {
            //store service or shipping to db
            //special state if custom plan and subscription changed from plan to other plan we need to
            //send cancel subsbscription webhook to portal then start new one
            $installedApp = AddAppActions::make([
                'app' => $app,
                'event_type' => $subscription && $subscription->isAppChangeSubscription() ?
                    SubscriptionWebhookPortalEventType::CHANGED :
                    SubscriptionWebhookPortalEventType::START,
            ])->run();

            $this->app_access_request->update([
                'status'               => MarketPlaceAppAccessRequestStatus::ACCEPT,
                'response_status_date' => Carbon::now(),
                'installed_app_id'     => optional($installedApp)->id,
            ]);

            $this->updateSubscriptionData($subscription);

            //notification
            Notification::route('mail', store()->owner->email)
                ->notify(new MarketPlaceAppRequestAccessAcceptNotification($this->app_access_request));

            return;
        }

        //store service or shipping to db
        $installedApp = AddAppActions::make([
            'app' => $app,
        ])->run();

        $this->updateSubscriptionData($subscription);

        /**
         * if salla app need authorization and have feature
         * for add feature if application related to salla
         * but need to check if 
         */
        if ($app->is_salla_app) {
             InstallSallaAppAction::make([
                'app' => $app,
            ])->run();
        }
    }

    /**
     * check subscription status and start date
     * @param $installedApp
     */
    private function updateSubscriptionData($subscription)
    {
        if (empty($subscription) || ($subscription->status != SallaSubscriptionStatus::PENDING)) {
            return;
        }

        $data = [];
        $data['status'] = SallaSubscriptionStatus::ACTIVE;

        //check if subscription different date
        if (
            ($subscription->subscription_type == SallaSubscriptionType::RECURRING) &&
            (Carbon::parse($subscription->start_date)->lessThan(Carbon::now()))
        ) {
            $data['start_date'] = Carbon::now();
            $data['end_date'] = Carbon::now()->addDays($subscription->days);
        }

        $subscription->update($data);
    }
}
