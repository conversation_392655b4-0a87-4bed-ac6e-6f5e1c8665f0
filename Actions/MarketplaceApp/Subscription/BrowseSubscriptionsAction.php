<?php

namespace Modules\MarketPlace\Actions\MarketplaceApp\Subscription;

use Modules\MarketPlace\Repositories\Criteria\Subscription\WithoutFreeSubscriptionCriteria;
use Salla\Core\Base\Action;
use Illuminate\Validation\Rule;
use Salla\Core\Traits\HasParameters;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceType;
use Modules\MarketPlace\Repositories\Filters\SubscriptionFilter;
use Modules\MarketPlace\Repositories\SallaSubscriptionRepository;
use Modules\MarketPlace\Repositories\Criteria\Subscription\DateCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\StoreCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\PayoutCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\StatusCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\CouponCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\AppPlanCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\WithTrashedCriteria;
use Modules\MarketPlace\Repositories\Criteria\Subscription\WithoutDemoSubscriptionCriteria;

class BrowseSubscriptionsAction extends Action
{

    /**
     * @return array
     */
    public function rules()
    {
        return [
            'developer_company_id' => [
                'required',
                'numeric'
            ],
            'type'                 => [
                'nullable',
                Rule::in(SallaProductMarketplaceType::toArray()),
            ],
            'theme_id'             => [
                'nullable',
                'numeric',
            ],
            'app_id'               => [
                'nullable',
                'numeric',
            ],
            'plan'                 => [
                'nullable',
                'numeric',
            ],
            'start_date'           => [
                'nullable',
                'date',
                //'before:end_date',
            ],
            'end_date'             => [
                'nullable',
                'date',
                //'after:start_date',
            ],
            'status'               => [
                'nullable',
                'string',
                Rule::in(SallaSubscriptionStatus::toArray()),
            ],
            'store_id'             => [
                'nullable',
                'numeric',
            ],
            'payout_id'            => [
                'nullable',
                'numeric',
            ],
            'coupon_id'            => [
                'nullable',
                'numeric',
            ],
            'result_type'          => [
                'nullable',
                'string'
            ],
        ];
    }

    /**
     *
     */
    protected function prepareForValidation()
    {
        $store_id = $this->get('store_id');
        if (!empty($store_id)) {
            $this->set('store_id', optimus()->decode($store_id));
        }

        $coupon_id = $this->get('coupon_id');
        if (!empty($coupon_id)) {
            $this->set('coupon_id', optimus()->decode($coupon_id));
        }
    }

    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection|mixed|SallaSubscriptionRepository
     * @throws \Prettus\Repository\Exceptions\RepositoryException
     */
    public function handle()
    {
        $repository = app(SallaSubscriptionRepository::class);

        $repository->pushCriteria(new WithTrashedCriteria());
        $repository->pushCriteria(new WithoutDemoSubscriptionCriteria());

        $type = $this->get('type', SallaProductMarketplaceType::APP); //app, theme
        if ($type == SallaProductMarketplaceType::THEME) {
            //theme
            $repository->getThemes(
                $this->developer_company_id,
                $this->get('theme_id', null)
            );
        } else {
            //apps
            //$repository->pushCriteria(new WithoutFreeSubscriptionCriteria());
            $repository->getApps(
                $this->developer_company_id,
                $this->get('app_id', null)
            );
        }

        //plan
        $repository->pushCriteria(
            new AppPlanCriteria(new SubscriptionFilter(['plan' => $this->get('plan', null)]))
        );

        //date: start, end
        $repository->pushCriteria(
            new DateCriteria(new SubscriptionFilter([
                'start_date' => $this->get('start_date', null),
                'end_date'   => $this->get('end_date', null),
            ]))
        );

        //status
        $status = $this->get('status', null);
        $repository->pushCriteria(
            new StatusCriteria(new SubscriptionFilter([
                'status' => !empty($status) ? SallaSubscriptionStatus::mapStatusToSubscriptionStatus($status) : null,
            ]))
        );

        //store id, name
        $repository->pushCriteria(
            new StoreCriteria(new SubscriptionFilter([
                'store_id'   => $this->get('store_id', null),
                'store_name' => $this->get('store_name', null),
            ]))
        );

        //payout_id
        $repository->pushCriteria(
            new PayoutCriteria(new SubscriptionFilter([
                'payout_id' => $this->get('payout_id', null),
            ]))
        );

        //coupon_id
        $repository->pushCriteria(
            new CouponCriteria(new SubscriptionFilter([
                'coupon_id' => $this->get('coupon_id', null),
            ]))
        );

        $repository->with([
            'store.owner:id,model_has_roles.store_id,first_name,last_name',
            'sallaProduct.app',
        ])
            ->orderBy('salla_subscriptions.id', 'DESC');

        /**
         * if we want to take result as query like in export
         */
        if ($this->result_type == 'query') {
            return $repository;
        }

        return $repository->paginate();
    }
}
