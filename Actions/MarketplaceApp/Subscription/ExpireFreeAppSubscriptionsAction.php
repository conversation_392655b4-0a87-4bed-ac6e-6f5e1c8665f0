<?php

namespace Modules\MarketPlace\Actions\MarketplaceApp\Subscription;

use Salla\Core\Base\Action;
use Modules\MarketPlace\Enum\SallaSubscriptionType;
use Modules\MarketPlace\Jobs\ExpiredSubscriptionJob;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;

class ExpireFreeAppSubscriptionsAction extends Action
{

    /**
     * @return array[]
     */
    public function rules()
    {
        return [
            'app'      => [
                'required',
            ],
            'store_id' => [
                'nullable',
                'numeric',
            ],
        ];
    }

    /**
     * @return void
     */
    public function handle()
    {
        /**
         * @var SallaProductMarketplaceApp $app
         */
        $app = $this->get('app');
        $store_id = $this->get('store_id', null);

        SallaSubscriptions::where('product_id', $app->product_id)
            ->where('status', SallaSubscriptionStatus::ACTIVE)
            ->where('order_id', 0)
            ->where('subscription_type', SallaSubscriptionType::FREE)
            ->when(!empty($store_id), function ($query) use ($store_id) {
                return $query->where('store_id', $store_id);
            })
            ->chunk(100, function ($subscriptions) {
                foreach ($subscriptions as $subscription) {
                    /**
                     * @var SallaSubscriptions $subscription
                     */
                    dispatch(new ExpiredSubscriptionJob($subscription));
                }
            });
    }
}