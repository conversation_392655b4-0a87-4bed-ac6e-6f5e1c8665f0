<?php

namespace Modules\MarketPlace\Actions\Cart;

use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use Modules\Domain\Rules\AddDomainProductToCart;
use Modules\Domain\Rules\MoveDomainProductToCart;
use Modules\Expert\ExpertClients\Request\OrderExpertClient;
use Modules\MarketPlace\Contract\SallaCartService;
use Modules\MarketPlace\Entities\SallaCart;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaCartItemFeature;
use Modules\MarketPlace\Entities\SallaCartItemFreeProduct;
use Modules\MarketPlace\Entities\SallaProductPriceDiscount;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Entities\SallaProductPriceFeature;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\PartnerAffiliateUtmSource;
use Modules\MarketPlace\Enum\PartnerAffiliateVisitorType;
use Modules\MarketPlace\Enum\SallaCartSource;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Presenter\Affiliate\PartnerAffiliateDataHelper;
use Modules\MarketPlace\Rules\AddAddonProductToCartRule;
use Modules\MarketPlace\Rules\AddStoreAppRule;
use Modules\MarketPlace\Rules\AddSubscriptionToCartRule;
use Modules\MarketPlace\Rules\CheckProductForTemplateStoreRule;
use Modules\MarketPlace\Rules\CheckProductPriceFeaturesCartRule;
use Modules\MarketPlace\Rules\CheckProductPriceOfCartRule;
use Modules\MarketPlace\Rules\CheckStoreCustomPriceRule;
use Salla\Core\Base\Action;
use Salla\Events\Salla\Product\ProductAddedToCart;
use Salla\Events\Salla\UpdateCart;
use Spatie\GoogleTagManager\GoogleTagManager;
use Modules\Store\Traits\StoreTemplate\DevelopmentStoreUserTrait;


/**
 * Class AddItemAction.
 *
 * @method self setProduct(SallaProducts $product)
 * @method self setCart(SallaCart $cart)
 *
 * @property SallaProducts product
 * @property SallaCart cart
 * @property null|int product_price_id
 */
class AddItemAction extends Action
{
    use DevelopmentStoreUserTrait;

    private SallaCartService $cartService;

    protected ?SallaProductPrice $product_price;

    protected function prepareForValidation()
    {
        if (
            empty($this->get('product_price_id')) ||
            !is_numeric($this->get('product_price_id'))
        ) {
            throw ValidationException::withMessages(['error' => __t('marketplace::cart.messages.error.please select product price')]);
        }

        $this->cartService = app(SallaCartService::class);
        $this->set('product_price_id', app('optimus')->decode($this->get('product_price_id')));

        if ($this->has('cart') && !($this->cart instanceof SallaCart)) {
            $this->set('cart', $this->cartService->findCart(app('optimus')->decode($this->get('cart'))));
        }

        $this->initCart();
    }

    public function rules(): array
    {
        $this->product_price = SallaProductPrice::query()->withTrashed()->find($this->product_price_id);

        if (!$this->product_price) {
            throw ValidationException::withMessages(['error' => __t('marketplace::cart.messages.error.please select product price')]);
        }
        $this->product_price->setRelation('product',$this->product);

        $rules = [
            'product'              => [
                'required',
                new CheckProductForTemplateStoreRule(),
            ],
            'product_price_id'     => [
                'required',
                'exists:salla_products_pricing,id',
                new AddAddonProductToCartRule($this->cartService, $this->product),
                new AddSubscriptionToCartRule($this->product, $this->cart, $this->product_price, $this->features),
                new CheckProductPriceOfCartRule($this->product),
                new CheckStoreCustomPriceRule($this->product),
                new CheckProductPriceFeaturesCartRule($this->product, $this->product_price, $this->features),
            ],
            'quantity'             => [
                'nullable',
                'numeric',
            ],
            'special_price'        => [
                'nullable',
                'boolean',
            ],
            'features'             => [
                'nullable',
                'array',
            ],
            'with_first_time_cost' => [
                'nullable',
                'boolean',
            ],
            'is_renew' => [
                'nullable',
                'boolean',
            ],
            'promotion_id' => [
                'nullable',
                'integer',
            ],
            'external_promotion_id' => [
                'nullable',
                'integer',
            ],
            'promotion' => [
                'nullable',
            ],
        ];

        if ($this->product) {

            abort_if($this->product->type === SallaProductType::SHIPPING, 404);

            if ($this->product->type === SallaProductType::DOMAIN) {
                $rules['value'] = [
                    'required',
                    new AddDomainProductToCart(),
                ];
            }

            if ($this->product->type === SallaProductType::MOVE_DOMAIN) {
                $rules['value.domain'] = [
                    'required',
                    new MoveDomainProductToCart(),
                ];
                $rules['value.authCode'] = 'required';
            }

            if ($this->product->type === SallaProductType::MOBILE_APPS && in_array(array_keys($rules),['value'])) {
                $rules['value'] = [new AddStoreAppRule()];
            }

            /**
             * for cart app in new dashboard we don't pass first time cost so we need to check if the app has first time cost
             * currently apply promotion for the app only
             */
            if($this->cart->source == SallaCartSource::APPS_V2) {
                if (!$this->has('with_first_time_cost')) {
                    $this->set('with_first_time_cost', AddFirstTimeCostAction::make([
                        'product'       => $this->product,
                        'product_price' => $this->product_price,
                    ])->run());
                }

                if (!$this->has('promotion') && ($this->promotion_id || $this->external_promotion_id)) {
                    $this->set('promotion', AddPromotionAction::make([
                        'product'       => $this->product,
                        'product_price' => $this->product_price,
                        'promotion_id'  => $this->promotion_id,
                        'external_promotion_id'  => $this->external_promotion_id,
                    ])->run());
                }
            }
        }

        return $rules;
    }

    public function attributes(): array
    {
        $product = $this->get('product');

        $attributes = [
            'product_id'  => __t('marketplace::cart.model.product_id'),
            'period'      => __t('marketplace::cart.model.period'),
            'domain'      => __t('marketplace::cart.model.domain'),
            'auth'        => __t('marketplace::cart.model.authCode'),
            'sender_name' => __t('marketplace::cart.model.sender_name'),
        ];

        if (\in_array($product->type, [SallaProductType::DOMAIN, SallaProductType::MOVE_DOMAIN], true)) {
            $attributes['value'] = __t('marketplace::cart.model.value.domain');
        }

        return $attributes;
    }

    public function messages(): array
    {
        $messages = [];

        if ($this->product && $this->product->type === SallaProductType::DOMAIN) {
            $messages['value.required'] = __t('marketplace::cart.messages.error.domain is required');
        }

        if ($this->product && $this->product->type === SallaProductType::MOVE_DOMAIN) {
            $messages['value.domain.required'] = __t('marketplace::cart.messages.error.domain is required');
        }

        return $messages;
    }

    public function handle(): SallaCart
    {
        /*
         * for new way will not warning user will just replace old product
         */
        if (!$this->canAddProduct()) {
            return $this->cart;
        }
        $value = $this->get('value');

        /**
         * @var SallaCartItem $cartItem
         */
        $cartItem = $this->cart->items()->create([
            'cart_id'          => $this->cart->getKey(),
            'product_id'       => $this->product->getKey(),
            'currency'         => $this->product->currency,
            'period'           => $this->getPeriod(),
            'product_price_id' => $this->product_price->getKey(),
            'value'            => (!empty($value) && \is_array($value) ? json_encode($value) : $value),
            'product_price'    => $this->getProductPrice(),
            'quantity'         => $this->product->has_quantity ? $this->get('quantity', 1) : 1,
            // if this price base in dynamic parameters like current plan date & others
            // we need to known to et the price again each time we processing the items
            'is_dynamic_price' => \in_array($this->product->type, SallaProductType::getSubscriptionType(), true),
            //check if item is free
            'is_special_price' => $this->checkIfItemFree(),
            'first_time_cost'  => $this->getFirstTimeCost(),
            'promotion_id'     => $this->promotion?->id,
        ]);
        $cartItem->setRelation('product', $this->product);

        /**
         * for business need
         * in app state we will prevent use counpon while apply promotion
         */
        if($this->promotion && $this->cart->coupon_code){
            //coupon is not allowed with promotion
            $this->cartService->resetCoupon();
        }

        //Set Cart Parameters
        $this->setCartParameters();

        //Sometimes when item is product maybe has conditioned discount price
        //so let us check if has discount price and re set it as price
        $this->setConditionedPriceDiscountInfo($cartItem);

        // Gift
        $this->setGiftsOfProduct($cartItem);

        //Feature
        $this->setFeaturesOfProduct($cartItem);

        event(
            new ProductAddedToCart(
                $this->product,
                $cartItem->product_price,
                $cartItem
            )
        );
        //update Addon product if exist
        event(new UpdateCart($this->cart));

        GoogleTagManager::addToCart($this->product);

        $this->cart->refresh();

        $this->cart->trackUtm([], false);

        SetItemUtmAction::make([
            'cart'      => $this->cart,
            'cartItem'  => $cartItem,
        ])->run();

        return $this->cart;
    }

    private function canAddProduct(): bool
    {
        // in case the item already exists in cart lets return current cart
        $cartItem = $this->cart->items->first(function (SallaCartItem $cartItem) {
            return $this->product->id == $cartItem->product_id &&
                $cartItem->product->type != SallaProductType::MOBILE_APPS
                &&
                (
                    ($cartItem->product_price_id == $this->product_price->getKey()) ||
                    ($cartItem->price->period == $this->product_price->period)
                );
        });

        if (!empty($cartItem)) {
            return false;
        }

        // lets cleanup old items from same type or same product
        if (in_array($this->product->type, [SallaProductType::PLAN, SallaProductType::MOBILE_APPS])) {
            $this->cart->items->filter(function (SallaCartItem $item) {
                return $this->product->type === $item->product->type;
            })->each(function (SallaCartItem $item) {
                if (SallaProductType::IsMOBILE_APPS($item->product->type) && $this->get('value') == null) {
                    $this->set('value', $item->value);
                }
                $item->delete();
            });
        } else {
            $this->cart->items->filter(function (SallaCartItem $item) {
                return $this->product->getKey() == $item->product_id;
            })->each(function (SallaCartItem $item) {
                $item->delete();
            });
        }

        return true;
    }

    /**
     * @return int|null
     */
    private function getPeriod()
    {
        return !empty($this->promotion?->period) ? $this->promotion->period :
            $this->product_price->period;
    }

    /**
     * check if item is free in special plan it will be free
     * in store template state all product will free except developer theme it will be not free
     * @return bool
     */
    private function checkIfItemFree()
    {
        // in case of development store the user will have to pay for all items (theme, installed apps ..)
        if(
            !feature('ready-store-theme-purchase')->isHaveFeature() &&
            $this->hasApprovedDevelopmentStore(auth()->user()->id)
        ) {
            return false;
        }

        if($this->get('special_price', false)) {
            return $this->cart->items->contains(function (SallaCartItem $cartItem) {
                return $cartItem->product->type == SallaProductType::PLAN &&
                    $cartItem->product->type_value == SallaProductPlanType::SPECIAL;
            });
        }

        if(!$this->cart->store->isTemplate()) {
            return false;
        }

        /**
         * in transfer store cart if product not theme store will pay it
         */
        if(
            ($this->hasApprovedDevelopmentStore(auth()->user()->id)) ||
            ($this->cart->source == SallaCartSource::TRANSFER_STORE)
        ) {
            /**
             * when transfer store the developer buy service before
             */
            if($this->product->isExpertService()) {
                return true;
            }

            if(feature('ready-store-theme-purchase')->isHaveFeature()) {
                return false;
            }

            //if private theme it will be free, if salla theme he will pay it
            if ($this->product->type != SallaProductType::THEME) {
                return false;
            }

            return !empty($this->product->theme) && !$this->product->theme->is_salla_theme;
        }

        /**
         * if store is template and add service he need to buy it
         */
        if($this->product->isExpertService()) {
            return false;
        }

        if(feature('ready-store-theme-purchase')->isHaveFeature()) {
            //like theme he will buy if it salla theme or partner theme
            return true;
        }

        //Normal Cart
        if ($this->product->type != SallaProductType::THEME) {
            return true;
        }

        return empty($this->product->theme) || $this->product->theme->is_salla_theme;
    }

    private function setGiftsOfProduct(SallaCartItem $cartItem)
    {
        // if this Item has discounted price, then no need to add any gift for it
        if ($cartItem->product_price_discount_id) {
            return;
        }

        $giftsSelected = $this->get('gifts', []);

        $productGifts = $cartItem->product->getExtraProducts(
            $cartItem->product_price_id,
            null,
            false,
            $this->cart->isTransferStoreSource()
        );


        if (empty($productGifts)) {
            return;
        }

        // domain -> no select  -> add
        // sms -> no select -> add
        // theme -> select -> value match
        // sender name -> no select -> add
        // TODO :: refacting
        $giftsSelectedId = [];
        foreach ($productGifts as $type => $listProduct) {
            if (empty($listProduct) || $listProduct->count() == 0) {
                continue;
            }

            if ($listProduct->count() < 2) {
                $product           = $listProduct->first();
                $giftsSelectedId[] = $product->id;

                SallaCartItemFreeProduct::updateOrCreate([
                    'cart_item_id' => $cartItem->id,
                    'free_id'      => $product->free_id,
                ], [
                    'product_id'       => $product->id,
                    'product_price_id' => $product->free_product_price_id,
                ]);
            } else {
                if (!empty($giftsSelected[$type])) {
                    //if user can select more than one free product
                    $extraProductsSelectedType = Arr::wrap($giftsSelected[$type]);
                    foreach ($extraProductsSelectedType as $val) {
                        $temp       = explode('_', $val);
                        $free_id    = $temp[0];
                        $product_id = $temp[1];
                        foreach ($listProduct as $product) {
                            //for theme state
                            if (
                                ($product->id == $product_id) &&
                                ($product->free_id == $free_id)
                            ) {
                                $giftsSelectedId[] = $product->id;

                                SallaCartItemFreeProduct::updateOrCreate([
                                    'cart_item_id' => $cartItem->id,
                                    'free_id'      => $product->free_id,
                                ], [
                                    'product_id'       => $product->id,
                                    'product_price_id' => $product->free_product_price_id,
                                ]);

                                continue;
                            }
                        }
                    }
                } else {
                    $product           = $listProduct->first();
                    $giftsSelectedId[] = $product->id;

                    SallaCartItemFreeProduct::updateOrCreate([
                        'cart_item_id' => $cartItem->id,
                        'free_id'      => $product->free_id,
                    ], [
                        'product_id'       => $product->id,
                        'product_price_id' => $product->free_product_price_id,
                    ]);
                }
            }
        }

        if (!empty($giftsSelectedId)) {
            $cartItem->save();
        }
    }

    /**
     * @return float|int|mixed
     */
    private function getProductPrice()
    {
        if (SallaProductAddonType::isExternalPriceServices($this->product->type_value) && is_array($this->get('value')) && optional($this->get('value'))['order_id']) {
            return $this->getExternalPrice();
        }

        // if product is affiliate product take from product price
        if(SallaProductType::AFFILIATE === $this->product?->type ?? null) {
            return $this->product_price->getPrice()->getBasePriceAsFloat();
        }

        $price = $this->product_price->getPriceUntilNextRenewal()->getBasePriceAsFloat();
        if ($this->checkIfItemFree()) {
            return 0;
        }

        /**
         * in app state if have promotion get price of promotion
         * for example : app plan price 99 for monthly and there is promotion subscription buy 2 month and get 1 free :
         * so the merchant will see in the checkout 99 x 2 = 198
         */
        if($this->promotion?->price) {
            $price = $this->promotion->price;
        }

        //First Time Cost
        $first_time_cost = $this->getFirstTimeCost();
        if(!empty($first_time_cost)) {
            $price += $first_time_cost;
        }

        return $price;
    }

    /**
     * @return float|int|mixed
     */
    private function getExternalPrice()
    {
        $orderType = match ($this->product->type_value) {
            SallaProductAddonType::INFLUENCER_SERVICE => 'campaigns',
            default => 'orders',
        };
        $price = app(OrderExpertClient::class)->getQutation($this->get('value')['order_id'], $orderType);
        if (!$price) {
            throw ValidationException::withMessages(['error' => __t('marketplace::cart.messages.error.please select product price')]);
        }
        return $price;
    }

    private function setFeaturesOfProduct(SallaCartItem $cartItem)
    {
        $cartItem->priceFeatures()
            ->whereIn('id', $cartItem->priceFeatures()
                ->select('id')
                ->pluck('id')
                ->toArray()
            )
            ->delete();

        $features_input = $this->get('features');
        if (empty($features_input) || !is_array($features_input)) {
            return;
        }

        $feature_price = 0;
        $features      = SallaProductPriceFeature::where('product_price_id', $this->product_price->id)
            ->whereIn('id', array_column($features_input, 'id'))
            ->get();

        foreach ($features as $feature) {
            $feature_input = array_filter($features_input, function ($val) use ($feature) {
                return $val['id'] == $feature->id;
            });

            if (empty($feature_input)) {
                continue;
            }

            $feature_input = array_values($feature_input)[0];
            $quantity      = !empty($feature_input['value']) ? $feature_input['value'] : 1;

            SallaCartItemFeature::create([
                'cart_id'      => $cartItem->cart_id,
                'cart_item_id' => $cartItem->id,
                'feature_id'   => $feature->id,
                'price'        => $feature->price,
                'quantity'     => $quantity,
            ]);

            $feature_price += $feature->price * $quantity;
        }

        $cartItem->update([
            'feature_price' => $feature_price,
            'product_price' => ($cartItem->product_price + $feature_price),
        ]);
    }

    private function setConditionedPriceDiscountInfo(SallaCartItem $cartItem): void
    {
        if (!$cartItem->product->isSupportDiscountedPrice()) {
            return;
        }

        //we used the cached attribute
        /** @var SallaProductPriceDiscount $priceDiscount */
        if ($priceDiscount = $this->product_price->getConditionedPriceDiscount()) {
            $cartItem->update([
                'product_price_discount_id' => $priceDiscount->getKey(),
                'product_price'             => $priceDiscount->getPrice()->getBasePriceAsFloat(),
            ]);
        }
    }

    /**
     * initialize cart
     */
    protected function initCart()
    {
        /*
         * @var SallaCart $cart
         */
        if ($this->has('cart') && $this->cart instanceof SallaCart) {
            $this->cartService->setCart($this->cart);
        } else {
            $this->cart = $this->cartService->getCart();
        }
    }

    /**
     * add this amount to price for first time only when purchase product
     * @return mixed|null
     */
    private function getFirstTimeCost()
    {
        if (!$this->get('with_first_time_cost', null) || !$this->product_price->hasFirstTimeCost()) {
            return null;
        }

        $value = $this->get('value');
        $value = is_string($value) ? json_decode($value, true) : $value;
      if (SallaProductType::IsMOBILE_APPS($this->product->type)
          && (!isset($value['account'])
          || $value['account'] != 'salla')
      ){
          return null;
      }
        return $this->product_price->first_time_cost;
    }

    /**
     * Set Cart Parameters
     */
    private function setCartParameters()
    {
        $parameters = [];
        if($this->get('is_renew', false)) {
            $parameters['is_renew'] = true;
        }

        if($this->get('is_upgrade', false)) {
            $parameters['is_upgrade'] = true;
        }

        if (!$this->cartService->isInstallmentAllowed() && $this->cart->isInstallmentPayment()) {
            $parameters['is_installment_payment'] = false;
        }

        if(is_array($this->get('value')) && !empty($this->get('value')['external_order_id'])) {
            $parameters['external_order_id'] = $this->get('value')['external_order_id'] ?? null;
        }

        if(empty($parameters)) {
            return;
        }

        $this->cartService->setCartParameters($parameters);
    }
}
