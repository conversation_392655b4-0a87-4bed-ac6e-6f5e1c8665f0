<?php

namespace Modules\MarketPlace\Actions\Cart;

use App\Traits\HasLog;
use Modules\MarketPlace\Actions\Order\CreateOrderAction;
use Modules\MarketPlace\Actions\Payment\CompletePayment;
use Modules\MarketPlace\Entities\SallaCart;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Features\AIProductFeature;
use Modules\MarketPlace\Presenter\CartPayment\PaymentPresenter;
use Modules\MarketPlace\Presenter\TotalPresenter;
use Salla\Core\Base\Action;
use Salla\Paymetns\Actions\CreateInvoice;
use Salla\Paymetns\Actions\GetPaymentMethodCard;
use Salla\Paymetns\Data\Item;
use Salla\Paymetns\Traits\HasRunWithCatch;

/**
 * Class SubmitCartAction.
 *
 * @method self setReference(\Salla\Paymetns\Contracts\Invoice\InvoiceReference $reference)
 * @method self setTotals(\Modules\MarketPlace\Presenter\TotalPresenter $total)
 * @method self setItems(\Illuminate\Support\Collection $items)
 * @method self setPaymentRequest(\Salla\Paymetns\Contracts\PaymentRequestInterface $request)
 *
 * @method PaymentPresenter run(array $attributes = [])
 *
 * @property \Salla\Paymetns\Contracts\PaymentRequestInterface  payment_request
 * @property \Illuminate\Support\Collection                     items
 * @property TotalPresenter                                     totals
 * @property \Salla\Paymetns\Contracts\Invoice\InvoiceReference reference
 * @property SallaCart                                          cart
 */
class CreatePurchases extends Action
{
    use HasLog, HasRunWithCatch;

    public function rules()
    {
        return [
            'reference' => [
                'required', function ($attribute, $value, $fail) {

                    $referenceCheck = $this->checkReference();

                    if (!is_null($referenceCheck)) {
                        $fail($referenceCheck);
                    }
                },
            ],
        ];
    }

    public function handle()
    {
        $this->debug('Payment: Creating a purchases...');

        // $this->setReference($this->reference);
        $this->setTotals($this->reference->getTotals());
        $this->set('cart', $this->reference->getCart());

        $this->debug(function () {
            return $this->totals->toArray();
        });

        $this->cart->update([
            'amount'                => $this->totals->getSubTotal(),
            'total'                 => $this->totals->getTotal(),
            'tax'                   => $this->totals->getTax(),
            'tax_amount'            => $this->totals->getTaxAmount(),
            'total_discount_amount' => $this->totals->getTotalDiscount(),
            'store_credit'          => $this->totals->getCredit()->getMinusCredit(),
            'change_plan_credit'    => $this->totals->getCredit()->getPlusCredit(),
        ]);

        $this->addBreadcrumb('Payment totals', ['total' => $this->totals->toArray()], $this->cart);

        if(!in_array($this->get('payment_method'), ['bank', 'free'])) {
            $this->set('gateway', store()->getSetting('salla::payment.gateway', 'noon'));
        }

        $payment = CreateInvoice::createFrom($this)
            ->setCard($this->delegateTo(GetPaymentMethodCard::class))
            ->setItems($this->getItems())
            ->setMethodSlug($this->payment_request->getPaymentMethod())
            ->run();

        $this->debug(sprintf('Payment: invoice created with status (%s)', $payment->getStatus()));

        if ($payment->isPending() || ! $payment->isSuccess()) {
            return $payment;
        }

        $cart              = $this->reference->getCart();
        $isCartInstallment = !empty($cart->parameters['is_installment_cart'])
            && $cart->parameters['is_installment_cart'] == true;

        if ($isCartInstallment){
            grayLog('debug','Check the installment applypay',[
                'order_id'            => $cart->order_id,
                'is_cart_installment' => $isCartInstallment,
                'request_invoices'    => request()->get('invoices'),
                'cart_data'           => $cart,
                'current_route'       => request()->route(),
                'request_all'         => request()->all(),
            ]);
        }
        // if cart already has order, just capture the payment and move cart to purchased
        if (($cart->order_id || $isCartInstallment) && $this->payment_request->getPaymentMethod() == 'apple_pay') {

            $payment = CompletePayment::make()
                ->setInvoice($this->reference->getInvoice())
                ->setTotals($this->totals)
                ->setCart($cart)
                ->run();

            $cart->update(['status' => 'purchased']);

            return $payment;
        }

        return CreateOrderAction::make()
            ->setTotal($this->totals)
            ->setInvoice($this->reference->getInvoice())
            ->setPayment($payment)
            ->setReference($this->reference)
            ->setSource($this->source ?? '')
            ->run();
    }

    protected function getItems(): \Omnipay\Common\ItemBag
    {
        $items = $this->reference->getItemsBag();

        if ($this->totals->getTaxAmount()) {
            $items->add(new Item([
                'name'     => 'Tax',
                'quantity' => 1,
                'price'    => $this->totals->getTaxAmount(),
            ]));
        }

        if ($this->totals->getTotalDiscount()) {
            $items->add(new Item([
                'name'     => 'Discount',
                'quantity' => 1,
                'price'    => $this->totals->getTotalDiscount() * -1,
            ]));
        }

        return $items;
    }

    protected function checkReference(): string|null
    {

        if ($this->reference->isEmpty()) {
            return __('marketplace::cart.messages.error.cart is empty');
        }

        $isAI = $this->reference->getItemsByType(SallaProductType::AI_DESCRIPTION)->isNotEmpty();
        if ($isAI && !feature(AIProductFeature::getName())->isHaveFeature()){
            return __('marketplace::cart.messages.error.product not exists');
        }

        if ($this->reference->checkSallaPlanPending()) {
            return __('product.pending_order_plan_massage');
        }

        if ($this->reference->checkSallaProductSenderName()) {
            return __('document.sender_request.messages.error.you have product sender request need to add missing document');
        }

        if ($this->reference->checkSallaProductsNeedPlan()) {
            if ($isAI) {
                return __('marketplace::cart.messages.error.to_pay_AI_description_you_must_have_salla_plus_or_pro_plan_first');
            }

            return __('marketplace::cart.messages.error.to_pay_themes_you_must_have_salla_plus_or_pro_plan_first');
        }

        return null;
    }
}
