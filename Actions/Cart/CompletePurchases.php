<?php

namespace Modules\MarketPlace\Actions\Cart;

use App\Traits\HasLog;
use Modules\MarketPlace\Actions\Order\CreateOrderAction;
use Modules\MarketPlace\Actions\Payment\CompletePayment;
use Modules\MarketPlace\Entities\SallaOrders;
use Modules\MarketPlace\Presenter\CartPayment\PaymentPresenter;
use Modules\MarketPlace\Presenter\TotalPresenter;
use Salla\Core\Base\Action;
use Salla\Paymetns\Contracts\CreditCardResponse;
use Salla\Paymetns\Presenters\OrderPresenter;
use Salla\Paymetns\Traits\HasRunWithCatch;
use Salla\Paymetns\Traits\HasRunWithLock;

/**
 * Class CompletePurchases
 *
 * @package Modules\MarketPlace\Actions\Cart
 * @method self setReference(\Salla\Paymetns\Contracts\Invoice\InvoiceReference $reference)
 * @method self setTotals(\Modules\MarketPlace\Presenter\TotalPresenter $total)
 *
 * @method PaymentPresenter run(array $attributes = [])
 *
 * @property TotalPresenter                                     totals
 * @property \Salla\Paymetns\Contracts\Invoice\InvoiceReference reference
 */
class CompletePurchases extends Action
{
    use HasLog, HasRunWithCatch;

    public function handle()
    {
        // lets check if invoice available
        // if not will throw execution and HasRunWithCatch will handle it
        $isInvoiceAvailable = optional($this->reference)->hasInvoice() && $this->reference->getInvoice();
        if(!$isInvoiceAvailable){
            return PaymentPresenter::error('العملية المالية ملغاة في حالة تم خصم مبلغ مالي من بطاقتك بامكانك دوما التواصل مع خدمة العملاء عبر المحادثة لاسترداد المبلغ');
        }

        // let's check if the transaction reference is already used for other order
        /** @var SallaOrders $order */
        $order = SallaOrders::query()
            ->where('epayment_id', $this->reference->getInvoice()->transactionReference)
            ->first();

        if ($order || $this->reference->getInvoice()->status->isPaid()) {
            $order_id = $order ? $order->getKey() : $this->reference->getInvoice()->target_id;

            return PaymentPresenter::success()->setOrder(new OrderPresenter([
                'order_id' => $order_id,
            ]));
        }

        $this->reference->getInvoice()->load([
            'gateway' => function ($query) {
                return $query->withoutGlobalScopes();
            },
        ]);

        $this->debug('Calculating cart totals...');

        $this->setTotals($this->reference->getTotals());

        $this->debug(function () {
            return $this->totals->toArray();
        });

        $transactionAmount = $this->reference->getInvoice()->extra_parameters->get('amount');
        if (abs($this->totals->getAmountInBaseCurrency() - $transactionAmount) > 0.5) {
            // lets try to reverse the amount if gateway support that
            app('payment')
                ->fromInvoice($this->reference->getInvoice())
                ->reverse($this->reference->getInvoice()->parameters)
                ->send();

            return PaymentPresenter::error('اجمالي المنتجات في السلة غير متطابق مع المبلغ المدفوع')
                ->ignoreInvoiceStatus();
        }

        /* @var \Omnipay\Common\Message\AbstractResponse $response */
        $response = payments()->fromInvoice($this->reference->getInvoice())
            ->completePurchase($this->reference->getInvoice()->parameters)
            ->send();

        if (! $response->isSuccessful() && ! $response->isPending()) {
            return PaymentPresenter::error($response->getMessage());
        }

        // lets handling the credit card saving for noon
        if ($response instanceof CreditCardResponse && isset($this->reference->getInvoice()->parameters['card']['id'])) {
            payments()->saveCreditCardDetails($this->reference->getInvoice()->parameters['card']['id'], $response);
        }

        if($this->reference->getInvoice()) {
            $this->reference->getInvoice()->update([
                'captured' =>  (is_object($response) && method_exists($response, 'getCapturedAmount')) ?
                    round($response->getCapturedAmount(), 2) : round($response->getAmount(), 2),
            ]);
        }

        $response = new PaymentPresenter([
            'order_status'   => $response->isPending() ? 'pending_review' : 'paid',
            'payment_method' => $this->reference->getInvoice()->paymentMethod->slug,
        ]);

        $cart = $this->reference->getCart();
        // if cart already has order, just capture the payment and move cart to purchased
        if ($cart->order_id) {

            $payment = CompletePayment::make()
                ->setInvoice($this->reference->getInvoice())
                ->setTotals($this->totals)
                ->setCart($cart)
                ->run();

            $cart->update(['status' => 'purchased']);

            return $payment;
        }

        $actionResult = CreateOrderAction::make()
            ->setTotal($this->totals)
            ->setInvoice($this->reference->getInvoice())
            ->setPayment($response)
            ->setReference($this->reference)
            ->run();

        // This means the action faced a problem
        if ($actionResult === false) {
            return PaymentPresenter::error('هناك مشكلة حدثت أثناء معالجة طلبك بعد الدفع');
        }

        return $actionResult;
    }
}
