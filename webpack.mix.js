const mix = require('laravel-mix')

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
// TODO :: fixed build issue for app.css and all.js
// mix.js([
//
//         'public/cp/assets/js/core/libraries/jquery.min.js',
//         'public/cp/assets/js/core/libraries/bootstrap.min.js',
//         'public/cp/assets/js/plugins/loaders/pace.min.js',
//         'public/cp/assets/js/plugins/ui/moment/moment.min.js',
//         'public/cp/assets/js/plugins/forms/styling/uniform.min.js',
//         'public/cp/assets/js/plugins/media/fancybox.min.js',
//         'public/cp/assets/js/plugins/notifications/sweetalert2.min.js',
//         'public/cp/assets/js/plugins/forms/styling/switchery.min.js',
//         'public/cp/assets/js/plugins/forms/selects/bootstrap-select.min.js',
//         'public/cp/assets/js/plugins/pickers/daterangepicker.js',
//         'public/cp/assets/js/plugins/visualization/d3/d3.min.js',
//         'public/cp/assets/js/plugins/visualization/d3/d3_tooltip.js',
//         'public/cp/assets/js/plugins/forms/inputs/autosize.min.js',
//         'public/cp/assets/js/plugins/xpull/xpull.js',
//         'public/cp/assets/js/plugins/forms/inputs/bootstrap-maxlength.js',
//         'public/cp/assets/js/plugins/slim/old_slim.kickstart.min.js',
//         'public/cp/assets/js/plugins/quill/quill.min.js',
//         'public/cp/assets/js/plugins/forms/inputs/clipboard.min.js',
//         'public/cp/assets/js/plugins/pickers/color/spectrum.js',
//         'public/cp/assets/js/plugins/biomp/biomp.js',
//         'public/cp/assets/js/plugins/jquery.addrule.js',
//         'public/cp/assets/js/pages/form_checkboxes_radios.js',
//         'public/cp/assets/js/core/libraries/jquery-ui.min.js',
//         'public/cp/assets/js/plugins/forms/styling/switch.min.js',
//         'public/cp/assets/js/plugins/jquery-confirm/jquery-confirm.js',
//         'public/cp/assets/js/plugins/ui/jquery.simpler-sidebar.min.js',
//         'public/cp/assets/uploader/js/fileinput.min.js',
//         'public/cp/assets/uploader/js/locales/ar.js',
//         'public/cp/assets/js/pages/dashboard.js',
//         'public/cp/assets/js/main.js',
//         'public/cp/assets/js/core/app.js',
//         'public/cp/assets/js/core/settings.js',
//
//     ], 'public/js/all.js')
//     .version();
//
// mix.js([
//     'public/cp/assets/js/charts/amcharts/amcharts.js',
//     'public/cp/assets/js/charts/amcharts/serial.js',
//     'public/cp/assets/js/charts/amcharts/pie.js',
//     'public/cp/assets/js/charts/amcharts/themes/light.js',
//     'public/cp/assets/js/charts/amcharts/plugins/responsive/responsive.min.js',
// ], 'public/js/charts.js');
//
// mix.styles([
//         'public/cp/assets/css/icons/icomoon/styles.css',
//         'public/cp/assets/css/icons/fontawesome/styles.min.css',
//         'public/cp/assets/css/bootstrap.min.css',
//         'public/cp/assets/css/core.min.css',
//         'public/cp/assets/css/components.min.css',
//         'public/cp/assets/css/colors.min.css',
//         'public/cp/assets/css/bootstrap-social.css',
//         'public/cp/assets/css/plugins/slim/slim.min.css',
//         'public/cp/assets/css/plugins/quill/quill.snow.css',
//         'public/cp/assets/css/plugins/notifications/sweetalert2.min.css',
//         'public/cp/assets/js/plugins/jquery-confirm/jquery-confirm.css',
//         'public/cp/assets/uploader/css/fileinput.min.css',
//         'public/cp/assets/uploader/css/fileinput-rtl.min.css',
//         'public/cp/assets/css/custom.css',
//     ], 'public/css/all.css')
//     .version();

mix.sass('resources/email/scss/email.scss', 'public/css/email.css');
mix.sass('resources/assets/scss/re-custom.scss', 'public/cp/assets/css/re-custom.css');
mix.sass('resources/assets/scss/themes/dark/dark.scss', 'public/cp/assets/css/themes/dark.css');
mix.sass('resources/assets/scss/register.scss', 'public/cp/assets/css/register.css');
mix.sass('resources/assets/scss/full-calendar-custom.scss', 'public/cp/assets/css/full-calendar-custom.css');
mix.sass('resources/assets/scss/product-sorting.scss', 'public/cp/assets/css/product-sorting.css')
mix.sass('resources/assets/scss/export-logs.scss', 'public/cp/assets/css/export-logs.css')
mix.sass('resources/assets/scss/operation-logs.scss', 'public/cp/assets/css/operation-logs.css')
mix.sass('resources/assets/scss/export-templates.scss', 'public/cp/assets/css/export-templates.css')
mix.sass('resources/assets/scss/order-options.scss', 'public/cp/assets/css/order-options.css')
mix.sass('resources/assets/scss/mahly.scss', 'public/cp/assets/css/mahly.css')

mix.sass(
  'resources/assets/scss/dashboardapi.scss',
  'public/cp/assets/css/dashboardapi.css'
)
mix.sass('resources/assets/scss/docs-api.scss', 'public/css/docs-api.css')

mix.js('resources/assets/js/components/product-sorting/main.js', 'public/cp/assets/js/product-sorting.js').vue();
mix.js('resources/assets/js/components/export-logs/main.js', 'public/cp/assets/js/export-logs.js').vue();
mix.js('resources/assets/js/components/operation-logs/main.js', 'public/cp/assets/js/operation-logs.js').vue();
mix.js('resources/assets/js/components/orders-custom-fields/main.js', 'public/cp/assets/js/orders-custom-fields.js').vue();
mix.js('resources/assets/js/components/export-templates/main.js', 'public/cp/assets/js/export-templates.js').vue();
mix.js('resources/assets/js/components/order-options/main.js', 'public/cp/assets/js/order-options.js').vue();
mix.js('resources/assets/js/components/docs-api/docs-api.js', 'public/js/docs-api.js').vue();
mix.js('resources/assets/js/components/shipping-role/main.js', 'cp/assets/js/shipping-role.js').vue();
mix.js('resources/assets/js/components/blog-categories/main.js', 'public/cp/assets/js/blog-categories.js').vue()
mix.js('resources/assets/js/components/entity/main.js', 'public/cp/assets/js/entity.js').vue()
mix.js('resources/assets/js/components/nd-services/main.js', 'public/cp/assets/js/nd-services.js').vue()
    // .js('node_modules/intl-tel-input/build/js/intlTelInput.js', 'public/cp/assets/js/plugins/intl-tel-input/intlTelInput.js')
    // .js('node_modules/intl-tel-input/build/js/intlTelInput-jquery.js', 'public/cp/assets/js/plugins/intl-tel-input/intlTelInput-jquery.js')
    // .js('node_modules/intl-tel-input/build/js/intlTelInput-jquery.min.js', 'public/cp/assets/js/plugins/intl-tel-input/intlTelInput-jquery.min.js')
    .js('resources/assets/js/assets/intlTell/intlTell.js', 'public/cp/assets/js/plugins/intl-tel-input/noneQIntlTell.js');

mix
    .copyDirectory('node_modules/intl-tel-input/build/js', 'public/cp/assets/js/plugins/intl-tel-input/js')
    .copyDirectory('node_modules/intl-tel-input/build/img', 'public/cp/assets/js/plugins/intl-tel-input/img')
mix.css('resources/assets/js/assets/intlTell/css/intlTelInput.css', 'public/cp/assets/js/plugins/intl-tel-input/css/intlTelInput.css');
mix
  .js(
    'resources/assets/js/components/quantity-management/main.js',
    'cp/assets/js/quantity-management.js'
  )
  .vue()
mix.js(
  'resources/assets/js/components/notify-quantity-settings/main.js',
  'cp/assets/js/notify-quantity-settings.js'
)
mix
  .js(
    'resources/assets/js/components/products-view/main.js',
    'cp/assets/js/products-view.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/inventory-view/main.js',
    'cp/assets/js/inventory-view.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/bulk-editor/main.js',
    'cp/assets/js/bulk_editor.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/bulk-pricing/main.js',
    'cp/assets/js/bulk_pricing.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/quantity-editor/main.js',
    'cp/assets/js/quantity_editor.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/wholesale-editor/main.js',
    'cp/assets/js/wholesale_editor.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/single-product-view/main.js',
    'cp/assets/js/single_product_view.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/quantity-log/main.js',
    'cp/assets/js/product_quantity_log.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/payments-rules/main.js',
    'cp/assets/js/payments_rules.js'
  )
  .vue()
mix
  .js(
    'resources/assets/js/components/sort-informative-pages/main.js',
    'cp/assets/js/sort_informative_pages.js'
  )
  .vue()
mix.js(
  'resources/assets/js/components/loyalty-system/main.js',
  'cp/assets/js/loyalty_system.js'
)
mix
  .js(
    'resources/assets/js/components/otp/main.js',
    'public/cp/assets/js/otp.js'
  )
  .vue()
mix.js(
  'resources/assets/js/components/shipping-companies/main.js',
  'cp/assets/js/shipping_companies.js'
)

// MultiLangal Support
mix
  .js(
    'resources/assets/js/components/multilingual/main.js',
    'public/cp/assets/js/multilingual.js'
  )
  .vue()
  .sass(
    'resources/assets/scss/multilingual.scss',
    'public/cp/assets/css/multilingual.css'
  )

// Currencies Settings
mix.sass(
  'resources/assets/scss/currencies-settings.scss',
  'public/cp/assets/css/currencies-settings.css'
)

mix
  .js(
    [
      'resources/assets/js/assets/semantic/api.js',
      'resources/assets/js/assets/semantic/calendar.js',
      'resources/assets/js/assets/semantic/popup.min.js',
      'resources/assets/js/assets/semantic/transition.min.js',
      'resources/assets/js/assets/semantic/dropdown.min.js',
    ],
    'public/cp/assets/js/semantic.js'
  )
  .styles(
    [
      //'resources/assets/js/assets/semantic/semantic.min.css', TODO:: missing file
      //'resources/assets/js/assets/search/search.min.css', TODO:: missing file
      'resources/assets/js/assets/semantic/popup.min.css',
      'resources/assets/js/assets/semantic/transition.min.css',
      'resources/assets/js/assets/semantic/table.css',
      'resources/assets/js/assets/semantic/calendar.min.css',
      'resources/assets/js/assets/semantic/dropdown.rtl.min.css',
    ],
    'public/cp/assets/css/semantic.css'
  )

mix.js(__dirname + '/resources/assets/js/app.js', 'public/js/dashboard.js')

mix.js(
  __dirname + '/resources/assets/js/services/ApplePay.js',
  'public/js/payments/apple-pay.js'
)
// .sass( __dirname + '/resources/assets/sass/app.scss', 'public/css/dashboard.css');


mix
  .js(
    'resources/assets/js/assets/filepond/filepond.js',
    'public/js/filepond.js'
  )
  .sass('resources/assets/scss/uploader.scss', 'public/css/uploader.css')

mix.combine(['./public/cp/assets/js/core/settings.js'], 'public/js/settings.js')

if (mix.inProduction()) {
  mix.version()
  mix.then(() => {
    const convertToFileHash = require('laravel-mix-make-file-hash')
    convertToFileHash({
      publicPath: 'public',
      manifestFilePath: 'public/mix-manifest.json',
    })
  })
}