<div class="row">
  <!-- Activation Guideline Iframe -->
  <div class="col-xs-12">
      <div id="iframe_wrapper" class="pb-20 text-center">
        <div class="loader"></div>
      </div>
  </div>
</div>

@push('scripts')
  <script>
    // Remove the value whenever the app gets loaded
    localStorage.removeItem('activation_slug');
    
    window.addEventListener('load', () => {
      const iframe = document.createElement('iframe');
        iframe.id = 'activation_guideline_iframe';
        iframe.src = `https://activation-guideline.pages.dev/ar?storeId=${store.id}&xApiKey=${window.token.key}`;
        iframe.scrolling = 'no';
        iframe.width = '100%';
        iframe.height = 0;
        iframe.loading = 'lazy';
        iframe.seamless = 'seamless';
        iframe.setAttribute('data-hj-allow-iframe', 'true');
        iframe.style.cssText = 'border: unset !important; display: block !important; margin: 0 !important; padding: 0 !important; width: 100% !important';
        document.getElementById('iframe_wrapper').appendChild(iframe);
    
      function onMessage(event) {
        if (iframe) {
          if (event.data.event === 'navigateTo') {
            const activationSlug = event.data.data.slug;
            localStorage.setItem('activation_slug', activationSlug);
            window.location = `/${activationSlug == 'introduction-page' ? 'pages' : activationSlug == 'custom-domain' ? 'domain' : activationSlug == 'product-setup' ? 'products/?page=1&status%5B%5D=without-description&types%5B%5D=all&filtering=1' : activationSlug == 'store-design' ? 'marketplace/themes/management' : 'settings/component/basic'}`
            return
          }

          if (event.data.event === 'resize') {
            iframe.style.height = event.data.data.height + 'px';
            const loader = document.querySelector('#iframe_wrapper .loader');
            loader && loader.remove();
          }

          if (document.body.classList.contains('dark')) {
            iframe.contentWindow.postMessage({
              call: 'darkMode',
              darkMode: true
            }, '*')
          }
        }
      }

      window.addEventListener('message', onMessage);  
    });
  </script>
@endpush