<?php

use Illuminate\Support\Facades\Route;
use Modules\DashboardApi\Http\Middleware\TokenableMiddleware;
use Modules\PostOnboarding\Http\Controllers\Dashboard\Api\V2\PostOnboardingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

Route::prefix('/v2/post-onboarding')
    ->name('api.v2.post-onboarding.')
    ->namespace('V2')
    ->middleware([TokenableMiddleware::class])
    ->group(function () {
        Route::get('/user-state', [PostOnboardingController::class, 'userState'])->name('user-state');
        Route::get('/missions', [PostOnboardingController::class, 'index'])->name('missions.index');
        Route::get('/missions/{missionId?}', [PostOnboardingController::class, 'show'])->name('missions.show');
        Route::post('/missions/{missionId}/{action}', [PostOnboardingController::class, 'missionAction'])->name('missions.action');
        Route::get('/tasks', [PostOnboardingController::class, 'tasks'])->name('tasks.index');
        Route::get('/tasks/{taskId}', [PostOnboardingController::class, 'task'])->name('tasks.show');
        Route::post('/tasks/{taskId}/{action}', [PostOnboardingController::class, 'taskAction'])->name('tasks.action');
    });

