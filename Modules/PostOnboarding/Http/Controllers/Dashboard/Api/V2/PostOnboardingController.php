<?php

namespace Modules\PostOnboarding\Http\Controllers\Dashboard\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Modules\PostOnboarding\Actions\GetUserState;
use Salla\Gamification\Facades\Gamification;
use Salla\Gamification\Http\Exceptions\ExceptionHandler;

class PostOnboardingController extends Controller
{
    /**
     * Display the post onboarding dashboard.
     */
    public function index()
    {
        try {
            // Get missions using the facade
            $missionService = Gamification::missions();
            $missionService = $missionService->attach([
                'tags' => $this->getTags(),
                'type' => request()->get('type', 'current'),
            ]);
            
            return response()->json([
                "status" => "success",
                "code" => 200,
                "data" => [
                    'missions' => request()->get('type', 'current') === 'splitted' ?  
                    $missionService->listSplitted() : 
                    $missionService->list(),
                ]
            ]);

        } catch (\Exception $e) {
            return ExceptionHandler::handleException($e);
        }
    }


    /**
     * Get Mission by ID.
     */
    public function show(string $missionId = '')
    {
        try {
            // Get missions using the facade
            $missionService = Gamification::missions();
            $missionsList = !!$missionId ? $missionService->read($missionId) : $missionService->list();
            return response()->json([
                "status" => "success",
                "code" => 200,
                "data" => [
                    'missions' => $missionsList,
                ]
            ]);

        } catch (\Exception $e) {
            return ExceptionHandler::handleException($e);
        }
    }


    /**
     * Get Mission by ID.
     */
    public function task(string $taskId = '')
    {
        try {
            // Get tasks using the facade
            $taskService = Gamification::tasks();
            $tasksList = !!$taskId ? $taskService->read($taskId) : $taskService->list();
            return response()->json([
                "status" => "success",
                "code" => 200,
                "data" => [
                    'task' => $tasksList,
                ]
            ]);

        } catch (\Exception $e) {
            return ExceptionHandler::handleException($e);
        }
    }
    /**
     * get task by id.
     */
    public function tasks(string $taskId)
    {
       
        try {
            // Get tasks using the facade
            $tasksList = Gamification::tasks()->attach([
                'tags' => $this->getTags(),
                'type' => request()->get('type', 'current'),
            ])->get($taskId);

            return response()->json([
                "status" => "success",
                "code" => 200,
                "data" => [
                    'actions' => $tasksList->tasks,
                ],
            ]);

        } catch (\Exception $e) {
            return ExceptionHandler::handleException($e);
        }
    }

    /**
     * tasks action to handle tasks related to missions.
     */
    public function taskAction(string $taskId, string $action)
    {
       
        try {
            // Get actions using the facade
            $actionsList = Gamification::actions()->action($taskId, $action);

            return response()->json([
                "status" => "success",
                "code" => 200,
                "data" => [
                    'actions' => $actionsList->actions,
                ],
            ]);

        } catch (\Exception $e) {
            return ExceptionHandler::handleException($e);
        }
    }

    /**
     * Mission action to handle actions related to missions.
     */
    public function missionAction(string $missionId, string $action)
    {
        try {
            // Get mission actions using the actions facade
            $actionsList = Gamification::actions()->action($missionId, $action, 'mission');

            return response()->json([
                "status" => "success",
                "code" => 200,
                "data" => [
                    'actions' => $actionsList->actions,
                ],
            ]);

        } catch (\Exception $e) {
            return ExceptionHandler::handleException($e);
        }
    }

    /**
     * Get the current user's gamification state.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function userState()
    {
        // Get the current store
        $store = store()->getCurrent();

        // Get the user state
        $userState = GetUserState::make()->handle($store);

        // Return the user state as a JSON response
        return response()->json([
            'status'=> Response::HTTP_OK,
            'success'=> true,
            'data'=> [
                'user_state' => $userState,
            ]
        ]);
    }

    /**
     * Get tags from request and combine them with user state.
     * Handles both comma-separated strings and arrays of tags.
     *
     * @return string
     */
    private function getTags(): string
    {
        // Get the current store
        $store = store()->getCurrent();

        // Get user state 
        $userState = GetUserState::make()->handle($store);

        // Get tags from request
        $requestTags = request()->get('tags', '');

        // Convert tags to array if it's a string
        $tagsArray = is_array($requestTags) ? $requestTags : array_filter(explode(',', $requestTags));

        // Add user state to tags
        $tagsArray[] = "state:{$userState}";

        // Return comma-separated string of unique tags
        return implode(',', array_unique(array_filter($tagsArray)));
    }
} 