<?php

namespace Modules\Onboarding\Actions\Steps;

use Modules\ThemeCustomization\Entities\ThemeCustomization;
use Salla\Core\Base\Action;
use Salla\Core\Entities\Store;
use Modules\Auth\Services\MerchantService;
use Modules\Onboarding\Enums\OnboardingStep;
use Modules\Store\Events\StoreStatusUpdated;
use Modules\Onboarding\Factories\StepFactory;
use Salla\Core\Enum\Plan;
use Salla\FeatureRules\Facades\FeaturesManager;
use Modules\Store\Enum\CustomerEffortScorePageType;
use Modules\Payment\Actions\Gateways\ActivateGateway;
use App\Notifications\CP\StoreLaunchEmailNotification;
use Modules\Settings\Actions\ChangeStoreDocumentStatus;
use Modules\Component\Actions\SaveStoreThemeBlocksAction;
use Modules\Onboarding\Actions\Store\MarkStoreAsOnboardedAction;
use Modules\ThemeCustomization\Actions\Versions\CreateVersionAction;
use Modules\Onboarding\Actions\User\DispatchEmailVerificationJobAction;

/**
 * Class CompleteLaunchStore
 *
 * Handles the process of completing setting the store as launched.
 *
 * @property \App\Models\User $merchant The merchant performing the action.
 */
class CompleteLaunchStore extends Action
{
    /**
     * Define validation rules for the request data.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<string>|string>
     */
    public function rules(): array
    {
        return [
            'merchant' => ['required'],
            'store'    => ['required'],
        ];
    }
    /**
     * Handle the completion of the launch store step.
     *
     * This method saves the store launch date and sets the setting as launched.
     *
     * @param  Store  $store
     * @return void
     */
    public function handle(Store $store): void
    {
        store($store);
        $this->handleStoreLaunch();
        $this->dispatchEmailVerificationJob();
        $this->updateStoreSettings();
        $this->markStoreAsOnboarded($store);
        $this->completeCloseStep();

        $this->activatePaymentMethods();
        $this->initializeDefaultThemeAndContent();
    }

    /**
     * Marks the given store as onboarded by invoking the MarkStoreAsOnboardedAction.
     *
     * @param  Store  $store The store instance to be marked as onboarded.
     * @return void
     */
    private function markStoreAsOnboarded(Store $store): void
    {
        (new MarkStoreAsOnboardedAction())->handle($store);
    }

    /**
     * Dispatches the email verification job for the current merchant.
     *
     * This method creates a new instance of the DispatchEmailVerificationJobAction
     * and calls its handle method, passing the merchant's ID as a parameter.
     *
     * @return void
     */
    private function dispatchEmailVerificationJob(): void
    {
        if ($this->merchant->email_verified !== 1) {
            (new DispatchEmailVerificationJobAction())->handle($this->merchant->id);
        }

        $this->merchant->notify(new StoreLaunchEmailNotification());
    }

    /**
     * Completes the "Close" step of the onboarding process for the merchant.
     *
     * This method uses the StepFactory to create an instance of the "Close" step
     * and marks it as complete for the current merchant.
     *
     * @return void
     */
    private function completeCloseStep(): void
    {
        $step = app(StepFactory::class)->create(OnboardingStep::CLOSE->slug(), $this->merchant);
        $step->complete();
    }

    /**
     * Handles the store launch.
     *
     * @param  MerchantService $merchantService
     * @return void
     */
    private function handleStoreLaunch(): void
    {
        $this->activateStoreStatus();
        //ignore now until first product step is completing
        //        event(new DemoOrderCreating());
    }

    /**
     * Activates the store status.
     *
     * @return void
     */
    private function activateStoreStatus(): void
    {
        if ($this->shouldForceCloseStore()) {
            return;
        }

        $this->updateStoreStatus();
        $this->updateStoreDocumentStatus();
        clearStoreCache();
    }

    /**
     * Checks if the store should remain closed.
     *
     * @return bool
     */
    private function shouldForceCloseStore(): bool
    {
        return store()->getSettingFromArray('store::status.force_close', false) ||
        !FeaturesManager::isHaveFeature('store-status');
    }

    /**
     * Updates the store status to active.
     *
     * @return void
     */
    private function updateStoreStatus(): void
    {
        store()->update(['launched_at' => now()]);

        store()->setSetting('store::status', [
            'title'   => store()->getSettingFromArray('store::status.title', 'المتجر قيد الصيانة'),
            'message' => store()->getSettingFromArray('store::status.message',
                'عذراً عزيزي العميل، المتجر حالياً قيد الصيانة و سنعاود العمل خلال فترة وجيزة'),
            'status'  => 'active',
        ]);

        if (store()->status === 'idle') {
            //if it's idle, change its status to active
            store()->update(['status' => 2, 'generate_epayment' => store()->isPaid()]);
            ChangeStoreDocumentStatus::make()->run();
        }
    }

    /**
     * Updates store document status and triggers related events.
     *
     * @return void
     */
    private function updateStoreDocumentStatus(): void
    {
        clearStoreCache(store()->getKey());
        event(new StoreStatusUpdated(store()->getCurrent()));
    }

    /**
     * Updates store-related settings after launch.
     *
     * @return void
     */
    private function updateStoreSettings(): void
    {
        store()->setSetting('store::just_got_launched_now', 1);
        store()->setSetting('store::launched', 1);
        store()->setSetting('store::survey.onboarding', CustomerEffortScorePageType::ONBOARDING_PART_2);
        store()->forgetSetting('store-competitor-data');
    }

    /**
     * Initializes the default theme and content for the store.
     *
     * This method performs the following actions:
     * 1. Creates the default theme version and sets it as the default theme.
     * 2. Sets the default content for the store using the current theme.
     *
     * @return void
     */
    private function initializeDefaultThemeAndContent(): void
    {
        $freeThemes = [config('onboarding.default_theme_id'), config('onboarding.theme_click_id')];
        $versions = ThemeCustomization::query()
            ->where('store_id', store()->getKey())
            ->get();

        $diff = collect($freeThemes)->diff($versions->pluck('theme'));
        if ($diff->isEmpty()) {
            return; // All free themes already have versions
        }

        $diff->each(function ($themeId) {
            CreateVersionAction::make()
                ->setThemeId($themeId)
                ->run();
        });
    }

    /**
     * Activates the payment methods by setting the gateway to 'salla' and running the activation process.
     *
     * @return void
     */
    private function activatePaymentMethods(): void
    {
        if (!store()->isPaid()) {
            return;
        }

        ActivateGateway::make()->setGateway('salla')->run();
    }
}
