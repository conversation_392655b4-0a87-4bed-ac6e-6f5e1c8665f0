<?php

namespace Modules\Onboarding\Actions\Steps;

use Salla\Core\Base\Action;
use Salla\Core\Entities\Store;
use Modules\Onboarding\Enums\OnboardingStep;
use Modules\Onboarding\Factories\StepFactory;
use Modules\Onboarding\Actions\Store\CreateStoreAction;
use Modules\Onboarding\Actions\User\CheckOwnerAndLogAction;
use Modules\Onboarding\Actions\User\AssignMerchantRolesAction;
use Modules\Onboarding\Actions\User\TriggerTwoFactorAuthenticationAction;
use Modules\Onboarding\Services\StoreInitializationService;
use Modules\Onboarding\Services\StoreEventDispatcherService;

/**
 * Class CompleteCreateStore
 *
 * Handles the process of completing the store creation step during onboarding.
 *
 * @property \App\Models\User $merchant The merchant performing the action.
 * @property string $entity The selected legal entity for the store.
 * @property string $country The selected country for the store.
 */
class CompleteCreateStore extends Action
{
    /**
     * Prepare validation attributes for the action data.
     */
    protected function prepareForValidation() : void
    {
        $this->country = $this->country ? strtoupper($this->country) : 'SA';
    }

    /**
     * Define validation rules for the request data.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<string>|string>
     */
    public function rules(): array
    {
        return [
            'merchant' => ['required'],
            'entity'   => ['required', 'string'],
            'country'  => ['sometimes', 'string', 'in:SA,AE']
        ];
    }

    /**
     * Handle the completion of the store creation step.
     *
     * This method orchestrates multiple actions required to complete the store creation process.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->validateOwner();

        $store = $this->createOrUpdateStore();

        $this->refreshMerchant();

        $this->dispatchStoreEvents($store);

        $this->initializeStore($store);

        $this->triggerTwoFactorAuthentication();

        $this->assignMerchantRoles();

        $this->completeSupportNumber($store);
    }

    /**
     * Validate the store owner.
     *
     * @return void
     */
    private function validateOwner(): void
    {
        (new CheckOwnerAndLogAction())->handle($this->merchant);
    }

    /**
     * Assign roles to the merchant.
     *
     * @return void
     */
    private function assignMerchantRoles(): void
    {
        (new AssignMerchantRolesAction())->handle($this->merchant);
    }

    /**
     * Trigger two-factor authentication for the merchant.
     *
     * @return void
     */
    private function triggerTwoFactorAuthentication(): void
    {
        (new TriggerTwoFactorAuthenticationAction())->handle($this->merchant);
    }

    /**
     * Create or update the store in the database.
     *
     * @return Store
     */
    private function createOrUpdateStore(): Store
    {
        return (new CreateStoreAction())->handle(
            $this->merchant,
            $this->entity,
            $this->country
        );
    }

    /**
     * Refresh the merchant instance to update its associations.
     *
     * @return void
     */
    private function refreshMerchant(): void
    {
        $this->merchant = $this->merchant->refresh();
    }

    /**
     * Dispatch store creation events using the event dispatcher service.
     *
     * @param Store $store
     * @return void
     */
    private function dispatchStoreEvents(Store $store): void
    {
        $eventDispatcher = new StoreEventDispatcherService();
        $eventDispatcher->dispatchStoreCreationEvents($store, $this->merchant);
    }

    /**
     * Initialize store settings using the initialization service.
     *
     * @param Store $store
     * @return void
     */
    private function initializeStore(Store $store): void
    {
        $initializationService = new StoreInitializationService();
        $initializationService->initializeStore($store, $this->merchant);
    }

    /**
     * Completes the support number setup for the given store.
     *
     * @param Store $store The store entity for which the support number is being completed.
     * @return void
     */
    private function completeSupportNumber(Store $store): void
    {
        $fullPhone = $this->buildFullPhoneNumber($store);

        $step = app(StepFactory::class)->create(OnboardingStep::SUPPORT_NUMBER->slug(), $this->merchant);
        $step->complete(['full_phone' => $fullPhone]);
    }

    /**
     * Build the full phone number for the store.
     *
     * @param Store $store
     * @return string
     */
    private function buildFullPhoneNumber(Store $store): string
    {
        return $store->user->mobile_code_country . $store->user->mobile;
    }
}
