<?php

namespace Modules\Onboarding\Actions\Store;

use App\Models\User;
use Modules\Onboarding\Services\ScrappedDataService;
use Salla\Core\Enum\Plan;
use Illuminate\Support\Str;
use Salla\Core\Base\Action;
use Salla\Core\Entities\Store;
use Illuminate\Support\Facades\DB;

/**
 * Class CreateStoreAction
 *
 * Handles the creation or update of a store and associates the user with the store.
 */
class CreateStoreAction extends Action
{

    const COUNTRY_CURRENCY_MAP = [
        'SA' => 'SAR',
        'AE' => 'AED',
    ];

    /**
     * Create or update a store in the database and associate the merchant with it.
     *
     * @param  User   $merchant The merchant requesting the store creation or update.
     * @param  string $entity   The legal entity of the store.
     * @return Store  The created or updated store instance.
     */
    public function handle(User $merchant, string $entity, string $country): Store
    {
        $store = DB::transaction(function () use ($merchant, $entity, $country) {
            $store = $this->createOrUpdateStore($merchant, $entity, $country);
            $store->trackUtm($this->getUtmFields($merchant));
            $this->associateMerchantWithStore($merchant, $store);

            return $store;
        });

        (new ScrappedDataService())->mapScrappedData();

        return $store;
    }

    /**
     * Create or update the store record in the database.
     *
     * @param  User   $merchant The merchant requesting the store creation or update.
     * @param  string $entity   The legal entity of the store.
     * @return Store  The created or updated store instance.
     */
    private function createOrUpdateStore(User $merchant, string $entity, string $country): Store
    {
        return Store::updateOrCreate(
            ['id' => $merchant->store_id],
            [
                'plan'          => Plan::create(Plan::BASIC)->value(),
                'has_onboarded' => 0,
                'theme'         => '5541564',
                'theme_id'      => 25,
                'entity'        => $entity,
                'kyc_country'   => $country,
                'currency'      => self::COUNTRY_CURRENCY_MAP[$country] ?? 'SAR',
                'username'      => Str::uuid(),
                ...$this->defaultValues(),
                ...$this->getUtmFields($merchant),
            ]
        );
    }

    /**
     * Associate the merchant with the created or updated store.
     *
     * @param  User   $merchant The merchant to associate with the store.
     * @param  Store  $store    The store to associate with the merchant.
     * @return void
     */
    private function associateMerchantWithStore(User $merchant, Store $store): void
    {
        if ($merchant->store_id === $store->id) {
            return;
        }

        (new AssociateUserWithStoreAction())->handle($merchant, $store);
    }

    /**
     * Get UTM fields for the store.
     *
     * @return array The UTM fields.
     */
    private function getUtmFields(User $merchant): array
    {
        $utmData = $merchant->utm;

        return [
            'utm_campaign' => $utmData?->utm_campaign,
            'utm_medium'   => $utmData?->utm_medium,
            'utm_source'   => $utmData?->utm_source,
            'utm_content'  => $utmData?->utm_content,
        ];
    }

    private function defaultValues(): array
    {
        return [
            'show_products_offers' => 0,
        ];
    }
}
