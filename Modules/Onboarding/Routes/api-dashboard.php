<?php

use Illuminate\Support\Facades\Route;
use Modules\DashboardApi\Http\Middleware\TokenableMiddleware;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */
Route::prefix('/v2/onboarding')
    ->name('api.v2.onboarding.')
    ->namespace('V2')
    ->middleware([TokenableMiddleware::class])
    ->group(function () {
        // persona country question
        Route::get('/persona-country', 'PersonaStoreCountryController')->name('persona.country');
        // scrapper endpoints
        Route::post('/persona-competitor', 'SaveScrappedDataController')->name('persona.competitor');

        // OTP endpoints
        Route::post('/otp', 'OtpController')->name('send-otp');

        // Domain endpoints
        Route::post('domain/check', 'DomainCheckController')->name('domain.check');
        Route::get('domain/toggle-view', 'ToggleDomainViewController')->name('domain.toggle-view');

        // Product endpoints
        Route::post('product/image', 'UploadFirstProductImageController')->name('product.image');
        Route::post('product/file', 'UploadFirstProductFileController')->name('product.file');
        Route::get('product/import/upload-url', 'ImportProductUrlController@getTempUplaodUrl')
            ->name('product.import');
        Route::get('product/import/uploaded-file-url', 'ImportProductUrlController@getUploadedFileUrl')
            ->name('product.import.uploaded-file-url');
        Route::get('product/import/file-info', 'ImportProductUrlController@getFileInfo')->name('product.import.file-info');
        // Shipping endpoints
        Route::get('shipping/companies', 'ShippingInitialController@getShippingLogos')->name('shipping.companies');
        Route::post('shipping/start', 'ShippingInitialController@markInitialViewed')->name('shipping.start');
        Route::post('shipping/resolve', 'ResolveShippingController')->name('shipping.resolve');

        Route::post('shipping/resolve/confirm', 'ResolveShippingController@confirm')->name('shipping.resolve.confirm');

        // KYC endpoints
        Route::get('kyc/info', 'KycInfoController')->name('kyc.info');

        // Onboarding endpoints
        Route::get('/status', 'OnboardingController@status')->name('status');
        Route::post('/{stepSlug}', 'OnboardingController@complete')->name('complete');
        Route::post('/{stepSlug}/delete', 'OnboardingController@delete')->name('delete');
        Route::get('/{stepSlug}', 'OnboardingController@data')->name('data');
    });
