<?php

return [
    'initial' => [
        'title'       => 'Initial',
        'description' => '',
    ],
    'persona-questions' => [
        'title'       => 'Persona Questions',
        'description' => '',
        'questions'   => [
            'legal-entity'      => [
                'question'    => 'Choose your legal entity',
                'description' => 'We\'ll guide you to launch your store the right way.',
            ],
            'business-industry' => [
                'question'    => 'Almost there - What do you plan to sell?',
                'description' => 'Pick the industries that match your products or services.',
            ],
            'store-country' => [
                'question'    => 'Where do you sell?',
                'description' => 'Locate the country from which you will issue your government documents, '
                    . 'such as your license and commercial register, '
                    . 'to help us prepare your store according to its regulations.',
            ]
        ],
        'options'     => [
            'legal-entity' => [
                'person'                => 'Individual',
                'company'               => 'Sole proprietorship',
                'firm'                  => 'Company',
                'charity'               => 'Charity',
            ],
            'store-country' => [
                'sa'    => '🇸🇦 Saudi Arabia',
                'ae'    => '🇦🇪 United Arab Emirates',
                'other' => 'Other'
            ]
        ]
    ],
    'support-number' => [
        'title'       => 'Clients Support Channels',
        'description' => '',
    ],
    'domain' => [
        'title'       => 'Store\'s Domain',
        'description' => '',
    ],
    'shipping' => [
        'title'       => 'Shipping Location',
        'description' => '',
    ],
    'launch' => [
        'title'       => 'Launch the Store',
        'description' => '',
    ],
    'close' => [
        'title'       => 'Close',
        'description' => '',
    ],
    'kyb' => [
        'title'       => 'kyb',
        'description' => '',
    ],
    'theme' => [
        'title'       => 'store theme',
        'description' => '',
    ],

    'messages' => [
        'support_number' => [
            'full_phone'        => 'Phone number',
            'sms_code'          => 'Verification code',
            'country_code'      => 'Country code',
            'sms_code_required' => 'Verification code is required',
            'invalid_sms_code'  => 'Invalid verification code',
            'same_owner_phone'  => 'This is the same owner phone number and it is already verified.',
            'otp'               => 'Enter otp code sent to: :number',
        ],

        'domain' => [
            'custom_domain_not_available'         => 'Your plan did not have custom domain',
            'domain'                              => 'Domain',
            'domain_not_available'                => 'This domain is not available. You can change it or choose form following suggestions:',
            'domain_not_available_no_suggestions' => 'This domain is not available. Please change it and try again.',
            'toggle_view_not_allowed'             => 'You are not allowed to change views',
        ],

        'product' => [
            'mimes'                    => 'The file format is not supported.',
            'image_mimes'              => 'Only images of type: jpg, png, jpeg, gif are allowed.',
            'file'                     => 'Product file',
            'image'                    => 'Product image',
            'image_dimensions'         => 'The image dimensions must not be less than 100x250 pixels.',
            'name_required'            => 'The product name is required.',
            'name_string'              => 'The product name must consist of letters.',
            'price'                    => 'The product price',
            'price_required'           => 'The product price is required.',
            'price_numeric'            => 'The product price must be a number.',
            'price_min'                => 'The product price must be greater than zero.',
            'price_max'                => 'The product price cannot exceed :price',
            'cost_price_required'      => 'The financial support amount is required.',
            'cost_price_numeric'       => 'The financial support amount must be a number.',
            'cost_price_min'           => 'The financial support amount must be greater than zero.',
            'cost_price_max'           => 'The financial support amount must be less than :price',
            'quantity_integer'         => 'The product quantity must be an integer greater than zero.',
            'quantity_min'             => 'The product quantity must be greater than zero.',
            'product_type_required'    => 'The product type is required.',
            'product_type_string'      => 'The product type must consist of letters.',
            'product_image_string'     => 'The product image is invalid. Please try uploading it again.',
            'calories_required'        => 'The calorie count is required.',
            'code_content_required'    => 'The digital code is required.',
            'digital_files_required'   => 'Please add a valid file',
            'file_name_required'       => 'Please add a file',
            'file_size_required'       => 'Please add a file',
            'file_url_required'        => 'Please add a file',
            'file_url_type'            => 'Please add a valid file',
            'file_size_numeric'        => 'The file size must be a number',
            'target_amount_required'   => 'The financial support duration is required',
            'target_amount_in'         => 'The financial support duration must be 3, 6, or 12 months',
            'is_default_required'      => 'The donation default amount is required',
            'donation_amount_required' => 'The donation amount is required',
            'donation_amount_numeric'  => 'The donation amount must be a number',
            'donation_amount_distinct' => 'The donation amount must not be duplicated',
            'donation_amount_min'      => 'The donation amount must be greater than or equal to 1',
            'donation_amount_max'      => 'The donation amount cannot exceed :price',
        ]
    ]
];
