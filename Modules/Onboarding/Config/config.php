<?php

use Modules\Onboarding\Enums\OnboardingStep;

return [
    'name'  => 'Onboarding',

    'steps' => [
        [
            'order'       => OnboardingStep::INITIAL->value,
            'slug'        => OnboardingStep::INITIAL->slug(),
            'class'       => \Modules\Onboarding\Steps\InitialStep::class,
            'is_optional' => false,
            'parent'      => 'general',
        ],
        [
            'order'       => OnboardingStep::PERSONA_QUESTIONS->value,
            'slug'        => OnboardingStep::PERSONA_QUESTIONS->slug(),
            'class'       => \Modules\Onboarding\Steps\PersonaQuestionsStep::class,
            'is_optional' => false,
            'parent'      => 'general',
        ],
        [
            'order'       => OnboardingStep::CREATE_STORE->value,
            'slug'        => OnboardingStep::CREATE_STORE->slug(),
            'class'       => \Modules\Onboarding\Steps\CreateStoreStep::class,
            'is_optional' => false,
            'parent'      => 'general',
        ],
        [
            'order'       => OnboardingStep::STORE_IDENTITY->value,
            'slug'        => OnboardingStep::STORE_IDENTITY->slug(),
            'class'       => \Modules\Onboarding\Steps\StoreIdentityStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::SUPPORT_NUMBER->value,
            'slug'        => OnboardingStep::SUPPORT_NUMBER->slug(),
            'class'       => \Modules\Onboarding\Steps\SupportNumberStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::DOMAIN->value,
            'slug'        => OnboardingStep::DOMAIN->slug(),
            'class'       => \Modules\Onboarding\Steps\DomainStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::SHIPPING->value,
            'slug'        => OnboardingStep::SHIPPING->slug(),
            'class'       => \Modules\Onboarding\Steps\ShippingStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::PRODUCT->value,
            'slug'        => OnboardingStep::PRODUCT->slug(),
            'class'       => \Modules\Onboarding\Steps\FirstProductStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::KYC->value,
            'slug'        => OnboardingStep::KYC->slug(),
            'class'       => \Modules\Onboarding\Steps\KycStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::THEME->value,
            'slug'        => OnboardingStep::THEME->slug(),
            'class'       => \Modules\Onboarding\Steps\ThemeStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::LAUNCH->value,
            'slug'        => OnboardingStep::LAUNCH->slug(),
            'class'       => \Modules\Onboarding\Steps\LaunchStoreStep::class,
            'is_optional' => false,
            'parent'      => 'settings',
        ],
        [
            'order'       => OnboardingStep::CLOSE->value,
            'slug'        => OnboardingStep::CLOSE->slug(),
            'class'       => \Modules\Onboarding\Steps\CloseStep::class,
            'is_optional' => false,
            'parent'      => 'general',
        ],
    ],

    'default_theme_id' => env('THEME_RAED_ID', 4),
    'theme_click_id'   => env('THEME_CLICK_ID', 25),
];
