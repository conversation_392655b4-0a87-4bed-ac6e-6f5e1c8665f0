<?php

return [
    'otp' => 'رمز التوثيق',
    'user_id' => 'معرف المستخدم',
    'generic_error' => 'حدث خطأ. من فضلك حاول مرة أخرى لاحقا!',
    'otp_sent' => 'تم إرسال الرمز إلى جوالك',
    'strong_password_error_msg' => 'يجب أن تحتوي كلمة المرور على :message على الأقل.',
    'one_number' => 'رقم واحد',
    'one_symbol' => 'رمز واحد',
    'one_capital_letter' => 'حرف كبير واحد',
    'one_small_letter' => 'حرف صغير واحد',
    'invalid_value' => 'القيمة المدخلة غير صالحة',
    'invalid_otp' => 'رمز التحقق غير صحيح، الرجاء إعادة المحاولة',
    'otp_verified' => 'تم التحقق الرمز بنجاح',
    'attempts_exceeded' => 'لقد استنفذت عدد محاولات إعادة الإرسال يرجى التواصل معنا لحل المشكلة',
    'user_has_store' => 'المستخدم لديه متجر مسجل مسبقاً',
    'valid_coupon' => 'مبروك عليك خصم :discount',
    'invalid_coupon' => 'الكوبون المستخدم غير صحيح',
    'entity' => 'الكيان',
    'options' => 'أجوبة الأسئلة الاختيارية',
    'sms_generic_error' => 'هناك مشكلة في إرسال الرسائل. تواصل معنا لمساعدتك',
    'otp_tries_exceeded' => 'لقد تخطيت عدد المرات المسموح بها لإعادة الإرسال. الرجاء المحاولة بعد :minutes دقيقة',
    'activities' => 'فئة التجارة',
    'about' => 'وصف النشاط',
    'store_name' => 'اسم المتجر',
    'store_url' => 'رابط المتجر',
    'otp_mobile' => ' سلة | تأكيد رقم الجوال',
    'otp_email' => ' سلة | تأكيدالبريد الإلكتروني',
    "password_symbols" => "يجب أن تحتوي كلمة المرور على حرف خاص واحد على الأقل.",
    "confirm_update" => 'تم تحديث رقم الجوال الجديد بنجاح',
    'invitation' => [
        'expired' => 'انتهت صلاحية الدعوة',
        'used' => 'تم استخدام الدعوة مسبقاً',
        'no_store_id' => 'لا يوجد متجر مرتبط بالدعوة',
        'invalid_store_id' => 'ليس لديك صلاحية الوصول لهذا المتجر',
        'invalid_user_id' => 'ليس لديك صلاحية الوصول لهذا المستخدم',
    ],
    'on_boarding' => [
        'step_name'   => 'خطوة',
        'title'       => 'متبقي لك خطوات بسيطة لتدشين متجرك وبدء رحلتك في التجارة مع سلة!',
        'finished'       => 'أكملت كل الخطوات الأساسية ومتجرك الآن جاهز للتدشين!',
        'steps_name'  => 'خطوات',
        'launch'      => 'تدشين المتجر',
        'skip'        => 'تخطي',

        'side_menu_title' => 'استكمل خطوات تدشين متجرك',

        'launched' => [
            'title' => 'متجر مبارك!',
            'text' => 'تم تدشين متجرك بنجاح. انطلق، تاجر واستمتع بتجارتك مع منصة سلة!',
            'action' => 'اكتشف المنصة',
        ],

        'steps' => [
            'info' => [
                'name' => 'أضف معلومات متجرك',
                'icon' => 'sicon-file-cabinet',
            ],

            'product' => [
                'name' => 'أضف أول منتج لمتجرك',
                'icon' => 'sicon-t-shirt',
            ],

            'first_product' => [
                'name'   => 'أضف أول منتج',
                'action' => 'اضافة منتج',
            ],

            'shipping' => [
                'name' => 'فعل خيار الشحن',
                'icon' => 'sicon-shipping-fast',
            ],

            'shipping_location' => [
                'name'   => 'حدد موقع استلام الشحنات',
                'action' => 'تحديد الموقع',
            ],

            'shipping_company' => [
                'name'   => 'اختر شركات الشحن',
                'action' => 'تحديد الشركات',
            ],

            'payment' => [
                'name' => 'فعل المدفوعات الإلكترونية',
                'icon' => 'sicon-debit-card-back',
            ],

            'store_verification' => [
                'name'   => 'وثق متجرك',
                'action' => 'توثيق المتجر',
            ],

            'payment_method' => [
                'name'   => 'فعل المدفوعات الإلكترونية',
                'action' => 'تفعيل المدفوعات',
                'on_basic_plan' => 'يجب الاشتراك في الباقات المدفوعة لتفعيل بوابات الدفع.',
                'on_basic_plan_charity' => 'يجب توثيق متجرك لتفعيل بوابات الدفع.',
            ],

            'design' => [
                'name'   => 'اختر تصميم متجرك',
                'icon' => 'sicon-browser-alt',
            ],

            'theme' => [
                'name'   => 'اختر قالب التصميم',
                'action' => 'اختيار التصميم',
            ],

            'theme_customization' => [
                'name'   => 'خصص التصميم',
                'action' => 'تخصيص التصميم',
                'on_basic_plan' => 'يجب الاشتراك في الباقات المدفوعة لتفعيل تخصيص التصميم.',
                'on_basic_plan_charity' => 'يجب توثيق متجرك لتفعيل تخصيص التصميم.',
            ],

            'plan' => [
                'name' => 'اكتشف باقات سلة',
                'icon' => 'sicon-shopping-bag2',
            ],
        ],
    ],
];
