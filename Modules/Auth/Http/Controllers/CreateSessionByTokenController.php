<?php

namespace Modules\Auth\Http\Controllers;

use <PERSON><PERSON>\LaravelOtp\Services\OtpService;

class CreateSessionByTokenController
{
    public function __invoke()
    {
        if (auth()->guest()) {
            return responder()->error('user-not-found')->respond(400);
        }

        if (request()->attributes->get('token_verified')) {
            app(OtpService::class)->setReceipent(auth()->user())->pass2fa();
        }

        session()->forget('current_store');

        if (feature('multi-stores')->isHaveFeature() && store()->getId()) {
            /** we are using it on the web session @see \Modules\Auth\Http\Middleware\ResolveCurrentStoreSetting */
            session()->put('current_store', [
                'id' => store()->getId(),
                'user_id' => auth()->id()
            ]);
        }

        // this needed to be checked for it in the middleware in
        // expert-module/Http/Middleware/PartnerRequestAccessMiddleware.php
        // to handle old dashboard as well
        if (request()->attributes->get('partner_request_access')) {
            session(['partner_request_access' => request()->attributes->get('partner_request_access')]);
        }

        return responder()->success();
    }
}
