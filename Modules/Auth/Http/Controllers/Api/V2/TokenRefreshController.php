<?php

namespace Modules\Auth\Http\Controllers\Api\V2;

use App\Models\User;
use Carbon\Carbon;
use Modules\Auth\Http\Requests\Api\TokenRefreshRequest;
use Illuminate\Routing\Controller;
use Modules\DashboardApi\Strategies\Api\Authentication\SallaEncryptionStrategy;
use Salla\LaravelOtp\Services\OtpService;
use Symfony\Component\HttpFoundation\Response;
use Salla\Encryption\Facades\Encryption;

class TokenRefreshController extends Controller
{
    public function __invoke(TokenRefreshRequest $request)
    {
        try {
            $data = encryption()->decode($request->input('token'), (new SallaEncryptionStrategy)->getConfig());

            if (!array_key_exists('jti', $data->getClaims()) || !$data->get('jti')) {
                return responder()->error('not_refresh_token')->respond(Response::HTTP_UNAUTHORIZED);
            }

            $userId = optimus()->decode($data->get('user'));
            $user = $userId ? User::find($userId) : null;

            if (!$user) {
                return responder()->error('user_not_found')->respond(Response::HTTP_UNAUTHORIZED);
            }

            if ($user->force_otp_after && Carbon::parse($user->force_otp_after)->gt($data->get('iat'))) {
                return responder()->error('token_expired')->respond(Response::HTTP_UNAUTHORIZED);
            }

            $tokenVerified = (bool)($data->get('verified') ?? 0);
            if (!$tokenVerified) {
                return responder()->error('error')->respond(Response::HTTP_UNAUTHORIZED);
            }

            $claims = [
                'user'  => optimus()->encode($user->id),
                'owner' => $user->store->owner?->id ? optimus()->encode($user->store->owner->id ):  null,
                'verified' => true,
                'source' => request()->attributes->get('source', request('source', 'dashboard')),
                'fingerprint' => app(OtpService::class)->setReceipent($user)->getFingerprint(),
            ];

            if (array_key_exists('store', $data->getClaims()) && $data->get('store')) {
                $claims['store'] = $data->get('store');
            }

            $token = SallaEncryptionStrategy::encode($claims);
            $refreshToken = SallaEncryptionStrategy::encode($claims, ['jti' => 1]);
        } catch (\Exception $e) {
            return responder()->error('error', $e->getMessage())
                ->respond(Response::HTTP_BAD_REQUEST);
        }

        return responder()->success([
            'token' => $token,
            'refresh_token' => $refreshToken,
            'expire_at' => now()->addMinutes(
                config('salla-encryption.expiration.public')
            )->format('Y-m-d H:i:s'),
        ])->respond(Response::HTTP_OK);
    }
}
