<?php

namespace Modules\Auth\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class ResolveCurrentStoreSetting
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $this->loadCurrentStore();

        if (!$request->hasCookie('s_domains_token') && $token = generateHelpToken()) {
            // Putting the cookies without securing them is potentially dangerous, but ha, consult @zyaid on that :(
            Cookie::queue('s_domains_token', $token, 60 * 24 * 7, null, config('session.domain'), true, false);
        }

        return $next($request);
    }

    private function loadCurrentStore(): void
    {
        if (auth()->guest()) {
            return;
        }

        if ($this->shouldLoadFromSession()) {
            store()->loadFromId(session('current_store.id'));
            return;
        }

        if (auth()->user()->store_id) {
            store()->loadFromId(auth()->user()->store_id);
        }
    }

    private function shouldLoadFromSession(): bool
    {

        // TODO: for more security add extra validation that the current store belongs to the current user,
        //to avoid cases when the current user removed from the store but the session still exists.
        return store()->getSetting('features::multi-stores')
            && !isLegacyDashboard()
            && store()->getId()
            && session('current_store.id')
            && session('current_store.user_id') === auth()->id()
            && !request()->routeIs('cp.auth.token-session');
    }
}
