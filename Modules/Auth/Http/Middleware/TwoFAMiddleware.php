<?php


namespace Modules\Auth\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\Auth\Traits\Auth\HasSupportAccess;
use Modu<PERSON>\Auth\Traits\FindUser;
use Mo<PERSON><PERSON>\Auth\Traits\RedirectWithContentType;
use Salla\Core\Enum\TwoFaStatus;
use Salla\LaravelOtp\Services\OtpService;
use Modules\Auth\Enum\AuthRouteMap;
use Salla\Logger\Facades\Logger;

class TwoFAMiddleware
{
    use FindUser, HasSupportAccess, RedirectWithContentType;

    public function handle(Request $request, Closure $next)
    {
        $user = $this->user();

        if (!$user || app()->environment('testing')) {
            return $next($request);
        }

        if (
            request()->attributes->get('token_verified') ||
            app(OtpService::class)->setReceipent($user)->sessionIsVerified() ||
            $this->isSupportOrPartnerSession($user)
        ) {
            return $next($request);
        }

        if ($this->isDashboardOrLogsRequest($request)) {
            return $next($request);
        }

        if (
            $user->two_fa_status === TwoFaStatus::ACTIVE &&
            !Str::startsWith($request->route()?->getName(), ['api.v2.auth.otp'])
        ) {            
            $this->log(
                'Redirect To OTP', 
                $request->fullUrl(),
                request()->attributes->get('token_verified'),
                app(OtpService::class)->setReceipent($user)->sessionIsVerified(),
                $this->isSupportOrPartnerSession($user),
                $this->isDashboardOrLogsRequest($request)
            );

            return $this->redirect(AuthRouteMap::getOtpRoute([
                'source' => '2fa', 'intended_to' => $request->fullUrl()
            ]));
        }

        return $next($request);
    }

    private function isDashboardOrLogsRequest(Request $request)
    {
        return in_array($request->route()->getName(), ['cp.operations-logs.latest', 'api.v1.notifications.counter',
            'cp.dashboard.summery', 'cp.dashboard.orders', 'impex.import.queue.check-status'
        ]);
    }

    private function log(string $message, $fullUrl, $tokenVerified, $sessionIsVerified, $isSupportOrPartnerSession, $isDashboardOrLogsRequest){
        Logger::setAdditions('redirect_conditons', [
            'token_verified' => $tokenVerified,
            'session_verified' => $sessionIsVerified,
            'partner_session' => $isSupportOrPartnerSession,
            'isDashboardOrLogsRequest' => $isDashboardOrLogsRequest,
            'store_username' => store()->username

        ]);
    }
}
