<?php

namespace Modules\Auth\Http\Requests\Api;

use App\Models\Country;
use App\Models\User;
use Illuminate\Validation\Rule;
use Salla\Users\Enum\UserStatusEnum;
use Salla\Users\Models\UserInvitation;
use Modules\Auth\Rules\StrongPasswordRule;
use Salla\Users\Rules\InvitedEmailRule;
use Salla\Users\Rules\MobileUniqueRule;
use Salla\Users\Rules\RoleNotExistRule;
use Salla\Users\Http\Requests\UpdateUserRequest;
use Salla\Core\Rules\EmailCheckerRule;
use Modules\Auth\Rules\UserRegisterDevelopmentStoreRule;

class UserRegisterRequest extends UpdateUserRequest
{
    public ?UserInvitation $invitation = null;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $invitation = $this->invitation;
        $rules      = [
            'country_code' => ['required', 'size:2', 'string'],
            'name'         => ['bail', 'required', 'string', 'min:2', 'max:250'],
            'email'        => ['required', 'email', 'max:250',
                Rule::unique('users', 'email')->ignore($this->id ?? 0), new EmailCheckerRule],
            'mobile'       => ['bail', 'required', 'numeric', 'phone:' . strtoupper($this->get('country_code')),
                new MobileUniqueRule($this->mobile_code_country, $this->id, $this->store_id ?? store()->getId())],
            'password'     => ['bail', 'required', 'string', 'min:' . config('salla.minimum_password_length'), new StrongPasswordRule],
            'coupon_code'  => ['nullable', 'string'],
            'dev_store'    => [
                  'nullable',
                  new UserRegisterDevelopmentStoreRule($this),
            ],
        ];

        if ($invitation) {
            $rules['invitation_id'] = [
                'bail',
                function ($attribute, $value, $fail) use ($invitation) {
                    if ($invitation->user_id && $invitation->used == 1) {
                        return $fail(__t('users::user.messages.invitation_is_used_before'));
                    }
                },
                function ($attribute, $value, $fail) use ($invitation) {
                    if ($invitation->isExpired()) {
                        return $fail(__t('users::user.messages.invitation_link_is_expired'));
                    }
                },
            ];

            $rules['role'] = ['required', 'integer', new RoleNotExistRule()];

            $rules['email'] = array_merge($rules['email'], [
                function ($attribute, $value, $fail) use ($invitation) {
                    if ($invitation
                        && $invitation->user
                        && $invitation->user->email != $this->get('email')) {
                        return $fail(__t('users::user.messages.emails_not_identical'));
                    }

                    $the_email_already_registered = User::query()->where('email', $this->get('email'))
                        ->whereHas('roles', function ($q) {
                            $q->where('model_has_roles.status', '<>', UserStatusEnum::DELETED);
                        })->exists();

                    if ($the_email_already_registered) {
                        return $fail(__t('users::user.messages.the_email_already_registered'));
                    }
                },
            ]);
        }

        return $rules;
    }

    public function prepareForValidation()
    {
        // set mobile_code_country as a number
        $mobile_code_country = Country::query()
            ->where('country_code', $this->get('country_code'))
            ->value('mobile_code_country');

        //todo:: enhance this way
        if ($this['invitation'] && is_numeric($this['invitation'])) {
            $this['invitation'] = optimus()->decode($this['invitation']);
            $this['role_id'] = $this['role_id'] && is_numeric($this['role_id']) ? optimus()->decode($this['role_id']) : 0;
            $this['store_id'] = $this['store_id'] && is_numeric($this['store_id']) ? optimus()->decode($this['store_id']) : 0;

            $this->invitation = UserInvitation::find($this['invitation']);
        }

        $user_id = $this->invitation->user_id ?? ($this->get('id') ?? 0);

        $this->merge([
            'mobile_code_country' => $mobile_code_country,
            'id'                  => $user_id,
        ]);

        if ($this->invitation) {  // set invitation link relative needed data
            $this->merge([
                'role'          => $this->invitation->role_id,
                'invitation_id' => $this->invitation->id,
                'store_id'      => $this->invitation->store_id,
            ]);
        }
    }
}
