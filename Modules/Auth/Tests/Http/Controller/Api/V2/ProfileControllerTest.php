<?php

namespace Modules\Auth\Tests\Http\Controller\Api\V2;

use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Modules\Auth\Entities\UserChangeRequest;
use Modules\Auth\Enum\UserChangeRequestStatus;
use Modules\Auth\Http\Middleware\ReCaptchaV3;
use Modules\DashboardApi\Strategies\Api\Authentication\SallaEncryptionStrategy;
use Modules\DashboardApi\Transformers\UserTransformer;
use Salla\Encryption\Facades\Encryption;
use Symfony\Component\HttpFoundation\Response;
use Tests\CreateStore;
use Tests\TestCase;

class ProfileControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->withoutMiddleware(ReCaptchaV3::class);
    }

    public function test_if_url_info_user_not_exist()
    {
        $response = $this->getJson('api/v2/user/info');
        $response->assertStatus(Response::HTTP_NOT_FOUND);
    }

    public function test_if_user_not_auth()
    {
        $response = $this->getJson('admin/v2/auth/user/info');
        $response->assertStatus(Response::HTTP_UNAUTHORIZED);
    }

    public function test_get_info_user()
    {
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->getJson('admin/v2/auth/user/info');
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
            'data' => transformation($this->createUser(), UserTransformer::class)->transform(),
        ]);
    }

    public function test_url_update_profile_user_not_exist()
    {
        $response = $this->putJson('api/v2/user');
        $response->assertStatus(Response::HTTP_NOT_FOUND);
    }

    public function test_validation_update_profile_user()
    {
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/user', []);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => 'alert.invalid_fields',
            ]
        ]);
    }

    public function test_update_profile_for_user_name()
    {
        Bus::fake();
        $user = $this->createUser();
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/user', [
            'name' => $name = fake()->name,
        ]);
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
            'data' => [
                'status' => 'updated',
            ]
        ]);
        $user->refresh();
        $this->assertEquals($name, $user->first_name . ' ' . $user->last_name);
    }

    public function test_update_profile_for_user_email()
    {
        Bus::fake();
        $user = $this->createUser();
        $email = fake()->email;
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/user', [
            'name' => fake()->name,
            'email' => $email,
        ]);
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
            'data' => [
                'status' => 'updated',
            ]
        ]);
        $user->refresh();
        $this->assertFalse((bool)$user->email_verified);
        $this->assertEquals($email, $user->email);
    }

    public function test_update_profile_for_user_mobile()
    {
        Bus::fake();
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/user', [
            'name' => fake()->name,
            'mobile_number' => '0510008765',
            'mobile_code_country' => '966',
            'mobile_country_name' => 'SA',
        ]);
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
            'data' => [
                'status' => 'pending',
            ]
        ]);
    }

    public function test_confirm_update_for_user_mobile()
    {
        $user = $this->createUser();

        UserChangeRequest::query()
            ->where('user_id', $user->id)
            ->where('status', UserChangeRequestStatus::PENDING)
            ->update(['status' => UserChangeRequestStatus::CANCELLED]);

        $changeRequest = UserChangeRequest::query()->create([
            'user_id' => $user->id,
            'status' => UserChangeRequestStatus::PENDING,
            'old_values' => ['mobile' => $user->mobile],
            'new_values' => ['mobile' => '510008765'],
        ]);

        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")
            ->putJson('admin/v2/auth/user/change-request/' . $changeRequest->getRouteKey(),
                ['otp' => app('otp')->generate('merchant::auth.+966510008765')]);
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['status' => Response::HTTP_OK, 'success' => true]);

        $user->refresh();
        $this->assertEquals($user->mobile, '510008765');
    }

    public function test_url_change_password_user_not_exist()
    {
        $response = $this->putJson('api/v2/update-password');
        $response->assertStatus(Response::HTTP_NOT_FOUND);
    }

    public function test_validation_old_password_is_required()
    {
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/update-password', []);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
        ]);
    }

    public function test_validation_wrong_old_password()
    {
        $token = $this->createToken();
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/update-password', [
            'old_password' => fake()->regexify('[A-Za-z0-9!@#$%^&*()_+]{12,16}') . rand(1111, 9999) . '#@AS',
        ]);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
            'error' => [
                'message' => 'alert.invalid_fields',
            ]
        ]);
    }

    public function test_update_password_for_user()
    {
        $user = $this->createUser();
        $token = $this->createToken();
        $password = fake()->regexify('[A-Za-z0-9!@#$%^&*()_+]{12,16}') . rand(1111, 9999) . '#@AS';
        $response = $this->withHeader('Authorization', "Bearer $token")->putJson('admin/v2/auth/update-password', [
            'old_password' => '123456',
            'password' => $password,
            'password_confirmation' => $password,
        ]);
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
            'data' => [
                'code' => 'success',
                'message' => __('global.password_changed_successfully'),
            ],
        ]);
        $user->refresh();
        $this->assertTrue(Hash::check($password, $user->password));
    }

}