<?php

namespace Modules\Auth\Tests\Http\Controller\Api\V2;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Modules\Auth\Http\Middleware\ReCaptchaV3;
use Symfony\Component\HttpFoundation\Response;
use Tests\CreateStore;
use Tests\TestCase;

class LoginControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->withoutMiddleware(ReCaptchaV3::class);
    }

    public function test_route_login_not_exist()
    {
        $response = $this->postJson('api/v2/login', [
            'email' => fake()->email
        ]);
        $response->assertStatus(Response::HTTP_NOT_FOUND);
    }

    public function test_validation_login()
    {
        $response = $this->withHeader('Accept-Language', 'en')->postJson('admin/v2/auth/login', [
            'email' => fake()->email
        ]);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => 'alert.invalid_fields',
            ]
        ]);
    }

    public function test_login_user_has_been_success()
    {
        $user = $this->createUser();
        $response = $this->withHeader('Accept-Language', 'en')->postJson('admin/v2/auth/login', [
            'email' => $user->email,
            'password' => '123456',
        ]);

        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
            'data' => [
                'intended_to' => '/',
            ],
        ]);
    }

    public function test_too_many_login_attempts()
    {
        RateLimiter::shouldReceive('tooManyAttempts')->once()->andReturn(true);
        RateLimiter::shouldReceive('availableIn')->once()->andReturn(60);

        $user = $this->createUser();
        $response = $this->withHeader('Accept-Language', 'en')->postJson('admin/v2/auth/login', [
            'email' => $user->email,
            'password' => '123456',
        ]);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => trans('auth.throttle.inMinutes', ['minutes' => 1]),
            ]
        ]);
    }


    public function test_login_user_has_been_unsuccessful()
    {
        $user = $this->createUser();
        $response = $this->withHeader('Accept-Language', 'en')->postJson('admin/v2/auth/login', [
            'email' => $user->email,
            'password' => '*********',
        ]);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => __('auth.failed'),
            ]
        ]);
    }

    public function test_login_user_account_is_not_activated()
    {
        $user = $this->createUser();
        $user->update(['status' => 'inactive', 'role' => 'user']);

        $response = $this->withHeader('Accept-Language', 'en')->postJson('admin/v2/auth/login', [
            'email' => $user->email,
            'password' => '123456',
        ]);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
        $response->assertJson([
            'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'success' => false,
            'error' => [
                'code' => 'error',
            ],
        ]);
    }

    public function test_login_user_has_been_success_with_user_agent()
    {
        $user = $this->createUser();
        $user->update(['mobile_verified' => false, 'last_seen' => now()]);

        $response = $this->withHeader('Accept-Language', 'en')
            ->withHeader('user-agent', 'sallapp')
            ->postJson('admin/v2/auth/login', [
                'email' => $user->email,
                'password' => '123456',
            ]);

        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
        ]);
    }
}
