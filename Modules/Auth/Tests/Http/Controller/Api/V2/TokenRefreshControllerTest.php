<?php

namespace Modules\Auth\Tests\Http\Controller\Api\V2;

use Modules\Auth\Http\Middleware\ReCaptchaV3;
use Modules\DashboardApi\Enums\SallaClaimKeys;
use Modules\DashboardApi\Strategies\Api\Authentication\SallaEncryptionStrategy;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class TokenRefreshControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();

        $this->withoutMiddleware(ReCaptchaV3::class);
    }

    public function test_route_refresh_token_not_exist()
    {
        $response = $this->postJson(route('api.v2.auth.token.refresh'), [
            'token' => fake()->text
        ]);
        $response->assertStatus(Response::HTTP_BAD_REQUEST);
    }

    public function test_validation_refresh_token()
    {
        $response = $this->postJson(route('api.v2.auth.token.refresh'), ['token' => SallaEncryptionStrategy::encode([])]);
        $response->assertStatus(Response::HTTP_UNAUTHORIZED);
        $response->assertJson([
            'status' => Response::HTTP_UNAUTHORIZED,
            'success' => false,
        ]);
    }

    public function test_get_new_token_by_refresh_token()
    {
        $user = $this->createUser();
        $response = $this->postJson(route('api.v2.auth.token.refresh'), [
            'token' => SallaEncryptionStrategy::encode([
                SallaClaimKeys::USER->value => optimus()->encode($user->id),
                SallaClaimKeys::STORE->value => optimus()->encode($this->testStoreId),
                SallaClaimKeys::VERIFIED->value => true,
                SallaClaimKeys::SOURCE->value => 'dashboard',
                SallaClaimKeys::FINGERPRINT->value => 'sss',
                SallaClaimKeys::EMAIL->value => $user->email,
                SallaClaimKeys::OWNER->value => $user->id,
            ], ['jti' => 1]),
        ]);
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson([
            'status' => Response::HTTP_OK,
            'success' => true,
        ]);
    }

}