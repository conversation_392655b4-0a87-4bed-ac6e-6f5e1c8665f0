<?php

use Illuminate\Support\Facades\Route;
use Modules\Auth\Http\Controllers\Api\V2\AppStoreRedirectLinkController;
use Modules\Auth\Http\Controllers\Api\V2\AutoLoginController;
use Modules\Auth\Http\Controllers\Api\V2\DetectCountryController;
use Modules\Auth\Http\Controllers\Api\V2\ForgetPasswordController;
use Modules\Auth\Http\Controllers\Api\V2\LoginController;
use Modules\Auth\Http\Controllers\Api\V2\ProfileController;
use Modules\Auth\Http\Controllers\Api\V2\RegisterByInvitationController;
use Modules\Auth\Http\Controllers\Api\V2\RegisterController;
use Modules\Auth\Http\Controllers\Api\V2\RequestOtpController;
use Modules\Auth\Http\Controllers\Api\V2\ResetPasswordController;
use Modules\Auth\Http\Controllers\Api\V2\TokenRefreshController;
use Modules\Auth\Http\Controllers\Api\V2\VerifyOtpController;
use Modules\Auth\Http\Middleware\CheckIfOnboarded;
use Modules\Auth\Http\Middleware\OtpIsPassed;
use Modules\Auth\Http\Middleware\OtpIsVerified;
use Modules\Auth\Http\Middleware\ReCaptchaV3;
use Modules\Auth\Http\Middleware\RedirectIfUserHasNoStore;
use Modules\Auth\Http\Middleware\RedirectIf2faVerified;
use Modules\Auth\Http\Middleware\RedirectIfFromAppsStore;
use Modules\DashboardApi\Http\Middleware\TokenableMiddleware;
use Modules\MarketPlace\Http\Middleware\PrepareMoneyMiddleware;
use Salla\Core\Http\Middleware\EscapeEmojiMiddleware;

Route::prefix('v2/auth')
    ->name('api.v2.auth.')
    ->middleware([ReCaptchaV3::class, EscapeEmojiMiddleware::class])
    ->group(function () {
        Route::middleware(['user.guest'])->group(function () {
            Route::post('register', RegisterController::class)
                ->middleware([RedirectIfFromAppsStore::class])
                ->name('register');
            Route::post('login', LoginController::class)
                ->middleware([RedirectIfFromAppsStore::class])
                ->name('login');
            Route::post('refresh', TokenRefreshController::class)
                ->name('token.refresh');
            Route::post('password/forget', ForgetPasswordController::class)->name('forget-password');
            Route::put('password/forget', [ResetPasswordController::class, 'reset'])->name('reset-password');
            Route::get('registration/invitation/{code}', RegisterByInvitationController::class)
                ->where('code', '[0-9]+')
                ->name('register-by-invitation');
            Route::get('app-store/redirect-link', AppStoreRedirectLinkController::class)
                ->withoutMiddleware(ReCaptchaV3::class)
                ->name('app-store-redirect-link');
            Route::post('auto-login', AutoLoginController::class)->name('auto-login');
            Route::get('detect-country', DetectCountryController::class)
                ->withoutMiddleware(ReCaptchaV3::class)
                ->name('detect-country');
        });

        Route::middleware(['auth', OtpIsVerified::class, 'trimmer', 'ksa-only'])
            ->withoutMiddleware(ReCaptchaV3::class)
            ->group(function () {
                Route::get('user/info', [ProfileController::class, 'info'])->name('user.info');
                Route::put('user', [ProfileController::class, 'update'])->name('user.update');
                Route::put('user/change-request/{change_request}', [ProfileController::class, 'confirmUpdate'])
                    ->name('user.confirm-update');
                Route::put('update-password', [ProfileController::class, 'updatePassword'])
                    ->name('update-password');
            });

        Route::name('otp.')
            ->middleware(['auth', 'trimmer', 'ksa-only'])
            ->group(function () {
                Route::post('/otp/request', RequestOtpController::class)
                    ->middleware(RedirectIf2faVerified::class)
                    ->name('send');
                Route::post('/otp/verify', VerifyOtpController::class)->name('verify');
                Route::post('/otp/resend', [RequestOtpController::class, 'resend'])->name('resend');
                // Route::post('/otp/reset', OtpResetController::class)->name('reset');
            });
    });

Route::prefix('v2/journey')
    ->name('api.v2.auth.register.')
    ->namespace('Api\V2')
    ->middleware([
        'auth',
        OtpIsPassed::class,
        'trimmer',
        EscapeEmojiMiddleware::class,
        CheckIfOnboarded::class,
        'ksa-only',
    ])
    ->group(function () {
        // This group can works with user instance only without store
        Route::get('merchant/activities', '\App\Http\Controllers\CP\MainActivityController')
            ->name('merchant.activities');
        Route::post('merchant/logo', 'UploadStoreLogoController')->name('merchant.logo'); //multi-part request
        Route::delete('merchant/logo', 'DeleteStoreLogoController')->name('merchant.logo.destroy');

        // This group can works with user instance only without store
        Route::prefix('identity')
            ->group(function () {
                // JSON routes with session
                Route::get('merchant', 'GetMerchantInfoController')->name('identity.merchant');
                Route::post('merchant', 'StoreMerchantController')->name('identity.merchant-store');
                Route::get('questions', 'OnboardingQuestionController')->name('identity.questions');
                Route::post('questions', 'OnboardingQuestionsAnswersController')
                    ->name('identity.questions-answers');
                Route::get('store-readiness', 'StoreReadinessController')->name('identity.store-readiness');
                Route::post('store-readiness-answers', 'StoreReadinessAnswersController')
                    ->name('identity.store-readiness-answers');
                Route::post('username', 'UsernameCheckController')->name('identity.check-username');
            });

        // This group works only when the user has a linked store
        Route::middleware([RedirectIfUserHasNoStore::class])
            ->group(function () {
                // temporary solution will be removed once feature/additional-onboarding-steps branch merged with this
                Route::get('product/info', 'GetStoreProductController')->name('merchant.product.info');
                Route::post('product/store', 'StoreProductController')->name('merchant.product.store');
                Route::post('product/image', 'UploadFirstProductImageController')->name('product.image');
                Route::delete('product/image', 'DeleteFirstProductImageController')
                    ->name('product.image.destroy');
                Route::post('product/file', 'UploadFirstProductFileController')->name('product.file');

                // store theme
                Route::get('theme/info', 'GetStoreThemeController')->name('theme.info');
                Route::post('theme/store/{product}', 'StoreThemeController')->name('theme.store');

                Route::prefix('address')->group(function () {
                    Route::post('/', 'LocationController')->name('address.store');
                    Route::get('resolve', 'GetLocationInfoController')->name('address.resolve');
                });
                Route::prefix('shipping')->group(function () {
                    Route::get('/methods', 'ShippingStepController')->name('shipping.methods');
                    Route::put('/status', 'ShippingStatusController')->name('shipping.status');
                    Route::put('/next', 'ShippingNextController')->name('shipping.next');
                });

                Route::prefix('payments')->group(function () {
                    Route::get('/methods', 'PaymentStepController')->name('payment.methods');
                    Route::put('/status', 'PaymentStatusController')->name('payment.status');
                });

                Route::prefix('plan')->group(function () {
                    Route::get('available', 'PlanStepController')->name('plan.available');
                    Route::post('{plan}', 'AddPlanController')
                        ->middleware([PrepareMoneyMiddleware::class])
                        ->name('plan.store');
                });

                Route::post('began', 'OnboardingCompleteController')->name('began');

                // Dashboard OnBoarding
                Route::name('steps.')->prefix('steps')->group(function () {
                    Route::get('/', 'ShowOnboardingStepsController')->name('list');
                    Route::get('/{name}', 'ShowOnboardingStepModalController')->name('show');
                    Route::post('/skip', 'SkipOnboardingStepController')->name('skip');
                    Route::post('/begin', 'LaunchController')->name('begin-on');
                });
            });
    });
