<?php

namespace Modules\Auth\Providers;

use Carbon\Carbon;
use Illuminate\Contracts\Auth\Factory as AuthFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Auth\Actions\ValidateUserStatus;
use Modules\Auth\Enum\AuthRouteMap;
use Modules\DashboardApi\Strategies\Api\Authentication\TokenAuthenticationFactory;
use Salla\Logger\Facades\Logger;

class Guard
{
    /**
     * The authentication factory implementation.
     *
     * @var \Illuminate\Contracts\Auth\Factory
     */
    protected $auth;

    /**
     * The number of minutes tokens should be allowed to remain valid.
     *
     * @var int
     */
    protected $expiration;

    /**
     * The provider name.
     *
     * @var string
     */
    protected $provider;

    /**
     * Create a new guard instance.
     *
     * @param \Illuminate\Contracts\Auth\Factory $auth
     * @param int $expiration
     * @param string $provider
     * @return void
     */
    public function __construct(AuthFactory $auth, $expiration = null, $provider = null)
    {
        $this->auth = $auth;
        $this->expiration = $expiration;
        $this->provider = $provider;
    }

    /**
     * Retrieve the authenticated user for the incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function __invoke(Request $request)
    {
        $token = $this->getTokenFromRequest($request);

        if (!$token) {
            $this->log('Guard:Invalid-token', 'Invalid-token');
            return;
        }

        $strategy = TokenAuthenticationFactory::create($token);
        $authenticationResponse = $strategy->authenticate($token);

        if (!$authenticationResponse->isSuccess()) {
            $this->log('TokenAuthenticationFactory', sprintf('%s: %s',class_basename(get_class($strategy)), $authenticationResponse->getErrorMessage()));
            return;
        }

        $user = $authenticationResponse->getUser();
        $userStatus = ValidateUserStatus::make(['user' => $user])->run();
        if (!$userStatus->isSuccess()) {
            $this->log('Guard:Invalid-user', $userStatus->getMessage());
            return;
        }

        $this->updateLastSeen($user);
        return $user;
    }

    /**
     * Update the last seen time for the user and store.
     *
     * @param mixed $user
     * @return void
     */
    private function updateLastSeen($user)
    {
        if (is_null($user->last_seen) || !Carbon::parse($user->last_seen)->greaterThan(now()->subMinutes(30))) {
            $user->forceFill(['last_seen' => now()])->save();
        }

        if (store()->hasStore()) {
            // Update the store's last seen time if it has not been updated in the last 30 minutes or the last seen is null
            if (is_null(store()->last_seen) || !Carbon::parse(store()->last_seen)->greaterThan(now()->subMinutes(30))) {
                store()->update(['last_seen' => now()]);
            }
        }
    }

    /**
     * Get the token from the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return string|null
     */
    protected function getTokenFromRequest(Request $request)
    {
        $token = $request->bearerToken() ?: $request->get('token', $request->header('X-API-KEY'));

        if (empty($token) || !is_string($token) || !Str::startsWith($token, 'v4.public.')) {
            return null;
        }

        return $token;
    }

    private function log(string $case, $message){
        Logger::setAdditions('return_case', ['case' => $case, 'message' => $message]);
    }
}
