<?php

namespace Modules\Auth\Traits\Auth;

use App\Models\User;
use Modules\Auth\Enum\AuthRouteMap;
use Modules\DashboardApi\Enums\SallaClaimKeys;
use Modules\DashboardApi\Strategies\Api\Authentication\SallaEncryptionStrategy;
use <PERSON>la\LaravelOtp\Services\OtpService;
use <PERSON>la\Logger\Facades\Logger;

trait CurrentUserTokens
{
    /**
     * @return array
     * @throws \Throwable
     */
    public function getUserData(array $data = [], User $user = null): array
    {
        $user = $user ?: $this->getCurrentUser();

        if (blank($user)) {
            throw new \Exception('User not found');
        }

        if (!store()->hasStore() && $user->store_id) {
            store()->loadFromId($user->store_id);
        }

        $otpService = app(OtpService::class)->setReceipent($user);
        $isTrustedDevice = $data['token_verified'] ?? false;

        $storeId = $data['store_id'] ?? $user->store_id;

        $claims = [
            SallaClaimKeys::USER->value => optimus()->encode($user->id),
            SallaClaimKeys::STORE->value => $storeId ? optimus()->encode($storeId) : null,
            SallaClaimKeys::VERIFIED->value => $isTrustedDevice,
            SallaClaimKeys::SOURCE->value => request()->attributes->get('source', request('source', 'dashboard')),
            SallaClaimKeys::FINGERPRINT->value => $otpService->getFingerprint(),
            SallaClaimKeys::EMAIL->value => $user->email,
            SallaClaimKeys::OWNER->value => optimus()->encode($data['owner_id'] ?? store()?->owner?->id ?? null),
        ];
        // if (($claims[SallaClaimKeys::OWNER->value] ?? null) === null) {
            Logger::message('debug','Owner claim is null',[
                'owner_store_id' => $storeId ?? $user->store_id ?? 'user->store_id is null',
                'owner_source' => $claims[SallaClaimKeys::SOURCE->value] ?? null,
                'owner_data_owner_id' => $data['owner_id'] ?? 'data[owner_id] is null',
                'owner_store_owner_id' => store()?->owner?->id ?? 'id is null',
                'owner_store_owner' => store()?->owner ?? 'owner is null',
                'owner_store' => !!store() ?? 'store() is null',
                'owner_has_store' => store()->hasStore(),
            ]);
        // }

        
        if (request()->attributes->get('partner_request_access') && request()->attributes->get('source', request('source', 'dashboard')) == 'partners') {
            $claims[SallaClaimKeys::PARTNER_REQUEST_ACCESS->value] = request()->attributes->get('partner_request_access');
        }

        if ($features = $this->getFeatures()) {
            $claims[SallaClaimKeys::FT->value] =  $features;
        }

        $data['token'] = SallaEncryptionStrategy::encode($claims);
        $data['verified'] = $isTrustedDevice;
        $data['refresh_token'] = SallaEncryptionStrategy::encode($claims, ['jti' => 1]);
        $data['expire_at'] = now()->addMinutes(config('salla-encryption.expiration.public'))
            ->format('Y-m-d H:i:s');

        if (!$isTrustedDevice && isset($data['next_url']) && $data['next_url'] !== AuthRouteMap::OTP) {
            $data['intended_to'] = $data['next_url'];
            $data['next_url'] = AuthRouteMap::OTP;
        }

        return $data;
    }

    private function getFeatures(): string
    {
        return collect(['multi-stores'])
            ->filter(fn($name) => feature($name)->isHaveFeature())
            ->map(fn($name) => feature($name)->getClaimName())
            ->implode(',');
    }

    public function getCurrentUser()
    {
        $user = auth()->check() ? auth()->user() : null;

        if (!$user && auth('api')->check()) {
            $user = auth('api')->user();
        }

        return $user;
    }
    
}
