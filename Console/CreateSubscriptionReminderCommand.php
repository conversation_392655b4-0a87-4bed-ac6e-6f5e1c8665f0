<?php

namespace Modules\MarketPlace\Console;

use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Actions\SubscriptionReminder\SubscriptionReminderAction;
use Modules\MarketPlace\Repositories\SallaSubscriptionRepository;
use Modules\MarketPlace\Services\SubscriptionReminderService;
use Modules\MarketPlace\Services\SubscriptionValidationService;

class CreateSubscriptionReminderCommand extends MarketplaceBaseCommand
{
    protected $signature = 'marketplace:subscription-reminder:create';

    protected $description = 'Create subscription reminders and handle expired ones.';

    public function handle(SallaSubscriptionRepository $repository): void
    {
        $this->createSubscriptionReminders($repository);
        $this->processExpiredSubscriptions();
    }

    /**
     * Create subscription reminders for active subscriptions.
     *
     * @param SallaSubscriptionRepository $repository
     */
    private function createSubscriptionReminders(SallaSubscriptionRepository $repository): void
    {
        $subscriptions = $repository->forCreateSubscriptionReminderCommand()
            ->with(['store:id,username,name' => ['owner:id,model_has_roles.store_id,email']])
            ->cursor();

        foreach ($subscriptions as $subscription) {
            try {
                if (resolve(SubscriptionValidationService::class)->shouldSkipSubscription($subscription) 
                    || $subscription->sallaProductWithTrashed->is_sandbox) {
                    continue;
                }
                SubscriptionReminderAction::dispatch(['subscription' => $subscription])->onQueue('merchants-emails');
            } catch (\Exception $exception) {
                sentryCaptureException($exception);
            }
        }
    }

    /**
     * Process expired subscription reminders.
     */
    private function processExpiredSubscriptions(): void
    {
        $subscriptionReminderService = resolve(SubscriptionReminderService::class);

        $subscriptionReminderService->getExpiredSubscriptionsQuery()
            ->chunk(500, function ($subscriptionReminders) use ($subscriptionReminderService) {
                foreach ($subscriptionReminders as $subscriptionReminder) {
                    $subscriptionReminderService->notifySubscriptionReminder($subscriptionReminder);
                    $subscriptionReminderService->deleteSubscriptionReminder($subscriptionReminder);
                }
            });
    }
}
