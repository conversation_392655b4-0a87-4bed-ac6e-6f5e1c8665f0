<?php

namespace Modules\MarketPlace\Console\MarketplaceApp;

use Illuminate\Console\Command;
use Modules\MarketPlace\Entities\Partner\PartnerSetting;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppPlanType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppDomainType;
use Modules\MarketPlace\Actions\MarketplaceApp\Subscription\ExpireFreeAppSubscriptionsAction;

class ExpireFreeSubscriptionsOfAppsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:expire-free-subscription-of-app {--company=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace Expire Free Subscriptions of App';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     */
    public function handle()
    {
        $company = $this->option('company');

        $list_salla_company = [
            4,
            17501,
            62186,
        ];

        if (!empty($company) && in_array($company, $list_salla_company)) {
            $this->error('this salla company');
            return;
        }

        $list_except_company = array_merge(
            PartnerSetting::where('key', 'features::show_app_free_plan')
                ->where('bag', '!=', 'general')
                ->where('value', 1)
                ->get()
                ->pluck('bag')
                ->toArray(),
            $list_salla_company
        );

        PartnerCompany::whereNotIn('id', $list_except_company)
            ->when(!empty($company), function ($query) use ($company) {
                return $query->where('id', $company);
            })
            ->chunk(100, function ($companies) {
                SallaProductMarketplaceApp::whereIn('developer_user_id', $companies->pluck('id')->toArray())
                    ->where('domain_type', SallaProductMarketplaceAppDomainType::APP)
                    ->where('type', SallaProductMarketplaceAppType::PUBLIC)
                    ->where('status', SallaProductMarketplaceAppStatus::LIVE)
                    ->whereIn('type', [
                        SallaProductMarketplaceAppPlanType::RECURRING,
                        SallaProductMarketplaceAppPlanType::ON_DEMAND,
                    ])
                    ->where('is_salla_app', 0)
                    ->chunk(100, function ($apps) {
                        foreach ($apps as $app) {
                            ExpireFreeAppSubscriptionsAction::make([
                                'app' => $app,
                            ])->run();
                        }

                        sleep(10);
                    });
            });
    }
}