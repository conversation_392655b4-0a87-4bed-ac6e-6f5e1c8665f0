<?php

namespace Modules\MarketPlace\Console\MarketplaceApp;

use Illuminate\Console\Command;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Actions\MarketplaceApp\Subscription\ExpireFreeAppSubscriptionsAction;

class ExpireAppCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:expire-app {app_id} {--store=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace Expire App';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @return void
     */
    public function handle()
    {
        ExpireFreeAppSubscriptionsAction::make([
            'app'      => SallaProductMarketplaceApp::getByApp($this->argument('app_id')),
            'store_id' => $this->option('store'),
        ])->run();
    }
}