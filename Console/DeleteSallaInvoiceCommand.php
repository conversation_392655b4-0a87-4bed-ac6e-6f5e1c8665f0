<?php

namespace Modules\MarketPlace\Console;

use <PERSON><PERSON>\Logger\Facades\Logger;
use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Entities\SallaInvoice;
use Modules\MarketPlace\Entities\SallaInvoiceItem;
use Mo<PERSON>les\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Notifications\Invoice\SallaInvoiceExpiredNotification;
use Modules\MarketPlace\Repositories\SallaInvoiceRepository;
use Salla\Core\Scopes\CurrentStoreScope;

class DeleteSallaInvoiceCommand extends MarketplaceBaseCommand
{
    protected $signature = 'marketplace:invoice:delete';

    protected $description = 'Delete un-paid Invoice';

    /**
     * Handle the command to delete unpaid invoices.
     *
     * @param SallaInvoiceRepository $sallaInvoiceRepository
     * @return void
     */
    public function handle(SallaInvoiceRepository $sallaInvoiceRepository)
    {
        $sallaInvoiceRepository->forDeleteSallaInvoiceCommand()
            ->with([
                'store:id,name,username' => ['owner:id,model_has_roles.store_id,email'],
                'items' => ['subscription:id,type,end_date' => ['sallaProduct:id,name']],
            ])
            ->withoutGlobalScope(CurrentStoreScope::class)
            ->chunk(500, function ($invoices) {
                $invoiceIds = $invoices->pluck('id')->toArray();
                $invoices->each(function ($invoice) {
                    $this->notifyIfApplicable($invoice);
                });
                $this->deleteInvoices($invoiceIds);
            });
    }

    /**
     * Send a notification to the store owner if the store is a special plan.
     *
     * @param SallaInvoice $invoice
     * @return void
     */
    private function notifyIfApplicable(SallaInvoice $invoice): void
    {
        Logger::message('debug', 'DeleteSallaInvoiceCommand::send notification', [
            'invoice_id' => $invoice->id,
        ]);

        store($invoice->store_id);

        if (!$this->shouldNotify($invoice)) {
            return;
        }
        
        $email = optional(optional($invoice->store)->owner)->email;
        if ($email) {
            Notification::route('mail', $email)->notify(new SallaInvoiceExpiredNotification($invoice->id));
        }
    }

    /**
     * Determine if a notification should be sent for the given invoice.
     *
     * @param SallaInvoice $invoice
     * @return bool
     */
    private function shouldNotify(SallaInvoice $invoice): bool
    {
        return store()->getPlan() !== SallaProductPlanType::SPECIAL 
            || ($invoice->getNotifiableItems()->isNotEmpty() && store()->getPlan() === SallaProductPlanType::SPECIAL);
    }

    /**
     * Delete the given invoice ids.
     *
     * @param array $invoiceIds The list of invoice ids to delete.
     * @return void
     */
    private function deleteInvoices(array $invoiceIds): void
    {
        SallaInvoice::whereIn('id', $invoiceIds)->update(['deleted_at' => now()]);
        $itemIds = SallaInvoiceItem::select('id')
                                    ->whereIn('invoice_id', $invoiceIds)
                                    ->whereNull('deleted_at')
                                    ->pluck('id')
                                    ->toArray();
        if (!empty($itemIds)) {
            SallaInvoiceItem::whereIn('id', $itemIds)
                             ->update(['deleted_at' => now()]);
        }
    }
}

