<?php


namespace Modules\MarketPlace\Console;


use Illuminate\Console\Command;
use <PERSON>la\Logger\Facades\Logger;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Actions\MarketplaceApp\Subscription\RefundSubscriptionAction;

class CheckSallaSubscriptionPendingCommand extends Command
{
    /**
     * @var string
     */
    protected $signature = 'marketplace:check-pending-subscription';

    /**
     * @var string
     */
    protected $description = 'Check Pending Subscription For App';

    /**
     *
     */
    public function handle()
    {
        $total = [];

        SallaSubscriptions::query()
            ->where('status', SallaSubscriptionStatus::PENDING)
            ->whereRaw('DATE_ADD(created_at, INTERVAL 10 MINUTE) <= NOW()')
            ->with(['order', 'orderItem', 'sallaProduct.marketplaceApp'])
            ->chunk(100, function ($subscriptions) use (&$total) {
                foreach ($subscriptions as $subscription) {
                    /**
                     * @var SallaSubscriptions $subscription
                     */
                    //check if app already have installed app
                    if ($instlledApp = $subscription->getInstalledApp()) {
                        Logger::message('debug', 'pending_subscription_have_installed_app', [
                            'subscription_id' => $subscription->id,
                            'installed_id'    => $instlledApp->id
                        ]);

                        $subscription->update([
                            'status' => SallaSubscriptionStatus::ACTIVE,
                        ]);

                        continue;
                    }

                    Logger::message('debug', 'pending_subscription_not_have_installed_app', [
                        'subscription_id' => $subscription->id,
                    ]);

                    RefundSubscriptionAction::make([
                        'subscription' => $subscription,
                    ])->run();

                    $total[] = $subscription->getKey();
                }
            });

        // TODO:: remove after debugging.
        Logger::message('debug', 'Complete checking pending subscriptions', [
            'total_subscriptions' => count($total),
            'subscriptions_ids'   => $total
        ]);
    }
}