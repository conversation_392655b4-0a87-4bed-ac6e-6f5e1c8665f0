<?php

namespace Modules\MarketPlace\Console;

use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Entities\SallaInvoice;
use Modules\MarketPlace\Notifications\Invoice\SallaInvoiceUnPaidNotifyNotification;
use Modules\MarketPlace\Repositories\SallaInvoiceRepository;

class NotifyUnpaidSallaInvoiceCommand extends MarketplaceBaseCommand
{
    protected $signature = 'marketplace:invoice:notify';

    protected $description = 'Notify un-paid Invoice';

    public function handle(SallaInvoiceRepository $repository): void
    {
        $repository->forNotifyUnpaidSallaInvoiceCommand()
            ->with([
                'store:id,name,username' => ['owner:id,model_has_roles.store_id,email'],
                'items' => ['subscription:id,type,end_date' => ['sallaProduct:id,name']],
            ])
            ->chunk(5000, function ($invoices) {
                $invoices->each(fn(SallaInvoice $invoice) => $this->processInvoice($invoice));
            });
    }

    /**
     * Process a single invoice, deleting items with deleted subscriptions
     * and sending notifications if applicable.
     *
     * @param SallaInvoice $invoice
     */
    private function processInvoice(SallaInvoice $invoice): void
    {
        $this->deleteItemsWithDeletedSubscriptions($invoice);

        if ($this->shouldSendNotification($invoice)) {
            $this->sendNotification($invoice);
        }
    }

    /**
     * Delete invoice items with deleted subscriptions.
     *
     * @param SallaInvoice $invoice
     */
    private function deleteItemsWithDeletedSubscriptions(SallaInvoice $invoice): void
    {
        $invoice->items->each(function ($item) {
            if (!$item->subscription) {
                $item->delete();
            }
        });
    }

    /**
     * Determine if a notification should be sent for the invoice.
     *
     * @param SallaInvoice $invoice
     * @return bool
     */
    private function shouldSendNotification(SallaInvoice $invoice): bool
    {
        $hasActiveSubscription = $invoice->items->contains(fn($item) => $item->subscription);
        $email = optional(optional($invoice->store)->owner)->email;

        return $hasActiveSubscription && $email;
    }

    /**
     * Send the unpaid invoice notification.
     *
     * @param SallaInvoice $invoice
     */
    private function sendNotification(SallaInvoice $invoice): void
    {
        $email = optional(optional($invoice->store)->owner)->email;

        if ($email) {
            Notification::route('mail', $email)->notify(new SallaInvoiceUnPaidNotifyNotification($invoice));
        }
    }
}
