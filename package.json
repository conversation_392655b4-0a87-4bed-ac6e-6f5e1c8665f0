{"private": true, "scripts": {"development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "production": "mix --production", "dev": "npm run development", "prod": "npm run production"}, "devDependencies": {"axios": "^0.22.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-runtime": "^6.26.0", "babel-runtime-regenerator": "^7.8.4", "bootstrap": "^4.5.3", "core-js": "^3.6.5", "cross-env": "^7.0", "css-loader": "^3.4.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-vue": "^7.17.0", "jquery": "^3.5.1", "laravel-mix": "^6.0.31", "laravel-mix-make-file-hash": "^2.2.0", "laravel-mix-merge-manifest": "^0.1.2", "lodash": "^4.17.21", "prettier": "^2.4.1", "resolve-url-loader": "^3.1.2", "sass": "^1.43.2", "sass-loader": "^12.1.0", "serialize-javascript": "^5.0.1", "style-loader": "^1.2.1", "stylus-loader": "^3.0.2", "vue": "^2.6.12", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.12", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "webpack": "^5.57.1"}, "dependencies": {"@alfsnd/vue-bootstrap-select": "^0.4.4", "@babel/runtime": "^7.15.4", "@bachdgvn/vue-otp-input": "^1.0.8", "@gtm-support/vue2-gtm": "^1.2.1", "@mhmdaljefri/vue-datagrid": "^3.0.98", "@popperjs/core": "^2.10.2", "@revolist/revogrid-column-numeral": "^1.0.2", "@riophae/vue-treeselect": "^0.4.0", "@salla.sa/core": "^1.2.31", "@salla.sa/languages": "^1.0.41", "@salla.sa/lit-components": "^0.8.11", "@sentry/tracing": "^6.2.0", "@sentry/vue": "^6.2.0", "@simonwep/pickr": "^1.8.2", "collect.js": "^4.28.4", "filepond": "^4.23.1", "filepond-plugin-file-encode": "^2.1.10", "filepond-plugin-file-metadata": "^1.0.8", "filepond-plugin-file-poster": "^2.4.2", "filepond-plugin-file-rename": "^1.1.8", "filepond-plugin-file-validate-size": "^2.2.5", "filepond-plugin-file-validate-type": "^1.2.5", "filepond-plugin-image-crop": "^2.0.4", "filepond-plugin-image-edit": "^1.6.1", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-filter": "^1.0.1", "filepond-plugin-image-preview": "^4.6.4", "filepond-plugin-image-resize": "^2.0.9", "filepond-plugin-image-transform": "^3.7.6", "filepond-plugin-image-validate-size": "^1.2.6", "ga-gtag": "^1.1.7", "i": "^0.3.6", "infinite-scroll": "^4.0.1", "intl-tel-input": "^18.5.3", "jquery-filepond": "^1.0.0", "lodash.isequal": "^4.5.0", "mixpanel-browser": "^2.49.0", "notify-send": "^0.1.2", "nprogress": "^0.2.0", "pdf-lib": "^1.17.1", "postcss": "^8.3.9", "query-string": "^7.0.1", "quill-image-extend-module": "^1.1.2", "sweetalert2": "^10.16.5", "swiper": "^10.2.0", "tippy.js": "^6.3.7", "tiptap": "^1.32.2", "tiptap-extensions": "^1.35.2", "verte": "0.0.12", "vue-animejs": "^2.1.1", "vue-collapsible-component": "^0.1.3", "vue-country-flag": "^2.3.2", "vue-datetime-js": "^1.2.2", "vue-debounce": "^2.5.7", "vue-easytable": "^2.8.3", "vue-filepond": "^6.0.3", "vue-form-wizard": "^0.8.4", "vue-good-table": "^2.21.11", "vue-html5-editor": "^1.1.1", "vue-i18n": "^8.28.2", "vue-infinite-loading": "^2.4.5", "vue-infinite-scroll": "^2.0.2", "vue-js-modal": "^2.0.0-rc.6", "vue-js-toggle-button": "^1.3.3", "vue-mugen-scroll": "^0.2.6", "vue-multiselect": "^2.1.6", "vue-password-strength-meter": "^1.7.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.2", "vue-select": "^3.10.8", "vue-slick-carousel": "^1.0.6", "vue-slider-component": "^3.2.23", "vue-social-sharing": "^3.0.4", "vue-sticky-directive": "0.0.10", "vue-sweetalert2": "^4.3.1", "vue-tables-2": "^2.3.3", "vue-tel-input": "^5.5.0", "vue-toasted": "^1.1.28", "vue2-datepicker": "^3.10.2", "vue2-google-maps": "^0.10.7", "vuejs-paginate": "^2.1.0", "vuelidate": "^0.7.6", "vuetable-2": "^2.0.0-beta.4", "zxcvbn": "^4.4.2"}}