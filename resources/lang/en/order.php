<?php

return [

    'title_header' => 'Orders',
    'single'       => 'Order',
    'total'        => 'Total Amount',
    'status'       => 'Status',

    'no_data'                         => 'No orders yet',
    'customer'                        => 'Customer',
    'call'                            => 'Call',
    'shipping'                        => 'Shipping',
    'shipping_or_picking_from_branch' => 'Shipping',
    'free_shipping'                   => 'Free Shipping',
    'address'                         => 'Shipping Address',
    'payment'                         => 'Payment',
    'payment_sent'                    => 'Payment Sent',
    'payment_photo'                   => 'Receipt',
    'no_payment'                      => 'Not Paid',
    'cod'                             => 'Cash on Delivery',
    'sadad_payment'                   => 'Paid via SADAD',
    'print'                           => 'Print Invoice',
    'print_warehouse'                 => 'Print Order List',
    'print_not_available'             => 'Printing feature is not available in the trial account',
    'the_product'                     => 'Product',
    'quantity'                        => 'Quantity',
    'seats_count'                     => 'Seats',
    'reservation_date'                => 'Reservation Date',
    'weight'                          => 'Weight',
    'weight_edit'                     => 'Weight per unit',
    'price'                           => 'Price',
    'sum'                             => 'Total',
    'cart_total'                      => 'Cart Total',
    'shipping_cost'                   => 'Shipping Cost',
    'cod_cost'                        => 'Cash on Delivery Fee',
    'loyalty_free_shipping'           => 'Free shipping earned by redeeming loyalty points',
    'loyalty_coupon'                  => 'Discount coupon earned by redeeming loyalty points',
    'total_cost'                      => 'Order Total',
    'ordre_status'                    => 'Order Status',
    'shipping_message'                => 'When the order is complete, please change its status to (Completed) to send it to the company',
    'shipping_sent_message'           => 'Delivery order has been sent to the company',
    'customer_note'                   => 'Additional notes for the customer..',
    'edit_staus'                      => 'Edit Order Status',
    'delete'                          => 'Delete Order',
    'ask_delete'                      => 'Are you sure you want to delete this order?',
    'ask_delete_with_loyalty'         => 'Do you want to delete the order that contains redeemed loyalty points?',
    'delete_loyalty_message'          => 'Redeemed points will be returned to customers upon confirmation of deletion',
    'confirm_delete'                  => 'Confirm Deletion',
    'date'                            => 'Order Date',
    'to_customer'                     => 'Issued to',
    'payment_details'                 => 'Payment Details',
    'payment_method_lbl'              => 'Payment Method',
    'sadad'                           => 'SADAD',
    'footer_thank'                    => 'Thank you for shopping from our store. Have a great day!',
    'tax'                             => 'Tax',
    'tax_with_percent'                => 'Tax (:percent%)',
    'change_orders_to_status'         => 'Change orders to status',
    'delete_orders'                   => 'Delete Orders',
    'delete_orders_title'             => 'Are you sure you want to delete the selected orders?',
    'delete_orders_caution'           => 'Please note that if you agree to delete, the amount paid will be refunded to the customer',
    'customer_status'                 => ':customer_name',
    'user_status'                     => ':user_fullname',
    'bank_caution'                    => 'Please verify that the amount has been received in your account before processing the order',
    'print_order_invoices'            => 'Print Order Invoices',
    'print_order_invoices_summary'    => 'Print Order Invoices Summary',
    'print_order_polices'             => 'Print Order BOLs',
    'print_order_warehouses'          => 'Print Order Preparation Lists',
    'total_weight'                    => 'Total Weight',
    'exporting_orders'                => 'Export Orders',
    'total_summery'                   => 'Order Summary',
    'order_options'                   => 'Order Options',
    'delivery_to'                     => 'Delivery to',
    'city'                            => 'City',
    'country'                         => 'Country',
    'title_header_product'            => 'Products',
    'title_header_product_in_branch'  => 'Products in Branch (:branch)',
    'title_header_product_without_branch'  => 'Products without Branch',
    'title_header_product_in_shipment'=> 'Products in Shipment (:shipment)',
    'order_shipments'                 => 'Order Shipments',
    'shipping_company'                => 'Shipping Company',
    'branch'                          => 'Branch',
    'policy_number'                   => 'Policy Number',
    'shipment_status'                 => 'Shipment Status',
    'paidVia' => 'Paid via :method',
    'buttons' => [
        'print_invoices'         => 'Print Invoices',
        'print_invoices_summary' => 'Print Invoices Summary',
        'print_warehouse'        => 'Print Preparation Lists',
        'print_polices'          => 'Print BOLs',
        'assign_multi_order'     => 'Assign to Employees',
        'issue_policy'           => 'Issue BOL',
        'request_captain'        => 'Request Captain',
        'cancel_request_captain' => 'Cancel Captain Request',
        'track_order'            => 'Track Order',
        'print_policy'           => 'Print BOL',
        'assign_users'           => 'Assign Orders to Employees',
        'assign_tags'            => 'Assign Tags to Orders'
    ],

    'status_ico' => [
        '-1'   => 'icon-cross2',
        '1'    => 'icon-credit-card',
        '2'    => 'icon-inbox',
        '3'    => 'icon-gift',
        '4'    => 'icon-checkmark-circle',
        '5'    => 'icon-minus-circle2',
        '6'    => 'icon-alert',
        '7'    => 'icon-reload-alt',

    ],

    'status_id' => [
        '-1'    => 'Deleted',
        '1'     => 'Awaiting Payment',
        '2'     => 'Awaiting Review',
        '3'     => 'In Progress',
        '4'     => 'Completed',
        '5'     => 'Canceled',
        '6'     => 'Payment Failed',
        '7'     => 'Refunded',
        '8'     => 'In Delivery',
        '9'     => 'Delivered',
        '10'    => 'Shipped',
        'draft' => 'Draft',
    ],
    'payment_method' => [
        'bank'               => 'Bank',
        'cod'                => 'Cash on Delivery',
        'sadad'              => 'SADAD',
        'credit_card'        => 'Credit Card',
        'paypal'             => 'PayPal',
        'mada'               => 'Mada',
        'apple_pay'          => 'Apple Pay',
        'google_pay'         => 'Google Pay',
        'stc_pay'            => 'STC Pay',
        'tabby_installment'  => 'Tabby',
        'tamara_installment' => 'Tamara',
        'mispay_installment' => 'MIS Pay',
        'emkan_installment' => 'Emkan',
        'knet'               => 'KNET',
        'spotii_pay'         => 'Spotii',
        'free'               => 'Free',
        'waiting'            => 'Awaiting Payment',
        'geidea'             => 'Geidea'
    ],
    'receipt' => [
        'ar' => [
            'receipt'                => 'Tax Invoice',
            'electronic_store'       => 'Electronic Store',
            'store_contact_details'  => 'Store Contact Details',
            'agreement_label'        => 'Acknowledgment',
            'tax_number'             => 'Tax Number',
            'tax_no_not_available'   => 'Not Available',
            'order'                  => 'Order',
            'block'                  => 'Block',
            'street'                 => 'Street',
            'postal_code'            => 'Postal Code',
            'by_account'             => 'By Account',
            'shipping_details'       => 'Shipping Details',
            'expected_shipping_days' => 'Expected Shipping Days',
            'shipping_number'        => 'Shipping Number',
            'shipping_code'          => 'Shipping Code',
            'account_number'         => 'Account Number',
            'aramex'                 => 'Aramex',
            'tax'                    => 'Tax',
            'price_with_tax'         => 'Price including Tax',
            'credit_card'            => 'Credit Card',
            'mada'                   => 'Mada',
            'by'                     => 'By',
            'codes'                  => 'Codes',
            'to_customer'            => 'Issued to',
            'from_customer'          => 'Issued from',
            'payment_details'        => 'Payment Details',
            'total'                  => 'Total Amount',
            'payment_method_lbl'     => 'Payment Method',
            'cod'                    => 'Cash on Delivery',
            'sadad'                  => 'SADAD',
            'the_product_id'         => 'Product ID',
            'the_product'            => 'The Product',
            'category'               => 'Category',
            'brand'                  => 'Brand',
            'quantity'               => 'Quantity',
            'price'                  => 'Price',
            'after_discount'         => 'After Discount',
            'sum'                    => 'Total',
            'cart_total'             => 'Cart Total',
            'cart_total_without_tax' => 'Subtotal (Excluding Tax)',
            'shipping_cost'          => 'Shipping Cost',
            'shipping'               => 'Shipping',
            'cod_cost'               => 'Cash on Delivery Fee',
            'free'                   => 'Free',
            'total_cost'             => 'Total Order Cost',
            'footer_thank'           => 'Thank you for shopping from our store. We wish you a great day!',
            'cost_price'             => 'Cost Price',
            'sku'                    => 'SKU',
            'bar_code'               => 'Barcode',
            'apple_pay'              => 'Apple Pay',
            'stc_pay'                => 'STC Pay',
            'tamara_installment'     => 'Tamara',
            'tabby_installment'      => 'Tabby',
            'knet'                   => 'KNET',
            'spotii_pay'             => 'Spotii',
            'weight'                 => 'Weight',
            'total_weight'           => 'Total Weight',
        ],
    ],

    'setting' => [
        'title' => 'Order Options',
    ],

    'refund' => [
        'exceed_allowed_time_to_refund' => 'The allowed time for refund has been exceeded. Please contact the customer to refund the amount.',
        'order_amount_refunded'         => ':amount :currency has been refunded to the customer on :date',
    ],

    'gift_sent_at' => 'Sent Date',
    'gift_is_scheduled' => 'Scheduled',

    'loyalty_prize_update_alert' => 'The order cannot be edited currently, as it contains a loyalty prize of free shipping or a coupon',
    'loyalty_prize_product_update_alert' => 'The price of this product cannot be edited',
    'payment_not_completed' => 'Payment not completed',
];
