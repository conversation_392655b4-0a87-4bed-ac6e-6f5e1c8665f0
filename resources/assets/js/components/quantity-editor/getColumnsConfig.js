import {
    renderRadio,
    inputNumber
  } from '../vue-salla-table/cells/index.js'
  
/**
 *
 * @param {string[]} columns : a list of fields;
 * @param {boolean} isSmallScreen
 * @returns a list of revo grid columns config
 */
export default function getColumnsConfig(columns, isSmallScreen = false) {
    return columns?.map(({ name, title }) => {
        const defaultOptions = {
            prop: name,
            name: title,
            size: columns.length > 4 ? 140 : 180,
            editor: 'inputNumberEditor',
            cellTemplate: inputNumber,
        }
        
        switch (name) {
            case 'name-sticky':
                return {
                    ...defaultOptions,
                    prop: 'name-sticky',
                    pin: isSmallScreen ? undefined : 'colPinEnd', // end means start in rtl
                    size: columns.length > 4 ? 400 : 400,
                    editor: 'inputEditor',
                    readonly: true,
                    cellTemplate: null,
                }
        
            case 'sku':
                return {
                ...defaultOptions,
                editor: 'inputEditor',
                size: columns.length > 4 ? 140 : 180,
                readonly: true,
                cellTemplate: null,
            }

            case 'branches':
                return {
                ...defaultOptions,
                editor: 'inputEditor',
                size: 500,
                readonly: true,
                cellTemplate: null,
            }
        
            case 'unlimited_quantity':
                return {
                    ...defaultOptions,
                    prop: 'unlimited_quantity',
                    editor: 'radioBoxEditor',
                    size: columns.length > 4 ? 140 : 180,
                    cellTemplate: renderRadio,
                }
            default:
                return defaultOptions
        }
    });
}
  