<template>
    <div>
        <div class="row btns-row">
            <div class="col-xs-6">
                <a :href="getProductsUrl()" class="btn btn--trans btn--return">
                    <i class="sicon-caret-right-circle right-back-arrow"></i>
                    العودة الى المنتجات
                </a>
            </div>
            <div class="col-xs-6">
                <div class="align-left rec-list rec-list--horizental rec-list--align-center products-control">
                    <div class="filter-mode bulk-edit-filter" v-if="showSearch()"
                         :data-filter-mode="moreParams && moreParams.filtering ===1">
                        <a id="rec_filter_toggle" class="btn btn-filter-toggle" @click="getFilters($event)"><i
                                class="sicon-filter">
                        </i> تصفية <i class="sicon-cancel" @click="cancelFilter"
                                      v-show="moreParams && moreParams.filtering ===1"></i></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Products ---->
        <filters
        v-if="enableFilter && showSearch()"
        :extraClass="false"
        :exportUrlParam="'/export/product-quantities'" />

        <div class="panel panel-default">
            <div class="panel-heading pt-15 pb-15 sides">
                <h6 class="panel-title panel-title--sub-title">
                    تعديل كمية المنتجات
                    <span v-if="!showSearch() && products.data"> - المنتجات المحددة :
                        {{getProductsCount()}}</span>
                </h6>
                <edition-status
                    v-if="preUpdatedProducts.length > 0"
                    :on-save="handleUpdateCommit"
                    :on-undo="handleUpdateUndo"
                    :edition-counter="preUpdatedProducts.length"
                    :status="editionStatus"
                />
            </div>
            <div class="panel-body no-padding">
                <div v-show="pageFetching">
                    <mini-loader size="sm" :wide="true" :center="true" />
                </div>
                <div v-if="showSearch()" :class="['form-group search-field bulk-editor mb-0',isSearching ? 'loading' : '',]" >
                    <div v-show="false" class="loader-absolute">
                        <mini-loader size="sm" />
                    </div>
                    <input
                        v-model="keyword"
                        v-debounce:1000ms="search"
                        type="text"
                        class="form-control"
                        placeholder="ابحث بإسم المنتج ، او الـ SKU"
                    />
                </div>

                <VSallaTable
                    :range="true"
                    :grid-editors="gridEditors"
                    :source="source"
                    :columns="columns"
                    :on-reached-end="handlePaginate"
                    :whiteRow="true"
                    :dimmedBranches="!has_branches && !isSmallScreen" />

                <no-data v-if="showNoDataBox"/>
            </div>
        </div>
    </div>
</template>
<script>

    // Local Files
    import Filters from "../products-view/components/filters";
    import getColumnsConfig from './getColumnsConfig';
    import MiniLoader from '../loaders/mini-loader';
    import EditionStatus from './EditionStatus';
    import NoData from './NoData';
    import Http from "../../utils/http";
    import VSallaTable, { VGridVueEditor } from '../vue-salla-table';
    import ProductService from "../../services/ProductService";

    // Libs
    import Vue from 'vue';
    import lodash from 'lodash';
    import qs from 'query-string';
    import vueDebounce from 'vue-debounce';

    // Editors
    import RadioboxEditor from '../vue-salla-table/editors/radiobox-editor';
    import InputNumber from '../vue-salla-table/editors/input-number';
    import Input from '../vue-salla-table/editors/input';


    // define editors
    const inputNumberEditor = VGridVueEditor(
        Vue.component('InputNumber', InputNumber)
    );
    const radioBoxEditor = VGridVueEditor(
        Vue.component('RadioboxEditor', RadioboxEditor)
    );
    const inputEditor = VGridVueEditor(
        Vue.component('Input', Input)
    );

    const editors = {
        inputNumberEditor,
        radioBoxEditor,
        inputEditor,
    };

    Vue.use(vueDebounce, {
        lock: true,
        listenTo: ['keyup'],
        defaultTime: '900ms',
        fireOnEmpty: false
    });

    export default ({
        data() {
            return {
                enableFilter: false,
                fields: [
                    {
                        name: 'quantity',
                        title: 'الكمية'
                    }
                ],
                has_branches: false,
                pageFetching: false,
                keyword: null,
                moreParams: {},
                source: [],
                products: [],
                columns: [],
                preUpdatedProducts: [],
                beforeUpdateProductsIds: [],
                gridEditors: editors,
                lastPage: 1,
                currentPage: 1,
                editionStatus: 'default', // can be one of ['update', 'saving', 'saved', 'default']
            }
        },

        components: {
            NoData,
            Filters,
            MiniLoader,
            EditionStatus,
            VSallaTable
        },

        watch: {
            isSmallScreen() {
                this.updateColumns()
            },
        },

        computed: {
			isMobileApp: () => {
        if (typeof dataLayer !== 'undefined' && dataLayer.length) {
          return dataLayer[0].page.mobileApp;
        }
            },
            isSearching: function () {
                return this.pageFetching && this.keyword
            },
            showNoDataBox: function () {
                return this.source.length <= 0 && !this.pageFetching
            },
        },

        created() {
            this.has_branches = !!ProductService.getDatum('branches');
            this.fields = ProductService.getDatum('branches', this.fields);
            this.moreParams = ProductService.getDatum('params');
            this.enableFilter = ProductService.getDatum('enable_filters') && this.showSearch();

            this.appendExtraFields();
            this.handleWindowResize();
            this.updateColumns();
        },

        mounted() {
            this.getProducts();
            window.addEventListener('afteredit', this.handleEdit);
            window.addEventListener('beforeaange', this.rangeHandler);
            window.addEventListener('resize', this.handleWindowResize);
            Salla.event.addEventListener('products::submit-filters', this.submitFilters);
        },

        beforeDestroy() {
            window.addEventListener('afteredit', this.handleEdit);
            window.removeEventListener('beforeaange', this.rangeHandler);
            window.addEventListener('resize', this.handleWindowResize);
            document.removeEventListener('products::submit-filters', this.submitFilters);
        },

        methods: {

            // Cancel Range edit if the product is SKU
            rangeHandler(event) {
               const newData = event.detail.newData,
                    source = event.target.source;
               for (const key in newData) {
                    if ((source[key].type === 'sku' && event.detail.oldProps[0] === 'unlimited_quantity')
                    || (source[key].unlimited_quantity && event.detail.oldProps[0] !== 'unlimited_quantity')
                    || (source[key].has_skus && event.detail.oldProps[0] !== 'unlimited_quantity')) {
                        event.preventDefault()
                    }
                }

                if (event.detail.oldProps[0] === 'sku' || event.detail.oldProps[0] === 'name-sticky' || event.detail.oldProps[0] === 'branches') {
                    event.preventDefault()
                }
            },

            // Update Columns
            updateColumns() {
                this.columns = getColumnsConfig(this.fields,this.isSmallScreen)
            },

            // Handler to check the window size on DOM load
            handleWindowResize() {
                this.isSmallScreen = window.innerWidth <= 800
            },

            // Handle the cell edit group or single
            handleEdit(event) {
                const { model, models } = event.detail,
                 updatedRows = model ? { 0: model } : models;

                // empty list if user sent reuquest to backend (editionStatus === 'saved')
                const prevPreUpdatedProducts =
                    this.editionStatus === 'saved' ? [] : this.preUpdatedProducts
                // uniq list of updated products
                const preUpdatedProducts = _.uniqBy(
                    [
                        ...prevPreUpdatedProducts,
                        ...Object.entries(updatedRows).map(([, value]) => value),
                    ],
                    'id'
                )

                this.preUpdatedProducts = preUpdatedProducts
                const preUpdatedProductsIds = preUpdatedProducts.map(({ id }) => id)
                this.beforeUpdateProductsIds = preUpdatedProductsIds
                this.updateStatus('update');
            },

            // Status Update
            updateStatus(status) {
                this.editionStatus = status
            },

            //sent updated prodcuts to BE
            handleUpdateCommit() {
                this.postProdcuts(this.preUpdatedProducts)
            },

            //revert products to prev version
            handleUpdateUndo() {
                const ids = this.beforeUpdateProductsIds
                const beforeUpdateProductsCopy = this.products.filter((product) =>
                    ids.includes(product.id)
                )

                this.postProdcuts(beforeUpdateProductsCopy, true).then(() => {
                    const products = this.source.map((product) => {
                        const productIndex = ids.indexOf(product.id)
                        const isUpdated = ids.includes(product.id)
                        const updateProduct = beforeUpdateProductsCopy[productIndex]

                        if (isUpdated) {
                            return updateProduct
                        }
                        return product
                    });

                    this.updateSource(products);

                    // sync products with latest version of source
                    this.products = _.cloneDeep(products);
                })
            },

            // Post for Updating the product
            async postProdcuts(products, undo = false) {
                this.updateStatus('saving')

                try {
                    await Http.post('editor', {
                        products: products,
                    })
                    this.updateStatus(undo ? 'default' : 'saved')
                    // reset counter and more...
                    if (undo) {
                        this.beforeUpdateProductsIds = []
                        this.preUpdatedProducts = []
                    }
                } catch ({ response }) {
                    this.updateStatus('update')
                    laravel.ajax.errorHandler(response)
                }
            },

            // Upsdate the source on updating the products
            updateSource(newSource) {
                this.source = _.cloneDeep(newSource)
            },

            // Append three static fields on every quantity edit
            appendExtraFields() {
                if (this.has_branches || window.innerWidth <= 800) {
                    this.fields.unshift(
                        {
                            name: 'name-sticky',
                            title: 'اسم المنتج',
                        },
                        {
                            name: 'sku',
                            title: 'SKU'
                        },
                        {
                            name: 'unlimited_quantity',
                            title: 'كمية غير محدودة'
                        }
                    )
                } else {
                    this.fields.unshift(
                        {
                            name: 'name-sticky',
                            title: 'اسم المنتج',
                        },
                        {
                            name: 'branches',
                            title: 'الفروع',
                        },
                        {
                            name: 'sku',
                            title: 'SKU'
                        },
                        {
                            name: 'unlimited_quantity',
                            title: 'كمية غير محدودة'
                        }
                    )
                }
            },

            getProductsUrl() {
                return baseUrl + "/products"
            },

            getBulkProductsUrl(params = {}) {
                const searchParams = qs.stringify(params, { arrayFormat: 'bracket' })
                return `${baseUrl}/products/quantity/editor?${searchParams}`
            },

            showSearch() {
                return !window.location.href.includes('products[0]')
            },

            // fetch all filters data
            getFilters() {
                Salla.event.createAndDispatch('products::fetch-filters')
            },

            // Submit Filter
            submitFilters(e) {
                this.moreParams = { ...e.detail, filtering: 1 };
                this.getProducts();
            },

            // Search By Keyword
            search() {
                this.moreParams.keyword = this.keyword ? this.keyword : null;
                this.getProducts();
            },

            // Cancel Filtration
            cancelFilter(event) {
                event.stopPropagation();
                this.resetFilters();
                Salla.event.createAndDispatch('products::reset-filters');
            },

            // Reset all filters parameters
            resetFilters() {
                this.moreParams = {};
                this.getProducts();
            },

            // Get Parent Products Count without skus
            getProductsCount() {
                let selected_products = _.filter(this.products.data, function (p) {
                    return !p.product_id; // remove the child skus that has parent id
                });
                return selected_products.length;
            },

            // convert product.status to boolean
            productNormalizer({ ...product }) {
                return {
                    ...product,
                }
            },

            // handle paginations on scroll
            handlePaginate() {
                const canPaginate = this.currentPage < this.lastPage && !this.pageFetching

                if (canPaginate) {
                    this.getProducts(this.currentPage + 1)
                }
            },

            // Fetch The Products
            async getProducts(page = 1, prePage = 40) {
                this.pageFetching = true
                try {
                    const productsUrl = this.getBulkProductsUrl({
                        per_page: prePage,
                        page,
                        ...this.moreParams,
                    });

                    const { data: { products },} = await Http.get(productsUrl);

                    let allProducts = [...products.data]
                    if (page > 1 && this.source.length < prePage * page) {
                        allProducts = [...this.source, ...products.data]
                    }

                    const normalizeList = allProducts.map(this.productNormalizer)

                    // prevent assing refernce with clonDeep
                    this.source = lodash.cloneDeep(normalizeList)
                    this.products = lodash.cloneDeep(normalizeList)
                    this.lastPage = products.last_page
                    this.currentPage = page

                    return products.data
                } catch (error) {

                } finally {
                    this.pageFetching = false
                }
            },

        },
    });
</script>

<style scoped lang="scss">
    #salla-table-wrapper {
        position: sticky;
        top: 100px;
    }

    .search-field {
        border-top: none;
        position: relative;
        z-index: 10
    }

    .loader-absolute {
        position: absolute;
        inset: 10px;
    }
</style>
