import Vue from 'vue'
import Vuex from 'vuex'
import { groupColumnsBySection } from '../utils'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    sections: [],
    features: {},
    translations: {
      product_info: 'تفاصيل المنتج',
      product_options: 'خيارات المنتج',
      seo_improvements: 'seo تحسينات ',
      match_columns: 'بيانات أخرى',
      order_info: 'تفاصيل الطلب',
      customer_details: 'بيانات العميل',
    },
    activeTemplate: {
      columns: [],
      name: '',
      id: null,
    },
    activeTab: 'products',
    orders: {
      columns: [],
      templates: [],
    },
    products: {
      columns: [],
      templates: [],
    },
  },
  mutations: {
    setInitialData(state, data) {
      state.features = data.features
      state.orders = data.orders
      state.products = data.products
      state.sections = groupColumnsBySection(data.sections)
      state.activeTab = data.activeTab
    },
    setActiveTemplate(state, data) {
      state.activeTemplate = data
    },
    setActiveTab(state, data) {
      state.activeTab = data
      localStorage.setItem('activeExportTemplateTab', data)
      state.sections = groupColumnsBySection(state[state.activeTab].columns)
    },
    fetchTemplates(state) {
      axios
        .get('/admin/v2/export-temps')
        .then((response) => {
          state.customTemplates = response.data.data
        })
        .catch((error) => {
          console.log(error, 'error new templates')
        })
    },
    addTemplateField(state, data) {
      state.activeTemplate.columns.push(data)
      state.sections = state.sections.map((section) => {
        section.columns = section.columns.map((column) => {
          if (column.name == data.name) {
            column.added = true
          }
          return column
        })
        return section
      })
    },
    setAddedFields(state, data) {
      state.activeTemplate.columns = data
    },
    restoreTemplateField(state, data) {
      state.activeTemplate.columns = state.activeTemplate.columns.filter(
        (column) => column.name !== data.name
      )
      state.sections = state.sections.map((section) => {
        section.columns = section.columns.map((column) => {
          if (column.name == data.name) {
            column.added = false
          }
          return column
        })
        return section
      })
    },
  },
  actions: {},
  modules: {},
})
