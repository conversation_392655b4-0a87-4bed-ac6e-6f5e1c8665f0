export const addSortToArray = (array) => {
  return array.map((item, index) => {
    item.sort = index + 1
    return item
  })
}

export const requestData = async (url) => {
  const config = {
    'Content-Type': 'application/json',
    'X-API-KEY': window.token.key,
    's-store-id': store.id,
    ...(window.headers || {}),
  }
  axios.defaults.headers.common = {
    ...axios.defaults.headers.common,
    ...config,
  }
  return await axios.get(url)
}

// create custom swal function
export const $error = (text) => {
  swal({
    title: ' خطأ',
    text: text,
    type: 'error',
    showConfirmButton: false,
    timer: 2000,
  })
}
export const groupColumnsBySection = (columns) => {
  const grouped = columns.reduce((grouped, column) => {
    let key = column.section
    if (!grouped[key]) {
      grouped[key] = []
    }
    grouped[key].push(column)
    return grouped
  }, {})

  return Object.entries(grouped).map(([section, columns]) => ({
    section,
    columns,
  }))
}
