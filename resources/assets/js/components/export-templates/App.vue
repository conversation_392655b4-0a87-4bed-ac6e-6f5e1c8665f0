<script src="store/index.js"></script>
<template>
  <div>
    <div v-if="showInfo" class="alert-box alert-box--info">
      <i class="sicon-info"></i>
      <article>
        <p>الحد الأقصى لعدد القوالب هو 5 قوالب</p>
      </article>
      <i class="sicon-cancel font-14" @click="hideInfo"></i>
    </div>
    <div class="row btns-row nav nav-lg">
      <div class="col-xs-6 main-btn">
        <button
          class="btn btn-tiffany btn-rounded btn-xlg"
          @click="showAddModal"
        >
          <i class="sicon-add"></i>
          إضافة قالب تصدير
        </button>
      </div>
    </div>
    <div id="">
      <div class="row">
        <div class="col-md-12">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h6 class="panel-title">
                <i class="sicon-cloud-download"></i>&nbsp; قوالب التصدير
                &nbsp;<span class="text-muted text-size-small">
                  {{ templateLengthString }}
                </span>
              </h6>
            </div>
            <ExportProductTemplates />
            <ExportTemplateModal />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ExportProductTemplates from './components/ExportProductTemplates.vue'
import { mapMutations, mapState } from 'vuex'
import ExportTemplateModal from './components/ExportTemplateModal.vue'
import { addSortToArray } from './utils'

export default {
  components: {
    ExportTemplateModal,
    ExportProductTemplates,
  },

  data() {
    return {
      showInfo: true,
    }
  },
  computed: {
    ...mapState({
      sections: (state) => state.sections,
      activeTab: (state) => state.activeTab,
      orders: (state) => state.orders,
      products: (state) => state.products,
    }),
    templatesLength() {
      return this.activeTab == 'products'
        ? this.products?.length
        : this.orders?.length
    },
    templateLengthString() {
      if (this.templatesLength == 0) {
        return 'لا يوجد قوالب'
      }
      if (this.templatesLength == 1) {
        return 'قالب واحد'
      }
      if (this.templatesLength == 2) {
        return 'قالبين'
      }
      if (this.templatesLength > 2) {
        return `${this.templatesLength} قوالب`
      }
    },
  },
  created() {
    var active_tab =
      localStorage.getItem('activeExportTemplateTab') ?? 'products'
    console.log(window.initialData)
    if (!window.initialData.features.orders) {
      active_tab = 'products'
    }

    this.setInitialData({
      sections: window.initialData[active_tab].columns,
      activeTab: active_tab,
      features: window.initialData.features,
      orders: window.initialData.orders,
      products: window.initialData.products,
    })
  },
  mounted() {},
  methods: {
    ...mapMutations(['setInitialData', 'setActiveTemplate']),
    showAddModal() {
      if (this.templatesLength == 5) {
        swal({
          text: 'لأضافة قالب جديد يرجى حذف قالب اخر الحد الأقصى لعدد القوالب هو 5',
          type: 'warning',
          confirmButtonText: 'موافق',
        })
        return
      }
      this.setActiveTemplate({
        id: null,
        name: '',
        columns: [],
      })
      $('#product-templates-modal').modal('show')
    },
    hideInfo() {
      this.showInfo = false
    },
  },
}
</script>

<style lang="scss">
.template-fields {
  max-height: 400px;
  overflow-y: scroll;

  li {
    cursor: pointer;
    border: 1px solid #eeeeee;
    padding: 12px;
    color: #444444;
    font-weight: 400;

    i {
      color: #bbbbbb;
    }
  }
}
</style>
