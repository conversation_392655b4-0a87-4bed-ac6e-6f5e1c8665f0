<template>
  <div>
    <ul
      class="nav nav-tabs nav-tabs-solid nav-tabs-component nav-justified product-control-nav mb-15"
    >
      <li
        :class="activeTab === 'products' ? 'active' : ''"
        @click="setActiveTab('products')"
      >
        <a
          href="#custom_products_templates"
          data-toggle="tab"
          aria-expanded="false"
        >
          <i class="sicon-t-shirt position-left"></i>المنتجات
        </a>
      </li>
      <li
        v-if="features.orders"
        :class="activeTab === 'orders' ? 'active' : ''"
        @click="setActiveTab('orders')"
      >
        <a
          href="#custom_orders_templates"
          data-toggle="tab"
          aria-expanded="false"
        >
          <i class="sicon-t-shirt position-left"></i>الطلبات
        </a>
      </li>
    </ul>
    <div class="">
      <div
        v-if="activeTab === 'products'"
        id="custom_products_templates"
        class="tab-pane active"
      >
        <NoData
          v-if="customTemplates.length === 0"
          show-icon
          title="لم يتم اضافة قوالب بعد"
          subtitle="يمكنك الإضافة من خلال العناصر المتاحة"
        />
      </div>
      <div
        v-if="activeTab === 'orders' && features.orders"
        id="custom_orders_templates"
        class="tab-pane"
      >
        <NoData
          v-if="customTemplates.length === 0"
          show-icon
          title="لم يتم اضافة قوالب بعد"
          subtitle="يمكنك الإضافة من خلال العناصر المتاحة"
        />
      </div>
      <div
        v-if="customTemplates.length != 0"
        class="table-responsive table-responsive--ov-visible table-responsive--ov-x-visible"
      >
        <table class="table table-no-top-border">
          <tbody>
            <tr v-for="template in customTemplates" :key="template.id">
              <td>
                <a
                  class="load-data-page"
                  @click="(e) => showEditModal(e, template.id)"
                >
                  {{ template.name }}
                </a>
                <span class="help-block text-m m-0">
                  {{ template.columns.length }} عناصر
                </span>
              </td>
              <td colspan="3">
                <div
                  class="rec-list rec-list--horizontal rec-list--align-center rec-list--align-left nowrap"
                >
                  <div class="text-right more-opt d-inline-block ml-15">
                    <button
                      class="btn btn-default btn--more-nav rec-btn rec-btn--trans"
                    >
                      <div class="dot"></div>
                      <ul class="rec-list rec-list--vertical more-options">
                        <li>
                          <a
                            class="font-13 text-default"
                            @click="(e) => showEditModal(e, template.id)"
                          >
                            <i
                              class="sicon-edit flip-x text-primary mr-5 font-14"
                            ></i>
                            تعديل القالب
                          </a>
                        </li>
                        <li>
                          <a
                            class="font-13 text-default"
                            @click="(e) => deleteTemplate(e, template.id)"
                          >
                            <i
                              class="sicon-trash-2 flip-x text-danger mr-5 font-14"
                            ></i>
                            حذف القالب
                          </a>
                        </li>
                      </ul>
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import NoData from './NoData.vue'

export default {
  components: { NoData },

  data() {
    return {}
  },
  computed: {
    ...mapState({
      sections: (state) => state.sections,
      activeTab: (state) => state.activeTab,
      features: (state) => state.features,
      orders: (state) => state.orders,
      products: (state) => state.products,
    }),
    customTemplates() {
      return this.activeTab === 'products'
        ? this.products.templates
        : this.orders.templates
    },
  },
  created() {},
  mounted() {},
  methods: {
    ...mapMutations({
      setActiveTab: 'setActiveTab',
    }),
    deleteTemplate(e, id) {
      e.preventDefault()
      swal({
        title: 'حذف القالب',
        text: 'هل أنت متأكد انك تريد حذف القالب ؟',
        type: 'error',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: 'موافق',
        cancelButtonText: 'إلغاء',
      })
        .then((willDelete) => {
          if (willDelete) {
            this.deleteTemplateRequest(id)
          }
        })
    },
    deleteTemplateRequest(id) {
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': window.token.key,
          's-store-id': store.id,
          ...(window.headers || {}),
        },
      }
      axios
        .delete(window.apiUrl + `/exports/templates/${id}`, config)
        .then(() => {
          swal({
            title: 'تم الحذف بنجاح',
            type: 'success',
            timer: 2000,
          })
          // this.$store.commit('fetchTemplates')
          localStorage.setItem('activeExportTemplateTab', this.activeTab)
          window.location.reload()
        })
        .catch((err) => {
          let message = err.response.data.error.message || 'حدث خطأ ما'
          message += ' برجاء المحاوله مره اخري '
          swal({
            title: 'خطأ',
            text: message,
            type: 'error',
            timer: 2000,
          })
          if (err.response.status == 401) {
            getFreshToken()
          }
        })
    },
    showEditModal(e, id) {
      e.preventDefault()
      const template = this.customTemplates.find((t) => t.id === id)
      const temp_fields = template.columns.map((item) => {
        return {
          ...item,
          added: true,
        }
      })
      this.$store.state.sections = this.sections.map((section) => {
        return {
          ...section,
          columns: section.columns.map((column) => {
            return {
              ...column,
              added: template.columns
                .map((item) => item.name)
                .includes(column.name),
            }
          }),
        }
      })
      this.$store.commit('setActiveTemplate', {
        ...template,
        columns: temp_fields,
      })
      $('#product-templates-modal').modal('show')
    },
  },
}
</script>
<style lang="scss"></style>
