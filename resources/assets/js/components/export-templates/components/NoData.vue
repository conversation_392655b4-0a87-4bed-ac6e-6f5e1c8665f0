<template>
  <div id="reports_placeholder" class="rec-placeholder mt-50 mb-50">
    <div v-if="showIcon" class="rec-placeholder__icon">
      <i class="sicon-cloud-download"></i>
    </div>
    <h2 class="rec-placeholder__title">{{ title }}</h2>
    <div v-if="subtitle" class="rec-placeholder__desc">
      <p>
        {{ subtitle }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoData',
  props: {
    title: {
      type: String,
      default: 'لا توجد بيانات',
    },
    subtitle: {
      type: String,
      required: false,
    },
    showIcon: {
      type: Boolean,
      default: false,
    },
  },
}
</script>
