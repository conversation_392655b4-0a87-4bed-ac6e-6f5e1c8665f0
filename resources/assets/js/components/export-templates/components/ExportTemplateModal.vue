<template>
  <Modal
    id="product-templates-modal"
    :modal-title="modalTitle"
    @saveModal="onSaveModal"
  >
    <div class="col-xs-12">
      <div class="form-group">
        <label>اسم القالب</label>
        <div class="input-group" title="سعر التكلفة">
          <span class="input-group-addon input-group-addon-small">
            <i class="sicon-coin-dollar"></i>
          </span>
          <input
            id="name"
            v-model="activeTemplate.name"
            maxlength="50"
            type="text"
            name="name"
            class="form-control"
            placeholder="أدخل اسم القالب"
          />
        </div>
        <label
          v-if="activeTemplate.name.length >= 50"
          class="text-danger font-12 text-left"
        >
          يجب أن يكون الاسم أقل من 50 حرف
        </label>
      </div>
    </div>
    <div class="col-xs-12">
      <div class="row">
        <div class="col-md-6">
          <AddedFields />
        </div>
        <div class="col-md-6">
          <AvailableFields />
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
import AddedFields from './AddedFields.vue'
import AvailableFields from './AvailableFields.vue'
import Modal from './Modal.vue'
import { mapState } from 'vuex'
import { $error } from '../utils'
export default {
  name: 'ExportTemplateModal',
  components: { AddedFields, AvailableFields, Modal },
  computed: {
    ...mapState({
      activeTemplate: (state) => state.activeTemplate,
      activeTab: (state) => state.activeTab,
    }),

    modalTitle() {
      return this.activeTemplate?.id ? 'تعديل قالب تصدير' : 'إضافة قالب تصدير'
    },
  },
  methods: {
    onSaveModal() {
      if (this.activeTemplate.name === '') {
        $error('يجب إدخال اسم القالب')
        return
      }
      if (this.activeTemplate.columns.length === 0) {
        $error('يجب إضافة عناصر للقالب')
        return
      }

      $('#product-templates-modal').modal('hide')
      showLoading()

      const template = {
        columns: this.activeTemplate.columns.map((column) => column.name),
        name: this.activeTemplate.name,
        entity: this.activeTab == 'products' ? 'products' : 'orders',
      }

      if (this.activeTemplate.id) {
        template.id = this.activeTemplate.id
      }
      const url = this.activeTemplate.id
        ? window.apiUrl + '/exports/templates/' + this.activeTemplate.id
        : window.apiUrl + '/exports/templates'
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': window.token.key,
          's-store-id': store.id,
          ...(window.headers || {}),
        },
      }

      let client = ''

      if(this.activeTemplate.id) {
        client = this.updateTemplate(url, template, config)
      } else {
        client = this.newTemplate(url, template, config)
      }

      client
        .then(() => {
          swal({
            title: 'تمت العملية بنجاح',
            text: this.activeTemplate.id
              ? 'تم تعديل القالب بنجاح'
              : 'تم إضافة القالب بنجاح',
            type: 'success',
            showConfirmButton: false,
            timer: 2000,
          })
          // this.$store.commit('fetchTemplates')
          localStorage.setItem('activeExportTemplateTab', this.activeTab)
          window.location.reload()
        })
        .catch((error) => {
          const message = error.message ?? ''
          swal({
            title: ' خطأ',
            text: message,
            type: 'error',
            showConfirmButton: false,
            timer: 2000,
          })
        })
      hideLoading()
    },
    newTemplate(url, template, config) {
      return axios.post(url, template, config)
    },
    updateTemplate(url, template, config) {
      return axios.put(url, template, config)
    }
  },
}
</script>
