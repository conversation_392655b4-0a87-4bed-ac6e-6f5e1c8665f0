<template>
  <div class="panel panel-default">
    <div class="panel-heading border-bottom">
      <h6 class="panel-title">العناصر المتاحة</h6>
    </div>
    <div class="panel-body p-10 template-fields">
      <div v-for="section in sections" :key="section.section">
        <h6
          v-if="
            section.columns.length > 0 &&
            !section.columns.every((column) => column.added)
          "
          class="text-center font-17"
        >
          {{ translations[section.section] }}
        </h6>
        <ul class="list-group no-border">
          <li
            v-for="(item, key) in filteredColumns(section)"
            :key="key"
            class="mb-10 d-flex font-14 justify-content-between align-items-center"
          >
            {{ item.title }}
            <i class="sicon-add" @click="onAddField(item)"></i>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { mapMutations, mapState } from 'vuex'

export default {
  name: 'AvailableFields',
  computed: {
    ...mapState({
      activeTemplate: (state) => state.activeTemplate,
      sections: (state) => state.sections,
      translations: (state) => state.translations,
    }),
    filteredColumns() {
      return (section) => {
        return section.columns.filter((item) => !item.added)
      }
    },
  },
  methods: {
    ...mapMutations(['addTemplateField']),
    onAddField(item) {
      if (
        this.activeTemplate.columns.some((column) => column.name === item.name)
      ) {
        return
      }

      item.added = true
      this.addTemplateField(item)
    },
  },
}
</script>

<style lang="scss"></style>
