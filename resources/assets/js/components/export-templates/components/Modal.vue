<template>
  <div :id="id" class="modal fade modal-backup" data-backdrop="static" >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <h6 class="modal-title">{{modalTitle}}</h6>
          <button type="button" @click="$emit('closeModal')" class="close" data-dismiss="modal">×</button>
        </div>
        <div class="modal-body">
          <slot></slot>
        </div>
        <div class="modal-footer no-icons">
          <button type="button" @click="$emit('saveModal', id)" :disabled="disabled" class="btn btn-info btn-save" v-if="!showEdit">{{saveBtn}}</button>
          <button class="btn btn-info btn-danger" @click="$emit('edit')" v-if="showEdit">تعديل</button>
          <button class="btn btn-info btn-close" @click="$emit('closeModal')" data-dismiss="modal" >{{closeBtn}}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    id: {
      type: String,
      default: '',
    },
    modalTitle: {
      type: String,
      default: '',
    },
    saveBtn: {
      type: String,
      default: 'حفظ',
    },
    closeBtn: {
      type: String,
      default: 'إلغاء',
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showEdit: {
      type: Boolean,
      default: false
    }
  }
}
</script>
