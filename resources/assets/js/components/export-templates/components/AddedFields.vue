<template>
  <div class="panel panel-default">
    <div class="panel-heading border-bottom">
      <h6 class="panel-title">العناصر المضافة</h6>
    </div>
    <div class="panel-body p-10 template-fields">
      <Nodata
        v-if="columns.length === 0"
        title="لم يتم اضافة عناصر بعد"
        subtitle="يمكنك الإضافة من خلال العناصر المتاحة"
      />
      <draggable
        v-else
        v-model="columns"
        tag="ul"
        v-bind="dragOptions"
        class="rec-list rec-list--vertical rec-list--draggable mb-0"
      >
        <li
          v-for="item in columns"
          :key="item.name"
          class="mb-10 d-flex font-14 justify-content-between align-items-center"
        >
          <i class="sicon-menu move-handler"></i>
          <span class="text ml-20">{{ item.title }}</span>
          <i
            class="sicon-trash-2 text-danger"
            @click="onRestoreField(item)"
          ></i>
        </li>
      </draggable>
    </div>
  </div>
</template>
<script>
import Nodata from './NoData.vue'
import { mapMutations, mapState } from 'vuex'
import draggable from 'vuedraggable'
import { addSortToArray } from '../utils'

export default {
  components: { Nodata, draggable },
  data() {
    return {
      dragOptions: {
        handle: '.move-handler',
        easing: 'cubic-bezier(0.86, 0, 0.07, 1)',
        animation: 500,
      },
    }
  },
  computed: {
    columns: {
      get() {
        return this.$store.state.activeTemplate.columns.filter(
          (item) => item.added
        )
      },
      set(value) {
        const newValue = addSortToArray(value)
        this.$store.commit('setAddedFields', newValue)
      },
    },
  },
  methods: {
    ...mapMutations(['restoreTemplateField']),
    onRestoreField(item) {
      this.restoreTemplateField(item)
    },
  },
}
</script>
