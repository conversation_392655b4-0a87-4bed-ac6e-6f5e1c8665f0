<template>
    <div class="rules">
        <h5 class="rules__title text-primary">
            <span>1</span>
             تحديد شركة الشحن
        </h5>
        <div :class="{'form-group': true, 'has-error': errors.title}">
            <div class="input-group">
                <span class="input-group-addon input-group-addon-small"><i class="sicon-type-square"></i></span>
                <input type="text" required="required" class="form-control" placeholder="ادخل عنوان القيد" v-model="titleValue" @change="titleChanged">
            </div>
            <form-error v-show="errors.title" :errors="errors">
                {{ errors.title? errors.title.toString() : 'عنوان القيد مطلوب، ويجب ألا يحتوى على حروف خاصة مثل ـ-@ او سطر جديد'}}
            </form-error>
        </div>
        <div :class="{'form-group': true, 'has-error': errors.shipping_companies_ids}">
            <div class="input-group">
                <span class="input-group-addon input-group-addon-small"><i class="sicon-shipping"></i></span>
                <treeselect
                    :multiple="true"
                    :options="shippingCompanies"
                    :searchable="false"
                    class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                    placeholder="اختر شركة شحن"
                    v-model="rule.shipping_companies_ids"
                    :normalizer="normalizer"
                />
            </div>
            <form-error v-show="errors.shipping_companies_ids" :errors="errors">
                {{ errors.shipping_companies_ids? errors.shipping_companies_ids.toString() : 'شركة الشحن مطلوبة'}}
            </form-error>
        </div>
    </div>
</template>

<script>

import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import FormError from "./FormError";

export default {
    components: {
         Treeselect,
        'form-error': FormError
    },
    props: ['title', 'rule', 'errors', 'shippingCompanies'],
    data() {
        return {
            titleValue: this.title,
            normalizer(node) {
              return {
                label: node.name,
                value: node.id,
              }
            },
        }
    },
    methods: {
        titleChanged(value) {
            this.$emit('titleChanged', this.titleValue);
        },
    },
}
</script>