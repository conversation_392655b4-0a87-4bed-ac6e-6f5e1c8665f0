<template>
    <div class="rules">
        <h5 class="rules__title text-primary">
        <span>2</span>
            اضافة قيد الشحن
        </h5>
      <section v-show="!shippingCompanySelected" class="alert-box alert-box--info mt-15">
        <i class="sicon-alert"></i>
        <article>
          قم بتحديد شركة الشحن اولا لاضافة القيود
        </article>
      </section>
        <div v-show="shippingCompanySelected">
            <div class="form-group form-group--with-delete" v-for="(shipRule, index) in shippingRules" :key="index">
                <div :class="{'rules__groups': true, 'rules__groups-cart-price': true, 'space': shippingRules.length > 1}">
                    
                    <div :class="{'select-group': true, 'has-error': errors['options.types']? true: false}">
                        <span class="input-group-addon input-group-addon-small"><i class="sicon-list-check"></i></span>
                        <v-treeselect
                            v-model="shipRule.selectedRule"
                            @input="updateSelectedTypes()"
                            :searchable="false"
                            class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                            :class="{'vue-treeselect--has-error': errors['options.types']? true: false}"
                            placeholder="اختر احد القيود"
                            :options="shippingRuleTypes"
                            :normalizer="ruleNormalizer"
                       />
                    </div>

                    <!-- product quantity -->
                    <div v-show="shipRule.selectedRule === 'product_quantity'" :class="{'select-group': true, 'categories': true, 'product_quantity_ids': false, 'has-error': errors['options.product_quantity_ids']? true: false}" :style="{flex: 1}">
                        <span class="input-group-addon input-group-addon-small"><i class="sicon-box"></i></span>
                        <v-treeselect
                            v-model="rule.options.product_quantity_ids"
                            :flat="true"
                            :multiple="true"
                            placeholder="اختر المنتجات"
                            :with-icon="true"
                            :load-options="loadProductOptions"
                            :searchable="true"
                            :async="true"
                            :withID="false"
                            loading-text="جارٍ تحميل المنتجات"
                            class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                            :class="{'vue-treeselect--has-error': errors['options.product_quantity_ids']}"
                            value-format="object"
                            :clearable="true"
                            :close-on-select="false"
                        >
                            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                                {{ node.label }}
                            </label>
                        </v-treeselect>
                        <span v-show="shipRule.selectedRule === 'product_quantity'" class="help-block cs error-msg mb-0 text-muted text-light font-13">
                        <strong>تنبيه:</strong> يجب أن لا يتجاوز عدد المنتجات 30 منتجًا.
                      </span>
                        <form-error v-show="errors['options.product_quantity_ids']" class="error-msg mb-0" :errors="errors">
                            {{ errors['options.product_quantity_ids']? errors['options.product_quantity_ids'].toString() : 'حقل المنتجات مطلوب.'}}
                        </form-error>
                    </div>
                    
                    <div v-show="shipRule.selectedRule === 'product_quantity'" :class="{'quantity_field': true, 'condition': true, 'has-error': !!errors['options.product_quantity']}">
                        <input v-model="rule.options.product_quantity" 
                            type="number"
                            placeholder="الكمية"
                            class="form-control full-bordered" />
                        <form-error class="error-msg mb-0" v-if="errors['options.product_quantity']" :errors="errors">
                            {{ (errors['options.product_quantity']||'حقل شرط كمية المنتجات يجب أن يكون عددًا صحيحًا.').toString()}}
                        </form-error>
                    </div>
                    
                    <div v-show="shipRule.selectedRule === 'products_quantity_sum'" :class="{'condition': true, 'has-error': errors['options.products_quantity_sum_condition']? true: false}">
                        <span class="v-select-placeholder" v-show="rule.options.products_quantity_sum_condition === null">شرط الكمية</span>
                        <v-select :options="comparisonOperators" label="name" class="v-select--full"  :searchable="false"
                            v-model="rule.options.products_quantity_sum_condition" :reduce="comparisonOperators => comparisonOperators.value"/>
                        <form-error  class="error-msg mb-0" v-show="errors['options.products_quantity_sum_condition']" :errors="errors">
                            {{ errors['options.products_quantity_sum_condition']? errors['options.products_quantity_sum_condition'].toString() : 'حقل شرط كمية المنتجات مطلوب.'}}
                        </form-error>
                    </div>

                    <div :class="{'fields-group': true, 'has-error': errors['options.products_quantity_sum']? true: false}" v-show="shipRule.selectedRule === 'products_quantity_sum'">
                        <div class="input-group">
                            <input v-model="rule.options.products_quantity_sum" placeholder="كمية المنتجات" class="form-control full-bordered _parseArabicNumbers "
                                 style="padding-right: 5px !important">
                            <span class="input-group-addon">منتج</span>
                        </div>
                        <form-error  class="error-msg mb-0" v-show="errors['options.products_quantity_sum']" :errors="errors">
                            {{ errors['options.products_quantity_sum']? errors['options.products_quantity_sum'].toString() : 'حقل كمية المنتجات مطلوب.'}}
                        </form-error>
                    </div>
                    

                    <!-- branches count -->
                    <div v-show="shipRule.selectedRule === 'branches_count'" :class="{'condition': true, 'has-error': errors['options.branches_count_condition']? true: false}">
                        <span v-show="rule.options.branches_count_condition === null" class="v-select-placeholder">شرط العدد</span>
                        <v-select v-model="rule.options.branches_count_condition" :options="comparisonOperators" label="name" class="v-select--full"  :searchable="false"
                             :reduce="comparisonOperators => comparisonOperators.value"/>
                        <form-error class="error-msg mb-0" v-show="errors['options.branches_count_condition']" :errors="errors">
                            {{ errors['options.branches_count_condition'] ? errors['options.branches_count_condition'].toString() : 'حقل شرط عدد الفروع مطلوب.'}}
                        </form-error>
                    </div>

                    <div v-show="shipRule.selectedRule === 'branches_count'" :class="{'fields-group': true, 'has-error': errors['options.branches_count'] ? true : false}">
                        <div class="input-group">
                            <input v-model="rule.options.branches_count" placeholder="عدد الفروع " class="form-control full-bordered _parseArabicNumbers "
                                 style="padding-right: 5px !important">
                            <span class="input-group-addon">فرع</span>
                        </div>
                        <form-error v-show="errors['options.branches_count']" class="error-msg mb-0" :errors="errors">
                            {{ errors['options.branches_count']? errors['options.branches_count'].toString() : 'حقل عدد الفروع مطلوب.'}}
                        </form-error>
                    </div>
                    <!-- branches count end -->
                     
                    <div :class="{'select-group': true, 'categories': true, 'has-error': errors['options.included_category_ids']? true: false}" v-show="shipRule.selectedRule === 'category'">
                        <span class="input-group-addon input-group-addon-small"><i class="sicon-tag-special"></i></span>
                        <v-treeselect
                            v-model="rule.options.included_category_ids"
                            :flat="true"
                            :multiple="true"
                            :searchable="false"
                            :show-count="false"
                            :default-expand-level="2"
                            :clearable="true"
                            :close-on-select="false"
                            :loading-text="'جاري جلب البيانات...'"
                            :no-children-text="'...'"
                            :is-default-expanded="true"
                            :clear-on-select="false"
                            class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                            :class="{'vue-treeselect--has-error': errors['options.included_category_ids']}"
                            placeholder="اختر تصنيف المنتج"
                            :options="categories"
                            :normalizer="normalizer"
                            noResultsText="عفوا! لا توجد نتائج بحث"
                        >
                            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                                {{ node.label }}
                            </label>
                        </v-treeselect>
                      <span v-show="shipRule.selectedRule === 'category'" class="help-block cs error-msg mb-0 text-muted text-light font-13">
                        <strong>تنبيه:</strong> يجب أن لا يتجاوز عدد التصنيات {{ maxCategoryIds }} تصنيف.
                      </span>
                        <form-error  class="error-msg mb-0" v-show="errors['options.included_category_ids']" :errors="errors">
                            {{ errors['options.included_category_ids']? errors['options.included_category_ids'].toString() : 'حقل التصنيف مطلوب.'}}
                        </form-error>
                    </div>

                    <div v-show="shipRule.selectedRule === 'cart-price'" :class="{'condition': true, 'has-error': errors['options.cart_price_condition']? true: false}">
                        <span class="v-select-placeholder" v-show="rule.options.cart_price_condition === null">الشرط</span>
                        <v-select :options="comparisonOperators" label="name" class="v-select--full"  :searchable="false"
                            v-model="rule.options.cart_price_condition" :reduce="comparisonOperators => comparisonOperators.value"/>
                        <form-error  class="error-msg mb-0" v-show="errors['options.cart_price_condition']" :errors="errors">
                            {{ errors['options.cart_price_condition']? errors['options.cart_price_condition'].toString() : 'حقل شرط قيمة سلة المشتريات مطلوب.'}}
                        </form-error>
                    </div>

                    <div :class="{'fields-group': true, 'has-error': errors['options.cart_price']? true: false}" v-show="shipRule.selectedRule === 'cart-price'">
                        <div class="input-group">
                            <input type="text" placeholder="قيمة المشتريات" class="form-control full-bordered "
                                v-model="rule.options.cart_price" style="padding-right: 5px !important">
                            <span class="input-group-addon">ر.س</span>
                        </div>
                        <form-error  class="error-msg mb-0" v-show="errors['options.cart_price']" :errors="errors">
                            {{ errors['options.cart_price']? errors['options.cart_price'].toString() : 'حقل قيمة سلة المشتريات مطلوب.'}}
                        </form-error>
                    </div>

                    <div v-show="shipRule.selectedRule === 'cart-weight'" :class="{'condition': true, 'has-error': errors['options.cart_weight_condition']? true: false}">
                        <span class="v-select-placeholder" v-show="rule.options.cart_weight_condition === null">الشرط</span>
                        <v-select :options="comparisonOperators" class="v-select--full" label="name"  :searchable="false"
                            v-model="rule.options.cart_weight_condition" :reduce="comparisonOperators => comparisonOperators.value"/>
                        <form-error  class="error-msg mb-0" v-show="errors['options.cart_weight_condition']" :errors="errors">
                            {{ errors['options.cart_weight_condition']? errors['options.cart_weight_condition'].toString() : 'حقل شرط وزن سلة المشتريات مطلوب.'}}
                        </form-error>
                    </div>

                    <div :class="{'fields-group': true, 'has-error': errors['options.cart_weight']? true: false}" v-show="shipRule.selectedRule === 'cart-weight'">
                        <div class="input-group">
                            <input type="text" placeholder="وزن المشتريات" class="form-control full-bordered "
                                v-model="rule.options.cart_weight" style="padding-right: 5px !important">
                            <span class="input-group-addon">KG</span>
                        </div>
                        <form-error  class="error-msg mb-0" v-show="errors['options.cart_weight']" :errors="errors">
                            {{ errors['options.cart_weight']? errors['options.cart_weight'].toString() : 'حقل وزن سلة المشتريات مطلوب.'}}
                        </form-error>
                    </div>

                    <div :class="{'select-group': true, 'categories': true, 'has-error': errors['options.customer_group_id']? true: false}"
                         v-show="shipRule.selectedRule === 'customer-group'">
                      <span class="input-group-addon input-group-addon-small"><i class="sicon-tag-special"></i></span>
                      <v-treeselect
                          v-model="rule.options.customer_group_id"
                          :flat="false"
                          :multiple="false"
                          :searchable="true"
                          :show-count="false"
                          :default-expand-level="2"
                          :clearable="true"
                          :close-on-select="false"
                          :loading-text="'جاري جلب البيانات...'"
                          :no-children-text="'...'"
                          :is-default-expanded="true"
                          :clear-on-select="false"
                          noResultsText="عفوا! لا توجد نتائج بحث"
                          :class="{'vue-treeselect--custom': true, 'vue-treeselect--without-effect': true,
                              'vue-treeselect--has-error': errors['options.customer_group_id'],
                              'vue-treeselect--with-icon': true, 'vue-treeselect--initial': true}"
                          placeholder="اختر مجموعة العملاء"
                          :options="customerGroups"
                          :normalizer="normalizer"
                      >
                        <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                          {{ node.label }}
                        </label>
                      </v-treeselect>
                      <form-error  class="error-msg mb-0" v-show="errors['options.customer_group_id']" :errors="errors">
                        {{ errors['options.customer_group_id']? errors['options.customer_group_id'].toString() : 'حقل مجموعة العملاء مطلوب.'}}
                      </form-error>
                    </div>

                    <!-- companies periods start -->
                    <div v-show="shipRule.selectedRule === 'company-periods'" :class="{'select-group': true, 'categories': true, 'has-error': errors['options.company_periods.days']? true: false}" :style="{flex: 1}">
                      <span class="input-group-addon input-group-addon-small"><i class="sicon-clock"></i></span>
                      <v-treeselect
                          v-model="rule.options.company_periods.days"
                          :flat="false"
                          :multiple="true"
                          :searchable="false"
                          :show-count="false"
                          :default-expand-level="2"
                          :clearable="true"
                          :close-on-select="false"
                          :is-default-expanded="true"
                          :clear-on-select="false"
                          class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                          :class="{'vue-treeselect--has-error': errors['options.company_periods.days']}"
                          placeholder="حدد الايام لتطبيق القيد"
                          :options="companyDays"
                          :normalizer="ruleNormalizer"
                      >
                      <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                          {{ node.label }}
                      </label>
                      </v-treeselect>
                      <form-error  class="error-msg mb-0" v-show="errors['options.company_periods.days']" :errors="errors">
                        {{ errors['options.company_periods.days']? errors['options.company_periods.days'].toString() : 'حقل الايام مطلوب.'}}
                      </form-error>
                    </div>

                    <div v-show="shipRule.selectedRule === 'company-periods'" :style="{flex: '0 0 15%'}" :class="{'condition': true, 'has-error': errors['options.company_periods.time_from']? true: false}">
                      <span class="v-select-placeholder"  v-show="!rule.options.company_periods.time_from">بداية الوقت</span>
                      <v-select :options="timesOptions" class="vue-treeselect--custom" label="name"  :searchable="false"
                                v-model="rule.options.company_periods.time_from" :reduce="timesOptions => timesOptions.value"/>
                      <form-error  class="error-msg mb-0" v-show="errors['options.company_periods.time_from']" :errors="errors">
                        {{ errors['options.company_periods.time_from']? errors['options.company_periods.time_from'].toString() : 'حقل بداية الوقت مطلوب.'}}
                      </form-error>
                    </div>

                    <div v-show="shipRule.selectedRule === 'company-periods'"  :style="{flex: '0 0 15%'}" :class="{'condition': true, 'has-error': errors['options.company_periods.time_to']? true: false}">
                      <span class="v-select-placeholder" v-show="!rule.options.company_periods.time_to">نهاية الوقت</span>
                      <v-select :options="timesOptions" class="vue-treeselect--custom" label="name"  :searchable="false"
                                v-model="rule.options.company_periods.time_to" :reduce="timesOptions => timesOptions.value"/>
                      <form-error  class="error-msg mb-0" v-show="errors['options.company_periods.time_to']" :errors="errors">
                        {{ errors['options.company_periods.time_to']? errors['options.company_periods.time_to'].toString() : 'حقل نهاية الوقت مطلوب.'}}
                      </form-error>
                    </div>
                    <!-- companies periods end -->

                    <div v-show="shipRule.selectedRule === 'branch_id'" :class="{'select-group': true, 'categories': true, 'has-error': errors['options.branch_id'] ? true: false}">
                        <span class="input-group-addon input-group-addon-small"><i class="sicon-tag-special"></i></span>
                        <v-treeselect
                            v-model="rule.options.branch_id"
                            :flat="true"
                            :multiple="true"
                            :searchable="true"
                            :show-count="false"
                            :default-expand-level="2"
                            :clearable="true"
                            :close-on-select="false"
                            :loading-text="'جاري جلب البيانات...'"
                            :no-children-text="'...'"
                            :is-default-expanded="true"
                            :clear-on-select="true"
                            class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                            :class="{'vue-treeselect--has-error': errors['options.branch_id']}"
                            placeholder="اختر الفرع/المستودع"
                            :options="branches"
                            :normalizer="normalizer"
                            noResultsText="عفوا! لا توجد نتائج بحث"
                        >
                            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
                                {{ node.label }}
                            </label>
                        </v-treeselect>
                        <form-error  class="error-msg mb-0" v-show="errors['options.branch_id']" :errors="errors">
                          {{ errors['options.branch_id']? errors['options.branch_id'].toString() : 'حقل الفرع/المستودع مطلوب.'}}
                        </form-error>
                    </div>
                    
                    <div v-show="shipRule.selectedRule" :class="{'fields-group': true, 'has-error': ((errors['options.inclusion_criteria'] || errors['options.inclusion_criteria.' + shipRule.selectedRule])) ? true : false}">
                        <v-treeselect
                            v-model="shipRule.inclusionCriteria"
                            :options="criteria"
                            :clearable="false"
                            class=" vue-treeselect--enhanced-tags vue-treeselect--without-effect"
                            :searchable="false"
                            :class="{'vue-treeselect--has-error': false}"
                            placeholder="الشرط الاضافي"
                            @input="updateSelectedTypes()"
                            >
                                <label slot="option-label" slot-scope="{ node, labelClassName }" :class="labelClassName">
                                    <div><b>{{ node.label }}</b></div>
                                    <span v-if="node.id" class="help-block cs error-msg m-0 text-muted text-light font-11">{{ 'السماح باستخدام عناصر القيد مع شركة الشحن' }}</span>
                                    <span v-else class="help-block cs error-msg m-0 text-muted text-light font-11">{{ 'منع استخدام عناصر القيد مع شركة الشحن' }}</span>
                                </label>
                        </v-treeselect>
                        <form-error  v-show="((errors['options.inclusion_criteria'] || errors['options.inclusion_criteria.' + shipRule.selectedRule]))" class="error-msg mb-0" :errors="errors">
                            {{ (errors['options.inclusion_criteria.' + shipRule.selectedRule] ? errors['options.inclusion_criteria.' + shipRule.selectedRule].toString() : (errors['options.inclusion_criteria'] ? errors['options.inclusion_criteria'].toString() : 'حقل الشرط الاضافي مطلوب.')) }}
                        </form-error>
                    </div>
                    
                    <button type="button" class="btn  m-0" @click="deleteRule(index)" v-show="shippingRules.length > 1" style="background: unset; transform: translateY(0px);">
                        <i class="sicon-trash-2"></i>
                    </button>
                </div>
                <map-area  v-if="shipRule.selectedRule === 'specific-area'" v-on:currentPlaceEvent="currentPlace" :selectedBounds="selectedBounds" :errors="errors"/>
                <hr />
            </div>
            <button :disabled="disabled" class="btn btn-add btn--outlined primary" type="button" @click="addRule" ><i class="sicon-add"></i>اضافة قيد جديد</button>
        </div>
    </div>
</template>

<script>

import MapArea from './MapArea';
import vSelect from 'vue-select';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import FormError from "./FormError";
import { ASYNC_SEARCH } from '@riophae/vue-treeselect';

export default {
    components: {
        'v-select': vSelect,
        'map-area': MapArea,
        'v-treeselect': Treeselect,
        'form-error': FormError
    },
    props: [
        'shippingCompanySelected',
        'types',
        'setInclusionCriteria',
        'selectedBounds',
        'categories',
        'maxCategoryIds',
        'branches',
        'selectedTypes',
        'inclusionCriteria',
        'comparisonOperators',
        'shippingRuleTypes',
        'errors',
        'customerGroups',
        'rule',
        'selectedBoundsChanged',
        'companyDays'
    ],
    data() {
        return {
            shippingRules: [
                {
                    selectedRule: null,
                }
            ],
            normalizer(node) {
              return {
                label: node.name,
                value: node.id,
                children: node.sub_categories,
              }
            },
            ruleNormalizer(node) {
              return {
                label: node.name,
                id: node.value,
                isDisabled: node.disabled
              }
			      },
            timesOptions: [],
            criteria: [
                {
                    label: 'ضمن',
                    id: true,
                    placeholder: 'السماح باستخدام عناصر القيد مع شركة الشحن', 
                },
                {
                    label: 'ليس ضمن',
                    id: false,
                    placeholder: 'منع استخدام عناصر القيد مع شركة الشحن', 
                },
            ],
        }

    },
    computed: {
        disabled() {
            return this.shippingRules.length >= 5 ? 'disabled'  : false;
        }
    },

    watch: {
		shippingRules: {
			handler(newVal){
				if(newVal){
					for(let opt of this.shippingRuleTypes || []){
						const findValue = this.shippingRules.find(item => {
							return item.selectedRule === opt.value
						});

						if(findValue){
							opt.disabled = true;
						} else{
							opt.disabled = false;
						}
					}
				}
			},
			deep: true
		}
	},
    created() {
        if (this.selectedTypes.length) {
            this.shippingRules = [];
            this.selectedTypes.forEach((val) => {
                this.shippingRules.push({
                    selectedRule: val,
                    inclusionCriteria: val in this.inclusionCriteria ? this.inclusionCriteria[val] : true
                });
            })
        }

      this.generateTimeOptions();
    },
    methods: {
        addRule() {
            if (this.shippingRules[this.shippingRules.length -1].selectedRule == null) {
                swal({
					title: "تنبيه",
                    text: 'قم باختيار نوع القيد',
                    type: 'error',
                    showConfirmButton: true,
                    confirmButtonText: 'موافق'
                });
                return false;
            }

            this.shippingRules.push({
                selectedRule: null,
            })
        },

        deleteRule(index) {
            this.shippingRules.splice(index, 1);
            this.updateSelectedTypes();
        },

        updateSelectedTypes() {
            var typesOptions =  [];
            var criteriaOptions = {};

            this.shippingRules.forEach((shipRule) => {
                if (shipRule.selectedRule !== null) {
                    typesOptions.push(shipRule.selectedRule);
                    criteriaOptions[shipRule.selectedRule] = (shipRule.inclusionCriteria === true || shipRule.inclusionCriteria === false) ? shipRule.inclusionCriteria : null;
                }
            });

            this.types(typesOptions);
            this.setInclusionCriteria(criteriaOptions);
        },

        currentPlace(val) {
            this.selectedBoundsChanged(val)
        },
      generateTimeOptions() {
        for (let hour = 0; hour < 24; hour++) {
          const time = hour.toString().padStart(2, '0') + ":00";
          this.timesOptions.push({ id: time, name: time, value: time });
        }
      },

      loadProductOptions({ action, searchQuery, callback }) {
        if (action === ASYNC_SEARCH) {
            axios.get(`/order/product_search?q=${searchQuery}&without_donating_products=true`)
            .then(res => {
                const products = res.data.map(i => ({
                    id: i.id,
                    label: i.name,
                }));
                callback(null, products);
            })
            .catch(e => {
                    callback(new Error('حدث خطأ ما، رجاءاً حاول مرةً ثانية'));
            });
        }
      },
    },
}
</script>