<template>
  <div>
    <section id="shipping_companies_required_location" class="alert-box alert-box--info mt-15">
      <i class="sicon-alert"></i>
      <article>
        <ul>
          <li><a href="/help_center/MzEG">تفعيل تحديد الموقع الجغرافي للعميل</a> متطلب حتى تعمل بشكل سليم</li>
          <li>قم برسم حدود المنطقة المدعومة</li>
        </ul>
      </article>
    </section>
    <div class="map-view" :class="{'has-error': errors['options.bounds']? true: false}">
      <div class="controls">
        <button type="button" class="btn btn-tiffany btn-add-poly" @click="addPath" v-show="paths.length < 1">
          <i class="sicon-add"></i> <span>اضافة تحديد</span>
        </button>
        <button type="button" class="btn btn-danger btn-delete-poly" @click="removePath" v-show="paths.length">
          <i class="sicon-cancel"></i><span>الغاء تحديد</span>
        </button>
        <button id="current-location" type="button" class="btn btn-tiffany " @click="getCurrentLocation">
          <i class="sicon-location-target"></i>
          موقعي الحالي
        </button>
        <gmap-autocomplete
            class="form-control location-find"
            id="map_search"
            placeholder="ابحث عن موقع..."
            @place_changed="addMarkerSearch">
        </gmap-autocomplete>
      </div>
      <div class="alert alert-paid alert-styled-left" v-show="message !== null">
        <span>{{ message }}</span>
      </div>
      <GmapMap
          :options=gMapOptions
          :center="defaultPosition"
          :zoom="zoom"
          style="width: 100%; height: 350px"
          ref="map"
      >
        <gmap-polygon
            v-if="paths.length > 0"
            :paths="paths"
            :editable="true"
            @paths_changed="updateEdited($event)"
            :options="optionsPoly"
            ref="polygon">
        </gmap-polygon>
      </GmapMap>

      <!--server side validation -->
      <form-error v-show="errors['options.bounds']" :errors="errors">
        {{ errors['options.bounds'] ? errors['options.bounds'].toString() : 'حقل المنطقة على الخريطة مطلوب.' }}
      </form-error>
    </div>
  </div>
</template>


<script>

import * as VueGoogleMaps from 'vue2-google-maps';
import Vue from 'vue';
import FormError from "./FormError";

Vue.use(VueGoogleMaps, {
  load: {
    key: 'AIzaSyBPhPJ4KG13ywvmeAovLRnbi7WzlsdcWKs',
    libraries: 'places',
  },
  installComponents: true,
});


export default {
  components: {
    'form-error': FormError
  },
  props: ['selectedBounds', 'errors'],

  data() {
    return {
      edited: null,
      paths: this.selectedBounds.length > 0 ? this.selectedBounds : [],
      optionsPoly: {
        strokeColor: "#5dd5c4",
        fillColor: "#8ce1d5",
      },
      mvcPaths: null,
      errorMessage: null,
      polygonGeojson: '',
      gMapOptions: {
        zoomControl: true,
        scrollwheel: false,
        mapTypeControl: false,
        scaleControl: true,
        streetViewControl: false,
        rotateControl: false,
        fullscreenControl: false,
        disableDefaultUi: false,
      },
      zoom: 10,
      defaultPosition: {lat: 24.7756042, lng: 46.7360729},
      // currentPlace: {lat: 24.7756042, lng: 46.7360729},
      message: null
    }
  },

  watch: {
    polygonPaths: _.throttle(function (paths) {
      if (paths) {
        this.paths = paths
        this.polygonGeojson = JSON.stringify({
          type: 'Polygon',
        }, null, 2)
      }
    }, 1000)
  },
  computed: {
    polygonPaths: function () {
      if (!this.mvcPaths) return null

      let paths = [];
      for (let i = 0; i < this.mvcPaths.getLength(); i++) {
        let path = [];
        for (let j = 0; j < this.mvcPaths.getAt(i).getLength(); j++) {
          let point = this.mvcPaths.getAt(i).getAt(j);
          path.push({lat: point.lat(), lng: point.lng()});
        }
        paths.push(path);
      }

      this.$emit('currentPlaceEvent', paths);

      return paths;
    },
    google: VueGoogleMaps.gmapApi
  },
  methods: {
    centerMapOnPolygonBounds() {
      let bounds = new google.maps.LatLngBounds(),
          i,
          polygonCoords = this.selectedBounds[0];

      for (i = 0; i < polygonCoords.length; i++) {
        bounds.extend(polygonCoords[i]);
      }
      const newCenter = {
        lat: bounds.getCenter().lat(),
        lng: bounds.getCenter().lng()
      }
      this.$refs.map.fitBounds(bounds);

      this.defaultPosition = newCenter;
    },
    updateCenter: function (place) {
      this.center = {
        lat: place.geometry.location.lat(),
        lng: place.geometry.location.lng(),
      }
    },
    updateEdited: function (mvcPaths) {
      this.mvcPaths = mvcPaths
    },
    addPath: function () {
      if (this.paths.length > 0) {
        return;
      }

      // obtain the bounds, so we can guess how big the polygon should be
      let bounds = this.$refs.map.$mapObject.getBounds(),
          northEast = bounds.getNorthEast(),
          southWest = bounds.getSouthWest(),
          center = bounds.getCenter(),
          degree = this.paths.length + 1,
          f = Math.pow(0.66, degree);

      let path = [
        {lng: center.lng(), lat: (1 - f) * center.lat() + (f) * northEast.lat()},
        {lng: (1 - f) * center.lng() + (f) * southWest.lng(), lat: (1 - f) * center.lat() + (f) * southWest.lat()},
        {lng: (1 - f) * center.lng() + (f) * northEast.lng(), lat: (1 - f) * center.lat() + (f) * southWest.lat()},
      ]

      this.paths.push(path);

      this.$emit('currentPlaceEvent', this.paths);
    },
    removePath: function () {
      this.paths.splice(this.paths.length - 1, 1);

      this.$emit('currentPlaceEvent', this.paths);
    },
    getCurrentLocation: function () { // Getting the user current location
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(coordinate => {

              const newCoordinate = {
                lat: coordinate.coords.latitude,
                lng: coordinate.coords.longitude,
              }

              this.UpdateMarker(newCoordinate);

            },
            error => {
              this.message = 'ننصح بالسماح بصلاحية تحديد الموقع من اعدادات المتصفح لتحديد موقعك بشكل تلقائي.'
            })
      } else {
        this.message = 'ميزة تحديد الموقع الجغرافي غير مدعومة في متصفحك.'
      }
    },
    UpdateMarker(marker, manual = false) { // Updating the marker location
      this.defaultPosition = marker;
      this.zoom = 15;

      this.removePath();
              
      setTimeout( () => {
        this.addPath();
      }, 200);
    },
    addMarker(ev) { // Updating the marker location on locate manual location
      const position = {
        lat: ev.latLng.lat(),
        lng: ev.latLng.lng(),
      };
      this.UpdateMarker(position, true);
    },
    addMarkerSearch(ev) { // Updating the marker location on input search
      const position = {
        lat: ev.geometry.location.lat(),
        lng: ev.geometry.location.lng()
      }
      this.UpdateMarker(position);
    }
  },
  mounted() {
    this.$refs.map.$mapPromise.then((map) => {
      this.map = map;
      setTimeout(() => {
        if (this.selectedBounds.length) {
          this.centerMapOnPolygonBounds()
        } else {
          this.addPath();
        }
      }, 1000);
    });

  }
}
</script>