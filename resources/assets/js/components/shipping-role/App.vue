<template>
    <div>
        <a v-if="isMobileApp" href="#!" @click.prevent="handleBack()" style="line-height: 46px;; display: block" class="btn-products-back mb-20">
            <i class="sicon-caret-right-circle right-back-arrow"></i>
            عودة لقيود شركات الشحن
        </a>
        <div class="panel panel-default mb-20">
            <div class="panel-heading rec-list rec-list--horizontal p-15">
              <i class="sicon-compass-direction mr-10 pt-5"></i>
              <div>
                <h6 class="panel-title">اضافة قيد شركة شحن جديد</h6>
                <p class="text-small text-muted text-light font-13 no-margin">عند إضافة قيد، ستظهر شركات شحن محددة لعملائك حسب الشروط المضافة</p>
              </div>
            </div>
            <div class="panel-body">
                <form class="shipping-role">

                    <shipping-companies
                    :shippingCompanies="shippingCompanies"
                    :selectedShippingCompaniesIds="rule.shipping_companies_ids"
                    :title="rule.title"
                    v-on:titleChanged="titleChanged"
                    :rule="rule"
                    :errors="errors" />

                    <shipping-rule
                        :rule="rule"
                        :shippingCompanySelected="rule.shipping_companies_ids.length"
                        :types="typesSelected"
                        :set-inclusion-criteria="setInclusionCriteria"
                        :selectedTypes="rule.options.types"
                        :inclusion-criteria="rule.options.inclusion_criteria"
                        :shippingRuleTypes="shippingRuleTypes"
                        :categories="categories"
                        :maxCategoryIds="maxCategoryIds"
                        :branches="branches"
                        :comparisonOperators="comparisonOperators"
                        :selectedBounds="rule.options.bounds"
                        :errors="errors"
                        :customerGroups="customerGroups"
                        :companyDays="companyDays"
                        :selectedBoundsChanged="boundsSelected"
                    />
                </form>
            </div>
        </div>
        <div class="rec-list rec-list--horizental controls align-items-stretch">
            <button
                class="btn btn-tiffany btn-save btn-full"
                :disabled="!rule.shipping_companies_ids.length || isShippingRulesAccessDenied"
                v-on:click="saveRule($event)"
                data-inline-loader
            >
              {{saveRuleText}}
            </button>
            <!-- TODO:: Show This If It's Going To Be Shipping Rule Edit -->
            <button
                class="btn btn-danger btn-delete btn-full h-auto"
                v-show="rule.id != 0"
                :disabled="isShippingRulesAccessDenied"
                v-on:click="deleteRule(rule)"
            >
              {{deleteRuleText}}
            </button>
        </div>
    </div>
</template>

<script>

import ShippingCompanies from './components/ShippingCompanies';
import ShippingRule from './components/ShippingRule';
import Http from "../../utils/http";
import initialData from "./RuleService";
import FormError from "./components/FormError";

export default {
    components: {
        'shipping-companies': ShippingCompanies,
        'shipping-rule': ShippingRule,
        'form-error': FormError
    },
    data() {
        return {
            isLegacy: window.sallaLegacy,
            shippingCompanySelected: false,
            disableSave: false,
            saveRuleText: 'حفظ',
            deleteRuleText: 'حذف',
            errors: [],
            submitting: false,
            rule: {
                id: 0,
                title: '',
                shipping_companies_ids: null,
                options: {
                    types: [],
                    inclusion_criteria: {},
                    included_category_ids: null,
                    product_quantity_ids: null,
                    product_quantity: null,
                    cart_price_condition: null,
                    cart_price: null,
                    cart_weight_condition: null,
                    cart_weight: null,
                    bounds: null,
                    customer_group_id: null,
                    company_periods:{
                      days : [],
                      time_from: null,
                      time_to: null
                    },
                    branch_id: null,
                    products_quantity_sum: null,
                    products_quantity_sum_condition: null
                }
            },
            categories: [],
            maxCategoryIds: 30,
            customerGroups: [],
            comparisonOperators:[],
            shippingRuleTypes:[],
            shippingCompanies:[],
            companyDays:[],
            isShippingRulesAccessDenied: false
        }
    },
	computed: {
		isMobileApp: () => {
        if (typeof dataLayer !== 'undefined' && dataLayer.length) {
				return dataLayer[0].page.mobileApp;
			}
		}
	},
    methods: {
        titleChanged(value) {
            this.rule.title = value;
        },
        typesSelected(value) {
            this.rule.options.types = value;
        },
        setInclusionCriteria(criteria) {
            this.rule.options.inclusion_criteria = criteria;
        },
        saveRule(event) {
            event.preventDefault();

            this.saveRuleText = 'جاري الحفظ.';
            this.submitting = true;
            this.errors = [];

            if (this.submitting) {
                (this.rule.id <= 0)? this.createRule(event) : this.updateRule(event);
            }
        },

        boundsSelected(value) {
            this.rule.options.bounds = value;
        },

        createRule(event){
          showLoading(event)
            Http.post('/shipping/rules', this.transformRulePayload(this.rule),
                ({data}) => {
                    this.showMessage('تم الحفظ بنجاح');
                },
                ({ response }) => {
                    window.hideLoading();
                    if (response) {
                        this.saveRuleText = 'حفظ';
                        this.submitting = false;
                        this.errors = response.data.error.fields;
                        try {
                            laravel.ajax.errorHandler(response);
                        } catch (_) {
                            //
                        }
                    }
                });
        },

        updateRule(event) {
          showLoading(event)
            Http.put('/shipping/rules/' + this.rule.id, this.transformRulePayload(this.rule),
                ({resp}) => {
                    this.showMessage('تم الحفظ بنجاح');
                },
                ({ response }) => {
                    window.hideLoading();
                    if (response) {
                        this.saveRuleText = 'حفظ';
                        this.submitting = false;
                        this.errors = response.data.error.fields;
                        try {
                            laravel.ajax.errorHandler(response);
                        } catch (_) {
                            //
                        }
                    }
                });
        },
        transformRulePayload(rule) {
            rule = JSON.parse(JSON.stringify(rule));
            if (rule.options.types.indexOf("company-periods") === -1) {
              delete rule.options.company_periods;
            }

            if (rule.options.types.indexOf("product_quantity") !== -1) {
              rule.options.product_quantity_ids = rule.options.product_quantity_ids?.map((item) => {
                return item.id;
              })
            } else {
              delete rule.options.product_quantity_ids
              delete rule.options.product_quantity
            }

            if (! rule.options.product_quantity) {
                delete rule.options.product_quantity
            }

            if (rule.options.types.indexOf("branch_id") == -1) {
              delete rule.options.branch_id
            }

            if (rule.options.types.indexOf("products_quantity_sum") == -1) {
              delete rule.options.products_quantity_sum
              delete rule.options.products_quantity_sum_condition
            }

            return rule;
        },
        deleteRule(rule) {
            event.preventDefault();

            swal({
                title: "تنبيه ",
                text: 'هل انت متاكد من هذا الاجراء ؟',
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "تأكيد",
                cancelButtonText: "إلغاء"
            }).
            then(() => {
                 return axios
                    .request({url: '/shipping/rules/' + rule.id, rule, method: 'delete', responseType: 'json' })
                     .then(() => {
                        this.showMessage('تم الحذف بنجاح');
                     }).catch(() => {
                        window.hideLoading();
                    });
            });
        },
        handleBack () {
            window.history.back();
        },
        showMessage(message) {
            window.hideLoading();
            swal({title: message, type: 'success', showConfirmButton: false, timer: 1500 }).catch(() => {
                this.navigateTo('/shipping/rules');
            });
        },
        navigateTo(path) {
            if (this.isLegacy) {
                window.location = path;
            } else {
                window.history.back();
            }
        }
    },

    created() {
        this.rule = initialData.getDatum('rule', []);
        this.categories = initialData.getDatum('categories', []);
        this.maxCategoryIds = initialData.getDatum('maxCategoryIds', []);
        this.branches = initialData.getDatum('branches', []);
        this.comparisonOperators = initialData.getDatum('comparisonOperators', []);
        this.shippingRuleTypes = initialData.getDatum('shippingRuleTypes', []);
        this.customerGroups = initialData.getDatum('customerGroups', []);
        this.shippingCompanies = initialData.getDatum('shippingCompanies', []);
        this.companyDays = initialData.getDatum('shippingRuleDays', []);
        this.isShippingRulesAccessDenied = initialData.getDatum('isShippingRulesAccessDenied', false);

        if (Object.keys(this.rule.shipping_companies_ids).length > 0) {
            this.shippingCompanySelected = true;
            this.disableSave = false
        } else {
            this.shippingCompanySelected = false;
            this.disableSave = 'disabled';
        }
    },
}
</script>
