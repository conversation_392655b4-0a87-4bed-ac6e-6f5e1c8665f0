export default {
    getDatum(key, defaultValue = null) {
        if (initialData instanceof Object) {
            return initialData.hasOwnProperty(key) ? initialData[key] : defaultValue;
        }

        return defaultValue;
    },

    setDatum(key, data) {
        if (initialData instanceof Object && initialData.hasOwnProperty(key)) {
            initialData[key] = data;

            return;
        }

        console.error('not found');
    },

    setData(data) {
        if (initialData instanceof Object) {
            _.assign(initialData, data);

            return;
        }

        console.error('not found');
    },

    setDatumProperty(key , property , data){
        if (initialData instanceof Object && initialData.hasOwnProperty(key)) {
            initialData[key][property] = data;

            return;
        }
        console.error('not found');
    }
}
;
