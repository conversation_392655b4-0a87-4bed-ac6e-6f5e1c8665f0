<template>
  <div id="modal_digital_card_management" :class="{ 'custom-modal': true, fade: showModal }">
    <div :class="{ 'modal-dialog': true, show: showDialog }">
      <div class="modal-content">
        <!----- modal Header -------->
        <div class="modal-header bg-info">
          <button type="button" class="close" @click="closeModal">&times;</button>
          <h6 class="modal-title">إدارة الأكواد - {{ product !== null ? product.name : null }}</h6>
        </div>
        <!----- modal Body -------->
        <div class="modal-body">
          <form class="mb-0">
            <div class="row">
              <div class="col-xs-12">
                <section
                  v-show="exceed_variants_limit"
                  id="exceed_variants_limit"
                  class="alert-box alert-box--danger alert-dismissable mb-20"
                >
                  <span class="mr-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none">
                      <path
                        d="M13.9374 3.25018L24.2575 21.1252C24.5567 21.6434 24.3791 22.3059 23.861 22.605C23.6963 22.7002 23.5095 22.7502 23.3193 22.7502H2.67904C2.08072 22.7502 1.5957 22.2652 1.5957 21.6669C1.5957 21.4766 1.64575 21.2899 1.74084 21.1252L12.061 3.25018C12.3602 2.73202 13.0226 2.5545 13.5408 2.85365C13.7056 2.94873 13.8423 3.08549 13.9374 3.25018ZM4.55542 20.5835H21.443L12.9991 5.95851L4.55542 20.5835ZM11.9158 17.3335H14.0825V19.5002H11.9158V17.3335ZM11.9158 9.75018H14.0825V15.1669H11.9158V9.75018Z"
                        fill="#CA4146"
                      />
                    </svg>
                  </span>
                  <article class="">
                    <p>
                      بلغت الحد الأقصى للقيم
                      {{ settings['variants_limit']?.toString() }}.
                    </p>
                  </article>
                  <a
                    href="#"
                    style="text-decoration: none"
                    type="button"
                    data-dismiss="alert"
                    @click="exceed_variants_limit = false"
                  >
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">Close</span>
                  </a>
                </section>

                <section v-show="showGeneralInfoAlert" class="alert-box alert-box--info alert-dismissable mb-20">
                  <i class="sicon-info ml-10 font-20"></i>
                  <article class="">
                    <p>بإمكانك إضافة أكواد متعددة بناءً على خيارات المنتج.</p>
                  </article>
                  <a
                    href="#"
                    style="text-decoration: none"
                    type="button"
                    data-dismiss="alert"
                    @click="showGeneralInfoAlert = false"
                  >
                    <span aria-hidden="true">&times;</span>
                    <span class="sr-only">Close</span>
                  </a>
                </section>

                <label id="options_toggle" class="mt-0 mb-15 d-flex align-items-center" for="has_options">
                  <input
                    id="has_options"
                    ref="switch"
                    v-model="has_options"
                    :disabled="product?.quantity > 0"
                    type="checkbox"
                    name="active_options"
                    :class="{ switchery: true }"
                    :checked="has_options ? 'checked' : 0"
                  />
                  تفعيل خيارات المنتج
                  <span v-if="product?.quantity > 0" class="tooltip-toggle top right primary no-border">
                    <i class="sicon-info ml-10 font-20"></i>
                    <span class="tooltip-content">
                      <p class="font-12">يمكن تعطيل خيارات المنتج بعد حذف كافة الأكواد المُضافة.</p>
                    </span>
                  </span>
                </label>

                <product-quantity
                  v-if="product && product.type === 'codes'"
                  v-show="has_options"
                  :options_names="options_names"
                  :options="options"
                  :countries="countries"
                  :has_options="has_options"
                  :product="product"
                  :settings="settings"
                  :default_quantities="default_quantities"
                  :skus="skus"
                  :delete-product-option="deleteProductOption"
                  :delete-value="deleteValue"
                  :errors="errors"
                  :features_list="features_list"
                  :exceed_options_limit="exceed_options_limit"
                  :exceed_variants_limit="exceed_variants_limit"
                  :languages="languages"
                  :show-err="showErr"
                  @changeDefaultOptionHandler="changeDefaultOptionHandler"
                  @update-codes="handleUpdateSkus"
                  @update-options="handleUpdateOptions"
                  @update-exceed-variants-limit="handleUpdateExceedVariantsLimit"
                />

                <!----- Save button-------->
                <button
                  v-if="permissions.edit_product"
                  class="btn btn-tiffany btn-full btn-xlg mt-15"
                  type="button"
                  data-inline-loader
                  @click="save($event)"
                >
                  حفظ
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable no-undef */
import ProductQuantity from './components/ProductQuantity.vue'
import Http from '../../utils/http'

export default {
  components: {
    'product-quantity': ProductQuantity,
  },
  data: () => ({
    product: null,
    options_names: {},
    showModal: false,
    permissions: [],
    showDialog: false,
    has_options: false,
    languages: {},
    countries: [],
    settings: {},
    errors: null,
    features: [],
    features_list: [],
    exceed_options_limit: false,
    exceed_variants_limit: false,
    default_quantities: [],
    options: [],
    showErr: false,
    showGeneralInfoAlert: true,
    skus: [
      {
        id: null,
        name: null,
        sale_price: null,
        regular_price: null,
        stock_quantity: 0,
        barcode: null,
        weight: null,
        price: null,
        sku: null,
        mpn: null,
        gtin: null,
        notify_quantity: null,
        quantities: [],
        is_default: false,
        values: [],
        codes: [],
      },
    ],
  }),
  computed: {},

  mounted() {
    Salla.event.addEventListener('digital-card-options-management::show-modal', this.show)
    Salla.event.addEventListener('initiate-language-fields', this.initiateLanguageFields)

    $('#has_options').change(function () {
      if (this.checked) {
        this.has_options = 1
        $('.rec-options-list').slideDown()
      } else {
        this.has_options = 0
        $('.rec-options-list').slideUp()
      }
    })
  },
  beforeDestroy() {
    document.removeEventListener('digital-card-options-management::show-modal', this.show)
  },
  methods: {
    handleUpdateSkus({ skus }) {
      this.skus = skus
    },
    handleUpdateOptions({ options }) {
      this.options = options
    },
    handleUpdateExceedVariantsLimit({ exceed_variants_limit }) {
      this.exceed_variants_limit = exceed_variants_limit
    },
    addProductOption(name, slug, display_type_placeholder, display_type, placeholder) {
      // Generate temp option detail id (value id)
      // to link new value with  sku when generate new sku
      let temp_id = Math.random().toString(36).substring(2)
      // Generate temp option id to link it with
      // all their child values (details)
      let temp_option_id = Math.random().toString(36).substring(2)
      // Generate New Option
      this.options.push({
        id: temp_option_id,
        placeholder,
        name,
        slug,
        display_type_placeholder,
        display_type,
        is_new: true,
        values: [
          {
            id: temp_id,
            name: null,
            display_value: null,
            option_id: temp_option_id,
            is_new: true,
            is_default: false,
          },
        ],
      })

      // Push New Values To Skus
      this.pushSkuValues(temp_id, temp_option_id)
      Salla.event.createAndDispatch('initiate-language-fields')
    },

    // Fetch Modal Data
    prepareData(res) {
      this.product = res.product
      this.permissions = res.permissions
      this.default_quantities = res.default_quantities
      this.countries = res.countries
      this.features = res.features
      this.options_names = res.options_names
      this.features_list = res.features_list
      this.settings = res.settings
      let options = res.options
      if (options.length) {
        options[0].slug = 'store'
        options[1].slug = 'card_value'
        this.options = options
        this.has_options = true
        this.$refs.switch.click()
      }

      this.options.forEach((option) => {
        option.translations = this.options_names[option.slug] || {}
        option.name = option.translations[res.languages['iso_code']]['option_name']
      })
      let skus = res.product_skus

      if (skus.length) {
        this.skus = skus
      } else {
        this.skus[0].quantities = JSON.parse(JSON.stringify(this.default_quantities))
      }

      this.total_quantity = this.product.quantity

      let defaultLang = {
        id: 1,
        name: 'العربية',
        rtl: 1,
        iso_code: 'ar',
        country_code: 'sa',
        flag: 'https://assets.salla.sa/images/flags/ar.svg',
        status: true,
        auto_translate: false,
        order: 0,
      }
      this.languages = res.languages || {
        supported: [defaultLang],
        current: defaultLang,
        default: defaultLang,
        iso_code: 'ar',
        feature: false,
      }
    },
    // Push New Values(option details) To Skus
    pushSkuValues(temp_id, temp_option_id) {
      this.skus.forEach((sku) => {
        sku.values.push({
          id: temp_id,
          name: null,
          option_id: temp_option_id,
          is_new: true,
          is_default: true,
        })
      })
    },
    // Submit Changes
    save() {
      this.showErr = false
      showLoading()
      Http.put(
        '/products/quantity/management/' + this.product.id,
        this.getPayload(),
        ({ data }) => {
          hideLoading()
          // close the options and quantities modal

          // Update product quantity
          Salla.event.createAndDispatch('product-details::update-quantity', {
            can_change_quantity: data.can_change_quantity,
            productId: this.product.id,
            quantity: data.quantity,
          })

          // Reset Errors
          this.errors = null
          laravel.ajax.successHandler(data)
          this.closeModal()
        },
        ({ response }) => {
          hideLoading()
          const errors = response.data.error.fields
          this.errors = errors
          this.showErr = true
          if (errors.skus) {
            swal({
              text: errors.skus,
              type: 'error',
              showConfirmButton: false,
              timer: 3000,
            })
          }

          Object.keys(errors).forEach((key) => {
            const error = errors[key]
            if (error && key.includes('price')) {
              swal({
                text: error,
                type: 'error',
                showConfirmButton: false,
                timer: 3000,
              })
              return false
            }
          })
        }
      )
    },
    // Delete Value from option Depending On The value Index And The Parent Index
    deleteValue(parentIndex, index, value) {
      if (this.options[parentIndex].values.length > 1) {
        const option = [...this.options[parentIndex].values]
        option.splice(index, 1)
        this.$set(this.options[parentIndex], 'values', option)

        this.deleteSkus(value)
        this.$nextTick(() => {
          $('.bootstrap-select').selectpicker('refresh')
        })
      }
      this.validSkusCount()
    },
    // Delete sku
    deleteSkus(detail) {
      this.skus = _.reject(this.skus, (sku) => {
        return sku.values.some((item) => item.id != null && item.id === detail.id)
      })

      this.refreshSkusTotalQuantity()
    },
    // Delete Product Group Depending On The Product Group Index
    deleteProductOption(index, option) {
      if (this.options.length > 1) {
        const groups = Object.assign([], this.options)
        groups.splice(index, 1)

        this.options = groups
      }

      this.exceed_options_limit = this.options.length >= this.settings['options_limit']
      this.rebuildSkus(option)
    },
    rebuildSkus(option) {
      let values = []
      // Delete specific option values
      // from all skus
      _.each(this.skus, function (sku) {
        _.remove(sku.values, function (value) {
          return value.option_id === option.id
        })
      })

      // Delete duplicate skus
      this.skus = _.reject(this.skus, (sku) => {
        let sku_values = _.map(sku.values, 'id')

        let includes = values.some((a) => sku_values.every((v, i) => v === a[i]))
        if (!includes) {
          values.push(sku_values)
        }
        return includes
      })

      // we need to refresh total skus quantity
      // after rebuild all skus
      this.refreshSkusTotalQuantity()
    },
    // Refresh Sku Total
    refreshSkusTotalQuantity() {
      this.product.quantity = _.sum(_.map(this.skus, 'stock_quantity')) || 0
    },
    // check skus/variants limit
    validSkusCount() {
      let combinations_count = 1
      this.options.forEach((option) => {
        combinations_count = combinations_count * option.values.length
      })

      this.exceed_variants_limit =
        this.settings['variants_limit'] && combinations_count > this.settings['variants_limit']
    },
    //Change default option value
    changeDefaultOptionHandler(value, index) {
      this.options[index].values.forEach((e) => (e.id !== value.id ? (e.is_default = false) : null))
      value.is_default = !value.is_default
    },
    // Load Modal Data
    show(event) {
      const data = event.detail.data
      this.reset()

      this.addProductOptions(data, 'store', 'store', 'دولة', 'text', 'المتجر')
      this.addProductOptions(data, 'card_value', 'card_value', 'نص', 'text', 'قيمة البطاقة')
      this.prepareData(data)
      this.showModal = true

      $('body').addClass('modal-open').css('padding', '0 15px 0 0')

      this.initiateLanguageFields()
      setTimeout(() => {
        this.showDialog = true
      }, 500)
    },
    reset() {
      Object.assign(this.$data, this.$options.data.call(this))
      this.refreshSwitchery()
    },
    addProductOptions(data, optionName, slug, displayTypePlaceholder, displayType, placeholder) {
      const optionNameLocalized = data.options_names[optionName][data.languages.iso_code]['option_name']
      this.addProductOption(optionNameLocalized, slug, displayTypePlaceholder, displayType, placeholder)
    },

    refreshSwitchery() {
      let checkbox = document.querySelector('#modal_digital_card_management #has_options')

      if (checkbox.nextSibling) {
        checkbox.nextSibling.remove()
      }

      new Switchery(checkbox, { color: '#57d4c4', size: 'small' })
    },
    closeModal() {
      this.product = null
      this.showDialog = false
      this.has_options = false
      $('body').removeClass('modal-open').css('padding', '0')
      setTimeout(() => {
        this.showModal = false
      }, 500)
    },
    // Prepare Submit Payload
    getPayload() {
      return {
        has_options: this.has_options,
        skus: this.skus,
        options: this.options,
        total_quantity: this.has_options ? this.product.quantity : this.total_quantity,
      }
    },
    initiateLanguageFields() {
      setTimeout(() => {
        if (window.initFieldLang) {
          $('.rec-ls-field:not(.field-lang-initiated)').each(function () {
            window.initFieldLang($(this))
          })
        }
      }, 500)
    },
  },
}
</script>

<style lang="scss">
#modal_digital_card_management {
  input:disabled {
    & + .switchery {
      border-color: #eeeeee !important;
      box-shadow: rgb(221, 221, 221) 0px 0px 0px 10px inset !important;
      > small {
        background: #999999 !important;
      }
    }
  }
}
.custom-modal#modal_digital_card_management #options_toggle:hover:has(input:disabled) {
  opacity: 1;
}
</style>
