<template>
  <div class="rec-options-list" :style="{ display: show }">
    <!-- Product Options Group Layout ---------->
    <div v-for="(option, optIndex) in options" :key="optIndex" class="option-section">
      <div class="option-section-inner">
        <div class="row">
          <div class="col col-sm-6 col-xs-12">
            <div class="form-group">
              <div class="input-group">
                <span class="input-group-addon input-group-addon-small"><i class="sicon-file-partial"></i></span>
                <input
                  type="text"
                  class="form-control form-control--small"
                  readOnly
                  :value="option.translations[languages['iso_code']]['option_name']"
                />
              </div>
            </div>
          </div>
          <div class="col col-sm-6 col-xs-12">
            <div class="form-group">
              <div class="input-group">
                <span class="input-group-addon input-group-addon-small"><i class="sicon-file-partial"></i></span>
                <input
                  type="text"
                  class="form-control form-control--small"
                  readOnly
                  :value="option.slug == 'store' ? 'دولة' : 'نص'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-for="(value, index) in option.values" :key="index" class="option-options">
        <div class="value-container">
          <div
            v-if="option.slug === 'store'"
            class="form-group"
            :class="{
              'has-error': errors && errors['options.' + optIndex + '.' + 'values.' + index + '.name'],
              'has-default-option':
                product.type === 'codes' && !isBasicPlan && features_list.default_product_option_value,
            }"
          >
            <template v-if="product.type === 'codes' && !isBasicPlan && features_list.default_product_option_value">
              <!------- Select Option Default Value -------->
              <div class="tooltip-toggle top right">
                <span class="default-option-selector">
                  <button
                    type="button"
                    class="btn"
                    :class="{ selected: value.is_default }"
                    @click="changeDefaultOption(value, optIndex)"
                  >
                    <i class="sicon-check"></i>
                  </button>
                </span>
                <div v-if="value.is_default" class="tooltip-content width-auto">
                  <p class="font-12 nowrap">الخيار الافتراضي</p>
                </div>
              </div>
            </template>

            <div class="input-group full-width">
              <span class="input-group-addon input-group-addon-small">
                <i class="sicon-type-square"></i>
              </span>
              <select
                v-model="value.name"
                class="bootstrap-select selectpicker full-width"
                :class="{
                  'has-error': errors && errors['options.' + optIndex + '.' + 'values.' + index + '.name'],
                }"
                :name="option.slug + '_' + optIndex"
                @change="countryOptionUpdated($event, value, index, optIndex)"
              >
                <option value="-111">
                  <span class="placeholder-option"> اختر دولة المتجر </span>
                </option>
                <option
                  v-for="(country, key) in countries"
                  :key="key"
                  :disabled="countryOptionDisabled(country)"
                  :value="country.text"
                >
                  <span class="country-select-option">
                    <country-flag :country="country.country_code" size="small" />
                    {{ country.text }}
                  </span>
                </option>
              </select>
            </div>
          </div>

          <lingual-field
            v-if="option.slug !== 'store'"
            :events-load="{ value: value, optIndex: optIndex }"
            name="option_details_name"
            :translations="value.translations"
            icon="sicon-type-square"
            :outer-attrs="{
              class: {
                'has-error': errors && errors['options.' + optIndex + '.' + 'values.' + index + '.name'],
                'has-default-option':
                  product.type === 'codes' && !isBasicPlan && features_list.default_product_option_value,
              },
            }"
            :input-attrs="{
              class: 'option_details_name product_price option_values_cls',
            }"
            placeholder="أدخل قيمة البطاقة (مثل: 100 ريال)"
            :languages="languages"
            :drop-down-attrs="{ style: dropDownStylesForOption(option) }"
            @value-updated="optionDetailUpdated"
          >
            <template
              v-if="product.type === 'codes' && !isBasicPlan && features_list.default_product_option_value"
              #before-input
            >
              <!------- Select Option Default Value -------->
              <div class="tooltip-toggle top right">
                <span class="default-option-selector">
                  <button
                    type="button"
                    class="btn"
                    :class="{ selected: value.is_default }"
                    @click="changeDefaultOption(value, optIndex)"
                  >
                    <i class="sicon-check"></i>
                  </button>
                </span>
                <div v-if="value.is_default" class="tooltip-content width-auto">
                  <p class="font-12 nowrap">الخيار الافتراضي</p>
                </div>
              </div>
            </template>
          </lingual-field>

          <button
            v-if="option.values.length > 1"
            type="button"
            class="btn btn-delete-item btn-delete-value"
            @click="deleteValue(optIndex, index, value)"
          >
            <i class="icon sicon-trash-2"></i>
          </button>
        </div>
        <div class="custom-error">
          <div class="form-group has-error">
            <form-error
              v-if="showErr && errors && errors['options.' + optIndex + '.' + 'values.' + index + '.name']"
              :errors="errors"
            >
              {{
                errors['options.' + optIndex + '.' + 'values.' + index + '.name'].toString().includes('is required')
                  ? 'هذا الحقل مطلوب.'
                  : errors['options.' + optIndex + '.' + 'values.' + index + '.name'].toString()
              }}
            </form-error>
          </div>
        </div>
      </div>
      <!-------------- End Option Details (Values) Layout ----------->

      <!--------- Add New Option Detail ( Value) -------------------->
      <button type="button" class="btn btn-add-value btn-full" @click="addOptionValue(optIndex, option)">
        <i class="sicon-add"></i> إضافة قيمة جديدة
      </button>
    </div>
    <!--------- End Product Option Group ------------->

    <!--------- Add New Option ----------------------->

    <section v-show="exceed_options_limit" id="exceed_options_limit" class="alert-box alert-box--danger mb-20 mt-20">
      <i class="sicon-info ml-10 font-20"></i>
      <article>
        <p>{{ messages.options_limit }}</p>
      </article>
    </section>

    <!-- Product Skus (options combinations ) Layout  --------->

    <div v-show="showSkus" class="row">
      <section v-show="showOnlyAddedCodeMessage" role="alert" class="alert-box alert-box--info mb-20 mt-20">
        <i class="sicon-info ml-10 font-20"></i>
        <article>
          <p>ستظهر للعميل الخيارات المُضاف لها أكواد فقط.</p>
        </article>
        <i class="sicon-cancel font-14" @click="showOnlyAddedCodeMessage = false"></i>
      </section>

      <hr />

      <!-- Product Skus Total Quantity --------->
      <div class="col-xs-6 col-xs-offset-6 pr-20 align-left">
        <h5 class="mb-10 mt-0 font-15">إجمالي الأكواد : {{ product.quantity }}</h5>
      </div>

      <div class="col-xs-12">
        <h6 v-if="errors && errors.total_quantity" class="mt-0 mb-10 font-14 text-danger">
          {{ errors.total_quantity[0] }}
        </h6>
      </div>

      <!-- ?Product Skus Layout ------------>
      <div class="col-xs-12 skus-conatainer">
        <div
          id="accordion"
          class="panel-group rec-accordion rec-accordion--enhanced"
          role="tablist"
          aria-multiselectable="true"
        >
          <div v-for="(sku, skuIndex) in skus" :key="skuIndex" class="panel panel-default">
            <!---------- Sku Panel Head ( Name & Total Stock quantity ) ---------->
            <div
              :id="`headingOne${skuIndex}`"
              class="panel-heading rec-accordion__heading collapsed"
              aria-expanded="false"
              role="tab"
              data-toggle="collapse"
              data-parent="#accordion"
              :href="`#collapse${skuIndex}`"
              :aria-controls="`collapse${skuIndex}`"
            >
              <div>
                <h4>
                  {{ getSkuName(sku) }}
                </h4>
              </div>
              <span>
                الأكواد المُضافة:
                {{ sku.stock_quantity }}
              </span>
            </div>
            <!---------- Sku Panel Body ------------------------>
            <div
              :id="`collapse${skuIndex}`"
              class="panel-collapse rec-accordion__collapse collapse"
              role="tabpanel"
              :aria-labelledby="`headingOne${skuIndex}`"
            >
              <div :class="{ 'panel-body pb-0': true, 'rec-accordion__body': true }">
                <div v-if="product.products_price_edit" class="row">
                  <!---------- Sku Price ------------------>
                  <div class="col-xs-12">
                    <div
                      class="form-group"
                      :class="{
                        'has-error': errors && errors['skus.' + skuIndex + '.price'],
                      }"
                    >
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-dollar-coin-stack"></i></span>
                        <input
                          :id="`price_${sku.id}`"
                          v-model="sku.price"
                          type="text"
                          class="form-control form-control--small _parseArabicNumbers"
                          placeholder="السعر"
                        />
                        <span class="input-group-addon">ر.س</span>
                      </div>
                      <form-error v-if="errors && errors['skus.' + skuIndex + '.price']" :errors="errors">
                        {{
                          errors['skus.' + skuIndex + '.price'].toString().includes('is required')
                            ? 'هذا الحقل مطلوب.'
                            : errors['skus.' + skuIndex + '.price'].toString()
                        }}
                      </form-error>
                    </div>
                  </div>

                  <!---------- Sku Cost Price ------------------>
                  <div class="col-md-4 col-xs-12">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-dollar-coin-stack"></i></span>
                        <input
                          :id="`cost_${sku.id}`"
                          v-model="sku.regular_price"
                          type="text"
                          placeholder="سعر التكلفة"
                          class="form-control form-control--small _parseArabicNumbers"
                        />
                        <span class="input-group-addon">ر.س</span>
                      </div>
                    </div>
                  </div>
                  <!---------- Sku Sale Price ------------------>
                  <div class="col-md-4 col-xs-12">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-dollar-coin-stack"></i></span>
                        <input
                          :id="`discount_${sku.id}`"
                          v-model="sku.sale_price"
                          type="text"
                          placeholder="السعر المخفض"
                          class="form-control form-control--small _parseArabicNumbers"
                        />
                        <span class="input-group-addon">ر.س</span>
                      </div>
                    </div>
                  </div>

                  <!---------- Sku Notify Quantity -------------------->
                  <div v-if="features_list.notify_quantity" class="col-md-4 col-xs-6">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-bell"></i></span>
                        <input
                          :id="`low_quantity_${sku.id}`"
                          v-model="sku.notify_quantity"
                          type="text"
                          placeholder="أقل كمية للتنبيه"
                          class="form-control form-control--small _parseArabicNumbers"
                        />
                      </div>
                    </div>
                  </div>

                  <!---------- Sku Barcode -------------------->
                  <div class="col-md-3 col-xs-6">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-barcode"></i></span>
                        <input
                          :id="`barcode_${sku.id}`"
                          v-model="sku.barcode"
                          type="text"
                          placeholder="الباركود"
                          class="form-control form-control--small _parseArabicNumbers"
                        />
                      </div>
                    </div>
                  </div>

                  <!---------- Sku Code -------------------->
                  <div :class="`${features_list.notify_quantity ? 'col-md-3 col-xs-6' : 'col-md-4 col-xs-12'}`">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-tag"></i></span>
                        <input
                          v-model="sku.sku"
                          type="text"
                          placeholder="SKU"
                          class="form-control form-control--small"
                        />
                      </div>
                    </div>
                  </div>

                  <!---------- MPN -------------------->
                  <div v-if="showMpnAndGtin" class="col-md-3 col-xs-6">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-barcode"></i></span>
                        <input
                          v-model="sku.mpn"
                          type="text"
                          placeholder="MPN"
                          class="form-control form-control--small"
                        />
                      </div>
                    </div>
                  </div>
                  <!---------- GTIN -------------------->
                  <div v-if="showMpnAndGtin" class="col-md-3 col-xs-6">
                    <div class="form-group">
                      <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-barcode"></i></span>
                        <input
                          v-model="sku.gtin"
                          type="text"
                          placeholder="GTIN"
                          class="form-control form-control--small"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!----- End Sku Quantity --------->
              </div>
              <hr />
              <div class="add-codes-section">
                <h2 class="dark mb-5 mt-5 font-14">
                  <i class="mr-5 sicon-page-content"></i>
                  إضافة الأكواد
                </h2>
                <p v-show="!showBulkAddCodes" class="text-muted text-muted-small mb-0">
                  لإضافة مجموعة أكواد دفعة واحدة اضغط هنا
                  <span class="bulk-add-code-btn" style="cursor: pointer" @click="showBulkAddCodes = true">
                    اضغط هنا
                  </span>
                </p>

                <BulkAddCodes v-show="showBulkAddCodes" :sku-index="skuIndex" @code-input="addBulkCode" />

                <!--                list of codes -->
                <div class="pt-20">
                  <div v-for="(code, index) in sku.codes" :key="index" class="codes-container row">
                    <CodeInput
                      :code="code"
                      :index="index"
                      :sku-index="skuIndex"
                      @code-input="updateCode"
                      @code-delete="deleteCode"
                    />
                  </div>
                </div>

                <button
                  type="button"
                  class="btn btn-add-value btn-full font-14 add-code-btn"
                  @click="addCode(skuIndex)"
                >
                  <i class="sicon-add"></i> إضافة كود
                </button>
              </div>
            </div>
          </div>
          <!----- End Sku Panel Body -------->
        </div>
      </div>
      <!------ End Sku Panel  --------------->
    </div>
    <!----- End Skus Panel Group --------->
  </div>
</template>

<script>
/* eslint-disable no-undef */
import FormError from '../../products-view/components/FormError'
import vueDebounce from 'vue-debounce'
import Vue from 'vue'
import LingualField from '@salla.sa/languages/components/LingualField'
import CountryFlag from 'vue-country-flag'
import CodeInput from './CodeInput.vue'
import BulkAddCodes from './BulkAddCodes.vue'
Vue.use(vueDebounce, {
  lock: true,
  listenTo: ['keyup'],
  defaultTime: '1000ms',
  fireOnEmpty: false,
})

export default {
  components: {
    CodeInput,
    'form-error': FormError,
    LingualField: LingualField,
    CountryFlag,
    BulkAddCodes: BulkAddCodes,
  },
  props: [
    'show',
    'options',
    'has_options',
    'skus',
    'product',
    'total_quantity',
    'default_quantities',
    'deleteValue',
    'deleteProductOption',
    'errors',
    'showErr',
    'exceed_options_limit',
    'exceed_variants_limit',
    'languages',
    'countries',
    'features_list',
    'options_names',
    'settings',
  ],
  data() {
    return {
      messages: [],
      showAlertMessage: false,
      showMpnAndGtin: false,
      isBasicPlan: false,
      showOnlyAddedCodeMessage: true,
      showBulkAddCodes: false,
    }
  },
  created() {
    this.product.quantity = _.sum(_.map(this.skus, 'stock_quantity'))
    this.showMpnAndGtin = this.shouldShowMpnAndGtin(this.product.type)
    this.checkCombinations()
    window.dataLayer[0].store.plan === 'basic' ? (this.isBasicPlan = true) : null
  },
  mounted() {
    this.initiateBootstrapSelect()
  },
  computed: {
    showSkus() {
      return this.skus.length > 1 || (this.skus.length === 1 && this.skus[0]?.values[0]?.name)
    },
    countryOptionDisabled() {
      return (country) => {
        return this.options.some((option) => {
          return option.values.some((value) => {
            return value.name === country.text
          })
        })
      }
    },
  },
  methods: {
    makeInputsReadOnly(inputs) {
      inputs.forEach((input) => {
        input.setAttribute('readonly', true)
      })
    },
    initiateBootstrapSelect() {
      $('.bootstrap-select').selectpicker()
      // Initialize Bootstrap Select
      $('.btn-add-option-group').click(function () {
        $('.bootstrap-select').selectpicker()
      })
    },
    addCode(skuIndex) {
      let new_codes = this.skus[skuIndex].codes || []
      new_codes.push('')
      const temp_skus = this.skus.map((sku, index) => {
        if (index === skuIndex) {
          sku.codes = new_codes
        }
        return sku
      })
      this.$emit('update-codes', { skus: temp_skus })
      this.recalculateSkuTotal(this.skus[skuIndex])
    },
    addBulkCode(data) {
      const temp_skus = this.skus.map((sku, skuIndex) => {
        if (skuIndex === data.skuIndex) {
          sku.codes = sku.codes.concat(data.codes)
        }
        return sku
      })
      this.$emit('update-codes', { skus: temp_skus })
      this.showBulkAddCodes = false
      this.recalculateSkuTotal(this.skus[data.skuIndex])
    },
    updateCode(data) {
      const temp_skus = this.skus.map((sku, sku_index) => {
        if (sku_index === data.skuIndex) {
          sku.codes = sku.codes.map((item, code_index) => {
            if (data.index === code_index) {
              item = data.value
            }
            return item
          })
        }
        return sku
      })
      this.$emit('update-codes', { skus: temp_skus })
    },
    deleteCode(data) {
      const temp_skus = this.skus.map((sku, sku_index) => {
        if (sku_index === data.skuIndex) {
          sku.codes = sku.codes.filter((item, code_index) => {
            return data.index !== code_index
          })
        }
        return sku
      })
      this.$emit('update-codes', { skus: temp_skus })
      this.recalculateSkuTotal(this.skus[data.skuIndex])
    },

    countryOptionUpdated(e, value, valueIndex, optIndex) {
      const temp_options = this.options.map((option, index) => {
        if (index === optIndex) {
          option.values = option.values.map((val, valIndex) => {
            if (valIndex === valueIndex) {
              val.name = this.getCountry(e.target.value).text
              val.country_code = this.getCountry(e.target.value).country_code
            }
            return val
          })
        }
        return option
      })
      this.$emit('update-options', { options: temp_options })
      this.updateSkus(value, optIndex, value.is_default)
      this.$nextTick(() => {
        $('.bootstrap-select').selectpicker('refresh')
      })
    },
    getCountry(value) {
      return this.countries.find((country) => country.text == value)
    },
    // Changing Branch Product Quantity on Keyup event
    changeQuantity(e, branch, sku) {
      let quantity = parseInt(e.target.value)
      if (isNaN(quantity)) {
        quantity = 0
      }
      branch.quantity = quantity
      this.recalculateSkuTotal(sku)
    },

    // Format sku name based on values( option details) name
    getSkuName(sku) {
      return _.map(sku.values, 'name').join('  -  ')
    },

    // Add New Option Details(value) To Specific Option
    addOptionValue(index, option) {
      if (!this.validSkusCount(option.id)) {
        $('#modal_digital_card_management').animate({
          scrollTop: $('#exceed_variants_limit').offset().top,
        })

        return
      }
      this.errors = null
      this.options[index].values.push({
        id: null,
        name: null,
        display_value: null,
        is_new: true,
        option_id: option.id,
        image_url: null,
        hashed_display_value: null,
        is_default: false,
      })
      Salla.event.createAndDispatch('initiate-language-fields')
      this.$nextTick(() => {
        this.initiateBootstrapSelect()
      })
    },

    // Update Product Skus
    updateSkus(detail, optIndex, isDefault) {
      // when we create a new option value (detail)
      // we need to go through the whole process to generate skus & id of detail
      if (detail.id === null) {
        return this.generateSkus(detail, optIndex)
      }

      // we need to update the all name of detail in all skus values
      const temp_skus = this.skus.map((sku) => {
        let result = _.find(sku.values, function (value) {
          value.option_id === detail.option_id && isDefault ? (value.is_default = false) : null
          return detail.id != null && value.id === detail.id
        })

        if (result) {
          result['name'] = detail.name
          result['is_default'] = isDefault
        }

        const defaultOption = sku.values.every((ele) => ele.is_default === true)
        defaultOption ? (sku.is_default = true) : (sku.is_default = false)
        return sku
      })
      this.$emit('update-codes', { skus: temp_skus })
    },

    // Generate New Product Skus
    generateSkus(detail, optIndex) {
      // * Generate detail(value) temp id
      detail.id = Math.random().toString(36).substring(2)

      // * Remove the selected option from options array
      // make combinations later
      let options = this.options.filter(function (value, index) {
        return index !== optIndex
      })
      // * Extract the values(details) From Options
      let details = _.map(options, 'values'),
        detailClone = { ...detail }
      details.push([detailClone])

      // * Generate Sku Combinations from Details(Values)
      let combinations = details.reduce((a, b) => a.reduce((r, v) => r.concat(b.map((w) => [].concat(v, w))), []))
      // * Create New Skus Based on Combinations
      this.createSkus(combinations)
    },
    createSkus(combinations) {
      let newSkus = []
      combinations.forEach((item) => {
        newSkus.push({
          id: null,
          price: null,
          name: null,
          regular_price: null,
          stock_quantity: 0,
          sale_price: null,
          barcode: null,
          weight: null,
          sku: null,
          mpn: null,
          gtin: null,
          is_default: false,
          values: Array.isArray(item) ? item : [item],
          quantities: JSON.parse(JSON.stringify(this.default_quantities)),
          notify_quantity: null,
          translations: {},
          codes: [],
        })
      })
      const temp_skus = this.skus.concat(newSkus)
      this.$emit('update-codes', { skus: temp_skus })
      // this.updateFirstSku();
    },
    recalculateSkuTotal(sku) {
      // Refresh sku stock quantity and product total quantity
      sku.stock_quantity = sku.codes.length || 0
      this.product.quantity = _.sum(_.map(this.skus, 'stock_quantity')) || 0
    },
    dropDownStylesForOption(option) {
      return {
        color: 'left:35px',
        image: 'left:85px',
        text: '',
      }[option.display_type]
    },

    changeDefaultOption(value, index) {
      this.$emit('changeDefaultOptionHandler', value, index)
      this.updateSkus(value, index, value.is_default)
    },

    optionDetailUpdated($event) {
      //update options
      $event.object.value.translations = $event.translations
      $event.object.value.name = $event.translations[this.languages.iso_code]['option_details_name']
      this.updateSkus($event.object.value, $event.object.optIndex, $event.object.value.is_default)
    },

    updateFirstSku() {
      let total_sku = _.sum(_.map(this.skus, 'stock_quantity'))
      let product_quantity = parseInt(this.product.quantity)
      if (total_sku === 0 && product_quantity && product_quantity !== total_sku) {
        this.skus[0].stock_quantity = product_quantity
        this.skus[0].quantities[0].quantity = product_quantity
      }
    },
    validSkusCount(option_id) {
      let combinations_count = 1,
        values_count = 0

      // calculate combinations/skus count
      this.options.forEach((option) => {
        values_count = option.values.length
        if (option.id === option_id) {
          values_count += 1
        }
        combinations_count = combinations_count * values_count
      })

      // check skus validation count
      const temp_exceed_variants_limit =
        this.settings['variants_limit'] && combinations_count > this.settings['variants_limit']
      this.$emit('update-exceed-variants-limit', { exceed_variants_limit: temp_exceed_variants_limit })
      return !temp_exceed_variants_limit
    },
    validOptionsLimit() {
      this.exceed_options_limit =
        this.settings['options_limit'] && this.options.length >= this.settings['options_limit']
      return !this.exceed_options_limit
    },
    // show skus values if the array has more than one sku
    // or if the skus has one value with not empty name
    checkCombinations() {
      let comb_count = 1
      this.options.forEach((option) => {
        comb_count = comb_count * option.values.length
      })

      if (comb_count !== this.skus.length) {
        this.regenerateSkus()
      }
    },
    // Regenerate combinations if the variants count is not correct
    regenerateSkus() {
      let details = _.map(this.options, 'values')
      let combinations = details.reduce((a, b) => a.reduce((r, v) => r.concat(b.map((w) => [].concat(v, w))), []))

      this.skus.length = 0
      this.createSkus(combinations)
      this.showAlertMessage = true
    },
    shouldShowMpnAndGtin(type) {
      return !['service', 'food'].includes(type)
    },
  },
}
</script>
<style scoped lang="scss">
.help-block {
  margin-right: 1rem;
}

.country-select-option {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
}
.rec-accordion__heading:before {
  top: 9px !important;
}
.add-codes-section {
  padding: 0 20px 20px;
  .text-muted {
    color: #999 !important;
    font-size: 12px !important;
  }
  .add-code-btn {
    margin-top: 2rem !important;
    color: var(--01--Primary-primary, #004d5a) !important;
    font-weight: 500 !important;
    padding: 10px 14px !important;
  }
}
.bulk-add-code-btn {
  font-size: 12px;
  text-decoration-line: underline;
  color: #73e7cc;
  font-weight: 700;
}

.input-group > .bootstrap-select:first-child > .btn,
.input-group > .bs-select-hidden:first-child + .bootstrap-select > .btn {
  border-bottom-right-radius: 3px !important;
  border-top-right-radius: 3px !important;
}
</style>
<style lang="scss">
#modal_digital_card_management {
  .custom-error .help-block {
    margin-top: -7px;
  }
  .help-block {
    color: #f55157;
    font-size: 12px;
    font-weight: 400;
  }
  .placeholder-option{
    font-weight: 400;
    color: #999999;
    line-height: 20px;
  }
  .has-error .btn-group.bootstrap-select.input-group-btn {
    border: none !important;
    button {
      border: 1px solid #f55157 !important;
      border-right: none !important;
    }
  }
  .input-group {
    > .bootstrap-select {
      &:first-child,
      &:last-child {
        > .btn {
          border-bottom-right-radius: 0 !important;
          border-top-right-radius: 0 !important;
        }
      }
    }

    > .bs-select-hidden {
      &:first-child,
      &:last-child {
        + .bootstrap-select {
          > .btn {
            border-bottom-right-radius: 0 !important;
            border-top-right-radius: 0 !important;
          }
        }
      }
    }
  }

  .dropdown-menu {
  >.disabled {
    >a {
      background-color: #F8F8F8;
      color: #BBBBBB;
       .flag {
          filter: grayscale(1);
        }
      &:focus,
      &:hover {
        background-color: #F8F8F8;
        color: #BBBBBB;

        
      }
    }
  }
}
.select2-results__option[aria-selected=true],
.bootstrap-select.btn-group .dropdown-menu>.selected>a {
  background-color: #EEFCF9 !important;
  color: #444444 !important;
  .flag {
    filter: grayscale(0);
  }
}
}

</style>
