<script>
export default {
  name: 'CodeInput',
  props: ['code', 'index', 'skuIndex'],
}
</script>

<template>
  <div class="option-section code-input-container" style="padding: 12px; margin-bottom: 12px !important">
    <div class="option-section-inner">
      <div class="row">
        <div class="col-xs-12">
          <div class="form-group mb-0">
            <div class="input-group">
              <input
                :value="code"
                type="text"
                class="form-control"
                @input="
                  $emit('code-input', {
                    index: index,
                    skuIndex: skuIndex,
                    value: $event.target.value,
                  })
                "
              />
              <span class="input-group-addon">
                <span
                  class="delete-code-btn"
                  @click="
                    $emit('code-delete', {
                      index: index,
                      skuIndex: skuIndex,
                    })
                  "
                >
                  <i class="icon sicon-trash-2"></i>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.code-input-container {
  .input-group-addon {
    border: 1px solid var(--06--Danger-danger, #f55157);
    input {
      color: var(--07--Light-Theme-Dark-color-dark-100, #999) !important;
      font-size: 14px !important;
      font-weight: 400 !important;
    }
    .delete-code-btn {
      cursor: pointer;
      :before,
      :after {
        color: var(--06--Danger-danger, #f55157);
      }
    }
  }
}
</style>
