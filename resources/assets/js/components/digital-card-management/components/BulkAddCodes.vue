<script>
export default {
  name: 'BulkAddCodes',
  props: ['code', 'index', 'skuIndex'],
  methods: {
    parseTxtFile($event) {
      window.showLoading()
      let codes = []
      var file_content_input = $($event.target).closest('.upload_div_container').find('.file_content');
      var file_content_lines = file_content_input.val().split('\n')
      for (var i = 0; i < file_content_lines.length; i++) {
        if ($.trim(file_content_lines[i]) != '') {
          codes.push($.trim(file_content_lines[i]))
        }
      }
      this.$emit('code-input', {
        skuIndex: this.skuIndex,
        codes,
      });
      file_content_input.val('')
      window.hideLoading()
      $($event.target).closest('.upload_div_container').hide()
    },
  },
}
</script>

<template>
  <div class="upload_div_container option-section mb-20 mt-10">
    <div class="row">
      <div class="col-xs-12">
        <div class="form-group">
          <textarea
            class="file_content form-control code_content right-border"
            rows="5"
            placeholder="انسخ الأكواد وألصقها هنا، وتأكد من أن كل سطر يحتوي على كود واحد فقط"
          ></textarea>
        </div>
      </div>
    </div>
    <button type="button" class="btn btn--outlined bulk-add-code-btn" @click="parseTxtFile">
      إدراج
    </button>
  </div>
</template>

<style lang="scss" scoped>
.option-section {
  padding: var(--Spacing-lg, 8px) !important;
}
textarea {
  border-radius: var(--Radius-xs, 4px);
  border: 1px solid var(--07--Light-Theme-Gray-color-gray-300, #eee) !important;
  background: var(--07--Light-Theme-White-color-White-Card-BG, #fff) !important;
  placeholder {
    color: var(--07--Light-Theme-Dark-color-dark-100, #999) !important;
    font-size: 14px !important;
    font-weight: 400 !important;
  }
}
.bulk-add-code-btn {
  border-radius: var(--Radius-xs, 4px);
  border: 1px solid var(--02--Secondary-secondary-500, #96edd9);
}
</style>
