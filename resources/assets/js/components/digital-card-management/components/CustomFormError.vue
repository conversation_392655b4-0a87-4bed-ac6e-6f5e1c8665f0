<template>
  <div v-if="showErr && errors && errors[errorKey]" class="row custom-error">
    <form-error :errors="errors">
      {{ errors[errorKey].toString().includes('is required') ? 'هذا الحقل مطلوب.' : errors[errorKey].toString() }}
    </form-error>
  </div>
</template>

<script>
export default {
  props: ['showErr', 'errors', 'errorKey', 'optIndex', 'index'],
}
</script>

<style lang="scss" scoped>
.custom-error {
  .form-group {
    margin-bottom: 0;
  }
  width: 100%;
  display: block;
}
</style>
