<template>
	<div id="commercial_register_verify_panel"
       :class="{'panel': true,
      'panel-default': true, 
      'has-steps': true, 
      'sides': true, 
      'step-checked': (entity.commercial_register_is_verified && (entity.commercial_register_number || entity.commercial_register_unified_number)),
      'panel--disabled': entity.owner_identity_is_completed === false || entity.manager_identity_is_completed === false}">
		<div class="panel-heading">
            <h6 class="panel-title" data-step="4">
              السجل التجاري
            </h6>
        </div>
        <div class="panel-body">
            <div v-show="!serviceCheck && !incorrectData && !(!entity.commercial_register_is_verified && entity.commercial_register_is_locked) && entity.owner_identity_is_completed && entity.manager_identity_is_completed">
                <article class="rec-article mb-20">
                  <h6 class="mt-0 font-15 mb-5">يرجى التأكد من تطبيق الشروط التالية قبل طلب التحقق:</h6>
                    <ul>
                      <li class="mb-5 font-13">
                        أن يكون السجل التجاري ساري المفعول.
                      </li>
                      <li class="mb-5 font-13">
                        أن يكون السجل التجاري واضح.
                      </li>
                      <li class="mb-5 font-13">
                        أن يكون السجل التجاري بدون أقتصاص.
                      </li>
                      <li class="mb-5 font-13">
                        أن تكون صورة السجل التجاري مطابقة للسجل الموثق في المتجر.
                      </li>
                      <li class="font-13">
                        أن يكون نشاط السجل التجاري مطابق لنشاط المتجر.
                      </li>
                    </ul>
                </article>
                <section class="alert-box alert-box--info">
                  <i class="sicon-info"></i>
                  <article>
                    <p>سيتم ارسال رسالة الى جوال المالك المسجل في ابشر , علما انه لا يمكن إجراء أكثر من عملية تحقق واحدة واحدة في اليوم لذا يرجى التأكد من إدخال البيانات بشكل صحيح</p>
                  </article>
                </section>
                <div :class="{'form-group': true, 'has-error': (commercial_register_unified_numberEmpty || ten_numbers_validation) && (!entity.commercial_register_unified_number || entity.commercial_register_unified_number.toString().length < 10 )}">
                    <label>الرقم الوطني الموحد <span class="text-danger">*</span></label>
					          <div class="input-group">
                        <span class="input-group-addon"><i class="sicon-contact-card"></i></span>
                        <input type="text" class="form-control _parseArabicNumbers" maxlength="10" placeholder="اضف الرقم الوطني الموحد" v-model="entity.commercial_register_unified_number">
                    </div>
                    <form-error v-show="ten_numbers_validation && entity.commercial_register_unified_number.toString().length < 10">الرقم المدخل يجب أن يتكون من 10 أرقام</form-error>
                    <form-error v-show="commercial_register_unified_numberEmpty && !entity.commercial_register_unified_number">الرجاء إدخال الرقم الوطني الموحد</form-error>
                </div>

                <div :class="{'form-group': true, 'has-error': registerImageEmpty && !entity.commercial_register_image}">
                    <imager
                        :cropAspectRatio="'null'"
                        :entity="entity"
                        v-model="entity.commercial_register_image"
                        :label="'صورة السجل التجاري'"
                        :uploader="'commercialRegister'"
                        :finishedUpload="entity.commercial_register_image !== null"
                        :imgSrc="entity.commercial_register_image"
                        :uploading="entity.commercial_register_image == null"/>
                    <form-error v-show="registerImageEmpty && !entity.commercial_register_image">الرجاء تحميل صورة السجل التجاري</form-error>
                </div>
                <button :disabled="disabledBtn" class="btn btn--outlined primary w-lg-200 h-40 rec-fvm font-14"
                         @click="check($event)" data-inline-loader>{{entity.commercial_register_is_verified ? 'حفظ البيانات' : 'تحقق'}}</button>
            </div>

            <error-message
                :icon="'sicon-info'"
                :extra="false"
                :title="'التحقق من مدير المتجر اولا'"
                :text="'يرجى التحقق من مالك و مدير المتجر قبل السجل التجاري'"
                :subText="'و يجب أن تكون بيانات المالك بالأعلى مطابقة لمالك السجل التجاري'"
                :showBtn="false"
                v-show="! (entity.owner_identity_is_completed && entity.manager_identity_is_completed)"
            />

            <error-message
                :icon="'sicon-info'"
                :extra="false"
                :title="error"
                :text="'يرجى التحقق من بيانات مالك المنشأة'"
                :subText="'بيانات مالك المتجر بالأعلى يجب أن تكون مطابقة لمالك السجل التجاري'"
                :showBtn="false"
                v-show="incorrectData"
            />

            <!-- Error Message Shown if verify is Locked -->
            <error-message
                :icon="'sicon-forward'"
                :extra="true"
                :title="error"
                :text="'يمكنك معاودة المحاولة بعد 24 ساعة'"
                :subText="'ونؤكد على أهمية التاكد من البيانات قبل إرسالها للتحقق'"
                :showBtn="false"
                v-show="!entity.commercial_register_is_verified && entity.commercial_register_is_locked"
            />

        </div>
    <otp-modal :action-name="source" />
    </div>
</template>

<script>
import Imager from './layouts/Imager';
import FormError from './layouts/FormError';
import ErrorMessage from './layouts/ErrorMessage';
import TaxNumberMixin from "../mixins/TaxNumberMixin";
import OtpModal from '../../otp/components/otp-modal'
import CommonSettingsMixin from "../mixins/CommonSettingsMixin";

export default {
    mixins: [TaxNumberMixin, CommonSettingsMixin],
    props: ['entity'],
    data () {
        return {
          source:'commercial-register',
            validationCheck: false,
            ten_numbers_validation: false,
            commercial_register_unified_numberEmpty: this.validationCheck && (this.entity.commercial_register_unified_number == null || this.entity.commercial_register_unified_number === ''),
            registerImageEmpty: this.validationCheck && (this.entity.commercial_register_image == null || this.entity.commercial_register_image === ''),
            serviceCheck: false,
            incorrectData: false,
            error: 'بيانات المالك غير مطابقة',
        }
    },

    components: {
        'imager': Imager,
        'otp-modal': OtpModal,
        'form-error': FormError,
        'error-message': ErrorMessage
    },

  mounted() {
    Salla.event.addEventListener(`otpVerified::${this.source}`, (data) => {
      this.verify(data.detail)
    })
  },

  beforeDestroy() {
    document.removeEventListener(`otpVerified::${this.source}`,this.verify())
  },
    methods: {
      check(event) { // Check For Entered Data
        this.validationCheck = true;
        if (this.entity.commercial_register_unified_number == null || this.entity.commercial_register_unified_number === '') {
          this.commercial_register_unified_numberEmpty = true;
        } else if (this.entity.commercial_register_unified_number.length !== 10) {
          this.ten_numbers_validation = true;
        } else if (this.entity.commercial_register_image == null) {
          this.registerImageEmpty = true;
        } else {
          this.commercial_register_unified_numberEmpty = false;
          this.ten_numbers_validation = false;
          this.registerImageEmpty = false;

          this.showOtpModal(event)
        }
      },
      showOtpModal: function (event) {
        showLoading(event)
        Salla.event.createAndDispatch(`showOtpModal::${this.source}`)
      },
      async verify(payload) {
            await axios.post('/entity/verification/verify/commercial', {
                commercial_register_unified_number: this.entity.commercial_register_unified_number,
                commercial_register_image: this.entity.commercial_register_image,
                source: payload.source,
                otp_code: payload.otp,
            }).then(response => {
              this.entity.commercial_register_is_verified = true;
              hideLoading();
            }).catch(({ response }) => {
              if (response.data && response.data.error && response.data.error.message) {
                this.error = response.data.error.message;
                hideLoading();
                laravel.errors.renderValidation(response.data, ' ');
                return;
              }
              laravel.ajax.errorHandler(response);
              hideLoading();
            });
          hideLoading();
        }
    }
}
</script>
