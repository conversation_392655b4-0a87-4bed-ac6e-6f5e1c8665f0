<template>
  <div id="bank_accounts_panel" :style="hasResErrorMessage ? {borderColor: '#A4090F', borderWidth: '1.5px'} : {}" :class="{'panel': true, 'panel-default': true, 'has-steps': true, 'sides': false,
    'panel--disabled':  !entity.manager_identity_is_verified || (['company', 'firm'].includes(entity.type) && !entity.commercial_register_is_verified),
    'step-checked': localVerify,
    'mb-20': true
    }">
    <div
        :style="hasResErrorMessage ? { borderBottom: '0px', backgroundColor: '#F551571A' } : {}"
        class="panel-heading"
    >
      <h6 class="panel-title" :style="hasResErrorMessage ? {color: '#A4090F'} : {}" :data-step="5">
        الحساب البنكي
      </h6>
      <i class="sicon"></i>
    </div>

    <div class="panel-body">
      <div v-show="entity.manager_identity_is_verified">

        <div v-if="hasResErrorMessage" class="my-20">
          <ul>
            <li style="color: #A4090F">
              {{ resErrorMessage }}
            </li>
          </ul>
        </div>

        <article class="rec-article mb-20">
          <h6 class="mt-0 font-15 mb-5">يرجى التأكد من تطبيق الشروط التالية قبل طلب التحقق:</h6>
          <ul v-if="entity.type === 'person'">
            <li class="font-13 mb-5">
              أن يكون الحساب البنكي مطابق للأسم الموجود في شهادة الأيبان وليس على البطاقة البنكية
            </li>
            <li class="font-13 mb-5">
              لمعرفة كيفية أستخراج شهادة الأيبان <a href="https://help.salla.sa/article/**********" :target="!isMobile ? '_blank' : null">أضغط هنا</a>
            </li>
          </ul>
          <ul v-else-if="entity.type === 'firm' || entity.type === 'company'">
            <li class="font-13 mb-5">
              أن يكون الحساب البنكي باسم مؤسسة أو شركة، وليس فرد
            </li>
            <li class="font-13 mb-5">
              يجب ان يكون الحساب البنكي مربوط بالسجل التجاري
            </li>
            <li class="font-13 mb-5">
              لمعرفة كيفية أستخراج شهادة الأيبان <a href="https://help.salla.sa/article/**********" :target="!isMobile ? '_blank' : null">أضغط هنا</a>
            </li>
          </ul>
          <ul v-else>
            <li class="font-13 mb-5">
              أن يتم ربط الدفع في المتجر ببوابة دفع خارجية
            </li>
            <li class="font-13 mb-5">
              أن يكون أسم الحساب مطابق تماما لأسم الجمعية
            </li>
            <li class="font-13 mb-5">
              لمعرفة كيفية أستخراج شهادة الأيبان <a href="https://help.salla.sa/article/**********" :target="!isMobile ? '_blank' : null">أضغط هنا</a>
            </li>
          </ul>
        </article>

        <div :class="{'form-group': true, 'has-error':(errors && errors.bank_id) || (activeStore && !account.bank_id)}">
          <label>حدد البنك <span class="text-danger">*</span></label>
          <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-office"></i>
                </span>
            <treeselect
                v-model="account.bank_id"
                :multiple="false"
                :options="banks"
                :searchable="false"
                class="vue-treeselect--custom vue-treeselect--with-icon vue-treeselect--without-effect"
                placeholder="حدد البنك"
                :normalizer="normalizer"
                @select="changeHandler($event)"
            />
          </div>
          <form-error v-show="errors && errors.bank_id" >
            {{errors.bank_id? errors.bank_id.toString() : ''}}
          </form-error>
          <form-error v-show="errors && activeStore && !account.bank_id" >
            الرجاء تحديد البنك
          </form-error>
        </div>

        <div v-if="shouldShowAccountTypeDropdown" :class="{'form-group': true, 'has-error':errors.account_type || (activeStore && !account.account_type)}">
          <label>نوع الحساب البنكي <span class="text-danger">*</span></label>
          <div class="input-group">
            <span class="input-group-addon">
                    <i class="sicon-newspaper"></i>
                </span>
            <treeselect
                v-model="account.account_type"
                :multiple="false"
                :options="accountTypes"
                :searchable="false"
                class="vue-treeselect--custom vue-treeselect--with-icon vue-treeselect--without-effect has-error"
                placeholder="نوع الحساب البنكي"
                @select="handelAccountTypeChange($event)"
            />
          </div>
          <form-error v-show="errors && activeStore && !account.account_type" >
            حقل نوع الحساب البنكي مطلوب.
          </form-error>
          <form-error v-show="errors && errors.account_type" >
            {{errors.account_type ? errors.account_type.toString() : ''}}
          </form-error>
        </div>

        <template v-if="shouldShowAccountInputs">
        <div v-show="!supportLeanVerification" :class="{'form-group': true, 'has-error':errors.account_name || invalidAccountName || ( activeStore && isInvalidAccountName())}">
          <label>{{entityName}}<span class="text-danger">*</span></label>
          <div class="input-group">
            <span class="input-group-addon">
                <i class="sicon-user"></i>
            </span>
            <input v-model="account.account_name"
                   type="text"
                   class="form-control"
                   :placeholder="entityName"
                   @blur="isInvalidAccountName() ? invalidAccountName = true : invalidAccountName = false"
            >
          </div>
          <form-error v-show="(errors && invalidAccountName) || ( activeStore && isInvalidAccountName())" >
            <span v-if="entity.type === 'person'"> الرجاء كتابة الاسم الثلاثي لتوثيق المتجر</span>
            <span v-else> الرجاء كتابة {{entityName}} لتوثيق المتجر</span>
          </form-error>
          <form-error v-show="errors && errors.account_name" >
            {{errors.account_name ? errors.account_name.toString() : ''}}
          </form-error>
        </div>

        <div v-show="!supportLeanVerification" :class="{'form-group': true, 'has-error':errors.account_number || (activeStore && !account.account_number)}">
          <label>رقم الحساب <span class="text-danger">*</span></label>
          <div class="input-group">
          <span class="input-group-addon">
            <i class="sicon-envelope"></i>
          </span>
            <input
                  id="bank_account_number"
                  v-model="account.account_number"
                  type="text"
                  class="form-control _parseArabic"
                  :disabled="isInvalidAccountName()"
                  placeholder="رقم الحساب" />
          </div>
          <form-error v-show="errors && activeStore && !account.account_number" >
            حقل رقم الحساب مطلوب.
          </form-error>
          <form-error v-show="errors && errors.account_number" >
            {{errors.account_number ? errors.account_number.toString() : ''}}
          </form-error>
        </div>

        <div :class="{'form-group': true, 'has-error':errors.iban_number || (activeStore && !account.iban_number)}">
          <label>رقم الايبان <span class="text-danger">*</span></label>
          <div class="input-group">
          <span class="input-group-addon">
            <i class="sicon-envelope"></i>
          </span>
            <input
                  id="bank_iban_number" v-model="account.iban_number"
                  type="text"
                  maxlength="22"
                  class="form-control _parseArabic"
                  placeholder="رقم الآيبان"
                  :disabled="isInvalidAccountName()"
                  @change="localVerify = false">
            <span class="input-group-addon">SA</span>
          </div>
          <form-error v-show="errors && activeStore && !account.iban_number" >
            حقل رقم الآيبان مطلوب.
          </form-error>
          <form-error v-show="errors && errors.iban_number" >
            {{errors.iban_number ? errors.iban_number.toString() : ''}}
          </form-error>
        </div>

        <div v-show="!supportLeanVerification" class="form-group">
          <label>نوع شهادة التوثيق <span class="text-danger">*</span></label>
          <div class="input-group">
          <span class="input-group-addon">
                  <i class="sicon-newspaper"></i>
              </span>
            <treeselect
                v-model="account.certificate_type"
                :multiple="false"
                :options="certificateTypes"
                :searchable="false"
                class="vue-treeselect--custom vue-treeselect--with-icon vue-treeselect--without-effect has-error"
                placeholder="اختر نوع شهادة التوثيق"
            />
          </div>
          <form-error v-show="errors && activeStore && !account.certificate_type" >
            حقل نوع شهادة التوثيق مطلوب.
          </form-error>
          <form-error v-show="errors && errors.certificate_type" >
            {{errors.certificate_type ? errors.certificate_type.toString() : ''}}
          </form-error>
        </div>

        <div v-if="account.certificate_type === 'iban' && !supportLeanVerification" :class="{'form-group': true, 'has-error': (errors.iban_certificate) || (activeStore && (!account.iban_certificate))}">
          <imager
              v-model="account.iban_certificate"
              :crop-aspect-ratio="'null'"
              :entity="entity"
              :label="'شهادة الأيبان'"
              :finished-upload="account.iban_certificate !== null"
              :img-src="account.iban_certificate"
              :uploading="account.iban_certificate == null"
              allowed-image-type="يسمح بصورة من نوع jpg, jpeg, png او ملف من نوع pdf*"
              accepted-file-types="image/jpg, image/jpeg, image/png, application/pdf"
              :bank-account="true"
              @deleteImage="account.iban_certificate = null"
          />

          <form-error v-show="!account.iban_certificate">
            حقل شهاده الأيبان مطلوب.
          </form-error>

          <form-error v-show="errors && errors.iban_certificate" >
            {{errors.iban_certificate ? errors.iban_certificate.toString() : ''}}</form-error>

        </div>

        <div v-if="account.certificate_type === 'sbc'" :class="{'form-group': true, 'has-error': (errors.sbc_certificate) || (activeStore && (!account.sbc_certificate))}">

          <imager
              v-model="account.sbc_certificate"
              :crop-aspect-ratio="'null'"
              :entity="entity"
              :label="'شهادة منصة الأعمال'"
              :finished-upload="account.sbc_certificate !== null"
              :img-src="account.sbc_certificate"
              :uploading="account.sbc_certificate == null"
              allowed-image-type="يسمح بصورة من نوع jpg, jpeg, png او ملف من نوع pdf*"
              accepted-file-types="image/jpg, image/jpeg, image/png, application/pdf"
              :bank-account="true"
              @deleteImage="account.sbc_certificate = null"
          />

          <form-error v-show="!account.sbc_certificate">
            حقل شهاده منصة الأعمال مطلوب.
          </form-error>

          <form-error v-show="errors && errors.sbc_certificate" >
            {{errors.sbc_certificate ? errors.sbc_certificate.toString() : ''}}</form-error>

        </div>

        <div v-if="supportLeanVerification && localVerify && (accountHolderName || (entity.main_account.verified && (entity.type === 'person' && account.account_type === 'personal') && entity.main_account.details.account_name))" class="mb-10">
          <div v-text="entity.type === 'person' ? 'اسم صاحب الحساب' : 'اسم المؤسسة'"></div>
          <label class="mt-5"> {{ accountHolderName ?? entity.main_account.details.account_name }} <i class="sicon-check-circle2 text-green-800" aria-hidden="true"></i></label>
        </div>

        <button
                v-if="supportLeanVerification && !accountHolderName" :disabled="localVerify" class="btn btn--outlined primary w-lg-200 h-40 rec-fvm font-14"
                data-inline-loader  @click="verifyIban($event)">{{entity.main_account.verified ? 'إعادة التحقق' : 'تحقق'}}</button>
        </template>

        <template v-if="shouldShowIbanOnly">
        <div :class="{'form-group': true, 'has-error':errors.iban_number || (activeStore && !account.iban_number)}">
          <label>رقم الايبان <span class="text-danger">*</span></label>
          <div class="input-group">
          <span class="input-group-addon">
            <i class="sicon-envelope"></i>
          </span>
            <input
                  id="bank_iban_number" v-model="account.iban_number"
                  type="text"
                  maxlength="22"
                  class="form-control _parseArabic"
                  placeholder="رقم الآيبان"
                  @change="localVerify = false">
            <span class="input-group-addon">SA</span>
          </div>
          <form-error v-show="errors && activeStore && !account.iban_number" >
            حقل رقم الآيبان مطلوب.
          </form-error>
          <form-error v-show="errors && errors.iban_number" >
            {{errors.iban_number ? errors.iban_number.toString() : ''}}
          </form-error>
        </div>

        <div v-if="localVerify && (accountHolderName || (entity.main_account.verified && entity.main_account.details.account_name))" class="mb-10">
          <div>اسم المؤسسة</div>
          <label class="mt-5"> {{ accountHolderName ?? entity.main_account.details.account_name }} <i class="sicon-check-circle2 text-green-800" aria-hidden="true"></i></label>
        </div>
        <button
                v-if="!accountHolderName"
                :disabled="localVerify"
                class="btn btn--outlined primary w-lg-200 h-40 rec-fvm font-14"
                data-inline-loader
                @click="verifyIban($event)">
          {{entity.main_account.verified ? 'إعادة التحقق' : 'تحقق'}}
        </button>
        </template>
      </div>

      <error-message
          v-show="!entity.manager_identity_is_completed"
          :icon="'sicon-info'"
          :extra="false"
          :title="'التحقق من مدير المتجر أولاً'"
          :text="'يرجى التحقق من مالك و مدير المتجر قبل الحساب البنكي'"
          :sub-text="'و يجب أن تكون بيانات المالك أو اسم الشركة بالأعلى مطابقة لبيانات الحساب البنكي'"
          :show-btn="false"
      />
    </div>
  </div>
</template>

<style>
.swal2-container .swal2-modal .swal2-icon {
  font-size: 60px;
}
</style>

<script>
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import FormError from "./layouts/FormError";
import ErrorMessage from './layouts/ErrorMessage';
import Imager from './layouts/Imager';
import CommonSettingsMixin from "../mixins/CommonSettingsMixin";

export default {
  components: {
    Treeselect,
    FormError,
    ErrorMessage,
    Imager,
  },
  mixins: [CommonSettingsMixin],
  props: ['main_account', 'entity', 'banks', 'verified', 'activeStore','errors'],
  data() {
    return {
      certificateTypes: [
        {
          id: 'iban',
          label: 'شهادة الأيبان',
        },
        {
          id: 'sbc',
          label: 'شهادة منصة الأعمال',
        },
      ],

      accountTypes: [
        {
          id: 'personal',
          label: 'حساب شخصي',
        },
        {
          id: 'commercial',
          label: 'حساب تجاري',
        },
      ],
      resErrorMessage: null,
      hasResErrorMessage: false,
      source:'bank_accounts',
      invalidAccountName: false,
      accountHolderName: null,
      account: this.main_account ? this.main_account : this.getDefaultAccount(),
      normalizer(node) {
        return {
          label: node.name,
          id: node.id
        }
      },
      localVerify: false,
      leanVerificationSupported: true,
      initialBankId: null,
    }
  },
  computed: {
    entityLabel() {
      const entityLabels = {
        person: 'صاحب الحساب',
        company: 'المؤسسة',
        firm: 'الشركة',
        charity: 'الجمعية',
      }
      return entityLabels[this.entity.type]
    },
    supportLeanVerification() {
      if (!this.leanVerificationSupported || !this.account.bank_id) return false;

      const bank = this.findBankById(this.account.bank_id);
      if (!bank) return false;

      // Disable lean verification ONLY for Alrajhi bank when:
      // - Entity type is 'person' AND account type is 'commercial'
      if (bank.code === 'RJHI' && this.entity.type === 'person' && this.account.account_type === 'commercial') {
        return false;
      }

      if (!bank?.lean_supported) return false;

      const type = this.entity.type;
      const accountTypeSelected = !!this.account.account_type;

      if (type === 'charity') return false;
      if (type === 'person' && accountTypeSelected && this.account.account_type === 'personal') return true;
      return type === 'company' || type === 'firm';

    },
    entityName() {
      const entityLabels = {
        person: 'اسم صاحب الحساب الثلاثي',
        company: 'اسم المؤسسة',
        firm: 'اسم الشركة',
        charity: 'اسم الجمعية',
      }
      return entityLabels[this.entity.type]
    },
    isMobile () {
      let check = false;
      (function(a){if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);
      return check;
    },
    mainAccountVerified() {
      return this.localVerify;
    },
    bankChanged() {
      return this.account.bank_id !== this.initialBankId;
    },
    shouldShowAccountInputs() {
      // Early returns for basic validation
      if (!this.account.bank_id) return false;

      const bank = this.findBankById(this.account.bank_id);
      if (!bank) return false;

      if (!this.leanVerificationSupported) return true;

      // Entity-specific logic
      return this.shouldShowInputsForEntityType(bank);
    },
    shouldShowAccountTypeDropdown() {
      return this.entity.type === 'person' && !!this.account.bank_id;
    },
    shouldShowIbanOnly() {
      if (!this.account.bank_id) return false;
      if(!this.leanVerificationSupported) return false;

      const bank = this.findBankById(this.account.bank_id);

      if (!bank) return false;

      // Handle person entity
      if (this.entity.type === 'person') {
        // Don't show IBAN only if it's Alrajhi with commercial account type
        if (bank.code === 'RJHI' && this.account.account_type === 'commercial') {
          return false;
        }
        // Show IBAN only for personal accounts with lean supported banks
        return bank && bank.lean_supported && !! this.account.account_type;
      }

      // Handle company/firm entities
      if (this.entity.type === 'company' || this.entity.type === 'firm') {
        return bank.lean_supported;
      }

      return false;
    },
  },
  watch: {
    bankChanged(newVal) {
      if (newVal) {
        this.localVerify = !!(this.initialBankId === this.account.bank_id && this.verified);
      } else {
        if (this.initialBankId === this.account.bank_id && this.verified) {
          this.localVerify = true;
        }
      }
    }
  },
  mounted() {
    this.localVerify = this.verified
    this.initialBankId = this.account.bank_id;
  },
  created() {
    this.formatIbanNumber();
  },
  methods: {
    shouldShowInputsForEntityType(bank) {
      const entityHandlers = {
        person: () => this.shouldShowInputsForPerson(bank),
        charity: () => true,
        company: () => !bank.lean_supported,
        firm: () => !bank.lean_supported
      };

      const handler = entityHandlers[this.entity.type];
      return handler ? handler() : false;
    },
    shouldShowInputsForPerson(bank) {
      // First check if account type is selected
      if (!this.account.account_type) return false;
      
      // Show inputs if bank doesn't support lean OR it's commercial account with Alrajhi
      return !bank.lean_supported || this.isCommercialAlrajhiAccount(bank);
    },
    isCommercialAlrajhiAccount(bank) {
      return this.account.account_type === 'commercial' && bank.code === 'RJHI';
    },
    handelAccountTypeChange(){
      this.accountHolderName = null;
      this.localVerify = false;

      this.$emit('update-entity', {
        account_name: null
      });

      this.account.certificate_type = null;
    },
    changeHandler() {
      this.account.iban_number = '';
      this.account.account_type = null;
      this.accountHolderName = null;
      this.localVerify = false;

      this.$emit('update-entity', {
        account_name: null
      });

    },
    getDefaultAccount() {
      return {
        'account_number': null,
        'account_name': null,
        'iban_number': null,
        'bank_id': null,
        'bank_name': null,
        'iban_certificate': null,
        'sbc_certificate': null,
        'certificate_type': null,
        'lean_supported': false,
      };
    },
    handleValidationErrors(validationErrors) {
      for (const field in validationErrors) {
        // eslint-disable-next-line no-prototype-builtins
        if (validationErrors.hasOwnProperty(field)) {
          // eslint-disable-next-line vue/no-mutating-props
          this.errors[field] = validationErrors[field][0];
        }
      }
    },
    getValidationErrorMessage(validationErrors, defaultMessage) {
      if (validationErrors && validationErrors.national_id) {
        return validationErrors.national_id[0];
      }
      if (validationErrors && validationErrors.iban_number) {
        return validationErrors.iban_number[0];
      }
      if (validationErrors && validationErrors.certificate_type) {
        return validationErrors.certificate_type[0];
      }
      if (validationErrors && validationErrors.iban_certificate) {
        return validationErrors.iban_certificate[0];
      }
      if (validationErrors && validationErrors.sbc_certificate) {
        return validationErrors.sbc_certificate[0];
      }
      if (validationErrors && validationErrors.account_name) {
        return validationErrors.account_name[0];
      }
      if (validationErrors && validationErrors.account_type) {
        return validationErrors.account_type[0];
      }
      return defaultMessage;
    },
    formatIbanNumber() {
      if (!this.account.iban_number) return;
      this.account.iban_number = this.account.iban_number.replace('SA', '');
    },
    isInvalidAccountName() {

      if (this.supportLeanVerification) {
        return false;
      }

      if (this.entity.type === 'person') {
        return !this.account.account_name || (this.account.account_name.trim().split(/\s+/).length < 3);
      }
      return !this.account.account_name;
    },
    async verifyIban(event) {
      const payload = {
        national_id: this.entity.owner_identity_number,
        iban_number: this.main_account.iban_number,
        bank_id: this.main_account.bank_id,
        account_number: this.main_account.account_number,
        account_name: this.main_account.account_name,
        certificate_type: this.account.certificate_type,
        account_type: this.account.account_type,
        iban_certificate: this.entity.main_account.details.certificate_type === 'iban' ? this.entity.main_account.details.iban_certificate : null,
        sbc_certificate: this.entity.main_account.details.certificate_type === 'sbc' ? this.entity.main_account.details.sbc_certificate : null,
      }
      try {
        showLoading(event); // eslint-disable-line no-undef

        const { data } = await axios.post('/entity/verification/verify/iban', payload);

        swal({
          text: data.message,
          type: 'success',
          timer: 3000,
          showConfirmButton: false,
        });

        this.$emit('update-entity', {
          verified: true,
          account_name: data.data.account_holder_name,
          account_number : null,
          certificate_type: null,
          iban_certificate: null,
          sbc_certificate: null
        });

        this.accountHolderName = data.data.account_holder_name;

        this.hasResErrorMessage = false;

        this.resErrorMessage = '';
        this.localVerify = true;

      } catch (error) {

        if (error.response) {
          const { status, data: errorData } = error.response;

          this.$emit('update-entity', {
            verified: false,
          });

          if (status === 422) {
            this.localVerify = false;
            this.handleValidationErrors(errorData.error.fields);
            this.hasResErrorMessage = true;
            this.resErrorMessage = this.getValidationErrorMessage(errorData.error.fields, errorData.error.message);
          } else {
            this.showSweetAlert(errorData.error.message || 'عفوا, حدث خطأ ما', errorData?.error?.data?.show_confirm);
          }

          if (errorData?.error?.data && errorData?.error?.data?.type === 'UNSUPPORTED') {
            this.handleUnsupportedBankError(errorData);
          }

          if (errorData?.error?.data && errorData?.error?.data?.type === 'REDIRECT_TO_MANUAL') {
            this.handleLeanVerificationLimitExceeded(errorData);
          }
        } else {
          this.showSweetAlert('عفوا, حدث خطأ ما الرجاء المحاولة لاحقا', false);
        }

      }
      hideLoading(); // eslint-disable-line no-undef
    },
    findBankById(id) {
      return this.banks.find(bank => bank.id === id);
    },
    showSweetAlert(message, showConfirmButton) {
      swal({
        text: message,
        type: 'warning',
        showConfirmButton: showConfirmButton,
        showCancelButton: !showConfirmButton,
        confirmButtonText: "إعادة المحاولة",
        cancelButtonText: "إغلاق",
        cancelButtonClass: "btn btn-default bg-gray text-dark-200 font-norma wide",
        confirmButtonClass: "btn btn-success bg-gray text-dark-200 font-norma wide",
        buttonsStyling: false,
      }).then( result =>  result &&  window.location.reload())
    },
    handleUnsupportedBankError(errorData) {
      this.switchToManualVerification();
      this.notifyParentOfManualVerification();
      this.showManualVerificationAlert(errorData.error.message)
    },
    handleLeanVerificationLimitExceeded(errorData) {
      this.switchToManualVerification();
      this.notifyParentOfManualVerification();
      this.showManualVerificationAlert(errorData.error.message);
    },
    switchToManualVerification() {
      this.leanVerificationSupported = false;
      this.shouldShowIbanOnly = false;
      this.shouldShowAccountInputs = true;
    },
    notifyParentOfManualVerification() {
      this.$emit('update-entity', {
        should_go_with_manual_verification: true,
      });
    },
    showManualVerificationAlert(message) {
      this.showSweetAlert(message + ' سيتم التحويل للتحقق اليدوي.', false);
    }
  },
}
</script>

<style lang="scss">
.has-error{
  .filepond--drip{background: red}
}
</style>
