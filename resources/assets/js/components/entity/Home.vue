<template>
  <div>
    <div v-if="storeVerifications && storeVerifications.length == 0 " class="panel panel-default">
      <div class="panel-heading">
        <h6 class="panel-title">سجل طلبات التوثيق<span class="ml-5">(0 طلب)</span></h6>
      </div>
      <div class="panel-body rec-placeholder mt-50 mb-50">
          <div class="rec-placeholder__icon"><i class="sicon-shield-check"></i></div>
          <h2 class="rec-placeholder__title">لا توجد طلبات توثيق</h2>
          <p class="rec-placeholder__desc">ارفع طلب توثيق متجرك بسهولة وبخطوات بسيطة! </p>
      </div>
    </div>

    <div v-else class="table table-responsive table-responsive--ov-x-visible table-verification">
      <table class="table">
        <thead>
          <tr class="active">
            <th>رقم طلب التوثيق</th>
            <th>تاريخ رفع الطلب</th>
            <th width="20%" class="text-left-align">{{info.has_options ? 'خيارات' : ''}}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item,index in storeVerifications">
            <td>{{storeVerifications.length - index}}</td>
            <td>{{formatDate(item.uploaded_at.date)}} <span v-if="isToday(item.uploaded_at.date)" class="badge badge--grey ml-5">اليوم</span> </td>
            <td class="rec-flex end">
              <div class="rec-flex" style="min-width: 120px;">
                <div class="status" :class="item.status">{{getStatus(item.status)}}</div>
                <div class="text-right more-opt d-inline-block ml-15">
                  <button v-if="item.status == 'under_review' || item.status == 'rejected' && item.can_view " class="btn btn-default btn--more-nav rec-btn rec-btn--trans">
                    <div class="dot"></div>
                    <ul class="rec-list rec-list--vertical more-options">
                      <li v-if="item.status == 'rejected' && item.can_view">
                        <a href="/entity/verification/verify" class="font-13 text-default">
                          <i class="sicon-share flip-x text-primary mr-5 font-14"></i>
                          عرض سبب الرفض
                        </a>
                      </li>
                      <li v-if="item.status == 'under_review'">
                        <a class="font-13 text-danger" @click="cancelRequest(item)">
                          <i class="sicon-trash-2 flip-x mr-5 font-14"></i>
                            إلغاء طلب التوثيق
                        </a>
                      </li>
                    </ul>
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
const moment = require('moment');
// require('moment/locale/ar');
export default {

  data() {
    return {
      info: {},
      storeVerifications: null,
    };
  },

  mounted(){
    this.getRequests();
    sallaLegacy && this.onClickSendVerify();
    this.setBreadcrumbs();
  },

  computed:{
    getReqBtnTitle() {
      let title = this.info.can_update_verification ? 'تحديث بيانات التوثيق' : 'رفع طلب توثيق';
      return title;
    }
  },

  methods:{
    setBreadcrumbs(){
      const items = [
        { label: 'المحفظة', route: "/payments/wallet" },
        { label: 'توثيق المتجر' }
      ];
      window.parent?.postMessage({
        event: 'breadcrumb', data: items
      }, '*');
    },
    
    onClickSendVerify(){
      let that = this;
      document.getElementById('sendVerify').addEventListener('click', function(){
        if(this.dataset.btntype == 'update'){
            swal({
              title: 'تحديث توثيق المتجر',
              text: 'يمكنك تحديث بيانات التوثيق عبر رفع طلب توثيق جديد، وسيبقى توثيق متجرك مكتمل حتى قبول الطلب الجديد..',
              type: 'warning',
              showConfirmButton: true,
              showCancelButton: true,
              confirmButtonText: "تحديث بيانات التوثيق",
              cancelButtonText: "إغلاق"
            })
            .then( result =>  result &&  (window.location.href = '/entity/verification/verify') )
            .catch(() => {return});
        }else{
          window.location.href = '/entity/verification/verify';
        }
      })
    },
    cancelRequest(request){
      let msgTitle = 'هل أنت متأكد من إلغاء طلب التوثيق؟',
          msgText  =  'سيتم حذف كافة بيانات التوثيق في الطلب بشكل نهائي',
          isShowConfirmBtn = true;

      if(!request.can_cancel){
        msgTitle = 'هذة الميزة متاحة فقط للباقات المدفوعة';
        msgText  =  'يجب أن تكون مشترك على باقة مدفوعة لكي تتمكن من إلغاء طلب التوثيق';
        isShowConfirmBtn = false ;
      }

      swal({
            title: msgTitle,
            text: msgText,
            type: 'warning',
            showConfirmButton: isShowConfirmBtn,
            showCancelButton: true,
            confirmButtonText: "إلغاء الطلب",
            cancelButtonText: "إغلاق"
          })
          .then( result =>  (result && request.can_cancel) &&  this.cancelAction(request.id) )
          .catch(() => {return});
    },

    cancelAction(requestId){
      axios.post(`${window.location.origin}/entity/verification/cancel-request`, {id: requestId}).then(res=>{
       let currentReq = this.storeVerifications.find(item => item.id == requestId);
       currentReq.status = 'canceled';
       window.location.reload();  
      }).catch(err=> {});
    },

    getStatus(key){
      let status = {
        under_review: "قيد المراجعة",
        approved: "مكتمل",
        rejected: "مرفوض",
        canceled: "ملغي",
      }
      return status[key];
    },
     async getRequests(){
        await axios.get(`${window.location.origin}/entity/verification/info`)
          .then (res => {
            this.storeVerifications = res.data.data.store_verifications || [];
            this.info = res.data.data;
            hideLoading();
          })
          .catch(error => {
            hideLoading();
          })
      },

      formatDate(dateString) {
        return moment(dateString).locale('ar_sa').format('dddd Do MMMM YYYY | h:mm a')
      },

      isToday(date){
        let reqDate = new Date(date);
        if (reqDate.toDateString() == new Date().toDateString()){
          return true
        }
        return false;
      }
  }
};
</script>