<template>
    <div>
      <section v-if="entity && entity.rejection_reason && entity.rejection_reason.length > 0" class="alert-box alert-box--danger">
          <i class="sicon-warning"></i>
          <article>
            <h6 class="mt-0 font-16 mb-5">أسباب رفض طلب توثيق المتجر</h6>
            <ul>
              <li v-for="reasonObj in entity.rejection_reason" :key="reasonObj.id">
                <p>{{ reasonObj.reason }}</p>
              </li>
            </ul>
          </article>
      </section>


<!--      <section  v-if="entity.show_contact_details_warning" class="alert-box alert-box&#45;&#45;warning">-->
<!--        <i class="sicon-warning"></i>-->
<!--        <article>-->
<!--          <h6 class="mt-0 font-16 mb-5">يرجى التأكد من تطبيق الشروط التالية قبل البدء بتوثيق المتجر:  </h6>-->
<!--          <ul>-->
<!--            <li class="font-13">-->
<!--              أضافة معلومات التواصل-->
<!--              <a href="/settings/component/basic">انقر هنا لتحديث معلومات التواصل</a>-->
<!--            </li>-->
<!--          </ul>-->
<!--        </article>-->
<!--      </section>-->

    <!-- First Panel => Entity Type -->
    <entity-type v-if="entity && entity.id" :entity="entity"/>

    <!-- Second Panel => Store Owner -->
    <store-owner v-if="entity && entity.id" :entity="entity" />

    <!-- Third Panel => Store Manager  -->
    <store-manager v-if="entity && entity.id" :entity="entity" />

        <freelance-register :entity="entity"
                            :errors="errors"
                            v-if="['person'].includes(entity.type)" :activeStore="activeStore" />

        <!-- Forth Panel => Commercial Register -->
        <commercial-register :entity="entity" v-if="['company','firm'].includes(entity.type)" />

    <!-- Fifth Panel => Tax Number -->
    <!-- <tax-number :entity="entity" v-show="entity.type !== 'charity'" />-->

        <!-- Sixty Panel => Charity Image -->
        <charity :entity="entity" v-if="['charity'].includes(entity.type)" />

        <!-- Seventh Panel Bank Accounts -->
        <bank-accounts v-if="entity.main_account"
                       :errors="errors"
                       :entity="entity"
                       :main_account="entity.main_account.details"
                       :banks="entity.main_account.banks"
                       :verified="entity.main_account.verified"
                       :activeStore="activeStore"
                       @update-entity="handleEntityUpdate"
        />
        <button  v-if="entity && entity.id" type="button" class="btn btn-xl btn-tiffany btn-xlg wide"
                @click="checkDocumentaionStatus"
                data-inline-loader >
          {{entity.is_store_documented || entity.has_under_review_request ? 'اعادة ارسال الطلب' : 'ارسال الطلب' }}
        </button>

        <!-- content -->
      
        <documentaion-info v-if="entity.id" ref="documentationInfoRef" :entity="entity" @submit="sendRequest" />
    </div>
</template>
<script>

import DocumentaionInfo from './components/DocumentaionInfo';
import EntityType from './components/EntityType';
import StoreOwner from './components/StoreOwner';
import StoreManager from './components/StoreManager';
import FreelanceRegister from './components/FreelanceRegister';
import CommercialRegister from './components/CommercialRegister';
import TaxNumber from './components/TaxNumber';
import Charity from './components/Charity';
import BankAccounts from './components/BankAccounts';
import InitialDataService from './services/InitialDataService';
import axios from 'axios';

export default {
    data() {
        return {
          errors: [],
          entity: {},
          activeStore: false,
          shouldGoWithManualVerification: false,
        }
    },
    mounted(){
      this.getEntity();
    },
    created() {
      // Breadcrumb data, adding the breadcrumb here since this route is coming from Vue.js
      const items = [
        {
          label: "المحفظة",
          url: "/wallet",
        },
        {
          label: "توثيق المتجر",
          url: "/entity/verification",
        },
        {
          label: "التوثيق",
          url: "/entity/verification/verify",
        },
      ];

      window.parent?.postMessage({
        event: "breadcrumb",
        data: items,
      }, '*');
    },
    methods: {
      async getEntity(){
        setTimeout(() => {
          showLoading();
        }, 1);
        await axios.get(`${window.location.origin}/entity/verification/verify/info`)
          .then (res => {
            this.setEntity(res.data.data)
            hideLoading();
            setTimeout(() => {
              window?.parent?.postMessage({ event: "overlay.close" }, "*");
            }, 1500)
          })
          .catch(error => {
            hideLoading();
          })
      },

      setEntity(storeDocument) { // Fire when the type changes
          this.entity = storeDocument;
      },
      checkDocumentaionStatus(){
        $('#entity_modal').modal('hide')
        if( !this.entity.owner_identity_is_verified || !this.entity.manager_identity_is_verified
            || (['company','firm'].includes(this.entity.type) && !this.entity.commercial_register_is_verified)){
          let field = !this.entity.owner_identity_is_verified ? 'store_owner' :
              (!this.entity.manager_identity_is_verified ? 'store_manager' : 'commercial_register');
          let message = this.getErrorMessage(field)
          swal({
            title: '',
            text: message,
            type: 'warning',
            showConfirmButton: false,
            timer: 2500,
          });

          $("html, body").animate({ scrollTop: $('#' + field + '_verify_panel').offset().top }, 200)
          return false;
        }

        if(this.entity.is_store_documented && !this.entity.has_prior_approved_request){
             swal({
              title: 'تحويل المتجر لوضع الصيانة',
              text: 'في حال رفع طلب توثيق جديد سيتم تحويل المتجر لوضع قيد الصيانة إلى أن يتم قبول طلب التوثيق الجديد',
              type: 'warning',
              showConfirmButton: true,
              showCancelButton: true,
              confirmButtonText: "موافق",
              cancelButtonText: "إلغاء"
            })
            .then( result =>  result &&  this.activeStoreDocumentation() )
            .catch(() => {return});
            return
          }

        this.activeStoreDocumentation();
        },

      async activeStoreDocumentation() {
        this.activeStore = true;

          //charity validation
        if (this.entity.type === 'charity' && !this.entity.charity_certificate_image) {
          $("html, body").animate({ scrollTop: $('#charity_register_panel').offset().top }, 200)
          return false;
        }

        // freelance_certificate valiation
        if ((this.entity.type === 'person') && (!this.entity.freelance_certificate || !this.entity.freelance_number)) {
          $("html, body").animate({ scrollTop: $('#freelace_register_panel').offset().top }, 200)
          return false
        }

        // bank account validation
        const mainAccount = this.entity.main_account;
        const selectedBank = mainAccount.banks.find(bank => bank.id === mainAccount.details.bank_id);
        const alrajihiBank = selectedBank?.code === 'RJHI';

        const isBankUnverified = !mainAccount.verified;
        const isLeanSupported = selectedBank?.lean_supported &&
            this.entity.type !== 'charity' &&
            !(alrajihiBank && this.entity.type === 'person' && mainAccount.details.account_type === 'commercial');

        // If Lean is supported but bank is not verified => show warning
        if (isLeanSupported && isBankUnverified && !this.shouldGoWithManualVerification) {

          swal({
            title: '',
            text: 'يرجى التحقق من الحساب البنكي أولا',
            type: 'warning',
            showConfirmButton: false,
            timer: 2500,
          });

          $("html, body").animate({ scrollTop: $('#bank_accounts_panel').offset().top }, 200);

          return false;

        }

        // If Lean is NOT supported, all fields are required manually
        if (!isLeanSupported && (
            !mainAccount.details?.account_name ||
            !mainAccount.details?.account_number ||
            !mainAccount.details?.bank_id ||
            !mainAccount.details?.iban_number ||
            (mainAccount.details?.certificate_type === 'iban' && !mainAccount.details?.iban_certificate) ||
            (mainAccount.details?.certificate_type === 'sbc' && !mainAccount.details?.sbc_certificate)
        )) {

          swal({
            title: '',
            text: 'يرجى تعبئة بيانات الحساب البنكي',
            type: 'warning',
            showConfirmButton: false,
            timer: 2500,
          });

          return false;

        }

        await this.$refs.documentationInfoRef.getSignedImagesUrls();

        $('#entity_modal').modal('show')
      },

      async sendRequest(){
        $('#entity_modal').modal('hide')
        let payload = {
              id: this.entity.main_account.details.id ? this.entity.main_account.details.id : null,
              account_number: this.entity.main_account.details.account_number,
              account_name: this.entity.main_account.details.account_name,
              account_type: this.entity.main_account.details.account_type,
              bank_id: this.entity.main_account.details.bank_id,
              bank_name: this.entity.main_account.details.bank_name,
              iban_number: this.entity.main_account.details.iban_number,
              iban_certificate: this.entity.main_account.details.certificate_type === 'iban' ? this.entity.main_account.details.iban_certificate : null,
              sbc_certificate: this.entity.main_account.details.certificate_type === 'sbc' ? this.entity.main_account.details.sbc_certificate : null,
              certificate_type: this.entity.main_account.details.certificate_type,
              entity: this.entity.type,
              request_validation_box: this. entity.request_validation_box,
              owner_identity_image: this.entity.owner_identity_image,
              manager_identity_image: this.entity.manager_identity_image,
              commercial_register_image: this.entity.commercial_register_image
          }

          if(this.entity.type === "person"){
            payload.freelance_certificate = this.entity.freelance_certificate;
            payload.freelance_number = this.entity.freelance_number;
          }

          else if(this.entity.type === "charity"){
            payload.charity_certificate_image= this.entity.charity_certificate_image;
          }

         await axios.post(`${window.location.origin + window.location.pathname}/send-request`, payload)
          .then (res => {
            hideLoading();
            this.activeStore = false;
            swal({
              text: res.data.message,
              type: 'success',
              showConfirmButton: false,
              timer: 2500
            });
            location.reload();
          })
          .catch(error => {
            hideLoading();
            let errorMessage = error.response.data.error.message;
            this.errors = error.response.data.error.fields || {};
            $("html, body").animate({ scrollTop: $('#bank_accounts_panel').offset().top }, 200)
            if(errorMessage !== 'alert.invalid_fields') {
              swal({
                title: '',
                html: errorMessage,
                confirmButtonText: 'موافق',
                type: 'warning',
              });
            }
          })
      },

      getErrorMessage(field) {
        switch (field) {
          case 'store_owner':
            return 'يرجى التحقق من هوية مالك المتجر أولا';
            break;
          case 'store_manager':
            return 'يرجى التحقق من هوية مدير المتجر أولا'
            break;

          case 'commercial_register':
            return 'يرجى التحقق من السجل التجاري أولا';
            break;
        }
      },
      handleEntityUpdate(data) {
        if ('verified' in data) {
          this.entity.main_account.verified = data.verified;
        }

        if ('account_name' in data) {
          this.entity.main_account.details.account_name = data.account_name;
        }

        if ('account_number' in data) {
          this.entity.main_account.details.account_number = data.account_number;
        }

        if ('certificate_type' in data) {
          this.entity.main_account.details.certificate_type = data.certificate_type;
        }

        if ('iban_certificate' in data) {
          this.entity.main_account.details.iban_certificate = data.iban_certificate;
        }

        if ('sbc_certificate' in data) {
          this.entity.main_account.details.sbc_certificate = data.sbc_certificate;
        }

        if ('should_go_with_manual_verification' in data) {
          this.shouldGoWithManualVerification = data.should_go_with_manual_verification;
        }
      }
  },
    components: {
      'entity-type': EntityType,
      'store-owner': StoreOwner,
      'store-manager': StoreManager,
      'commercial-register': CommercialRegister,
      'tax-number': TaxNumber,
      'charity': Charity,
      BankAccounts,
      FreelanceRegister,
      DocumentaionInfo,
    }
}
</script>
