<template>
  <div id="order_custom_fields_wrapper">
    <div
      v-if="selected_options.length >= 4"
      class="alert-box alert-box--warning"
    >
      <i class="sicon-info"></i>
      <article>
        <p>يمكن تحديد 4 عناصر كحد أقصى.</p>
      </article>
    </div>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h6 class="panel-title">
          <i class="sicon-list"></i>
          تخصيص قائمة الطلبات
          <span class="text-mute font-12 ml-20"> (الحد الأقصى 4 عناصر) </span>
        </h6>
        <p class="text-muted font-14 mt-10">
          حدِّد معلومات الطلب التي تود استعراضها في قائمة الطلبات وفقًا
          لاحتياجاتك.
        </p>
      </div>
      <div class="panel-body no-padding">
        <ul class="rec-list rec-list--vertical rec-list--order-status">
          <li
            v-for="option in options"
            :key="option.id"
            class="option-item p-20"
          >
            <span>{{ option.title }}</span>
            <div class="">
              <input
                :id="`option-${option.id}-switchery`"
                class="switchery"
                type="checkbox"
                :checked="checkIfSelected(option.id)"
                @change="toggleOption(option)"
              />
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
/*global showLoading,axios,swal,hideLoading*/

export default {
  components: {},
  filters: {},
  data() {
    return {
      options: [],
      selected_options: [],
    }
  },
  computed: {},
  mounted() {
    this.options = window.initialData?.options

    this.checkSelectedOptions()
  },
  methods: {
    checkSelectedOptions() {
      this.selected_options = [
        ...this.options
          .filter((option) => option.is_selected)
          .map((option) => option.id),
      ]
    },
    checkIfSelected(optionId) {
      return this.selected_options.includes(optionId)
    },
    toggleOption(option) {
      const elm = $(`#option-${option.id}-switchery`)
      if (!option.is_selected && this.selected_options.length >= 4) {
        elm.prop('checked', false)
        return
      }
      option.is_selected = !option.is_selected
      if (option.is_selected) {
        this.selected_options.push(option.id)
      } else {
        this.selected_options = this.selected_options.filter(
          (id) => id != option.id
        )
      }
      this.updateOptions(option)
    },
    async updateOptions(option) {
      showLoading()
      try {
        await axios.post('/orders/custom-fields', {
          option_id: option.id,
        })
        // if (data.status === 200) {
        //   swal({
        //     text: data.message || 'تم تحديث البيانات بنجاح',
        //     type: 'success',
        //     showConfirmButton: false,
        //     timer: 2500,
        //   });
        // }
      } catch (error) {
        let errorMessage = 'حدث خطأ ما'

        if (error.response && error.response.data) {
          errorMessage = error.response.data.message || errorMessage
        }

        swal({
          text: errorMessage,
          type: 'error',
          showConfirmButton: false,
          timer: 2500,
        })
      }
      hideLoading()
    },
  },
}
</script>

<style lang="scss">
.option-item {
  border: 1px solid #f8f8f8 !important;
  width: 100%;
  margin-bottom: 0 !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
