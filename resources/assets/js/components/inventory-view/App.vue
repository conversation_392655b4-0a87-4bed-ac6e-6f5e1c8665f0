<template>
  <div class="col-xs-7 sub-btns">

    <ul class="nav nav-lg">
      <li class="dropdown">
          <div class="filter-mode bulk-edit-filter align-left pt-5" :data-filter-mode="filters && filters.filtering ==1">
            <a id="rec_filter_toggle" class="btn btn-filter-toggle " @click="getFilters()">
              <i class="sicon-filter"></i>
              تصفية
              <i class="sicon-cancel" @click="cancelFilter" v-show="filters && filters.filtering ==1"></i>
            </a>
          </div>
      </li>
    </ul>

    <filters :showExport="can_export" :extraClass="false" :exportUrlParam="getExportUrl()"></filters>

  </div>
</template>

<script>
import Filters from './components/filters';
import initialData from "../../services/ProductService";

export default {
  data() {
    return {
      page: 1,
      filters: [],
      can_export: false,
    };
  },
  components: {
    'filters': Filters,
  },
  mounted() {
    Salla.event.addEventListener('inventory::submit-filters', this.submitFilters);
  },
  beforeDestroy() {
    document.removeEventListener('inventory::submit-filters', this.submitFilters);

  },
  created() {
    this.filters = initialData.getDatum('filters', []) || [];
    this.can_export = initialData.getDatum('can_export', false);
  },
  methods: {
    /**
     * append Filters data
     *
     * @param data
     * @returns {*}
     */
    appendFilters(data) {
      data.filtering = 1;
      data.tags = this.filters.tags || [];
      data.brands = this.filters.brands || [];
      data.branches = this.filters.branches || [];
      data.categories = this.filters.categories || [];
      data.keyword = this.filters.keyword || '';

      return data;
    },

    /**
     * Cancel Products filter
     * then reload all products
     */
    cancelFilter(e) {
      e.stopPropagation();
      this.filters = [];
      Salla.event.createAndDispatch('inventory::reset-filters');
      this.reloadProducts();
    },
    /**
     * fetch all filters data
     */
    getFilters() {
      Salla.event.createAndDispatch('inventory::fetch-filters');
    },


    /**
     * Reload products
     */
    reloadProducts() {
      this.page = 1;
      let data = {
        page: this.page,
      };

      if (this.filters && this.filters.filtering) {
        data = this.appendFilters(data);
      }

      window.location.href = '/products/inventory?'+ this.getQueryString(data);
    },

    submitFilters(e) {
      this.filters = [];
      this.filters.filtering = 1;
      this.filters.tags = e.detail.tags;
      this.filters.brands = e.detail.brands;
      this.filters.branches = e.detail.branches;
      this.filters.categories = e.detail.categories;
      this.filters.keyword = e.detail.keyword;
      this.reloadProducts();
    },

    /**
     * append filters query string to export url
     * @returns {string}
     */
    getExportUrl() {
      let data = {};

      if (this.filters && this.filters.filtering) {
        data = this.appendFilters(data);
      }

      return '/products/inventory/export?type=csv&' + this.getQueryString(data);
    },

    getQueryString(data) {
      return Object.keys(data).map(function (key) {
        if (typeof(data[key]) != 'object') {
          return key + '=' + data[key];
        }

        if (typeof(data[key]) == 'object' && data[key].length > 0) {
          let objectString = (data[key].length > 0) ?
              data[key].map(id =>  key + '[]=' + id).join('&') :
              key + '[]=' + data[key];

          return objectString;
        }
      }).filter(key => key != undefined).join('&');
    }
  },
}
</script>
