<!------------ Inventory Filters By ( branches, brands, categories, or tags ) ------------>
<template>
    <div :class="{'rec-filter-wrapper': true,  'rec-filter-wrapper-products': true, 'checkPlacement': extraClass}">
        <div class="rec-filter-cont">
            <form>
                <div class="rec-filters">
                    <h1 class="title title--small">
                        <i class="sicon-filter"></i> فرز المنتجات حسب
                        <button type="button" id="filter_close" class="btn btn--circular"></button>
                    </h1>

                    <div v-for="(filter,index) in filters" class="rec-filter__section" :key="index">
                        <template v-if="!['status', 'types'].includes(index)">
                            <button type="button"
                                :data-target="`#filter_${index}_categories`"
                                data-toggle="collapse" class="btn filter-head"
                                aria-expanded="true">
                                <span> <i :class="filter.icon"></i>{{ filter.label }}</span>
                            </button>
                            <div class="collapse filter-content collapse in"
                                :id="`filter_${index}_categories`"
                                aria-expanded="true">

                                <ul class="rec-list rec-list--vertical">
                                    <list :filter="filter" :placeholder="'الماركة'" v-if="filter.name==='brands'"
                                        :updateSelectedList="updateSelectedBrands" :index="index"></list>
                                    <list :filter="filter" :placeholder="'الفرع'" v-if="filter.name==='branches'"
                                        :updateSelectedList="updateSelectedBranches" :index="index"></list>
                                    <categories :filter="filter" v-if="filter.name==='categories'"
                                                :set_selected_categories="true"
                                                :updateSelectedList="updateSelectedCategories" :index="index"></categories>
                                    <list :filter="filter" :placeholder="'الوسوم'" v-if="filter.name==='tags'"
                                        :updateSelectedList="updateSelectedTags" :index="index"></list>
                                </ul>
                            </div>
                        </template>
                    </div>
                    <div class="rec-filter__submit">
                        <button type="button" class="btn btn-tiffany btn-filter-submit" @click="submitFilters">عرض
                            النتائج
                        </button>
                        <button type="reset" @click="resetFilters" class="btn btn-outline-dark btn-filter-reset">إعادة
                            تعيين
                        </button>
                    </div>

                    <div v-if="showExport" class="rec-filter__section mt-10">
                        <div v-if="hasFilter" class="btn-group rec-btn-group full-width" role="group">
                            <a @click="exportFilterResults(exportUrlParam)" id="filterOptions"
                               type="button"
                               class="btn btn-wide btn-default btn-icon-prepend rec-fvm">
                                <i class="sicon-file-download"></i>
                                تصدير النتائج
                            </a>
                        </div>
                        <div v-else class="btn-group rec-btn-group full-width" role="group" data-toggle="tooltip" title="يجب عليك فلترة المنتجات حتى تستطيع تصديرها">
                            <button disabled="disabled"
                               class="btn btn-wide btn-default btn-icon-prepend rec-fvm">
                                <i class="sicon-file-download"></i>
                                تصدير النتائج
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>


<script>

    import Http from "../../../utils/http";
    import initialData from "../../../services/ProductService";
    import List from "../../products-view/components/filterTypes/list";
    import Checkbox from "../../products-view/components/filterTypes/checkbox";
    import Categories from "../../products-view/components/filterTypes/categories";

    export default {
        props: ['extraClass', 'exportUrlParam', 'showExport'],
        components: {Categories, Checkbox, List},
        data: () => ({
            filters: [],
            selected_filters: [],
            selected_tags: [],
            selected_brands: [],
            selected_branches: [],
            selected_categories: [],
        }),
        computed: {
            hasFilter(){
                let filters = ['selected_filters', 'selected_tags', 'selected_brands', 'selected_branches', 'selected_categories'];

                return filters.some(el => this[el].length);
            }
        },
        created() {
          let filtersArr = initialData.getDatum('filters', []) || [];

          if (Object.keys(filtersArr).length) {
                this.selected_filters = filtersArr || [];
                this.selected_tags = filtersArr.tags || [];
                this.selected_brands = filtersArr.brands || [];
                this.selected_branches = filtersArr.branches || [];
                this.selected_categories = filtersArr.categories || [];
            }

        },
        mounted() {
            Salla.event.addEventListener('inventory::fetch-filters', this.getFilters);
            Salla.event.addEventListener('inventory::set-selected-filters', this.setSelectedFilters);
            Salla.event.addEventListener('inventory::reset-filters', this.resetSelectedFilters);


            $('.rec-filter-wrapper-products .btn-filter-submit').click(function (e) {
                e.preventDefault()
                $(this).parents('.rec-filter-wrapper-products').removeClass('reveal');
                $(this).parents('.rec-filter-wrapper-products').addClass('conceal');
                $('body').removeClass('rec-filter-open')
            });

            $('.bulk-edit-filter').click(function () {
                $('.rec-filter-wrapper-products').addClass('reveal');
                $('.rec-filter-wrapper-products').removeClass('conceal');
                $('body').addClass('rec-filter-open')
            });
        },
        beforeDestroy() {
            document.removeEventListener('inventory::fetch-filters', this.getFilters);
            document.removeEventListener('inventory::set-selected-filters', this.setSelectedFilters);
            document.removeEventListener('inventory::reset-filters', this.resetSelectedFilters)
        },
        methods: {
            // Update selected brands values
            updateSelectedBrands(index, value) {
                this.selected_brands = [];
                value.forEach((val) => {
                    this.selected_brands.push(val.value)
                });
            },

            // Update selected tags values
            updateSelectedTags(index, value) {
                this.selected_tags = [];
                value.forEach((val) => {
                    this.selected_tags.push(val.value)
                });
            },

            // update selected branches values
            updateSelectedBranches(index, value) {
                this.selected_branches = [];
                value.forEach((val) => {
                    this.selected_branches.push(val.value)
                });
            },

            // update selected categories values
            updateSelectedCategories(index, value) {
                this.selected_categories = [];
                value.forEach((val) => {
                    this.selected_categories.push(val.value)
                });
            },

            // Get all filters Data
            getFilters() {
              if(Object.keys(this.filters).length) {
                return;
              }

              Http.get('/products/inventory/filters', ({data}) => {
                this.filters = data.data.filters;

                // set selected filters
                Salla.event.createAndDispatch('inventory::set-selected-filters', {
                  'selected_filters': this.selected_filters
                });
              }, ({error}) => {
                laravel.errors.renderValidation(error.data, ' ');
              }, this.selected_filters);
            },

            // prepare all request data before submit filters
            submitFilters() {
                // this.selected_types = _.filter(this.filters['types'].options, o => o.selected === true).map(v => v.value);
                // this.selected_status = _.filter(this.filters['status'].options, o => o.selected === true).map(v => v.value);
                const keyword = new URL(window.location).searchParams.get('keyword')

                Salla.event.createAndDispatch('inventory::submit-filters', {
                    'tags': this.selected_tags,
                    'brands': this.selected_brands,
                    'branches': this.selected_branches,
                    'categories': this.selected_categories,
                    ...(keyword ? { keyword } : {}),
                })
            },
            // Reset filters values
            resetFilters() {
              // to reset selected filters in every list or cartegroies components
              Salla.event.createAndDispatch('inventory::reset-filters')

              // to reset local data in this component
              this.resetSelectedFilters();
            },

            // remove "all" selected value
            // if the user select any filter option
            checkForAll(value, index) {
                if (value.srcElement.id === 'StatusFilter_0' && value.target.checked) {
                    this.filters.status.options.forEach(ele => {
                        ele.selected = false
                    });
                    this.filters.status.options[0].selected = true
                } else if ((value.srcElement.id !== `StatusFilter_0` && value.srcElement.id !== `ProductTypeFilter_${index}`) && value.target.checked) {
                    this.filters.status.options[0].selected = false
                } else if (value.srcElement.id === 'ProductTypeFilter_0' && value.target.checked) {
                    this.filters.types.options.forEach(ele => {
                        ele.selected = false
                    });
                    this.filters.types.options[0].selected = true
                } else if ((value.srcElement.id !== `ProductTypeFilter_0` && value.srcElement.id !== `StatusFilter_${index}`) && value.target.checked) {
                    this.filters.types.options[0].selected = false
                }
            },
            setSelectedFilters(e) {
                let selected_filters = e.detail.selected_filters || [];

                // if (e.detail.selected_filters) {
                if (Object.keys(selected_filters).length) {
                    this.selected_filters = selected_filters || [];
                    this.selected_tags = selected_filters.tags || [];
                    this.selected_brands = selected_filters.brands || [];
                    this.selected_branches = selected_filters.branches || [];
                    this.selected_categories = selected_filters.categories || [];
                }
            },

            resetSelectedFilters() {
                this.selected_filters = this.selected_branches = this.selected_brands = this.selected_tags = this.selected_categories = [];
            },

            // export products or quantities
            exportFilterResults(exportUrlParam) {
              showLoading();
                let exportUrl = (exportUrlParam) ? exportUrlParam : '/products/inventory/print?type=csv';

                let data = {
                    'type': 'xlsx',
                    'filtering': 1,
                    'tags': this.selected_tags,
                    'brands': this.selected_brands,
                    'branches': this.selected_branches,
                    'categories': this.selected_categories
                };

                $.ajax({
                    url: exportUrl,
                    dataType: 'json',
                    data: data,
                    async: true,
                    success: (resp) => {
                        laravel.ajax.successHandler(resp);
                    },
                    error: (error) => {
                        laravel.ajax.errorHandler(error);
                    },
                    complete: function () {
                        hideLoading();
                    }
                });

            }
        }
    }
</script>
