<template>
  <div>
    <form>
      <div class="grid single-product" id="products_div">
        <product :languages="languages" :mahlyData="mahlyData"
                 :store-document-status="storeDocumentStatus"
                 :from-salla-app="fromSallaApp"
                 :permissions="permissions"
                 :features="features"
                 :quantity-editable-types="quantityEditableTypes"
                 :store-have-mobile-app="storeHaveMobileApp"
                 :plan="plan"
                 :store-currency="storeCurrency"/>
      </div>
    </form>

    <digital-card-options></digital-card-options>
  </div>
</template>

<script>
import Product from './components/Product'
import initialData from '../../services/ProductService'
import DigitalCardOptions from "../digital-card-management/App.vue";

export default {
  data() {
    return {
      languages: [],
      features: '',
      quantityEditableTypes: '',
      fromSallaApp: '',
      storeDocumentStatus: '',
      plan: '',
      storeCurrency: '',
      storeHaveMobileApp: '',
    }
  },
  components: {
    'product': Product,
    'digital-card-options': DigitalCardOptions,
  },
  created() {
    // todo:: enhocnce it wit
    let defaultLang = {
      "id": 1,
      "name": "العربية",
      "rtl": 1,
      "iso_code": "ar",
      "country_code": "sa",
      "flag": "https://assets.salla.sa/images/flags/ar.svg",
      "status": true,
      "auto_translate": false,
      "order": 0
    };
    this.languages = initialData.getDatum('languages', {
      supported: [defaultLang],
      current: defaultLang,
      default: defaultLang,
      iso_code: 'ar',
      feature: false,
    })
    this.mahlyData = initialData.getDatum('mahly_data', {})

    this.features = initialData.getDatum('features', [])
    this.permissions = initialData.getDatum('permissions', [])
    this.quantityEditableTypes = initialData.getDatum(
        'quantityEditableTypes',
        []
    )
    this.fromSallaApp = initialData.getDatum('fromSallaApp', false)
    this.storeDocumentStatus = initialData.getDatum('storeDocumentStatus')
    this.plan = initialData.getDatum('plan')
    this.storeCurrency = initialData.getDatum('storeCurrency', 'ر.س')
    this.storeHaveMobileApp = initialData.getDatum('storeHaveMobileApp')
  },
}
</script>
