<template>
    <div>
        <product
                :productClass="'product-box'"
                :product="product"
                :productTypes="productTypes"
                :categories="categories"
                :baseStoreUrl="baseStoreUrl"
                :features="features"
                :products="products"
                :permissions="permissions"
                :quantityEditableTypes="quantity_editable_types"
                :validProductTypes="validProductTypes"
                :languages="languages"
                :from-salla-app="fromSallaApp"
                :mahlyData="mahlyData"
        />
        <options-and-quantity></options-and-quantity>
        <notify-quantity-settings></notify-quantity-settings>
        <!-- Add Category From Product -->
        <add-category :categories="categories"></add-category>
    </div>
</template>

<script>
    import AddCategory from "../../products-view/components/addCategory";
    import Product from '../../products-view/components/Product'
    import initialData from "../../../services/ProductService";
    import NotifyQuantitySettings from '../../notify-quantity-settings/App';
    import OptionsAndQuantity from '../../quantity-management/App';

    export default {
      props     : [
        'languages',
      ],
        data() {
            return {
                product: null,
                productTypes: null,
                products:[],
                categories: [],
                baseStoreUrl: baseStoreUrl,
                permissions:[],
                features:[],
                quantity_editable_types:[],
                validProductTypes: [],
                fromSallaApp:false
            }
        },
        components: {
            'product': Product,
            'options-and-quantity' : OptionsAndQuantity,
            'notify-quantity-settings' : NotifyQuantitySettings,
            'add-category' : AddCategory
        },
        mounted() {
            Salla.event.addEventListener('products::reload-categories-list', this.reloadCategories);

            // update total product quantity after changed from options and quantity modal
            Salla.event.addEventListener('product-details::update-quantity', (data) => {
               this.product.quantity = data.detail.quantity;
            });

            Salla.event.addEventListener('product-details::update-status', (data) => {
              this.product.status = data.detail.status;
              this.product.show_in_web = data.detail.show_in_web;
              this.product.show_in_app = data.detail.show_in_app;
            });

            this.initialClipboard();
        },
        beforeDestroy() {
            document.removeEventListener('product-details::update-quantity');
            document.removeEventListener('products::reload-categories-list', this.reloadCategories);
            document.removeEventListener('product-details::update-status');
        },

        created() {
            this.product = initialData.getDatum('product', []);
            this.products = initialData.getDatum('products', []);
            this.quantity_editable_types = initialData.getDatum('quantityEditableTypes', []);
            this.validProductTypes = initialData.getDatum('validProductTypes', []);
            this.productTypes = initialData.getDatum('productTypes', []);
            this.closeToOut = initialData.getDatum('closeToOut', 0);
            this.features = initialData.getDatum('features', []);
            this.categories = initialData.getDatum('categories', []);
            this.permissions = initialData.getDatum('permissions', []);
            this.fromSallaApp = initialData.getDatum('fromSallaApp', false);
            this.mahlyData = initialData.getDatum('mahly_data', {});
        },

        methods: {
            /**
             * reload categories lists after a new category is added,
             */
            reloadCategories(e) {
                this.categories = e.detail.categories;
            },
            initialClipboard() {
                let clipboard = new Clipboard('a.copy-product-link');
                clipboard.on('success', function (e) {
                    e.clearSelection();
                    laravel.alert('تم نسخ رابط المنتج', 'success')
                });
            },
        }
    }
</script>
