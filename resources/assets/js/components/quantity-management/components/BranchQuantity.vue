<template>
    <div>

        <!------- This will be load if the product managed by branches -------->
        <div class="branches-quantity"  v-if="!has_options && branches.length">
            <div class="row">
                <!------------ Unlimited quantity Check -------->
                <div class="col-xs-6">
                    <div class="rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg">
                        <input v-model="product.unlimited_quantity" type="checkbox" name="checked"
                               id="quantity_limit"/>
                        <label for="quantity_limit">الكمية غير محدودة</label>
                    </div>
                </div>

                <!------------ Product Total Quantity -------->
                <div class="col-xs-6 align-left" v-show="!product.unlimited_quantity">
                    <h5 v-model="product.quantity" class="mb-10 mt-0 font-15">إجمالي الكمية
                        {{ total_quantity }}</h5>
                </div>
            </div>

            <h6 v-if="errors && errors.total_quantity" class="mt-0 mb-10 font-14 text-danger">{{errors.total_quantity[0]}}</h6>

            <!-----------  Product Quantity Per Branches Table ----->
            <table class="table table--border table--tiffany-head branches-management mb-20">
                <thead class="">
                <tr>
                    <td>الفرع</td>
                    <td class="text-center">الكمية</td>
                </tr>
                </thead>
                <tbody :class="{'disabled': product.unlimited_quantity}">
                <tr v-for="(branch,index) in branches" :key="index">
                    <td><span>{{branch.name}}</span></td>
                    <td>
                        <div class="btn-group qty-field">
                            <button type="button" @click="qtyChangeValue('add',index)" class="btn rec-btn--trans">
                                <i class="sicon-add"></i>
                            </button>
                            <div class="form-group">
                                <input type="number" class="form-control product-quantity" min="0"
                                    @keyup="changeQuantity($event, branch)"
                                    :value="branch.quantity">
                            </div>
                            <button @click="qtyChangeValue('sub',index)" type="button" class="btn rec-btn--trans">
                                <i class="sicon-minus"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table> <!---------- End Quantities Per Branches Table -------->
        </div> <!------------- End Branch quantity Div ------------------->

        <!------------- Enable or disable Product Options If the product is Advancable" --------->
        <div class="row" v-if="product.advancable">
          <div class="col-xs-12">
            <section class="alert-box alert-box--info mb-20">
              <i class="sicon-info"></i>
              <article v-if="branches.length">
                <p>بامكانك ادارة كمية المنتج المتوفرة في الفروع المختلفة للمتجر من خلال تحديد الكمية لكل
                  فرع. وفي حال وجود قيم مختلفة للمنتج يمكنك اضافتها وتحديد الكمية المتوفرة لنفس المنتج</p>
              </article>
              <article v-if="!branches.length">
                <p>بإمكانك إدارة الكمية بناء على خيارات المنتج</p>
              </article>
            </section>
            <label id="options_toggle" class="mt-0 mb-15" for="has_options">
                  <input type="checkbox" name="active_options" v-model="has_options" :class="{'switchery': true}" id="has_options"
                          :checked="has_options?'checked':0">
                  تفعيل خيارات المنتج
            </label>
          </div>
        </div>
        <product-quantity v-if="product.advancable"
                          :branches="branches"
                          :options="options"
                          :has_options="has_options"
                          :product="product"
                          :default_quantities="default_quantities"
                          :skus="skus"
                          v-show="has_options"
                          :deleteProductOption="deleteProductOption"
                          :deleteValue="deleteValue"
                          :errors="errors"
                          :exceed_options_limit="exceed_options_limit"
                          :exceed_variants_limit="exceed_variants_limit"
                          :languages="languages"
                          @updateUnlimited="updateUnlimited"
                          @changeDefaultOptionHandler="changeDefaultOptionHandler"/>
    </div> <!----- End Product Options ------------>

</template>
<script>

    import ProductQuantity from './ProductQuantity';
    import Http from "../../../utils/http";
    import initialData from "../../../services/ProductService";
    import FormError from "../../products-view/components/FormError";

    export default {
        props: ['showModal'],
        components: {
            'product-quantity': ProductQuantity,
            'form-error' : FormError
        },
        data: () => ({
            errors: null,
            // Default options
            options: [{
                id: '98754fgbvhjm',
                name: null,
                display_type: 'text', // default feature type
                is_new: true,
                values: [
                    {
                        id: 'hgffddkj765',
                        name: null,
                        display_value: null,
                        is_new: true,
                        option_id: '98754fgbvhjm',
                        is_default: false
                    }
                ]
            }],
            // Default product skus
            skus: [{
                id: null,
                name: null,
                sale_price: null,
                regular_price: null,
                stock_quantity: 0,
                barcode: null,
                weight: null,
                price: null,
                sku:  null,
                mpn:  null,
                gtin: null,
                notify_quantity: null,
                quantities: [],
                is_default: false,
                values: [ // related with option details
                    {
                        id: 'hgffddkj765',
                        name: null,
                        display_value: null,
                        is_new: true,
                        option_id: '98754fgbvhjm',
                        image_url : null,
                        hashed_display_value: null,
                        is_default: false
                    }
                ]
            }],
            branches: [],
            has_options: false,
            product: null,
            unlimited_quantity: false,
            total_quantity: null,
            default_quantities: [],
            exceed_options_limit : false,
            exceed_variants_limit : false,
            languages: {},
        }),
        created() {
            this.fetchData();
        },
        mounted() {
            Salla.event.addEventListener('quantity-management::fetch-data', this.fetchData);
            Salla.event.addEventListener('quantity-management::submit', this.save);

            if(this.product.advancable) {
                const ele = document.querySelector('.switchery');
                new Switchery(ele, {color: '#57d4c4', size: 'small'});
            }

            // Simple JQuery Animation
            $('#has_options').change(function () {
                if (this.checked) {
                    this.has_options = 1;
                    $('.branches-quantity').slideUp()
                    $('.rec-options-list').slideDown()
                } else {
                    this.has_options = 0;
                    $('.branches-quantity').slideDown()
                    $('.rec-options-list').slideUp()
                }
            });
        },
        beforeDestroy() {
            document.removeEventListener('quantity-management::fetch-data', this.fetchData);
            document.removeEventListener('quantity-management::submit', this.save);
        },
        methods: {
          //Change default option value
          changeDefaultOptionHandler (value, index) {
            this.options[index].values.forEach(e => e.id !== value.id ? e.is_default = false : null);
            value.is_default = !value.is_default;
          },

            // Changing Branch Product Quantity on Keyup event
            changeQuantity(e, branch) {
                let quantity = parseInt(e.target.value);
                branch.quantity = quantity;
                this.setTotalQuantity()
            },

            // Switch Case To ADD Or Subtract For Product Quantity
            qtyChangeValue(type, index) {
                switch (type) {
                    case('add'):
                        this.branches[index]['quantity'] += 1;
                        this.setTotalQuantity();
                        break;
                    case ('sub'):

                        if (this.branches[index]['quantity'] <= 0) {
                            return null;
                        }

                        this.branches[index]['quantity'] -= 1;
                        this.setTotalQuantity();
                        break;
                    default:
                        return null;
                }
            },

            // Set Total Product Branches Quantity
            setTotalQuantity() {
                this.total_quantity = _.sum(_.map(this.branches, 'quantity')) || 0;
            },

            // Submit Changes
            save(event) {
                if ($('#accordion').find('.has-error').length) {
                    this.showErr = true;
                    setTimeout( () => {
                        this.showErr = false;
                    }, 800)
                } else {
                    this.showErr = false;
                    showLoading(event.detail);
                    Http.put('/products/quantity/management/' + this.product.id, this.getPayload(), ({data}) => {
                        hideLoading();
                        // close the options and quantities modal
                        this.$emit('closeModal');

                        // Update product quantity
                        Salla.event.createAndDispatch('product-details::update-quantity', {
                            'can_change_quantity' : data.can_change_quantity,
                            'productId': this.product.id,
                            'quantity': data.quantity,
                            'unlimited_quantity': this.has_options ? this.unlimited_quantity : this.product.unlimited_quantity
                        });

                        // Reset Errors 
                        this.errors = null;
                        laravel.ajax.successHandler(data)
                    }, ({response}) => {
                        hideLoading();
                        const errors = response.data.error.fields
                        this.errors = errors;

                        if (errors.skus) {
                          swal({
                              text: errors.skus,
                              type: 'error',
                              showConfirmButton: false,
                              timer: 3000
                          });
                        }

                      Object.keys(errors).forEach(key => {
                          var error = errors[key];
                          if(error) {
                            swal({
                              text: error,
                              type: 'error',
                              showConfirmButton: false,
                              timer: 3000
                            });
                          }
                         return false;
                      })

                    });               
              }
            },
            // Delete Product Group Depending On The Product Group Index
            deleteProductOption(index, option) {
                if (this.options.length > 1) {
                    const groups = Object.assign([], this.options);
                    groups.splice(index, 1);

                    this.options = groups;
                }

                this.exceed_options_limit = this.options.length >= this.settings['options_limit'];
                this.rebuildSkus(option);
            },
            // Delete sku
            deleteSkus(detail) {
                this.skus = _.reject(this.skus, (sku) => {
                    return sku.values.some(item => (item.id != null && item.id === detail.id));
                });

                this.refreshSkusTotalQuantity();
            },
            // Rebuild All skus to make them unique
            rebuildSkus(option) {
                let values = [];
                // Delete specific option values
                // from all skus
                _.each(this.skus, function (sku) {
                    _.remove(sku.values, function (value) {
                        return value.option_id === option.id;
                    });
                });

                // Delete duplicate skus
                this.skus = _.reject(this.skus, (sku) => {
                    let sku_values = _.map(sku.values, 'id');

                    let includes = values.some(a => sku_values.every(
                        (v, i) => v === a[i])
                    );
                    if (!includes) {
                        values.push(sku_values)
                    }
                    return includes;
                });

                // we need to refresh total skus quantity
                // after rebuild all skus
                this.refreshSkusTotalQuantity();
            },
            // Delete Value from option Depending On The value Index And The Parent Index
            deleteValue(parentIndex, index, value) {
                if (this.options[parentIndex].values.length > 1) {
                    const option = Object.assign([], this.options[parentIndex].values);
                    option.splice(index, 1);

                    this.options[parentIndex].values = option;
                    this.deleteSkus(value);
                }

                this.validSkusCount()
            },
            // Prepare Submit Payload
            getPayload() {
                return {
                    'has_options': this.has_options,
                    'skus': this.skus,
                    'options': this.options,
                    'branches': this.branches,
                    'unlimited_quantity': this.has_options ? this.unlimited_quantity : this.product.unlimited_quantity,
                    'total_quantity': this.has_options ? this.product.quantity : this.total_quantity
                }
            },

            // Fetch Modal Data
            fetchData() {
                let all_data = initialData.getDatum('QuantityAndOptionsData');
                this.product = all_data.product;
                this.branches = all_data.branches || [];
                this.default_quantities = all_data.default_quantities;
                let options = all_data.options;
                if (options.length) {
                    this.options = options;
                    this.has_options = true;
                }
                let skus = all_data.product_skus;

                if (skus.length) {
                    this.skus = skus
                } else {
                    this.skus[0].quantities = JSON.parse(JSON.stringify(this.default_quantities));
                }
                this.product.quantity = this.product.quantity ? this.product.quantity : this.setTotalQuantity();
                this.total_quantity = this.product.quantity;
                this.unlimited_quantity = this.product.unlimited_quantity;
                this.settings = all_data.settings;

                let defaultLang = {
                    "id"            : 1,
                    "name"          : "العربية",
                    "rtl"           : 1,
                    "iso_code"      : "ar",
                    "country_code"  : "sa",
                    "flag"          : "https://assets.salla.sa/images/flags/ar.svg",
                    "status"        : true,
                    "auto_translate": false,
                    "order"         : 0
                };
                this.languages = all_data.languages || {
                    "supported": [defaultLang],
                    "current"  : defaultLang,
                    "default"  : defaultLang,
                    "iso_code" : "ar",
                    "feature"  : false
                };
            },

            // Refresh Sku Total
            refreshSkusTotalQuantity() {
                this.product.quantity = _.sum(_.map(this.skus, 'stock_quantity')) || 0
            },

            // Update Unlimited quantity
            updateUnlimited(value) {
                this.unlimited_quantity = value;
            },

            // check skus/variants limit
            validSkusCount() {
                let combinations_count = 1;
                this.options.forEach((option) => {
                    combinations_count = combinations_count * option.values.length;
                });

                this.exceed_variants_limit = this.settings['variants_limit'] && combinations_count > this.settings['variants_limit'];
            }
        }
    }
</script>
