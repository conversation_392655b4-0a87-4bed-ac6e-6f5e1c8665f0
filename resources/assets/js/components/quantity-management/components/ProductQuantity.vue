<template>
    <div class="rec-options-list" :style="{'display': show}">
        <!-- Product Options Group Layout ---------->
        <div class="option-section" v-for="(option, optIndex) in options" :key="optIndex">
            <div class="option-section-inner">
                <div class="row">
                    <div class="col col-sm-6 col-xs-12">
                <!------------ Option Name --------------->
                    <lingual-field name="option_name"
                                   :eventsLoad="{option:option, optIndex:optIndex}"
                                   :translations="option.translations"
                                   :outer-attrs="{class:{'has-error':  errors && errors['options.'+optIndex+'.name'] }}"
                                   icon="sicon-type-square"
                                   :input-attrs="{class:'option_group_name product_price_  option_group_cls'}"
                                   placeholder="مسمى الخيار (مثل اللون، القياس)"
                                   :languages="languages"
                                   @value-updated="optionUpdated"
                    >
                        <form-error v-if="errors && errors['options.'+optIndex+'.name']" :errors="errors">{{ errors['options.'+optIndex+'.name'].toString()}}</form-error>
                    </lingual-field>
                </div>
                <div class="col col-sm-6 col-xs-12">
                    <div class="form-group">
                        <div class="input-group">
                            <span class="input-group-addon input-group-addon-small"><i
                                    class="sicon-file-partial"></i></span>
                            <select v-model="option.display_type" class="bootstrap-select option_group_feature_type"
                                    data-width="100%" name="option_group" @change="onChangeFeature(optIndex,option)">
                                <option v-for="(feature, key) in features" :key="key" :value="key">
                                    {{feature}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
              </div>
              <button v-if="options.length > 1" @click="deleteProductOption(optIndex,option)"  class="btn btn-delete-item btn-delete-option" type="button">
                <i class="icon sicon-trash-2"></i>
              </button>
            </div>

            <!---------- Option Details (Values) Layout ---->
            <div class="option-options" v-for="(value, index) in option.values" :key="index">
                <div class="value-container">
                        <lingual-field :eventsLoad="{value:value, optIndex:optIndex}"
                                       name="option_details_name"
                                       :translations="value.translations"
                                       :outer-attrs="{class:{'has-error':  errors && (errors['options.'+optIndex+'.'+'values.'+index+'.name'] || errors['options.'+optIndex+'.'+'values.'+index+'.display_value']),
                                                             'has-default-option': product.type === 'product' && !isBasicPlan && features_list.default_product_option_value}}"
                                       :inner-attrs="{class:'input-group type-'+option.display_type}"
                                       icon="sicon-type-square"
                                       :input-attrs="{class:'option_details_name product_price option_values_cls'}"
                                       :placeholder=" 'القيمة ' +' '+ (index + 1)"
                                       :languages="languages"
                                       :drop-down-attrs="{style:dropDownStylesForOption(option)}"
                                       @value-updated="optionDetailUpdated"
                        >
                            <template #before-input v-if="product.type === 'product' && !isBasicPlan && features_list.default_product_option_value">
                              <!------- Select Option Default Value -------->
                              <div class="tooltip-toggle top right">
                                <span class="default-option-selector">
                                  <button type="button" class="btn" :class="{'selected': value.is_default}" @click="changeDefaultOption(value, optIndex)">
                                    <i class="sicon-check"></i>
                                  </button>
                                </span>
                                <div v-if="value.is_default" class="tooltip-content width-auto">
                                  <p class="font-12 nowrap">الخيار الافتراضي</p>
                                </div>
                              </div>
                            </template>
                            <!-------  Value Feature Type (Text , Image or color )-------->
                            <template v-slot:after-input>
                                <!-------  Value Feature Type (Text , Image or color )-------->
                                <span class="input-group-addon feature-group-addon input-group-addon-small" v-if="option.display_type !== 'text'">
                                  <features
                                          :value="value"
                                          :feature="option.display_type"
                                          :errors="errors"
                                          :prodId="product.id"
                                          :index="index"
                                          :parentIndex="optIndex"
                                          :options="options"
                                  />
                                </span>
                            </template>
                            <!------- Validation Messages ----------->
                            <form-error v-if="errors && errors['options.'+optIndex+'.'+'values.'+index+'.name']" :errors="errors">
                                {{ errors['options.'+optIndex+'.'+'values.'+index+'.name'].toString()}}
                            </form-error>
                            <form-error v-if="errors && errors['options.'+optIndex+'.'+'values.'+index+'.display_value']" :errors="errors">
                                {{ errors['options.'+optIndex+'.'+'values.'+index+'.display_value'].toString()}}
                            </form-error>
                            <form-error v-if="errors && errors['options.'+optIndex+'.'+'values.'+index+'.image_url']" :errors="errors">
                              {{ errors['options.'+optIndex+'.'+'values.'+index+'.image_url'].toString()}}
                            </form-error>
                        </lingual-field>

                    <button type="button" v-if="option.values.length > 1" @click="deleteValue(optIndex, index,value)" class="btn btn-delete-item btn-delete-value"><i class="icon sicon-trash-2"></i></button>
                </div>
            </div> <!-------------- End Option Details (Values) Layout ----------->

            <!--------- Add New Option Detail ( Value) -------------------->
            <button @click="addOptionValue(optIndex,option)" type="button" class="btn btn-add-value">
              <i class="sicon-add"></i> إضافة قيمة جديدة
            </button>

        </div>  <!--------- End Product Option Group ------------->


        <!--------- Add New Option ----------------------->
        <button @click="addProductOption" type="button" class="btn btn-add-option-group border-slate text-slate-800 btn-flat btn-full" style="margin: 0 0 15px;">
          <i class="sicon-add"></i> إضافة خيار جديد
        </button>

        <section id="exceed_options_limit" class="alert-box alert-box--danger mb-20 mt-20" v-show="exceed_options_limit">
            <i class="sicon-info"></i>
            <article>
                <p>{{messages.options_limit}} </p>
            </article>
        </section>

        <section id="exceed_variants_limit" class="alert-box alert-box--danger mb-20 mt-20" v-show="exceed_variants_limit">
            <i class="sicon-info"></i>
            <article>
                <p>{{messages.variants_limit}}</p>
            </article>
        </section>
        <!-- Message If The Client Didn't Add Any Product Options -->
        <div class="align-center empty-placeholder" v-show="options[0].name === ''">
            <i class="sicon-inbox-multi"></i>
            <h4>قم باضافة خيارات للمنتج لتظهر لك الخيارات المتاحة وتحديد الكميات المتوفرة في الفروع والمستودعات</h4>
        </div>

      <!-- Alert for import product has skus, which will change quantities Layout  --------->
      <section class="alert-box alert-box--info mb-20 mt-20" v-if="showAlertMessage ||updated_by_import">
        <i class="sicon-info"></i>
        <article v-if="updated_by_import">
          <p>تم استيراد المنتج وإعادة إنشاء خياراته، الرجاء مراجعة البيانات.</p>
        </article>
          <article v-if="showAlertMessage">
              <p>تم إعادة توليد احتمالات المنتج ، الرجاء مراجعة البيانات وحفظها.</p>
          </article>
      </section>


        <!-- Product Skus (options combinations ) Layout  --------->

        <div v-show="showSkus()" class="row">
            <hr/>
            <!-- Unlimited Quantity Check  --------->
            <div class="col-xs-6" >
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg">
                    <input v-model="unlimited_quantity" @change="updateUnlimited" type="checkbox" name="checked" id="quantity_limit_two"/>
                    <label for="quantity_limit_two">الكمية غير محدودة</label>
                </div>
            </div>
            <!-- Product Skus Total Quantity --------->
            <div class="col-xs-6 align-left" v-show="!unlimited_quantity">
                <h5 class="mb-10 mt-0 font-15">إجمالي الكمية {{product.quantity}}</h5>
            </div>

            <div class="col-xs-12">
                <h6 v-if="errors && errors.total_quantity" class="mt-0 mb-10 font-14 text-danger">{{errors.total_quantity[0]}}</h6>
            </div>

            <!-- ?Product Skus Layout ------------>
            <div class="col-xs-12 skus-conatainer">
                <div class="panel-group rec-accordion" id="accordion" role="tablist" aria-multiselectable="true">
                    <div class="panel panel-default" v-for="(sku, skuIndex) in skus" :key="skuIndex">

                        <!---------- Sku Panel Head ( Name & Total Stock quantity ) ---------->
                        <div class="panel-heading rec-accordion__heading collapsed" aria-expanded="false" role="tab"
                             :id="`headingOne${skuIndex}`" data-toggle="collapse" data-parent="#accordion"
                             :href="`#collapse${skuIndex}`" :aria-controls="`collapse${skuIndex}`">
                            <div>
                                <h4>
                                    {{ getSkuName(sku) }}
                                </h4>
                            </div>
                            <span> متوفر عدد
                                {{sku.stock_quantity}}
                            </span>
                        </div>
                        <!---------- Sku Panel Body ------------------------>
                        <div :id="`collapse${skuIndex}`" class="panel-collapse rec-accordion__collapse collapse"
                             role="tabpanel"
                             :aria-labelledby="`headingOne${skuIndex}`">
                            <div :class="{'panel-body': true, 'rec-accordion__body': true}">
                                <div v-if="product.products_price_edit" class="row">
                                    <!---------- Sku Price ------------------>
                                    <div class="col-xs-12">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-dollar-coin-stack"></i></span>
                                                <input type="text" class="form-control form-control--small _parseArabicNumbers"  :id="`price_${sku.id}`" placeholder="السعر" v-model.lazy="sku.price">
                                                <span class="input-group-addon">ر.س</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!---------- Sku Cost Price ------------------>
                                    <div class="col-md-6 col-xs-12">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-dollar-coin-stack"></i></span>
                                                <input type="text" placeholder="سعر التكلفة"
                                                       class="form-control form-control--small _parseArabicNumbers"
                                                       v-model.lazy="sku.regular_price"
                                                       :id="`cost_${sku.id}`">
                                                <span class="input-group-addon">ر.س</span>
                                            </div>
                                        </div>
                                    </div>
                                  <!---------- Sku Sale Price ------------------>
                                  <div class="col-md-6 col-xs-12">
                                    <div class="form-group">
                                      <div class="input-group">
                                        <span class="input-group-addon"><i class="sicon-dollar-coin-stack"></i></span>
                                        <input type="text"
                                               placeholder="السعر المخفض"
                                               class="form-control form-control--small _parseArabicNumbers"
                                               v-model.lazy="sku.sale_price"
                                               :id="`discount_${sku.id}`">
                                        <span class="input-group-addon">ر.س</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="row">
                                    <!---------- Real Weight -------------------->
                                    <div :class="`${features_list.notify_quantity ? 'col-md-3 col-xs-6' : 'col-md-4 col-xs-12'}`">
                                        <div class="form-group" :class="{'has-error': errors && errors['skus.'+skuIndex+'.weight'] }">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-luggage-cart"></i></span>
                                                <input type="text"
                                                       placeholder="الوزن"
                                                       class="form-control form-control--small"
                                                       v-model="sku.weight" />
                                                <span class="input-group-addon">
                                                    {{ product.weight_type_label }}
                                                </span>
                                            </div>
                                            <form-error v-if="errors && errors['skus.'+skuIndex+'.weight']" :errors="errors">{{ errors['skus.'+skuIndex+'.weight'].toString()}}</form-error>
                                        </div>
                                    </div>

                                    <!---------- Sku Barcode -------------------->
                                    <div :class="`${features_list.notify_quantity ? 'col-md-3 col-xs-6' : 'col-md-4 col-xs-12'}`">
                                        <div class="form-group" :class="{'has-error': errors && errors['skus.'+skuIndex+'.barcode'] }">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-barcode"></i></span>
                                                <input v-model="sku.barcode" type="text" placeholder="الباركود"
                                                       class="form-control form-control--small" >
                                            </div>
                                            <form-error v-if="errors && errors['skus.'+skuIndex+'.barcode']" :errors="errors">{{ errors['skus.'+skuIndex+'.barcode'].toString()}}</form-error>
                                        </div>
                                    </div>

                                    <!---------- Sku Code -------------------->
                                    <div :class="`${features_list.notify_quantity ? 'col-md-3 col-xs-6' : 'col-md-4 col-xs-12'}`">
                                        <div class="form-group" :class="{'has-error': errors && errors['skus.'+skuIndex+'.sku'] }">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-tag"></i></span>
                                                <input type="text" placeholder="SKU"
                                                       class="form-control form-control--small" v-model="sku.sku">
                                            </div>
                                            <form-error v-if="errors && errors['skus.'+skuIndex+'.sku']" :errors="errors">{{ errors['skus.'+skuIndex+'.sku'].toString()}}</form-error>
                                        </div>
                                    </div>

                                    <!---------- Sku Notify Quantity -------------------->
                                    <div class="col-md-3 col-xs-6"
                                         v-if="features_list.notify_quantity">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-bell"></i></span>
                                                <input type="text" v-model.lazy="sku.notify_quantity" placeholder="أقل كمية للتنبيه" :id="`low_quantity_${sku.id}`"
                                                       class="form-control form-control--small _parseArabicNumbers">
                                            </div>
                                        </div>
                                    </div>
                                    <!---------- MPN -------------------->
                                    <div v-if="showMpnAndGtin" class="col-xs-6">
                                        <div class="form-group" :class="{'has-error': errors && errors['skus.'+skuIndex+'.mpn'] }">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-barcode"></i></span>
                                                <input
                                                    type="text" placeholder="MPN"
                                                    v-model="sku.mpn" class="form-control form-control--small">
                                            </div>
                                            <form-error v-if="errors && errors['skus.'+skuIndex+'.mpn']" :errors="errors">{{ errors['skus.'+skuIndex+'.mpn'].toString()}}</form-error>
                                        </div>
                                    </div>
                                    <!---------- GTIN -------------------->
                                    <div v-if="showMpnAndGtin" class="col-xs-6">
                                        <div class="form-group" :class="{'has-error': errors && errors['skus.'+skuIndex+'.gtin'] }">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="sicon-barcode"></i></span>
                                                <input
                                                    type="text" placeholder="GTIN"
                                                    v-model="sku.gtin" class="form-control form-control--small">
                                            </div>
                                            <form-error v-if="errors && errors['skus.'+skuIndex+'.gtin']" :errors="errors">{{ errors['skus.'+skuIndex+'.gtin'].toString()}}</form-error>
                                        </div>
                                    </div>
                                  </div>
                                  <!--------------- Sku Stock Quantity ------------------>
                                  <h5 class="mt-15 mb-5">الكمية</h5>

                                  <!--------------- Sku Quantity will be per branches if the product is managed by branches ------------------>
                                  <ul class="rec-list rec-list--vertical rec-list-qty"
                                      :class="{'disabled': !!unlimited_quantity}">
                                      <li v-for="(branch, index) in sku.quantities" :key="index">
                                          <span>{{branch.name}}</span>
                                          <div class="btn-group qty-field">
                                              <button @click="qtyChangeValue('add',branch,sku)" type="button"
                                                      class="btn rec-btn--trans">
                                                  <i class="sicon-add"></i>
                                              </button>
                                              <div class="form-group mb-0">
                                                  <input type="number" class="form-control product-quantity"
                                                      @keyup="changeQuantity($event, branch,sku)" :value="branch.quantity" min="0">
                                              </div>
                                              <button @click="qtyChangeValue('sub',branch,sku)" type="button"
                                                      class="btn rec-btn--trans">
                                                  <i class="sicon-minus"></i>
                                              </button>
                                          </div>
                                      </li>
                                  </ul> <!----- End Sku Quantity --------->
                                </div>
                              </div>
                            </div> <!----- End Sku Panel Body -------->
                        </div>
                    </div> <!------ End Sku Panel  --------------->
                </div> <!----- End Skus Panel Group --------->
            </div>
        </div> <!---- End Product Skus Layout ------->
    </div>
</template>

<script>
    import initialData from '../../../services/ProductService';
    import Features from './Features';
    import FormError from "../../products-view/components/FormError";
    import vueDebounce from 'vue-debounce';
    import Vue from "vue";
    import LingualField from "@salla.sa/languages/components/LingualField";

    Vue.use(vueDebounce, {
        lock: true,
        listenTo: ['keyup'],
        defaultTime: '1000ms',
        fireOnEmpty: false
    });

    export default {
        props: ['show', 'branches', 'options', 'has_options', 'skus', 'product', 'total_quantity', 'default_quantities', 'deleteValue',
                'deleteProductOption', 'errors', 'showErr', 'exceed_options_limit', 'exceed_variants_limit', 'languages'],
        data() {
            return {
                features: [
                    {
                        text: 'نص',
                    },
                    {
                        color: 'color',
                    },
                    {
                        image: 'image',
                    },
                ],
                unlimited_quantity : false,
                settings: [],
                messages:[],
                features_list : [],
                showAlertMessage: false,
                showMpnAndGtin: false,
                isBasicPlan: false
            };
        }
        ,
        components: {
            'features': Features,
            'form-error' : FormError,
            'LingualField': LingualField,
        },
        created() {
            let all_data = initialData.getDatum('QuantityAndOptionsData');
            this.features = all_data.features;
            this.settings = all_data.settings;
            this.unlimited_quantity = all_data.product.unlimited_quantity;
            this.updated_by_import = all_data.product.updated_by_import;
            this.product.quantity = _.sum(_.map(this.skus, 'stock_quantity'))
            this.messages = all_data.messages;
            this.features_list =  all_data.features_list || [];
            this.showMpnAndGtin = this.shouldShowMpnAndGtin(this.product.type);
            this.checkCombinations()
            window.dataLayer[0].store.plan === 'basic' ? this.isBasicPlan = true : null;
        },
        mounted() {
            $(function () {
                $('.bootstrap-select').selectpicker();
                // Initialize Bootstrap Select
                $('.btn-add-option-group').click(function () {
                    $('.bootstrap-select').selectpicker();
                });
            });
        },
        methods: {

            // Changing Branch Product Quantity on Keyup event
            changeQuantity(e, branch,sku) {
                let quantity = parseInt(e.target.value);
                if(isNaN(quantity)){
                    quantity = 0;
                }
                branch.quantity = quantity;
                this.recalculateSkuTotal(sku)
            },

            // Format sku name based on values( option details) name
            getSkuName(sku) {
                return _.map(sku.values, 'name').join('/');
            },

            // Add New Product Option To The Options Group Array
            addProductOption() {
                 if(!this.validOptionsLimit()){
                     return;
                 }
                // Generate temp option detail id (value id)
                // to link new value with  sku when generate new sku
                let temp_id = Math.random().toString(36).substring(2);
                // Generate temp option id to link it with
                // all their child values (details)
                let temp_option_id = Math.random().toString(36).substring(2);
                // Generate New Option
                this.options.push(
                    {
                        id: temp_option_id,
                        name: null,
                        display_type: 'text',
                        is_new: true,
                        values: [
                            {
                                id: temp_id,
                                name: null,
                                display_value: null,
                                option_id: temp_option_id,
                                is_new: true,
                                is_default: false
                            },
                        ],
                    },
                );

                // Push New Values To Skus
                this.pushSkuValues(temp_id, temp_option_id);
                Salla.event.createAndDispatch('initiate-language-fields');
            },

            // Add New Option Details(value) To Specific Option
            addOptionValue(index, option) {

                if(!this.validSkusCount(option.id))
                {
                    $('#modal_quantity_management').animate({
                        scrollTop: $('#exceed_variants_limit').offset().top
                    });

                    return;
                }
                this.options[index].values.push(
                    {
                        id: null,
                        name: null,
                        display_value: null,
                        is_new: true,
                        option_id: option.id,
                        image_url:null,
                        hashed_display_value:null,
                        is_default: false
                    },
                );
                Salla.event.createAndDispatch('initiate-language-fields');
            },

            // Switch Case To ADD Or Subtract For Product Quantity
            qtyChangeValue(type, branch,sku) {
                switch (type) {
                    case('add'):
                    let num = parseInt(branch.quantity)
                        num += 1;
                        branch.quantity = num;
                        break;
                    case ('sub'):

                        if (branch.quantity < 1) {
                            return null;
                        }
                        branch.quantity -= 1;
                        break;
                    default:
                        return null;
                }
                this.recalculateSkuTotal(sku)

            },

            // Push New Values(option details) To Skus
            pushSkuValues(temp_id, temp_option_id) {
                this.skus.forEach((sku) => {
                    sku.values.push({
                        id: temp_id,
                        name: null,
                        option_id: temp_option_id,
                        is_new: true,
                        is_default: true
                    });
                });
            },

            // Update Product Skus
            updateSkus(detail, optIndex, isDefault) {
                 // when we create a new option value (detail)
                // we need to go through the whole process to generate skus & id of detail
                if (detail.id === null) {
                  return this.generateSkus(detail, optIndex);
                }


                // we need to update the all name of detail in all skus values
                this.skus.forEach((sku) => {
                    let result = _.find(sku.values, function (value) {
                      value.option_id === detail.option_id && isDefault ? value.is_default = false : null;
                      return (detail.id != null && value.id === detail.id);
                    });

                    if (result) {
                      result['name'] = detail.name;
                      result['is_default'] = isDefault;
                    }

                    const defaultOption = sku.values.every((ele) => ele.is_default === true);
                    defaultOption ? sku.is_default = true : sku.is_default = false;
                });
            },

            // Generate New Product Skus
            generateSkus(detail, optIndex) {
                // * Generate detail(value) temp id
                detail.id = Math.random().toString(36).substring(2);

                // * Remove the selected option from options array
                // make combinations later
                let options = this.options.filter(function (value, index) {
                    return index !== optIndex;
                });

                // * Extract the values(details) From Options
                let details = _.map(options, 'values'),
                    detailClone = {...detail}
                details.push([detailClone]);

                // * Generate Sku Combinations from Details(Values)
                let combinations = details.reduce(
                    (a, b) => a.reduce(
                        (r, v) => r.concat(
                            b.map(w => [].concat(v, w)),
                        ),
                        [],
                    ),
                );

                // * Create New Skus Based on Combinations
                this.createSkus(combinations);
            },
            createSkus(combinations){
                combinations.forEach((item) => {
                    this.skus.push({
                        id: null,
                        price: null,
                        name: null,
                        regular_price: null,
                        stock_quantity: 0,
                        sale_price: null,
                        barcode: null,
                        weight: null,
                        sku: null,
                        mpn: null,
                        gtin: null,
                        is_default: false,
                        values: Array.isArray(item) ? item : [item],
                        // we need deep copy to avoid js reference reflect
                        quantities : JSON.parse(JSON.stringify(this.default_quantities)),
                        notify_quantity: null,
                        translations:{}
                    });
                });
                // this.updateFirstSku();
            },
            // Refresh Unlimited Quantity Value
            updateUnlimited(){
                this.$emit('updateUnlimited', this.unlimited_quantity)
            },
            recalculateSkuTotal(sku){
                // Refresh sku stock quantity and product total quantity
                sku.stock_quantity = _.sum(_.map(sku.quantities, 'quantity')) || 0;
                this.product.quantity = _.sum(_.map(this.skus, 'stock_quantity')) || 0
            },
            dropDownStylesForOption(option){
                return {
                    color:'left:35px',
                    image:'left:85px',
                    text:''
                }[option.display_type];
            },

            changeDefaultOption(value, index) {
              this.$emit('changeDefaultOptionHandler', value, index);
              this.updateSkus(value, index, value.is_default);
            },

            optionDetailUpdated($event) {
                //update options
                $event.object.value.translations = $event.translations;
                $event.object.value.name=$event.translations[this.languages.iso_code]['option_details_name'];
                this.updateSkus($event.object.value, $event.object.optIndex, $event.object.value.is_default);
            },


            optionUpdated($event) {
                $event.object.option.name=$event.translations[this.languages.iso_code]['option_name'];
                $event.object.option.translations = $event.translations;
            },
            updateFirstSku()
            {
                let total_sku = _.sum(_.map(this.skus, 'stock_quantity'));
                let product_quantity = parseInt(this.product.quantity);
                if(total_sku === 0 && product_quantity && product_quantity !== total_sku){
                      this.skus[0].stock_quantity = product_quantity;
                      this.skus[0].quantities[0].quantity = product_quantity;
                }
            },
            validSkusCount(option_id) {
                let combinations_count = 1, values_count = 0;

                // calculate combinations/skus count
                this.options.forEach((option) => {
                    values_count = option.values.length;
                    if (option.id === option_id) {
                        values_count += 1;
                    }
                    combinations_count = combinations_count * values_count;
                });

                // check skus validation count
                this.exceed_variants_limit = this.settings['variants_limit'] && combinations_count > this.settings['variants_limit'];

                return ! this.exceed_variants_limit;
            },
            validOptionsLimit()
            {
                this.exceed_options_limit =  this.settings['options_limit'] && this.options.length >=  this.settings['options_limit'];
                return !this.exceed_options_limit;
            },
            // show skus values if the array has more than one sku
            // or if the skus has one value with not empty name
            showSkus()
            {
                return this.skus.length > 1 || (this.skus.length === 1 && this.skus[0]?.values[0]?.name)
            },
            checkCombinations()
            {
                let comb_count = 1;
                this.options.forEach((option) => {
                    comb_count = comb_count * option.values.length;
                });

                if(comb_count !== this.skus.length){
                    this.regenerateSkus();
                }
            },
            // Regenerate combinations if the variants count is not correct
            regenerateSkus()
            {
                let details = _.map(this.options, 'values');
                let combinations = details.reduce(
                    (a, b) => a.reduce(
                        (r, v) => r.concat(
                            b.map(w => [].concat(v, w)),
                        ),
                        [],
                    ),
                );

                this.skus.length = 0;
                this.createSkus(combinations);
                this.showAlertMessage = true;
            },
            onChangeFeature(index, option){
              this.options[index].values.forEach(function(part, index) {
                part.image_url = null;
                part.display_value = null;
                part.hashed_display_value = null;
              }, this.options[index].values);
            },
            shouldShowMpnAndGtin(type) {
                return !['service', 'food'].includes(type);
            }
        },
    };
</script>
