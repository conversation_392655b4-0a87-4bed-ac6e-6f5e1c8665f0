<template>
    <div class="option-type-wrapper">

        <!--Color Feature Type -->
        <template v-if="feature == 'color'">
            <verte picker="square" v-model="value.display_value" model="hex"></verte>
        </template>

        <!-- Image Feature Type -->
        <imager
          v-else-if="feature === 'image'"
          cropAspectRatio="null"
          v-model="value.image_url"
          :prodId="prodId"
          :imageUrl="value.image_url"
          :imageId="value.display_value"
          :imageIdHashed="value.hashed_display_value"
          @deleteUrl="deleteDetail"
          :values="value"/>
    </div>
</template>


<script>
  import Http from "../../../utils/http";
  import Verte from 'verte';
  import Imager from './layout/Imager';
  import 'verte/dist/verte.css';

  export default {
    props: ['feature', 'value', 'prodId', 'index', 'parentIndex', 'options'],

    components: {
      Verte,
      'imager': Imager
    },

    methods: {
        deleteDetail(){
            Http.delete(baseUrl + "/products/" + this.prodId + "/image/" + this.value.hashed_display_value , [], ({data}) => {
              this.resetValues();
            }, ({response}) => {
                laravel.errors.renderValidation(response.data, ' ');
            });
        },

        resetValues() {
          this.value.image_url = null;
          this.value.display_value = null;
          this.value.hashed_display_value = null;
        }
    },
  }
</script>

