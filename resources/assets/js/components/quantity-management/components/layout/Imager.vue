<template>
    <div>
        <div class="media pr-5">
            <img v-show="imageUrl != null" :src="imageUrl" @click="editImage">
            <button type="button" v-show="imageUrl != null" @click="$emit('deleteUrl')" class="btn btn-delete-circle ml-5"><i class="sicon-cancel"></i></button>
            <button v-show="imageUrl == null" type="button" @click="openInputFile" class="btn rec-btn--trans text-muted font-12 pr-5">
                استعراض...
            </button>
            <input type="file" name="option_image" @change="inputFile" style="display: none;">
        </div>

        <DokaModal
            :src="src"
            v-if="enabled"
            :sizeMin="{ width: width, height: height }"
            :crop-aspect-ratio="doka.cropAspectRatioOptions"
            :labelStatusAwaitingImage="doka.labelStatusAwaitingImage"
            :labelStatusLoadImageError="doka.labelStatusLoadImageError"
            :labelStatusLoadingImage="doka.labelStatusLoadingImage"
            :labelStatusProcessingImage="doka.labelStatusProcessingImage"
            :labelButtonCancel="doka.labelButtonCancel"
            :labelButtonConfirm="doka.labelButtonConfirm"
            :labelButtonCropZoom="doka.labelButtonCropZoom"
            :labelButtonCropRotateLeft="doka.labelButtonCropRotateLeft"
            :labelButtonCropRotateRight="doka.labelButtonCropRotateRight"
            :labelButtonCropRotateCenter="doka.labelButtonCropRotateCenter"
            :labelButtonCropFlipHorizontal="doka.labelButtonCropFlipHorizontal"
            :labelButtonCropFlipVertical="doka.labelButtonCropFlipVertical"
            :labelButtonCropAspectRatio="doka.labelButtonCropAspectRatio"
            :labelButtonUtilCrop="doka.labelButtonUtilCrop"
            :labelButtonUtilFilter="doka.labelButtonUtilFilter"
            :labelButtonUtilResize="doka.labelButtonUtilResize"
            :labelButtonUtilColor="doka.labelButtonUtilColor"
            :labelMarkupTypeRectangle="doka.labelMarkupTypeRectangle"
            :labelMarkupTypeEllipse="doka.labelMarkupTypeEllipse"
            :labelMarkupTypeText="doka.labelMarkupTypeText"
            :labelMarkupTypeLine="doka.labelMarkupTypeLine"
            :labelMarkupSelectFontSize="doka.labelMarkupSelectFontSize"
            :labelMarkupSelectFontFamily="doka.labelMarkupSelectFontFamily"
            :labelMarkupSelectLineDecoration="doka.labelMarkupSelectLineDecoration"
            :labelMarkupSelectLineStyle="doka.labelMarkupSelectLineStyle"
            :labelMarkupSelectShapeStyle="doka.labelMarkupSelectShapeStyle"
            :labelMarkupRemoveShape="doka.labelMarkupRemoveShape"
            :labelColorBrightness="doka.labelColorBrightness"
            :labelColorContrast="doka.labelColorContrast"
            :labelColorExposure="doka.labelColorExposure"
            :labelColorSaturation="doka.labelColorSaturation"
            :markupColorOptions="doka.markupColorOptions"
            :markupFontSizeOptions="doka.markupFontSizeOptions"
            :markupFontFamilyOptions="doka.markupFontFamilyOptions"
            :markupShapeStyleOptions="doka.markupShapeStyleOptions"
            :markupLineStyleOptions="doka.markupLineStyleOptions"
            :markupLineDecorationOptions="doka.markupLineDecorationOptions"
            :utils="doka.utils"
            @confirm="handleDokaConfirm"
            @cancel="handleDokaCancel"
            @loaderror="handleDokaLoadError"
            @close="enabled=false"/>
    </div>
</template>

<script>
    import $notify from '../../../../utils/notify';
    const options = require('../../../../utils/DokaOptions').default;

    export default {
        props: ['value', 'cropAspectRatio', 'imageUrl', 'prodId', 'values', 'imageId', 'imageIdHashed'],
        data() {
            return {
                width: '100',
                height: '100',
                src: '',
                result: null,
                enabled: false,
                uploaded: false,
                doka: options,
            };
        },

        computed: {
            getImageSrc() {
                if (this.result !== null) {
                    return this.toUrl(this.result);
                }
                if (this.value !== null) {
                    return this.value;
                }
                return this.src;
            },
        },
        components: {
            'DokaModal': require('../../plugins/doka/vue/esm/index').DokaModal
        },
        methods: {
            handleDokaConfirm(output){
                this.result = output.file;
                const formData = new FormData();
                const { v1: uuidv1 } = require('uuid');
                formData.append('Content-Type', this.result.type);
                formData.append('option_image', this.result, uuidv1() + '.' + this.result.name.split('.').pop());
                let self = this;

                showLoading();

                axios({
                    method: 'POST',
                    url: `${baseUrl}/products/${this.prodId}/option/image`,
                    data: formData,
                    config: { headers: {'Content-Type': 'multipart/form-data' }}
                }).then(response => {
                    self.$emit('input', response.data.data.image.url);
                    self.values.display_value = response.data.data.image.id;
                    self.values.hashed_display_value = response.data.data.image.code_id;

                    hideLoading();
                }).catch(({ response }) => {
                    const message = $(response.data).find('Message').text() || response.data?.error?.fields['option_image'][0] || ''
                    $notify('فشل الرفع، الرجاء المحاولة مرة أخرى.', message);
                    self.result = null;
                    self.setDefaultImage();
                    hideLoading();
                });

            },
            handleDokaCancel(){
                this.enabled = false;
                this.setDefaultImage();
            },
            handleDokaLoadError(image){
                if (image.status === 'IMAGE_MIN_SIZE_VALIDATION_ERROR' && image.hasOwnProperty('data') && image.data.hasOwnProperty('minImageSize')) {
                    $notify(this.doka.labelStatusLoadImageError + ' الطول:' + image.data.minImageSize.height + ' العرض:' + image.data.minImageSize.width, '');
                } else {
                    $notify(this.doka.labelStatusImageError + ' (' + image.status + ')', '');
                }
                this.enabled = false;
                this.setDefaultImage();
            },
            openInputFile(event) {
                $(event.target).parent().find('input[type="file"]').click();
            },
            inputFile(e) {
                this.src = this.toUrl(e.target.files[0]);
                this.uploaded = true;
                this.enabled = true;

            },

            editImage () {
                this.enabled = true;
                this.src = `${baseUrl}/products/${this.prodId}/image/${this.imageIdHashed}`; // to avoid CORS
            },

            toUrl(src) {
                return src instanceof Blob ? URL.createObjectURL(src) : src;
            },

            setDefaultImage() {
                this.src = this.imageUrl
                this.uploaded = false;
            }
        }
    }
</script>

<style scoped>
    .btn-delete-circle{
        display: flex;
        justify-content: center;
        align-items: center
    }
</style>