/*!
 * Doka 4.2.0
 * Copyright 2019 PQINA Inc - All Rights Reserved
 * Please visit https://pqina.nl/doka/ for further information
 */
/* eslint-disable */


function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){_defineProperty(e,t,n[t])})}return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_nonIterableSpread()}function _arrayWithoutHoles(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}function _iterableToArray(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance")}var isNode=function(e){return e instanceof HTMLElement},insertBefore=function(e,t){return t.parentNode.insertBefore(e,t)},insertAfter=function(e,t){return t.parentNode.insertBefore(e,t.nextSibling)},isObject=function(e){return"object"===_typeof(e)&&null!==e},createStore=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=_objectSpread({},e),i=[],o=[],a=function(e,t,n){n?o.push({type:e,data:t}):(s[e]&&s[e](t),i.push({type:e,data:t}))},c=function(e){for(var t,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return l[e]?(t=l)[e].apply(t,r):null},u={getState:function(){return _objectSpread({},r)},processActionQueue:function(){var e=[].concat(i);return i.length=0,e},processDispatchQueue:function(){var e=[].concat(o);o.length=0,e.forEach(function(e){var t=e.type,n=e.data;a(t,n)})},dispatch:a,query:c},l={};t.forEach(function(e){l=_objectSpread({},e(r),l)});var s={};return n.forEach(function(e){s=_objectSpread({},e(a,c,r),s)}),u},defineProperty=function(e,t,n){"function"!=typeof n?Object.defineProperty(e,t,n):e[t]=n},forin=function(e,t){for(var n in e)e.hasOwnProperty(n)&&t(n,e[n])},createObject=function(e){var t={};return forin(e,function(n){defineProperty(t,n,e[n])}),t},attr=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null===n)return e.getAttribute(t)||e.hasAttribute(t);e.setAttribute(t,n)},ns="http://www.w3.org/2000/svg",svgElements=["svg","path"],isSVGElement=function(e){return svgElements.includes(e)},createElement=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};"object"===_typeof(t)&&(n=t,t=null);var r=isSVGElement(e)?document.createElementNS(ns,e):document.createElement(e);return t&&(isSVGElement(e)?attr(r,"class",t):r.className=t),forin(n,function(e,t){attr(r,e,t)}),r},appendChild=function(e){return function(t,n){void 0!==n&&e.children[n]?e.insertBefore(t,e.children[n]):e.appendChild(t)}},appendChildView=function(e,t){return function(e,n){return void 0!==n?t.splice(n,0,e):t.push(e),e}},removeChildView=function(e,t){return function(n){return t.splice(t.indexOf(n),1),n.element.parentNode&&e.removeChild(n.element),n}},getViewRect=function(e,t,n,r){var i=n[0]||e.left,o=n[1]||e.top,a=i+e.width,c=o+e.height*(r[1]||1),u={element:_objectSpread({},e),inner:{left:e.left,top:e.top,right:e.right,bottom:e.bottom},outer:{left:i,top:o,right:a,bottom:c}};return t.filter(function(e){return!e.isRectIgnored()}).map(function(e){return e.rect}).forEach(function(e){expandRect(u.inner,_objectSpread({},e.inner)),expandRect(u.outer,_objectSpread({},e.outer))}),calculateRectSize(u.inner),u.outer.bottom+=u.element.marginBottom,u.outer.right+=u.element.marginRight,calculateRectSize(u.outer),u},expandRect=function(e,t){t.top+=e.top,t.right+=e.left,t.bottom+=e.top,t.left+=e.left,t.bottom>e.bottom&&(e.bottom=t.bottom),t.right>e.right&&(e.right=t.right)},calculateRectSize=function(e){e.width=e.right-e.left,e.height=e.bottom-e.top},isNumber=function(e){return"number"==typeof e},thereYet=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.001;return Math.abs(e-t)<r&&Math.abs(n)<r},spring=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiffness,n=void 0===t?.5:t,r=e.damping,i=void 0===r?.75:r,o=e.mass,a=void 0===o?10:o,c=e.delay,u=void 0===c?0:c,l=null,s=null,d=0,f=!1,p=null,h=createObject({interpolate:function(e){if(null===p&&(p=e),!(e-u<p||f)){if(!isNumber(l)||!isNumber(s))return f=!0,void(d=0);thereYet(s+=d+=-(s-l)*n/a,l,d*=i)?(s=l,d=0,f=!0,h.onupdate(s),h.oncomplete(s)):h.onupdate(s)}},target:{set:function(e){if(isNumber(e)&&!isNumber(s)&&(s=e,p=null),null===l&&(l=e,s=e,p=null),f&&(p=null),s===(l=e)||void 0===l)return f=!0,d=0,p=null,h.onupdate(s),void h.oncomplete(s);f=!1},get:function(){return l}},resting:{get:function(){return f}},onupdate:function(e){},oncomplete:function(e){}});return h},easeInOutQuad=function(e){return e<.5?2*e*e:(4-2*e)*e-1},tween=function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.duration,i=void 0===r?500:r,o=n.easing,a=void 0===o?easeInOutQuad:o,c=n.delay,u=void 0===c?0:c,l=null,s=!0,d=!1,f=null,p=createObject({interpolate:function(n){s||null===f||(null===l&&(l=n),n-l<u||((e=n-l-u)<i?(t=e/i,p.onupdate((e>=0?a(d?1-t:t):0)*f)):(e=1,t=d?0:1,p.onupdate(t*f),p.oncomplete(t*f),s=!0)))},target:{get:function(){return d?0:f},set:function(e){if(null===f)return f=e,p.onupdate(e),void p.oncomplete(e);e<f?(f=1,d=!0):(d=!1,f=e),s=!1,l=null}},resting:{get:function(){return s}},onupdate:function(e){},oncomplete:function(e){}});return p},animator={spring:spring,tween:tween},createAnimator=function(e,t,n){var r=e[t]&&"object"===_typeof(e[t][n])?e[t][n]:e[t]||e,i="string"==typeof r?r:r.type,o="object"===_typeof(r)?_objectSpread({},r):{};return animator[i]?animator[i](o):null},addGetSet=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];(t=Array.isArray(t)?t:[t]).forEach(function(t){e.forEach(function(e){var i=e,o=function(){return n[e]},a=function(t){return n[e]=t};"object"===_typeof(e)&&(i=e.key,o=e.getter||o,a=e.setter||a),t[i]&&!r||(t[i]={get:o,set:a})})})},animations=function(e){var t=e.mixinConfig,n=e.viewProps,r=e.viewInternalAPI,i=e.viewExternalAPI,o=(e.viewState,_objectSpread({},n)),a=[];return forin(t,function(e,t){var c=createAnimator(t);c&&(c.onupdate=function(t){n[e]=t},c.target=o[e],addGetSet([{key:e,setter:function(e){c.target!==e&&(c.target=e)},getter:function(){return n[e]}}],[r,i],n,!0),a.push(c))}),{write:function(e){var t=!0;return a.forEach(function(n){n.resting||(t=!1),n.interpolate(e)}),t},destroy:function(){}}},addEvent=function(e){return function(t,n){e.addEventListener(t,n)}},removeEvent=function(e){return function(t,n){e.removeEventListener(t,n)}},listeners=function(e){var t=e.viewExternalAPI,n=e.view,r=[],i=addEvent(n.element),o=removeEvent(n.element);return t.on=function(e,t){r.push({type:e,fn:t}),i(e,t)},t.off=function(e,t){r.splice(r.findIndex(function(n){return n.type===e&&n.fn===t}),1),o(e,t)},{write:function(){return!0},destroy:function(){r.forEach(function(e){o(e.type,e.fn)})}}},apis=function(e){var t=e.mixinConfig,n=e.viewProps,r=e.viewExternalAPI;addGetSet(t,r,n)},isDefined=function(e){return null!=e},defaults={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},styles=function(e){var t=e.mixinConfig,n=e.viewProps,r=e.viewInternalAPI,i=e.viewExternalAPI,o=e.view,a=_objectSpread({},n),c={};addGetSet(t,[r,i],n);var u=function(){return o.rect?getViewRect(o.rect,o.childViews,[n.translateX||0,n.translateY||0],[n.scaleX||0,n.scaleY||0]):null};return r.rect={get:u},i.rect={get:u},t.forEach(function(e){n[e]=void 0===a[e]?defaults[e]:a[e]}),{write:function(){if(propsHaveChanged(c,n))return applyStyles(o.element,n),Object.assign(c,_objectSpread({},n)),!0},destroy:function(){}}},propsHaveChanged=function(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!0;for(var n in t)if(t[n]!==e[n])return!0;return!1},applyStyles=function(e,t){var n=t.opacity,r=t.perspective,i=t.translateX,o=t.translateY,a=t.scaleX,c=t.scaleY,u=t.rotateX,l=t.rotateY,s=t.rotateZ,d=t.originX,f=t.originY,p=t.width,h=t.height,m="",g="";(isDefined(d)||isDefined(f))&&(g+="transform-origin: ".concat(d||0,"px ").concat(f||0,"px;")),isDefined(r)&&(m+="perspective(".concat(r,"px) ")),(isDefined(i)||isDefined(o))&&(m+="translate3d(".concat(i||0,"px, ").concat(o||0,"px, 0) ")),(isDefined(a)||isDefined(c))&&(m+="scale3d(".concat(isDefined(a)?a:1,", ").concat(isDefined(c)?c:1,", 1) ")),isDefined(s)&&(m+="rotateZ(".concat(s,"rad) ")),isDefined(u)&&(m+="rotateX(".concat(u,"rad) ")),isDefined(l)&&(m+="rotateY(".concat(l,"rad) ")),m.length&&(g+="transform:".concat(m,";")),isDefined(n)&&(g+="opacity:".concat(n,";"),0===n&&"BUTTON"===e.nodeName&&(g+="visibility:hidden;"),n<1&&(g+="pointer-events:none;")),isDefined(h)&&(g+="height:".concat(h,"px;")),isDefined(p)&&(g+="width:".concat(p,"px;"));var v=e.elementCurrentStyle||"";g.length===v.length&&g===v||(e.setAttribute("style",g),e.elementCurrentStyle=g)},Mixins={styles:styles,listeners:listeners,animations:animations,apis:apis},updateRect=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.layoutCalculated||(e.paddingTop=parseInt(n.paddingTop,10)||0,e.marginTop=parseInt(n.marginTop,10)||0,e.marginRight=parseInt(n.marginRight,10)||0,e.marginBottom=parseInt(n.marginBottom,10)||0,e.marginLeft=parseInt(n.marginLeft,10)||0,t.layoutCalculated=!0),e.left=t.offsetLeft||0,e.top=t.offsetTop||0,e.width=t.offsetWidth||0,e.height=t.offsetHeight||0,e.right=e.left+e.width,e.bottom=e.top+e.height,e.scrollTop=t.scrollTop,e.hidden=null===t.offsetParent&&"fixed"!==n.position,e},createView=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.tag,n=void 0===t?"div":t,r=e.name,i=void 0===r?null:r,o=e.attributes,a=void 0===o?{}:o,c=e.read,u=void 0===c?function(){}:c,l=e.write,s=void 0===l?function(){}:l,d=e.create,f=void 0===d?function(){}:d,p=e.destroy,h=void 0===p?function(){}:p,m=e.filterFrameActionsForChild,g=void 0===m?function(e,t){return t}:m,v=e.didCreateView,y=void 0===v?function(){}:v,E=e.didWriteView,T=void 0===E?function(){}:E,_=e.ignoreRect,w=void 0!==_&&_,R=e.ignoreRectUpdate,I=void 0!==R&&R,C=e.mixins,A=void 0===C?[]:C;return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=createElement(n,i?"doka--".concat(i):null,a),o=window.getComputedStyle(r,null),c=updateRect(),l=null,d=!1,p=[],m=[],v={},E={},_=[s],R=[u],C=[h],x=function(){return r},O=function(){return[].concat(p)},S=function(){return l||(l=getViewRect(c,p,[0,0],[1,1]))},b=function(){return r.layoutCalculated=!1},M={element:{get:x},style:{get:function(){return o}},childViews:{get:O}},P=_objectSpread({},M,{rect:{get:S},ref:{get:function(){return v}},is:function(e){return i===e},appendChild:appendChild(r),createChildView:function(e){return function(t,n){return t(e,n)}}(e),linkView:function(e){return p.push(e),e},unlinkView:function(e){p.splice(p.indexOf(e),1)},appendChildView:appendChildView(r,p),removeChildView:removeChildView(r,p),registerWriter:function(e){return _.push(e)},registerReader:function(e){return R.push(e)},registerDestroyer:function(e){return C.push(e)},invalidateLayout:b,dispatch:e.dispatch,query:e.query}),L={element:{get:x},childViews:{get:O},rect:{get:S},resting:{get:function(){return d}},isRectIgnored:function(){return w},invalidateLayout:b,_read:function(){l=null,p.forEach(function(e){return e._read()}),!(I&&c.width&&c.height)&&updateRect(c,r,o);var e={root:G,props:t,rect:c};R.forEach(function(t){return t(e)})},_write:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=0===n.length;return _.forEach(function(i){!1===i({props:t,root:G,actions:n,timestamp:e})&&(r=!1)}),m.forEach(function(t){!1===t.write(e)&&(r=!1)}),p.filter(function(e){return!!e.element.parentNode}).forEach(function(t){t._write(e,g(t,n))||(r=!1)}),p.forEach(function(t,i){t.element.parentNode||(G.appendChild(t.element,i),t._read(),t._write(e,g(t,n)),r=!1)}),d=r,T({props:t,root:G,actions:n,timestamp:e}),r},_destroy:function(){m.forEach(function(e){return e.destroy()}),C.forEach(function(e){e({root:G})}),p.forEach(function(e){return e._destroy()})}},D=_objectSpread({},M,{rect:{get:function(){return c}}});Object.keys(A).sort(function(e,t){return"styles"===e?1:"styles"===t?-1:0}).forEach(function(e){var n=Mixins[e]({mixinConfig:A[e],viewProps:t,viewState:E,viewInternalAPI:P,viewExternalAPI:L,view:createObject(D)});n&&m.push(n)});var G=createObject(P);f({root:G,props:t});var V=(r.children||[]).length;return p.forEach(function(e,t){G.appendChild(e.element,V+t)}),y(G),createObject(L,t)}},createPainter=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,r="__framePainter";if(window[r])return window[r].readers.push(e),void window[r].writers.push(t);window[r]={readers:[e],writers:[t]};var i=window[r],o=1e3/n,a=null,c=null;return function e(t){c=window.requestAnimationFrame(e),a||(a=t);var n=t-a;n<=o||(a=t-n%o,i.readers.forEach(function(e){return e()}),i.writers.forEach(function(e){return e(t)}))}(performance.now()),{pause:function(){window.cancelAnimationFrame(c)}}},createRoute=function(e,t){return function(n){var r=n.root,i=n.props,o=n.actions,a=void 0===o?[]:o,c=n.timestamp;a.filter(function(t){return e[t.type]}).forEach(function(t){return e[t.type]({root:r,props:i,action:t.data,timestamp:c})}),t&&t({root:r,props:i,actions:a,timestamp:c})}},isArray=function(e){return Array.isArray(e)},isEmpty=function(e){return null==e},trim=function(e){return e.trim()},toString=function(e){return""+e},toArray=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",";return isEmpty(e)?[]:isArray(e)?e:toString(e).split(t).map(trim).filter(function(e){return e.length})},isBoolean=function(e){return"boolean"==typeof e},toBoolean=function(e){return isBoolean(e)?e:"true"===e},isString=function(e){return"string"==typeof e},toNumber=function(e){return isNumber(e)?e:isString(e)?toString(e).replace(/[a-z]+/gi,""):0},toInt=function(e){return parseInt(toNumber(e),10)},toFloat=function(e){return parseFloat(toNumber(e))},isInt=function(e){return isNumber(e)&&isFinite(e)&&Math.floor(e)===e},toBytes=function(e){if(isInt(e))return e;var t=toString(e).trim();return/MB$/i.test(t)?(t=t.replace(/MB$i/,"").trim(),1e3*toInt(t)*1e3):/KB/i.test(t)?(t=t.replace(/KB$i/,"").trim(),1e3*toInt(t)):toInt(t)},isFunction=function(e){return"function"==typeof e},toFunctionReference=function(e){for(var t=self,n=e.split("."),r=null;r=n.shift();)if(!(t=t[r]))return null;return t},isNull=function(e){return null===e},getType=function(e){return isArray(e)?"array":isNull(e)?"null":isInt(e)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(e)?"bytes":_typeof(e)},replaceSingleQuotes=function(e){return e.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",')},conversionTable={array:toArray,boolean:toBoolean,int:function(e){return"bytes"===getType(e)?toBytes(e):toInt(e)},float:toFloat,bytes:toBytes,string:function(e){return isFunction(e)?e:toString(e)},object:function(e){try{return JSON.parse(replaceSingleQuotes(e))}catch(t){return e}},file:function(e){return e},function:function(e){return toFunctionReference(e)}},convertTo=function(e,t){return conversionTable[t](e)},getValueByType=function(e,t,n){if(e===t)return e;var r=getType(e);if(r!==n){var i=convertTo(e,n);if(r=getType(i),null===i)throw'Trying to assign value with incorrect type to "'.concat(option,'", allowed type: "').concat(n,'"');e=i}return e},createOption=function(e,t){var n=e;return{enumerable:!0,get:function(){return n},set:function(r){n=getValueByType(r,e,t)}}},createOptions=function(e){var t={};return forin(e,function(n){var r=isString(e[n])?e[n]:n,i=e[r];t[n]=r===n?createOption(i[0],i[1]):t[r]}),createObject(t)},resetState=function(e){e.file=null,e.activeView=null,e.rootRect={x:0,y:0,left:0,top:0,width:0,height:0},e.stage=null,e.stageOffset=null,e.image=null,e.zoomTimeoutId=null,e.instantUpdate=!1,e.filePromise=null,e.fileLoader=null,e.instructions={size:null,crop:null,filter:null},e.filter=null,e.colorMatrices={},e.size={width:!1,height:!1,aspectRatioLocked:!0,aspectRatioPrevious:!1},e.crop={rectangle:null,transforms:null,rotation:null,flip:null,aspectRatio:null,isRotating:!1,isDirty:!1,draft:{rectangle:null,transforms:null}}},createInitialState=function(e){var t={noImageTimeout:null,options:createOptions(e)};return resetState(t),t},fromCamels=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-";return e.split(/(?=[A-Z])/).map(function(e){return e.toLowerCase()}).join(t)},logIfDeprecatedOption=function(e,t){e!==t&&console.warn("'".concat(e,"' is deprecated, please use '").concat(t,"' instead."))},createOptionAPI=function(e,t){var n={};return forin(t,function(r){var i=isString(t[r])?t[r]:r;n[r]={get:function(){return logIfDeprecatedOption(r,i),e.getState().options[i]},set:function(t){logIfDeprecatedOption(r,i),e.dispatch("SET_".concat(fromCamels(i,"_").toUpperCase()),{value:t})}}}),n},createOptionActions=function(e){return function(t,n,r){var i={};return forin(e,function(e){var n=fromCamels(e,"_").toUpperCase();i["SET_".concat(n)]=function(i){var o;try{o=r.options[e],r.options[e]=i.value}catch(e){}t("DID_SET_".concat(n),{value:r.options[e],prevValue:o})}}),i}},createOptionQueries=function(e){return function(t){var n={};return forin(e,function(e){n["GET_".concat(fromCamels(e,"_").toUpperCase())]=function(n){return t.options[e]}}),n}},getUniqueId=function(){return Math.random().toString(36).substr(2,9)},arrayRemove=function(e,t){return e.splice(t,1)},on=function(){var e=[],t=function(t,n){arrayRemove(e,e.findIndex(function(e){return e.event===t&&(e.cb===n||!n)}))};return{fire:function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];e.filter(function(e){return e.event===t}).map(function(e){return e.cb}).forEach(function(e){setTimeout(function(){e.apply(void 0,r)},0)})},on:function(t,n){e.push({event:t,cb:n})},onOnce:function(n,r){e.push({event:n,cb:function(){t(n,r),r.apply(void 0,arguments)}})},off:t}},Type={BOOLEAN:"boolean",INT:"int",STRING:"string",ARRAY:"array",OBJECT:"object",FUNCTION:"function",ACTION:"action",SERVER_API:"serverapi",REGEX:"regex",FILE:"file"},IS_BROWSER="undefined"!=typeof window&&void 0!==window.document,isBrowser=function(){return IS_BROWSER},getOptions=function(){return _objectSpread({},defaultOptions)},setOptions=function(e){forin(e,function(e,t){defaultOptions[e]&&setOption(e,t)})},correctDeprecatedOption=function(e){return isString(defaultOptions[e])?defaultOptions[e]:e},setOption=function(e,t){e=correctDeprecatedOption(e),defaultOptions[e][0]=getValueByType(t,defaultOptions[e][0],defaultOptions[e][1])},defaultOptions={id:[null,Type.STRING],className:[null,Type.STRING],src:[null,Type.FILE],maxImagePreviewWidth:[1500,Type.INT],maxImagePreviewHeight:[1500,Type.INT],allowButtonCancel:[!0,Type.BOOLEAN],allowButtonConfirm:[!0,Type.BOOLEAN],allowDropFiles:[!1,Type.BOOLEAN],allowAutoClose:[!0,Type.BOOLEAN],allowAutoDestroy:[!1,Type.BOOLEAN],utils:[["crop"],Type.ARRAY],outputData:[!1,Type.BOOLEAN],outputFile:[!0,Type.BOOLEAN],outputStripImageHead:[!0,Type.BOOLEAN],outputType:[null,Type.STRING],outputQuality:[null,Type.INT],outputFit:["cover",Type.STRING],outputUpscale:[!0,Type.BOOLEAN],outputWidth:[null,Type.INT],outputHeight:[null,Type.INT],size:[null,Type.OBJECT],sizeMin:[{width:1,height:1},Type.OBJECT],sizeMax:[{width:9999,height:9999},Type.OBJECT],filter:[null,Type.OBJECT],filters:[{original:{label:"Original",matrix:function(){return null}},chrome:{label:"Bright",matrix:function(){return[1.398,-.316,.065,-.273,.201,-.051,1.278,-.08,-.273,.201,-.051,.119,1.151,-.29,.215,0,0,0,1,0]}},fade:{label:"Dark",matrix:function(){return[1.073,-.015,.092,-.115,-.017,.107,.859,.184,-.115,-.017,.015,.077,1.104,-.115,-.017,0,0,0,1,0]}},mono:{label:"Mono",matrix:function(){return[.212,.715,.114,0,0,.212,.715,.114,0,0,.212,.715,.114,0,0,0,0,0,1,0]}},noir:{label:"Noir",matrix:function(){return[.15,1.3,-.25,.1,-.2,.15,1.3,-.25,.1,-.2,.15,1.3,-.25,.1,-.2,0,0,0,1,0]}}},Type.OBJECT],crop:[null,Type.OBJECT],cropShowSize:[!1,Type.BOOLEAN],cropZoomTimeout:[null,Type.INT],cropMask:[null,Type.FUNCTION],cropMaskInset:[0,Type.INT],cropAllowImageTurnRight:[!1,Type.BOOLEAN],cropResizeKeyCodes:[[18,91,92,93],Type.ARRAY],cropAspectRatio:[null,Type.STRING],cropAspectRatioOptions:[null,Type.ARRAY],cropMinImageWidth:[1,Type.INT],cropMinImageHeight:[1,Type.INT],beforeCreateBlob:[null,Type.FUNCTION],afterCreateBlob:[null,Type.FUNCTION],onconfirm:[null,Type.FUNCTION],oncancel:[null,Type.FUNCTION],onclose:[null,Type.FUNCTION],onload:[null,Type.FUNCTION],onloaderror:[null,Type.FUNCTION],oninit:[null,Type.FUNCTION],ondestroy:[null,Type.FUNCTION],labelButtonReset:["Reset",Type.STRING],labelButtonCancel:["Cancel",Type.STRING],labelButtonConfirm:["Done",Type.STRING],labelButtonUtilCrop:["Crop",Type.STRING],labelButtonUtilResize:["Resize",Type.STRING],labelButtonUtilFilter:["Filter",Type.STRING],labelStatusAwaitingImage:["Waiting for image…",Type.STRING],labelStatusLoadImageError:["Error loading image…",Type.STRING],labelStatusLoadingImage:["Loading image…",Type.STRING],labelStatusProcessingImage:["Processing image…",Type.STRING],labelButtonCropZoom:["Zoom",Type.STRING],labelButtonCropRotateLeft:["Rotate left",Type.STRING],labelButtonCropRotateRight:["Rotate right",Type.STRING],labelButtonCropRotateCenter:["Center rotation",Type.STRING],labelButtonCropFlipHorizontal:["Flip horizontal",Type.STRING],labelButtonCropFlipVertical:["Flip vertical",Type.STRING],labelButtonCropAspectRatio:["Aspect ratio",Type.STRING],styleFullscreenSafeArea:[isBrowser()&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream?"bottom":"none",Type.STRING],styleLayoutMode:[null,Type.STRING]},limit=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return Math.min(n,Math.max(t,e))},roundFloat=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return parseFloat(e.toFixed(t))},vectorEqual=function(e,t){return roundFloat(e.x)===roundFloat(t.x)&&roundFloat(e.y)===roundFloat(t.y)},roundVector=function(e,t){return{x:roundFloat(e.x,t),y:roundFloat(e.y,t)}},vectorSubtract=function(e,t){return createVector(e.x-t.x,e.y-t.y)},vectorDot=function(e,t){return e.x*t.x+e.y*t.y},vectorDistanceSquared=function(e,t){return vectorDot(vectorSubtract(e,t),vectorSubtract(e,t))},vectorDistance=function(e,t){return Math.sqrt(vectorDistanceSquared(e,t))},vectorLimit=function(e,t){return createVector(limit(e.x,t.x,t.x+t.width),limit(e.y,t.y,t.y+t.height))},vectorRotate=function(e,t,n){var r=Math.cos(t),i=Math.sin(t),o=createVector(e.x-n.x,e.y-n.y);return createVector(n.x+r*o.x-i*o.y,n.y+i*o.x+r*o.y)},createVector=function(){return{x:arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,y:arguments.length>1&&void 0!==arguments[1]?arguments[1]:0}},rectEqualsRect=function(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height},rectFitsInRect=function(e,t){var n=rectBounds(e),r=rectBounds(t);return n.left>=r.left&&n.top>=r.top&&n.bottom<=r.bottom&&n.right<=r.right},rotateRectCorners=function(e,t,n){return 0===t?{tl:e.tl,tr:e.tr,br:e.br,bl:e.bl}:{tl:vectorRotate(e.tl,t,n),tr:vectorRotate(e.tr,t,n),br:vectorRotate(e.br,t,n),bl:vectorRotate(e.bl,t,n)}},rectRotate=function(e,t,n){var r=rotateRectCorners(rectCorners(e),t,n),i=r.tl,o=r.tr,a=r.br,c=r.bl,u=Math.min(i.x,o.x,a.x,c.x),l=Math.min(i.y,o.y,a.y,c.y),s=Math.max(i.x,o.x,a.x,c.x),d=Math.max(i.y,o.y,a.y,c.y);return createRect(u,l,s-u,d-l)},rectScale=function(e,t,n){return createRect(t*(e.x-n.x)+n.x,t*(e.y-n.y)+n.y,t*e.width,t*e.height)},rectTranslate=function(e,t){return createRect(e.x+t.x,e.y+t.y,e.width,e.height)},TRANSFORM_MAP={translate:rectTranslate,rotate:rectRotate,scale:rectScale},rectTransform=function(e,t,n){return t.reduce(function(e,t){return(0,TRANSFORM_MAP[t[0]])(e,t[1],n)},e)},rectClone=function(e){return createRect(e.x,e.y,e.width,e.height)},rectBounds=function(e){return{top:e.y,right:e.x+e.width,bottom:e.y+e.height,left:e.x}},rectFromBounds=function(e){var t=e.top,n=e.right,r=e.bottom,i=e.left;return{x:i,y:t,width:n-i,height:r-t}},rectCenter=function(e){return createVector(e.x+.5*e.width,e.y+.5*e.height)},rectCorners=function(e){return{tl:{x:e.x,y:e.y},tr:{x:e.x+e.width,y:e.y},br:{x:e.x+e.width,y:e.y+e.height},bl:{x:e.x,y:e.y+e.height}}},createRect=function(e,t,n,r){return{x:e,y:t,width:n,height:r}},getNumericAspectRatioFromString=function(e){if(isEmpty(e))return e;if(/:/.test(e)){var t=e.split(":"),n=t[0];return t[1]/n}return parseFloat(e)},getCenteredCropRect=function(e,t){var n=e.width,r=n*t;return r>e.height&&(n=(r=e.height)/t),{x:.5*(e.width-n),y:.5*(e.height-r),width:n,height:r}},calculateCanvasSize=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=e.height/e.width,i=t,o=1,a=r;a>i&&(o=(a=i)/r);var c=Math.max(1/o,i/a),u=e.width/(n*c*o);return{width:u,height:u*t}},createVector$1=function(e,t){return{x:e,y:t}},vectorDot$1=function(e,t){return e.x*t.x+e.y*t.y},vectorSubtract$1=function(e,t){return createVector$1(e.x-t.x,e.y-t.y)},vectorDistanceSquared$1=function(e,t){return vectorDot$1(vectorSubtract$1(e,t),vectorSubtract$1(e,t))},vectorDistance$1=function(e,t){return Math.sqrt(vectorDistanceSquared$1(e,t))},getOffsetPointOnEdge=function(e,t){var n=e,r=t,i=1.5707963267948966-t,o=Math.sin(1.5707963267948966),a=Math.sin(r),c=Math.sin(i),u=Math.cos(i),l=n/o;return createVector$1(u*(l*a),u*(l*c))},getRotatedRectSize=function(e,t){var n=e.width,r=e.height,i=getOffsetPointOnEdge(n,t),o=getOffsetPointOnEdge(r,t),a=createVector$1(e.x+Math.abs(i.x),e.y-Math.abs(i.y)),c=createVector$1(e.x+e.width+Math.abs(o.y),e.y+Math.abs(o.x)),u=createVector$1(e.x-Math.abs(o.y),e.y+e.height-Math.abs(o.x));return{width:vectorDistance$1(a,c),height:vectorDistance$1(a,u)}},getImageRectZoomFactor=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{x:.5,y:.5},i=r.x>.5?1-r.x:r.x,o=r.y>.5?1-r.y:r.y,a=2*i*e.width,c=2*o*e.height,u=getRotatedRectSize(t,n);return Math.max(u.width/a,u.height/c)},getAxisAlignedImageRect=function(e,t){var n=t.origin,r=t.translation,i=t.scale;return rectTransform(e,[["scale",i],["translate",r]],n)},getOffsetPointOnEdge$1=function(e,t){var n=e,r=t,i=1.5707963267948966-t,o=Math.sin(1.5707963267948966),a=Math.sin(r),c=Math.sin(i),u=Math.cos(i),l=n/o;return createVector(u*(l*a),u*(l*c))},getRotatedRectCorners=function(e,t){var n=e.width,r=e.height,i=t%(Math.PI/2),o=getOffsetPointOnEdge$1(n,i),a=getOffsetPointOnEdge$1(r,i),c=rectCorners(e);return{tl:createVector(c.tl.x+Math.abs(o.x),c.tl.y-Math.abs(o.y)),tr:createVector(c.tr.x+Math.abs(a.y),c.tr.y+Math.abs(a.x)),br:createVector(c.br.x-Math.abs(o.x),c.br.y+Math.abs(o.y)),bl:createVector(c.bl.x-Math.abs(a.y),c.bl.y-Math.abs(a.x))}},getCropFromView=function(e,t,n){var r=n.origin,i=n.translation,o=getAxisAlignedImageRect(e,n),a={x:r.x+i.x,y:r.y+i.y},c=2*Math.PI+n.rotation%(2*Math.PI),u=getRotatedRectCorners(t,c),l=vectorRotate(u.tl,-c,a),s=vectorRotate(u.tr,-c,a),d=vectorRotate(u.br,-c,a),f={x:Math.min(l.x,s.x,d.x),y:Math.min(l.y,s.y,d.y),width:Math.max(l.x,s.x,d.x)-Math.min(l.x,s.x,d.x),height:Math.max(l.y,s.y,d.y)-Math.min(l.y,s.y,d.y)},p=createRect(f.x,f.y,f.width,f.height),h=rectCenter(p),m=t.height/t.width,g={x:(h.x-o.x)/o.width,y:(h.y-o.y)/o.height},v=g.y>.5?1-g.y:g.y,y=2*(g.x>.5?1-g.x:g.x)*o.width,E=2*v*o.height;return{center:g,zoom:Math.min(y/p.width,E/p.height),rotation:n.rotation,aspectRatio:m}},getCropFromStateRounded=function(e,t){var n=getCropFromState(e,t);return{center:{x:roundFloat(n.center.x),y:roundFloat(n.center.y)},rotation:roundFloat(n.rotation),zoom:roundFloat(n.zoom),aspectRatio:roundFloat(n.aspectRatio),flip:_objectSpread({},t.flip)}},getCropFromState=function(e,t){var n=getCropFromView(e,t.rectangle,t.transforms);return{center:{x:n.center.x,y:n.center.y},rotation:n.rotation,zoom:n.zoom,aspectRatio:n.aspectRatio,flip:_objectSpread({},t.flip)}},limitSize=function(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"width",o=e.width,a=e.height;if(!o&&!a)return{width:o,height:a};if(o=o&&limit(o,t.width,n.width),a=a&&limit(a,t.height,n.height),!r)return{width:o,height:a};if(a)if(o)"width"===i?a=o/r:"height"===i?o=a*r:(a*r<t.width?a=(o=t.width)/r:o/r<t.height&&(o=(a=t.height)*r),a*r>n.width?a=(o=n.width)/r:o/r>n.height&&(o=(a=n.height)*r));else{a=limit(a*r,t.width,n.width)/r}else o=limit(o/r,t.height,n.height)*r;return{width:o,height:a}},dotColorMatrix=function(e,t){var n=new Array(20);return n[0]=e[0]*t[0]+e[1]*t[5]+e[2]*t[10]+e[3]*t[15],n[1]=e[0]*t[1]+e[1]*t[6]+e[2]*t[11]+e[3]*t[16],n[2]=e[0]*t[2]+e[1]*t[7]+e[2]*t[12]+e[3]*t[17],n[3]=e[0]*t[3]+e[1]*t[8]+e[2]*t[13]+e[3]*t[18],n[4]=e[0]*t[4]+e[1]*t[9]+e[2]*t[14]+e[3]*t[19]+e[4],n[5]=e[5]*t[0]+e[6]*t[5]+e[7]*t[10]+e[8]*t[15],n[6]=e[5]*t[1]+e[6]*t[6]+e[7]*t[11]+e[8]*t[16],n[7]=e[5]*t[2]+e[6]*t[7]+e[7]*t[12]+e[8]*t[17],n[8]=e[5]*t[3]+e[6]*t[8]+e[7]*t[13]+e[8]*t[18],n[9]=e[5]*t[4]+e[6]*t[9]+e[7]*t[14]+e[8]*t[19]+e[9],n[10]=e[10]*t[0]+e[11]*t[5]+e[12]*t[10]+e[13]*t[15],n[11]=e[10]*t[1]+e[11]*t[6]+e[12]*t[11]+e[13]*t[16],n[12]=e[10]*t[2]+e[11]*t[7]+e[12]*t[12]+e[13]*t[17],n[13]=e[10]*t[3]+e[11]*t[8]+e[12]*t[13]+e[13]*t[18],n[14]=e[10]*t[4]+e[11]*t[9]+e[12]*t[14]+e[13]*t[19]+e[14],n[15]=e[15]*t[0]+e[16]*t[5]+e[17]*t[10]+e[18]*t[15],n[16]=e[15]*t[1]+e[16]*t[6]+e[17]*t[11]+e[18]*t[16],n[17]=e[15]*t[2]+e[16]*t[7]+e[17]*t[12]+e[18]*t[17],n[18]=e[15]*t[3]+e[16]*t[8]+e[17]*t[13]+e[18]*t[18],n[19]=e[15]*t[4]+e[16]*t[9]+e[17]*t[14]+e[18]*t[19]+e[19],n},viewCache=[],getColorMatrixFromMatrices=function(e){var t=[];return forin(e,function(e,n){return t.push(n)}),t.length?t.reduce(function(e,t){return dotColorMatrix(_toConsumableArray(e),t)},t.shift()):[]},getImageScalar=function(e){return e.crop.draft.transforms?e.crop.draft.transforms.scale:e.crop.transforms.scale},getMinCropSize=function(e){var t=e.image.width/e.image.naturalWidth,n=getImageScalar(e);return{width:e.options.cropMinImageWidth*n*t,height:e.options.cropMinImageHeight*n*t}},getMaxCropSize=function(e){var t=getImageScalar(e);return{width:e.image.width*t,height:e.image.height*t}},getWidth=function(e){return e.options.size?e.options.size.width:null},getHeight=function(e){return e.options.size?e.options.size.height:null},getOutputSizeWidth=function(e){return!1===e.size.width?getWidth(e):e.size.width},getOutputSizeHeight=function(e){return!1===e.size.height?getHeight(e):e.size.height},getSize=function(e){return!1!==e.size.width&&!1!==e.size.height?{width:e.size.width,height:e.size.height}:e.instructions.size?{width:e.instructions.size.width,height:e.instructions.size.height}:{width:null,height:null}},getAspectRatioOptions=function(e){return e.options.cropAspectRatioOptions?e.options.cropAspectRatioOptions.map(function(e){return{label:e.label,value:e.value?getNumericAspectRatioFromString(e.value):null}}):null},queries=function(e){return{ALLOW_MANUAL_RESIZE:function(){return e.options.utils.includes("resize")},GET_SIZE:function(){return getSize(e)},GET_SIZE_INPUT:function(){return{width:e.size.width,height:e.size.height}},GET_SIZE_ASPECT_RATIO_LOCK:function(){return e.size.aspectRatioLocked},IS_ACTIVE_VIEW:function(t){return e.activeView===t},GET_STYLES:function(){return Object.keys(e.options).filter(function(e){return/^style/.test(e)}).map(function(t){return{name:t,value:e.options[t]}})},GET_FILE:function(){return e.file},GET_IMAGE:function(){return e.image},GET_STAGE:function(){return _objectSpread({},e.stage,e.stageOffset)},GET_ROOT:function(){return e.rootRect},GET_MIN_IMAGE_SIZE:function(){return{width:e.options.cropMinImageWidth,height:e.options.cropMinImageHeight}},GET_IMAGE_PREVIEW_SCALE_FACTOR:function(){return e.image.width/e.image.naturalWidth},GET_MIN_PREVIEW_IMAGE_SIZE:function(){var t=e.image.width/e.image.naturalWidth;return{width:e.options.cropMinImageWidth*t,height:e.options.cropMinImageHeight*t}},GET_MIN_CROP_SIZE:function(){return getMinCropSize(e)},GET_MAX_CROP_SIZE:function(){return getMaxCropSize(e)},GET_MIN_PIXEL_CROP_SIZE:function(){var t=e.crop.transforms.scale,n=getMinCropSize(e);return{width:n.width/t,height:n.height/t}},GET_MAX_PIXEL_CROP_SIZE:function(){var t=e.crop.transforms.scale,n=getMaxCropSize(e);return{width:n.width/t,height:n.height/t}},GET_CROP_ASPECT_RATIO_OPTIONS:function(){return getAspectRatioOptions(e)},GET_ACTIVE_CROP_ASPECT_RATIO:function(){var t=e.crop.aspectRatio;return isString(t)?getNumericAspectRatioFromString(t):t},GET_CROP_ASPECT_RATIO:function(){var t=isString(e.options.cropAspectRatio)?getNumericAspectRatioFromString(e.options.cropAspectRatio):e.options.cropAspectRatio,n=getAspectRatioOptions(e);return n?n.find(function(e){return e.value===t})?t:n[0].value:t},GET_CROP_RECTANGLE_ASPECT_RATIO:function(){var t=e.crop.rectangle;return t.width/t.height},GET_CROP_FLIP:function(){return e.crop.flip?_objectSpread({},e.crop.flip):_objectSpread({},e.instructions.crop.flip)},GET_CROP_ROTATION:function(){return _objectSpread({},e.crop.rotation)},GET_CROP:function(t,n){var r=n+"",i=viewCache[t]?viewCache[t][r]:null;if(i)return i;viewCache[t]||(viewCache[t]=[]);var o=getCropView(e);return viewCache[t][r]=o,o},GET_COLOR_MATRIX:function(){return getColorMatrixFromMatrices(e.colorMatrices)},GET_PREVIEW_IMAGE_DATA:function(){return e.file.preview},GET_THUMB_IMAGE_DATA:function(){return e.file.thumb},GET_FILTER:function(){return e.filter}}},getCurrentImageSize=function(e,t){var n=getOutputSizeWidth(e),r=getOutputSizeHeight(e),i=t.width/t.height;return limitSize({width:n,height:r},e.options.sizeMin,e.options.sizeMax,i)},getCurrentCropSize=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.zoom,r=t.rotation,i=t.center,o=t.aspectRatio,a=calculateCanvasSize(e,o,n),c={x:.5*a.width,y:.5*a.height},u={x:0,y:0,width:a.width,height:a.height,center:c},l=n*getImageRectZoomFactor(e,getCenteredCropRect(u,o),r,i);return{widthFloat:a.width/l,heightFloat:a.height/l,width:Math.round(a.width/l),height:Math.round(a.height/l)}},canZoom=function(e,t){var n=rectCenter(t),r=rectCenter(e);return!vectorEqual(r,n)},getCropView=function(e){if(!e.stage||!e.image)return null;var t=e.crop.draft.rectangle||{free:e.crop.rectangle,limited:e.crop.rectangle},n=e.crop.draft.transforms||e.crop.transforms,r=n.origin,i=n.translation,o=n.scale,a=n.interaction,c=e.crop.rotation,u=e.crop.flip,l=!(!e.crop.draft.rectangle&&!e.crop.draft.transforms),s=l||e.instantUpdate,d=canZoom(t.limited,e.stage),f=e.crop.isDirty||l,p=e.crop.isRotating,h={width:e.image.naturalWidth,height:e.image.naturalHeight},m=getColorMatrixFromMatrices(e.colorMatrices),g={crop:getCurrentCropSize(h,getCropFromState(e.image,{rectangle:t.limited,transforms:{origin:r,translation:i,scale:o,rotation:c.main+c.sub},flip:u})),image:getCurrentImageSize(e,t.limited)},v={x:0,y:0},y=0,E=0;if(s&&a){if(a.translation){var T=a.translation.x-i.x,_=a.translation.y-i.y;v.x=100*Math.sign(T)*Math.log10(1+Math.abs(T)/100),v.y=100*Math.sign(_)*Math.log10(1+Math.abs(_)/100)}if(a.scale){var w=a.scale-o;y=.25*Math.sign(w)*Math.log10(1+Math.abs(w)/.25)}if(a.rotation){var R=a.rotation-(c.main+c.sub);E=.05*Math.sign(R)*Math.log10(1+Math.abs(R)/.05)}}var I={},C=t.free,A=rectBounds(C),x=rectBounds(t.limited);return forin(A,function(e){var t=A[e]-x[e];I[e]=x[e]+5*Math.sign(t)*Math.log10(1+Math.abs(t)/5)}),{canRecenter:d,canReset:f,isDraft:s,isRotating:p,cropRect:{x:I.left,y:I.top,width:I.right-I.left,height:I.bottom-I.top},origin:r,translation:i,translationBand:v,scale:o,scaleBand:y,rotation:c,rotationBand:E,flip:u,interaction:a,cropStatus:g,colorMatrix:m}},readExif=function(e,t){if(1165519206!==e.getUint32(t+=2,!1))return-1;var n=18761===e.getUint16(t+=6,!1);t+=e.getUint32(t+4,n);var r=e.getUint16(t,n);t+=2;for(var i=0;i<r;i++)if(274===e.getUint16(t+12*i,n))return e.getUint16(t+12*i+8,n)},readData=function(e){var t=new DataView(e);if(65496!=t.getUint16(0,!1))return null;for(var n,r=t.byteLength,i=2;i<r;){if(t.getUint16(i+2,!1)<=8)return-1;if(n=t.getUint16(i,!1),i+=2,65505===n)return readExif(t,i);if(65280!=(65280&n))return null;i+=t.getUint16(i,!1)}},getImageOrientation=function(e){return new Promise(function(t,n){var r=new FileReader;r.onload=function(){return t(readData(r.result)||-1)},r.readAsArrayBuffer(e.slice(0,262144))})},Direction={HORIZONTAL:1,VERTICAL:2},getCenteredCropRect$1=function(e,t){var n=e.width,r=n*t;r>e.height&&(n=(r=e.height)/t);var i=.5*(e.width-n),o=.5*(e.height-r);return createRect(i,o,n,r)},getRotatedRectSize$1=function(e,t){var n=e.width,r=e.height,i=getOffsetPointOnEdge$1(n,t),o=getOffsetPointOnEdge$1(r,t),a=rectCorners(e),c=createVector(a.tl.x+Math.abs(i.x),a.tl.y-Math.abs(i.y)),u=createVector(a.tr.x+Math.abs(o.y),a.tr.y+Math.abs(o.x)),l=createVector(a.bl.x-Math.abs(o.y),a.bl.y-Math.abs(o.x));return{width:vectorDistance(c,u),height:vectorDistance(c,l)}},getImageRectZoomFactor$1=function(e,t,n,r){var i=r.x>.5?1-r.x:r.x,o=r.y>.5?1-r.y:r.y,a=2*i*e.width,c=2*o*e.height,u=getRotatedRectSize$1(t,n);return Math.max(u.width/a,u.height/c)},getImageTransformsFromCrop=function(e,t,n){var r=e.center,i=e.zoom,o=e.aspectRatio,a=rectCenter(t),c={x:a.x-n.width*r.x,y:a.y-n.height*r.y},u=2*Math.PI+e.rotation%(2*Math.PI),l=getImageRectZoomFactor$1(n,getCenteredCropRect$1(t,o||n.height/n.width),u,r);return{origin:{x:r.x*n.width,y:r.y*n.height},translation:c,scale:i*l,rotation:e.rotation}},copyImageTransforms=function(e){return{origin:_objectSpread({},e.origin),translation:_objectSpread({},e.translation),rotation:e.rotation,scale:e.scale}},limitImageTransformsToCropRect=function(e,t,n,r){var i=n.translation,o=n.scale,a=n.rotation,c=n.origin,u={origin:_objectSpread({},c),translation:_objectSpread({},i),scale:o,rotation:2*Math.PI+a%(2*Math.PI)},l=e.height/e.width,s={x:c.x+i.x,y:c.y+i.y},d=getRotatedRectCorners(t,u.rotation),f=vectorRotate(d.tl,-u.rotation,s),p=vectorRotate(d.tr,-u.rotation,s),h=vectorRotate(d.br,-u.rotation,s),m=vectorRotate(d.bl,-u.rotation,s),g={x:Math.min(f.x,p.x,h.x,m.x),y:Math.min(f.y,p.y,h.y,m.y),width:Math.max(f.x,p.x,h.x,m.x)-Math.min(f.x,p.x,h.x,m.x),height:Math.max(f.y,p.y,h.y,m.y)-Math.min(f.y,p.y,h.y,m.y)},v=createRect(g.x,g.y,g.width,g.height),y=rectCenter(v),E=rectBounds(v),T=getAxisAlignedImageRect(e,n),_=rectCenter(T),w={x:T.x,y:T.y},R={x:_.x,y:_.y},I=y.x,C=y.y,A={x:w.x,y:w.y,width:T.width,height:T.height};if(!rectFitsInRect(v,T))if("moving"===r){A.y>v.y?A.y=v.y:A.y+A.height<E.bottom&&(A.y=E.bottom-A.height),A.x>v.x?A.x=v.x:A.x+A.width<E.right&&(A.x=E.right-A.width);var x=getAxisAlignedImageRect(e,_objectSpread({},n,{scale:u.scale})),O=rectCenter(x);R.x=O.x,R.y=O.y,w.x=x.x,w.y=x.y,A.x=R.x-.5*A.width,A.y=R.y-.5*A.height,A.y>v.y?A.y=v.y:A.y+A.height<E.bottom&&(A.y=E.bottom-A.height),A.x>v.x?A.x=v.x:A.x+A.width<E.right&&(A.x=E.right-A.width);var S={x:A.x-w.x,y:A.y-w.y},b={x:S.x*Math.cos(u.rotation)-S.y*Math.sin(u.rotation),y:S.x*Math.sin(u.rotation)+S.y*Math.cos(u.rotation)};u.translation.x+=b.x,u.translation.y+=b.y}else if("resizing"===r){T.width<v.width&&(A.width=v.width,A.height=A.width*l,A.height<v.height&&(A.height=v.height,A.width=A.height/l)),T.height<v.height&&(A.height=v.height,A.width=A.height/l,A.width<v.width&&(A.width=v.width,A.height=A.width*l)),A.x=R.x-.5*A.width,A.y=R.y-.5*A.height,A.y>v.y?A.y=v.y:A.y+A.height<E.bottom&&(A.y=E.bottom-A.height),A.x>v.x?A.x=v.x:A.x+A.width<E.right&&(A.x=E.right-A.width),u.scale=getImageRectZoomFactor$1(e,t,u.rotation,{x:(I-A.x)/A.width,y:(C-A.y)/A.height});var M=getAxisAlignedImageRect(e,_objectSpread({},n,{scale:u.scale})),P=rectCenter(M);R.x=P.x,R.y=P.y,w.x=M.x,w.y=M.y,A.x=R.x-.5*A.width,A.y=R.y-.5*A.height,A.y>v.y?A.y=v.y:A.y+A.height<E.bottom&&(A.y=E.bottom-A.height),A.x>v.x?A.x=v.x:A.x+A.width<E.right&&(A.x=E.right-A.width);var L={x:A.x-w.x,y:A.y-w.y},D={x:L.x*Math.cos(u.rotation)-L.y*Math.sin(u.rotation),y:L.x*Math.sin(u.rotation)+L.y*Math.cos(u.rotation)};u.translation.x+=D.x,u.translation.y+=D.y}else if("rotating"===r){var G=!1;if(A.y>v.y){var V=A.y-v.y;A.y=v.y,A.height+=2*V,G=!0}if(A.y+A.height<E.bottom){var N=E.bottom-(A.y+A.height);A.y=E.bottom-A.height,A.height+=2*N,G=!0}if(A.x>v.x){var B=A.x-v.x;A.x=v.x,A.width+=2*B,G=!0}if(A.x+A.width<E.right){var U=E.right-(A.x+A.width);A.x=E.right-A.width,A.width+=2*U,G=!0}G&&(u.scale=getImageRectZoomFactor$1(e,t,u.rotation,{x:(I-T.x)/T.width,y:(C-T.y)/T.height}))}return _objectSpread({},u,{rotation:n.rotation})},getTransformOrigin=function(e,t,n){var r=n.origin,i=n.translation,o=n.scale,a=2*Math.PI+n.rotation%(2*Math.PI),c={x:r.x+i.x,y:r.y+i.y},u=getRotatedRectCorners(t,a),l=vectorRotate(u.tl,-a,c),s=vectorRotate(u.tr,-a,c),d=vectorRotate(u.br,-a,c),f=vectorRotate(u.bl,-a,c),p=createRect(Math.min(l.x,s.x,d.x,f.x),Math.min(l.y,s.y,d.y,f.y),Math.max(l.x,s.x,d.x,f.x)-Math.min(l.x,s.x,d.x,f.x),Math.max(l.y,s.y,d.y,f.y)-Math.min(l.y,s.y,d.y,f.y)),h=createRect(p.x,p.y,p.width,p.height),m=getAxisAlignedImageRect(e,n),g=rectCorners(m),v=rectCenter(m),y=vectorRotate(g.tl,a,c),E=vectorRotate(g.br,a,c),T=y.x+.5*(E.x-y.x),_=y.y+.5*(E.y-y.y),w=rectTranslate(m,{x:T-v.x,y:_-v.y}),R=rectTranslate(h,{x:T-v.x,y:_-v.y}),I=rectCenter(R),C={x:w.x,y:w.y},A=w.width,x=w.height,O=(I.x-C.x)/A,S=(I.y-C.y)/x,b={x:O*e.width,y:S*e.height},M=1-o,P=b.x*M,L=b.y*M,D={x:C.x+A*O,y:C.y+x*S},G=vectorRotate(C,a,{x:C.x+.5*A,y:C.y+.5*x}),V=vectorRotate(C,a,D),N=G.x-V.x,B=G.y-V.y;return{origin:roundVector(b),translation:roundVector({x:C.x-P+N,y:C.y-L+B})}},EdgeMap={n:function(e){return{x:e.x+.5*e.width,y:e.y}},e:function(e){return{x:e.x+e.width,y:e.y+.5*e.height}},s:function(e){return{x:e.x+.5*e.width,y:e.y+e.height}},w:function(e){return{x:e.x,y:e.y+.5*e.height}}},getEdgeCenterCoordinates=function(e,t){return EdgeMap[e](t)},getImageTransformsFromRect=function(e,t,n){var r=n.origin,i=n.translation,o=(n.scale,2*Math.PI+n.rotation%(2*Math.PI)),a=getAxisAlignedImageRect(e,n),c={x:r.x+i.x,y:r.y+i.y},u=getRotatedRectCorners(t,o),l=vectorRotate(u.tl,-o,c),s=vectorRotate(u.tr,-o,c),d=vectorRotate(u.br,-o,c),f={x:Math.min(l.x,s.x,d.x),y:Math.min(l.y,s.y,d.y),width:Math.max(l.x,s.x,d.x)-Math.min(l.x,s.x,d.x),height:Math.max(l.y,s.y,d.y)-Math.min(l.y,s.y,d.y)},p=createRect(f.x,f.y,f.width,f.height),h=rectBounds(p),m=rectBounds(a),g=a;if(h.top<m.top||h.right>m.right||h.bottom>m.bottom||h.left<m.left){var v=_objectSpread({},m);if(h.top<=v.top){var y=v.bottom-v.top,E=v.right-v.left,T=Math.max(1,p.height/y),_=y*T,w=E*T-E;v.bottom=h.top+_,v.top=h.top,v.left-=.5*w,v.right+=.5*w}if(h.bottom>=v.bottom){var R=v.bottom-v.top,I=v.right-v.left,C=Math.max(1,p.height/R),A=R*C,x=I*C-I;v.bottom=h.bottom,v.top=h.bottom-A,v.left-=.5*x,v.right+=.5*x}if(h.left<=v.left){var O=v.bottom-v.top,S=v.right-v.left,b=Math.max(1,p.width/S),M=S*b,P=O*b-O;v.right=h.left+M,v.left=h.left,v.top-=.5*P,v.bottom+=.5*P}if(h.right>=v.right){var L=v.bottom-v.top,D=v.right-v.left,G=Math.max(1,p.width/D),V=D*G,N=L*G-L;v.right=h.right,v.left=h.right-V,v.top-=.5*N,v.bottom+=.5*N}g=createRect(v.left,v.top,v.right-v.left,v.bottom-v.top)}var B=rectCorners(g),U=rectCenter(g),F=vectorRotate(B.tl,o,c),z=vectorRotate(B.br,o,c),k=F.x+.5*(z.x-F.x),W=F.y+.5*(z.y-F.y),j=rectTranslate(g,{x:k-U.x,y:W-U.y}),Y=rectTranslate(p,{x:k-U.x,y:W-U.y}),q=rectCenter(Y),Z={x:j.x,y:j.y},H=j.width,X=j.height,$=(q.x-Z.x)/H,Q=(q.y-Z.y)/X,K=H/e.width,J={x:$*e.width,y:Q*e.height},ee=1-K,te=J.x*ee,ne=J.y*ee,re={x:Z.x+H*$,y:Z.y+X*Q},ie=vectorRotate(Z,o,{x:Z.x+.5*H,y:Z.y+.5*X}),oe=vectorRotate(Z,o,re),ae=ie.x-oe.x,ce=ie.y-oe.y;return{origin:J,translation:{x:Z.x-te+ae,y:Z.y-ne+ce},scale:K,rotation:n.rotation}},getEdgeTargetRect=function(e,t,n,r,i,o,a,c,u){var l=o.left,s=o.right,d=o.top,f=o.bottom,p=s-l,h=f-d,m=i.left,g=i.right,v=i.top,y=i.bottom;if(n===Direction.VERTICAL){if(v=e.y>0?r.y:Math.min(r.y,Math.max(t.y,d)),y=e.y>0?Math.max(r.y,Math.min(t.y,f)):r.y,a){var E=(y-v)/a;m=r.x-.5*E,g=r.x+.5*E}}else if(m=e.x>0?r.x:Math.min(r.x,Math.max(t.x,l)),g=e.x>0?Math.max(r.x,Math.min(t.x,s)):r.x,a){var T=(g-m)*a;v=r.y-.5*T,y=r.y+.5*T}var _,w,R,I,C=c.width,A=c.height;if(n===Direction.VERTICAL?(_=r.x-.5*C,w=r.x+.5*C,e.y<0?(R=r.y-A,I=r.y):e.y>0&&(R=r.y,I=r.y+A)):(R=r.y-.5*A,I=r.y+.5*A,e.x<0?(_=r.x-C,w=r.x):e.x>0&&(_=r.x,w=r.x+C)),a)if(n===Direction.VERTICAL){var x=Math.min((y-v)/a,p),O=x*a;m<l&&(g=(m=l)+x),g>s&&(m=(g=s)-x),r.x=m+.5*x,e.y<0?v=r.y-O:e.y>0&&(y=r.y+O)}else{var S=Math.min((g-m)*a,h),b=S/a;v<d&&(y=(v=d)+S),y>f&&(v=(y=f)-S),r.y=v+.5*S,e.x<0?m=r.x-b:e.x>0&&(g=r.x+b)}var M=rectFromBounds({top:v,right:g,bottom:y,left:m}),P=function(){var t=C*a;n===Direction.HORIZONTAL?(v=r.y-.5*t,y=r.y+.5*t):e.y<0?(y=r.y,v=y-t):e.y>0&&(v=r.y,y=v+t)},L=function(){var t=A/a;n===Direction.VERTICAL?(m=r.x-.5*t,g=r.x+.5*t):e.x<0?(g=r.x,m=g-t):e.x>0&&(m=r.x,g=m+t)};if(g<w&&(g=w,m=w-C,a&&P()),m>_&&(m=_,g=_+C,a&&P()),v>R&&(v=R,y=R+A,a&&L()),y<I&&(y=I,v=I-A,a&&L()),g-m>u.width&&(e.x<0?m=r.x-u.width:g=r.x+u.width),y-v>u.height&&(e.y<0?v=r.y-u.height:y=r.y+u.height),g-m==0&&(e.x>0?g=r.x+2:m=r.x-2),y-v==0&&(e.y>0?y=r.y+2:v=r.y-2),Math.round(m)<l||Math.round(g)>s||Math.round(v)<d||Math.round(y)>f){var D=f-d,G=s-l;if(m<l){m=l;var V=Math.min(g-m,G);g=m+V}if(g>s){g=s;var N=Math.min(g-m,G);m=g-N}if(v<d){v=d;var B=Math.min(y-v,D);y=v+B}if(y>f){y=f;var U=Math.min(y-v,D);v=y-U}M=rectFromBounds({top:v,right:g,bottom:y,left:m})}return{free:M,limited:rectFromBounds({top:v,right:g,bottom:y,left:m})}},CornerMap={nw:function(e){return{x:e.x,y:e.y}},ne:function(e){return{x:e.x+e.width,y:e.y}},se:function(e){return{x:e.x+e.width,y:e.y+e.height}},sw:function(e){return{x:e.x,y:e.y+e.height}}},getCornerCoordinates=function(e,t){return CornerMap[e](t)},getCornerTargetRect=function(e,t,n,r,i,o,a){var c=rectBounds(r),u=c.left,l=c.right,s=c.top,d=c.bottom,f=vectorLimit({x:t.x,y:t.y},r),p=e.x>0?n.x:Math.min(f.x,n.x),h=e.x>0?Math.max(n.x,f.x):n.x,m=e.y>0?n.y:Math.min(f.y,n.y),g=e.y>0?Math.max(n.y,f.y):n.y;if(i){var v=f.x-n.x;e.x>0?h=Math.max(n.x,n.x+e.x*v):p=Math.min(n.x,n.x-e.x*v),e.y>0?g=Math.max(n.y,n.y+e.x*v*i):m=Math.min(n.y,n.y-e.x*v*i)}var y=rectFromBounds({top:m,right:h,bottom:g,left:p});rectFromBounds({top:m,right:h,bottom:g,left:p});if(o.width&&o.height&&(h-p<o.width&&(e.x>0?h=n.x+o.width:p=n.x-o.width),g-m<o.height&&(e.y>0?g=n.y+o.height:m=n.y-o.height),h-p>a.width&&(e.x<0?p=n.x-a.width:h=n.x+a.width),g-m>a.height&&(e.y<0?m=n.y-a.height:g=n.y+a.height)),h-p==0&&(e.x>0?h=n.x+2:p=n.x-2),g-m==0&&(e.y>0?g=n.y+2:m=n.y-2),Math.round(p)<u||Math.round(h)>l||Math.round(m)<s||Math.round(g)>d){var E=d-s,T=l-u;if(p<u){p=u;var _=Math.min(h-p,T);h=p+_,i&&(e.y>0&&(g=n.y+_*i),e.y<0&&(m=n.y-_*i))}if(h>l){h=l;var w=Math.min(h-p,T);p=h-w,i&&(e.y>0&&(g=n.y+w*i),e.y<0&&(m=n.y-w*i))}if(m<s){m=s;var R=Math.min(g-m,E);g=m+R,i&&(e.x>0&&(h=n.x+R/i),e.x<0&&(p=n.x-R/i))}if(g>d){g=d;var I=Math.min(g-m,E);m=g-I,i&&(e.x>0&&(h=n.x+I/i),e.x<0&&(p=n.x-I/i))}y=rectFromBounds({top:m,right:h,bottom:g,left:p})}return{free:y,limited:rectFromBounds({top:m,right:h,bottom:g,left:p})}},TURN=Math.PI/2,PI_QUARTER=Math.PI/4,splitRotation=function(e){var t=roundFloat(PI_QUARTER),n=roundFloat(TURN),r=e/n,i=Math.floor(r)*n,o=e-i;return o>t&&(o-=n,i+=n),{main:i,sub:o}},getImageSize=function(e){return new Promise(function(t,n){var r=new Image;r.src=URL.createObjectURL(e),r.onerror=function(e){clearInterval(i),n(e)};var i=setInterval(function(){r.naturalWidth&&r.naturalHeight&&(clearInterval(i),URL.revokeObjectURL(r.src),t({width:r.naturalWidth,height:r.naturalHeight}))},1)})},scaleImageSize=function(e,t){var n={width:e.width,height:e.height};if(e.width>t.width||e.height>t.height){var r=e.height/e.width,i=t.width/e.width,o=t.height/e.height;i<o?(n.width=e.width*i,n.height=n.width*r):(n.height=e.height*o,n.width=n.height/r)}return n},isImage=function(e){return/^image/.test(e.type)},MATRICES={1:function(){return[1,0,0,1,0,0]},2:function(e){return[-1,0,0,1,e,0]},3:function(e,t){return[-1,0,0,-1,e,t]},4:function(e,t){return[1,0,0,-1,0,t]},5:function(){return[0,1,1,0,0,0]},6:function(e,t){return[0,1,-1,0,t,0]},7:function(e,t){return[0,-1,-1,0,t,e]},8:function(e){return[0,-1,1,0,0,e]}},getImageOrientationMatrix=function(e,t,n){return-1===n&&(n=1),MATRICES[n](e,t)},isFlipped=function(e){return e&&(e.horizontal||e.vertical)},getBitmap=function(e,t,n){if(!t&&!isFlipped(n))return e.width=e.naturalWidth,e.height=e.naturalHeight,e;var r=document.createElement("canvas"),i=e.naturalWidth,o=e.naturalHeight,a=t>=5&&t<=8;a?(r.width=o,r.height=i):(r.width=i,r.height=o);var c=r.getContext("2d");if(t&&c.transform.apply(c,getImageOrientationMatrix(i,o,t)),isFlipped(n)){var u=[1,0,0,1,0,0];(!a&&n.horizontal||a&n.vertical)&&(u[0]=-1,u[4]=i),(!a&&n.vertical||a&&n.horizontal)&&(u[3]=-1,u[5]=o),c.transform.apply(c,u)}return c.drawImage(e,0,0,i,o),r},imageToImageData=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.zoom||1,i=getBitmap(e,t,n.flip),o={width:i.width,height:i.height},a=document.createElement("canvas"),c=n.aspectRatio||o.height/o.width,u=calculateCanvasSize(o,c,r),l={x:.5*u.width,y:.5*u.height},s={x:0,y:0,width:u.width,height:u.height,center:l},d=r*getImageRectZoomFactor(o,getCenteredCropRect(s,c),n.rotation,n.center);a.width=Math.round(u.width/d),a.height=Math.round(u.height/d),l.x/=d,l.y/=d;var f=l.x-o.width*(n.center?n.center.x:.5),p=l.y-o.height*(n.center?n.center.y:.5),h=a.getContext("2d");return h.translate(l.x,l.y),h.rotate(n.rotation||0),h.drawImage(i,f-l.x,p-l.y,o.width,o.height),h.getImageData(0,0,a.width,a.height)},IS_BROWSER$1="undefined"!=typeof window&&void 0!==window.document;IS_BROWSER$1&&(HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(e,t,n){var r=this.toDataURL(t,n).split(",")[1];setTimeout(function(){for(var n=atob(r),i=n.length,o=new Uint8Array(i),a=0;a<i;a++)o[a]=n.charCodeAt(a);e(new Blob([o],{type:t||"image/png"}))})}}));var imageDataToBlob=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return new Promise(function(r){var i=document.createElement("canvas");i.width=e.width,i.height=e.height,i.getContext("2d").putImageData(e,0,0);var o=n?n(i):i;Promise.resolve(o).then(function(e){e.toBlob(r,t.type,t.quality)})})},cropSVG=function(e,t){return new Promise(function(n){var r=new FileReader;r.onloadend=function(){var e=r.result,i=document.createElement("div");i.style.cssText="position:absolute;pointer-events:none;width:0;height:0;visibility:hidden;",i.innerHTML=e;var o=i.querySelector("svg");document.body.appendChild(i);var a=o.getBBox();i.parentNode.removeChild(i);var c=i.querySelector("title"),u=o.getAttribute("viewBox")||"",l=o.getAttribute("width")||"",s=o.getAttribute("height")||"",d=parseFloat(l)||null,f=parseFloat(s)||null,p=(l.match(/[a-z]+/)||[])[0]||"",h=(s.match(/[a-z]+/)||[])[0]||"",m=u.split(" ").map(parseFloat),g=m.length?{x:m[0],y:m[1],width:m[2],height:m[3]}:a,v=null!=d?d:g.width,y=null!=f?f:g.height;o.style.overflow="visible",o.setAttribute("width",v),o.setAttribute("height",y);var E=t.aspectRatio||y/v,T=v,_=T*E,w=getImageRectZoomFactor({width:v,height:y},getCenteredCropRect({width:T,height:_},E),t.rotation,t.center),R=t.zoom*w,I=t.rotation*(180/Math.PI),C={x:.5*T,y:.5*_},A={x:C.x-v*t.center.x,y:C.y-y*t.center.y},x=["rotate(".concat(I," ").concat(C.x," ").concat(C.y,")"),"translate(".concat(C.x," ").concat(C.y,")"),"scale(".concat(R,")"),"translate(".concat(-C.x," ").concat(-C.y,")"),"translate(".concat(A.x," ").concat(A.y,")")],O=["scale(".concat(t.flip.horizontal?-1:1," ").concat(t.flip.vertical?-1:1,")"),"translate(".concat(t.flip.horizontal?-v:0," ").concat(t.flip.vertical?-y:0,")")],S='<?xml version="1.0" encoding="UTF-8"?>\n<svg width="'.concat(T).concat(p,'" height="').concat(_).concat(h,'" \nviewBox="0 0 ').concat(T," ").concat(_,'" \npreserveAspectRatio="xMinYMin"\nxmlns="http://www.w3.org/2000/svg">\n\x3c!-- Generator: PQINA - https://pqina.nl/ --\x3e\n<title>').concat(c?c.textContent:"",'</title>\n<desc>Cropped with FilePond.</desc>\n<g transform="').concat(x.join(" "),'">\n<g transform="').concat(O.join(" "),'">\n').concat(o.outerHTML,"\n</g>\n</g>\n</svg>");n(S)},r.readAsText(e)})},objectToImageData=function(e){var t;try{t=new ImageData(e.width,e.height)}catch(n){t=document.createElement("canvas").getContext("2d").createImageData(e.width,e.height)}return t.data.set(e.data),t},TransformWorker=function(){var e={resize:function(e,t){var r=t.mode,i=void 0===r?"contain":r,o=t.upscale,a=void 0!==o&&o,c=t.width,u=t.height,l=t.filter;if(!c&&!u)return e;null===c?c=u:null===u&&(u=c);if("force"!==i){var s=c/e.width,d=u/e.height,f=1;if("cover"===i?f=Math.max(s,d):"contain"===i&&(f=Math.min(s,d)),f>1&&!1===a)return e;c=e.width*f,u=e.height*f}for(var p=e.width,h=e.height,m=Math.round(c),g=Math.round(u),v=e.data,y=new Uint8ClampedArray(m*g*4),E=p/m,T=h/g,_=Math.ceil(.5*E),w=Math.ceil(.5*T),R=0;R<g;R++)for(var I=0;I<m;I++){for(var C=4*(I+R*m),A=0,x=0,O=0,S=0,b=0,M=0,P=0,L=(R+.5)*T,D=Math.floor(R*T);D<(R+1)*T;D++)for(var G=Math.abs(L-(D+.5))/w,V=(I+.5)*E,N=G*G,B=Math.floor(I*E);B<(I+1)*E;B++){var U=Math.abs(V-(B+.5))/_,F=Math.sqrt(N+U*U);if(F>=-1&&F<=1&&(A=2*F*F*F-3*F*F+1)>0){var z=v[(U=4*(B+D*p))+3];P+=A*z,O+=A,z<255&&(A=A*z/250),S+=A*v[U],b+=A*v[U+1],M+=A*v[U+2],x+=A}}y[C]=S/x,y[C+1]=b/x,y[C+2]=M/x,y[C+3]=P/O,l&&n(C,y,l)}return{data:y,width:m,height:g}},filter:function(e,t){for(var n=e.data,r=n.length,i=t[0],o=t[1],a=t[2],c=t[3],u=t[4],l=t[5],s=t[6],d=t[7],f=t[8],p=t[9],h=t[10],m=t[11],g=t[12],v=t[13],y=t[14],E=t[15],T=t[16],_=t[17],w=t[18],R=t[19],I=0,C=0,A=0,x=0,O=0;I<r;I+=4)C=n[I]/255,A=n[I+1]/255,x=n[I+2]/255,O=n[I+3]/255,n[I]=Math.max(0,Math.min(255*(C*i+A*o+x*a+O*c+u),255)),n[I+1]=Math.max(0,Math.min(255*(C*l+A*s+x*d+O*f+p),255)),n[I+2]=Math.max(0,Math.min(255*(C*h+A*m+x*g+O*v+y),255)),n[I+3]=Math.max(0,Math.min(255*(C*E+A*T+x*_+O*w+R),255));return e}},t=function(t,n){var r=t.transforms,i=null;if(r.forEach(function(e){"filter"===e.type&&(i=e)}),i){var o=null;r.forEach(function(e){"resize"===e.type&&(o=e)}),o&&(o.data.filter=i.data,r=r.filter(function(e){return"filter"!==e.type}))}n(function(t,n){return t.forEach(function(t){n=e[t.type](n,t.data)}),n}(r,t.imageData))};function n(e,t,n){for(var r=0,i=0,o=0,a=t[e]/255,c=t[e+1]/255,u=t[e+2]/255,l=t[e+3]/255;r<4;r++)o=255*(a*n[i=5*r]+c*n[i+1]+u*n[i+2]+l*n[i+3]+n[i+4]),t[e+r]=Math.max(0,Math.min(o,255))}self.onmessage=function(e){t(e.data.message,function(t){self.postMessage({id:e.data.id,message:t},[t.data.buffer])})}},correctOrientation=function(e,t,n){if(1165519206===e.getUint32(t+4,!1)){t+=4;var r=18761===e.getUint16(t+=6,!1);t+=e.getUint32(t+4,r);var i=e.getUint16(t,r);t+=2;for(var o=0;o<i;o++)if(274===e.getUint16(t+12*o,r))return e.setUint16(t+12*o+8,1,r),!0;return!1}},readData$1=function(e){var t=new DataView(e);if(65496!==t.getUint16(0))return null;for(var n,r,i=2,o=!1;i<t.byteLength;){if(n=t.getUint16(i,!1),r=t.getUint16(i+2,!1)+2,!(n>=65504&&n<=65519||65534===n))break;if(o||(o=correctOrientation(t,i,r)),i+r>t.byteLength)break;i+=r}return e.slice(0,i)},getImageHead=function(e){return new Promise(function(t,n){var r=new FileReader;r.onload=function(){return t(readData$1(r.result)||null)},r.readAsArrayBuffer(e.slice(0,262144))})},getBlobBuilder=function(){return window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder},createBlob=function(e,t){var n=getBlobBuilder();if(n){var r=new n;return r.append(e),r.getBlob(t)}return new Blob([e],{type:t})},getUniqueId$1=function(){return Math.random().toString(36).substr(2,9)},createWorker=function(e){var t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(t),r=new Worker(n);return{transfer:function(e,t){},post:function(e,t,n){var i=getUniqueId$1();r.onmessage=function(e){e.data.id===i&&t(e.data.message)},r.postMessage({id:i,message:e},n)},terminate:function(){r.terminate(),URL.revokeObjectURL(n)}}},loadImage=function(e){return new Promise(function(t,n){var r=new Image;r.onload=function(){t(r)},r.onerror=function(e){n(e)},r.src=e})},transformImage=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise(function(r,i){if(!e||!isImage(e))return i();var o=n.stripImageHead,a=n.beforeCreateBlob,c=n.afterCreateBlob,u=t.crop,l=t.size,s=t.filter,d=t.output,f=t.image.orientation?Math.max(1,Math.min(8,t.image.orientation)):null,p=d.quality,h=null===p?null:p/100,m=d.type,g=[];"number"!=typeof l.width&&"number"!=typeof l.height||g.push({type:"resize",data:l}),s&&20===s.length&&g.push({type:"filter",data:s});var v=function(e){var t=c?c(e):e;Promise.resolve(t).then(r)},y=function(e,t){return imageDataToBlob(e,t,a).then(function(e){if(o)return v(e);getImageHead(e).then(function(t){null!==t&&(e=new Blob([t,e.slice(20)],{type:e.type})),v(e)})}).catch(i)};if(/svg/.test(e.type)&&null===m)return cropSVG(e,u).then(function(e){r(createBlob(e,"image/svg+xml"))});var E=URL.createObjectURL(e);loadImage(E).then(function(t){URL.revokeObjectURL(E);var n=imageToImageData(t,f,u),r={quality:h,type:m||e.type};if(!g.length)return y(n,r);var i=createWorker(TransformWorker);i.post({transforms:g,imageData:n},function(e){y(objectToImageData(e),r),i.terminate()},[n.data.buffer])})})},leftPad=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return(t+e).slice(-t.length)},getDateString=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return"".concat(e.getFullYear(),"-").concat(leftPad(e.getMonth()+1,"00"),"-").concat(leftPad(e.getDate(),"00"),"_").concat(leftPad(e.getHours(),"00"),"-").concat(leftPad(e.getMinutes(),"00"),"-").concat(leftPad(e.getSeconds(),"00"))},getBaseCropInstructions=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=e("GET_CROP_ASPECT_RATIO"),i={center:{x:.5,y:.5},flip:{horizontal:!1,vertical:!1},zoom:1,rotation:0,aspectRatio:null};n?Object.assign(i,n):t.options.crop?Object.assign(i,t.options.crop):i.aspectRatio=r;var o=t.size,a=o.width,c=o.height;if(a&&c)i.aspectRatio=a/c;else if(t.instructions.size){var u=t.instructions.size,l=u.width,s=u.height;i.aspectRatio=l/s}return i},getExtensionFromFilename=function(e){return e.split(".").pop()},guesstimateExtension=function(e){if("string"!=typeof e)return"";var t=e.split("/").pop();return/svg/.test(t)?"svg":/zip|compressed/.test(t)?"zip":/plain/.test(t)?"txt":/msword/.test(t)?"doc":/[a-z]+/.test(t)?"jpeg"===t?"jpg":t:""},getFileFromBlob=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i="string"==typeof n?e.slice(0,e.size,n):e.slice(0,e.size,e.type);return i.lastModifiedDate=new Date,isString(t)||(t=getDateString()),t&&null===r&&getExtensionFromFilename(t)?i.name=t:(r=r||guesstimateExtension(i.type),i.name=t+(r?"."+r:"")),i},getFilenameWithoutExtension=function(e){return e.substr(0,e.lastIndexOf("."))||e},ExtensionMap={jpeg:"jpg","svg+xml":"svg"},renameFileToMatchMimeType=function(e,t){var n=getFilenameWithoutExtension(e),r=t.split("/")[1],i=ExtensionMap[r]||r;return"".concat(n,".").concat(i)},getValidOutputMimeType=function(e){return/jpeg|png|svg\+xml/.test(e)?e:"image/jpeg"},isColorMatrix=function(e){return Array.isArray(e)&&20===e.length},TURN$1=Math.PI/2,getOutputSize=function(e){var t={upscale:e("GET_OUTPUT_UPSCALE"),mode:e("GET_OUTPUT_FIT"),width:e("GET_OUTPUT_WIDTH"),height:e("GET_OUTPUT_HEIGHT")},n=e("GET_SIZE_INPUT");if(e("ALLOW_MANUAL_RESIZE")&&(n.width||n.height)){var r=n.width,i=n.height,o=e("GET_CROP_RECTANGLE_ASPECT_RATIO");r&&!i?i=r/o:i&&!r&&(r=i*o),t.width=r,t.height=i,t.upscale=!0,t.mode="force"}return t},prepareOutput=function(e,t,n){return new Promise(function(r,i){var o={data:null,file:null},a=getCropFromStateRounded(t.image,t.crop),c=getOutputSize(n),u={crop:a,image:{orientation:t.file.orientation},size:c,output:{type:n("GET_OUTPUT_TYPE"),quality:n("GET_OUTPUT_QUALITY")},filter:n("GET_COLOR_MATRIX")};if(e.data&&(o.data=u),e.file){var l={beforeCreateBlob:n("GET_BEFORE_CREATE_BLOB"),afterCreateBlob:n("GET_AFTER_CREATE_BLOB"),stripImageHead:n("GET_OUTPUT_STRIP_IMAGE_HEAD")},s=t.file.data;transformImage(s,u,l).then(function(e){o.file=getFileFromBlob(e,renameFileToMatchMimeType(s.name,getValidOutputMimeType(e.type))),r(o)}).catch(i)}else r(o)})},resetRotationScale=function(e){e.crop.draft.rotateMinScale=null},storeRotationScale=function(e){e.crop.draft.rotateMinScale||(e.crop.draft.rotateMinScale=e.crop.transforms.scale)},rotate=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];storeRotationScale(e);var i=_objectSpread({},e.crop.transforms,{scale:e.crop.draft.rotateMinScale});e.crop.draft.transforms=getRotateTransforms(e.image,e.crop.rectangle,i,t.main+t.sub,n,e.crop.draft.transforms?e.crop.draft.transforms.rotation:e.crop.rotation.main+e.crop.rotation.sub,r),e.crop.rotation=splitRotation(e.crop.draft.transforms.rotation)},reset=function(e,t,n){if(null!==e.stage){clearCenterTimeout(e),e.crop.rectangle=getCenteredCropRect$1(e.stage,e.instructions.crop.aspectRatio||e.image.aspectRatio),e.crop.draft.rectangle=null,e.crop.transforms=getImageTransformsFromCrop(e.instructions.crop,e.stage,e.image),e.crop.draft.transforms=null,e.crop.rotation=splitRotation(e.instructions.crop.rotation),e.crop.flip=_objectSpread({},e.instructions.crop.flip);var r=t("GET_CROP_ASPECT_RATIO_OPTIONS")||[],i=r.map(function(e){return e.value}).find(function(t){return t===e.instructions.crop.aspectRatio}),o=r.find(function(e){return null===e.value});i?e.crop.aspectRatio=i:o&&r.length?e.crop.aspectRatio=null:e.crop.aspectRatio=t("GET_CROP_ASPECT_RATIO"),e.crop.isDirty=!1,e.size.width=!!e.instructions.size&&e.instructions.size.width,e.size.height=!!e.instructions.size&&e.instructions.size.height,e.size.aspectRatioLocked=!0,e.size.aspectRatioPrevious=!1,e.filter=e.instructions.filter,n("FILTER_SET_VALUE",{value:e.filter}),resetRotationScale(e)}},recenter=function(e){if(e.stage){clearCenterTimeout(e);var t=e.crop.draft.transforms||e.crop.transforms,n=getCropFromView(e.image,e.crop.rectangle,t);e.crop.aspectRatio&&(n.aspectRatio=e.crop.aspectRatio),e.crop.transforms=getImageTransformsFromCrop(n,e.stage,e.image),e.crop.draft.transforms=null;var r=e.crop.aspectRatio||e.crop.rectangle.height/e.crop.rectangle.width;e.crop.rectangle=getCenteredCropRect$1(e.stage,r),e.crop.draft.rectangle=null,resetRotationScale(e)}},confirmImageDraft=function(e){e.crop.draft.rectangle=null,e.crop.transforms=e.crop.draft.transforms||e.crop.transforms,e.crop.transforms.interaction=null,e.crop.draft.transforms=null,e.crop.transforms=_objectSpread({},e.crop.transforms,getTransformOrigin(e.image,e.crop.rectangle,e.crop.transforms)),e.crop.isRotating=!1,e.crop.isDirty=!0},startCenterTimeout=function(e,t,n){var r=t("GET_CROP_ZOOM_TIMEOUT");r&&(clearTimeout(e.zoomTimeoutId),e.zoomTimeoutId=setTimeout(function(){n("CROP_ZOOM")},r))},resetCenterTimeout=function(e,t,n){clearCenterTimeout(e),startCenterTimeout(e,t,n)},clearCenterTimeout=function(e){clearTimeout(e.zoomTimeoutId)},confirmCropDraft=function(e){e.crop.rectangle=e.crop.draft.rectangle.limited,e.crop.draft.rectangle=null,confirmImageDraft(e),resetRotationScale(e)},copyConfirmed=function(e){e.crop.draft.transforms=copyImageTransforms(e.crop.transforms),e.crop.draft.rectangle={limited:rectClone(e.crop.rectangle),free:rectClone(e.crop.rectangle)},clearCenterTimeout(e)},getMinScale=function(e,t){return Math.min(e.width/t.width,e.height/t.height)},getRotateTransforms=function(e,t,n,r,i,o,a){var c=limitImageTransformsToCropRect(e,t,_objectSpread({},copyImageTransforms(n),{rotation:r}),"rotating"),u=getMinScale(t,i);return roundFloat(c.scale,5)>roundFloat(u,5)?(a&&(o+=2*a),_objectSpread({},copyImageTransforms(n),{rotation:o,interaction:{rotation:c.rotation}})):(c.scale=Math.min(u,c.scale),c.interaction={rotation:c.rotation},c)},getResizeTransforms=function(e,t,n,r,i){var o=Math.max(1e-10,r),a=limitImageTransformsToCropRect(e,t,_objectSpread({},copyImageTransforms(n),{scale:o}),"resizing"),c=getMinScale(t,i);return a.scale=Math.min(c,a.scale),a.interaction={scale:o},a},getTranslateTransforms=function(e,t,n,r){var i={x:n.translation.x+r.x,y:n.translation.y+r.y},o=limitImageTransformsToCropRect(e,t,_objectSpread({},copyImageTransforms(n),{translation:i}),"moving");return o.interaction={translation:i},o},loadImage$1=function(e){return new Promise(function(t,n){if(isString(e))fetch(e).then(function(e){if(e.ok)return e.blob();n(e)}).then(function(e){return t(e)});else if(e instanceof Blob)t(e);else{if("IMG"===e.nodeName){var r=function(e){var n=document.createElement("canvas");n.width=e.naturalWidth,n.height=e.naturalHeight,n.getContext("2d").drawImage(e,0,0),n.toBlob(t)};return e.complete?void r(e):void(e.onload=function(){return r(e)})}"CANVAS"!==e.nodeName?n(e):e.toBlob(t)}})},shouldAbortImageLoad=function(e){return!1===e.file},actions=function(e,t,n){return{AWAIT_IMAGE:function(){n.file||(n.noImageTimeout=setTimeout(function(){e("AWAITING_IMAGE")},250))},REQUEST_REMOVE_IMAGE:function(){e("UNLOAD_IMAGE"),n.file=!1,n.noImageTimeout=setTimeout(function(){e("AWAITING_IMAGE")},500)},DID_UNLOAD_IMAGE:function(){e("ABORT_IMAGE")},REQUEST_ABORT_IMAGE:function(t){e("UNLOAD_IMAGE"),n.file=!1,n.queuedFile=t},DID_SET_SRC:function(t){t.value!==t.prevValue&&(clearTimeout(n.noImageTimeout),e("REQUEST_LOAD_IMAGE",{source:t.value}))},ABORT_IMAGE:function(){if(n.file=null,n.queuedFile){var t=n.queuedFile;n.queuedFile=null,e("REQUEST_LOAD_IMAGE",t)}},REQUEST_LOAD_IMAGE:function(t){var r=t.source,i=t.success,o=void 0===i?function(){}:i,a=t.failure,c=void 0===a?function(e){}:a,u=t.options,l=t.resolveOnConfirm,s=void 0!==l&&l;if(clearTimeout(n.noImageTimeout),!r)return c();null===n.file?(resetState(n),n.file={uid:getUniqueId()},e("DID_REQUEST_LOAD_IMAGE"),loadImage$1(r).then(function(t){if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");t.name||(t.name=getDateString()),n.file.orientation=-1,n.file.data=t,e("LOAD_IMAGE",{success:o,failure:c,options:u,resolveOnConfirm:s},!0),e("KICK")}).catch(function(t){if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");e("DID_LOAD_IMAGE_ERROR",{error:{status:"IMAGE_LOAD_ERROR",data:t}}),c(t)})):e("REQUEST_ABORT_IMAGE",{source:r,success:o,failure:c,options:u,resolveOnConfirm:s})},LOAD_IMAGE:function(r){var i=r.success,o=r.failure,a=r.options,c=void 0===a?{}:a,u=r.resolveOnConfirm;if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");var l=n.file.data;Promise.all([getImageSize(l),getImageOrientation(l)]).then(function(r){if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");var a=r[0],l=r[1];n.file.orientation=l;var s=a.width,d=a.height;l>=5&&l<=8?(a.width=d,a.height=s):(a.width=s,a.height=d);var f=t("GET_MIN_IMAGE_SIZE");if(a.width<f.width||a.height<f.height)return e("DID_LOAD_IMAGE_ERROR",{error:{status:"IMAGE_MIN_SIZE_VALIDATION_ERROR",data:{size:a,minImageSize:f}}}),resetState(n),void o();var p=scaleImageSize(a,{width:t("GET_MAX_IMAGE_PREVIEW_WIDTH"),height:t("GET_MAX_IMAGE_PREVIEW_HEIGHT")});n.image={x:0,y:0,width:p.width,height:p.height,naturalWidth:a.width,naturalHeight:a.height,aspectRatio:a.height/a.width},t("ALLOW_MANUAL_RESIZE")&&c.size&&(n.instructions.size={width:c.size.width,height:c.size.height}),n.instructions.crop=getBaseCropInstructions(t,n,c.crop?_objectSpread({},c.crop):null),n.instructions.filter=void 0===c.filter?n.options.filter:c.filter,e("DID_LOAD_IMAGE"),n.filePromise={resolveOnConfirm:u,success:i,failure:o}}).catch(function(t){if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");e("DID_LOAD_IMAGE_ERROR",{error:{status:"IMAGE_UNKNOWN_ERROR",data:t}}),resetState(n),o()})},CHANGE_VIEW:function(t){var r=t.id;n.activeView=r,e("SHOW_VIEW",{id:r})},UPDATE_ROOT_RECT:function(e){var t=e.rect;n.rootRect=t},DID_RESIZE_STAGE:function(r){var i=r.size,o=r.offset,a=r.animate,c=null===n.stage;if(n.stage=createRect(0,0,i.width,i.height),n.stageOffset=createVector(o.x,o.y),c){if(reset(n,t,e),!n.filePromise.resolveOnConfirm){var u=getCropFromStateRounded(n.image,n.crop),l=getOutputSize(t);n.filePromise.success({crop:u,image:{orientation:n.file.orientation},size:l,output:{type:t("GET_OUTPUT_TYPE"),quality:t("GET_OUTPUT_QUALITY")}})}}else n.instantUpdate=!a,recenter(n),setTimeout(function(){n.instantUpdate=!1},16)},RESIZE_SET_OUTPUT_SIZE_ASPECT_RATIO_LOCK:function(e){var t=e.value;n.size.aspectRatioLocked=t},RESIZE_SET_OUTPUT_SIZE:function(r){var i=r.width,o=r.height,a=limitSize({width:i=i||null,height:o=o||null},t("GET_SIZE_MIN"),t("GET_SIZE_MAX"),t("GET_CROP_RECTANGLE_ASPECT_RATIO"));if(n.size.width=a.width?Math.round(a.width):null,n.size.height=a.height?Math.round(a.height):null,i&&o){var c=o/i;if(c===n.crop.aspectRatio)return;!1===n.size.aspectRatioPrevious&&(n.size.aspectRatioPrevious=n.crop.aspectRatio),e("CROP_SET_ASPECT_RATIO",{value:c})}else!1!==n.size.aspectRatioPrevious&&(e("CROP_SET_ASPECT_RATIO",{value:n.size.aspectRatioPrevious}),n.size.aspectRatioPrevious=!1)},CROP_SET_ASPECT_RATIO:function(e){var r=e.value;if(clearCenterTimeout(n),n.crop.aspectRatio=r,n.crop.aspectRatio&&recenter(n),n.crop.isDirty=!0,n.size.width&&n.size.height)if(n.crop.aspectRatio){var i=n.size.width*n.crop.aspectRatio,o=limit(i,t("GET_SIZE_MIN").height,t("GET_SIZE_MAX").height);n.size.height=o,n.size.width=o/n.crop.aspectRatio}else n.size.height=null},CROP_ZOOM:function(){n.stage&&(clearCenterTimeout(n),recenter(n))},CROP_RECT_CORNER_DRAG_RELEASE:function(){return confirmCropDraft(n)||startCenterTimeout(n,t,e)},CROP_RECT_EDGE_DRAG_RELEASE:function(){return confirmCropDraft(n)||startCenterTimeout(n,t,e)},CROP_IMAGE_RESIZE_GRAB:function(){return copyConfirmed(n)||clearCenterTimeout(n)},CROP_IMAGE_ROTATE_GRAB:function(){copyConfirmed(n),clearCenterTimeout(n),n.crop.isRotating=!0},CROP_RECT_EDGE_DRAG_GRAB:function(){return copyConfirmed(n)||clearCenterTimeout(n)},CROP_RECT_CORNER_DRAG_GRAB:function(){return copyConfirmed(n)||clearCenterTimeout(n)},CROP_RECT_EDGE_DRAG:function(e){var r=e.offset,i=e.origin,o=e.anchor,a=n.image,c=n.stage,u=/n|s/.test(i)?Direction.VERTICAL:Direction.HORIZONTAL,l=getEdgeCenterCoordinates(i,n.crop.rectangle),s=getEdgeCenterCoordinates(o,n.crop.rectangle),d=vectorLimit({x:l.x+(u===Direction.HORIZONTAL?r.x:0),y:l.y+(u===Direction.VERTICAL?r.y:0)},c),f=t("GET_MIN_CROP_SIZE"),p=t("GET_MAX_CROP_SIZE"),h=getMinScale(n.crop.rectangle,t("GET_MIN_IMAGE_SIZE"))/n.crop.transforms.scale;f.width=roundFloat(f.width),f.height=roundFloat(f.height),p.width=roundFloat(p.width*h),p.height=roundFloat(p.height*h);var m={x:Math.sign(l.x-s.x),y:Math.sign(l.y-s.y)};n.crop.draft.rectangle=getEdgeTargetRect(m,d,u,s,rectBounds(n.crop.rectangle),rectBounds(c),n.crop.aspectRatio,f,p),n.crop.draft.transforms=getImageTransformsFromRect(a,n.crop.draft.rectangle.limited,n.crop.transforms)},CROP_RECT_CORNER_DRAG:function(e){var r=e.offset,i=e.origin,o=e.anchor,a=n.image,c=n.stage,u=getCornerCoordinates(i,n.crop.rectangle),l=getCornerCoordinates(o,n.crop.rectangle),s={x:u.x+r.x,y:u.y+r.y},d=t("GET_MIN_CROP_SIZE"),f=t("GET_MAX_CROP_SIZE"),p=getMinScale(n.crop.rectangle,t("GET_MIN_IMAGE_SIZE"))/n.crop.transforms.scale;d.width=roundFloat(d.width),d.height=roundFloat(d.height),f.width=roundFloat(f.width*p),f.height=roundFloat(f.height*p);var h={x:Math.sign(u.x-l.x),y:Math.sign(u.y-l.y)};n.crop.draft.rectangle=getCornerTargetRect(h,s,l,c,n.crop.aspectRatio,d,f),n.crop.draft.transforms=getImageTransformsFromRect(a,n.crop.draft.rectangle.limited,n.crop.transforms)},CROP_IMAGE_DRAG_GRAB:function(){return copyConfirmed(n)||clearCenterTimeout(n)},CROP_IMAGE_DRAG_RELEASE:function(){confirmImageDraft(n),resetRotationScale(n),startCenterTimeout(n,t,e)},CROP_IMAGE_RESIZE_RELEASE:function(){confirmImageDraft(n),resetRotationScale(n),startCenterTimeout(n,t,e)},CROP_IMAGE_ROTATE_RELEASE:function(){confirmImageDraft(n),startCenterTimeout(n,t,e)},CROP_IMAGE_DRAG:function(e){var t=e.value;clearCenterTimeout(n),n.crop.draft.transforms=getTranslateTransforms(n.image,n.crop.rectangle,n.crop.transforms,t)},CROP_IMAGE_RESIZE:function(e){var r=e.value;clearCenterTimeout(n);var i=n.crop.transforms;n.crop.draft.transforms=getResizeTransforms(n.image,n.crop.rectangle,i,i.scale+i.scale*r,t("GET_MIN_PREVIEW_IMAGE_SIZE"))},CROP_IMAGE_RESIZE_MULTIPLY:function(e){var r=e.value;clearCenterTimeout(n);var i=n.crop.transforms;n.crop.draft.transforms=getResizeTransforms(n.image,n.crop.rectangle,i,i.scale*r,t("GET_MIN_PREVIEW_IMAGE_SIZE"))},CROP_IMAGE_RESIZE_AMOUNT:function(e){var r=e.value;clearCenterTimeout(n);var i=n.crop.transforms;n.crop.draft.transforms=getResizeTransforms(n.image,n.crop.rectangle,i,(n.crop.draft.transforms?n.crop.draft.transforms.scale:i.scale)+r,t("GET_MIN_PREVIEW_IMAGE_SIZE"))},CROP_IMAGE_ROTATE:function(e){var r=e.value;clearCenterTimeout(n),n.crop.isRotating=!0,rotate(n,{main:n.crop.rotation.main,sub:r},t("GET_MIN_PREVIEW_IMAGE_SIZE"))},CROP_IMAGE_ROTATE_ADJUST:function(e){var r=e.value;clearCenterTimeout(n),rotate(n,{main:n.crop.rotation.main,sub:Math.min(Math.PI/4,Math.max(-Math.PI/4,n.crop.rotation.sub+r))},t("GET_MIN_PREVIEW_IMAGE_SIZE")),confirmImageDraft(n)},CROP_IMAGE_ROTATE_CENTER:function(){clearCenterTimeout(n),rotate(n,{main:n.crop.rotation.main,sub:0},t("GET_MIN_PREVIEW_IMAGE_SIZE")),confirmImageDraft(n)},CROP_IMAGE_ROTATE_LEFT:function(){resetCenterTimeout(n,t,e),rotate(n,{main:n.crop.rotation.main-TURN$1,sub:n.crop.rotation.sub},t("GET_MIN_PREVIEW_IMAGE_SIZE"),-TURN$1),confirmImageDraft(n)},CROP_IMAGE_ROTATE_RIGHT:function(){resetCenterTimeout(n,t,e),rotate(n,{main:n.crop.rotation.main+TURN$1,sub:n.crop.rotation.sub},t("GET_MIN_PREVIEW_IMAGE_SIZE"),TURN$1),confirmImageDraft(n)},CROP_IMAGE_FLIP_HORIZONTAL:function(){resetCenterTimeout(n,t,e),0===roundFloat(n.crop.rotation.main%Math.PI/2,5)?n.crop.flip.horizontal=!n.crop.flip.horizontal:n.crop.flip.vertical=!n.crop.flip.vertical,n.crop.isDirty=!0},CROP_IMAGE_FLIP_VERTICAL:function(){resetCenterTimeout(n,t,e),0===roundFloat(n.crop.rotation.main%Math.PI/2,5)?n.crop.flip.vertical=!n.crop.flip.vertical:n.crop.flip.horizontal=!n.crop.flip.horizontal,n.crop.isDirty=!0},DID_RECEIVE_IMAGE_DATA:function(e){var t=e.previewData,r=e.thumbData;n.file.preview=t,n.file.thumb=r},SET_COLOR_MATRIX:function(t){var r=t.key,i=t.matrix;i?n.colorMatrices[r]=_toConsumableArray(i):delete n.colorMatrices[r],e("DID_SET_COLOR_MATRIX",{key:"filter",matrix:i})},FILTER_SET_FILTER:function(t){var r=t.value;n.crop.isDirty=!0,e("FILTER_SET_VALUE",{value:r})},FILTER_SET_VALUE:function(r){var i=r.value,o=isColorMatrix(i)?i:null;if(isString(i)){var a=t("GET_FILTERS");forin(a,function(e,t){e===i&&(o=t.matrix())})}n.filter=i,e("SET_COLOR_MATRIX",{key:"filter",matrix:o})},DID_SET_FILTER:function(t){var n=t.value;n!==t.prevValue&&(e("FILTER_SET_VALUE",{value:n}),e("SET_DATA",{filter:n}))},DID_SET_SIZE:function(t){var n=t.value;n!==t.prevValue&&e("SET_DATA",{size:n})},DID_SET_CROP:function(t){var n=t.value;n!==t.prevValue&&e("SET_DATA",{crop:n})},SET_DATA:function(r){if(r.size&&t("ALLOW_MANUAL_RESIZE")){var i=_objectSpread({width:null,height:null},r.size),o=limitSize(i,t("GET_SIZE_MIN"),t("GET_SIZE_MAX"),t("GET_CROP_RECTANGLE_ASPECT_RATIO"));n.instructions.size=_objectSpread({},o),e("RESIZE_SET_OUTPUT_SIZE",o)}r.filter&&(n.instructions.filter=r.filter),r.crop&&(n.instructions.crop=getBaseCropInstructions(t,n,r.crop),e("EDIT_RESET"))},GET_DATA:function(r){var i=r.success,o=r.failure,a=r.file,c=r.data;if(n.file&&n.stage){var u={file:isBoolean(a)?a:t("GET_OUTPUT_FILE"),data:isBoolean(c)?c:t("GET_OUTPUT_DATA"),success:i,failure:o};e(u.file?"REQUEST_PREPARE_OUTPUT":"PREPARE_OUTPUT",u)}},REQUEST_PREPARE_OUTPUT:function(t){var n=t.file,r=t.data,i=t.success,o=t.failure;e("PREPARE_OUTPUT",{file:n,data:r,success:i,failure:o},!0),e("DID_REQUEST_PREPARE_OUTPUT")},PREPARE_OUTPUT:function(r){var i=r.file,o=r.data,a=r.success,c=void 0===a?function(){}:a,u=r.failure,l=void 0===u?function(){}:u;if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");prepareOutput({file:i,data:o},n,t).then(function(t){if(e("DID_PREPARE_OUTPUT"),shouldAbortImageLoad(n))return e("ABORT_IMAGE");c(t)}).catch(function(t){if(shouldAbortImageLoad(n))return e("ABORT_IMAGE");l(t)})},EDIT_RESET:function(){clearCenterTimeout(n),reset(n,t,e)},EDIT_CONFIRM:function(){if(n.file&&n.stage){clearCenterTimeout(n),e("CROP_ZOOM");var r={file:t("GET_OUTPUT_FILE"),data:t("GET_OUTPUT_DATA"),success:function(t){n.filePromise.resolveOnConfirm&&n.filePromise.success(t),e("DID_CONFIRM",{output:t})}};e(r.file?"REQUEST_PREPARE_OUTPUT":"PREPARE_OUTPUT",r)}},EDIT_CANCEL:function(){n.filePromise&&n.filePromise.success(null),e("DID_CANCEL")},EDIT_CLOSE:function(){clearCenterTimeout(n)},EDIT_DESTROY:function(){resetState(n)},SET_OPTIONS:function(t){var n=t.options;forin(n,function(t,n){e("SET_".concat(fromCamels(t,"_").toUpperCase()),{value:n})})}}},wrapper=function(e,t){return createView({ignoreRect:!0,name:e,mixins:t,create:function(e){var t=e.root,n=e.props;n.className&&t.element.classList.add(n.className),n.controls.map(function(e){var n=t.createChildView(e.view,e);e.didCreateView&&e.didCreateView(n),t.appendChildView(n)})}})},textNode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return createView({ignoreRect:!0,tag:e,create:function(e){e.root.element.textContent=t}})},hideBusyIndicators=function(e){e.element.dataset.viewStatus="idle",hideBusyIndicatorsAnimated(e)},hideBusyIndicatorsAnimated=function(e){e.ref.busyIndicators.forEach(function(e){e.translateY=-10,e.opacity=0,e.markedForRemoval=!0})},showBusyIndicator=function(e,t){e.element.dataset.viewStatus="busy";var n=addBusyIndicator(e,t);hideBusyIndicatorsAnimated(e),e.ref.busyIndicators.push(n),n.markedForRemoval=!1,n.translateY=0,n.opacity=1},addBusyIndicator=function(e,t){return e.appendChildView(e.createChildView(wrapper("status-bubble",{styles:["opacity","translateY"],apis:["markedForRemoval"],animations:{opacity:{type:"tween",duration:500},translateY:{type:"spring",mass:20}}}),{translateY:20,opacity:0,controls:[{view:textNode("p",t)}]}))},editStatus=createView({name:"edit-status",ignoreRect:!0,create:function(e){var t=e.root;t.ref.busyIndicators=[],t.element.setAttribute("tabindex",-1)},write:createRoute({AWAITING_IMAGE:function(e){var t=e.root;t.rect.element.hidden||showBusyIndicator(t,t.query("GET_LABEL_STATUS_AWAITING_IMAGE"))},DID_PRESENT_IMAGE:function(e){var t=e.root;hideBusyIndicators(t)},DID_LOAD_IMAGE_ERROR:function(e){var t=e.root;showBusyIndicator(t,t.query("GET_LABEL_STATUS_LOAD_IMAGE_ERROR"))},DID_REQUEST_LOAD_IMAGE:function(e){var t=e.root;showBusyIndicator(t,t.query("GET_LABEL_STATUS_LOADING_IMAGE"))},DID_REQUEST_PREPARE_OUTPUT:function(e){var t=e.root;showBusyIndicator(t,t.query("GET_LABEL_STATUS_PROCESSING_IMAGE"))},DID_PREPARE_OUTPUT:function(e){var t=e.root;hideBusyIndicators(t)}}),didWriteView:function(e){var t=e.root;t.ref.busyIndicators=t.ref.busyIndicators.filter(function(e){return!e.markedForRemoval||0!==e.opacity||(t.removeChildView(e),!1)})}}),Interaction={down:"pointerdown",move:"pointermove",up:"pointerup"},createPointerRegistry=function(){var e=[],t=function(t){return e.findIndex(function(e){return e.pointerId===t.pointerId})};return{update:function(n){var r=t(n);r<0||(e[r]=n)},multiple:function(){return e.length>1},count:function(){return e.length},active:function(){return e.concat()},push:function(n){(function(e){return t(e)>=0})(n)||e.push(n)},pop:function(n){var r=t(n);r<0||e.splice(r,1)}}},addEvent$1=function(e,t,n,r){return e.addEventListener(Interaction[t],n,r)},removeEvent$1=function(e,t,n){return e.removeEventListener(Interaction[t],n)},createDragger=function(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{stopPropagation:!0,cancelOnMultiple:!1},o={x:0,y:0},a={parameters:null,enabled:!0,origin:null,cancel:!1,cancelled:!1,pointers:createPointerRegistry()},c=function(e,t){return t.apply(null,[function(e){return{x:e.pageX-o.x,y:e.pageY-o.y}}(e)].concat(a.parameters))},u=function(n){var r=0===a.pointers.count();r&&(a.active=!1,a.cancel=!1,a.cancelled=!1),a.pointers.push(n),addEvent$1(document.documentElement,"up",s),r?(e===n.target||e.contains(n.target))&&n.isPrimary&&(n.preventDefault(),i.stopPropagation&&(n.stopPropagation(),n.stopImmediatePropagation()),a.active=!0,o.x=n.pageX,o.y=n.pageY,addEvent$1(document.documentElement,"move",l),a.parameters=t()||[]):i.cancelOnMultiple&&(a.cancel=!0)},l=function(e){e.isPrimary&&(a.cancelled||(e.preventDefault(),c(e,n),a.cancel&&(a.cancelled=!0,c(e,r))))},s=function e(t){a.pointers.pop(t),0===a.pointers.count()&&(removeEvent$1(document.documentElement,"move",l),removeEvent$1(document.documentElement,"up",e)),a.active&&(a.cancelled||(t.preventDefault(),i.stopPropagation&&t.stopPropagation(),c(t,n),c(t,r)))};return addEvent$1(document.documentElement,"down",u),{enable:function(){a.enabled||addEvent$1(document.documentElement,"down",u),a.enabled=!0},disable:function(){a.enabled&&removeEvent$1(document.documentElement,"down",u),a.enabled=!1},destroy:function(){removeEvent$1(document.documentElement,"up",s),removeEvent$1(document.documentElement,"move",l),removeEvent$1(document.documentElement,"down",u)}}},transforms={1:function(){return[1,0,0,1,0,0]},2:function(e){return[-1,0,0,1,e,0]},3:function(e,t){return[-1,0,0,-1,e,t]},4:function(e,t){return[1,0,0,-1,0,t]},5:function(){return[0,1,1,0,0,0]},6:function(e,t){return[0,1,-1,0,t,0]},7:function(e,t){return[0,-1,-1,0,t,e]},8:function(e){return[0,-1,1,0,0,e]}},fixImageOrientation=function(e,t,n,r){-1!==r&&e.transform.apply(e,transforms[r](t,n))},createPreviewImage=function(e,t,n,r,i){t=Math.round(t),n=Math.round(n);var o=i||document.createElement("canvas"),a=o.getContext("2d");return r>=5&&r<=8?(o.width=n,o.height=t):(o.width=t,o.height=n),a.save(),fixImageOrientation(a,t,n,r),a.drawImage(e,0,0,t,n),a.restore(),o},BitmapWorker=function(){self.onmessage=function(e){createImageBitmap(e.data.message.file).then(function(t){self.postMessage({id:e.data.id,message:t},[t])})}},isBitmap=function(e){return/^image/.test(e.type)&&!/svg/.test(e.type)},canCreateImageBitmap=function(e){return"createImageBitmap"in window&&isBitmap(e)},loadImage$2=function(e){return new Promise(function(t,n){var r=new Image;r.onload=function(){t(r)},r.onerror=function(e){n(e)},r.src=e})},createWorker$1=function(e){var t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(t),r=new Worker(n),i=[];return{transfer:function(e,t){},post:function(e,t,n){var o=getUniqueId();i[o]=t,r.onmessage=function(e){var t=i[e.data.id];t&&(t(e.data.message),delete i[e.data.id])},r.postMessage({id:o,message:e},n)},terminate:function(){r.terminate(),URL.revokeObjectURL(n)}}},arrayEqual=function(e,t){return Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(function(e,n){return e===t[n]})},cloneImageData=function(e){var t;try{t=new ImageData(e.width,e.height)}catch(n){t=document.createElement("canvas").getContext("2d").createImageData(e.width,e.height)}return t.data.set(new Uint8ClampedArray(e.data)),t},previewWorker=null,previewWorkerTerminationTimeout=null,applyColorMatrix=function(e,t){return new Promise(function(n){previewWorker||(previewWorker=createWorker$1(TransformWorker)),clearTimeout(previewWorkerTerminationTimeout),previewWorker.post({transforms:[{type:"filter",data:t}],imageData:e},function(e){clearTimeout(previewWorkerTerminationTimeout),previewWorkerTerminationTimeout=setTimeout(function(){previewWorker.terminate(),previewWorker=null},5e3),n(e)},[e.data.buffer])})},imageBitmap=createView({name:"image-bitmap",ignoreRect:!0,ignoreRectUpdate:!0,tag:"canvas",mixins:{apis:["zIndex","hasColorMatrix","setColorMatrix"],styles:["opacity","zIndex"],animations:{opacity:"spring"}},create:function(e){var t=e.root,n=e.props;t.ref.activeColorMatrix=null;var r=t.element;r.width=n.imageData.width,r.height=n.imageData.height;var i=r.getContext("2d"),o=function(e){i.putImageData(e,0,0),t.dispatch("DID_APPLY_COLOR_MATRIX",{matrix:t.ref.activeColorMatrix})},a=function(e){return new Promise(function(r){var i=cloneImageData(n.imageData);if(t.ref.activeColorMatrix=e,!isColorMatrix(e))return o(i),void r(t);applyColorMatrix(i,e).then(o).then(function(){return r(t)})})};n.hasColorMatrix=function(e){return!e&&!isColorMatrix(t.ref.activeColorMatrix)||arrayEqual(t.ref.activeColorMatrix,e)},n.setColorMatrix=a,a(n.colorMatrix).then(n.onready||function(){})},write:function(e){var t=e.root,n=e.props;t.element.dataset.dokaLayerIndex=n.zIndex||0}}),imagePreview=createView({name:"image-preview",ignoreRect:!0,ignoreRectUpdate:!0,tag:"div",mixins:{apis:["colorMatrix"],styles:["perspective","rotateX","rotateY","rotateZ","scaleX","scaleY"],animations:{rotateX:{type:"spring",stiffness:.25,damping:.25,mass:2.5},rotateY:{type:"spring",stiffness:.25,damping:.25,mass:2.5},scaleX:{type:"spring",stiffness:.25,damping:.75,mass:15},scaleY:{type:"spring",stiffness:.25,damping:.75,mass:15}}},write:createRoute({DID_PRESENT_IMAGE:function(e){var t=e.root;t.scaleX=1,t.scaleY=1},DID_SET_COLOR_MATRIX:function(e){var t=e.root,n=e.action,r=t.ref.bitmaps,i=r.find(function(e){return e.hasColorMatrix(n.matrix)});if(i){if(1===i.zIndex||1===r.length)return;i.opacity=null,i.opacity=0,i.opacity=1,i.zIndex=1;var o=r.find(function(e){return e!==i});o&&(o.zIndex=0)}else{if(i||2!==r.length)r.push(t.appendChildView(t.createChildView(imageBitmap,{imageData:t.query("GET_PREVIEW_IMAGE_DATA"),colorMatrix:n.matrix})));else r.find(function(e){return 0===e.zIndex}).setColorMatrix(n.matrix)}},DID_APPLY_COLOR_MATRIX:function(e){var t=e.root,n=e.action,r=t.ref.bitmaps,i=r.find(function(e){return e.hasColorMatrix(n.matrix)});i&&(r.forEach(function(e){return e.zIndex=0}),i.opacity=null,i.opacity=0,i.opacity=1,i.zIndex=1)}}),create:function(e){var t=e.root;t.rotateZ=1e-5;var n="preview"!==t.query("GET_STYLE_LAYOUT_MODE");t.scaleX=n?1.15:1,t.scaleY=n?1.15:1,t.rotateX=n?1:null,t.rotateY=n?1:null;var r=t.query("GET_FILE"),i=URL.createObjectURL(r.data);!function(){var e=function(e){var n=scaleImageSize(e,{width:t.query("GET_MAX_IMAGE_PREVIEW_WIDTH"),height:t.query("GET_MAX_IMAGE_PREVIEW_HEIGHT")}),i=createPreviewImage(e,n.width,n.height,r.orientation),o=document.createElement("canvas"),a=scaleImageSize(e,{width:256,height:256});o.width=a.width,o.height=a.height,o.getContext("2d").drawImage(i,0,0,a.width,a.height);var c=i.getContext("2d").getImageData(0,0,i.width,i.height),u=o.getContext("2d").getImageData(0,0,o.width,o.height);t.ref.bitmaps=[t.appendChildView(t.createChildView(imageBitmap,{imageData:c,colorMatrix:t.query("GET_COLOR_MATRIX"),onready:function(){t.dispatch("DID_RECEIVE_IMAGE_DATA",{previewData:c,thumbData:u}),t.dispatch("DID_PRESENT_IMAGE")}}))]},n=function(){loadImage$2(i).then(e)};if(canCreateImageBitmap(r.data)){var o=createWorker$1(BitmapWorker);o.post({file:r.data},function(t){o.terminate(),t?e(t):n()})}else n()}()}}),imageOverlaySpring={type:"spring",stiffness:.4,damping:.65,mass:7},imageContainer=createView({name:"image-container",ignoreRect:!0,ignoreRectUpdate:!0,mixins:{apis:["flipHorizontal","flipVertical","scale","colorMatrix"],styles:["originX","originY","translateX","translateY","scaleX","scaleY","rotateZ","opacity"],animations:{opacity:{type:"tween",delay:150,duration:750},scaleX:imageOverlaySpring,scaleY:imageOverlaySpring,translateX:imageOverlaySpring,translateY:imageOverlaySpring,originX:imageOverlaySpring,originY:imageOverlaySpring,rotateZ:{type:"spring",stiffness:.25,damping:.25,mass:2.5}}},create:function(e){var t=e.root;t.ref.image=t.appendChildView(t.createChildView(imagePreview,{rotateX:0,rotateY:0,perspective:2e3})),t.ref.dragger=createDragger(t.element,function(){t.dispatch("CROP_IMAGE_DRAG_GRAB")},function(e){t.dispatch("CROP_IMAGE_DRAG",{value:e})},function(){t.dispatch("CROP_IMAGE_DRAG_RELEASE")},{cancelOnMultiple:!0})},destroy:function(e){e.root.ref.dragger.destroy()},write:createRoute({SHOW_VIEW:function(e){var t=e.root;"crop"===e.action.id?(t.element.setAttribute("tabindex",0),t.ref.dragger.enable()):(t.element.removeAttribute("tabindex"),t.ref.dragger.disable())}},function(e){var t=e.root,n=e.props,r=t.ref.image,i=n.colorMatrix;r.rotateX=n.flipVertical?Math.PI:0,r.rotateY=n.flipHorizontal?Math.PI:0,r.colorMatrix=i})}),autoPrecision=isBrowser()&&1===window.devicePixelRatio?function(e){return Math.round(e)}:function(e){return e},transformTranslateScale=function(e,t,n,r,i,o){e||(t.translateX=null,t.translateY=null,t.scaleX=null,t.scaleY=null);var a=autoPrecision(n),c=autoPrecision(r),u=autoPrecision(n+i)-a,l=autoPrecision(r+o)-c;t.translateX=a,t.translateY=c,t.scaleX=.01*u,t.scaleY=.01*l},sides=["top","right","bottom","left"],overlayPanel=function(e){return createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"image-overlay-panel-".concat(e),mixins:{styles:["translateX","translateY","scaleX","scaleY"],animations:{translateX:imageOverlaySpring,translateY:imageOverlaySpring,scaleX:imageOverlaySpring,scaleY:imageOverlaySpring}}})},imageOverlay=createView({ignoreRect:!0,name:"image-overlay",mixins:{styles:["opacity"],animations:{opacity:"spring"},apis:["rectangle","container","animate"]},create:function(e){var t=e.root;sides.forEach(function(e){t.ref[e]=t.appendChildView(t.createChildView(overlayPanel(e)))})},write:function(e){var t=e.root,n=e.props,r=t.ref,i=r.top,o=r.right,a=r.bottom,c=r.left,u=n.rectangle,l=n.container,s=n.animate;if(u){var d=t.rect.element,f=l,p=u.x,h=u.y,m=f.top,g=f.left,v=h+m,y=f.width,E=p+g,T=p+u.width,_=h+u.height,w=f.height-(_+m),R=g+d.width-(p+u.width);transformTranslateScale(s,i,-g-2,-m-2,y+4,v+2),transformTranslateScale(s,c,-g-2,h-2,E+2,u.height+4),transformTranslateScale(s,o,T,h-2,R+2,u.height+4),transformTranslateScale(s,a,-g-2,_,y+4,w+2)}}}),sides$1=["top","right","bottom","left"],edge=function(e){return createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"image-outline-edge-".concat(e),mixins:{styles:["translateX","translateY","scaleX","scaleY"],animations:{translateX:imageOverlaySpring,translateY:imageOverlaySpring,scaleX:imageOverlaySpring,scaleY:imageOverlaySpring}}})},imageOutline=createView({name:"image-outline",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"spring"}},apis:["animate","rectangle","edgeWidth"]},create:function(e){var t=e.root;sides$1.forEach(function(e){t.ref[e]=t.appendChildView(t.createChildView(edge(e)))})},write:function(e){var t=e.root,n=e.props,r=t.ref,i=r.top,o=r.right,a=r.bottom,c=r.left,u=n.animate,l=n.rectangle,s=n.edgeWidth;if(l){var d=l.x,f=l.y,p=l.width,h=l.height,m=2*s;transformTranslateScale(u,i,d-s+1,f-s+1,p+m-2,s),transformTranslateScale(u,c,d-s+1,f-s+1,s,h+m-2),transformTranslateScale(u,o,d+p-1,f-s+1,s,h+m-2),transformTranslateScale(u,a,d-s+1,f+h-1,p+m-2,s)}}}),image=createView({name:"image",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:250}},apis:["offsetTop"]},create:function(e){var t=e.root;t.ref.hasTroubleAnimatingTransformOrigin="safari"in window,t.ref.image=t.appendChildView(t.createChildView(imageContainer,{flip:{horizontal:!1,vertical:!1},translateX:null,translateY:null,rotateZ:null,scaleX:null,scaleY:null,opacity:0})),t.ref.overlay=t.appendChildView(t.createChildView(imageOverlay,{opacity:0})),t.ref.outline=t.appendChildView(t.createChildView(imageOutline,{opacity:0}))},write:createRoute({DID_PRESENT_IMAGE:function(e){e.root.ref.image.opacity=1}},function(e){var t=e.root,n=e.props,r=e.actions,i=e.timestamp,o=t.ref,a=o.image,c=o.outline,u=o.overlay,l=o.hasTroubleAnimatingTransformOrigin,s=t.query("GET_CROP",n.id,i);if(s){var d=s.isDraft,f=s.cropRect,p=s.cropStatus,h=s.origin,m=s.translation,g=s.translationBand,v=s.scale,y=s.scaleBand,E=s.rotation,T=s.rotationBand,_=s.flip,w=s.colorMatrix,R=t.query("GET_ROOT"),I=t.query("GET_STAGE"),C=I.x,A=I.y;if(d&&(a.originX=null,a.originY=null,a.translateX=null,a.translateY=null,a.scaleX=null,a.scaleY=null,a.rotateZ=null),a.colorMatrix=w,a.perspective=100,l)!!r.find(function(e){return"CROP_IMAGE_DRAG_RELEASE"===e.type})&&v>1&&(a.originX=null,a.originY=null,a.translateX=null,a.translateY=null);var x=.8,O=1,S=_objectSpread({},f),b=1,M=t.query("IS_ACTIVE_VIEW","crop")?1:5;if(t.query("IS_ACTIVE_VIEW","resize")){x=.9,O=1;var P=p.image.width,L=p.image.height;b=null===P&&null===L?p.crop.width/f.width:null===P?L/f.height:P/f.width;var D=f.width*b,G=f.height*b;S.x=S.x+(.5*f.width-.5*D),S.y=S.y+(.5*f.height-.5*G),S.width=D,S.height=G}a.originX=h.x,a.originY=h.y,a.translateX=m.x+g.x+C,a.translateY=m.y+g.y+A,a.scaleX=(v+y)*b,a.scaleY=(v+y)*b,a.rotateZ=E.main+E.sub+T,a.flipHorizontal=_.horizontal,a.flipVertical=_.vertical,u.animate=!d,u.opacity=x,u.container={left:R.left,top:R.top+n.offsetTop,width:R.width,height:R.height},u.rectangle={x:S.x+C,y:S.y+A,width:S.width,height:S.height},c.animate=!d,c.opacity=O,c.edgeWidth=M,c.rectangle={x:S.x+C,y:S.y+A,width:S.width,height:S.height}}})}),KEY_MAP={38:"up",40:"down",37:"left",39:"right",189:"minus",187:"plus",72:"h",76:"l",81:"q",82:"r",84:"t",86:"v",90:"z",219:"left_bracket",221:"right_bracket"},createKeyboard=function(e,t,n,r,i){var o=null,a=!0,c={enabled:!0},u=function(e){var i=KEY_MAP[e.keyCode]||e.keyCode;n[i]&&(e.stopPropagation(),a&&(o=t(i),a=!1),n[i](o),r(o))},l=function(e){var t=KEY_MAP[e.keyCode]||e.keyCode;n[t]&&(e.stopPropagation(),i(o),a=!0)};return e.addEventListener("keydown",u),e.addEventListener("keyup",l),{enable:function(){c.enabled||(e.addEventListener("keydown",u),e.addEventListener("keyup",l)),c.enabled=!0},disable:function(){c.enabled&&(e.removeEventListener("keydown",u),e.removeEventListener("keyup",l)),c.enabled=!1},destroy:function(){e.removeEventListener("keydown",u),e.removeEventListener("keyup",l)}}},MAGIC=312,createDiv=function(e,t){return createView({name:e,ignoreRect:!0,create:t})},cropRotatorLine=createView({name:"crop-rotator-line",ignoreRect:!0,ignoreRectUpdate:!0,mixins:{styles:["translateX"],animations:{translateX:"spring"}},create:function(e){for(var t=e.root,n='<svg viewBox="-90 -5 180 10" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">',r=0;r<=180;r+=2){var i=r*(176/180)-90+2,o=r%10==0?.5:.2;if(n+='<circle fill="currentColor" cx="'.concat(i,'" cy="').concat(0,'" r="').concat(o,'"/>'),r%10==0)n+='<text fill="currentColor" x="'.concat(i+(i<0?-2.25:0===i?-.75:-1.5),'" y="').concat(3.5,'">').concat(-90+r,"&deg;</text>")}n+="</svg>",t.element.innerHTML=n}}),cropRotator=createView({name:"crop-rotator",ignoreRect:!0,mixins:{styles:["opacity","translateY"],animations:{opacity:{type:"spring",damping:.5,mass:5},translateY:"spring"},apis:["rotation","animate"]},create:function(e){var t=e.root,n=e.props;t.element.setAttribute("tabindex",0);var r=document.createElement("button");r.innerHTML="<span>".concat(t.query("GET_LABEL_BUTTON_CROP_ROTATE_CENTER"),"</span>"),r.className="doka--crop-rotator-center",r.addEventListener("click",function(){t.dispatch("CROP_IMAGE_ROTATE_CENTER")}),t.appendChild(r);var i=null;t.appendChildView(t.createChildView(createDiv("crop-rotator-line-mask",function(e){var t=e.root,n=e.props;i=t.appendChildView(t.createChildView(cropRotatorLine,{translateX:Math.round(n.rotation*MAGIC)}))}),n)),t.ref.line=i;var o=document.createElement("div");o.className="doka--crop-rotator-bar",t.appendChild(o);var a=Math.PI/4,c=0;t.ref.dragger=createDragger(o,function(){c=i.translateX/MAGIC,t.dispatch("CROP_IMAGE_ROTATE_GRAB")},function(e){var n=e.x/t.rect.element.width*(Math.PI/2),r=limit(c+n,-a,a);t.dispatch("CROP_IMAGE_ROTATE",{value:-r})},function(){t.dispatch("CROP_IMAGE_ROTATE_RELEASE")}),t.ref.keyboard=createKeyboard(t.element,function(){c=0},{left:function(){c+=Math.PI/128,t.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:c})},right:function(){c-=Math.PI/128,t.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:c})}},function(){},function(){})},destroy:function(e){var t=e.root;t.ref.dragger.destroy(),t.ref.keyboard.destroy()},write:function(e){var t=e.root,n=e.props,r=e.timestamp,i=n.animate,o=n.rotation;i||0===o||(t.ref.line.translateX=null);var a=0,c=t.query("GET_CROP",n.id,r);if(c&&c.interaction&&c.interaction.rotation){var u=splitRotation(c.interaction.rotation).sub-o;a=.025*Math.sign(u)*Math.log10(1+Math.abs(u)/.025)}t.ref.line.translateX=Math.round((-o-a)*MAGIC)}}),corners=["nw","ne","se","sw"],getOppositeCorner=function(e){return corners[(corners.indexOf(e)+2)%corners.length]},edges=["n","e","s","w"],getOppositeEdge=function(e){return edges[(edges.indexOf(e)+2)%edges.length]},line=createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-rect-focal-line",mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{translateX:"spring",translateY:"spring",scaleX:"spring",scaleY:"spring",opacity:"spring"}}}),createEdge=function(e){return createView({ignoreRect:!0,ignoreRectUpdate:!0,tag:"div",name:"crop-rect-edge-".concat(e),mixins:{styles:["translateX","translateY","scaleX","scaleY"]},create:function(t){var n=t.root;n.element.classList.add("doka--crop-rect-edge"),n.element.setAttribute("tabindex",0),n.element.setAttribute("role","button"),n.ref.dragger=createDragger(n.element,function(){return n.dispatch("CROP_RECT_EDGE_DRAG_GRAB"),[e,getOppositeEdge(e)]},function(e,t,r){n.dispatch("CROP_RECT_EDGE_DRAG",{offset:e,origin:t,anchor:r})},function(){n.dispatch("CROP_RECT_EDGE_DRAG_RELEASE")}),n.ref.keyboard=createKeyboard(n.element,function(){return{x:0,y:0}},{up:function(e){e.y-=20},down:function(e){e.y+=20},left:function(e){e.x-=20},right:function(e){e.x+=20}},function(t){n.dispatch("CROP_RECT_EDGE_DRAG",{offset:t,origin:e,anchor:getOppositeEdge(e)})},function(){n.dispatch("CROP_RECT_EDGE_DRAG_RELEASE")})},destroy:function(e){var t=e.root;t.ref.keyboard.destroy(),t.ref.dragger.destroy()}})},createCorner=function(e,t,n){return createView({ignoreRect:!0,ignoreRectUpdate:!0,tag:"div",name:"crop-rect-corner-".concat(e),mixins:{styles:["translateX","translateY","scaleX","scaleY"],animations:{translateX:imageOverlaySpring,translateY:imageOverlaySpring,scaleX:{type:"spring",delay:n},scaleY:{type:"spring",delay:n},opacity:{type:"spring",delay:t}}},create:function(t){var n=t.root;n.element.classList.add("doka--crop-rect-corner"),n.element.setAttribute("role","button"),n.element.setAttribute("tabindex",-1),n.ref.dragger=createDragger(n.element,function(){return n.dispatch("CROP_RECT_CORNER_DRAG_GRAB"),[e,getOppositeCorner(e)]},function(e,t,r){n.dispatch("CROP_RECT_CORNER_DRAG",{offset:e,origin:t,anchor:r})},function(){n.dispatch("CROP_RECT_CORNER_DRAG_RELEASE")})},destroy:function(e){e.root.ref.dragger.destroy()}})},cropRect=createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-rect",mixins:{apis:["rectangle","draft","rotating"]},create:function(e){var t=e.root;t.ref.wasRotating=!1;corners.forEach(function(e,n){var r=10*n,i=250+r+50,o=250+r;t.ref[e]=t.appendChildView(t.createChildView(createCorner(e,i,o),{opacity:0,scaleX:.5,scaleY:.5}))}),edges.forEach(function(e){t.ref[e]=t.appendChildView(t.createChildView(createEdge(e)))}),t.ref.lines=[];for(var n=0;n<10;n++)t.ref.lines.push(t.appendChildView(t.createChildView(line,{opacity:0})));t.ref.animationDir=null},write:function(e){var t=e.root,n=e.props,r=n.rectangle,i=n.draft,o=n.rotating;if(r){var a=t.ref,c=a.n,u=a.e,l=a.s,s=a.w,d=a.nw,f=a.ne,p=a.se,h=a.sw,m=a.lines,g=a.animationDir,v=r.x,y=r.y,E=r.x+r.width,T=r.y+r.height,_=T-y,w=E-v,R=t.query("IS_ACTIVE_VIEW","crop");if(R&&"in"!==g?(t.ref.animationDir="in",corners.map(function(e){return t.ref[e]}).forEach(function(e){e.opacity=1,e.scaleX=1,e.scaleY=1})):R||"out"===g||(t.ref.animationDir="out",corners.map(function(e){return t.ref[e]}).forEach(function(e){e.opacity=0,e.scaleX=.5,e.scaleY=.5})),transformTranslate(i,d,v,y),transformTranslate(i,f,E,y),transformTranslate(i,p,E,T),transformTranslate(i,h,v,T),transformTranslateScale$1(i,c,v,y,w/100,1),transformTranslateScale$1(i,u,E,y,1,_/100),transformTranslateScale$1(i,l,v,T,w/100,1),transformTranslateScale$1(i,s,v,y,1,_/100),o){t.ref.wasRotating=!0;var I=m.slice(0,5),C=1/I.length;I.forEach(function(e,t){transformTranslateScale$1(i,e,v,y+_*(C+t*C),w/100,.01),e.opacity=.5});var A=m.slice(5);C=1/A.length,A.forEach(function(e,t){transformTranslateScale$1(i,e,v+w*(C+t*C),y,.01,_/100),e.opacity=.5})}else if(i){t.ref.wasRotating=!1;var x=m[0],O=m[1],S=m[2],b=m[3];transformTranslateScale$1(i,x,v,y+.333*_,w/100,.01),transformTranslateScale$1(i,O,v,y+.666*_,w/100,.01),transformTranslateScale$1(i,S,v+.333*w,y,.01,_/100),transformTranslateScale$1(i,b,v+.666*w,y,.01,_/100),x.opacity=.5,O.opacity=.5,S.opacity=.5,b.opacity=.5}else{var M=m[0],P=m[1],L=m[2],D=m[3];!t.ref.wasRotating&&M.opacity>0&&(transformTranslateScale$1(i,M,v,y+.333*_,w/100,.01),transformTranslateScale$1(i,P,v,y+.666*_,w/100,.01),transformTranslateScale$1(i,L,v+.333*w,y,.01,_/100),transformTranslateScale$1(i,D,v+.666*w,y,.01,_/100)),m.forEach(function(e){return e.opacity=0})}}}}),transformTranslateScale$1=function(e,t,n,r,i,o){e&&(t.translateX=null,t.translateY=null,t.scaleX=null,t.scaleY=null),t.translateX=autoPrecision(n),t.translateY=autoPrecision(r),t.scaleX=i,t.scaleY=o},transformTranslate=function(e,t,n,r){e&&(t.translateX=null,t.translateY=null),t.translateX=autoPrecision(n),t.translateY=autoPrecision(r)},setInnerHTML=function(e,t){if(!/svg/.test(e.namespaceURI)||"innerHTML"in e)e.innerHTML=t;else{var n=document.createElement("div");n.innerHTML="<svg>"+t+"</svg>";for(var r=n.firstChild;r.firstChild;)e.appendChild(r.firstChild)}},cropMask=createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-mask",tag:"svg",mixins:{styles:["opacity","translateX","translateY"],animations:{scale:imageOverlaySpring,maskWidth:imageOverlaySpring,maskHeight:imageOverlaySpring,translateX:imageOverlaySpring,translateY:imageOverlaySpring,opacity:{type:"tween",delay:0,duration:1e3}},apis:["rectangle","animate","maskWidth","maskHeight","scale"]},create:function(e){var t=e.root;t.customWriter=t.query("GET_CROP_MASK")(t.element,setInnerHTML)||function(){}},didWriteView:function(e){var t=e.root,n=e.props,r=n.maskWidth,i=n.maskHeight,o=n.scale;if(r&&i){t.element.setAttribute("width",autoPrecision(r)),t.element.setAttribute("height",autoPrecision(i));var a=t.query("GET_CROP_MASK_INSET");t.customWriter({x:o*a,y:o*a,width:r-o*a*2,height:i-o*a*2},{width:r,height:i})}}}),updateText=function(e,t){var n=e.childNodes[0];n?t!==n.nodeValue&&(n.nodeValue=t):(n=document.createTextNode(t),e.appendChild(n))},cropSize=createView({ignoreRect:!0,name:"crop-size",mixins:{styles:["translateX","translateY","opacity"],animations:{translateX:"spring",translateY:"spring",opacity:"spring"},listeners:!0},create:function(e){var t=e.root,n=createElement("span");n.className="doka--crop-size-info doka--crop-resize-percentage",t.ref.resizePercentage=n,t.appendChild(n);var r=createElement("span");r.className="doka--crop-size-info";var i=createElement("span");i.className="doka--crop-size-multiply",i.textContent="×";var o=createElement("span"),a=createElement("span");t.ref.outputWidth=o,t.ref.outputHeight=a,r.appendChild(o),r.appendChild(i),r.appendChild(a),t.appendChild(r),t.ref.previousValues={width:0,height:0,percentage:0}},write:function(e){var t=e.root,n=e.props,r=e.timestamp,i=t.query("GET_CROP",n.id,r);if(i){var o=i.cropStatus,a=t.ref,c=a.outputWidth,u=a.outputHeight,l=a.resizePercentage,s=a.previousValues,d=o.image,f=o.crop,p=f.width,h=f.height,m=f.widthFloat/f.heightFloat;d.width&&d.height?(p=d.width,h=d.height):d.width&&!d.height?(p=d.width,h=d.width/m):d.height&&!d.width&&(h=d.height,p=d.height*m),p=Math.round(p),h=Math.round(h);var g=d.width?Math.round(d.width/f.width*100):0;p!==s.width&&(updateText(c,p),s.width=p),h!==s.height&&(updateText(u,h),s.height=h),g!==s.percentage&&(d.width&&updateText(l,"".concat(g,"%")),s.percentage=g)}}}),button=createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"button",mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:250}},apis:["id"],listeners:!0},tag:"button",create:function(e){var t=e.root,n=e.props;t.element.innerHTML="".concat(n.icon||"","<span>").concat(n.label,"</span>"),t.element.setAttribute("type",n.type||"button"),n.name&&n.name.split(" ").forEach(function(e){t.element.className+=" doka--button-".concat(e)}),t.ref.handleClick=function(e){"string"==typeof n.action?t.dispatch(n.action):n.action()},t.element.addEventListener("click",t.ref.handleClick),n.create&&n.create({root:t,props:n})},destroy:function(e){var t=e.root;t.element.removeEventListener("click",t.ref.handleClick)}}),createIcon=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:24;return'<svg width="'.concat(t,'" height="').concat(t,'" viewBox="0 0 ').concat(t," ").concat(t,'" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false">').concat(e,"</svg>")},centerZoomButton=function(e){var t=e.root,n=e.props,r=e.timestamp,i=t.query("GET_CROP",n.id,r),o=t.ref.btnZoom;if(o){var a=i.cropRect;o.translateX=a.x+.5*a.width,o.translateY=a.y+.5*a.height}},cropSubject=createView({name:"crop-subject",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:250}}},create:function(e){var t=e.root,n=e.props;t.opacity=1,t.query("GET_CROP_MASK")&&(t.ref.maskView=t.appendChildView(t.createChildView(cropMask))),t.ref.cropView=t.appendChildView(t.createChildView(cropRect)),t.query("GET_CROP_SHOW_SIZE")&&(t.ref.cropSize=t.appendChildView(t.createChildView(cropSize,{id:n.id,opacity:1,scaleX:1,scaleY:1,translateX:null}))),t.query("GET_CROP_ZOOM_TIMEOUT")||(t.ref.btnZoom=t.appendChildView(t.createChildView(wrapper("zoom-wrapper",{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:250}}}),{opacity:0,controls:[{view:button,label:t.query("GET_LABEL_BUTTON_CROP_ZOOM"),name:"zoom",icon:createIcon('<g fill="currentColor" fill-rule="nonzero"><path d="M12.5 19a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13zm0-2a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9z"/><path d="M15.765 17.18a1 1 0 1 1 1.415-1.415l3.527 3.528a1 1 0 0 1-1.414 1.414l-3.528-3.527z"/></g>',26),action:function(){return t.dispatch("CROP_ZOOM")}}]})))},write:createRoute({CROP_RECT_EDGE_DRAG_RELEASE:centerZoomButton,CROP_RECT_CORNER_DRAG_RELEASE:centerZoomButton},function(e){var t=e.root,n=e.props,r=e.timestamp,i=t.query("GET_CROP",n.id,r);if(i){var o=t.ref,a=o.cropView,c=o.maskView,u=o.btnZoom,l=o.cropSize,s=i.cropRect,d=i.isRotating,f=i.isDraft,p=i.scale;if(t.query("IS_ACTIVE_VIEW","crop")){if(a.draft=f,a.rotating=d,a.rectangle=s,l){f&&(l.translateX=null,l.translateY=null);var h=getCropSizeOffset(t.rect.element,l.rect.element,s);l.translateX=f?h.x:autoPrecision(h.x),l.translateY=f?h.y:autoPrecision(h.y)}c&&(f&&(c.translateX=null,c.translateY=null,c.maskWidth=null,c.maskHeight=null),c.translateX=autoPrecision(s.x),c.translateY=autoPrecision(s.y),c.maskWidth=s.width,c.maskHeight=s.height,c.scale=p),u&&(u.opacity=i.canRecenter&&!i.isDraft?1:0)}}})}),getCropSizeOffset=function(e,t,n){var r=n.x,i=n.x+n.width,o=n.y+n.height,a=i-t.width-16,c=o-t.height-16;return t.width>n.width-32&&(a=r+(.5*n.width-.5*t.width),(c=o+16)>e.height-t.height&&(c=o-t.height-16)),{x:a=Math.max(0,Math.min(a,e.width-t.width)),y:c}},now=function(){return performance.now()},throttle=function(e,t){var n=null,r=null;return function(){var i=arguments;if(!r)return e.apply(null,Array.from(arguments)),void(r=now());clearTimeout(n),n=setTimeout(function(){now()-r>=t&&(e.apply(null,Array.from(i)),r=now())},t-(now()-r))}},climb=function(e,t){for(;1===e.nodeType&&!t(e);)e=e.parentNode;return 1===e.nodeType?e:null},isMyTarget=function(e,t){var n=climb(t,function(e){return e.classList.contains("doka--root")});return!!n&&n.contains(e)},updateIndicators=function(e){var t=e.root,n=e.props,r=e.action.position,i=n.pivotPoint,o=t.ref,a=o.indicatorA,c=o.indicatorB,u=i.x-r.x,l=i.y-r.y,s={x:i.x+u,y:i.y+l},d={x:i.x-u,y:i.y-l};a.style.cssText="transform: translate3d(".concat(s.x,"px, ").concat(s.y,"px, 0)"),c.style.cssText="transform: translate3d(".concat(d.x,"px, ").concat(d.y,"px, 0)")},getPositionFromEvent=function(e){return{x:e.pageX,y:e.pageY}},cropResize=createView({ignoreRect:!0,ignoreRectUpdate:!0,name:"crop-resizer",mixins:{apis:["pivotPoint"]},create:function(e){var t=e.root,n=e.props;t.ref.isActive=!1,t.ref.indicatorA=document.createElement("div"),t.appendChild(t.ref.indicatorA),t.ref.indicatorB=document.createElement("div"),t.appendChild(t.ref.indicatorB);var r=t.query("GET_CROP_RESIZE_KEY_CODES"),i={origin:{x:null,y:null},position:{x:null,y:null},selecting:!1,enabled:!1,scrollY:0,offsetX:0,offsetY:0},o=now();t.ref.state=i;var a=createPointerRegistry(),c=0,u=!1;t.ref.resizeStart=function(e){if(t.ref.isActive&&(0===a.count()&&(u=!1),a.push(e),addEvent$1(document.documentElement,"up",t.ref.resizeEnd),isMyTarget(t.element,e.target)&&a.multiple())){e.stopPropagation(),e.preventDefault();var n=a.active(),r=getPositionFromEvent(n[0]),i=getPositionFromEvent(n[1]);c=vectorDistance(r,i),addEvent$1(document.documentElement,"move",t.ref.resizeMove),u=!0}},t.ref.resizeMove=function(e){if(t.ref.isActive&&u&&(e.preventDefault(),2===a.count())){a.update(e);var n=a.active(),r=getPositionFromEvent(n[0]),i=getPositionFromEvent(n[1]),o=(vectorDistance(r,i)-c)/c;t.dispatch("CROP_IMAGE_RESIZE",{value:o})}},t.ref.resizeEnd=function(e){if(t.ref.isActive){a.pop(e);var n=0===a.count();n&&(removeEvent$1(document.documentElement,"move",t.ref.resizeMove),removeEvent$1(document.documentElement,"up",t.ref.resizeEnd)),u&&(e.preventDefault(),n&&t.dispatch("CROP_IMAGE_RESIZE_RELEASE"))}},addEvent$1(document.documentElement,"down",t.ref.resizeStart);var l=performance.now(),s=0,d=1,f=throttle(function(e){var n=Math.sign(e.wheelDelta||e.deltaY),r=now(),i=r-l;l=r,(i>750||s!==n)&&(d=1,s=n),d+=.05*n,t.dispatch("CROP_IMAGE_RESIZE_MULTIPLY",{value:Math.max(.1,d)}),t.dispatch("CROP_IMAGE_RESIZE_RELEASE")},100);t.ref.wheel=function(e){t.ref.isActive&&/doka--/.test(e.target.className)&&isMyTarget(t.element,e.target)&&(e.preventDefault(),f(e))},document.addEventListener("wheel",t.ref.wheel,{passive:!1}),t.ref.move=function(e){if(t.ref.isActive&&(i.position.x=e.pageX-t.ref.state.offsetX,i.position.y=e.pageY-t.ref.state.scrollY-t.ref.state.offsetY,i.enabled))if(isMyTarget(t.element,e.target)){"idle"===t.element.dataset.state&&t.dispatch("RESIZER_SHOW",{position:_objectSpread({},i.position)}),e.preventDefault(),t.dispatch("RESIZER_MOVE",{position:_objectSpread({},i.position)});var r=n.pivotPoint,a=r.x-i.position.x,u=r.y-i.position.y,l={x:r.x+a,y:r.y+u},s=_objectSpread({},i.position);if(i.selecting){var d=(vectorDistance(l,s)-c)/c,f=performance.now();f-o>25&&(o=f,t.dispatch("CROP_IMAGE_RESIZE",{value:d}))}}else t.dispatch("RESIZER_CANCEL")},t.ref.select=function(e){if(t.ref.isActive&&isMyTarget(t.element,e.target)){var r=n.pivotPoint,o=r.x-i.position.x,a=r.y-i.position.y,u={x:r.x+o,y:r.y+a},l=i.position;c=vectorDistance(u,l),i.selecting=!0,i.origin.x=e.pageX,i.origin.y=e.pageY,t.dispatch("CROP_IMAGE_RESIZE_GRAB")}},t.ref.confirm=function(e){t.ref.isActive&&isMyTarget(t.element,e.target)&&(i.selecting=!1,t.dispatch("CROP_IMAGE_RESIZE_RELEASE"))},t.ref.blur=function(e){t.ref.isActive&&(i.selecting=!1,i.enabled=!1,document.removeEventListener("mousedown",t.ref.select),document.removeEventListener("mouseup",t.ref.confirm),t.dispatch("RESIZER_CANCEL"))},window.addEventListener("blur",t.ref.blur),document.addEventListener("mousemove",t.ref.move),t.ref.keyDown=function(e){t.ref.isActive&&r.includes(e.keyCode)&&i.position&&(i.enabled=!0,document.addEventListener("mousedown",t.ref.select),document.addEventListener("mouseup",t.ref.confirm),t.dispatch("RESIZER_SHOW",{position:_objectSpread({},i.position)}))},t.ref.keyUp=function(e){t.ref.isActive&&r.includes(e.keyCode)&&(i.enabled=!1,document.removeEventListener("mousedown",t.ref.select),document.removeEventListener("mouseup",t.ref.confirm),t.dispatch("RESIZER_CANCEL"))},document.body.addEventListener("keydown",t.ref.keyDown),document.body.addEventListener("keyup",t.ref.keyUp)},destroy:function(e){var t=e.root;document.removeEventListener("touchmove",t.ref.resizeMove),document.removeEventListener("touchend",t.ref.resizeEnd),document.removeEventListener("touchstart",t.ref.resizeStart),document.removeEventListener("wheel",t.ref.wheel),document.removeEventListener("mousemove",t.ref.move),document.removeEventListener("mousedown",t.ref.select),document.removeEventListener("mouseup",t.ref.confirm),document.body.removeEventListener("keydown",t.ref.keyDown),document.body.removeEventListener("keyup",t.ref.keyUp),window.removeEventListener("blur",t.ref.blur)},read:function(e){var t=e.root;t.ref.state.scrollY=window.scrollY;var n=t.element.getBoundingClientRect();t.ref.state.offsetX=n.x,t.ref.state.offsetY=n.y},write:createRoute({SHOW_VIEW:function(e){var t=e.root,n=e.action;t.ref.isActive="crop"===n.id},RESIZER_SHOW:function(e){var t=e.root,n=e.props,r=e.action;t.element.dataset.state="multi-touch",updateIndicators({root:t,props:n,action:r})},RESIZER_CANCEL:function(e){e.root.element.dataset.state="idle"},RESIZER_MOVE:updateIndicators})}),createGroup=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"group",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["opacity"],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return createView({ignoreRect:!0,name:e,mixins:{styles:["opacity"].concat(_toConsumableArray(t)),animations:_objectSpread({opacity:{type:"spring",stiffness:.25,damping:.5,mass:5}},n)},create:function(e){var t=e.root;e.props.controls.map(function(e){var n=t.createChildView(e.view,e);e.didCreateView&&e.didCreateView(n),t.appendChildView(n)})}})},list=createView({ignoreRect:!0,tag:"div",name:"dropdown-list",mixins:{styles:["translateY","opacity"],apis:["selectedValue"],animations:{translateY:"spring",opacity:{type:"tween",duration:250}}},create:function(e){var t=e.root,n=e.props;n.options.map(function(e){var r=t.createChildView(button,_objectSpread({},e,{action:function(){return t.dispatch(n.optionAction,{value:e.value})}}));return t.appendChildView(r)}),t.element.setAttribute("role","list"),t.ref.handleClick=function(){return n.action&&n.action()},t.element.addEventListener("click",t.ref.handleClick)},write:function(e){var t=e.root,n=e.props,r=(e.actions,n.options.findIndex(function(e){return e.value===n.selectedValue}));t.childViews.forEach(function(e,t){e.element.setAttribute("aria-selected",t===r)})},destroy:function(e){var t=e.root;t.element.removeEventListener("click",t.ref.handleClick)}}),dropdown=createView({ignoreRect:!0,tag:"div",name:"dropdown",mixins:{apis:["selectedValue"]},create:function(e){var t=e.root,n=e.props;t.ref.open=!1;var r=function(e){t.ref.open=e,t.dispatch(n.buttonAction)};t.ref.button=t.appendChildView(t.createChildView(button,_objectSpread({},n,{action:function(){r(!t.ref.open)}}))),t.ref.list=t.appendChildView(t.createChildView(list,_objectSpread({},n,{action:function(){r(!1)}}))),t.ref.handleBodyClick=function(e){t.element.contains(e.target)||r(!1)},t.element.addEventListener("focusin",function(e){e.target!==t.ref.button.element&&r(!0)}),t.element.addEventListener("focusout",function(e){t.element.contains(e.relatedTarget)||r(!1)}),document.body.addEventListener("click",t.ref.handleBodyClick)},destroy:function(e){var t=e.root;document.body.removeEventListener("click",t.ref.handleBodyClick)},write:function(e){var t=e.root,n=e.props;t.ref.list.opacity=t.ref.open?1:0,t.ref.list.translateY=t.ref.open?0:-5,t.ref.list.selectedValue=n.selectedValue}}),updateAspectRatioIcon=function(e,t){var n=e.element.querySelectorAll(".doka--icon-aspect-ratio rect");if(n.length){if(!t)return n[0].style.opacity=.2,n[1].style.opacity=.3,void(n[2].style.opacity=.4);n[0].style.opacity=t>1?1:.3,n[1].style.opacity=1===t?.85:.5,n[2].style.opacity=t<1?1:.3}},updateTurnIcons=function(e,t){Array.from(e.element.querySelectorAll(".doka--icon-turn rect")).forEach(function(e){t>1&&(e.setAttribute("x",e.previousElementSibling?5:4),e.setAttribute("width",9)),t<1&&(e.setAttribute("y",11),e.setAttribute("height",10))})},createRectangle=function(e){var t,n;e>1?(n=14,t=Math.round(n/e)):(t=14,n=Math.round(t*e));var r=Math.round(.5*(23-t)),i=Math.round(.5*(23-n));return'<svg width="23" height="23" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false"><g fill="currentColor"><rect x="'.concat(r,'" y="').concat(i,'" width="').concat(t,'" height="').concat(n,'" rx="2.5"/></g></svg>')},cropRoot=createView({name:"crop",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(e){var t=e.root,n=e.props;n.viewId="crop",n.hidden=!1,t.ref.isHiding=!1;var r=[{view:button,name:"tool",label:t.query("GET_LABEL_BUTTON_CROP_ROTATE_LEFT"),icon:createIcon('<g transform="translate(3 2)" fill="currentColor" fill-rule="evenodd" class="doka--icon-turn"><rect y="9" width="12" height="12" rx="1"/><path d="M9.823 5H11a5 5 0 0 1 5 5 1 1 0 0 0 2 0 7 7 0 0 0-7-7H9.626l.747-.747A1 1 0 0 0 8.958.84L6.603 3.194a1 1 0 0 0 0 1.415l2.355 2.355a1 1 0 0 0 1.415-1.414L9.823 5z" fill-rule="nonzero" /></g>',26),action:function(){return t.dispatch("CROP_IMAGE_ROTATE_LEFT")}},{view:button,name:"tool",label:t.query("GET_LABEL_BUTTON_CROP_FLIP_HORIZONTAL"),icon:createIcon('<g fill="currentColor" fill-rule="evenodd"><path d="M11.93 7.007V20a1 1 0 0 1-1 1H5.78a1 1 0 0 1-.93-1.368l5.15-12.993a1 1 0 0 1 1.929.368z"/><path d="M14 7.007V20a1 1 0 0 0 1 1h5.149a1 1 0 0 0 .93-1.368l-5.15-12.993A1 1 0 0 0 14 7.007z" opacity=".6"/></g>',26),action:function(){return t.dispatch("CROP_IMAGE_FLIP_HORIZONTAL")}},{view:button,name:"tool",label:t.query("GET_LABEL_BUTTON_CROP_FLIP_VERTICAL"),icon:createIcon('<g fill="currentColor" fill-rule="evenodd"><path d="M19.993 12.143H7a1 1 0 0 1-1-1V5.994a1 1 0 0 1 1.368-.93l12.993 5.15a1 1 0 0 1-.368 1.93z"/><path d="M19.993 14a1 1 0 0 1 .368 1.93L7.368 21.078A1 1 0 0 1 6 20.148V15a1 1 0 0 1 1-1h12.993z" opacity=".6"/></g></svg>',26),action:function(){return t.dispatch("CROP_IMAGE_FLIP_VERTICAL")}}],i=t.query("GET_CROP_ASPECT_RATIO_OPTIONS");i&&r.push({view:dropdown,name:"tool",label:t.query("GET_LABEL_BUTTON_CROP_ASPECT_RATIO"),icon:createIcon('<g class="doka--icon-aspect-ratio" fill="currentColor" fill-rule="evenodd"><rect x="2" y="4" opacity=".3" width="10" height="18" rx="1"/><rect opacity=".5" x="4" y="8" width="14" height="14" rx="1"/><rect x="6" y="12" width="17" height="10" rx="1"/></g>',26),buttonAction:"TOGGLE_ASPECT_RATIO",optionAction:"CROP_SET_ASPECT_RATIO",options:i.map(function(e){return _objectSpread({},e,{icon:createRectangle(e.value)})}),didCreateView:function(e){t.ref.aspectRatioDropdown=e}}),t.query("GET_CROP_ALLOW_IMAGE_TURN_RIGHT")&&r.splice(1,0,{view:button,name:"tool",label:t.query("GET_LABEL_BUTTON_CROP_ROTATE_RIGHT"),icon:createIcon('<g transform="translate(5 2)" fill="currentColor" fill-rule="evenodd" class="doka--icon-turn"><path d="M8.177 5H7a5 5 0 0 0-5 5 1 1 0 0 1-2 0 7 7 0 0 1 7-7h1.374l-.747-.747A1 1 0 0 1 9.042.84l2.355 2.355a1 1 0 0 1 0 1.415L9.042 6.964A1 1 0 0 1 7.627 5.55l.55-.55z" fill-rule="nonzero"/><rect x="6" y="9" width="12" height="12" rx="1"/></g>',26),action:function(){return t.dispatch("CROP_IMAGE_ROTATE_RIGHT")}}),t.ref.menu=t.appendChildView(t.createChildView(createGroup("toolbar",["opacity"],{opacity:{type:"spring",mass:15,delay:50}}),{opacity:0,controls:r})),t.menuItemsRequiredWidth=null,t.ref.subject=t.appendChildView(t.createChildView(cropSubject,_objectSpread({},n))),t.ref.rotator=t.appendChildView(t.createChildView(cropRotator,{rotation:0,opacity:0,translateY:20,id:n.id})),t.ref.resizer=t.appendChildView(t.createChildView(cropResize,{pivotPoint:{x:0,y:0}}));var o=0,a=0;t.ref.keyboard=createKeyboard(t.element,function(){return o=0,a=0,{x:0,y:0}},{up:function(e){e.y-=20},down:function(e){e.y+=20},left:function(e){e.x-=20},right:function(e){e.x+=20},plus:function(){o+=.1,t.dispatch("CROP_IMAGE_RESIZE_AMOUNT",{value:o}),t.dispatch("CROP_IMAGE_RESIZE_RELEASE")},minus:function(){o-=.1,t.dispatch("CROP_IMAGE_RESIZE_AMOUNT",{value:o}),t.dispatch("CROP_IMAGE_RESIZE_RELEASE")},left_bracket:function(){a-=Math.PI/128,t.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:a})},right_bracket:function(){a+=Math.PI/128,t.dispatch("CROP_IMAGE_ROTATE_ADJUST",{value:a})},h:function(){t.dispatch("CROP_IMAGE_FLIP_HORIZONTAL")},l:function(){t.dispatch("CROP_IMAGE_ROTATE_LEFT")},q:function(){t.dispatch("CROP_RESET")},r:function(){t.dispatch("CROP_IMAGE_ROTATE_RIGHT")},v:function(){t.dispatch("CROP_IMAGE_FLIP_VERTICAL")},z:function(){t.dispatch("CROP_ZOOM")}},function(e){e&&t.dispatch("CROP_IMAGE_DRAG",{value:e})},function(e){e&&t.dispatch("CROP_IMAGE_DRAG_RELEASE")})},read:function(e){var t=e.root,n=e.props;if(null===t.menuItemsRequiredWidth){var r=t.ref.menu.childViews.reduce(function(e,t){return e+t.rect.outer.width},0);t.menuItemsRequiredWidth=0===r?null:r}var i=t.ref.subject.rect.element,o=i.left,a=i.top,c=i.width,u=i.height;n.stagePosition={x:o,y:a,width:c,height:u}},write:createRoute({SHOW_VIEW:function(e){var t=e.root,n=e.action,r=e.props,i=t.ref,o=i.menu,a=i.rotator,c=i.subject;r.viewId===n.id?(c.opacity=1,o.opacity=1,a.opacity=1,a.translateY=0,r.hidden=!1,t.ref.isHiding=!1):(c.opacity=0,o.opacity=0,a.opacity=0,a.translateY=20,t.ref.isHiding=!0)},UNLOAD_IMAGE:function(e){var t=e.root.ref,n=t.menu,r=t.rotator;n.opacity=0,r.opacity=0,r.translateY=20},DID_PRESENT_IMAGE:function(e){var t=e.root,n=t.ref,r=n.menu,i=n.rotator;r.opacity=1,i.opacity=1,i.translateY=0;var o=t.query("GET_IMAGE");updateTurnIcons(t,o.height/o.width)}},function(e){var t=e.root,n=e.props,r=e.timestamp,i=t.ref,o=i.resizer,a=i.subject,c=i.menu,u=i.rotator,l=i.isHiding,s=i.aspectRatioDropdown,d=n.hidden,f=0===a.opacity&&0===c.opacity&&0===u.opacity;if(!d&&l&&f&&(n.hidden=!0),!n.hidden){var p=t.query("GET_CROP",n.id,r);if(p){if(s){var h=t.query("GET_ACTIVE_CROP_ASPECT_RATIO");s.selectedValue!==h&&(updateAspectRatioIcon(t,h),s&&(s.selectedValue=h))}o.pivotPoint={x:.5*o.rect.element.width,y:.5*o.rect.element.height},u.animate=!p.isDraft,u.rotation=p.rotation.sub,c.element.dataset.layout=t.menuItemsRequiredWidth>t.ref.menu.rect.element.width?"compact":"spacious"}}})}),sizeInput=createView({name:"size-input",mixins:{listeners:!0,apis:["id","value","placeholder","getValue","setValue","setPlaceholder","hasFocus","onChange"]},create:function(e){var t=e.root,n=e.props,r=n.id,i=n.min,o=n.max,a=n.value,c=n.placeholder,u=n.onChange,l=void 0===u?function(){}:u,s=n.onBlur,d=void 0===s?function(){}:s,f="doka--".concat(r),p=createElement("input",{type:"number",step:1,id:f,min:i,max:o,value:a,placeholder:c}),h=p.getAttribute("max").length,m=createElement("label",{for:f});m.textContent=n.label;var g=function(e,t,n){return isString(e)?((e=e.replace(/[^0-9]/g,"")).length>h&&(e=e.slice(0,h)),e=parseInt(e,10)):e=Math.round(e),Number.isNaN(e)?null:limit(e,t,n)},v=function(e){return e.length?parseInt(p.value,10):null};t.ref.handleInput=function(){p.value=g(p.value,1,o),l(v(p.value))},t.ref.handleBlur=function(){p.value=g(p.value,i,o),d(v(p.value))},p.addEventListener("input",t.ref.handleInput),p.addEventListener("blur",t.ref.handleBlur),t.appendChild(p),t.appendChild(m),t.ref.input=p,n.hasFocus=function(){return p===document.activeElement},n.getValue=function(){return v(p.value)},n.setValue=function(e){return p.value=e?g(e,1,999999):null},n.setPlaceholder=function(e){return p.placeholder=e}},destroy:function(e){var t=e.root;t.ref.input.removeEventListener("input",t.ref.handleInput),t.ref.input.removeEventListener("blur",t.ref.handleBlur)}}),checkboxInput=createView({name:"checkable",tag:"span",mixins:{listeners:!0,apis:["id","checked","onChange","onSetValue","setValue","getValue"]},create:function(e){var t=e.root,n=e.props,r=n.id,i=n.checked,o=n.onChange,a=void 0===o?function(){}:o,c=n.onSetValue,u=void 0===c?function(){}:c,l=createElement("input",{type:"checkbox",value:1,id:r});l.checked=i,t.ref.input=l;var s=createElement("label",{for:r});s.innerHTML=n.label,t.appendChild(l),t.appendChild(s),t.ref.handleChange=function(){u(l.checked),a(l.checked)},l.addEventListener("change",t.ref.handleChange),n.getValue=function(){return l.checked},n.setValue=function(e){l.checked=e,u(l.checked)},setTimeout(function(){u(l.checked)},0)},destroy:function(e){var t=e.root;t.ref.input.removeEventListener("change",t.ref.handleChange)}}),testResult=null,isIOS=function(){return null===testResult&&(testResult=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream),testResult},testResult$1=null,isAndroid=function(){return null===testResult$1&&(testResult$1=/Android/i.test(navigator.userAgent)),testResult$1},resizeForm=createView({ignoreRect:!0,name:"resize-form",tag:"form",mixins:{styles:["opacity"],animations:{opacity:{type:"spring",mass:15,delay:150}}},create:function(e){var t=e.root;t.element.setAttribute("novalidate","novalidate"),t.element.setAttribute("action","#"),t.ref.shouldBlurKeyboard=isIOS()||isAndroid();var n=t.query("GET_SIZE_MAX"),r=t.query("GET_SIZE_MIN"),i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.axisLock,o=void 0===i?"none":i,a=e.enforceLimits,c=void 0!==a&&a,u=t.ref,l=u.inputImageWidth,s=u.inputImageHeight,d=u.buttonConfirm,f=t.query("GET_SIZE_ASPECT_RATIO_LOCK"),p=t.query("GET_CROP_RECTANGLE_ASPECT_RATIO"),h={width:l.getValue(),height:s.getValue()},m=limitSize(h,c?r:{width:1,height:1},c?n:{width:999999,height:999999},f?p:null,o);if(f)"width"===o?s.setValue(m.width/p):"height"===o?l.setValue(m.height*p):(l.setValue(m.width||m.height*p),s.setValue(m.height||m.width/p));else if(m.width&&!m.height){var g=Math.round(m.width/p),v=limitSize({width:m.width,height:g},c?r:{width:1,height:1},c?n:{width:999999,height:999999},p,o);c&&l.setValue(Math.round(v.width)),s.setPlaceholder(Math.round(v.height))}else if(m.height&&!m.width){var y=Math.round(m.height*p);l.setPlaceholder(y)}var E=t.query("GET_SIZE_INPUT"),T=E.width,_=E.height,w=isNumber(T)?Math.round(T):null,R=isNumber(_)?Math.round(_):null,I=l.getValue(),C=s.getValue(),A=I!==w||C!==R;return d.opacity=A?1:0,t.dispatch("KICK"),{width:l.getValue(),height:s.getValue()}},o=t;t.appendChildView(t.createChildView(createFieldGroup("Image size",function(e){var t=e.root,a=t.query("GET_SIZE"),c=t.appendChildView(t.createChildView(sizeInput,{id:"image-width",label:"Width",value:isNumber(a.width)?Math.round(a.width):null,min:r.width,max:n.width,placeholder:0,onChange:function(){return i({axisLock:"width"})},onBlur:function(){return i({enforceLimits:!1})}})),u=t.appendChildView(t.createChildView(checkboxInput,{id:"aspect-ratio-lock",label:createIcon('<g fill="none" fill-rule="evenodd"><path stroke="currentColor" stroke-width="1.5" stroke-linecap="round" class="doka--aspect-ratio-lock-ring" d="M9.401 10.205v-.804a2.599 2.599 0 0 1 5.198 0V14"/><rect fill="currentColor" x="7" y="10" width="10" height="7" rx="1.5"/></g>'),checked:t.query("GET_SIZE_ASPECT_RATIO_LOCK"),onSetValue:function(e){var t=e?0:-3;u.element.querySelector(".doka--aspect-ratio-lock-ring").setAttribute("transform","translate(0 ".concat(t,")"))},onChange:function(e){t.dispatch("RESIZE_SET_OUTPUT_SIZE_ASPECT_RATIO_LOCK",{value:e}),i()}})),l=t.appendChildView(t.createChildView(sizeInput,{id:"image-height",label:"Height",value:isNumber(a.height)?Math.round(a.height):null,min:r.height,max:n.height,placeholder:0,onChange:function(){return i({axisLock:"height"})},onBlur:function(){return i({enforceLimits:!1})}}));o.ref.aspectRatioLock=u,o.ref.inputImageWidth=c,o.ref.inputImageHeight=l}))),t.ref.buttonConfirm=t.appendChildView(t.createChildView(button,{name:"app action-confirm icon-only",label:"Apply",action:function(){},opacity:0,icon:createIcon('><polyline fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" points="20 6 9 17 4 12"></polyline>'),type:"submit"})),t.ref.confirmForm=function(e){var n=i({enforceLimits:!0});e.preventDefault();var r=t.ref,o=r.shouldBlurKeyboard,a=r.buttonConfirm;o&&(document.activeElement.blur(),a.element.focus()),a.opacity=0,t.dispatch("RESIZE_SET_OUTPUT_SIZE",n)},t.element.addEventListener("submit",t.ref.confirmForm)},destroy:function(e){var t=e.root;t.element.removeEventListener("submit",t.ref.confirmForm)},write:createRoute({EDIT_RESET:function(e){var t=e.root.ref,n=t.inputImageWidth,r=t.inputImageHeight,i=t.aspectRatioLock,o=t.buttonConfirm;n.setValue(null),r.setValue(null),i.setValue(!0),o.opacity=0},RESIZE_SET_OUTPUT_SIZE:function(e){var t=e.root,n=e.action,r=t.ref,i=r.inputImageWidth,o=r.inputImageHeight;i.setValue(n.width),o.setValue(n.height)},CROP_SET_ASPECT_RATIO:function(e){var t=e.root,n=e.props,r=e.action,i=e.timestamp,o=t.query("GET_CROP",n.id,i);if(o){var a=o.cropStatus,c=t.ref,u=c.inputImageWidth,l=c.inputImageHeight;null!==r.value?(u.setValue(a.image.width),u.setPlaceholder(a.crop.width),l.setValue(a.image.height),l.setPlaceholder(a.crop.height)):u.getValue()&&l.getValue()&&(l.setValue(null),l.setPlaceholder(a.crop.height))}}},function(e){var t=e.root,n=e.props,r=e.timestamp,i=t.query("GET_CROP",n.id,r);if(i){var o=i.cropStatus,a=t.ref,c=a.inputImageWidth,u=a.inputImageHeight;if(!c.hasFocus()&&!u.hasFocus()){var l=t.query("GET_CROP_RECTANGLE_ASPECT_RATIO");if(null===c.getValue()&&null===u.getValue())c.setPlaceholder(o.crop.width),u.setPlaceholder(o.crop.height);else if(null===c.getValue()&&null!==o.image.height){var s=Math.round(o.image.height*l);c.setPlaceholder(s)}else if(null===u.getValue()&&null!==o.image.width){var d=Math.round(o.image.width/l);u.setPlaceholder(d)}}}})}),createFieldGroup=function(e,t){return createView({tag:"fieldset",create:function(n){var r=n.root,i=createElement("legend");i.textContent=e,r.element.appendChild(i),t({root:r})}})},resizeRoot=createView({name:"resize",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(e){var t=e.root,n=e.props;n.viewId="resize",n.hidden=!1,t.ref.isHiding=!1,t.ref.form=t.appendChildView(t.createChildView(resizeForm,{opacity:0,id:n.id}))},read:function(e){var t=e.root,n=e.props,r=t.ref.form.rect,i=t.rect;n.stagePosition={x:i.element.left,y:i.element.top+r.element.height,width:i.element.width,height:i.element.height-r.element.height}},write:createRoute({SHOW_VIEW:function(e){var t=e.root,n=e.action,r=e.props;n.id===r.viewId?(t.ref.isHiding=!1,t.ref.form.opacity=1,t.dispatch("CROP_ZOOM")):(t.ref.isHiding=!0,t.ref.form.opacity=0)}},function(e){var t=e.root,n=e.props,r=t.ref,i=r.form,o=r.isHiding,a=n.hidden;o&&0===i.opacity&&!a&&(n.hidden=!0)})}),tilePreviewWorker=null,tilePreviewWorkerTerminationTimeout=null,createFilterTile=function(e){return createView({ignoreRect:!0,tag:"li",name:"filter-tile",mixins:{styles:["opacity","translateY"],animations:{translateY:{type:"spring",delay:10*e},opacity:{type:"spring",delay:30*e}}},create:function(e){var t=e.root,n=e.props,r="filter-".concat(n.style),i=createElement("input",{id:r,type:"radio",name:"filter"});t.appendChild(i),i.checked=n.selected,i.value=n.style,i.addEventListener("change",function(e){i.checked&&n.onSelect()});var o=createElement("label",{for:r});o.textContent=n.label,t.appendChild(o);var a=n.imageData,c=createElement("canvas");c.width=a.width,c.height=a.height;var u=c.getContext("2d");t.appendChild(c),n.matrix?(tilePreviewWorker||(tilePreviewWorker=createWorker$1(TransformWorker)),clearTimeout(tilePreviewWorkerTerminationTimeout),tilePreviewWorker.post({transforms:[{type:"filter",data:n.matrix}],imageData:a},function(e){u.putImageData(e,0,0),clearTimeout(tilePreviewWorkerTerminationTimeout),tilePreviewWorkerTerminationTimeout=setTimeout(function(){tilePreviewWorker.terminate(),tilePreviewWorker=null},1e3)},[a.data.buffer])):u.putImageData(a,0,0)}})},filterList=createView({ignoreRect:!0,tag:"ul",name:"filter-list",mixins:{apis:["visible"]},create:function(e){var t=e.root,n=e.props;t.element.setAttribute("role","list"),t.ref.tiles=[],t.ref.isHiding=!1;var r=t.query("GET_THUMB_IMAGE_DATA"),i=t.query("GET_FILTERS"),o=[];forin(i,function(e,t){o.push(_objectSpread({id:e},t))}),t.ref.activeFilter=t.query("GET_FILTER"),t.ref.tiles=o.map(function(e,i){var o=e.matrix(),a=t.ref.activeFilter===e.id||arrayEqual(t.ref.activeFilter,o)||0===i;return t.appendChildView(t.createChildView(createFilterTile(i),{opacity:0,translateY:-5,id:n.id,style:e.id,label:e.label,matrix:o,imageData:cloneImageData(r),selected:a,onSelect:function(){return t.dispatch("FILTER_SET_FILTER",{value:o?e.id:null})}}))})},write:function(e){var t=e.root,n=t.query("GET_FILTER");if(n!==t.ref.activeFilter){t.ref.activeFilter=n;var r=t.query("GET_FILTERS"),i=n?isString(n)?n:isColorMatrix(n)?Object.keys(r).find(function(e){return arrayEqual(r[e].matrix(),n)}):null:"original";Array.from(t.element.querySelectorAll("input")).forEach(function(e){return e.checked=e.value===i})}t.query("IS_ACTIVE_VIEW","filter")?t.ref.tiles.forEach(function(e){e.opacity=1,e.translateY=0}):t.ref.tiles.forEach(function(e){e.opacity=0,e.translateY=-5})}}),filterRoot=createView({name:"filter",ignoreRect:!0,mixins:{apis:["viewId","stagePosition","hidden"]},create:function(e){var t=e.root,n=e.props;n.viewId="filter",n.hidden=!1,t.ref.isHiding=!1,t.ref.filters=t.appendChildView(t.createChildView(filterList,{id:n.id}))},read:function(e){var t=e.root,n=e.props;if(t.ref.filters){var r=t.rect,i=t.ref.filters.rect,o=0===i.element.top,a=o?r.element.top+i.element.height:r.element.top,c=o?r.element.height-i.element.height:r.element.height-i.element.height-r.element.top;n.stagePosition={x:r.element.left+i.element.left,y:a,width:r.element.width-i.element.left,height:c}}},write:createRoute({SHOW_VIEW:function(e){var t=e.root,n=e.action,r=e.props;t.ref.filters&&(n.id===r.viewId?(t.ref.isHiding=!1,t.ref.filters.visible=!0):(t.ref.isHiding=!0,t.ref.filters.visible=!1))}})}),hasStagePositionChanged=function(e,t){return!e||!t||!rectEqualsRect(e,t)},VIEW_MAP={crop:cropRoot,resize:resizeRoot,filter:filterRoot},viewStack=createView({name:"view-stack",ignoreRect:!0,create:function(e){var t=e.root;t.ref.activeView=null,t.ref.activeStagePosition=null,t.ref.shouldFocus=!1},write:createRoute({SHOW_VIEW:function(e){var t=e.root,n=e.props,r=e.action,i=t.childViews.find(function(e){return e.viewId===r.id});i||(i=t.appendChildView(t.createChildView(VIEW_MAP[r.id],_objectSpread({},n)))),t.ref.activeView=i,t.childViews.map(function(e){return e.element}).forEach(function(e){e.dataset.viewActive="false",e.removeAttribute("tabindex")});var o=t.ref.activeView.element;o.dataset.viewActive="true",o.setAttribute("tabindex",-1),t.ref.shouldFocus=!0},DID_PRESENT_IMAGE:function(e){var t=e.root;t.dispatch("CHANGE_VIEW",{id:t.query("GET_UTILS")[0]})}},function(e){var t=e.root,n=t.ref,r=n.activeView,i=n.previousStagePosition;if(r&&r.stagePosition&&(t.ref.shouldFocus&&(t.ref.activeView.element.focus({preventScroll:!0}),t.ref.shouldFocus=!1),hasStagePositionChanged(r.stagePosition,i))){var o=r.stagePosition,a=o.x,c=o.y,u=o.width,l=o.height;if(0===u&&0===l)return;t.dispatch("DID_RESIZE_STAGE",{offset:{x:a,y:c},size:{width:u,height:l},animate:!0}),t.ref.previousStagePosition=r.stagePosition}})}),editContent=createView({name:"content",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:250}}},create:function(e){var t=e.root,n=e.props;t.opacity=1,t.ref.viewStack=t.appendChildView(t.createChildView(viewStack,{id:n.id})),t.ref.image=null},write:createRoute({DID_PRESENT_IMAGE:function(e){e.root.ref.image.opacity=1},DID_LOAD_IMAGE:function(e){var t=e.root,n=e.props;t.ref.image=t.appendChildView(t.createChildView(image,{id:n.id}))}},function(e){var t=e.root,n=t.ref.image;n&&(n.offsetTop=t.rect.element.top)})}),updateResizeButton=function(e,t){e.element.dataset.scaleDirection=null===t||t>1?"up":"down"},editContainer=createView({name:"container",create:function(e){var t,n=e.root,r=[(t={view:button,didCreateView:function(e){return n.ref.btnReset=e},opacity:0,label:n.query("GET_LABEL_BUTTON_RESET")},_defineProperty(t,"didCreateView",function(e){return n.ref.btnReset=e}),_defineProperty(t,"name","app action-reset icon-only"),_defineProperty(t,"icon",createIcon('<g fill="currentColor" fill-rule="nonzero"><path d="M6.036 13.418L4.49 11.872A.938.938 0 1 0 3.163 13.2l2.21 2.209a.938.938 0 0 0 1.326 0l2.209-2.21a.938.938 0 0 0-1.327-1.326l-1.545 1.546zM12 10.216a1 1 0 0 1 2 0V13a1 1 0 0 1-2 0v-2.784z"/><path d="M15.707 14.293a1 1 0 0 1-1.414 1.414l-2-2a1 1 0 0 1 1.414-1.414l2 2z"/><path d="M8.084 19.312a1 1 0 0 1 1.23-1.577 6 6 0 1 0-2.185-3.488 1 1 0 0 1-1.956.412 8 8 0 1 1 2.912 4.653z"/></g>',26)),_defineProperty(t,"action",function(){return n.dispatch("EDIT_RESET")}),t)];n.query("GET_ALLOW_BUTTON_CANCEL")&&r.unshift({view:button,label:n.query("GET_LABEL_BUTTON_CANCEL"),name:"app action-cancel icon-fallback",opacity:1,icon:createIcon('<g fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></g>'),didCreateView:function(e){n.ref.btnCancel=e},action:function(){n.dispatch("EDIT_CANCEL")}}),n.ref.utilViews=[];var i=[],o={crop:{title:n.query("GET_LABEL_BUTTON_UTIL_CROP"),icon:createIcon('<g fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round" stroke="#FFF" stroke-width="2"><path d="M23 17H9a2 2 0 0 1-2-2v-5m0-3V1"/><path d="M1 7h14a2 2 0 0 1 2 2v7m0 4v3"/></g>')},resize:{title:n.query("GET_LABEL_BUTTON_UTIL_RESIZE"),icon:createIcon('<g fill="none" fill-rule="evenodd" stroke-width="2" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="12" width="10" height="10" rx="2"/><path d="M4 11.5V4a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5"/><path d="M14 10l3.365-3.365M14 6h4v4" class="doka--icon-resize-arrow-ne"/><path d="M14 10l3.365-3.365M14 6v4h4" class="doka--icon-resize-arrow-sw"/></g>')},filter:{title:n.query("GET_LABEL_BUTTON_UTIL_FILTER"),icon:createIcon('<g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18.347 9.907a6.5 6.5 0 1 0-1.872 3.306M3.26 11.574a6.5 6.5 0 1 0 2.815-1.417"/><path d="M10.15 17.897A6.503 6.503 0 0 0 16.5 23a6.5 6.5 0 1 0-6.183-8.51"/></g>')}};if(n.query("GET_UTILS").forEach(function(e){var t=o[e];t&&i.push(_objectSpread({id:e},t))}),i.length>1){var a=createGroup("utils");r.push({view:a,opacity:1,controls:i.map(function(e){return{view:button,label:e.title,name:"tab",opacity:1,icon:e.icon,id:e.id,didCreateView:function(t){n.ref.utilViews.push(t),n.ref["util_button_".concat(e.id)]=t},action:function(){return n.dispatch("CHANGE_VIEW",{id:e.id})}}})})}n.query("GET_ALLOW_BUTTON_CONFIRM")&&r.push({view:button,label:n.query("GET_LABEL_BUTTON_CONFIRM"),name:"app action-confirm icon-fallback",opacity:1,icon:createIcon('<polyline fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" points="20 6 9 17 4 12"></polyline>'),didCreateView:function(e){n.ref.btnConfirm=e},action:function(){n.dispatch("EDIT_CONFIRM")}}),n.ref.menu=n.appendChildView(n.createChildView(createGroup("menu"),{controls:r})),n.ref.menu.opacity=0,n.ref.status=n.appendChildView(n.createChildView(editStatus)),n.dispatch("AWAIT_IMAGE"),n.ref.handleFocusOut=function(){var e=n.ref.status;"busy"===e.element.dataset.viewStatus&&e.element.focus()},n.ref.handleFocusIn=function(e){var t=n.ref,r=t.menu,i=t.content,o=e.target;if(!r.element.contains(o)&&i&&i.element.contains(o)){if(!Array.from(n.element.querySelectorAll("[data-view-active=false]")).reduce(function(e,t){return t.contains(o)&&(e=!0),e},!1))return;r.element.querySelector("button,input,[tabindex]").focus()}},n.element.addEventListener("focusin",n.ref.handleFocusIn),n.element.addEventListener("focusout",n.ref.handleFocusOut)},destroy:function(e){var t=e.root;t.element.removeEventListener("focusin",t.ref.handleFocusIn),t.element.removeEventListener("focusout",t.ref.handleFocusOut)},write:createRoute({SHOW_VIEW:function(e){var t=e.root,n=e.action;t.ref.utilViews.forEach(function(e){return e.element.dataset.active=e.id===n.id})},UNLOAD_IMAGE:function(e){var t=e.root;t.ref.content&&(t.ref.content.opacity=0,t.ref.menu.opacity=0)},DID_UNLOAD_IMAGE:function(e){var t=e.root;t.removeChildView(t.ref.content),t.ref.content=null},DID_LOAD_IMAGE:function(e){var t=e.root,n=e.props;t.ref.content=t.appendChildView(t.createChildView(editContent,{opacity:null,id:n.id})),t.ref.menu.opacity=1}},function(e){var t=e.root,n=e.props,r=e.timestamp,i=t.query("GET_CROP",n.id,r);if(i){var o=t.ref,a=o.btnCancel,c=o.content,u=o.util_button_resize,l=i.canReset,s=i.cropStatus;if(t.ref.btnReset.opacity=l?1:0,a&&t.query("GET_UTILS").length>1){var d=t.query("GET_ROOT");a.opacity=l&&d.width<600?0:1}u&&updateResizeButton(u,s.image.width?s.image.width/s.crop.width:null),c&&0===c.opacity&&t.dispatch("DID_UNLOAD_IMAGE")}})}),createPointerEvents=function(e){var t={destroy:function(){}};if("onpointerdown"in window)return t;var n=0,r=[],i=function(e,t,n){var r=new UIEvent(t.type,{view:window,bubbles:!n});Object.keys(t).forEach(function(e){Object.defineProperty(r,e,{value:t[e],writable:!1})}),e.dispatchEvent(r)},o=function(e,t,o){return Array.from(t.changedTouches).map(function(a){var c=r[a.identifier],u={type:e,pageX:a.pageX,pageY:a.pageY,pointerId:a.identifier,isPrimary:c?c.isPrimary:0===n,preventDefault:function(){return t.preventDefault()}};return i(a.target,u,o),u})},a=function(e){o("pointerdown",e).forEach(function(e){r[e.pointerId]=e,n++})},c=function(e){o("pointermove",e)},u=function(e){o("pointerup",e).forEach(function(e){delete r[e.pointerId],n--})},l=function(e,t,n){var r={type:e,pageX:t.pageX,pageY:t.pageY,pointerId:0,isPrimary:!0,preventDefault:function(){return t.preventDefault()}};return i(t.target,r,n),r},s=function(e){l("pointerdown",e)},d=function(e){l("pointermove",e)},f=function(e){l("pointerup",e)};return"ontouchstart"in window?(e.addEventListener("touchstart",a),e.addEventListener("touchmove",c),e.addEventListener("touchend",u)):"onmousedown"in window&&(e.addEventListener("mousedown",s),e.addEventListener("mousemove",d),e.addEventListener("mouseup",f)),t.destroy=function(){r.length=0,e.removeEventListener("touchstart",a),e.removeEventListener("touchmove",c),e.removeEventListener("touchend",u),e.removeEventListener("mousedown",s),e.removeEventListener("mousemove",d),e.removeEventListener("mouseup",f)},t},prevent=function(e){return e.preventDefault()},editor=createView({name:"editor",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:350}},apis:["markedForRemoval"]},create:function(e){var t=e.root,n=e.props;n.markedForRemoval=!1,t.element.addEventListener("touchmove",prevent,{passive:!1}),t.element.addEventListener("gesturestart",prevent),t.ref.pointerPolyfill=createPointerEvents(t.element),t.appendChildView(t.createChildView(editContainer,_objectSpread({},n)))},destroy:function(e){var t=e.root;t.ref.pointerPolyfill.destroy(),t.element.removeEventListener("touchmove",prevent,!0),t.element.removeEventListener("gesturestart",prevent)}}),createTouchDetector=function(){function e(){t.fire("touch-detected"),window.removeEventListener("touchstart",e,!1)}var t=_objectSpread({},on(),{destroy:function(){window.removeEventListener("touchstart",e,!1)}});return window.addEventListener("touchstart",e,!1),t},createFileCatcher=function(e){var t=_objectSpread({},on(),{destroy:function(){e.removeEventListener("dragover",n),e.removeEventListener("drop",r)}}),n=function(e){return e.preventDefault()},r=function(e){e.preventDefault();var n=Array.from(e.dataTransfer.items||e.dataTransfer.files).map(function(e){return e.getAsFile&&"file"===e.kind?e.getAsFile():e});t.fire("drop",n)};return e.addEventListener("dragover",n),e.addEventListener("drop",r),t},createFocusTrap=function(e){var t=function(t){if(9===t.keyCode){var n=Array.from(e.querySelectorAll("button,input,[tabindex]")).filter(function(e){return"hidden"!==e.style.visibility&&-1!==e.tabIndex}),r=n[0],i=n[n.length-1];t.shiftKey?document.activeElement===r&&(i.focus(),t.preventDefault()):document.activeElement===i&&(r.focus(),t.preventDefault())}};return e.addEventListener("keydown",t),{destroy:function(){e.removeEventListener("keydown",t)}}},isFullscreen=function(e){return e.ref.isFullscreen},shouldBeFullscreen=function(e){return/fullscreen/.test(e.query("GET_STYLE_LAYOUT_MODE"))},isFloating=function(e){return/fullscreen|preview/.test(e.query("GET_STYLE_LAYOUT_MODE"))},mayBeAutoClosed=function(e){return e.query("GET_ALLOW_AUTO_CLOSE")},canBeAutoClosed=isFloating,canBeClosed=isFloating,updateStyleViewport=function(e){var t=e.ref,n=t.environment,r=t.isSingleUtil,i=t.canBeControlled;e.element.dataset.styleViewport=getViewportBySize(e.rect.element.width,e.rect.element.height)+" "+n.join(" ")+(r?" single-util":" multi-util")+(i?" flow-controls":" no-flow-controls")},setupFullscreenMode=function(e){var t=e.element,n=e.ref,r=n.handleFullscreenUpdate,i=n.handleEscapeKey;t.setAttribute("tabindex",-1),r(),e.ref.focusTrap=createFocusTrap(t),t.addEventListener("keydown",i),window.addEventListener("resize",r),document.body.classList.add("doka--parent"),document.body.appendChild(t),document.body.style.minHeight="512px";var o=document.querySelector("meta[name=viewport]");e.ref.defaultViewportContent=o?o.getAttribute("content"):null,o||((o=document.createElement("meta")).setAttribute("name","viewport"),document.head.appendChild(o)),o.setAttribute("content","width=device-width, height=device-height, initial-scale=1, maximum-scale=1, user-scalable=0"),e.opacity=1,e.element.contains(document.activeElement)||t.focus(),e.dispatch("INVALIDATE_VIEWPORT"),e.ref.isFullscreen=!0},cleanFullscreenMode=function(e){var t=e.element,n=e.ref,r=n.handleFullscreenUpdate,i=n.focusTrap,o=n.handleEscapeKey;t.removeAttribute("tabindex"),i.destroy(),t.removeEventListener("keydown",o),window.removeEventListener("resize",r),document.body.classList.remove("doka--parent");var a=document.querySelector("meta[name=viewport]");e.ref.defaultViewportContent?(a.setAttribute("content",e.ref.defaultViewportContent),e.ref.defaultViewportContent=null):a.parentNode.removeChild(a),e.ref.isFullscreen=!1},root=createView({name:"root",ignoreRect:!0,mixins:{styles:["opacity"],animations:{opacity:{type:"tween",duration:350}}},create:function(e){var t=e.root,n=e.props;t.element.id=t.query("GET_ID")||"doka-".concat(n.id),t.ref.environment=[],t.ref.shouldBeDestroyed=!1,t.ref.isClosing=!1,t.ref.isClosed=!1,t.ref.isFullscreen=!1,t.query("GET_ALLOW_DROP_FILES")&&(t.ref.catcher=createFileCatcher(t.element),t.ref.catcher.on("drop",function(e){e.forEach(function(e){t.dispatch("REQUEST_LOAD_IMAGE",{source:e})})})),t.ref.touchDetector=createTouchDetector(),t.ref.touchDetector.onOnce("touch-detected",function(){t.ref.environment.push("touch")}),t.ref.editor=t.appendChildView(t.createChildView(editor,{id:n.id})),t.query("GET_STYLES").filter(function(e){return!isEmpty(e.value)}).map(function(e){var n=e.name,r=e.value;t.element.dataset[n]=r}),t.ref.updateViewport=function(){t.dispatch("INVALIDATE_VIEWPORT")},window.addEventListener("resize",t.ref.updateViewport),t.ref.isSingleUtil=1===t.query("GET_UTILS").length,t.ref.canBeControlled=t.query("GET_ALLOW_BUTTON_CONFIRM")||t.query("GET_ALLOW_BUTTON_CANCEL"),updateStyleViewport(t);var r=document.createElement("div");r.style.cssText="position:fixed;height:100vh;top:0;",t.ref.measure=r,document.body.appendChild(r),t.ref.handleEscapeKey=function(e){27===e.keyCode&&t.dispatch("EDIT_CANCEL")},t.ref.initialScreenMeasureHeight=null,t.ref.handleFullscreenUpdate=function(){t.element.dataset.styleFullscreen=window.innerHeight===t.ref.initialScreenMeasureHeight},t.ref.clientRect={left:0,top:0}},read:function(e){var t=e.root,n=t.ref.measure;n&&(t.ref.initialScreenMeasureHeight=n.offsetHeight,n.parentNode.removeChild(n),t.ref.measure=null),t.ref.clientRect=t.element.getBoundingClientRect()},write:createRoute({ENTER_FULLSCREEN:function(e){var t=e.root;setupFullscreenMode(t)},EXIT_FULLSCREEN:function(e){var t=e.root;cleanFullscreenMode(t)},SHOW_VIEW:function(e){var t=e.root,n=e.action;t.element.dataset.view=n.id},DID_SET_STYLE_LAYOUT_MODE:function(e){var t=e.root,n=e.action;t.element.dataset.styleLayoutMode=n.value||"none",/fullscreen/.test(n.value)&&!/fullscreen/.test(n.prevValue)&&t.dispatch("ENTER_FULLSCREEN")},DID_REQUEST_LOAD_IMAGE:function(e){var t=e.root;if(0===t.opacity&&(t.opacity=1),t.ref.isClosing=!1,t.ref.isClosed=!1,!shouldBeFullscreen(t)||isFullscreen(t)){var n=t.query("GET_STYLE_LAYOUT_MODE");null!==n&&"modal"!==n||t.element.parentNode||t.dispatch("SET_STYLE_LAYOUT_MODE",{value:("fullscreen "+(n||"")).trim()})}else t.dispatch("ENTER_FULLSCREEN")},DID_CANCEL:function(e){var t=e.root;canBeAutoClosed(t)&&mayBeAutoClosed(t)&&t.dispatch("EDIT_CLOSE")},DID_CONFIRM:function(e){var t=e.root;canBeAutoClosed(t)&&mayBeAutoClosed(t)&&t.dispatch("EDIT_CLOSE")},EDIT_CLOSE:function(e){var t=e.root;canBeClosed(t)&&(t.opacity=t.opacity||1,t.opacity=0,t.ref.isClosed=!1,t.ref.isClosing=!0,t.query("GET_ALLOW_AUTO_DESTROY")&&(t.ref.shouldBeDestroyed=!0),isFullscreen(t)&&t.dispatch("EXIT_FULLSCREEN"))}},function(e){var t=e.root;updateStyleViewport(t);var n=t.query("GET_ROOT"),r=t.rect.element;n.width===r.width&&n.height===r.height||t.dispatch("UPDATE_ROOT_RECT",{rect:{x:t.ref.clientRect.left,y:t.ref.clientRect.top,left:t.ref.editor.rect.element.left,top:t.ref.editor.rect.element.top,width:t.rect.element.width,height:t.rect.element.height}})}),didWriteView:function(e){var t=e.root,n=t.ref,r=n.isClosed,i=n.isClosing,o=n.shouldBeDestroyed;!r&&i&&0===t.opacity&&(t.dispatch("DID_CLOSE"),t.ref.isClosed=!0,t.ref.isClosing=!1,shouldBeFullscreen(t)&&t.element.parentNode&&document.body.removeChild(t.element),o&&t.dispatch("EDIT_DESTROY"))},destroy:function(e){var t=e.root;isFullscreen(t)&&cleanFullscreenMode(t),shouldBeFullscreen(t)&&t.element.parentNode&&document.body.removeChild(t.element),window.removeEventListener("resize",t.ref.updateViewport),t.ref.touchDetector.destroy(),t.ref.catcher&&t.ref.catcher.destroy()}}),getViewportBySize=function(e,t){var n="";return 0===e&&0===t?"detached":(n+=t>e?"portrait":"landscape",(n+=e<=600?" x-cramped":e<=1e3?" x-comfortable":" x-spacious").trim())},createApp=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=getOptions(),n=createStore(createInitialState(t),[queries,createOptionQueries(t)],[actions,createOptionActions(t)]);n.dispatch("SET_OPTIONS",{options:e});var r=getUniqueId(),i=null,o=root(n,{id:r}),a=!1,c={_read:function(){a||o._read()},_write:function(e){var t=n.processActionQueue().filter(function(e){return!/^SET_/.test(e.type)});a&&!t.length||(s(t),(a=o._write(e,t))&&n.processDispatchQueue(),t.find(function(e){return"EDIT_DESTROY"===e.type})&&d())}},u=function(e){return function(t){var n={type:e};return t?(t.hasOwnProperty("error")&&(n.error=isObject(t.error)?_objectSpread({},t.error):t.error||null),t.hasOwnProperty("output")&&(n.output=t.output),n):n}},l={DID_CONFIRM:u("confirm"),DID_CANCEL:u("cancel"),DID_LOAD_IMAGE:u("load"),DID_LOAD_IMAGE_ERROR:u("loaderror"),DID_CLOSE:u("close"),DID_DESTROY:u("destroy"),DID_INIT:u("init")},s=function(e){e.length&&e.forEach(function(e){if(l[e.type]){var t=l[e.type];(Array.isArray(t)?t:[t]).forEach(function(t){setTimeout(function(){!function(e){var t=_objectSpread({doka:f},e);delete t.type,o&&o.element.dispatchEvent(new CustomEvent("Doka:".concat(e.type),{detail:t,bubbles:!0,cancelable:!0,composed:!0}));var r=[];e.hasOwnProperty("error")&&r.push(e.error);var i=["type","error"];Object.keys(e).filter(function(e){return!i.includes(e)}).forEach(function(t){return r.push(e[t])}),f.fire.apply(f,[e.type].concat(r));var a=n.query("GET_ON".concat(e.type.toUpperCase()));a&&a.apply(void 0,r)}(t(e.data))},0)})}})},d=function(){f.fire("destroy",o.element),o._destroy(),n.dispatch("DID_DESTROY")},f=_objectSpread({},on(),c,createOptionAPI(n,t),{setOptions:function(e){return n.dispatch("SET_OPTIONS",{options:e})},setData:function(e){n.dispatch("SET_DATA",e)},getData:function(e){return new Promise(function(t,r){n.dispatch("GET_DATA",_objectSpread({},e,{success:t,failure:r}))})},open:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(r,i){e&&n.dispatch("REQUEST_LOAD_IMAGE",{source:e,options:t,success:r,failure:i,resolveOnConfirm:!!t&&t.resolveOnConfirm})})},edit:function(e,t){return f.open(e,_objectSpread({},t,{resolveOnConfirm:!0}))},save:function(e){return new Promise(function(t,r){n.dispatch("GET_DATA",_objectSpread({},e,{success:t,failure:r}))})},clear:function(){return n.dispatch("REQUEST_REMOVE_IMAGE")},close:function(){return n.dispatch("EDIT_CLOSE")},destroy:d,insertBefore:function(e){insertBefore(o.element,e)},insertAfter:function(e){insertAfter(o.element,e)},appendTo:function(e){e.appendChild(o.element)},replaceElement:function(e){insertBefore(o.element,e),e.parentNode.removeChild(e),i=e},restoreElement:function(){i&&(insertAfter(i,o.element),o.element.parentNode.removeChild(o.element),i=null)},isAttachedTo:function(e){return!!o&&(o.element===e||i===e)},element:{get:function(){return o?o.element:null}}});return n.dispatch("DID_INIT"),createObject(f)},createAppObject=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=getOptions(),n={};return forin(t,function(e,t){isString(t)||(n[e]=t[0])}),forin(e,function(e){isString(t[e])&&logIfDeprecatedOption(e,t[e])}),createApp(_objectSpread({},n,e))},toCamels=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-";return e.replace(new RegExp("".concat(t,"."),"g"),function(e){return e.charAt(1).toUpperCase()})},lowerCaseFirstLetter=function(e){return e.charAt(0).toLowerCase()+e.slice(1)},attributeNameToPropertyName=function(e){return toCamels(e.replace(/^data-/,""))},mapObject=function e(t,n){forin(n,function(n,r){forin(t,function(e,i){var o=new RegExp(n);if(o.test(e)&&(delete t[e],!1!==r))if(isString(r))t[r]=i;else{var a=r.group;isObject(r)&&!t[a]&&(t[a]={}),t[a][lowerCaseFirstLetter(e.replace(o,""))]=i}}),r.mapping&&e(t[r.group],r.mapping)})},getAttributesAsObject=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[];forin(e.attributes,function(t){return n.push(e.attributes[t])});var r=n.filter(function(e){return e.name}).reduce(function(t,n){var r=attr(e,n.name);return t[attributeNameToPropertyName(n.name)]=r===n.name||r,t},{});return mapObject(r,t),r},createAppAtElement=function(e){var t=_objectSpread({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),n=getAttributesAsObject(e,{"^class$":"className"});Object.keys(n).forEach(function(e){isObject(n[e])?(isObject(t[e])||(t[e]={}),Object.assign(t[e],n[e])):t[e]=n[e]}),"CANVAS"!==e.nodeName&&"IMG"!==e.nodeName||(t.src=e.dataset.dokaSrc?e.dataset.dokaSrc:e);var r=createAppObject(t);return r.replaceElement(e),r},createApp$1=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return isNode(t[0])?createAppAtElement.apply(void 0,t):createAppObject.apply(void 0,_toConsumableArray(t.filter(function(e){return e})))},copyObjectPropertiesToObject=function(e,t,n){Object.getOwnPropertyNames(e).filter(function(e){return!n.includes(e)}).forEach(function(n){return Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})},PRIVATE_METHODS=["fire","_read","_write"],createAppAPI=function(e){var t={};return copyObjectPropertiesToObject(e,t,PRIVATE_METHODS),t},isOperaMini=function(){return"[object OperaMini]"===Object.prototype.toString.call(window.operamini)},hasPromises=function(){return"Promise"in window},hasBlobSlice=function(){return"slice"in Blob.prototype},hasCreateObjectURL=function(){return"URL"in window&&"createObjectURL"in window.URL},hasVisibility=function(){return"visibilityState"in document},hasTiming=function(){return"performance"in window},supported=function(){var e=isBrowser()&&!isOperaMini()&&hasVisibility()&&hasPromises()&&hasBlobSlice()&&hasCreateObjectURL()&&hasTiming();return function(){return e}}(),state={apps:[]},name="doka",fn=function(){},OptionTypes={},create=fn,destroy=fn,parse=fn,find=fn,getOptions$1=fn,setOptions$1=fn;if(supported()){createPainter(function(){state.apps.forEach(function(e){return e._read()})},function(e){state.apps.forEach(function(t){return t._write(e)})});var dispatch=function e(){document.dispatchEvent(new CustomEvent("doka:loaded",{detail:{supported:supported,create:create,destroy:destroy,parse:parse,find:find,setOptions:setOptions$1}})),document.removeEventListener("DOMContentLoaded",e)};"loading"!==document.readyState?setTimeout(function(){return dispatch()},0):document.addEventListener("DOMContentLoaded",dispatch);var updateOptionTypes=function(){return forin(getOptions(),function(e,t){OptionTypes[e]=t[1]})};OptionTypes={},updateOptionTypes(),create=function(){var e=createApp$1.apply(void 0,arguments);return e.on("destroy",destroy),state.apps.push(e),createAppAPI(e)},destroy=function(e){var t=state.apps.findIndex(function(t){return t.isAttachedTo(e)});return t>=0&&(state.apps.splice(t,1)[0].restoreElement(),!0)},parse=function(e){return Array.from(e.querySelectorAll(".".concat(name))).filter(function(e){return!state.apps.find(function(t){return t.isAttachedTo(e)})}).map(function(e){return create(e)})},find=function(e){var t=state.apps.find(function(t){return t.isAttachedTo(e)});return t?createAppAPI(t):null},getOptions$1=function(){var e={};return forin(getOptions(),function(t,n){e[t]=n[0]}),e},setOptions$1=function(e){return isObject(e)&&(state.apps.forEach(function(t){t.setOptions(e)}),setOptions(e)),getOptions$1()}}export{supported,OptionTypes,create,destroy,parse,find,getOptions$1 as getOptions,setOptions$1 as setOptions};
