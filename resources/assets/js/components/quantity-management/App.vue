<template>
    <div id="modal_quantity_management" :class="{'custom-modal': true, 'fade': showModal}">
        <div :class="{'modal-dialog': true, 'show': showDialog}" >
            <div class="modal-content">
                <!----- modal Header -------->
                <div class="modal-header bg-info">
                    <button type="button" class="close" @click="closeModal">&times;</button>
                    <h6 class="modal-title">إدارة الكميات
                    - {{product !== null ? product.name : null}}</h6>
                </div>
                <!----- modal Body -------->
                <div class="modal-body">
                    <form class="mb-0">
                        <!----- Manage Product Quantity Per Branches -------->
                        <branch-quantity @closeModal="closeModal" v-if="!!product"/>
                        <!----- Save button-------->
                        <button v-if="permissions.edit_product" class="btn btn-tiffany btn-full btn-xlg mt-15" type="button" @click="save($event)" data-inline-loader>حفظ</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    import ProductQuantity from './components/ProductQuantity';
    import BranchQuantity from './components/BranchQuantity';
    import initialData from "../../services/ProductService";

    export default {
        data: () => ({
            product : null,
            showModal : false,
            permissions:[],
            showDialog: false
        }),
        mounted() {
            Salla.event.addEventListener('quantity-management::show-modal', this.show);
            Salla.event.addEventListener('initiate-language-fields', this.initiateLanguageFields);
            // Stop close modal when click outside
            // $('#modal_quantity_management').click(e => {
            //     const senderElement = e.target;
            //     if ($(senderElement).is('div#modal_quantity_management')) {
            //         this.closeModal()
            //     }
            // })
        },
         beforeDestroy() {
            document.removeEventListener('quantity-management::show-modal', this.show);
        },
        components: {
            'product-quantity': ProductQuantity,
            'branch-quantity': BranchQuantity
        },

        methods: {
            // Submit Form
            save(event) {
                Salla.event.createAndDispatch('quantity-management::submit', event);
            },
            // Load Modal Data
            show(){
                let data = initialData.getDatum('QuantityAndOptionsData');
                this.product = data.product;
                this.permissions = data.permissions;
                this.showModal = true;

                $('body').addClass('modal-open').css('padding', '0 15px 0 0');

                this.initiateLanguageFields();
                setTimeout(() => {
                    this.showDialog = true;
                }, 500);
            },
            closeModal() {
                this.product = null;
                this.showDialog = false;
                $('body').removeClass('modal-open').css('padding', '0')
                setTimeout(() => {
                    this.showModal = false;
                }, 500);
            },
            initiateLanguageFields() {
                setTimeout(() => {
                    if (window.initFieldLang) {
                        $('.rec-ls-field:not(.field-lang-initiated)')
                            .each(function () {
                                window.initFieldLang($(this));
                            });
                    }
                }, 500);
            }
        }
    }
</script>
