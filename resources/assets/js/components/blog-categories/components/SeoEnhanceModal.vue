<template>
    <modal id="seo_enhance_modal" :modalTitle="'تحسينات SEO'" :saveBtn="'حفظ'" :closeBtn="'إغلاق'" @saveModal="saveModal">
        <lingual-field
            placeholder="عنوان صفحة التصنيف (Page Title)"
            name="metadata_title"
            icon="sicon-type-square"
            :input-attrs="{maxlength:70}"
            :languages="languages"
            :translations="category.translations"
            @input="titleUpdated"
            @value-changed="titleChanged"
            @blur="titleLength = false"
            @focus="titleLength = true"
        >
            <template v-slot:before-input>
              <label>عنوان صفحة التصنيف (Page Title)</label>
            </template>
            <span v-show="titleLength" class="bootstrap-maxlength label label-success">
                {{ meta_title ? meta_title.length : 0 }} / 70
            </span>
        </lingual-field>

        <lingual-field
            placeholder="رابط مخصص للتصنيف (Page Link)"
            name="custom_url"
            icon="sicon-link"
            :languages="languages"
            :translations="category.translations"
            @input="slugUpdated"
            @value-changed="slugChanged"
            @blur="titleLength = false"
            @focus="titleLength = true"
        >
            <template v-slot:before-input>
                <label>رابط مخصص للتصنيف (Page Link)</label>
            </template>
        </lingual-field>

        <lingual-field
            placeholder="وصف صفحة التصنيف (Page Description)"
            :input-attrs="{maxlength:150, cols:30, rows:2}"
            :translations="category.translations"
            @blur="descriptionLength = false"
            @focus="descriptionLength = true"
            @input="descUpdated"
            @value-changed="descChanged"
            name="metadata_description"
            :languages="languages"
            icon="sicon-content"
            type="textarea"
        >
            <template v-slot:before-input>
                <label>وصف صفحة التصنيف (Page Description)</label>
            </template>
            <span v-show="descriptionLength" class="bootstrap-maxlength label label-success">
                {{ meta_description ? meta_description.length : 0 }} / 150
            </span>
        </lingual-field>

        <div class="seo-preview">
            <a :href="seoUrl" class="title" target="_blank">{{ meta_title || category.name }}</a>
            <a :href="seoUrl" class="link" target="_blank" dir="ltr">{{ seoUrl }}</a>
            <span class="description">{{ meta_description || store_about }}</span>
        </div>
    </modal>
</template>

<script>
import InitialDataService from "../services/InitialDataService";
import Modal from './layout/Modal';
import axios from 'axios';
import LingualField from '@salla.sa/languages/components/LingualField'

export default {

    props: ['category'],

    data () {
        return {
            store_domain     : null,
            store_about      : null,
            titleLength      : false,
            descriptionLength: false,
            languages        : {},
            meta_title       : null,
            meta_description : null,
            custom_url       : null,
            initiatedID      : 0,
        }
    },

    watch: {
        category: function (category) {
            if(category.translations&& this.initiatedID !==category.id){
                this.initiatedID=category.id;
                this.initiateMetaData();
            }
        },
    },
    computed: {
        seoUrl: function () {
            return `${this.store_domain}/blog/`+(this.custom_url||this.category.slug)+`/c${this.category.id}`;
        },

    },

    components: {
      'modal'        : Modal,
      'lingual-field': LingualField,
    },

    created() {
        this.store_domain = InitialDataService.getDatum('store_domain', '');
        this.store_about = InitialDataService.getDatum('store_about', '');
        this.languages = InitialDataService.getDatum('languages', '');
        this.languageChangeListen();
    },

    methods: {
        /**
         * @param {{value: string, translations: Object, Object: HTMLElement|Object, storeLangValue: string, isoCode: string}} eventObject
         * @param {String} inputName
         */
        valueUpdated(eventObject, inputName) {
            this.category.translations=eventObject.translations;
            this.category[inputName] = eventObject.storeLangValue;
            this[inputName]=eventObject.value;
        },
        titleUpdated(eventObject){this.valueUpdated(eventObject, 'meta_title')},
        titleChanged(eventObject){this.valueUpdated(eventObject, 'meta_title')},
        slugUpdated(eventObject){this.valueUpdated(eventObject, 'custom_url')},
        slugChanged(eventObject){this.valueUpdated(eventObject, 'custom_url')},
        descUpdated(eventObject){this.valueUpdated(eventObject, 'meta_description')},
        descChanged(eventObject){this.valueUpdated(eventObject, 'meta_description')},
        // Method fires when the save modal button pressed
        async saveModal(id) {
            showLoading();
            await axios.post(`/blog/categories/seo/${this.category.id}`, Object.assign({
                _method: 'PUT',
                _token: _token,
                custom_url: this.category.custom_url,
                metadata_title: this.category.meta_title,
                metadata_description: this.category.meta_description
            }, this.category.translations))
            .then(res => {
                hideLoading();
                swal({
                    text: 'تم الحفظ',
                    type: 'success',
                    showConfirmButton: false,
                    timer: 1500
                });
                $(`#${id}`).modal('hide');
            })
            .catch(response => {
                hideLoading();
                laravel.ajax.errorHandler(response.response);
            })
        },
        /**
         * Because translated Data Comes With Different Way, we will Initiate them to be readable in Lingual Field;
         */
        initiateMetaData(){
            if(!this.category.translations|| !this.languages || !this.languages.supported){
                return;
            }
            this.languages.supported.forEach((lang) => {
                let trans = this.category.translations[lang.iso_code];
                if (!trans || !trans['extra_attributes'] || !trans['extra_attributes']['metadata']) {
                    return;
                }
                let meta = trans['extra_attributes']['metadata'];
                this.category.translations[lang.iso_code] = Object.assign(trans, {
                    metadata_title      : meta.title,
                    metadata_description: meta.description,
                });
            });
        },
        languageChangeListen(){
            Salla.event.addEventListener('languages::changed', (event) => {
                let lang = event.detail.current;
                if (!this.category.translations || !(lang = this.category.translations[lang])) {
                    return
                }
                this.meta_title = lang.metadata_title || this.category.name;
                this.meta_description = lang.metadata_description || this.store_about;
                this.custom_url = lang.custom_url || this.category.slug;
            });
        }
    },
}
</script>