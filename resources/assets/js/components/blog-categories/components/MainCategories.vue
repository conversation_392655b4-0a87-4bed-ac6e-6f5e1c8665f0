<template>
    <!-- Main Categories List -->
    <draggable v-bind="dragOptions" @end="categoriesSortable" class="rec-list rec-list--vertical rec-categories" tag="ul" :list="categories" :group="{ name: 'g1' }">
        <!-- 'rec-categories--new': (category.categoryTitle === '' || category.categoryTitle === null), -->
        <li :class="{'mb-20': true, 'rec-categories--hidden': category.status === 'hidden'}"
            v-for="(category, cateIndex) in categories" :key="cateIndex">
            <div class="wrap">
                <div>
                    <i class="sicon-menu move-handler"></i>
                    <icon-picker
                        :categoryID="category.id"
                        :value="category.icon"
                        @input="category.icon = $event"
                    />
                    <lingual-field
                            :events-load="category"
                            :outerAttrs="{'class':{'form-group':false}}"
                            :translations="category.translations"
                            :languages="languages"
                            :placeholder="{ar:'اضف عنوان التصنيف',en:'Title For Category'}"
                            name="name"
                            @value-updated="nameUpdated"
                    />
                    <!-- @value-updated="nameUpdated" -->
                </div>
                <div>
                    <button class="dropdown-toggle rec-btn--trans rec-btn" data-toggle="dropdown" aria-expanded="true">
                        <i class="sicon-ellipsis-vertical" ></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right mobile-top">
                        <li @click="hideCategory(categories, cateIndex, category.id)" v-if="category.status === 'active'">
                            <a>
                                <i class="sicon-eye-off"></i>
                                إخفاء التصنيف
                            </a>
                        </li>
                        <li @click="showCategory(categories, cateIndex, category.id)" v-if="category.status === 'hidden'">
                            <a>
                                <i class="sicon-eye"></i>
                                إظهار التصنيف
                            </a>
                        </li>
                        <li @click="hideCategoryArticles(categories, cateIndex, category.id)" v-if="!category.has_hidden_articles">
                            <a>
                                <i class="sicon-file-off"></i>
                                إخفاء مقالات التصنيف
                            </a>
                        </li>
                        <li @click="showCategoryArticles(categories, cateIndex, category.id)" v-if="category.has_hidden_articles">
                            <a>
                                <i class="sicon-eye"></i>
                                إظهار مقالات التصنيف
                            </a>
                        </li>
                        <li @click="editCategorySeo(category)">
                            <a>
                                <i class="sicon-settings"></i>
                                تحسينات SEO
                            </a>
                        </li>
                        <li @click="deleteCategory(categories, cateIndex, category.id)">
                            <a class="delete-product">
                                <i class="sicon-trash-2"></i>
                                حذف التصنيف
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </li>
    </draggable>
</template>
<script>
    import IconPicker from './IconPicker';
    import draggable from 'vuedraggable';
    import LingualField from "@salla.sa/languages/components/LingualField";
    
    let editedCat;

    export default {
        name: 'categories',
        props: [
          'sort',
          'categories',
          'addCategory',
          'level',
          'hideCategory',
          'showCategory',
          'hideCategoryArticles',
          'showCategoryArticles',
          'deleteCategory',
          'editCategory',
          'editCategorySeo',
          'languages',
        ],
        data () {
          return {
            dragOptions: {
              handle: ".move-handler",
              easing: "cubic-bezier(0.86, 0, 0.07, 1)",
              animation: 20,
              swapThreshold: 0.65,
            },
          }
        },
        components: {
            'icon-picker': IconPicker,
            draggable,
            LingualField
        },
        methods: {
            categoriesSortable(event) { // Method That Is Fired After Any Change With Drag And Drop
                this.sort()
            },
            nameUpdated(event){
              event.object.translations=event.translations;
              // this.editCategory(event.object);
              event.object.name = event.value;
              let ar = event.object.translations.ar;
              delete event.object.translations.ar;

              const cat = {
                id: event.object.id,
                name: ar.name,
                ...event.object.translations
              }
              editedCat = cat; 
              this.$emit('catEdit', cat)
            },

            onChildCatEdit(){ 
              this.$emit('catEdit', editedCat)
            }
        }
    }
</script>
