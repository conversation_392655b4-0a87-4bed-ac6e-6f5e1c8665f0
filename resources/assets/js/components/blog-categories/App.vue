<template>
    <div class="panel">
        <div class="panel-heading sides panel-heading--sticky">
            <h6 class="panel-title">
                <i class="sicon-inbox-multi"></i>
                تصنيفات المدونة
            </h6>

            <button @click="saveChanges" class="btn btn-tiffany btn-add cats-save-btn" :disabled="editedCategories.length==0"> حفظ التغيرات </button>
        </div>

        <div class="panel-body">
            <main-categories
                    :level="1"
                    :allCategories="categories"
                    :categories="categories"
                    :addCategory="addCategory"
                    :hideCategory="hideCategory"
                    :showCategory="showCategory"
                    :hideCategoryArticles="hideCategoryArticles"
                    :showCategoryArticles="showCategoryArticles"
                    :deleteCategory="deleteCategory"
                    :editCategory="editCategory"
                    :sort="sort"
                    :languages="languages"
                    :editCategorySeo="editCategorySeo"
                    @catEdit="onCatEdit"
            />
 
            <button class="btn btn-tiffany btn-add" id="add_category" @click="addCategory(categories, categories.length, 0)">
                <i class="sicon-add"></i>
                إضافة تصنيف جديد
            </button> 
            <seo-enhance-modal :category="seoCategory"/>
        </div>
    </div>
</template>

<script>
    import MainCategories from './components/MainCategories';
    import SeoEnhanceModal from './components/SeoEnhanceModal';
    import InitialDataService from './services/InitialDataService';
    import axios from 'axios';

    export default {
        data() {
            return {
                categories: [],
                languages: {},
                isModel: false,
                seoCategory: {
                    id : 0,
                    slug : '',
                    meta_title : '',
                    custom_url : '',
                    meta_description : '',
                },
                editedCategories: [],
            }
        },
        created() {
            this.isModel = InitialDataService.getDatum('isCatsModel', false);
            let defaultLang = {
                "id"            : 1,
                "name"          : "العربية",
                "rtl"           : 1,
                "iso_code"      : "ar",
                "country_code"  : "sa",
                "flag"          : "https://assets.salla.sa/images/flags/ar.svg",
                "status"        : true,
                "auto_translate": false,
                "order"         : 0
            };

            this.languages = InitialDataService.getDatum('languages', {
                "supported": [defaultLang],
                "current"  : defaultLang,
                "default"  : defaultLang,
                "iso_code" : "ar",
                "feature"  : false
            });
            this.categories = InitialDataService.getDatum(this.isModel?'modelCats':'categories', []);
            Salla.event.addEventListener('initiate-language-fields', this.initiateLanguageFields);
        },
        components: {
            'main-categories': MainCategories,
            'seo-enhance-modal': SeoEnhanceModal,
        },
        methods: {
            sort() {
                setTimeout(() => {
                    this.categoryRequest('sort', {categories: this.categories}, this.sorted)
                }, 500)
            },
            sorted(response) {
                if (response.data.categories) {
                    this.categories = response.data.categories;
                    this.fireCategoryEvent();
                }
            },
            addCategory(arrayElement, index, categoryID) { // Method To Add Main Category

                /**
                 * TODO: push empty item at first without calling new request.
                 * Use commented code below
                 */

                // const newCatInfo = { 
                //   "id": categoryID, 
                //   "categoryTitle": "", 
                //   "parent_id": categoryID, 
                //   "icon": "", 
                //   "expanded": "expanded", 
                //   "children": [],
                //   "sort_order": index,
                //   "has_hidden_products": false,
                //   "status": "hidden", 
                //   "show_in_app": true, 
                //   "translations": { "ar": { "name": "" } }
                // }
                // this.categories.push(newCatInfo);

                // let emptyCats = this.categories.map((cat,index)=>{
                //   if(cat.categoryTitle == ''){
                //     return cat;
                //   }
                // }).filter(function(x) {
                //     return x !== undefined;
                // });

                if(this.categories.length === 0 || (arrayElement[index - 1].name !== '' && arrayElement[index - 1].name !== null))
                  this.categoryRequest('', {sort_order: index}, this.added);
                else{
                  swal({
                      text: 'هناك تصنيف فارغ، من فضلك أدخل اسم التصنيف',
                      type: 'warning',
                      showConfirmButton: false,
                      timer: 2500
                    }).catch(swal.noop);
                }                
            },
            added(response) {
                if (response.data.category) {
                    let category = response.data.category;
                    this.categories.push(category)
                    this.$emit('catEdit', category)
                    //this.categories = response.data.categories;
                }
                this.fireCategoryEvent();
            },
            hideCategory(arrayElement, index, categoryID) { // Method To Hide Category
                let that = this;
                let catHidden = function (response) {
                    if (response.data.category) {
                        that.$set(arrayElement, index, response.data.category);
                    }
                    var message = 'تم إخفاء التصنيف بنجاح';
                    that.fireCategoryEvent(message); 
                };
                this.categoryRequest(categoryID + '/hide', catHidden);
            },
            showCategory(arrayElement, index, categoryID) { // Method To Hide Category
                let that = this;
                let catPublished = function (response) {
                    if(response.status != 200){
                      swal({
                        text: response.responseJSON.error.message,
                        type: 'warning',
                        showConfirmButton: false,
                        timer: 2500
                      }).catch(swal.noop);
                      return;
                    }
                    if (response.data.category) {
                        that.$set(arrayElement, index, response.data.category);
                    }
                    var message = 'تم إظهار التصنيف بنجاح';
                    that.fireCategoryEvent(message);
                };
                this.categoryRequest(categoryID + '/show', catPublished);
            },
            hideCategoryArticles(arrayElement, index, categoryID) { // Method To Hide Category Products
                let that = this;
                let hidden = function (response) {
                    if (response.data.category) {
                        that.$set(arrayElement, index, response.data.category);
                    }
                    that.fireCategoryEvent('تم إخفاء التصنيف ومقالاته بنجاح');
                };
                this.categoryRequest(categoryID + '/articles/hide', hidden);
            },
            showCategoryArticles(arrayElement, index, categoryID) { // Method To Hide Category Products
                let that = this;
                let published = function (response) {
                    if (response.data.category) {
                        that.$set(arrayElement, index, response.data.category);
                    }
                    swal({
                        text: 'تم إظهار التصنيف ومقالاته بنجاح',
                        type: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    })
                    that.fireCategoryEvent();
                };
                this.categoryRequest(categoryID + '/articles/show', published)
            },
            deleteWarningMessage(message, element, categoryID, index) {
                swal({
                    title: '<h2>' + 'تحذير' + '</h2>',
                    html: '<p>' + `${message}` + '</p>',
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    confirmButtonText: 'موافق !',
                    cancelButtonText: '<i class="fa fa-close"></i> إلغاء'
                }).then(() => {
                    this.deleteCategoryAjax(categoryID, element, index);
                });
            },
            deleteCategory(arrayElement, index, categoryID) { // Method To Delete Category
                if (arrayElement[index].articlesCount > 0) {
                    this.deleteWarningMessage('هذا التصنيف مرتبط بمقالات هل أنت متاكد من المسح؟', arrayElement, categoryID, index);
                } else {
                    if (categoryID > 0) {
                        this.deleteCategoryAjax(categoryID, arrayElement, index);
                    }
                }
            },
            deleteCategoryAjax(categoryID, element, index) { // Method To Delete Category

                // remove the data form edit array
                const catIndex = this.editedCategories.findIndex(cat => cat.id === categoryID);
                if(catIndex !== -1){
                  this.editedCategories.splice(catIndex, 1);
                }

                let that = this;
                let deleted = function (response) {
                    swal({
                        text: 'تم حذف التصنيف ومقالاته بنجاح',
                        type: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    }).catch(swal.noop);
                    element.splice(index, 1);
                    that.fireCategoryEvent();
                };
                this.categoryRequest(categoryID, {_method: 'DELETE'}, deleted);
            },
            editCategory(category) { // Method To Edit Category
                if (!(category.id && 
                      category.translations[this.languages.iso_code].name.length) &&
                      category.name == "") {
                    return;
                }
                let data = _.assign({_method: 'PUT'}, category.translations);
                this.categoryRequest(category.id, data, this.saved, this.failedToSave);

                console.log('edited now');
            },
            saved(response) {
                swal({
                    text: 'تم الحفظ',
                    type: 'success',
                    showConfirmButton: false,
                    timer: 1500
                }).catch(swal.noop);
                this.fireCategoryEvent();
            },
            failedToSave(response) {                
                if (response.responseJSON !== 'undefined' && 
                    response.responseJSON.errors &&  
                    response.responseJSON.errors.name.length > 0) {
                    swal('', response.responseJSON.errors.name[0], 'error');
                }
            },
            categoryRequest(url, data, successHandler, errorHandler) {
                //in case we don't have data, unshift arguments.
                if (typeof data == 'function') {
                    successHandler = data;
                    errorHandler = successHandler;
                    data = null;
                }
                $.ajax({
                    url: '/blog/categories/' + url,
                    type: 'POST',
                    dataType: 'json',
                    data: _.assign({_token: _token}, data),
                    success: successHandler,
                    error: errorHandler
                });
            },
            fireCategoryEvent(message) {
                /*if (message) {
                    swal({
                        text: message,
                        type: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    });
                }*/
                Salla.event.createAndDispatch('initiate-language-fields');
                if (this.isModel) {
                    //get only published categories
                    let cats = _.filter(this.categories, {status: 'active'});

                    // emit event with the categories after add/update categories data,
                    // to update the list of categories inside products page
                    Salla.event.createAndDispatch('articles::reload-categories-list', {
                        categories: cats,
                    });
                }
            },
            initiateLanguageFields(){
                setTimeout(() => {
                    if (window.initFieldLang) {
                        $('.rec-ls-field:not(.field-lang-initiated)')
                            .each(function () {
                                window.initFieldLang($(this));
                            });
                    }
                }, 500);
            },
            editCategorySeo(category) { // Method To Delete Category
                this.seoCategory = category;
                $("#seo_enhance_modal").modal('show');
            },

            onCatEdit(cat){
              console.log('emitted now'); 
              if(cat){
                const index = this.editedCategories.findIndex(x => x.id === cat.id);
                console.log('index:', index);
                if(index !== -1){
                  this.editedCategories.splice(index, 1);
                }

                this.editedCategories.push(cat);
              }
            },

            saveChanges(){
              showLoading();
              axios.post('categories/bulk/update', {'categories':this.editedCategories})
              .then((res)=>{
                console.log('res: ', res);
                this.saved();
                this.editedCategories = [];
              })
              .catch((error)=>{
                const errors = error.response.data.error.fields;
                let errorMessage = '';
                if(errors){
                  Object.keys(errors).forEach(key=>{ errorMessage = errors[key][0] })
                }
                swal({
                    text: errorMessage ? errorMessage : 'خطأ',
                    type: 'error',
                    showConfirmButton: false,
                    timer: 1500
                }).catch(swal.noop);
              })
              .finally((e)=>{
                hideLoading();
              })
            }
        },
        mounted() {
            // Simple Input Focus For Better UX
            $('#add_category').click(function () {
                setTimeout(() => {
                    Salla.event.createAndDispatch('initiate-language-fields');
                    $(this).siblings('ul').children('li:last-of-type').find('input').focus();
                }, 600);
            });

            $('#seo_enhance_modal').on('hide.bs.modal', function () {
                $(this).find('.form-group').removeClass('has-error');
                $(this).find('.help-block').remove();
            });
            $('#seo_enhance_modal').on('show.bs.modal', ()=>
                setTimeout(
                    ()=>Salla.event.createAndDispatch('languages::changed', {current: this.languages.iso_code, dir: 'rtl'})
                    ,100
                ))
        }
    }
</script>