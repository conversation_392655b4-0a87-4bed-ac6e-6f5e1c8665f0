<template>
  <div>
    <div
      class="table-responsive table-responsive--ov-visible table-responsive--ov-x-visible"
    >
      <table class="table table-no-top-border">
        <tbody>
          <tr v-for="order_option in order_options" :key="order_option.id">
            <td>
              <a
                class="load-data-page"
                :class="{
                  disabled: !is_feature_enabled,
                  'text-muted': !is_feature_enabled,
                }"
                @click="(e) => editOrderOption(e, order_option)"
              >
                {{ order_option.name }}
              </a>
            </td>
            <td colspan="3">
              <div
                class="rec-list rec-list--horizontal rec-list--align-center rec-list--align-left nowrap"
              >
                <div
                  v-if="showInlineDelete && selectedOption == order_option.id"
                  id="inline-delete"
                  class="d-flex align-items-center gap-10"
                >
                  <span class="text-danger">ﻫﻞ ﺗﺮﻳﺪ تأكيد حذف الحقل؟</span>
                  <button
                    class="btn bg-danger btn-sm"
                    @click="deleteOrderOption(order_option.id)"
                  >
                    نعم، حذف
                  </button>
                  <button class="btn btn-sm" @click="showInlineDelete = false">
                    إلغاء
                  </button>
                </div>
                <div
                  v-show="
                    !showInlineDelete && selectedOption != order_option.id
                  "
                  class="text-right more-opt d-flex ml-15"
                >
                  <div
                    class="rec-order-status__toggle"
                    :class="{ disabled: !is_feature_enabled }"
                  >
                    <input
                      :id="`order_option-active-${order_option.id}`"
                      v-model="order_option.status"
                      class="switchery"
                      type="checkbox"
                      :true-value="1"
                      :false-value="0"
                      @change="toggleOrderOption(order_option.id)"
                    />
                  </div>
                  <button
                    class="btn btn-default btn--more-nav rec-btn rec-btn--trans ml-10"
                  >
                    <div class="dot"></div>
                    <ul class="rec-list rec-list--vertical more-options">
                      <li>
                        <a
                          class="font-13 text-default"
                          :class="{ hidden: !is_feature_enabled }"
                          @click="(e) => editOrderOption(e, order_option)"
                        >
                          <i
                            class="sicon-edit flip-x text-primary mr-5 font-14"
                          ></i>
                          تعديل بيانات الحقل
                        </a>
                      </li>
                      <li>
                        <a
                          class="font-13 text-default"
                          :class="{ hidden: !is_feature_enabled }"
                          href="/reports?component=order_options"
                        >
                          <i
                            class="sicon-chart-bar flip-x text-primary mr-5 font-14"
                          ></i>
                          إحصائيات
                        </a>
                      </li>
                      <li>
                        <a
                          class="font-13 text-default"
                          @click.self="(e) => handleDelete(e, order_option)"
                        >
                          <i
                            class="sicon-trash-2 flip-x text-danger mr-5 font-14"
                          ></i>
                          حذف
                        </a>
                      </li>
                    </ul>
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { convertFieldOptionsToArray } from '../utils'

export default {
  data() {
    return {
      showInlineDelete: false,
      selectedOption: null,
    }
  },
  computed: {
    ...mapState({
      order_options: (state) => state.order_options,
      fields: (state) => state.fields,
      is_feature_enabled: (state) => state.is_feature_enabled,
    }),
  },
  methods: {
    handleDelete(e, order_option) {
      e.preventDefault()
      this.selectedOption = order_option.id
      this.showInlineDelete = true
    },
    editOrderOption(e, order_option) {
      const field_id = order_option.extra_attributes.field_type
      if (field_id == 'date') {
        this.$emit('fetchField', { id: order_option.id })
        return true
      }

      const field = this.fields.find((field) => field.id == field_id)
      const clonedField = _.cloneDeep(field)

      const updatedField = convertFieldOptionsToArray(clonedField)
      updatedField.option_id = order_option.id
      updatedField.fields.forEach((option) => {
        if (['text', 'number', 'textarea'].includes(option.type)) {
          option.value = order_option[option.name]
        }
        if (option.type == 'checkbox') {
          option.checked = order_option.extra_attributes[option.name]
        }
        if (option.type == 'radio' || option.type == 'product_category') {
          option.options.forEach((radio_option) => {
            radio_option.checked =
              order_option.extra_attributes[option.name] == radio_option.value
          })
        }
      })
      updatedField.options = _.cloneDeep(order_option.options)
      //   updatedField.options?.forEach((option, index) => {
      //     option.forEach((element) => {
      //       element.value = order_option.options[index][element.name]
      //     })
      //   })

      this.$store.commit('setActiveField', updatedField)
      this.$store.commit(
        'setSelectedCategories',
        order_option.selected_categories
      )
      $('#order-options-field-modal').modal('show')
    },
    deleteOrderOption(id) {
      this.deleteTemplateRequest(id)
    },
    deleteTemplateRequest(id) {
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': window.token.key,
          's-store-id': store.id,
          ...(window.headers || {}),
        },
      }
      axios
        .delete(window.apiUrl + `/order-options/${id}`, config)
        .then((res) => {
          swal({
            title: 'تم الحذف بنجاح',
            type: 'success',
            timer: 2000,
          })
          window.location.reload()
        })
        .catch((err) => {
          console.log(err)
          var message = err.response.data.error.message || 'حدث خطأ ما'
          message += ' برجاء المحاوله مره اخري '
          swal({
            title: 'خطأ',
            text: message,
            type: 'error',
            timer: 2000,
          })
          if (err.response.status == 401) {
            getFreshToken()
          }
        })
    },
    toggleOrderOption(id) {
      window.showLoading();
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': window.token.key,
          's-store-id': store.id,
          ...(window.headers || {}),
        },
      }
      axios
        .post(window.apiUrl + `/order-options/${id}`, {}, config)
        .then(() => {
          // swal({
          //   title: 'تم التعديل بنجاح',
          //   type: 'success',
          //   timer: 2000,
          // })
          //  window.location.reload();
          window.hideLoading();
        })
        .catch((err) => {
          window.hideLoading()
          var message = err.response.data.error.message || 'حدث خطأ ما'
          message += ' برجاء المحاوله مره اخري '
          swal({
            title: 'خطأ',
            text: message,
            type: 'error',
            timer: 2000,
          })
          if (err.response.status == 401) {
            getFreshToken()
          }
        })
    },
  },
}
</script>
<style lang="scss" scoped>
#inline-delete {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #fff;
  z-index: 999;
}
</style>
