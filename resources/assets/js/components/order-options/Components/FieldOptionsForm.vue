<template>
  <draggable
    v-model="activeFieldOptions"
    tag="ul"
    v-bind="dragOptions"
    class="rec-list rec-list--vertical rec-list--draggable mb-0"
  >
    <li v-for="(field, index) in activeFieldOptions" :key="index" class="bg-gray-50 no-border">
      <div class="form-group pt-20">
        <div class="row no-padding d-flex justify-content-center align-items-center flex-wrap">
          <span
            class="sicon-menu text-center text-primary font-20 move-handler hidden visible-lg mt-10 col-md-1"
          ></span>
          <template v-for="option in field">
            <div
              v-if="['text', 'number'].includes(option.type)"
              :key="option.name"
              class="col-xs-5 col-md-2 mx-10 no-padding mt-10"
            >
              <label v-if="option.title" class="d-flex justify-content-start align-items-center"
                >{{ option.title }}
                <span class="text-muted text-small ml-5">{{ !option.is_required ? '( اختياري )' : '' }}</span>
                <BaseTolltip v-if="option.type == 'text' || option.name == 'weight'" :tooltip="option.type == 'text' ? ' آقصي عدد لحروف النص ٥٠ حرف' : 'يحسب وزن الخيار ضمن إجمالي وزن الطلب وقد يؤثر في رسوم الشحن.'" />
              </label>
              <div class="input-group">
                <span class="input-group-addon input-group-addon-small">
                  <i :class="option.icon"></i>
                </span>
                <input
                  :id="`${option.name}-${index}`"
                  v-model="option.value"
                  maxlength="50"
                  type="text"
                  class="form-control"
                  :class="option.type == 'number' ? '_parseArabicNumbers' : ''"
                  :placeholder="option.placeholder"
                  :name="option.name"
                />
              </div>
            </div>
            <input
                  v-if="['id'].includes(option.type)"
                  :key="option.name"
                  :value="option.value"
                  type="hidden"
                  :name="option.name"
                />
          </template>
          <span
            @click="removeFieldOption(index)"
            class="sicon-trash text-center text-danger font-20 col-xs-12 col-md-1 mt-10 cursor-pointer hidden visible-md visible-lg"
          ></span>
          <span class="btn btn-danger mt-10 font-13 hidden-md hidden-lg col-xs-12 col-md-1" @click="removeFieldOption(index)">
            <i class="sicon-trash-2 flip-x text-danger mr-5 font-14"></i>
            حذف
          </span>
          <input type="hidden" name="separator" />
        </div>
      </div>
    </li>

    <button class="btn btn--outlined primary btn--with-icon btn-block mt-20" @click="addPlaceholderOption">
      <span class="sicon-add"></span>
      إضافة خيار جديد
    </button>
  </draggable>
</template>
<script>
import { mapState } from 'vuex'
import draggable from 'vuedraggable'
import BaseTolltip from './BaseTolltip.vue'
export default {
  components: { draggable, BaseTolltip },
  data() {
    return {
      message: 'Hello Vue!',
      dragOptions: {
        handle: '.move-handler',
        easing: 'cubic-bezier(0.86, 0, 0.07, 1)',
        animation: 500,
      },
    }
  },
  computed: {
    ...mapState({
      fields: (state) => state.fields,
    }),
    activeFieldOptions: {
      get() {
        return this.$store.state.activeField.options
      },
      set(value) {
        this.$store.commit('setSelectFieldOptions', value)
      },
    },
  },
  methods: {
    addPlaceholderOption(e) {
      e.preventDefault()
      this.$store.commit('addPlaceholderOption', JSON.parse(JSON.stringify(this.fields[3].options.placeholder)))
    },
    removeFieldOption(index) {
      this.$store.commit('removeSelectedFieldOption', index)
    },
  },
}
</script>

<style lang="scss" scoped>
.rec-list--draggable > li:before {display: none;}
.move-handler {
  cursor: move;
}
</style>
