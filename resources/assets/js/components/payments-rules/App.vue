<template>
    <div>
        <div class="panel panel-default mb-20">
            <div class="panel-heading">
                <h6 class="panel-title">
                    <i class="sicon-shield-alert"></i>
                    إضافة قيد جديد
                </h6>
            </div>
            <div class="panel-body">
                <section class="alert-box alert-box--info">
                    <i class="sicon-info"></i>
                    <article>
                        يتم تطبيق الإجراء المحدد على طرق الدفع المختارة عند تحقق جميع الشروط
                    </article>
                </section>
                <payments-methods
                    :rule="rule"
                    :payments="payments"
                    :errors="errors"
                        />

                <payments-rules
                    :rule="rule"
                    :paymentRuleTypes="paymentRuleTypes"
                    :countriesByCode="countriesByCode"
                    :countriesByMobileCode="countriesByMobileCode"
                    :isPaymentMethodSelected="isPaymentMethodSelected"
                    :comparisonOperators="comparisonOperators"
                    :countriesOperators="countriesOperators"
                    :categoriesOperators="categoriesOperators"
                    :customerGroups="customerGroups"
                    :categories="categories"
                    :errors="errors" />

                <payments-procedures :rule="rule"/>
            </div>
        </div>

        <div class="rec-list rec-list--horizental controls align-items-stretch">
            <button class="btn btn-tiffany btn-save btn-full"
            :disabled="rule.payment_methods_slugs === null || rule.payment_methods_slugs.length < 1"
            @click="saveRule($event)"
            data-inline-loader>
                {{ saveRuleText }}
            </button>
            <!-- TODO:: Show This If It's Going To Be Payment Rule Edit -->
            <button class="btn btn-danger btn-delete btn-full h-auto" v-show="rule.id !== 0" @click="deleteRule(rule)">{{ deleteRuleText }}</button>
        </div>
    </div>
</template>

<script>

import PaymentsMethods from './components/PaymentsMethods';
import paymentsRules from './components/PaymentsRules';
import PaymentsProcedures from './components/PaymentsProcedures';
import initialData from "../shipping-role/RuleService";
import Http from "../../utils/http";
import FormError from "./layouts/FormError";

export default {
    data () {
        return {
            isPaymentMethodSelected: false,
            saveRuleText: 'حفظ',
            deleteRuleText: 'حذف',
            errors: [],
            submitting: false,
            rule: {
                id: 0,
                title: null,
                payment_methods_slugs: null,
                options: {
                    types: [],
                    cart_price_condition: null,
                    cart_price: null,
                    products_quantity_condition: null,
                    products_quantity: null,
                    country_phone_code: null,
                    country_ip_address_code: null,
                    country_phone_condition: null,
                    country_ip_condition: null,
                    credit_card_country: null,
                    credit_card_condition: null,
                    customer_group_id: null,
                    categories_condition: null,
                    categories_ids: null,
                    other_categories_restricted:false,
                },
                paymentMethods: [],
                procedureType: null,
                declineDesc: null
            },
            comparisonOperators:[],
            countriesOperators:[],
            categoriesOperators:[],
            paymentRuleTypes:[],
            payments:[],
            countriesByCode:[],
            countriesByMobileCode:[],
            customerGroups: [],
            categories: [],
        }
    },
    created() {
        this.rule = initialData.getDatum('rule', []);
        this.comparisonOperators = initialData.getDatum('comparisonOperators', []);
        this.countriesOperators = initialData.getDatum('countriesOperators', []);
        this.categoriesOperators = initialData.getDatum('countriesOperators', []);
        this.paymentRuleTypes = initialData.getDatum('paymentRuleTypes', []);
        this.payments = initialData.getDatum('paymentMethods', []);
        this.countriesByCode = initialData.getDatum('countriesByCode', []);
        this.countriesByMobileCode = initialData.getDatum('countriesByMobileCode', []);
        this.customerGroups = initialData.getDatum('customerGroups', []);
        this.categories = initialData.getDatum('categories', []);
    },
    methods: {
        saveRule(event) {
            event.preventDefault();

            this.saveRuleText = 'جاري الحفظ...';
            this.submitting = true;
            this.errors = [];

            if (this.submitting) {
                (this.rule.id <= 0)? this.createRule(event) : this.updateRule(event);
            }
        },
        createRule(event){
            showLoading(event)
            Http.post('/payment/rules', this.rule,
                ({data}) => {
                    this.saveRuleText = 'تم الحفظ';
                    window.location.replace(`/payment/rules`);
                    hideLoading()
                },
                ({response}) => {
                  hideLoading()
                    if (response) {
                        this.saveRuleText = 'حفظ';
                        this.submitting = false;
                        this.errors = response.data.error.fields;
                        laravel.ajax.errorHandler(response);
                    }
                });
        },
        updateRule(event) {
          showLoading(event)
            Http.put('/payment/rules/' + this.rule.id, this.rule,
                ({resp}) => {
                    this.saveRuleText = 'تم الحفظ';
                    window.location.replace(`/payment/rules`);
                    hideLoading()
                },
                ({response}) => {
                    if (response) {
                        this.saveRuleText = 'حفظ';
                        this.submitting = false;
                        this.errors = response.data.error.fields;
                        laravel.ajax.errorHandler(response);
                    }
                    hideLoading()
                });
        },
        deleteRule(rule) {
            event.preventDefault();
            swal({
                title: "تنبيه ",
                text: 'هل انت متاكد من هذا الاجراء ؟',
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "تأكيد",
                cancelButtonText: "إلغاء"
            }).
            then(function () {
                return axios
                    .request({url: '/payment/rules/' + rule.id, rule, method: 'delete', responseType: 'json' })
                    .then(function(){
                        swal({title: 'تم الحذف بنجاح', type: 'success', showConfirmButton: false, timer: 1500 }).catch(function(timeout) {
                            window.location.href = '/payment/rules';
                        });
                    }).catch(function(){});
            });
        },
        handleBack () {
            window.history.back();
        },
        showMessage(message) {
            swal({title: message, type: 'success', showConfirmButton: false, timer: 1500 }).catch(function(timeout) {
                window.location.href = '/payment/rules';
            });
        }
    },
    components: {
        PaymentsMethods,
        paymentsRules,
        PaymentsProcedures
    }
}
</script>
