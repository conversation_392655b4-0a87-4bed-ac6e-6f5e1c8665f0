<template>
    <div class="rules">
        <div class="rules__title text-primary rec-flex">
            <span>3</span>
            <p class="m-0">
              الإجراءات
              <small class="text-muted text-muted-small m-0">قم بتحديد طريقة دفع أولا لإضافة الإجراءات</small>
            </p>
        </div>
        <div v-if="rule.payment_methods_slugs.length">
            <div class="form-group mb-0">
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-book"></i>
                    </span>
                    <treeselect
                        :multiple="false"
                        :options="procedures"
                        :searchable="false"
                        class="vue-treeselect--custom vue-treeselect--with-icon vue-treeselect--without-effect"
                        placeholder="اختر الإجراء"
                        v-model="rule.action"
                    />
                </div>
            </div>
            <div class="form-group mb-0 mt-20" v-if="rule.action === 'reject'">
                <div class="input-group wide">
                    <input type="text" class="form-control full-bordered pl-15" placeholder="إدخال سبب الرفض (سيظهر للعميل)" v-model="rule.reason">
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
    props: ['rule'],

    data () {
        return {
            procedures: [
                {
                    id: 'reject',
                    label: 'رفض العملية'
                },
                {
                    id: 'pending',
                    label: 'تحت المراجعة'
                },
              {
                id: 'hide',
                label: 'اخفاء طريقة الدفع'
              }
            ]
        }
    },

     components: {
        Treeselect
    }
}
</script>