<template>
    <div class="rules mb-40">
        <div class="rules__title text-primary rec-flex">
            <span>2</span>
            <p class="m-0">
              إضافة قيد الدفع
              <small class="text-muted text-muted-small m-0">قم بتحديد طريقة دفع اولا لإضافة القيود</small>
            </p>
        </div>
        <div v-if="rule.payment_methods_slugs.length">
            <div class="form-group form-group--with-delete" v-for="(theRule, index) in paymentsRules" :key="index">
                <div class="rules__groups"
                    :class="{'rules__groups-cart-price': theRule.selectedRule !== null, 'space': paymentsRules.length > 1}">
                    <div class="select-group">
                        <div class="input-group">
                            <span class="input-group-addon">
                                <i class="sicon-list-check"></i>
                            </span>
                            <treeselect
                                :multiple="false"
                                :options="paymentRuleTypes"
                                label="name"
                                :searchable="false"
                                class="vue-treeselect--custom vue-treeselect--with-icon vue-treeselect--without-effect"
                                placeholder="اختر احد القيود"
                                v-model="theRule.selectedRule"
                                @input="pushSelectedType($event)"
                            />
                        </div>
                    </div>

                    <div v-if="theRule.selectedRule === 'cart-price'" :class="{'condition': true, 'has-error': errors['options.cart_price_condition']? true: false}">
                        <treeselect
                            :multiple="false"
                            :searchable="false"
                            :options="comparisonOperators"
                            v-model="rule.options.cart_price_condition"
                            class="vue-treeselect--custom vue-treeselect--without-effect"
                            placeholder="الشرط"
                            :normalizer="normalizer"
                        />
                        <form-error class="error-msg mb-0" v-if="errors['options.cart_price_condition']" :errors="errors">
                            {{ errors['options.cart_price_condition']? errors['options.cart_price_condition'].toString() : 'حقل شرط قيمة سلة المشتريات مطلوب.'}}
                        </form-error>
                    </div>
                    <div v-if="theRule.selectedRule === 'products-quantity'" :class="{'condition': true, 'has-error': errors['options.cart_price_condition']? true: false}">
                        <treeselect
                            :multiple="false"
                            :searchable="false"
                            :options="comparisonOperators"
                            v-model="rule.options.products_quantity_condition"
                            class="vue-treeselect--custom vue-treeselect--without-effect"
                            placeholder="الشرط"
                            :normalizer="normalizer"
                        />
                        <form-error class="error-msg mb-0" v-if="errors['options.cart_price_condition']" :errors="errors">
                            {{ errors['options.cart_price_condition']? errors['options.cart_price_condition'].toString() : 'حقل شرط قيمة سلة المشتريات مطلوب.'}}
                        </form-error>
                    </div>

                    <div v-if="theRule.selectedRule === 'country-phone'" :class="{'condition': true, 'has-error': !!errors['options.country_phone_condition']}">
                        <treeselect
                            :multiple="false"
                            :searchable="false"
                            :options="countriesOperators"
                            v-model="rule.options.country_phone_condition"
                            class="vue-treeselect--custom vue-treeselect--without-effect"
                            placeholder="الشرط"
                            :normalizer="normalizer"
                        />
                        <form-error class="error-msg mb-0" v-if="errors['options.country_phone_condition']" :errors="errors">
                            {{ (errors['options.country_phone_condition']||'حقل شرط إنتماء الدولة مطلوب.').toString()}}
                        </form-error>
                    </div>
                    <div v-if="theRule.selectedRule === 'country-ip-address'" :class="{'condition': true, 'has-error': !!errors['options.country_ip_condition']}">
                        <treeselect
                            :multiple="false"
                            :searchable="false"
                            :options="countriesOperators"
                            v-model="rule.options.country_ip_condition"
                            class="vue-treeselect--custom vue-treeselect--without-effect"
                            placeholder="الشرط"
                            :normalizer="normalizer"
                        />
                        <form-error class="error-msg mb-0" v-if="errors['options.country_ip_condition']" :errors="errors">
                            {{ (errors['options.country_ip_condition']||'حقل شرط إنتماء الدولة مطلوب.').toString()}}
                        </form-error>
                    </div>

                    <div class="fields-group" v-if="theRule.selectedRule === 'cart-price'">
                        <div class="input-group">
                            <input :id="`cart_price_${index}`" type="text" class="form-control full-bordered _parseArabicNumbers pl-15" v-model="rule.options.cart_price" placeholder="قيمة المشتريات">
                            <span class="input-group-addon">
                                ر.س
                            </span>
                        </div>
                    </div>
                    <div class="fields-group" v-if="theRule.selectedRule === 'products-quantity'" @keyup="numberValidate">
                        <div class="input-group wide">
                            <input :id="`products_quantity_${index}`" type="text" class="form-control full-bordered _parseArabicNumbers pl-15" v-model="rule.options.products_quantity" placeholder="كمية المنتجات">
                        </div>
                    </div>

                    <div v-if="theRule.selectedRule === 'country-phone'" :class="{'outer-field': true, 'has-error': !!errors['options.country_phone_code']}">
                        <treeselect
                            :multiple="true"
                            :searchable="true"
                            :options="countriesByMobileCode"
                            v-model="rule.options.country_phone_code"
                            class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--enhanced-tags"
                            placeholder="اختر الدولة"
                            :normalizer="countryNormalizer"
                            noResultsText="عفوا! لا توجد نتائج بحث"
                        />
                        <form-error class="error-msg mb-0" v-if="errors['options.country_phone_code']" :errors="errors">
                            {{ (errors['options.country_phone_code']||'حقل الدولة مطلوب.').toString()}}
                        </form-error>
                    </div>


                    <div v-if="theRule.selectedRule === 'country-ip-address'" :class="{'outer-field': true, 'has-error': !!errors['options.country_ip_address_code']}">
                        <treeselect
                            :multiple="true"
                            :searchable="true"
                            :options="countriesByCode"
                            v-model="rule.options.country_ip_address_code"
                            class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--enhanced-tags"
                            placeholder="اختر الدولة"
                            :normalizer="countryNormalizer"
                            noResultsText="عفوا! لا توجد نتائج بحث"
                        />
                        <form-error class="error-msg mb-0" v-if="errors['options.country_ip_address_code']" :errors="errors">
                            {{ (errors['options.country_ip_address_code']||'حقل الدولة مطلوب.').toString()}}
                        </form-error>
                    </div>

                    <template v-if="theRule.selectedRule === 'credit-card-country'">
                        <div :class="{'condition': true, 'has-error': !!errors['options.credit_card_condition']}">
                            <treeselect
                                :multiple="false"
                                :searchable="false"
                                :options="countriesOperators"
                                v-model="rule.options.credit_card_condition"
                                class="vue-treeselect--custom vue-treeselect--without-effect"
                                placeholder="الشرط"
                                :normalizer="normalizer"
                            />
                            <form-error class="error-msg mb-0" v-if="errors['options.credit_card_condition']"
                                        :errors="errors">
                                {{ (errors['options.credit_card_condition'] || 'حقل شرط دولة البطاقة مطلوب.').toString() }}
                            </form-error>
                        </div>
                        <div :class="{'outer-field': true, 'has-error': !!errors['options.credit_card_country']}">
                            <treeselect
                                :multiple="true"
                                :searchable="true"
                                :options="countriesByCode"
                                v-model="rule.options.credit_card_country"
                                class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--enhanced-tags"
                                placeholder="اختر الدولة"
                                :normalizer="countryNormalizer"
                                noResultsText="عفوا! لا توجد نتائج بحث"
                            />
                            <form-error class="error-msg mb-0" v-if="errors['options.credit_card_country']" :errors="errors">
                                {{ (errors['options.credit_card_country']||'حقل دولة البطاقة مطلوب.').toString()}}
                            </form-error>
                        </div>
                    </template>

                  <div v-if="theRule.selectedRule === 'customer-group'" :class="{'condition': true, 'has-error': !!errors['options.customer_group_id']}">
                    <treeselect
                        :multiple="false"
                        :searchable="true"
                        :options="customerGroups"
                        v-model="rule.options.customer_group_id"
                        class="vue-treeselect--custom vue-treeselect--without-effect"
                        placeholder="اختر مجموعة العملاء"
                        :normalizer="customerGroupNormalizer"
                    />
                    <form-error  class="error-msg mb-0" v-show="errors['options.customer_group_id']" :errors="errors">
                      {{ errors['options.customer_group_id']? errors['options.customer_group_id'].toString() : 'حقل مجموعة العملاء مطلوب.'}}
                    </form-error>
                  </div>

                  <template v-if="theRule.selectedRule === 'product-categories'">
                    <div :class="{'condition': true, 'has-error': !!errors['options.categories_condition']}">
                      <treeselect
                          :multiple="false"
                          :searchable="false"
                          :options="categoriesOperators"
                          v-model="rule.options.categories_condition"
                          class="vue-treeselect--custom vue-treeselect--without-effect"
                          placeholder="الشرط"
                          :normalizer="normalizer"
                      />
                      <form-error class="error-msg mb-0" v-if="errors['options.categories_condition']"
                                  :errors="errors">
                        {{ (errors['options.categories_condition'] || 'حقل شرط التصنيف مطلوب.').toString() }}
                      </form-error>
                    </div>
                    <div :class="{'outer-field': true, 'has-error': !!errors['options.categories_ids']}">
                      <treeselect
                          :multiple="true"
                          :searchable="true"
                          :options="categories"
                          :flat="true"
                          :default-expand-level="2"
                          :is-default-expanded="true"
                          v-model="rule.options.categories_ids"
                          class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--enhanced-tags"
                          placeholder="اختر التصنيف"
                          :normalizer="customerGroupNormalizer"
                          noResultsText="عفوا! لا توجد نتائج بحث"
                      />
                      <form-error class="error-msg mb-0" v-if="errors['options.categories_ids']" :errors="errors">
                        {{ (errors['options.categories_ids']||'حقل التصنيف مطلوب.').toString()}}
                      </form-error>
                    </div>
                  </template>
                  <button class="btn btn-delete-circle ml-10 mb-15" @click="deleteRule(index, theRule.selectedRule)" v-if="paymentsRules.length > 1">
                        <i class="sicon-trash-2"></i>
                  </button>
                </div>
                <div v-if="theRule.selectedRule === 'product-categories'" class="mb-20">
                  <div class="rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg">
                    <input type="checkbox" v-model="rule.options.other_categories_restricted" name="other_categories_restricted" id="other_categories_restricted">
                    <label for="other_categories_restricted">
                      <h6 class="no-margin ml-10 font-regular">تطبيق الإجراء عند إضافة منتجات من تصنيفات أخرى ليست من ضمن القيد مع التصنيفات المحددة</h6>
                      <small class="text-muted text-muted-samll ml-10">
                        في حال تم إضافة منتجات في سلة المشتريات مع المنتجات المحددة من قبلك سيتم تطبيق الإجراء
                      </small>
                    </label>
                  </div>
                </div>
                <hr v-if="paymentsRules.length > 1 && index + 1 !== paymentsRules.length"/>
            </div>


            <button class="btn btn-add btn--outlined primary mb-10"
                    v-if="rule.payment_methods_slugs !== null || rule.payment_methods_slugs.length"
                    type="button" @click="addRule"
                    :disabled="paymentsRules.length === paymentRuleTypes.length">
                <i class="sicon-add"></i>
                اضافة قيد جديد
            </button>
        </div>
    </div>
</template>

<script>

import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import FormError from "../../shipping-role/components/FormError";

export default {
    props: [
        'rule',
        'comparisonOperators',
        'countriesOperators',
        'categoriesOperators',
        'paymentRuleTypes',
        'countriesByCode',
        'countriesByMobileCode',
        'errors',
        'isPaymentMethodSelected',
        'customerGroups',
        'categories',
    ],

    data () {
        return {
            disabled: false,
            paymentsRules: [
                {
                    selectedRule: null,
                }
            ],
            normalizer(node) {
                return {
                    id: node.value,
                    label: node.name,
                }
            },
            customerGroupNormalizer(node) {
              return {
                id: node.id,
                label: node.name,
                children: node.sub_categories,
              }
            },
          countryNormalizer(node) {
                return {
                    id: node.code,
                    label: node.name,
                }
            },

        }
    },

    components: {
        Treeselect,
        FormError
    },

    watch: {
		paymentsRules: {
			handler(newVal){
				if(newVal){
					for(let opt of this.paymentRuleTypes){
						const findValue = this.paymentsRules.find(item => {
							return item.selectedRule === opt.id
						});

						if(findValue){
							opt.isDisabled = true;
						} else{
							opt.isDisabled = false;
						}
					}
				}
			},
			deep: true
		}
    },

    created () {
        if (this.rule.options.types.length) {
            this.paymentsRules = [];
            for (let i = 0; i < this.rule.options.types.length; i++) {
                this.paymentsRules.push({selectedRule: this.rule.options.types[i]}); 
            }
        }
    },

    methods: {
        addRule () {
            if (this.paymentsRules[this.paymentsRules.length -1].selectedRule == null) {
                swal({
                    title: 'تنبيه',
                    text: 'قم باختيار نوع القيد',
                    type: 'error',
                    showConfirmButton: true,
                    confirmButtonText: 'موافق',
                });
                return false;
            }

            this.paymentsRules.push({
                selectedRule: null,
            });
        },

        deleteRule (index, type) {
            this.paymentsRules.splice(index, 1);
            this.pushSelectedType();
            this.resetValues(type)
        },

        pushSelectedType(val) {
            var typesOptions =  [];
            this.paymentsRules.forEach((payRule, index) => {
                if (payRule.selectedRule !== null) {
                    typesOptions.push(payRule.selectedRule);
                } else {
                    return null
                }
            });

            this.rule.options.types = typesOptions;
        },

        resetValues(type) {
            switch(type) {
                case 'cart-price':
                    this.rule.options.cart_price_condition = null;
                    this.rule.options.cart_price = null;
                break;
                case 'products-quantity':
                    this.rule.options.products_quantity_condition = null;
                    this.rule.options.products_quantity = null;
                break;
                case 'country-phone':
                    this.rule.options.country_phone_code = null;
                break;
                case 'country-ip-address':
                    this.rule.options.country_ip_address_code = null;
                break;
                case 'customer-group':
                    this.rule.options.customer_group_id = null;
                break;
              case 'product-categories':
                this.rule.options.categories_ids = null;
                this.rule.options.categories_condition = null;
                this.rule.options.other_categories_restricted = null;
                break;
            }
        },

        numberValidate(e) {
            let val = e.target.value;
            val.includes(".") ? this.rule.options.products_quantity = '' : null; 
        }
    }
}
</script>