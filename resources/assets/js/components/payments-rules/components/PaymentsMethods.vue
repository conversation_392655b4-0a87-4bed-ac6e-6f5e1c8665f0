<template>
    <div class="rules mb-40">
        <div class="form-group mt-30 mb-30" :class="{'has-error': errors['title']}">
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-type-square"></i>
                </span>
                <input type="text" class="form-control" v-model="rule.title" placeholder="أدخل عنوان القيد ( لايظهر للعميل )">
            </div>
            <form-error class="error-msg mb-0" v-if="errors['title']">
                {{errors.title[0]}}
            </form-error>
        </div>

        <p class="rules__title text-primary">
            <span>1</span>
            تحديد طريقة الدفع
        </p>
        <div class="form-group">
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-debit-card-back"></i>
                </span>
                <treeselect
                    :multiple="true"
                    :options="paymentsTypes"
                    :searchable="false"
                    class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--with-icon vue-treeselect--without-effect"
                    placeholder="اختر طريقة الدفع..."
                    v-model="rule.payment_methods_slugs"
                />
            </div>
        </div>
    </div>
</template>


<script>

import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import FormError from '../layouts/FormError';

export default {
    props: ['rule', 'payments', 'errors'],

    data () {
        return {
            paymentsTypes: []
        }
    },

    created () {
        for (const [key, value] of Object.entries(this.payments)) {
            this.paymentsTypes.push(
                {
                    id: key,
                    label: value
                }
            )
        }
    },

    components: {
        Treeselect,
        FormError
    }
}
</script>