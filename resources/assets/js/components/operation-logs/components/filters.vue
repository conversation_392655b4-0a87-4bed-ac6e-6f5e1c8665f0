<template>
  <div :class="{
    'rec-filter-wrapper': true,
    'rec-filter-wrapper-products': true,
    checkPlacement: extraClass,
  }">
    <div class="rec-filter-cont">
      <form>
        <div class="rec-filters">
          <h1 class="title title--small">
            <i class="sicon-filter"></i> فرز سجل الاحداث حسب
            <button id="filter_close" type="button" class="btn btn--circular"></button>
          </h1>
          <form>
            <div class="form-group mb-20 mt-20">
              <div class="rec-filter__section">
                <button type="button" data-toggle="collapse" data-target="#filter_event_types_options"
                  class="btn filter-head" aria-expanded="true">
                  <span>
                    <i class="sicon-archive"></i>
                    نوع الحدث
                  </span>
                </button>

                <div id="filter_event_types_options" class="filter-content collapse in" aria-expanded="true" style="">
                  <v-treeselect v-model="filter.type" :multiple="false" :options="types" :searchable="false" :normalizer="normalizer" class="
                      vue-treeselect--custom vue-treeselect--without-effect
                    " placeholder="نوع الحدث" />
                </div>
              </div>
            </div>
            <div class="form-group mb-20 mt-20">
              <div class="rec-filter__section">
                <button type="button" data-toggle="collapse" data-target="#filter_event_types_options"
                  class="btn filter-head" aria-expanded="true">
                  <span>
                    <i class="sicon-archive"></i>
                    الموظف
                  </span>
                </button>

                <div id="filter_event_types_options" class="filter-content collapse in" aria-expanded="true" style="">
                  <v-treeselect v-model="filter.user" :multiple="false" :options="users" :searchable="false" :normalizer="normalizer" class="
                      vue-treeselect--custom vue-treeselect--without-effect
                    " placeholder="نوع الحدث" />
                </div>
              </div>
            </div>
            <div class="rec-filter__section">
              <button type="button" data-toggle="collapse" data-target="#filter_date_options" class="btn filter-head"
                aria-expanded="true">
                <span>
                  <i class="sicon-calendar-dates"></i>
                  تاريخ الحدث
                </span>
              </button>

              <div id="filter_date_options" class="filter-content collapse in" aria-expanded="true" style="">
                <div :class="{
                  'form-group mb-5': true,
                  'has-error': errors && errors.startDate,
                }">
                  <v-datepicker id="expiry_date" v-model="filter.from" :style="{ width: '100%' }" value-type="format"
                    :clearable="false" class="mx--right-position" :append-to-body="false" name="expiry_date"
                    title="بداية الوقت" placeholder="من" input-class="form-control full"
                    :disabled-date="disabledDates">
                  </v-datepicker>
                </div>

                <div :class="{
                  'form-group': true,
                  'has-error': errors && errors.endDate,
                }">
                  <v-datepicker id="expiry_date" v-model="filter.to" :style="{ width: '100%' }" value-type="format"
                    :clearable="false" class="mx--right-position" :append-to-body="false" name="expiry_date"
                    title="نهاية الوقت" placeholder="إلى" input-class="form-control full"
                    :disabled-date="disabledDates">
                  </v-datepicker>
                </div>
              </div>
            </div>

          </form>

          <div class="rec-filter__submit">
            <button type="button" class="btn btn-tiffany btn-filter-submit rec-fvm" data-inline-loader
              @click.prevent="submitFilter($event)">
              عرض النتائج
            </button>
            <button type="button" class="btn btn-outline-dark btn-filter-reset  rec-fvm" @click.prevent="resetFilter">
              إعادة تعيين
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import DatePicker from "vue2-datepicker";
import "vue2-datepicker/locale/ar-sa";
import "vue2-datepicker/index.css";
export default {
  components: {
    "v-datepicker": DatePicker,
  },
  props: ["extraClass", "exportUrlParam", "types" ,'users'],
  data() {
    return {
      filter: {},
      normalizer(node) {
        return {
          label: node.label,
          name: node.name,
          id: node.name,
        };
      },
      errors: {},
    };
  },
  created() {
    this.$parent.$on("cancelFilters", this.resetFilter);
  },
  mounted() {
    $(".rec-filter-wrapper-products .btn-filter-submit").click(function (e) {
      e.preventDefault();
      $(this).parents(".rec-filter-wrapper-products").removeClass("reveal");
      $("body").removeClass("rec-filter-open");
    });
  },
  methods: {
    disabledDates(date) {
      const today = new Date();
      const twoWeekAgo = new Date();
      twoWeekAgo.setDate(today.getDate() - 15);
      return date < twoWeekAgo || date > today;
    },
    resetFilter() {
      this.filter = {};
      this.$emit("cancelFiltering");
    },
    submitFilter(event) {
      this.$emit("updateFilters", this.filter, event);
    },
  },
};
</script>
<style lang="scss">
.vue-treeselect--custom .vue-treeselect__control {
  border-right: 1px solid #eee !important;
}
</style>
