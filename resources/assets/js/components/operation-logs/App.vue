<template>
  <div>
    <section class="alert-box alert-box--warning"><i class="sicon-warning"></i>
      <article class="d-flex">
        <p class="mr-5">
          اﻟﺴﺠﻞ ﻳﻌﺮض اﻟﻌﻤﻠﻴﺎت اﻟﺘﻲ ﺗﻤﺖ ﺧﻠﺎل 10 ﻳﻮم
        </p>
      </article>
    </section>
<!--    <div class="view-control text-right mb-20">-->
<!--      <div class="filter-mode bulk-edit-filter text-right" :data-filter-mode="filters && filtering">-->
<!--        <a id="rec_filter_toggle" class="btn btn-filter-toggle"><i class="sicon-filter"></i> تصفية-->
<!--          <i v-show="filters && filtering" class="sicon-cancel" @click="cancelFilter"></i></a>-->
<!--      </div>-->
<!--    </div>-->
<!--    <filters :extra-class="true" :types="types" :users="userFilters" @updateFilters="updateFilters"-->
<!--      @cancelFiltering="cancelFiltering">-->
<!--    </filters>-->
    <div class="row">
      <div class="col-xs-12">
        <div class="panel panel-default mb-20">
          <div class="panel-heading">
            <h6 class="panel-title">
              <i class="sicon-archive inline-el"></i>
              ﺳﺠﻞ التحديثات العمليات
            </h6>
            <div class="heading-elements">
              <div class="btn-group">
  <template v-if="operation_log_type === 'bulk-print-policies'">
    <button class="btn btn-primary btn-xs" v-on:click="merging">
      <i class="sicon-download position-left"></i> دمج البوليصات
    </button>
  </template>
  <template v-if="operation_id">
    <a href="/settings/component/operation-logs" class="btn btn-primary btn-xs" target="_blank">
      <i class="sicon-arrow-left position-left"></i> رجوع
    </a>
  </template>
</div>


          </div>
        </div>
          <div class="table-responsive">
            <table class="table table-hover text-nowrap table-no-top-border">
              <thead>
                <tr class="special-offer-listing-entry">
                  <th>العمليه</th>
                  <th>القسم</th>
                  <th>حالة الإجراء</th>
                  <th>التاريخ والوقت</th>
                  <th>المسؤول عن العملية</th>
                  <th>التحميل</th>
                </tr>
              </thead>
              <tbody class="panel-group rec-accordion mb-0">
                <tr v-for="(log, i) in logs" :key="i" class="">
                  <td v-html="log.label_with_url"></td>
                  <td>{{ log.category }}</td>
                  <td>{{ log.status }}</td>
                  <td>{{ log.created_at }}</td>
                  <td>
                    {{ log.user_name }}
                    <br />
                    <b class="text text-muted">{{ log.role }}</b>
                  </td>
                  <td class="text-center">
                    <span>
                      <a v-if="log.downloadRoute" target="_blank" :href="log.downloadRoute">
                        <i class="sicon-cloud-download"></i>
                      </a>
                      <p v-else>
                        --
                      </p>
                    </span>
                  </td>
                </tr>

              </tbody>
            </table>
          </div>
          <div v-if="!logs.length" class="text-center text-muted text-muted-small p-20">
            لا توجد نتائج
          </div>
        </div>
        <mugen-scroll v-show="loading" :handler="loadMore" :should-handle="loading" class="align-center">
          <span class="loader mt-10"></span>
        </mugen-scroll>
      </div>
    </div>
  </div>
</template>
<script>
import filters from "./components/filters";
import MugenScroll from "vue-mugen-scroll";
import { PDFDocument } from 'pdf-lib';

export default {
  components: {
    filters,
    "mugen-scroll": MugenScroll,
  },
  filters: {
    pretty: function (value) {
      return JSON.stringify(JSON.parse(value), null, 2);
    },
  },
  data() {
    return {
      logs: [],
      page: 1,
      loading: true,
      filtering: false,
      filters: null,
      types: [],
      users: [],
      operation_id: null,
      operation_log_type: null
    };
  },
  computed: {
    userFilters() {
      return [... new Set(this.users.map((user) => {
        return {
          id: user.id,
          label: user.first_name + " " + user.last_name,
          name: user.id
        };
      }))];
    },
  },
  created() {
    let urlParams = new URLSearchParams(window.location.search);
    if(urlParams.has('operation_id')) {
      this.operation_id = urlParams.get('operation_id')
    }
  },
  mounted() {
    
    $("#rec_filter_toggle").click(function (e) {
      e.preventDefault();
      setTimeout(() => {
        // TODO double check the class name here
        $(".settings-webhooks-logs .rec-filter-wrapper-products")
          .removeClass("conceal")
          .addClass("reveal");
      }, 50);
      $("body").addClass("rec-filter-open");
    });

  },
  methods: {
    cancelFilter(event) {
      event.stopPropagation();
      this.filtering = false;
      this.filters = null;
      this.$emit("cancelFilters");
      this.page = 1;
      this.loadMore();
    },
    updateFilters(filters, event) {
      this.page = 1;
      this.logs = [];
      this.filters = filters;
      this.filtering = true;
      this.loadMore(event);
    },
    loadMore(event) {
      // this.page++;
      let data = {
        page: this.page,
      };
      if (this.filters) {
        data = this.appendFilters(data);
      }

      const url = this.operation_id ? `/settings/component/operation-logs?operation_id=${this.operation_id}` : '/settings/component/operation-logs'

      showLoading(event)
      $.ajax({
        url: url,
        dataType: "json",
        contentType: "application/json",
        data: data,
        async: false,
         success: (res) => {
          const data = res?.data || []
          this.logs.push(...data);

          if (res?.cursor?.next) {
          
            this.page++
            this.loading = true
          } else {
            this.loading = false;
          }
        },
        complete: () => {
        
          hideLoading();
         
        },
      });
    },
    appendFilters(data) {
      data.type = this.filters.type;
      data.user = this.filters.user;
      data.from = this.filters.from;
      data.to = this.filters.to;
      return data;
    },
    cancelFiltering() {
      this.filtering = false;
      this.filters = null;
      this.page = 1;
      this.loadMore();
    },
    merging() {

      showLoading();

    const pdfUrls = this.logs.map((v) => v.downloadRoute);

    this.mergePdfs(pdfUrls).then(mergedPdfFile => {
      // Handle the merged PDF file, e.g., save it or display it in the UI
      const blob = new Blob([mergedPdfFile], { type: 'application/pdf' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = this.operation_id + '.pdf';
      hideLoading();
      link.click();

    });
},
async mergePdfs(pdfUrls) {
    const mergedPdf = await PDFDocument.create();
    for (const url of pdfUrls) {
      const arrayBuffer = await fetch(url).then(res => res.arrayBuffer());
      const pdfDoc = await PDFDocument.load(arrayBuffer);
      const copiedPages = await mergedPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
      copiedPages.forEach(page => mergedPdf.addPage(page));
    }

    const mergedPdfFile = await mergedPdf.save();
    return mergedPdfFile;
    }
},
};
</script>
