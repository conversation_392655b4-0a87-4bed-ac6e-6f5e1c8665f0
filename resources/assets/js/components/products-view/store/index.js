import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

import mahally from "./modules/mahally";
import selectAll from "./modules/selectAll";
import filters from "./modules/filters";
import wholesaleMarketplace from "./modules/wholesaleMarketplace";


const createStore = () => {
  return new Vuex.Store({
    modules: {
      mahally: mahally,
      selectAll: selectAll,
      filters: filters,
      wholesaleMarketplace: wholesaleMarketplace
    }
  });
};

export default createStore;