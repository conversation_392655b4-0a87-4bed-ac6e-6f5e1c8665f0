import stubMahlyCategories from './../../../stubs/mahlyCategoreis'

const getMahallyCetogries =  (state) => {
    if(state.mahallyCategories.length) {
        return state.mahallyCategories;
    }
    
    /** todo we need to check if it's local or not  */
    return stubMahlyCategories;
}

const isMahallyFeaturedEnabled = (state) => {
    return state.mahally?.status;
}

export default {
    getMahallyCetogries,
    isMahallyFeaturedEnabled
}