import stubMahlyCategories from './../../../stubs/wholesaleCategories'

const getWholesaleCetogries =  (state) => {
    if(state.wholesaleCategories.length) {
        return state.wholesaleCategories;
    }
    
    /** todo we need to check if it's local or not  */
    return stubMahlyCategories;
}

const isWholesaleFeaturedEnabled = (state) => {
    return state.wholesale?.status;
}

export default {
    getWholesaleCetogries,
    isWholesaleFeaturedEnabled
}