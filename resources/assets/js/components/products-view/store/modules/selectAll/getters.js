export default {
    countSelectedProducts : (state, getters) => {
        if(getters.isSelectAll) {
            return getters.countTotalProducts
        }

        return state.selected_products.length;
    },
    countTotalProducts: (state) => state.count_total_products,
    isSelectAll: (state) => {
        return state.select_all
    },
    getSelectedProducts: (state) => {
        return state.selected_products
    },
    getSelectedProductIds: (state) => {
        return state.selected_products.map((p) => p.id)
    },
    getSelectedProductsWithName: (state) => {
        return state.selected_products.map((p) => {
            return {
                'id': p.id, 
                'name': p.name}
        })
    },
    getUnSelectedProductIds: (state) => {
        return state.un_selected_products.map((p) => p.id)
    }
}