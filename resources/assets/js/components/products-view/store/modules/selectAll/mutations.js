const setSelectedProducts = (state, products) => state.selected_products = products
const setSelectedProduct = (state, product) => state.selected_products.push(product)
const setCountTotalProducts = (state, count) => state.count_total_products = count
const removeFromSelectedProducts = (state, id) => {
     state.selected_products = state.selected_products.filter(product => product.id !== id)
}
const setSelectAll = (state, isSelectAll) => {
    state.select_all = isSelectAll
    // let's clear selected cuse once we click all we don't care about 
    // any selected products...
    state.selected_products = []
}

const resetSelectAll = (state) => {
    state.select_all = false
    state.selected_products = []
    state.un_selected_products = []
}


const setUnSelectedProducts = (state, products) => state.un_selected_products = products
const setUnSelectedProduct = (state, product) => state.un_selected_products.push(product)


export default {
    setSelectedProducts,
    removeFromSelectedProducts,
    setCountTotalProducts,
    setSelectAll,
    setSelectedProduct,
    // unselected 
    setUnSelectedProduct,
    setUnSelectedProducts,
    resetSelectAll
}