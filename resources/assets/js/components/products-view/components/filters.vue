<!------------ Product Filters By ( status , brands , categories , types or branches) ------------>
<template>
    <div :class="{ 'rec-filter-wrapper': true, 'rec-filter-wrapper-products': true, 'checkPlacement': extraClass }">
        <div class="rec-filter-cont">
            <form>
                <div class="rec-filters">
                    <h1 class="title title--small">
                        <i class="sicon-filter"></i> فرز المنتجات حسب
                        <button id="filter_close" type="button" class="btn btn--circular"></button>
                    </h1>

                    <div v-for="(filter, index) in filters" :key="index" class="rec-filter__section">
                        <button type="button" :data-target="`#filter_${index}_categories`" data-toggle="collapse"
                            class="btn filter-head" aria-expanded="true">
                            <span> <i :class="filter.icon"></i>{{ filter.label }}</span>
                        </button>
                        <div :id="`filter_${index}_categories`" class="collapse filter-content collapse in"
                            aria-expanded="true">

                            <ul class="rec-list rec-list--vertical">
                                <checkbox v-if="filter.type === 'checkbox'" :filter="filter" :check-for-all="checkForAll">
                                </checkbox>
                                <list v-if="filter.name === 'brands'" :filter="filter" :placeholder="'الماركة'"
                                    :update-selected-list="updateSelectedBrands" :index="index"></list>
                                <list v-if="filter.name === 'branches'" :filter="filter" :placeholder="'الفرع'"
                                    :update-selected-list="updateSelectedBranches" :index="index"></list>
                                <list v-if="filter.name === 'mahly'" :filter="filter" :placeholder="'محلي'"
                                    :update-selected-list="updateSelectedMahly" :index="index" :multiple="false"></list>
                                <categories v-if="filter.name === 'categories'" :filter="filter"
                                    :update-selected-list="updateSelectedCategories" :index="index"></categories>
                            </ul>
                        </div>
                    </div>
                    <div class="rec-filter__submit">
                        <button type="button" class="btn btn-tiffany btn-filter-submit" @click="submitFilters">عرض
                            النتائج
                        </button>
                        <button type="reset" class="btn btn-outline-dark btn-filter-reset" @click="resetFilters">إعادة
                            تعيين
                        </button>
                    </div>

                    <div class="rec-filter__section mt-10">
                        <div class="btn-group rec-btn-group full-width" role="group">

                            <!--            export products or quantities depending on the flag quantitiesExport -->
                            <a id="filterOptions" type="button" class="btn btn-wide btn-default btn-icon-prepend rec-fvm"
                                @click="exports">
                                <i class="sicon-file-download"></i>
                                تصدير النتائج
                            </a>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>


<script>

import initialData from "../../../services/ProductService";
import Http from "../../../utils/http";
import Checkbox from "./filterTypes/checkbox";
import List from "./filterTypes/list";
import Categories from "./filterTypes/categories";
import { mapMutations, mapActions } from 'vuex'

export default {
    components: { Categories, Checkbox, List },
    props:{
        extraClass: {
            type: Boolean,
        },
        exportUrlParam:{
            type: String,
            default: null
        },
        isUsingTemplates:{
            type: Boolean,
            default: false
        }
    },
    data: () => ({
        filters: [],
        selected_status: [],
        selected_types: [],
        selected_filters: [],
        selected_brands: [],
        selected_branches: [],
        selected_categories: [],
        selected_mahly: [],
        selected_wholesale: [],
    }),
    created() {
        this.selected_filters = initialData.getDatum('filters', []) || [];

        if (this.selected_filters.length) {
            this.selected_mahly = this.selected_filters.mahly || [];
            this.selected_brands = this.selected_filters.brands || [];
            this.selected_branches = this.selected_filters.branches || [];
            this.selected_categories = this.selected_filters.categories || [];
            this.selected_wholesale = this.selected_filters.wholesale || [];
        }
    },
    mounted() {
        Salla.event.addEventListener('products::fetch-filters', this.getFilters);
        Salla.event.addEventListener('products::set-selected-filters', this.setSelectedFilters);
        Salla.event.addEventListener('products::reset-filters', this.resetSelectedFilters);
        Salla.event.addEventListener('products::export-filter-results', this.exportFilterResults);
        $('#export-group-product-template-modal').on('shown.bs.modal', function () {
          $('#template_id').selectpicker('refresh')
        })
        $('.rec-filter-wrapper-products .btn-filter-submit').click(function (e) {
            e.preventDefault()
            $(this).parents('.rec-filter-wrapper-products').removeClass('reveal');
            $(this).parents('.rec-filter-wrapper-products').addClass('conceal');
            $('body').removeClass('rec-filter-open')
        });

        $('.bulk-edit-filter').click(function () {
            $('.rec-filter-wrapper-products').addClass('reveal');
            $('.rec-filter-wrapper-products').removeClass('conceal');
            $('body').addClass('rec-filter-open')
        })
    },
    beforeDestroy() {
        document.removeEventListener('products::fetch-filters', this.getFilters);
        document.removeEventListener('products::set-selected-filters', this.setSelectedFilters);
        document.removeEventListener('products::reset-filters', this.resetSelectedFilters)
        document.removeEventListener('products::export-filter-results', this.exportFilterResults);
    },
    methods: {
        // Update selected brands values
        updateSelectedBrands(index, value) {
            this.selected_brands = [];
            value.forEach((val) => {
                this.selected_brands.push(val.value)
            });
        },

        // update selected branches values
        updateSelectedBranches(index, value) {
            this.selected_branches = [];
            value.forEach((val) => {
                this.selected_branches.push(val.value)
            });
        },

        // update selected categories values
        updateSelectedCategories(index, value) {
            this.selected_categories = [];
            value.forEach((val) => {
                this.selected_categories.push(val.value)
            });
        },

        // update selected single mahly option
        updateSelectedMahly(index, val) {
            this.selected_mahly = val ? val.value : null
        },

        // Get all filters Data
        getFilters(e) {
            if (Object.keys(this.filters).length) {
                return;
            }
            Http.get('/products/filters', ({ data }) => {
                this.filters = data.data.filters;

                // once filters are fetched, select the specified value for the dropdown menu
                if (e.detail.mahally) {
                    this.selectProductMahally(e.detail.prop);
                }
            }, ({ error }) => {
                laravel.errors.renderValidation(error.data, ' ');
            }, this.selected_filters)
        },

        // prepare all request data before submit filters
        submitFilters() {
            this.selected_types = window._.filter(this.filters['types'].options, o => o.selected === true).map(v => v.value);
            this.selected_status = window._.filter(this.filters['status'].options, o => o.selected === true).map(v => v.value);
            if(this.filters['wholesale']) {
                this.selected_wholesale = window._.filter(this.filters['wholesale'].options, o => o.selected === true).map(v => v.value);
            } else {
                this.selected_wholesale = [];
            }
            let payload = {
                'types': this.selected_types,
                'status': this.selected_status,
                'brands': this.selected_brands,
                'branches': this.selected_branches,
                'categories': this.selected_categories,
                'mahly': this.selected_mahly,
                'wholesale': this.selected_wholesale,
            };

            window.Salla.event.createAndDispatch('products::submit-filters',payload)

            // let's notify our store about filters selected...
            this.setSelectedCustomFilters(payload)

            this.resetFiltersAction()
        },
        // Reset filters values
        resetFilters() {
            Salla.event.createAndDispatch('products::reset-filters')
            this.resetSelectedFilters();

            // let's notify our store about filters un selected...
            this.setSelectedCustomFilters([])

            this.resetFiltersAction()
        },

        // remove "all" selected value
        // if the user select any filter option
        checkForAll(value, index) {
            if (value.srcElement.id === 'StatusFilter_0' && value.target.checked) {
                this.filters.status.options.forEach(ele => {
                    ele.selected = false
                });
                this.filters.status.options[0].selected = true
            } else if ((value.srcElement.id !== `StatusFilter_0` && value.srcElement.id !== `ProductTypeFilter_${index}`) && value.target.checked) {
                this.filters.status.options[0].selected = false
            } else if (value.srcElement.id === 'ProductTypeFilter_0' && value.target.checked) {
                this.filters.types.options.forEach(ele => {
                    ele.selected = false
                });
                this.filters.types.options[0].selected = true
            } else if ((value.srcElement.id !== `ProductTypeFilter_0` && value.srcElement.id !== `StatusFilter_${index}`) && value.target.checked) {
                this.filters.types.options[0].selected = false
            }
        },
        setSelectedFilters(e) {
            this.selected_filters = e.detail.selected_filters || [];
            if (e.detail.selected_filters) {
                this.selected_brands = this.selected_filters.brands || [];
                this.selected_branches = this.selected_filters.branches || [];
                this.selected_categories = this.selected_filters.categories || [];
                this.selected_mahly = this.selected_filters.mahly || [];
                this.selected_wholesale = this.selected_filters.wholesale || [];
            }
        },

        resetSelectedFilters() {
            this.selected_filters = this.selected_branches = this.selected_brands = this.selected_categories = this.selected_mahly = this.selected_wholesale = [];
        },

        showGroupExportModal() {
            $('#template_id').selectpicker('refresh')
            $('#export-group-product-template-modal').modal('show')
        },

        // export products or quantities
        exportFilterResults(e) {
            this.selected_types = window._.filter(this.filters['types'].options, o => o.selected === true).map(v => v.value);
            this.selected_status = window._.filter(this.filters['status'].options, o => o.selected === true).map(v => v.value);
          if(this.filters['wholesale']) {
            this.selected_wholesale = window._.filter(this.filters['wholesale'].options, o => o.selected === true).map(v => v.value);
          } else {
            this.selected_wholesale = [];
          }
            let endpoints = {
                export_products_quantities: 'export/product-quantities',
                export_products_prices: 'export/product-prices',
                export_products_seo: 'export/product-seo',
                export_products_hs_codes: 'export/product-hs-codes',
            }

            let exportUrl = this.exportUrlParam ?? endpoints[e.detail.template_id] ?? '/export/products';

            let data = {
                'type': e.detail.format || 'xlsx',
                "template_id": e.detail.template_id || "",
                'filtering': 1,
                'types': this.selected_types,
                'status': this.selected_status,
                'brands': this.selected_brands,
                'branches': this.selected_branches,
                'categories': this.selected_categories,
                'mahly': this.selected_mahly,
                'wholesale': this.selected_wholesale,
            };

            showLoading();
            $.ajax({
                url: exportUrl,
                dataType: 'json',
                data: data,
                async: false,
                success: (resp) => {
                    laravel.ajax.successHandler(resp);
                },
                error: (error) => {
                    laravel.ajax.errorHandler(error);
                },
                complete: function () {
                    hideLoading();
                }
            });

        },

        // Select "missing or hidden mahly items" from selection menu, updated selected mahly val and submit filters
        selectProductMahally(prop) {
            let mahallyFilter = prop.detail ? prop.detail.prop : prop
            if (mahallyFilter === 'unlinked') {
                this.filters.mahly.options[1].selected = true
                this.selected_mahly = "unlinked"
            } else if (mahallyFilter === 'hidden') {
                this.filters.mahly.options[3].selected = true
                this.selected_mahly = "hidden"
            }
        },
        exports(e) {
          if (this.isUsingTemplates) {
            return this.showGroupExportModal(e)
          }
          return this.exportFilterResults(e)
        },
        ...mapMutations({
            setSelectedCustomFilters: 'filters/setSelectedFilters',
            resetSelectAll: 'selectAll/resetSelectAll',
        }),
        ...mapActions('filters', {
            resetFiltersAction: 'resetFiltersAction'
        })
    }
}
</script>
