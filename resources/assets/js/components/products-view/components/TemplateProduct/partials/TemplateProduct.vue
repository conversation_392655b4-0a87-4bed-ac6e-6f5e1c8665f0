<template>
  <div :class="{ 'active': active }" class="card card--vertical overflow-hidden relative">
    <div class="card__header  wide">
      <img :src=image class="d-block wide" :alt="name" />
    </div>
    <div class="card__content wide">
      <h6 class="m-0 lh-1 mb-5">{{name}}</h6>
      <small class="d-block text-muted sub-title">{{price}}</small>
    </div>
  </div>
</template>

<script>
export default {
  name: "ReadyProduct",
  props:['name','price','image','active'],
}
</script>

<style lang="scss" scoped>
$color-secondary: #76E8CD;
$color-primary-l: #004D5A;
$color-gray-200: #eeeeee;
$color-gray-300: #dddddd;
$color-gray-25:#f8f8f8;
$color-danger: #f55157;
$b-radius: 8px;
$b-radius-sm: 4px;

.card {
  width: 200px;
  border: 1px solid $color-gray-200;
  border-radius: $b-radius;
  cursor: pointer;

  &__header {
    height: 200px;
    background: $color-gray-25;
   
   &::before {
    content: 'منتج غير حقيقي';
    position: absolute;
    line-height: 1.7;
    color: white;
    background: $color-danger;
    cursor: pointer;
    opacity: 0.7;
    font-size: 10px;
    z-index: 10;
    top: 10px;
    border-radius: 40px;
    padding: 2px 8px 4px;
    left: 10px;
    }

    img {
      max-width: unset;
      height: 100%;
      object-fit: cover;
    }
  }
 

  &__content {
    padding: 10px;
  }

  &.active {
    border: 1px solid rgba($color-primary-l,0.35);

    h6 {
      color: $color-primary-l !important;
    }

    &:before {
      border-color: $color-primary-l;
    }

    &:after {
      opacity: 1;
    }
  }

  &:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    border-radius: 50px;
    border: 1px solid $color-gray-300;
    margin-left: 2px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    background: white;
  }

  &:after {
    content: '\ea9d';
    font-family: 'sallaicons', serif;
    font-size: 15px;
    color: $color-primary-l;
    position: absolute;
    top: 11px;
    right: 15px;
    z-index: 3;
    opacity: 0;
  }

  @media screen and (max-width: 400px) {
    width: 180px;
  }
}
</style>