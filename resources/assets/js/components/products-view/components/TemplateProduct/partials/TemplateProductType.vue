<template>
    <div :id="'#'+name" :class="{ 'active': active}"
         class="card card--horizontal">
    <span class="card__header">
      <i :class="[icon, 'text-dark-100', 'font-25', 'mr-10']"></i>
    </span>
        <div class="card__content overflow-hidden">
            <h6 class="lh-1 m-0 mb-5">
                {{title}}
                <span v-if="!(supportedPlan) && !(this.name === 'product')" class="badge badge--gold d-inline-block ml-5">ترقية الباقة</span>
            </h6>
            <small class="d-block text-muted text-muted-smaller sub-title">{{desc}}</small>
        </div>
    </div>
</template>
<script>
    import InitialDataService from "../../../../../services/ProductService.js";
    export default {
        name: "ProductPackage",
        props: ['name','icon', 'title', 'desc', 'active', '_packages'],
        data() {
            return {
                currentPlan: InitialDataService.getStorePlane(),
            }
        },
        computed: {
            supportedPlan() {
                return this._packages.includes(this.currentPlan);
            }
        }
    }
</script>

<style lang="scss" scoped>
$color-secondary: #76E8CD;
$color-gray-200: #eeeeee;
$color-gray-300: #dddddd;
$color-gray-25:#f8f8f8;
$color-primary-l: #004D5A;
$color-danger: #f55157;
$b-radius: 8px;
$b-radius-sm: 4px;
.card {
  align-items: flex-start;
  min-width: 250px;
  min-height: 60px;
  padding: 10px;
  border: 1px solid $color-gray-200;
  border-radius: $b-radius;
  cursor: pointer;

  h6 {
    position: relative;
    white-space: nowrap;

    &:before {
      content: '';
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
      border-radius: 50px;
      border: 1px solid $color-gray-300;
      margin-left: 2px;
    }

    &:after {
      content: '\ea9d';
      font-family: 'sallaicons', serif;
      font-size: 13px;
      color: $color-primary-l;
      position: absolute;
      top: 4px;
      right: 4px;
      opacity: 0;
    }
  }

  .sub-title {
    max-width: 80%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &.active {
    border: 1px solid rgba($color-primary-l,0.35);

    h6 {
      color: $color-primary-l !important;

      &:before {
        border-color: $color-primary-l;
      }

      &:after {
        opacity: 1;
      }
    }

    i[class^="sicon-"] {
      color: $color-primary-l !important;
    }
  }

  @media screen and (max-width: 380px) {
    width: 190px;
  }
}
</style>