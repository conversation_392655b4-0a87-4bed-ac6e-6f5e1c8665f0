<template>
    <div :class="{'inline': inline}" class="rec-title-block rec-title-block--smaller mb-15">
        <div class="rec-title-block__text os">
            <h2 :class="{'color-tiffany':active}">{{title}}</h2>
            <small v-show="desc" class="text-muted text-muted-smaller">{{desc}}</small>
        </div>
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: "blockTitle",
        props: ['title', 'desc', 'inline','active'],
    }
</script>
<style scoped>
    .color-tiffany{
        color :#5DD5C4;
    }
</style>