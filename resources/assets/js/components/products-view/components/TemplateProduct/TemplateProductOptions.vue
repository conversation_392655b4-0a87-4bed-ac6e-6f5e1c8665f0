<template>
  <div >
    <modal id="create_template_product"
           :close-btn="'إغلاق'"
           :hasLoader="true"
           :modalTitle="'استخدام نموذج جاهز'"
           :save-btn="'اضافة المنتج'"
           @saveModal="addTemplateProduct">

      <!-- Packages -->

      <div class="mb-10">
        <h5 class="p-0 m-0 mb-5 lh-1">اختر نوع المنتج</h5>
        <p class="text-muted text-muted-small p-0">جميع النماذج  تجريبية ولا يتم نشرها على المتجر</p>
      </div>

      <div class="grid-block grid-block--col-3 grid-block--gap-10 scroll scroll--x auto-flow pb-10">
        <div v-for="item in gettingCharity(templateProductTypes)" :key="item.name" @click="selectPack(item)">
          <template-product-type
              :_packages="item.plans"
              :active=" item === selectedTemplateProductType"
              :desc="item.description"
              :icon="item.icon"
              :name="item.name"
              :title="item.title"/>
        </div>
      </div>
      <div v-if="selectedTemplateProductType" class="grid-block grid-block--col-4 templates-list grid-block--gap-10 scroll auto-flow scroll--x mt-20 pb-10">
        <div v-for="(product,index) in products"
             :key="index"
             @click="selectProd(product)">
          <template-product
              :active="product === selectedTemplateProduct"
              :image="product.image"
              :name="product.name"
              :price="product.price"
          />
        </div>
      </div>
      <!-- if the no product selected -->
      <empty-placeholder
          v-else
          title="حدد نوع المنتج"
          description="اختر نوع المنتج من الأعلى ليتم إظهار نماذج للمنتجات"
      />
    </modal>
  </div>
</template>
<script>
import axios from "axios";
import Modal from '../layout/Modal';
import TemplateProduct from "./partials/TemplateProduct";
import TemplateProductType from './partials/TemplateProductType';
import EmptyPlaceholder from './partials/EmptyPlaceholder.vue';
import InitialDataService from "../../../../services/ProductService.js";
import Vue from 'vue';

export default {
  name: "TemplateProductOptions",
  components: {
    'template-product': TemplateProduct,
    'template-product-type': TemplateProductType,
    'modal': Modal,
    "empty-placeholder":EmptyPlaceholder
  },
  data() {
    return {
      baseApiUrl: window.apiUrl,
      headers :{
        token: JSON.parse(window.localStorage.getItem('token')).key,
        store_id: JSON.parse(window.localStorage.getItem('token')).id,
      },
      selectedTemplateProductType: null,
      selectedTemplateProduct: null,
      isSpecial: false,
      templateProductTypes: [],
      templateProductsList: [],
      currentPlan: InitialDataService.getStorePlane(),
    }
  },

  created() {
    this.getProductTemplates();
  },
  computed: {
    products() {
      return this.templateProductsList.filter(x => x.type === this.selectedTemplateProductType.name);
    },
  },
  methods: {
    getProductTemplates() {
      this.requestData(`${this.baseApiUrl}/products/types`)
          .then(res => {
            this.templateProductTypes = res.data.data
          })
          .catch((error) => {
            if(error.response && error.response.status === 401){
              getFreshToken(true);
            }
            return laravel.errors.renderValidationErrorBag(error.response?.data.error.fields);
          });
      this.requestData(`${this.baseApiUrl}/products/templates`)
          .then(res => {
            this.templateProductsList = res.data.data;
          })
          .catch((error) => {
            if(error.response && error.response.status === 401){
              getFreshToken(true);
            }
            return laravel.errors.renderValidationErrorBag(error.response?.data?.error?.fields);
          })
    },
    async requestData(url) {
      const config = {
        'Content-Type': 'application/json',
        'X-API-KEY': window.token.key,
        's-store-id': store.id,
        ...(window.headers || {}),
      };
      axios.defaults.headers.common = {...axios.defaults.headers.common, ...config};
      return await axios.get(url)
    },
    addTemplateProduct() {
      if (this.validate()) {
        if (!(this.selectedTemplateProductType.plans.includes(this.currentPlan)) && this.selectedTemplateProductType.name !== 'product') {
          swal({
            title: '',
            text: `
            نوع المنتج (${this.selectedTemplateProductType.title})
            متوفر فقط في باقات سلة بلس و برو
            `,
            type: 'warning',
            showCancelButton: true,
            cancelButtonText: 'لاحقا',
            showConfirmButton: true,
            confirmButtonText: "ترقية الباقة"
          }).then((result) => {
            if (result) {
              $(location).prop('href', "marketplace");
            }
          });
          return;
        }
        // Add template to product view
        this.$emit('created', this.selectedTemplateProduct);
        $('#create_template_product').modal('hide');
      }
      else if (!this.selectedTemplateProductType && !this.selectedTemplateProduct){
        swal({
          title: "تنبيه",
          text:`  عليك اختيار نوع واحد من القوالب الجاهزه`,
          type: "error",
          confirmButtonText: 'موافق'
        })

      }else{
        swal({
          title: "تنبيه",
          text:`يجب اختيار قالب من نوع ${this.selectedTemplateProductType.title}`,
          type: "error",
          confirmButtonText: 'موافق'
        })
      }
    },
    gettingCharity(templateProductTypes){
      Vue.prototype.$entity = window.dataLayer[0].store.entity  //to work with window 
      if(this.$entity !== 'charity'){
        return templateProductTypes.filter((item)=>item.name !=="donating")
      }
      return templateProductTypes
    },
    validate() {
      return !(this.selectedTemplateProductType === null || this.selectedTemplateProduct === null);
    },

    selectPack(_selectedTemplateProductType) {
      this.selectedTemplateProductType = _selectedTemplateProductType;
    },
    selectProd(_selectedTemplateProduct) {
      this.selectedTemplateProduct = _selectedTemplateProduct;
    },
  },
}
</script>

<style>
.auto-flow {
  grid-auto-flow:column;
}

.templates-list {
  grid-template-columns: repeat(4, 1fr);
}
</style>