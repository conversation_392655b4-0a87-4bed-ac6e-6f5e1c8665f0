<template>
    <modal id="propose-mahly-category" modalTitle="إذا لم تجد تصنيف مناسب في محلي لربطه بمنتجك، يُرجى مشاركتنا اسم التصنيف لإضافته." saveBtn="إرسال"
        @saveModal="saveModal"
        :closeBtn="'إلغاء'"
        :hasLoader="false"
    >
        <form ref="ProposeMahlyCat" @submit="saveModal">
            <label>
                يرجى مساعدتنا بكتابة التصنيف المناسب لك
            </label>
            <div class="input-group" :class="{'has-error': errorMsg.length}">
                <span class="input-group-addon input-group-addon-small"><i class="sicon-type-square"></i></span>
                <input class="form-control" type="text" v-model="proposedName" :placeholder="placeholder" :maxlength="maxlength">
            </div>
            <span v-if="errorMsg.length" class="font-12 text-danger error-message pl-10">
				{{errorMsg}}
			</span>
        </form>
    </modal>
</template>
  
<script>
import Modal from './layout/Modal';
export default {
    name: "proposeMahlyCategory",
    props: ['product'],
    data() {
        return {
            placeholder: 'اكتب اسم التصنيف المقترح...',
            proposedName: "",
            maxlength: 50,
            errorMsg: ""
        }
    },
    components: {
            'modal': Modal,
     },
     mounted () {
        $('#propose-mahly-category').on('hidden.bs.modal', () => {
            this.proposedName = "";
            this.errorMsg = "";
        });
    },
    methods: {
        async saveModal() {
            const data = {
                proposed_name: this.proposedName,
                product_id: this.product.id,
                product_name: this.product.name,
            }
            await axios.post('/mahly/categories/proposals', data)
                .then(() => {
                    swal({
                        title: '',
                        text: "تم إرسال اقتراحك وسيتم مراجعته من فريق محلي.",
                        type: 'success',
                        showConfirmButton: false,
                        timer: 4500
                    });
                    this.$refs.ProposeMahlyCat.reset();
                    this.proposedName = "";
                    this.errorMsg = "";
                    this.$emit('submitProposedName')
                    $(`#propose-mahly-category`).modal('hide');
                })
                .catch(({response}) => {
                    if (response.data.error.fields?.proposed_name) {
                        this.errorMsg = response.data.error.fields.proposed_name[0].replace("proposed name", "اسم التصنيف")
                    }
                    else if(response.data.error.message){
                        swal({
                            title: '',
                            text: response.data.error.message,
                            type: 'warning',
                        });
                    }
                    else{
                        swal({
                            title: '',
                            text: "حدث خطأ ما",
                            type: 'error',
                        });
                    }
                });
        }
    }
}
</script>