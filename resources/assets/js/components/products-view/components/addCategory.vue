<template>
    <div>
        <modal id="new_category" :modalTitle="'اضافة تصنيف جديد'" :saveBtn="'اضافة التصنيف'" :hasLoader="true" :closeBtn="'إلغاء'" @saveModal="saveModal">
            <lingual-field :outer-attrs="{class:{'form-group': true , 'has-error': errors.name }}"
                           placeholder="ادخل اسم التصنيف"
                           :translations="translations"
                           @value-updated="nameUpdated"
                           @value-changed="nameUpdated"
                           icon="sicon-format-text"
                           :languages="languages"
                           name="name"
            >
                <template v-slot:before-input>
                    <label>اسم التصنيف</label>
                </template>
                <!-- server side validation -->
                <form-error v-show="errors.name" :errors="errors">
                    {{ errors.name ? errors.name.toString() : ''}}
                </form-error>
            </lingual-field>

            <div class="rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg">
                <input type="checkbox" v-model="addToMain" name="add_to_main" id="add_to_main">
                <label for="add_to_main">اضافة الى تصنيف رئيسي</label>
            </div>

            <div :class="{'form-group': true, 'mb-0 mt-10' : true,'categories': true, 'has-error':errors.categories}" v-show="addToMain">
                <v-treeselect
                        ref="categoriesList"
                        value-format="object"
                        v-model="parent"
                        :multiple="false"
                        :flat="false"
                        :searchable="false"
                        :show-count="false"
                        :default-expand-level="1"
                        :clearable="false"
                        :close-on-select="true"
                        :loading-text="'جاري جلب البيانات...'"
                        :no-children-text="'...'"
                        :is-default-expanded="true"
                        :clear-on-select="false"
                        class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full"
                        placeholder="تحديد التصنيف الرئيسي"
                        :options="categories"
                        :normalizer="normalizer"
                >
                    <label slot="option-label"
                           slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                           :class="labelClassName">
                        {{ node.label }}
                    </label>
                </v-treeselect>
                <!-- server side validation -->
                <form-error v-show="errors.parent_id" :errors="errors">
                    {{ errors.parent_id ? errors.parent_id.toString() : ''}}
                </form-error>
            </div>
        </modal>
    </div>
</template>

<script>
    import Treeselect from '@riophae/vue-treeselect';
    import Modal from './layout/Modal';
    import Http from "../../../utils/http";
    import FormError from "./FormError";
    import LingualField from "@salla.sa/languages/components/LingualField"

    export default {
        name: "AddCategory",
        props: ['categories', 'languages'],
        data() {
            return {
                errors: [],
                addToMain: false,
                name : null,
                parent:null,
                translations:{},
                normalizer(node) {
                    return {
                        label: node.name,
                        value: node.id,
                        children: node.sub_categories,
                    }
                },
            };
        },
        components: {
            'v-treeselect': Treeselect,
            'modal': Modal,
            'form-error': FormError,
            'lingual-field':LingualField,
        },

        methods: {
            // Method fires when the save modal button pressed
            saveModal(event) {
              showLoading(event)
                Http.post("/category/add", Object.assign({
                        _token: _token,
                        parent_id: this.parent ? this.parent.id : null,
                        name : this.name,
                    },this.translations),
                    ({data}) => {
                        hideLoading()
                        laravel.ajax.successHandler(data);
                        // emit event with the categories after add/update categories data,
                        // to update the list of categories inside products page
                        Salla.event.createAndDispatch('products::reload-categories-list', {
                            categories: data.data.categories,
                        });
                        $("#new_category").modal('hide');
                    },
                    ({response}) => {
                        hideLoading()
                        this.errors = response.data.error.fields;
                    }
                );
            },
            nameUpdated(event) {
                this.translations = event.translations;
                this.name = event.storeLangValue;
            },
        },

        mounted () {
            $('#new_category').on('hidden.bs.modal', () => {
                this.name = null;
                this.translations = {};
                this.addToMain = false;
                this.parent = null
            });
        }
    }
</script>