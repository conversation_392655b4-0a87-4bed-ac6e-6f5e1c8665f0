<template>
    <div>
        <li v-for="(option, index) in filter.options" :key="index">
            <div class="rec-checkbox rec-checkbox--default light" >
                <input :id="`${filter.base_name}_${index}`" type="checkbox" class="filter-options"
                      v-model="option.selected" @change="checkForAll($event, index)">
                <label :for="`${filter.base_name}_${index}`">
                    <span>{{ option.label }}</span>
                </label>
            </div>
        </li>
    </div>
</template>

<script>
    export default {
        props: ['filter', 'checkForAll', 'index'],

        mounted() {
            Salla.event.addEventListener('products::reset-filters', this.resetFilters);
        },
        beforeDestroy() {
            document.removeEventListener('products::reset-filters', this.resetFilters);
        },
        methods:{
            resetFilters(){
                this.filter.options.forEach((option) => {
                    option.selected = false
                });
            }
        }
    }
</script>