<template>
    <div class="form-group">
        <v-treeselect
            v-model="selected"
            value-format="object"
            :multiple="multiple"
            class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full"
            :options="filter.options"
            :normalizer="normalizer"
            :placeholder="placeholder"
            @input="updateSelectedList(index, $event)"
        />
    </div>
</template>

<script>
    import Treeselect from '@riophae/vue-treeselect'
    import '@riophae/vue-treeselect/dist/vue-treeselect.css'

    export default {
        props: {
            filter: {type: Object, default: null},
            index: null,
            updateSelectedList: {type: Function, default: () => null},
            placeholder: {type: String, default: null},
            multiple: {type: Boolean, default: true}
        },
        components: {
            'v-treeselect': Treeselect
        },
        data() {
            const filteredOptions = _.filter(this.filter.options, o => o.selected === true)
            return {
                // fetch default selected values
                selected : this.multiple ? filteredOptions : filteredOptions[0],
                test: null,
                normalizer(node) {
					return {
						id: node.value,
					}
				},
            }
        },
        mounted() {
            Salla.event.addEventListener('products::reset-filters', this.resetFilters);
            Salla.event.addEventListener('inventory::reset-filters', this.resetFilters);
            Salla.event.addEventListener('products::set-mahally-filter', this.setMahallyFilter);
        },
        beforeDestroy() {
            document.removeEventListener('products::reset-filters', this.resetFilters);
            document.removeEventListener('inventory::reset-filters', this.resetFilters);
            document.removeEventListener('inventory::set-mahally-filter', this.setMahallyFilter);
        },
        methods:{
            resetFilters(){
                this.selected = this.multiple ? [] : null;
            },
            setMahallyFilter(e){
                if(this.filter.name === 'mahly'){
                    this.selected = null
                    if(e.detail.prop === 'hidden'){
                        this.selected = this.filter.options[3]
                    }else if(e.detail.prop === 'unlinked'){
                        this.selected = this.filter.options[1]
                    }
                }
            }
        }

    }
</script>