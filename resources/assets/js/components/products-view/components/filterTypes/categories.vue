<template>
    <div class="form-group">
        <v-treeselect
            v-model="selected_categories"
            value-format="object"
            :multiple="true"
            :flat="true"
            :searchable="true"
            :show-count="false"
            :default-expand-level="2"
            :clearable="false"
            :close-on-select="false"
            :loading-text="'جاري جلب البيانات...'"
            :no-children-text="'...'"
            :is-default-expanded="true"
            :clear-on-select="false"
            class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full"
            placeholder="التصنيف"
            :options="filter.options"
            :normalizer="normalizer"
            @input="updateSelectedList(index, $event)"
        />
    </div>
</template>

<script>
    import Treeselect from '@riophae/vue-treeselect'
	import '@riophae/vue-treeselect/dist/vue-treeselect.css'

    export default {
        props: ['filter', 'index','updateSelectedList', 'set_selected_categories'],
        components: {
            'v-treeselect': Treeselect,
        },
        data() {
            return {
                selected_categories: [],
                normalizer(node) {
					return {
						label: node.label,
						id: node.value,
						children: node.sub_categories,
					}
				},
            }
        },
        mounted() {
            Salla.event.addEventListener('products::reset-filters', this.resetFilters);
            Salla.event.addEventListener('inventory::reset-filters', this.resetFilters);

            if (this.set_selected_categories){
              this.setFiltersSelectedCategories();
            }
        },
        beforeDestroy() {
            document.removeEventListener('products::reset-filters', this.resetFilters);
            document.removeEventListener('inventory::reset-filters', this.resetFilters);
        },
        created() {
            //this.selected_categories = this.getDefaultSelectedCategories();
        },
        methods: {


          /**
           * Set this.selected_categories
           *
           */
            setFiltersSelectedCategories() {
              this.selected_categories = this.getDefaultSelectedCategories();
            },

            /**
             * check selected categories
             * and subcategories
             *
             * @returns {[]}
             */
            getDefaultSelectedCategories()
            {
                let list = [];
                this.filter.options.forEach((option) => {
                    if(option.selected === true){
                        list.push(option)
                    }
                    if(typeof option.sub_categories != 'undefined') {
                        option.sub_categories.forEach((sub_option) => {
                            if (sub_option.selected === true) {
                                list.push(sub_option)
                            }
                        })
                    }
                })
                return list;
            },
            resetFilters(){
                this.selected_categories = [];
            }
        }

    }
</script>
