<template>
  <modal id="export-group-product-template-modal" modal-title="إختر صيغة الملف" save-btn="تصدير" close-btn="إلغاء"
    @saveModal="onSaveModal">
    <div class="row d-flex justify-content-center">
      <div class="col-xs-12">
        <div class="form-group">
          <label for="template_id"> قالب التصدير</label>
          <div class="input-group">
            <span class="input-group-addon input-group-addon-small"><i class="sicon-cloud-download"></i></span>
            <select v-show="templates.length > 0" id="template_id" v-model="template_id" name="template_id" class="v-dropdown-container selectpicker sort-select">
              <option value="">اختر قالب التصدير</option>
              <option v-for="template in templates" :key="template.id" :value="template.id">
                {{ template.name }}
              </option>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label for="format">إختر صيغة الملف </label>
          <div class="input-group">
            <span class="input-group-addon input-group-addon-small"><i class="sicon-page"></i></span>
            <select id="format" v-model="format" name="format" class="v-dropdown-container selectpicker sort-select">
              <option value="xlsx">XLSX</option>
              <option value="csv">CSV</option>
            </select>
          </div>
        </div>
      </div>

    </div>
  </modal>
</template>

<script>
import Modal from './layout/Modal'

export default {
  components: {
    modal: Modal,
  },
  props: ['id', 'templates'],
  data() {
    return {
      format: 'xlsx',
      template_id: null,
    }
  },
  mounted() {
    $('#template_id').selectpicker('refresh')
  },
  updated() {
    $('#template_id').selectpicker('refresh')
  },
  methods: {
    onSaveModal() {
      if (!this.format) {
        swal({
          title: 'خطأ',
          text: 'يجب اختيار صيغة الملف',
          icon: 'error',
          type: 'error',
        })
        return
      }
      if (!this.template_id) {
        swal({
          title: 'خطأ',
          text: 'يجب اختيار قالب التصدير',
          icon: 'error',
          type: 'error',
        })
        return
      }
      showLoading()

      Salla.event.createAndDispatch('products::export-filter-results', {
        format: this.format,
        template_id: this.template_id,
      })


      $('#export-group-product-template-modal').modal('hide')
    },
  },
}
</script>
<style lang="scss" scoped>
#export-group-product-template-modal button i {
  color: #76E8CD;
  font-size: 32px;
}
</style>