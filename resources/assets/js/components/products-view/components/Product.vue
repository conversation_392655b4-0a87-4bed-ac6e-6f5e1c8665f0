<template>
  <div :class="productClass" :id="`product_conent_${product.id}`">
    <form class="product-form">
      <div
        :class="[
          'thumbnail',
          product.active,
          'save-product',
          { 'hidden-product': product.status === 'hidden' ? true : false },
        ]"
      >
        <!------------------Delete Product------------------------>
        <button
          type="button"
          v-if="product.id < 0"
          class="btn btn-remove btn--circular btn-delete-product"
          @click="$emit('deleteProduct', product.id)"
        >
          <i class="sicon-cancel"></i>
        </button>
        <!-- product hidden badge -->
        <div
          v-if="
            storeHaveMobileApp && (!product.show_in_web || !product.show_in_app)
          "
          class="product-hidden-badge-wrapper"
        >
          <div
            class="rec-btn rec-btn--circular flex product-hidden-badge tooltip-toggle bottom"
          >
            <i class="sicon-eye-off"></i>
            <div class="tooltip-content">
              <!-- hidden-platform class will be added conditionally -->
              <ul class="rec-list rec-list--vertical">
                <li :class="{ 'hidden-platform': !product.show_in_web }">
                  <i class="sicon-laptop"></i>
                  الموقع
                </li>
                <li
                  v-show="storeHaveMobileApp"
                  :class="{ 'hidden-platform': !product.show_in_app }"
                >
                  <i class="sicon-iphone-x"></i>
                  التطبيق
                </li>
              </ul>
            </div>
          </div>
        </div>
        <!-- end product hidden badge -->
        <div :class="{ caption: true, 'caption-small': true }">
          <div class="product-thumb-wrapper">
            <div class="thumb">
              <!---------- Product Image ----------------->
              <div class="thumb-mask" @click="openImages()">
                <img
                  :src="productImage"
                  alt="img"
                  :id="`product_image_${product.id}`"
                />
              </div>
              <!------------------------------------------>
              <div
                v-if="permissions.edit_product || permissions.delete_product"
                v-show="product.id > 0"
                class="product-check rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg"
              >
                <input
                  type="checkbox"
                  class="product-check"
                  v-model="product.checked"
                  @change="updateSelectedProducts(product)"
                  :name="`product_${product.id}`"
                  :id="`product_${product.id}`"
                  autocomplete="off"
                />
                <label :for="`product_${product.id}`">
                  <!--------------Product Type for small screens--------------->
                  <span class="product-type">{{
                    productTypes[product.type]['label']
                  }}</span>
                </label>
              </div>
              <!---------- Add Image Or Video ----------------->
              <button
                v-if="permissions.product_add_image"
                class="product_image_btn"
                @click="openImages()"
                type="button"
                data-inline-loader
              >
                <i class="sicon-image"></i>
                <span>إضافة صورة او فيديو</span>
              </button>
              <!---------- Pin Product ------------------------>
              <button
                v-if="permissions.pinned_product"
                v-show="product.id > 0"
                class="pin_btn"
                :class="{ pinned: product.pinned != 0 }"
                @click="pinProduct"
                type="button"
              >
                <i class="sicon-thumbtack"></i>
              </button>
            </div>
            <div class="product-name-type prfw">
              <!----------------- Product Name -------------->
              <LingualField
                :events-load="product"
                :id="product.id"
                :translations="product.translations"
                :languages="languages"
                :placeholder="`${
                  productTypes[product.type]['label']
                } - أدخل اسم المنتج  `"
                :icon="`${productTypes[product.type]['icon']}`"
                :name="'name'"
                :isNew="!!product.isNewTranslation"
                required="required"
                :outerAttrs="{
                  class: {
                    'form-group': true,
                    'tooltip-toggle': true,
                    trans: true,
                    top: true,
                    'has-error': product.hasNameError || errors.name,
                  },
                }"
                @value-updated="NameUpdated"
              >
                <template v-slot:before-icon>
                  <span class="tooltip-content">
                    <i :class="[`${productTypes[product.type]['icon']}`]"></i>
                    {{ productTypes[product.type]['label'] }}
                  </span>
                </template>
                <!-- client and server side validation -->
                <form-error
                  v-show="product.hasNameError || errors.name"
                  :errors="errors"
                >
                  {{ nameErrorText }}
                </form-error>
                <!-- client and server side validation -->
                <!-- <form-error v-show="errors.product_type" :errors="errors">
                    {{ errors.product_type ? errors.product_type.toString() : ''}}
                </form-error> -->
              </LingualField>
              <!--------------------------------------------->

              <!----------------- Product Price -------------->
              <div
                class="form-group product-price-small-screen mb-0"
                :class="{ 'has-error': product.hasPriceError || errors.price }"
              >
                <div class="input-group">
                  <span class="input-group-addon input-group-addon-small">
                    <div
                      v-if="product.type == 'financial_support'"
                      class="tooltip-toggle top right"
                    >
                      <i class="sicon-dollar-coin-stack"></i>
                      <div
                        class="tooltip-content d-flex align-items-center width-300"
                      >
                        <span class="sicon-dollar-coin-stack mr-5"></span>
                        <p class="font-12 text-dark-200">
                          فضلاً أدخل قيمة الكفالة و مدتها من بيانات المنتج
                        </p>
                      </div>
                    </div>
                    <i v-else class="sicon-dollar-coin-stack"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control _parseArabicNumbers"
                    :class="{ ltr: direction === 'ltr' }"
                    :id="`small_vr_price_${index}`"
                    placeholder="السعر"
                    :disabled="
                      !permissions.products_price_edit ||
                      product.type === 'financial_support'
                    "
                    v-model.lazy="product.price"
                  />
                  <span class="input-group-addon">{{ storeCurrency }}</span>
                </div>

                <!-- client and server side validation -->
                <form-error v-if="product.hasPriceError || errors.price">
                  {{
                    errors.price
                      ? errors.price.toString()
                      : 'يجب على حقل السعر أن لا يكون فارغا'
                  }}
                </form-error>
              </div>
              <!-------------------------------------------------->
            </div>
          </div>
          <div class="product-fields-wrapper prfw">
            <div
              :class="{
                'form-group': true,
                'has-error': product.hasQuantityError || errors.quantity,
                'quantity-low': showRedAlert(),
              }"
            >
              <div
                class="input-group product-quantities product-quantities--products-group"
              >
                <!----------------- Product Quantity -------------->
                <div>
                  <span class="input-group-addon input-group-addon-small">
                    <i
                      :class="[
                        `${
                          product.type === 'booking'
                            ? 'sicon-chair-theater'
                            : 'sicon-box-bankers'
                        }`,
                      ]"
                    ></i>
                  </span>

                  <input
                    type="text"
                    :id="`product_quantity_${index}`"
                    :class="{
                      'form-control': true,
                      product_quantity: true,
                      disabled: disableQuantity(),
                      _parseArabicNumbers: true,
                    }"
                    :placeholder="
                      product.type === 'booking'
                        ? 'العدد المتاح للحجز في الموعد الواحد'
                        : product.unlimited_quantity
                        ? 'الكمية المتوفرة'
                        : 'كمية غير محدودة'
                    "
                    @click="quantityClicked"
                    :readonly="!product.can_change_quantity"
                    :value="
                      disableQuantity() ? 'كمية غير محدودة' : product.quantity
                    "
                    @change="
                      !product.unlimited_quantity
                        ? (product.quantity = $event.target.value)
                        : null
                    "
                    @keyup.enter="saveProduct(product)"
                  />
                </div>
                <!-------------------------------------------------->

                <!----------------- Notify Quantity ---------------->
                <button
                  v-if="features.notify_quantity && product.type !== 'booking'"
                  :class="{
                    'rec-btn': true,
                    'btn-tiffany': true,
                    'btn-notify': true,
                    'value-filled': product.notify_quantity,
                  }"
                  type="button"
                  @click="openNotifyQuantitySettings(product.id)"
                >
                  <i class="sicon-bell-time"></i>
                </button>

                <!----------Infinite Quantity-------------->
                <button
                  type="button"
                  v-if="product.type !== 'booking'"
                  @click="unlimitedQuantityClicked"
                  :class="{
                    btn: true,
                    'btn-tiffany': true,
                    wide: !features.notify_quantity && !showQuantityModal(),
                    'btn-infinite': true,
                    active: product.unlimited_quantity,
                  }"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 30 30"
                    width="30px"
                    height="30px"
                  >
                    <path
                      d="M 8 8 C 3.6102416 8 0 11.595515 0 16 C 0 20.400585 3.599415 24 8 24 C 10.646 24 12.420344 22.745203 13.777344 21.033203 C 13.147344 20.063203 12.616672 19.057234 12.138672 18.115234 C 10.996672 19.940234 9.828 21 8 21 C 5.220585 21 3 18.779415 3 16 C 3 13.224485 5.2377584 11 8 11 C 9.4265669 11 10.267624 11.520682 11.15625 12.525391 C 12.044876 13.530099 12.834942 15.048526 13.652344 16.673828 C 14.469745 18.29913 15.315394 20.031983 16.585938 21.464844 C 17.85648 22.897705 19.696851 24 22 24 C 26.362802 24 30 20.414234 30 16 C 30 11.599415 26.400585 8 22 8 C 19.35 8 17.576703 9.2652813 16.220703 10.988281 C 16.849703 11.961281 17.379422 12.969109 17.857422 13.912109 C 19.003422 12.069109 20.172 11 22 11 C 24.779415 11 27 13.220585 27 16 C 27 18.765766 24.719198 21 22 21 C 20.566649 21 19.72091 20.477295 18.830078 19.472656 C 17.939247 18.468017 17.14913 16.95087 16.332031 15.326172 C 15.514933 13.701474 14.671546 11.969901 13.404297 10.537109 C 12.137048 9.1043186 10.298933 8 8 8 z"
                    />
                  </svg>
                </button>
                <!----------------------------------------->

                <!-- TODO:: Taghreed check this -->
                <div
                  @click="(e) => e.stopPropagation()"
                  :class="{
                    'rec-list': true,
                    'rec-list--horizental': true,
                    'left-arrow': !(
                      product.id > 0 && product.type === 'product'
                    ),
                  }"
                  v-show="showQuantityLimit"
                >
                  <span>نبهني عند وصول الكمية إلى</span>
                  <input
                    type="text"
                    :id="`low_quantity_${index}`"
                    class="form-control _parseArabicNumbers"
                    placeholder="ادخل اقل قيمة..."
                    v-model="product.notify_quantity"
                  />
                </div>
                <!---------------------------------------------------->

                <!----------------- Options and Quantity ------------->
                <button
                  type="button"
                  v-if="showQuantityModal()"
                  class="btn btn-tiffany btn-icon"
                  data-inline-loader
                  @click="openOptionsAndQuantity(product.id)"
                >
                  {{ getModalText() }}
                </button>
                <!---------------------------------------------------->
              </div>

              <!-- client and server side validation -->
              <form-error v-if="product.hasQuantityError || errors.quantity">
                {{
                  errors.quantity
                    ? errors.quantity.toString()
                    : 'يجب على حقل الكمية أن يكون رقمًا فقط'
                }}
              </form-error>
            </div>
            <!--------- Display categories --------------->
            <div
              :class="{
                'form-group': true,
                categories: true,
                'has-error': product.hasCategoriesError || errors.categories,
              }"
            >
              <div class="input-group">
                <v-treeselect
                  ref="categoriesList"
                  v-model="product.categories"
                  value-format="object"
                  :multiple="true"
                  :flat="true"
                  :searchable="false"
                  :show-count="false"
                  :default-expand-level="2"
                  :clearable="false"
                  :close-on-select="false"
                  :loading-text="'جاري جلب البيانات...'"
                  :no-children-text="'...'"
                  :is-default-expanded="true"
                  :clear-on-select="false"
                  class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--without-effect vue-treeselect--full"
                  placeholder="اختر تصنيف المنتج"
                  :options="categories"
                  :normalizer="normalizer"
                  :isoCode="languages.iso_code"
                >
                  <label
                    slot="option-label"
                    slot-scope="{
                      node,
                      shouldShowCount,
                      count,
                      labelClassName,
                      countClassName,
                    }"
                    :class="labelClassName"
                  >
                    {{ node.label }}
                  </label>
                </v-treeselect>
                <!--------------- Add Category -------------->
                <div v-if="permissions.categories" class="input-group-btn">
                  <button
                    class="btn btn-tiffany btn-icon btn-add-category"
                    type="button"
                    data-toggle="modal"
                    data-target="#new_category"
                  >
                    اضف تصنيف
                  </button>
                </div>
                <!------------------------------------------->
                <!-- validation -->
                <form-error
                  v-if="product.hasCategoriesError || errors.categories"
                  :errors="errors"
                >
                  {{ errors && errors.categories.toString() }}
                </form-error>
              </div>
            </div>
            <!------------- End Categories Section -------------->
            <!--------- Display Mahly categories section --------------->
            <div
              v-if="mahlyData.status"
              :class="{
                'form-group': true,
                categories: true,
                'has-error': false,
              }"
            >
              <!-- This placeholder is used for new products -->
              <div v-if="product.id < 0" class="mahally-tree-placeholder">
                ستظهر تصنيفات محلي بعد حفظ بيانات المنتج الأساسية
              </div>
              <div v-else class="input-group">
                <v-treeselect
                  ref="mahlyCategoriesList"
                  v-model="selectedMahlyCategoryId"
                  :class="treeselectClass"
                  :no-results-text="customNoResultsText"
                  :no-options-text="customNoResultsText"
                  :flat="true"
                  :multiple="true"
                  :searchable="true"
                  :show-count="false"
                  :clearable="true"
                  :close-on-select="true"
                  :always-open="false"
                  :loading-text="'جاري جلب البيانات...'"
                  :backspace-removes="false"
                  :no-children-text="'...'"
                  :is-default-expanded="false"
                  :clear-on-select="false"
                  :disable-branch-nodes="true"
                  :disable-fuzzy-matching="true"
                  class="vue-mahaly-custom-select vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--without-effect vue-treeselect--full"
                  placeholder="اختر تصنيف محلي"
                  :options="mahlyData.categories"
                  :normalizer="mahlyCatListNormalizer"
                  @select="handleMahlyCatSelect"
                  @input="handleMahlyCatInput"
                  @open="onOpenMahalySelect"
                  @search-change="onSearch"
                >
                  <div slot="before-list" class="input-search-container">
                    <input
                      ref="input"
                      class="form-control wide input-search"
                      placeholder="ابحث عن تصنيف محلي المناسب..."
                    />
                    <span v-if="showCursor" id="mock-cursor">|</span>
                  </div>

                  <template slot="value-label" slot-scope="{ node }">
                    <span class="value">{{ node.raw.name }}</span>
                    <span
                      title="Clear value"
                      class="clear"
                      @click="clearMahlyCatList"
                    >
                      <img
                        src="https://cdn.assets.salla.network/dash/vendor/mahlydash/images/icons/dismiss.svg"
                        alt="clear"
                      />
                    </span>
                  </template>
                  <label
                    v-if="node.id !== 0"
                    slot="option-label"
                    slot-scope="{ node, labelClassName }"
                    :class="labelClassName"
                  >
                    {{ node.label }}
                  </label>
                  <label
                    v-else
                    slot="option-label"
                    slot-scope="{ node, labelClassName }"
                    :class="labelClassName"
                    data-toggle="modal"
                    :data-target="'#propose-mahly-category-' + 234"
                  >
                    {{ node.label }}
                  </label>
                </v-treeselect>
                <!--------------- Add Category -------------->
                <div v-if="permissions.categories" class="input-group-btn">
                  <button
                    class="btn btn-tiffany btn-icon btn-add-category"
                    type="button"
                    @click="toggleMahallyCategoriesList()"
                  >
                    تصنيف محلي
                  </button>
                  <!-- <i class="sicon-mahally"></i> d-flex justify-content-between align-items-center -->
                </div>
                <!------------------------------------------->
                <!-- validation -->
                <form-error
                  v-if="product.hasCategoriesError || errors.categories"
                  :errors="errors"
                >
                  {{ errors && errors.categories.toString() }}
                </form-error>
              </div>
            </div>
            <!------------- End Mahly Categories Section -------------->
          </div>
        </div>
        <div :class="{ 'controls-wrapper': true }">
          <div class="options-wrapper">
            <!--------------- Product  Details -------------->
            <div class="options">
              <div class="form-group">
                <button
                  id="productOptionsBtn"
                  type="button"
                  class="btn border-slate text-slate-800 btn-flat btn-xs product-options-btn"
                  data-inline-loader
                  @click="productOptions(product, $event)"
                >
                  <i class="sicon-tune-alt"></i>
                  بيانات المنتج
                </button>
              </div>
            </div>
            <!----------------------------------------------->
            <!------------ More Options Section (Start) ----->
            <div
              v-if="product.id > 0 &&
                (permissions.hide_product || permissions.delete_product)
              "
              class="options"
            >
              <div class="btn-group">
                <button
                  type="button"
                  class="btn btn-xs btn-product-delete dropdown-toggle"
                  data-toggle="dropdown"
                >
                  المزيد &nbsp;
                  <span class="caret"></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-left mobile-top">
                  <!-- promote video option -->
                  <li
                    v-if="features.promote_product_video"
                    class="video-promote"
                  >
                    <a
                      target="_blank"
                      :href="`https://app.vidmass.com/smart-demo?product=${product.prod_id_encode_store}`"
                      ><i class="sicon-media-player"></i>إنشاء فيديو تسويقي</a
                    >
                  </li>
                  <!-- Copy Product Link---->
                  <li>
                    <a
                      class="copy-product-link"
                      :data-clipboard-text="`${baseStoreUrl}/${product.prod_id_encode_store}`"
                      ><i class="sicon-link"></i> نسخ رابط المنتج
                    </a>
                  </li>
                  <!-- Duplicate Product --->
                  <li>
                    <a class="clone-product" @click="cloneProduct"
                      ><i class="sicon-pages"></i> تكرار المنتج</a
                    >
                  </li>
                  <!-- Product Statistics--->
                  <li>
                    <a class="sales-product" :data-product-id="`${product.id}`"
                      ><i class="sicon-poll"></i>إحصائيات المنتج</a
                    >
                  </li>
                  <!-- Product Orders------>
                  <li v-if="features.export_product_orders">
                    <a
                      class="orders-product"
                      :href="`/orders?product=${product.prod_id_encode}&filtering=1&product_id=${product.prod_id_encode}`"
                      ><i class="sicon-box"> </i> استعراض طلبات المنتج</a
                    >
                  </li>
                  <li v-if="features.export_product_orders">
                    <a
                      class="orders-product"
                      :href="`/feedback?type=products&products=${product.id}&encoded=1&product_id=${product.prod_id_encode}`"
                      ><i class="sicon-star"> </i> استعراض تقييمات المنتج</a
                    >
                  </li>
                  <li class="dropdown-submenu dropdown-submenu-left" @click.stop="onChannelsClicked">
                    <a><i class="sicon-cloud-download"></i> قنوات عرض المنتج</a>
                    <ul
                      :class="['dropdown-menu', 'dropdown-menu-xs', 'dropdown-product-status', { 'show': isChannelOpen, 'hide': !isChannelOpen && isMobile }]">
                      <!-- Display Or Hide Product-->
                      <li v-if="permissions.hide_product">
                        <a v-if="!product.show_in_web" class="orders-product" @click="showInWeb">
                          <i :class="`sicon-vision`"></i>
                          {{ 'إظهار المنتج في المتجر' }}
                        </a>
                        <a v-if="product.show_in_web" @click="showInWeb" class="orders-product">
                          <i :class="`sicon-eye-off`"></i>
                          {{ 'إخفاء المنتج في المتجر' }}
                        </a>
                      </li>
                      <!-- Show Product In App-->
                      <li v-if="
                        (permissions.hide_product && storeHaveMobileApp) ||
                        plan == 'special'
                      ">
                        <a v-if="!product.show_in_app" class="orders-product" @click.stop="showInApp">
                          <i :class="`sicon-vision`"></i>
                          {{ 'إظهار المنتج في تطبيق المتجر' }}
                        </a>
                        <a v-if="product.show_in_app" class="orders-product" @click.stop="showInApp">
                          <i :class="`sicon-eye-off`"></i>
                          {{ 'إخفاء المنتج في تطبيق المتجر' }}
                        </a>
                      </li>
                      <!-- Show Product In Mahly-->
                      <!-- <li v-if="mahlyData.status">
                        <a class="orders-product" @click="showInMahly">
                          <i :class="`sicon-${product.show_in_mahly_app ? 'eye-off' : 'vision'}`"></i>
                          {{ product.show_in_mahly_app ? 'إخفاء' : 'إظهار' }} المنتج في تطبيق محلي
                        </a>
                      </li> -->
                      <!-- Show Product In Vendor-->
                      <li
                        v-if="features.vendor_app_feature && (product.type === 'product' || product.type === 'food' || product.type === 'codes')">
                        <a class="orders-product" @click.stop="showInVendor">
                          <i :class="`sicon-${product.show_in_vendor_app ? 'eye-off' : 'vision'}`"></i>
                          {{ product.show_in_vendor_app ? 'إخفاء' : 'إظهار' }} المنتج في سوق الجملة
                        </a>
                      </li>

                    </ul>
                  </li>
                  <!-- Export Product Orders -->
                  <li
                    :class="{ 'dropdown-submenu dropdown-submenu-left': features.export_product_orders, '': !features.export_product_orders }"
                    @click.stop="onExportClick">
                    <a @click.prevent="!features.export_product_orders ? alertForPackages() : null">
                      <i class="sicon-cloud-download"></i> تصدير طلبات المنتج
                    </a>
                    <ul v-if="features.export_product_orders"
                      :class="['dropdown-menu', 'dropdown-menu-xs', 'dropdown-product-status', { 'show': isExportOpen, 'hide': !isExportOpen && isMobile }]">
                      <li>
                        <a :href="`/export/product-orders?product_id=${product.prod_id_encode}&type=xlsx`"
                          class="dropdown-link ajax" data-nonconfirm="true" @click.stop>
                          <!-- Prevents event bubbling here -->
                          <i class="sicon-cloud-download position-right"></i> تصدير بصيغة xlsx
                        </a>
                      </li>
                      <li>
                        <a :href="`/export/product-orders?product_id=${product.prod_id_encode}&type=csv`"
                          class="dropdown-link ajax" data-nonconfirm="true" @click.stop>
                          <!-- Prevents event bubbling here -->
                          <i class="sicon-cloud-download position-right"></i> تصدير بصيغة csv
                        </a>
                      </li>
                    </ul>
                  </li>

                  <!-- Delete Product ------->
                  <li v-if="permissions.delete_product">
                    <a class="delete-product danger" @click="deleteProduct"
                      ><i class="sicon-trash"></i> حذف نهائي</a
                    >
                  </li>
                </ul>
              </div>
            </div>
            <!----- End More Options ------------------->
          </div>
          <!----- End Product Details --------------->

          <!-------------------- Save Product ------------>
          <div
            v-if="permissions.edit_product"
            :class="{
              'submit-wrapper': true,
              'submit-wrapper-save': submitting,
            }"
          >
            <button
              type="button"
              @click="saveProduct(product)"
              :data-product-id="`${product.id}`"
              class="btn btn-tiffany btn-xs btn-full save-product btn-save"
            >
              {{ saveProductText }}
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import ProductType from './ProductType'
import Multiselect from 'vue-multiselect'
import Http from '../../../utils/http'
import FormError from './FormError'
import vSelect from 'vue-select'
import initialData from '../../../services/ProductService'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import LingualField from '@salla.sa/languages/components/LingualField'
import * as Sentry from '@sentry/vue'
import {mapGetters, mapMutations} from 'vuex'

export default {
  props: [
    'products',
    'product',
    'index',
    'active',
    'baseStoreUrl',
    'categories',
    'productTypes',
    'validProductTypes',
    'storeId',
    'productClass',
    'updateCategoryIds',
    'fromSallaApp',
    'features',
    'permissions',
    'languages',
    'quantityEditableTypes',
    'initiateLanguageFields',
    'storeDocumentStatus',
    'storeHaveMobileApp',
    'plan',
    'mahlyData',
    'storeCurrency',
  ],
  components: {
    'product-type': ProductType,
    'multi-select': Multiselect,
    'form-error': FormError,
    'v-treeselect': Treeselect,
    LingualField: LingualField,
    'v-select': vSelect,
  },
  data() {
    return {
      isMobile: window.innerWidth <= 768,
      isChannelOpen: false,
      isExportOpen: false,
      selectedMahlyCategoryId: [],
      showCursor: true,
      customNoResultsText: 'لا يوجد نتائج متطابقة',
      addToMain: false,
      direction: 'rtl',
      errors: [],
      showQuantityLimit: false,
      saveProductText: 'حفظ',
      normalizer(node) {
        let nodeName =
          node.name ||
          _.get(node, 'translations.' + this.$attrs.isoCode + '.name')
        return {
          label: nodeName + (node.status == 'hidden' ? ' (مخفي)' : ''),
          value: node.id,
          children: node.sub_categories || node.subCategories,
        }
      },
      mahlyCatListNormalizer(node) {
        let nodeName = ''
        return {
          label: node.name,
          value: node,
          children:
            node.sub_categories && node.sub_categories.length
              ? node.sub_categories
              : 0,
        }
      },
      submitting: false,
      submittingMahlyStatus: false,
      check_product_interval: null,
      hasCategoriesError: false,
      hasNameError: true,
      hasNotifyQuantityError: false,
      hasPriceError: false,
      hasQuantityError: false,
      hasTypeError: false,
      lingualMethods: LingualField.methods,
      rules: {
        number: {
          regex: /^\d+(\.\d{1,9})?$/, // price regex numbers only allowed,
        },
      },
      rejectedTranslationsKeys: [
        // 'extra_attributes.metadata',
        // 'description',
        // 'custom_url',
        // 'promotion_title',
      ],
      prevMahlyCategory: null,
      isOpenMahlyCatsList: false,
    }
  },
  watch: {
    '$route'(to, from) {
      this.isMobile = window.innerWidth <= 768; // check on route change
    },
    selectedMahlyCategoryId: {
      handler(val) {
        if (val.length > 0) {
          this.product.mahly_category_id = val[0]
        } else {
          this.product.mahly_category_id = null
        }
      },
      deep: true,
    },
  },
  computed: {
    treeselectClass() {
      return {
        showPlaceholder: this.selectedMahlyCategoryId.length == 0,
      }
    },
    nameErrorText: function () {
      return this.errors.name
        ? this.errors.name.toString()
        : 'اسم المنتج مطلوب، ويجب ألا يحتوى على حروف خاصة مثل ـ-@ او سطر جديد'
    },
    productImage: function () {
      return this.product.image
        ? this.product.image
        : `${assetsBaseUrl}cp/assets/images/placeholder.png`
    },

    showInAppOption: function () {
      // since we are relying on the status weather we need
      // to show to the options to avoid miss understanding
      // the beehive of the options :)
      // when the product status is not within those status
      // we don't wanna show the options

      return this.product && ['sale', 'out'].includes(this.product.status)
    },
    ...mapGetters({
      isSelectAll: 'selectAll/isSelectAll',
      mahallyCetogries: 'mahally/getMahallyCetogries',
    }),
  },
  beforeUpdate() {
    setTimeout(() => {
      $(`.tooltip-toggle`).removeClass('visible')
    }, 1500)
  },

  created() {
    window.addEventListener('click', () => {
      if (this.showQuantityLimit === true) {
        this.showQuantityLimit = false
      }
    })
  },

  mounted() {
    this.prevMahlyCategory = this.product.mahly_category_id
    if (this.product.mahly_category_id != null) {
      this.selectedMahlyCategoryId = [
        ...this.selectedMahlyCategoryId,
        this.product.mahly_category_id,
      ]
    }
    Salla.event.addEventListener('languages::langDir', this.langDir)
  },
  methods: {
    onChannelsClicked() {
      if (this.isMobile) {
        this.isChannelOpen = !this.isChannelOpen;
        this.isExportOpen = false;
      }
    },
    onExportClick() {
      if (this.isMobile) {
        this.isExportOpen = !this.isExportOpen;
        this.isChannelOpen = false;
      }
    },
    alertForPackages() {
      swal({text: 'هذه الخاصية متاحة في باقة (سلة بلس و سلة برو و سلة سبيشل). يمكن ترقية الباقة من خلال باقة المتجر', type: 'info', showConfirmButton: false, timer: 3000 })
    },
    langDir(e) {
      this.direction = e.detail
    },

    /**
     * checkFieldError function
     * is used to check validation rules, client and server side validation of specific field
     * the client side depending on regex rules array, check field type [string or number]
     * the server side errors depending on errors array.
     *
     */
    checkFieldError(fieldName, type, isRequired) {
      let hasClientError = false,
        errorsArr = this.errors,
        rules = this.rules,
        hasError = false,
        fieldVal = this.product[fieldName],
        hasServerError = errorsArr && errorsArr[fieldName]
      // if the fieldName has client side validation
      if (type != 'server-only') {
        // if the type of fieldName should be number or string
        hasClientError = !rules[type].regex.test(fieldVal)
      }

      // the product has error if:
      // is not matching the regex, or exists in errors array.
      hasError =
        (hasClientError && fieldVal !== null && fieldVal != '') ||
        hasServerError

      if (isRequired) {
        // add check if the required fieldName
        return hasError || fieldVal === null || fieldVal === ''
      }

      return hasError
    },

    validateProduct() {
      // check price
      this.product.hasPriceError = this.checkFieldError(
        'price',
        'server-only',
        this.product.type !== 'financial_support'
      )

      // check quantity
      this.product.hasQuantityError = this.checkFieldError(
        'quantity',
        'number',
        false
      )

      // check notify quantity
      this.product.hasNotifyQuantityError = this.checkFieldError(
        'notify_quantity',
        'number',
        false
      )

      // check type
      this.product.hasTypeError = this.checkFieldError(
        'type',
        'server-only',
        true
      )

      // check categories
      this.product.hasCategoriesError = this.checkFieldError(
        'categories',
        'server-only',
        false
      )

      return (
        this.product.hasNameError != true &&
        this.product.hasPriceError != true &&
        this.product.hasQuantityError != true &&
        this.product.hasNotifyQuantityError != true &&
        this.product.hasTypeError != true &&
        this.product.hasCategoriesError != true
      )
    },

    /**
     * Pin Product
     */
    pinProduct() {
      if (this.product.id <= 0) {
        swal({
          title: 'تنبيه',
          text: 'قم بإضافة بيانات المنتج ثم الحفظ لتتمكن من تثبيت المنتج',
          type: 'error',
          confirmButtonText: 'موافق',
        })
        return
      }
      showLoading()
      let newState = this.product.pinned == 0 ? 1 : 0

      Http.post(
        '/products/' + this.product.id + '/pin',
        {
          pinned: newState,
          _token: _token,
        },
        ({ data }) => {
          this.product.pinned = newState
          hideLoading()
        },
        ({ error }) => {
          hideLoading()
        }
      )
    },

    toggleShowProduct() {
      this.toggleShowProductHandler()
    },

    toggleShowProductHandler() {
      let status = ''
      // if product is shown, hide it
      if (this.product.status == 'sale' || this.product.status == 'none') {
        status = 'hidden'
      } else {
        // if product is hidden, show it
        status =
          this.product.name != '' &&
          // product is donating we don't need to check for price
          (this.product.type === 'donating' ||
            // product not donating and has price >> sale
            (this.product.type !== 'donating' && this.product.price >= 0))
            ? 'sale'
            : 'none'
      }
      this.updateStatus(status)
    },

    /**
     * Update Product Status
     */
    updateStatus(status) {
      let prod = this.product
      showLoading()
      Http.post(
        '/products/' + this.product.id + '/status',
        {
          status: status,
          _token: _token,
        },
        ({ data }) => {
          hideLoading()
          eval(data.runJavascript)

          if (status == 'deleted') {
            this.$emit('removeProduct')

            // we don't show message in case of hide and show because styles are applied and it is clear,
            // but we need the message in case of deletion only

            laravel.ajax.successHandler(data)
          }

          this.product.status = status
        },
        ({ response }) => {
          if (response) {
            hideLoading()
            laravel.ajax.errorHandler(response)
            if (response.data.error.message && !response.data.error.fields) {
              swal({
                title: '',
                text: error.message,
                type: 'warning',
                showConfirmButton: false,
                timer: 4000,
              })
            }
          }
        }
      )
    },

    showInWeb() {
      showLoading()
      Http.post(
        '/products/' + this.product.id + '/show_in_web',
        {
          show_in_web: !this.product.show_in_web,
          _token: _token,
        },
        ({ data }) => {
          hideLoading()
          swal({
            text: data.message,
            type: 'success',
            confirmButtonText: 'موافق',
          })
          this.product.show_in_web = !this.product.show_in_web
          this.product.status =
            !this.product.show_in_web && !this.product.show_in_app
              ? 'hidden'
              : 'sale'
        },
        ({ response }) => {
          if (response) {
            hideLoading()
            laravel.ajax.errorHandler(response)
            if (response.data.error.message && !response.data.error.fields) {
              swal({
                title: '',
                text: error.message,
                type: 'warning',
                showConfirmButton: false,
                timer: 4000,
              })
            }
          }
        }
      )
    },
    showInApp() {
      showLoading()
      Http.post(
        '/products/' + this.product.id + '/show_in_app',
        {
          show_in_app: !this.product.show_in_app,
          _token: _token,
        },
        ({ data }) => {
          hideLoading()
          swal({
            text: data.message,
            type: 'success',
            confirmButtonText: 'موافق',
          })
          this.product.show_in_app = !this.product.show_in_app
          this.product.status =
            !this.product.show_in_app && !this.product.show_in_web
              ? 'hidden'
              : 'sale'
        },
        ({ response }) => {
          if (response) {
            hideLoading()
            laravel.ajax.errorHandler(response)
            if (response.data.error.message && !response.data.error.fields) {
              swal({
                title: '',
                text: error.message,
                type: 'warning',
                showConfirmButton: false,
                timer: 4000,
              })
            }
          }
        }
      )
    },

    /**
     * Update Product Show In Mahly
     */
    // showInMahly() {
    //   this.product.show_in_mahly_app = this.product.show_in_mahly_app ? false : true
    //   this.submittingMahlyStatus = true
    //   this.saveProduct(this.product)
    // },

    /**
     * Update Product Show In Vendor
     */
    showInVendor() {
      showLoading()

      Http.put(apiUrl + '/products/sales-channels/', {
            channel_id: 1715387403,
            products: [{
              id: this.product.id,
              category_id: this.product.vendor_category_id,
              published: !this.product.show_in_vendor_app
            }]
          },
          (response) => {
            hideLoading()
            swal({
              text: response.data.data.message,
              type: 'success',
              confirmButtonText: 'موافق',
            })
            this.product.show_in_vendor_app = !this.product.show_in_vendor_app
          },
          ({response}) => {
            hideLoading()
            swal({
              title: 'لا يمكن إظهار المنتج في سوق الجملة',
              text: Object.values(response?.data?.error?.fields)[0][0],
              type: 'warning',
              confirmButtonText: 'إغلاق',
            });
          }
      )
    },

    /**
     * Delete Product
     */
    deleteProduct() {
      swal({
        title: 'تحذير',
        text: 'سيتم حذف المنتج نهائياً',
        type: 'warning',
        showCancelButton: true,
        confirmButtonClass: 'btn-danger',
        confirmButtonText: 'موافق',
        cancelButtonText: 'إلغاء',
        closeOnConfirm: true,
        closeOnCancel: true,
      }).then(() => {
        this.updateStatus('deleted')
      })
    },

    // TODO:: don't make ajax request if no product to get the first image of product
    // used with mobile when click add photo or video to product
    updateProductImageFromSallaApp() {
      if (this.product.id < 0) {
        this.product.image = `${assetsBaseUrl}cp/assets/images/placeholder.png`
      }
      let counter = 1
      let check_product_interval = null

      setTimeout(function () {
        history.pushState('', document.title, window.location.pathname)
      }, 1000)

      // disable old product checking
      if (check_product_interval != null) {
        clearInterval(check_product_interval)
      }

      // setInterval
      check_product_interval = setInterval(() => {
        Http.get(
          '/products/' + this.product.id + '/image?first_only=true',
          (resp) => {
            // on success
            // disable time interval
            clearInterval(check_product_interval)
            check_product_interval = null

            // update product image
            this.product.image = resp.data
          },
          (error) => {
            this.product.image = `${assetsBaseUrl}cp/assets/images/placeholder.png`
          },
          {}
        )

        // if he forgot to close the upload win
        if (counter == 25) {
          clearInterval(check_product_interval)
          check_product_interval = null
        } else {
          counter++
        }
      }, 5000)
    },

    /**
     * Show Product Image Modal
     */
    openImages() {
      showLoading(event)
      $('#product_options').html('')
      if (this.product.id <= 0) {
        $.alert({
          title: 'تنبيه',
          content:
            'قم بإضافة بيانات المنتج ثم الحفظ لتتمكن من اضافة صور المنتج',
          buttons: {
            ok: {
              text: 'موافق',
            },
          },
        })
        hideLoading()
        return
      }

      Http.get(
        '/products/' + this.product.id + '/image',
        (resp) => {
          hideLoading()
          $('#product_options').html(resp.data)
          $('#modal_product_photos').modal()
        },
        (error) => {
          Sentry.captureException(error)
          laravel.ajax.errorHandler(error)
        }
      )
    },

    /**
     * Clone Product ..
     */
    cloneProduct() {
      let productId = this.product.id
      swal({
        title: 'تنبيه ',
        text: 'هل انت متأكد من نسخ المنتج سيتم انشاء منتج بنفس البيانات ؟',
        type: 'warning',
        showCancelButton: true,
        confirmButtonClass: 'btn-danger',
        confirmButtonText: 'تأكيد',
        cancelButtonText: 'إلغاء',
        closeOnConfirm: true,
        closeOnCancel: true,
      }).then(function () {
        showLoading()

        Http.post(
          '/products/' + productId + '/clone/',
          {},
          (resp) => {
            hideLoading()

            laravel.ajax.successHandler(resp.data)

            // push cloned product to array products
            Salla.event.createAndDispatch('products::product-cloned', {
              product: resp.data.data.product,
            })
          },
          ({ response }) => {
            hideLoading()

            if (response) {
              laravel.ajax.errorHandler(response)
            }
          }
        )
      })
    },

    /**
     * Save product is used in both create and update product
     */
    saveProduct(product) {
      this.saveProductText = 'جاري الحفظ...'
      this.submitting = true
      this.errors = []

      if (this.product.translations) {
        this.product.name =
          this.product.translations[this.languages.iso_code]?.name

        if (this.product.name.length === 0) {
          for (let lang of this.languages.supported) {
            if (
              lang.iso_code != this.languages.iso_code &&
              this.product.translations[lang.iso_code]?.name.length > 0
            ) {
              this.product.name = this.product.translations[lang.iso_code]?.name
              break
            }
          }
        }

        // this.unsetProductUnwantedTranslationsKeys();
      }

      if (!this.validateProduct()) {
        this.saveProductText = 'حفظ'
        this.submitting = false
        return
      }

      if (!this.prevMahlyCategory && this.product.mahly_category_id) {
        this.prevMahlyCategory = this.product.mahly_category_id
        // this.product.show_in_mahly_app = true
      }

      product.new = false

      if (this.storeId !== 4) {
        let type = this.product.type
        // the below code has been moved to backend to determine when to show the below
        // error/validation popup.
        // if (type !== 'product' && Object.values(this.validProductTypes).indexOf(type) === -1) {
        //   let typeObj = (this.productTypes)[type];
        //   swal({
        //     title: "تنبيه",
        //     text: "نوع المنتج (" + typeObj.label +
        //         ") متوفر فقط في الباقات (سلة بلس وسلة برو) يمكن ترقية الباقة من خلال متجر سلة",
        //     type: "error",
        //     confirmButtonText: 'موافق'
        //   });
        //   this.saveProductText = 'حفظ';
        //   this.submitting = false;
        //   return false;
        // }

        if (type == '') {
          swal({
            title: 'تنبيه',
            text: 'قم باختيار نوع المنتج لتتمكن من حفظ المنتج',
            type: 'error',
            confirmButtonText: 'موافق',
          })
          this.saveProductText = 'حفظ'
          this.submitting = false
          return false
        }

        this.product.id <= 0 ? this.createProduct() : this.updateProduct()
      }
    },

    createProduct() {
      Http.post(
        '/products',
        this.product,
        ({ data }) => {
          this.saveProductText = 'حفظ'
          this.submitting = false

          this.product.id = data.data.productId
          this.product.status = data.data.productStatus
          this.product.quantity_managed_by_branches =
            data.data.quantityManagedByBranches
          this.product.unlimited_quantity = data.data.unlimited_quantity
          this.product.image = data.data.image
          this.product.advancable = data.data.advancable
          this.product.prod_id_encode_store = data.data.prod_id_encode_store

          laravel.ajax.successHandler(data)
          eval(data.runJavascript)
          this.initiateLanguageFields()
        },
        ({ response }) => {
          this.saveProductText = 'حفظ'
          this.submitting = false
          if (response) {
            this.errors = response.data.error.fields
            laravel.ajax.errorHandler(response)
          }
        }
      )
    },

    updateProduct() {
      let product = this.product
      Object.keys(product.translations).forEach((key) => {
        if (product.translations[key].hasOwnProperty('description')) {
          delete product.translations[key].description
        }
      })
      Http.put(
        '/products/' + this.product.id,
        product,
        ({ data }) => {
          // laravel.ajax.successHandler(data);
          eval(data.runJavascript)
          this.saveProductText = 'حفظ'
          this.submitting = false
          this.product.unlimited_quantity = data.data.unlimited_quantity
        },
        ({ response }) => {
          this.saveProductText = 'حفظ'
          this.submitting = false

          if (response) {
            this.errors = response.data.error.fields
            if (this.submittingMahlyStatus) {
              // this.product.show_in_mahly_app = false
              this.submittingMahlyStatus = false
            }
            laravel.ajax.errorHandler(response)
          }
        }
      )
    },

    /**
     * Open Options And quantity Modal
     */
    openOptionsAndQuantity(prod_id) {
      showLoading()
      Http.get(
        '/products/quantity/management/' + prod_id,
        (response) => {
          initialData.setData({
            QuantityAndOptionsData: response.data.data.result,
          })
          if (this.product.type == 'codes') {
            Salla.event.createAndDispatch(
              'digital-card-options-management::show-modal',
              {
                data: response.data.data.result,
              }
            )
          } else {
            Salla.event.createAndDispatch('quantity-management::show-modal')
          }

          hideLoading()
        },
        () => {
          hideLoading()
        }
      )

      // Hide a repetitive console warning message
      $.event.special.touchstart = {
        setup: function (_, ns, handle) {
          this.addEventListener('touchstart', handle, {
            passive: !ns.includes('noPreventDefault'),
          })
        },
      }
    },

    productOptions(product, event) {
      showLoading(event)
      if (this.product.id <= 0) {
        $.alert({
          title: 'تنبيه',
          content:
            'قم بإضافة بيانات المنتج ثم الحفظ لتتمكن من التحكم بالخيارات المتقدمة',
          buttons: {
            ok: {
              text: 'موافق',
            },
          },
        })

        hideLoading()

        return
      }

      if (this.product.type == '') {
        $.alert({
          title: 'تنبيه',
          content: 'قم باختيار نوع المنتج لتتمكن من التحكم بالخيارات المتقدمة',
          buttons: {
            ok: {
              text: 'موافق',
            },
          },
        })

        hideLoading()

        return
      }

      $('#product_options').empty()

      $.ajax({
        url: baseUrl + '/products/' + this.product.id + '/details',
        type: 'GET',
        data: { product_type: this.product.type },
        cache: false,
        success: function (resp) {
          hideLoading()

          $('#product_options').html(resp)
          $('#modal_product_options').modal('show')

          $('#modal_product_options').on('shown.bs.modal', function () {
            // add product tags --- start ---
            $('.rec-tags').length ? $('.rec-tags').productTags() : null

            if ($('ul.nav').find('> li').length <= 5) {
              $('ul.nav').hide()
            }
          })
          $('#modal_product_options').on('hide.bs.modal', function () {})
        },
        error: function (err) {},
      })
    },
    NameUpdated($event) {
      $event.object.name = $event.value
    },
    quantityClicked(e) {
      if (!this.product.can_change_quantity) {
        if (this.product.id <= 0) {
          swal({
            title: 'تنبيه',
            text: 'قم بإضافة بيانات المنتج ثم الحفظ لتتمكن من تغيير الكمية',
            type: 'error',
            confirmButtonText: 'موافق',
          })
          return
        }
        let message = ''

        if (this.product.type === 'codes') {
          this.productOptions(this.product.id, e)
          return
        }

        if (
          this.product.quantity_managed_by_branches ||
          this.product.advancable
        ) {
          this.openOptionsAndQuantity(this.product.id)
          return
        }

        if (this.product.quantity_managed_by_branches) {
          message =
            this.product.type === 'product'
              ? 'لا يمكن تغيير كمية المنتج، يرجى تغييرها من الخيارات والكمية'
              : 'لا يمكن تغيير كمية المنتج، يرجي تغييرها من إدارة الكمية'
        } else {
          message = 'لا يمكن تغيير الكمية، يرجى تغييرها من بيانات المنتج'
        }
        swal({
          title: 'تنبيه',
          text: message,
          type: 'error',
          confirmButtonText: 'موافق',
        })
      }
    },
    updateSelectedProducts(product) {
      if (product.checked) {
        this.setSelectedProduct(product)
      } else if (this.isSelectAll) {
        this.setUnSelectedProduct(product)
      } else {
        this.removeFromSelectedProducts(product.id)
      }
    },

    getModalText() {
      if (this.product.id > 0 && this.product.type == 'codes') {
        return 'إدارة الأكواد'
      }

      if (this.product.id > 0 && this.product.advancable) {
        return 'الخيارات والكمية'
      }

      return 'إدارة الكمية'
    },

    /**
     * Open Notify Quantity Settings Modal
     */
    openNotifyQuantitySettings(prod_id) {
      showLoading()
      Http.get(
        '/products/quantity/notification_setting/' + prod_id,
        (response) => {
          initialData.setData({
            NotificationSettingData: response.data.data.result,
          })
          Salla.event.createAndDispatch('notify-quantity-settings::show-modal')

          hideLoading()
        },
        ({ response }) => {
          hideLoading()
        }
      )

      // Hide a repetitive console warning message
      $.event.special.touchstart = {
        setup: function (_, ns, handle) {
          this.addEventListener('touchstart', handle, {
            passive: !ns.includes('noPreventDefault'),
          })
        },
      }
    },

    showQuantityModal() {
      return (
        this.product.id > 0 &&
        (this.product.advancable ||
          this.product.quantity_managed_by_branches ||
          this.product.type == 'codes')
      )
    },

    unlimitedQuantityClicked() {
      if (!this.quantityEditableTypes.includes(this.product.type)) {
        swal({
          title: 'تنبيه',
          text: 'لايمكن تغيير كمية هذا النوع من المنتجات الى غير محدودة',
          type: 'error',
          confirmButtonText: 'موافق',
        })
        return
      }
      this.product.unlimited_quantity = !this.product.unlimited_quantity
      this.product.can_change_quantity = !this.product.can_change_quantity
    },
    showRedAlert() {
      return (
        this.product.quantity !== null &&
        !this.product.unlimited_quantity &&
        parseInt(this.product.quantity) <=
          parseInt(this.product.notify_quantity)
      )
    },

    unsetProductUnwantedTranslationsKeys() {
      for (let lang in this.product.translations) {
        for (let rejectKey of this.rejectedTranslationsKeys) {
          //omit function, for filtering an object to remove certain keys.
          this.product.translations = _.omit(
            this.product.translations,
            `${lang}.${rejectKey}`
          )
        }
      }
    },

    disableQuantity() {
      return this.product.unlimited_quantity && this.product.type !== 'booking'
    },

    toggleMahallyCategoriesList() {
      if (!this.isOpenMahlyList) {
        this.$refs.mahlyCategoriesList.openMenu()
        this.$refs.mahlyCategoriesList.focusInput()
      }
      this.isOpenMahlyList = !this.isOpenMahlyList
    },

    handleMahlyCatSelect(value) {
      const selectedKeys = this.$refs.mahlyCategoriesList
      this.selectedObj = value
      selectedKeys.closeMenu()
      if (value?.id === 0) {
        this.$emit('propose-mahly-category', this.product)
      }
    },
    // when clearing the menu, prevent value from becoming undefined
    handleMahlyCatInput(value) {
      const self = this
      const selectedKeys = this.$refs.mahlyCategoriesList
      if (value.length > 1) {
        selectedKeys.clear()
        selectedKeys.select(self.selectedObj)
      }
      if (value === undefined) {
        this.product.mahly_category_id = null
      }
    },

    onSearch(e) {
      this.$refs.input.value = e
      if (this.$refs.input.value.length > 0) {
        this.showCursor = false
      } else {
        this.showCursor = true
      }
    },
    onOpenMahalySelect() {
      this.showCursor = true
    },
    clearMahlyCatList() {
      this.$refs.mahlyCategoriesList.clear()
    },

    ...mapMutations('selectAll', ['setSelectedProduct']),
    ...mapMutations('selectAll', ['removeFromSelectedProducts']),
    ...mapMutations('selectAll', ['setUnSelectedProduct']),
  },
}
</script>
<style scoped>
.btn-remove,
.hidden-icon {
  padding: 3px 3px;
  border-radius: 50%;
  background-color: rgba(244, 67, 54, 0.8);
  color: white;
  font-family: 'sallaicons', serif;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-remove:hover {
  background-color: #f44336;
}

.mahally-tree-placeholder {
  background-color: var(--color-gray-50);
  color: var(--color-dark-300);
  font-size: 12px;
  box-shadow: inset 0 0 1px 1px var(--color-white);
  border-radius: 4px;
  text-align: center;
  padding: 5px 8px;
  border: 1px solid var(--color-gray-200);
  line-height: 24px;
  cursor: default;
}
</style>
<style lang="scss">


.vue-mahaly-custom-select .font-weight-bold {
  font-weight: 500;

  small {
    font-weight: normal;
  }
}
#products_div
  .product-box
  form.product-form
  .thumbnail
  .form-group
  .form-control.input-search {
  position: relative;
  padding-right: 30px !important;
  float: none !important;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.vue-mahaly-custom-select .vue-treeselect__placeholder,
.vue-treeselect__single-value {
  right: 20px;
}

.vue-mahaly-custom-select
  .vue-treeselect__list-item
  .vue-treeselect__option--selected {
  background-color: #fcfcfc;
  position: relative;

  &:before {
    content: '\ea9d';
    display: inline-block;
    height: 1px;
    position: absolute;
    left: 12px;
    top: 50%;
    font-family: sallaicons !important;
    color: #004956;
    font-size: 14px;
    line-height: 0;
    z-index: 3;
  }
}
#products_div
  .product-box
  form.product-form
  .thumbnail
  .form-group.categories
  .input-group
  .vue-mahaly-custom-select {
  z-index: unset;
}
.vue-mahaly-custom-select .vue-treeselect__value-container {
  position: relative;
  padding-right: 25px;
  &:before {
    content: '\e9a8';
    display: inline-block;
    height: 1px;
    position: absolute;
    right: 0;
    top: 50%;
    font-family: sallaicons !important;
    color: #bbbbbb;
    font-size: 14px;
    line-height: 0;
  }
}

.vue-mahaly-custom-select .vue-treeselect__option {
  cursor: pointer;
}

.vue-mahaly-custom-select .vue-treeselect__list-item {
  &:has(.vue-treeselect__list) {
    > .vue-treeselect__option > .vue-treeselect__label-container {
      background-color: #fcfcfc;
    }
    > .vue-treeselect__option {
      border-bottom: 1px solid #eeeeee;
    }
  }
}

.vue-mahaly-custom-select
  .vue-treeselect__list-item.vue-treeselect__indent-level-0
  > .vue-treeselect__option
  .vue-treeselect__label {
  font-weight: 500;
}

.vue-mahaly-custom-select.vue-treeselect--custom
  .vue-treeselect__list-item
  .vue-treeselect__option-arrow-container,
.vue-mahaly-custom-select.vue-treeselect--append-to-body
  .vue-treeselect__list-item
  .vue-treeselect__option-arrow-container {
  display: table-cell;
  float: left;
  top: 25px;

  svg {
    display: none;
  }

  position: relative;

  &:before {
    content: '\e90c';
    display: inline-block;
    height: 1px;
    position: absolute;
    left: 12px;
    top: 50%;
    font-family: sallaicons !important;
    color: #999999;
    font-size: 14px;
  }

  &:has(.vue-treeselect__option-arrow--rotated) {
    &::before {
      content: '\ed91';
      color: #004956;
    }
  }
}

.vue-mahaly-custom-select.vue-treeselect--custom
  .vue-treeselect__list-item
  .vue-treeselect__list
  .vue-treeselect__label:before,
.vue-mahaly-custom-select
  .vue-treeselect--append-to-body
  .vue-treeselect__list-item
  .vue-treeselect__list
  .vue-treeselect__label:before {
  display: none;
}

.vue-mahaly-custom-select.vue-treeselect--custom
  .vue-treeselect__list-item
  .vue-treeselect__option
  .vue-treeselect__label,
.vue-mahaly-custom-select
  .vue-treeselect--append-to-body
  .vue-treeselect__list-item
  .vue-treeselect__option
  .vue-treeselect__label {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.vue-mahaly-custom-select
  .vue-treeselect__list-item
  .vue-treeselect__option--highlight {
  background-color: #fcfcfc;
}

.vue-mahaly-custom-select .vue-treeselect__menu > .vue-treeselect__list {
  position: relative;
  overflow: auto;
  max-height: 240px;

  &::-webkit-scrollbar {
    width: 3px;
    background: #eeeeee;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  }

  &::-webkit-scrollbar-thumb {
    background-color: #004d5a;
    border-radius: 30px;
  }
}

.vue-mahaly-custom-select
  .vue-treeselect--custom
  .vue-treeselect__list-item
  .vue-treeselect__list
  .vue-treeselect__label:before,
.vue-mahaly-custom-select
  .vue-treeselect--append-to-body
  .vue-treeselect__list-item
  .vue-treeselect__list
  .vue-treeselect__label:before {
  display: none;
}

.vue-mahaly-custom-select
  .vue-treeselect--custom
  .vue-treeselect__list-item
  .vue-treeselect__option
  .vue-treeselect__label,
.vue-mahaly-custom-select
  .vue-treeselect--append-to-body
  .vue-treeselect__list-item
  .vue-treeselect__option
  .vue-treeselect__label {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.vue-mahaly-custom-select
  .vue-treeselect__list-item
  .vue-treeselect__option--highlight {
  background-color: #fcfcfc;
}

.vue-mahaly-custom-select
  .vue-treeselect.treeselect-invalid
  .vue-treeselect__control {
  border: 1px solid #f55157 !important;
  border-radius: 4px;
  margin-bottom: 1px;
}

.vue-mahaly-custom-select .vue-treeselect__menu {
  position: relative !important;
  max-height: auto;
  overflow: visible;
}

.vue-mahaly-custom-select .vue-treeselect__input {
  opacity: 0;
  width: 0 !important;
}

.vue-mahaly-custom-select .input-search-container {
  position: sticky;
  background-color: #fff;
  z-index: 1;
  top: 0;

  input {
    padding-right: 38px;
    background-color: #fff;
  }

  &:before {
    content: '\ef09';
    display: inline-block;
    height: 1px;
    position: absolute;
    right: 5px;
    top: 50%;
    font-family: sallaicons !important;
    color: #444444;
    font-size: 15px;
    line-height: 0;
    z-index: 3;
  }
}

.vue-mahaly-custom-select img {
  display: none;
}

.vue-mahaly-custom-select.showPlaceholder .vue-treeselect-helper-hide {
  display: block !important;
}
.vue-mahaly-custom-select.vue-treeselect--multi
  .vue-treeselect__input-container {
  padding: 0;
}

.vue-treeselect--searchable.vue-treeselect--multi.vue-treeselect--has-value
  .vue-treeselect__input-container {
  padding-top: 0;
  padding: 0;
}
.vue-mahaly-custom-select
  .vue-treeselect__list-item
  .vue-treeselect__option--selected {
  background-color: #fcfcfc;
  position: relative;

  &:before {
    content: '\ea9d';
    display: inline-block;
    height: 1px;
    position: absolute;
    left: 12px;
    top: 50%;
    font-family: sallaicons !important;
    color: #004956;
    font-size: 14px;
    line-height: 0;
  }
}

.vue-mahaly-custom-select #mock-cursor {
  display: inline-block;
  color: white;
  animation: blink-animation 0.8s steps(5, start) infinite;
  -webkit-animation: blink-animation 0.8s steps(5, start) infinite;
  font-size: 1.2em;
  font-weight: lighter;
  width: 1px;
  position: absolute;
  background: #9b9b9b;
  top: 5px;
  right: 30px;
  z-index: 3;
}

@keyframes blink-animation {
  to {
    visibility: hidden;
  }
}

@-webkit-keyframes blink-animation {
  to {
    visibility: hidden;
  }
}
</style>

<style>
.multi-language-input.form-control.en-language-input{
  direction:ltr;
}
</style>