<template>
  <modal id="export-templates-modal" modal-title="إختر صيغة الملف" save-btn="" close-btn="إلغاء"
    @saveModal="onSaveModal">
    <div class="row d-flex justify-content-center">
      <div class="col-xs-3">
        <button class="btn btn--large btn--outlined btn-light btn-block gap-10 py-10"
          :class="format == 'xlsx' && 'btn-outline-primary active'" @click="format = 'xlsx'">
          <i class="sicon-file-partial"></i>
          <span class="font-14">XLSX</span>
        </button>
      </div>
      <div class="col-xs-3">
        <button class="btn btn--large btn--outlined btn-light btn-block gap-10 py-10"
          :class="format == 'csv' && 'btn-outline-primary active'" @click="format = 'csv'">
          <i class="sicon-page"></i>
          <span class="font-14">CSV</span>
        </button>
      </div>
    </div>
    <template #modal-action-hyperlink >
      <a class="ajax btn btn-info btn-save" :data-nonconfirm="true" :href="`${template.url}?template_id=${template.id}&type=${format}`">
        تصدير
      </a>
    </template>
  </modal>
</template>

<script>
import Modal from './layout/Modal'

export default {
  components: {
    modal: Modal,
  },
  props: ['id', 'templates', 'template'],
  data() {
    return {
      format: 'xlsx',
    }
  },
  mounted() {

  },
  methods: {
    onSaveModal() {
      if (!this.format) {
        swal({
          title: 'خطأ',
          text: 'يجب اختيار صيغة الملف',
          icon: 'error',
          type: 'error',
        })
        return
      }
      console.log('save modal')
      showLoading()
      axios
        .post(`/${this.template.url}`, {
          format: this.format,
          template_id: this.template.id,
          type: this.template.type || 'default'
        })
        .then((response) => {
          console.log(response)
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          hideLoading()
        })

      $('#export-templates-modal').modal('hide')
    },
  },
}
</script>
<style lang="scss" scoped>
#export-templates-modal button {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &.active {
    border: 2px solid #76E8CD !important;
  }

  i {
    color: #76E8CD;
    font-size: 32px;
  }
}
</style>