<template>
    <div :id="id" class="modal fade modal-backup">
        <div class="modal-dialog" style="direction: rtl;">
            <div class="modal-content">
                <div class="modal-header bg-info">
                    {{modalTitle}}
                    <button type="button" class="close" data-dismiss="modal">×</button>
                </div>
                <div class="modal-body">
                    <slot></slot>
                </div>
                <div class="modal-footer no-icons">
                    <button type="button" @click="$emit('saveModal')" class="btn btn-info btn-save" v-if="!hasLoader && saveBtn">{{saveBtn}}</button>
                    <button type="button" @click="$emit('saveModal', $event)" class="btn btn-info btn-save" data-inline-loader v-if="hasLoader && saveBtn">{{saveBtn}}</button>
                    <slot name="modal-action-hyperlink"></slot>
                    <button class="btn btn-info btn-close" data-dismiss="modal">{{closeBtn}}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ['modalTitle', 'id', 'closeBtn', 'saveBtn', 'hasLoader']
}
</script>