<template>
  <div
    class="align-left rec-list rec-list--horizental rec-list--align-center products-control"
  >
    <div
      class="filter-mode bulk-edit-filter"
      :data-filter-mode="filters && filters.filtering == 1"
    >
      <a
        id="rec_filter_toggle"
        class="btn btn-filter-toggle"
        :class="{ active: filters && filters.filtering == 1 }"
        @click="getFilters()"
        ><i class="sicon-filter"></i> تصفية
        <i
          v-show="filters && filters.filtering == 1"
          class="sicon-cancel"
          @click="cancelFilter"
        ></i
      ></a>
    </div>

    <!-- Services ---->
    <services :permissions="permissions" :templates="templates" />

    <!-- Filter Products ---->
    <filters
      v-if="permissions.enable_filter"
      :extra-class="true"
      :is-using-templates="isUsingTemplates"
    ></filters>

    <!-- Switch Products View From Grid To List -->
    <button
      type="button"
      :class="['btn', 'switch', `btn--outlined ${primary}`, 'ml-10']"
      @click="switchView('grid')"
    >
      <i class="sicon-layout-grid"></i>
    </button>
    <button
      type="button"
      :class="['btn', 'switch', `btn--outlined ${trans}`]"
      @click="switchView('list')"
    >
      <i class="sicon-list"></i>
    </button>
    <div
      v-show="countSelectedProducts"
      :class="['products-checked']"
      style="position: relative; display: flex; justify-content: flex-end"
    >
      <div
        style="position: relative; left: auto; float: right"
        :class="{
          'rec-checkbox': true,
          'rec-checkbox--default': true,
          'rec-checkbox--large': true,
          'rec-checkbox--primary-bg': true,
          'rec-checkbox--active-dashed': countSelectedProducts ? true : false,
        }"
      >
        <input
          id="checked_products"
          type="checkbox"
          name="checked"
          :checked="countSelectedProducts < productsLength ? false : true"
          @change="checkAllProducts($event)"
        />
        <label for="checked_products"></label>
      </div>
      <a
        href="#!"
        class="dropdown-toggle ml-5"
        data-toggle="dropdown"
        aria-expanded="false"
        >المنتجات المحددة <span>{{ countSelectedProducts }}</span></a
      >

      <ul class="dropdown-menu dropdown-menu-right mobile-top">
        <li v-if="fromSallaApp != 1 && isFeatureEnabled('ai-seo-feature')">
          <a @click="openAiDescriptionGenerator">
            <i class="sicon-magic-wand"></i>
            توليد وصف المنتجات
            <span v-if="getStorePlanBadge('ai-description')" class="badge badge-warning">{{ getStorePlanBadge('ai-description') }}</span>
          </a>
        </li>
        <li v-if="fromSallaApp != 1 && isFeatureEnabled('ai-description-feature')">
          <a @click="openAiSeoGenerator">
            <i class="sicon-magic-wand"></i>
            توليد تحسينات (SEO)
            <span v-if="getStorePlanBadge('ai-seo')" class="badge badge-warning">{{ getStorePlanBadge('ai-seo') }}</span>
          </a>
        </li>
        <!-- ai-description-feature -->
        <li v-if="fromSallaApp != 1">
          <a @click="openProductsEditor">
            <i class="sicon-inbox-full"></i>
            تعديل المنتجات
          </a>
        </li>
        <li v-if="fromSallaApp != 1">
          <a @click="openProductPricesEditor">
            <i class="sicon-inbox-full"></i>
            تعديل الأسعار
          </a>
        </li>

        <li v-if="fromSallaApp != 1">
          <a @click="openQuantityEditor">
            <i class="sicon-inbox-multi"></i>
            تعديل الكميات
          </a>
        </li>
        <li v-if="isMahallyFeaturedEnabled">
          <a @click="openMahallyModal">
            <i class="sicon-mahally"></i>
            ربط بتصنيف محلي
          </a>
        </li>
        <li v-if="isVendorAppEnabled" :class="{ disabled: !selectedTypesValidWholesale }">
          <a class="d-flex" @click="openWholesaleModal">
            <svg
              class="d-inline-block mr-10 flex-shrink-0"
              width="19"
              height="16"
              viewBox="0 0 22 22"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21.9648 16.9456L20.8997 8.6634C20.8213 8.05344 20.2269 7.61898 19.572 7.69126C18.917 7.76433 18.4497 8.31792 18.5282 8.92788L19.5933 17.21C19.6786 17.8708 19.4637 18.5086 18.9904 19.0058C18.518 19.5022 17.8647 19.7754 17.1535 19.7754H4.84476C4.1327 19.7754 3.48034 19.5022 3.00791 19.0058C2.53377 18.5086 2.31973 17.8708 2.40501 17.21L3.47011 8.92788C3.61849 7.77704 4.66738 6.90895 5.91071 6.90895H15.5529C16.0211 6.90895 16.4688 6.72231 16.78 6.39668C17.0896 6.07263 17.2371 5.64057 17.1834 5.21169C17.0094 3.80433 16.2931 2.49704 15.1666 1.53047C14.0171 0.544043 12.5375 0 11 0C9.54263 0 8.12619 0.478917 7.00992 1.3478C6.50424 1.74173 6.43687 2.44303 6.85984 2.91401C7.28281 3.38498 8.0358 3.44773 8.54149 3.05379C9.22881 2.51848 10.102 2.22383 11 2.22383C12.6279 2.22383 14.0836 3.25632 14.6191 4.68592H5.91071C3.45987 4.68592 1.39107 6.39668 1.09943 8.6642L0.0343249 16.9464C-0.130258 18.2282 0.302094 19.5173 1.22223 20.4822C2.14065 21.4464 3.46158 22 4.84561 22H17.1544C18.5384 22 19.8585 21.4472 20.7778 20.4822C21.6979 19.5173 22.1303 18.2282 21.9657 16.9464L21.9648 16.9456Z"
                fill="#004956"
              />
            </svg>
            <span>ربط بتصنيف سوق الجملة</span>
            <DangerBaseTolltip
              v-if="!selectedTypesValidWholesale"/>
          </a>
        </li>
        <li v-if="permissions.delete_product">
          <a class="delete-product danger" @click="deleteSelectedProducts">
            <i class="sicon-trash"></i>
            حذف المنتج نهائيا
          </a>
        </li>
      </ul>
    </div>
    <MahallyLinkModal
      :modal-change="modalOpen"
      :products="selected_products"
    ></MahallyLinkModal>
    <WholesaleMarketplaceModal
      :modal-change="modalOpen"
      :products="selected_products"
      @wholesaleLinked="onWholeSaleLinked"
    ></WholesaleMarketplaceModal>
  </div>
</template>

<script>
import Filters from './filters'
import Services from './Services'
import Http from '../../../utils/http'
import MahallyLinkModal from './MahallyLinkModal.vue'
import WholesaleMarketplaceModal from './WholesaleMarketplaceModal.vue'
import { mapGetters } from 'vuex'
import _ from 'lodash'
import DangerBaseTolltip from './DangerBaseTolltip.vue'
export default {
  components: {
    filters: Filters,
    services: Services,
    MahallyLinkModal,
    WholesaleMarketplaceModal,
    DangerBaseTolltip,
  },
  props: {
    primary: {
      type: String,
      required: true,
    },
    trans: {
      type: String,
      required: true,
    },
    switchView: {
      type: Function,
      required: true,
    },
    checkAllProducts: {
      type: Function,
      required: true,
    },
    fromSallaApp: {
      type: Boolean,
      required: true,
    },
    productsLength: {
      type: Number,
      required: true,
    },
    products: {
      type: Array,
      required: true,
    },
    filters: {
      type: Array,
      required: true,
    },
    permissions: {
      type: Object,
      required: true,
    },
    features: {
      type: Object,
      required: true,
    },
    templates: {
      type: Array,
      required: true,
    },
    isUsingTemplates: {
      type: Boolean,
      required: true,
    },
    isVendorAppEnabled: {
      type: Boolean,
      required: true,
    },
    plan: {
      type: String,
      required: true
    }
  },
  data: () => ({
    cancel_filter: false,
    selected_products: [],
    modalOpen: false,
    selectedTypesValidWholesale: true,
  }),
  computed: {
    ...mapGetters({
      countSelectedProducts: 'selectAll/countSelectedProducts',
      countTotalProducts: 'selectAll/countTotalProducts',
      isSelectAll: 'selectAll/isSelectAll',
      getSelectedProducts: 'selectAll/getSelectedProducts',
      isMahallyFeaturedEnabled: 'mahally/isMahallyFeaturedEnabled',
    }),
  },
  watch: {
    getSelectedProducts: {
      handler() {
        if (this.isSelectedTypesValid()) {
          this.selectedTypesValidWholesale = true
        } else {
          this.selectedTypesValidWholesale = false
        }
      },
      deep: true, // Ensure the watcher detects changes within the objects
    },
  },
  methods: {
    onWholeSaleLinked(){
      this.$emit('reloadProducts');
    },
    isSelectedTypesValid() {
      const allowedTypes = ['food', 'codes', 'product']
      return this.getSelectedProducts.every((product) =>
        allowedTypes.includes(product.type)
      )
    },
    /**
     * fetch all filters data
     */
    getFilters() {
      window.Salla.event.createAndDispatch('products::fetch-filters')
    },

    /**
     * Cancel filtration
     * @param event
     */
    cancelFilter(event) {
      event.stopPropagation()
      this.$emit('cancelFilter')
    },
    getSelectedIDs() {
      return _.map(this.getSelectedProducts, 'id')
    },

    /**
     * Delete Selected Products
     */
    deleteSelectedProducts() {
      const self = this;
      if (this.permissions.delete_product) {
        swal({
          title: '<h2>' + 'تحذير' + '</h2>',
          html: '<p>' + 'سيتم حذف المنتج نهائيا' + '</p>',
          type: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#d33',
          confirmButtonText: 'موافق !',
          cancelButtonText: '<i class="fa fa-close"></i> إلغاء',
        }).then(
          () => {
            Http.delete(
              '/products/all',
              { products_ids: this.getSelectedIDs(),
                select_all: self.isSelectAll
               },
              ({ data }) => {
                this.selected_products = []
                laravel.ajax.successHandler(data)
                window.showLoading()
                this.$emit('reloadProducts')

                $('#products_div').find('.thumbnail').removeClass('active')
                $('.products-checked').slideUp()
              },
              ({ response }) => {
                laravel.errors.renderValidation(response.data, ' ')
              }
            )
          },
          function () {}
        )
      } else {
        swal({
          title: '<h2>' + 'تحذير' + '</h2>',
          html: '<p>' + 'ليس لديك صلاحية حذف المنتج' + '</p>',
          type: 'warning',
        }).then(
          () => {},
          function () {}
        )
      }
    },
    openQuantityEditor() {
      window.location.assign(
        this.appendArrayToUrl(
          this.getSelectedIDs(),
          'products',
          '/products/quantity/editor'
        )
      )
    },

    openProductsEditor() {
      window.location.assign(
        this.appendArrayToUrl(
          this.getSelectedIDs(),
          'products',
          '/products/bulk/editor'
        )
      )
    },

    openAiDescriptionGenerator() {
      window.location.assign(
        this.appendArrayToUrl(
          this.getSelectedIDs(),
          'products',
          '/addons/ai-description'
        )
      )
    },

    openAiSeoGenerator() {
      window.location.assign(
        this.appendArrayToUrl(
          this.getSelectedIDs(),
          'products',
          '/addons/ai-seo'
        )
      )
    },

    openProductPricesEditor() {
      window.location.assign(
        this.appendArrayToUrl(
          this.getSelectedIDs(),
          'products',
          '/products/bulk/pricing'
        )
      )
    },
    appendArrayToUrl(array, key, url) {
      let ids = array
        .map(function (el, idx) {
          return key + '[' + idx + ']=' + el
        })
        .join('&')

      return window.baseUrl + url + '?' + ids
    },
    openMahallyModal(e) {
      e.preventDefault()
      $('#mahally-link-modal').modal('show')
      this.modalOpen = !this.modalOpen
    },
    openWholesaleModal(e) {
      e.preventDefault()
      if (this.selectedTypesValidWholesale) {
        $('#wholesale-marketplace-modal').modal('show')
        this.modalOpen = !this.modalOpen
      }
    },
    isFeatureEnabled(feature) {
      return this.features[feature] || false; 
    },
    getStorePlanBadge(feature) {
      const features = {
        'ai-description': {
          condition: () => ['pro', 'plus', 'special'].includes(this.plan),
          label: 'بلس / برو'
        },
        'ai-seo': {
          condition: () => ['pro', 'special'].includes(this.plan),
          label: 'برو'
        }
      };

      if(! features[feature]) {
        return false;
      }

      if(features[feature].condition()) {
        return false;
      }

      return features[feature].label;
    }
  },
}
</script>
<style scoped>
.disabled {
  background-color: #eee;
}

.badge.badge-warning {
  background: #FFC62A;
  color: #AF820A;
  border-color: #FFC62A;
}
</style>
