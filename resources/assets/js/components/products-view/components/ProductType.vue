<template>
    <v-select :options="productTypes" :disabled="product.id > 0?true:false" Searching="false" appendToBody :calculate-position="withPopper" :reduce="productTypes => productTypes.value" v-model="product.type"
              @input="changeProductType">
        <template #option="{ description, label }">
            <h6>{{ label }}</h6>
            <span>{{ description }}</span>
        </template>
    </v-select>
</template>

<script>
import vSelect from 'vue-select';
import { createPopper } from '@popperjs/core';

export default {
    props: ['productTypes', 'product', 'changeProductType'],
    data() {
        return {
            placement: 'top'
        }
    },
    components: {
        'v-select': vSelect
    },
    methods: {
        withPopper (dropdownList, component, {width}) {
            const parent = document.querySelector('.product-name-type .form-group').offsetWidth;
            dropdownList.style.width = parent + 'px';
            const popper = createPopper(component.$refs.toggle, dropdownList, {
                placement: this.placement,
                modifiers: [
                {
                    name: 'offset', options: {
                    offset: [0, -1]
                    }
                },
                {
                    name: 'toggleClass',
                    enabled: true,
                    phase: 'write',
                    fn ({state}) {
                    component.$el.classList.toggle('drop-up', state.placement === 'top')
                    },
                }]
            });
            return () => popper.destroy();
        }
    }
}
</script>