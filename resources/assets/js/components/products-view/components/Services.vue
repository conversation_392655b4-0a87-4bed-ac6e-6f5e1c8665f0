<template>
    <ul class="nav product-action-btn-list">
        <li ref="product_services" class="dropdown">
            <a class="dropdown-toggle btn btn-filter-toggle" @click="getServices">
                <span v-show="loader" class="loader loader--smaller v-align-middle ml-5"></span>
                <i v-show="!loader" class="sicon-toolbox"></i>
                خدمات
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
                <template v-for="(service, index) in services">
                    <li v-if="service.available" :key="index" :class="service.class">
                    <!------------- if the service doesnt has submenu -------->
                    <a v-if="!service.sub_menu.length" :href="service.url" class="dropdown-link"
                        @click="checkConfirm(service)">
                        <i :class="service.icon"></i>
                        {{ service.label }}
                        <span v-if="service.badge" class="badge badge-warning">{{service.badge}}</span>
                    </a>

                    <!------------- if the service has submenu -------->
                    <a v-if="service.sub_menu.length" data-toggle="tab">
                        <i :class="service.icon"></i>
                        {{ service.label }}
                    </a>

                    <ul v-if="service.sub_menu.length && service.name != 'export_products'"
                        class="dropdown-menu dropdown-menu-xs" aria-labelledby="dLabel">
                        <li v-for="(sub_service, index) in service.sub_menu" :key="index" class="">
                            <a :class="`dropdown-link ${(sub_service.ajax === false || sub_service.message) ? '' : 'ajax'}`"
                                :data-nonconfirm="`${(sub_service.message) ? 'false' : 'true'}`" :href="sub_service.url"
                                @click="checkConfirm(sub_service)">
                                <i :class="`${service.icon} position-left`"></i>
                                {{ sub_service.label }}
                            </a>
                        </li>
                    </ul>
                    <ul v-if="service.sub_menu.length && service.name == 'export_products'"
                        class="dropdown-menu dropdown-menu-xs" aria-labelledby="dLabel">
                        <li v-if="permissions.product_export_templates" class="d-flex justify-content-between align-items-center pr-10 pb-5 border-bottom">
                            <a href="/settings/component/export-temps" class="`dropdown-link">
                                <i :class="`sicon-page position-left`"></i>
                                إضافة قالب تصدير
                            </a>
                            <i :class="`sicon-add`"></i>
                        </li>
                        <li v-for="(template, index) in templates" :key="index" class="">
                            <a href="" class="dropdown-link" @click="(e) => showExportModal(e, template)">

                                {{ template.name }}
                                {{template.is_custom ? ' ( مخصص )' : ''}}
                            </a>
                        </li>
                    </ul>
                </li>
                </template>
            </ul>
        </li>
    </ul>
</template>

<script>

import Http from "../../../utils/http";

export default {
    data: () => ({
        services: [],
        loader: false,
    }),
    props:['templates','permissions'],
    created() {
    },
    mounted() {
        Salla.event.addEventListener('products::fetch-services', this.getServices);
        document.addEventListener('click', (e) => {
            this.$refs.product_services.classList.remove("open");
        });
        console.log(this.templates, 'templates services');
    },
    beforeDestroy() {
        document.removeEventListener('products::fetch-services', this.getServices);
    },
    methods: {
        // Get all Services Data
        getServices(e) {
            e.stopPropagation();

            if (Object.keys(this.services).length) {
                /*TODO:: change to global*/
                let userAgent = window.navigator.userAgent;
                userAgent.match(/iPad/i) || userAgent.match(/iPhone/i) || userAgent === 'sallapp' ?
                    this.$refs.product_services.classList.toggle(" open") :
                    this.$refs.product_services.classList.toggle("open")
                return;
            }

            this.loader = true;
            Http.get('/products/services', ({ data }) => {
                this.$refs.product_services.classList.add("open");
                this.services = data.data.services;
                this.loader = false;

            }, ({ error }) => {
                laravel.errors.renderValidation(error.data, ' ');
            });
        },

        // Display Confirm message , then make the action
        checkConfirm(service) {
            if (!service.message) {
                return;
            }
            swal({
                title: service.message.title,
                text: service.message.text,
                type: 'warning',
                showCancelButton: true,
                confirmButtonClass: service.name === 'delete_all' ? 'red-alert' : '',
                confirmButtonColor: service.message.color ? service.message.color : '',
                confirmButtonText: service.message.confirm_button_text ? service.message.confirm_button_text : 'موافق',
                cancelButtonText: 'إلغاء'
            }).then(function (isConfirm) {
                if (isConfirm) {
                    let ajaxLoad = ['delete_all', 'sync_all'];
                    if (!ajaxLoad.includes(service.name)) {
                        window.location.href = service.message.url;
                        return;
                    }

                    $.ajax({
                        url: service.message.url,
                        type: service.message.type,
                        data: { _token: _token },
                        cache: false,
                        success: function (resp) {
                            laravel.ajax.successHandler(resp)
                            window.location.href = '/products';
                        },
                        error: function (err) {
                        }
                    });

                }
            }).catch(swal.noop);
        },
        showExportModal(e, template) {
            e.preventDefault()
            console.log(template, 'template for export')
            Salla.event.createAndDispatch('export-templates::export-modal', {
                template
            })
        }
    }
}
</script>
<style scoped>
.badge.badge-warning {
  background: #FFC62A;
  color: #AF820A;
  border-color: #FFC62A;
}
</style>
