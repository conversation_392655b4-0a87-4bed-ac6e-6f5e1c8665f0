<template>
  <div class="dropdown mt-12">
    <a id="add-btn" aria-expanded="false" class="btn btn-tiffany btn-xlg btn--dropdown btn-rounded mt-0"
       data-toggle="dropdown">
      <i class="sicon-keyboard_arrow_down"></i>
    </a>
    <ul class="dropdown-menu dropdown-menu-left dropdown-menu--products-type">
      <li v-for="(productType, index) in productTypes" :key="index" :data-type="productType.value"
          @click="addNewProduct(productType)">
        <a class="dropdown-link" :class="{'dropdown-link--template': productType.value === 'template'}" data-product-type="product">
          <i :class="productType.icon"></i>
          <h6>
            {{ productType.label }}
            <small v-if="productType.value === 'template'" class="badge badge--small badge--danger ml-5 d-inline-block">جديد</small>
            <small v-if="productType.value !== 'product' && plan === 'basic' && productType.value !== 'template'" class="d-inline-block badge badge--small badge--warning lh-1">بلس/ برو</small>
            <span>{{ productType.description }}</span>
          </h6>
        </a>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ['productTypes', 'addNewProduct', 'plan']
}
</script>
