<template>
  <Modal
    id="mahally-link-modal"
    :modal-title="'ربط المنتجات بتصنيف محلي'"
    close-btn="إلغاء"
    save-btn="ربط المنتجات"
    :loading="loading"
    class="text-left"
    @saveModal="saveField"
  >
    <section class="alert-box alert-box--info" style="color: #417ac8">
      <span class="mr-5">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="26"
          height="26"
          viewBox="0 0 26 26"
          fill="none"
        >
          <path
            d="M13.0001 23.8333C7.01699 23.8333 2.16675 18.983 2.16675 13C2.16675 7.01691 7.01699 2.16667 13.0001 2.16667C18.9831 2.16667 23.8334 7.01691 23.8334 13C23.8334 18.983 18.9831 23.8333 13.0001 23.8333ZM13.0001 21.6667C17.7866 21.6667 21.6667 17.7865 21.6667 13C21.6667 8.21353 17.7866 4.33333 13.0001 4.33333C8.21361 4.33333 4.33341 8.21353 4.33341 13C4.33341 17.7865 8.21361 21.6667 13.0001 21.6667ZM11.9167 7.58333H14.0834V9.75H11.9167V7.58333ZM11.9167 11.9167H14.0834V18.4167H11.9167V11.9167Z"
            fill="#417AC8"
          />
        </svg>
      </span>
      <article>
        <p>
          تم تحديد ({{ countSelectedProducts }}) منتجات تود ربطها بتصنيفات محلي
          قم باختيار التصنيف المناسب
        </p>
      </article>
    </section>
    <div>
      <label class="text-left mb-5 font-weight-bold"
        >تصنيف محلي <span class="text-danger">*</span>
        <small class="text-muted text-muted-smaller"> (تصنيف واحد فقط) </small>
      </label>
      <p>
        <small class="text-muted text-muted-smaller">
          يمكنك اختيار تصنيف واحد فقط للمنتجات المحددة.
        </small>
      </p>
      <div class="vue-treeselect--custom-wrapper">
        <v-treeselect
          ref="mahlyCategoriesList"
          v-model="mahly_category"
          :no-results-text="customNoResultsText"
          :no-options-text="customNoResultsText"
          :class="treeselectClass"
          :multiple="true"
          :input-class="['new-line-search']"
          :searchable="true"
          :show-count="false"
          :clearable="true"
          :close-on-select="true"
          :always-open="false"
          :loading-text="'جاري جلب البيانات...'"
          :no-children-text="'...'"
          :is-default-expanded="true"
          :backspace-removes="false"
          :clear-on-select="true"
          :flat="true"
          :disable-branch-nodes="true"
          class="vue-treeselect--custom vue-treeselect--enhanced-tags vue-treeselect--without-effect vue-treeselect--full"
          placeholder="اختر من تصنيفات محلي"
          :options="mahallyCetogries"
          :normalizer="mahlyCatListNormalizer"
          value-format="object"
          :disable-fuzzy-matching="true"
          @select="handleMahlyCatSelect"
          @input="handleMahlyCatInput"
          @search-change="onSearch"
          @open="onOpenMahalySelect"
        >
          <div slot="before-list" class="input-search-container">
            <input
              ref="input"
              class="form-control wide input-search"
              :placeholder="placeholderText"
            />
          </div>
          <template slot="value-label" slot-scope="{ node }">
            <span class="value">{{ node.raw.name }}</span>
            <span title="Clear value" class="clear" @click="clearMahlyCatList">
              <img
                src="https://cdn.assets.salla.network/dash/vendor/mahlydash/images/icons/dismiss.svg"
                alt="clear"
              />
            </span>
          </template>
        </v-treeselect>
        <p
          v-if="
            (mahly_category == null || mahly_category.length == 0) &&
            isSubmited == true
          "
          class="mt-5"
        >
          <small
            id="weight_error_message"
            class="help-block message-helpers help-tags text-danger"
          >
            يلزم اختيار تصنيف محلي للمتابعة.</small
          >
        </p>
      </div>
    </div>
  </Modal>
</template>
<script>
import axios from 'axios'
import Modal from './layout//Modal.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { mapGetters } from 'vuex'
import initialData from '../../../services/ProductService'

export default {
  components: {
    Modal,
    'v-treeselect': Treeselect,
  },
  props: [
    // eslint-disable-next-line vue/require-prop-types
    'products',
    'modalChange',
  ],
  data() {
    return {
      placeholderText: 'ابحث عن تصنيف محلي المناسب ...',
      showCursor: true,
      selectedObj: {},
      customNoResultsText: 'لا يوجد نتائج متطابقة',
      selectedProducts: 0,
      mahly_category: null,
      isSubmited: false,
      mahlyCatListNormalizer(node) {
        // eslint-disable-next-line no-unused-vars
        let nodeName = ''
        return {
          label: node.name,
          value: node,
          children:
            node.sub_categories && node.sub_categories.length
              ? node.sub_categories
              : 0,
        }
      },
      loading: false,
      value: [],
      mahally_endpoint: '',
      headers: {},
    }
  },
  computed: {
    ...mapGetters({
      countSelectedProducts: 'selectAll/countSelectedProducts',
      getSelectedProductIds: 'selectAll/getSelectedProductIds',
      getSelectedProductsWithName: 'selectAll/getSelectedProductsWithName',
      getUnSelectedProductIds: 'selectAll/getUnSelectedProductIds',
      mahallyCetogries: 'mahally/getMahallyCetogries',
      isSelectAll: 'selectAll/isSelectAll',
      getSelectedFilters: 'filters/getSelectedFilters',
      hasFiltersSelected: 'filters/hasSelectedFilters',
    }),

    treeselectClass() {
      return {
        'treeselect-invalid':
          this.isSubmited &&
          (this.mahly_category == null || this.mahly_category.length == 0),
        showPlaceholder:
          this.mahly_category == null || this.mahly_category.length == 0,
      }
    },
  },

  watch: {
    products: function (newVal) {
      this.selectedProducts = newVal.length
    },
    modalChange: function () {
      this.isSubmited = false
      this.clearMahlyCatList()
    },
  },
  created() {
    this.mahally_endpoint = initialData.getDatum('mahally_endpoint', '')
    this.headers = initialData.getDatum('headers', {})
  },
  methods: {
    // when clicking "لا يوجد تصنيف محلي" open modal
    handleMahlyCatSelect(value) {
      const selectedKeys = this.$refs.mahlyCategoriesList
      this.selectedObj = value
      selectedKeys.closeMenu()

      if (value.id === 0) {
        this.$emit('propose-mahly-category', this.product)
      }
    },
    // when clearing the menu, prevent value from becoming undefined
    handleMahlyCatInput(value) {
      const self = this
      const selectedKeys = this.$refs.mahlyCategoriesList
      if (value.length > 1) {
        selectedKeys.clear()
        selectedKeys.select(self.selectedObj)
      }
      if (value === undefined) {
        self.mahly_category = null
      }
    },
    onSearch(e) {
      const self = this
      if (e.length > 0) {
        self.placeholderText = ' '
      } else {
        self.placeholderText = 'ابحث عن تصنيف محلي المناسب ...'
      }
    },
    onOpenMahalySelect() {
      this.showCursor = true
    },
    clearMahlyCatList() {
      this.$refs.mahlyCategoriesList.clear()
    },
    saveField() {
      this.isSubmited = true
      if (this.mahly_category == null || this.mahly_category.length == 0) {
        console.warn('mahally category not selected!')
        return
      }
      // console.log(this.mahly_category);
      let data = {}
      data.select_all = this.isSelectAll
      data.products = this.getSelectedProductIds

      // console.log(this.getSelectedProductsWithName)
      if (this.mahly_category != null && this.mahly_category.length != 0) {
        data.mahly_category_id = this.mahly_category[0]?.id
        data.mahly_category_name = this.mahly_category[0]?.name
        data.un_select_products = this.getUnSelectedProductIds
      }

      if (this.hasFiltersSelected) {
        data = { ...data, ...this.getSelectedFilters }
        data.filtering = 1
      }

      if (this.mahly_category != null && this.mahly_category.length != 0) {
        this.loading = true
        window.showLoading()
        axios
          .post(this.mahally_endpoint, data, {
            headers: {
              ...this.headers,
              ...{
                'Content-Type': 'application/json',
                'X-API-KEY': window.token.key,
                's-store-id': initialData.getDatum('store_encode_id', null),
                // 'CF-Access-Client-Id':
                //   '9d24f17e73357e8e02941bc13f3e56e0.access',
                // 'CF-Access-Client-Secret':
                //   'ce7364ab08bc17d4c63d48d4dbd524ddbeb37ac38527eb3a96cd779ccfc54192',
              },
            },
          })
          .then(() => {
            swal({
              title: 'جاري ربط المنتجات بتصنيف محلي',
              text: '',
              type: 'success',
              showConfirmButton: false,
              timer: 2000,
            })
            $('#mahally-link-modal').modal('hide')
          })
          .catch((error) => {
            const message = error?.response?.data?.error?.fields
              ? Object.values(error?.response?.data?.error?.fields)[0][0]
              : error?.response?.data?.error.message ||
                error.message ||
                'حدث خطأ ما'
            swal({
              title: ' خطأ',
              text: message,
              type: 'error',
              showConfirmButton: false,
              timer: 2000,
            })
          })
          .finally(() => {
            this.loading = false
            window.hideLoading()
          })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
#mahally-link-modal .font-weight-bold {
  font-weight: 500;

  small {
    font-weight: normal;
  }
}
</style>
