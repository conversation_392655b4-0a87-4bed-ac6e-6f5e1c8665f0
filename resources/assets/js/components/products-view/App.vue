<template>
  <div>
    <section v-if="features.notify_quantity && closeToOut" class="alert-box alert-box--warning">
      <i class="sicon-warning"></i>
      <article>
        <p> لديك <span>({{ closeToOut }})</span> منتجات على وشك النفاد.
          <a class="tiffany" @click="appendStatus('close-to-out')">عرض المنتجات</a>
        </p>
      </article>
    </section>
    <template v-if="mahlyData.status && mahlyData.unlinked_products_alert.value > 0">
      <section v-if="getSelectedFilters?.mahly != 'unlinked'" class="alert-box alert-box--warning">
        <i class="sicon-warning"></i>
        <article class="d-flex">
          <p class="mr-5">
            {{ mahlyData.unlinked_products_alert.text }}
            <a class="m-0" @click="filterProductsMahally('unlinked')">عرض المنتجات غير المصنَّفة</a>
          </p>
        </article>
      </section>
      <section v-else class="alert-box alert-box--info mb-20 mt-20">
        <i class="sicon-info"></i>
        <article>
          <p>
            تم تصفية المنتجات غير المصنَّفة في محلي، يمكنك تحديد عدة منتجات لربطها بتصنيف محلي المناسب.</p>
        </article>
      </section>
    </template>



    <section v-if="countSelectedProducts" class="alert-box alert-box--warning">
      <i class="sicon-warning"></i>
      <article class="d-flex">
        <p v-if="isSelectAll" class="mr-5">

          <span class="">
            تم تحديد كل المنتجات ( <span id="products_count_header"> {{ countSelectedProducts }} </span> ) أي إجراء سوف
            يتم اتخاذه سوف يؤثر على المنتجات المحددة
          </span>
          <a @click="setSelectAll(!isSelectAll)" class="font-14 cursor-pointer">
            إلغاء التحديد
          </a>
        </p>
        <p v-else class="mr-5">
          <span class="">
            تم تحديد المنتجات الموجودة في هذه الصفحة فقط وعددها (<span
              id="products_count_header">{{ countSelectedProducts
              }}</span>)
          </span>
          <a class="font-14 cursor-pointer" @click="setSelectAll(true)">
            لتحديد كل المنتجات ({{ countTotalProducts }})
            <!-- <span v-if="hasSelectedFilters"><b>تنوية : </b>
              العدد بناءا على التصفية
            </span> -->
          </a>
        </p>
      </article>
    </section>



    <!-- <section v-if="mahlyData.status && mahlyData.hidden_products_alert.value > 0" class="alert-box alert-box--mahally">
      <i class="sicon-store"></i>
      <article class="d-flex">
        <p class="mr-5">
          {{ mahlyData.hidden_products_alert.text }}
          <a class="m-0" v-on:click="filterProductsMahally('hidden')">اضغط هنا</a>
        </p>
      </article>
    </section> -->

    <!-- Search Result Title-->
    <div v-if="keyword" class="row products-row">
      <div class="col-xs-12">
        <div id="div_row_search_info">
          <h5>
            عرض النتائج حسب كلمة البحث (
            {{ keyword }} )
            <button id="button_remove_search" type="button" class="btn btn-danger btn-xs rec-fvm" @click="cancelSearch">
              <i class="sicon-cancel"></i>&nbsp;
              إلغاء البحث
            </button>
          </h5>
        </div>
      </div>
    </div>

    <div class="view-control rec-list rec-list--horizontal rec-list--space-between rec-list--unwrap mb-20">
      <!-- Add New Product-->
      <add-product v-if="permissions.add_product" :product-types="productTypes" :add-new-product="addNewProduct"
        :plan="plan" :store-currency="storeCurrency" /> <!-- products.length -->
      <!-- Controls -->
      <controls :templates="exportTemplates" :primary="primary" :trans="trans" :switch-view="switchViewProducts"
        :check-all-products="checkAllProducts" :services-h-t-m-l="servicesHTML" :products-length="products.length"
        :products="products" :from-salla-app="fromSallaApp" :filters="filters" :permissions="permissions"
        :features="features"
        :isUsingTemplates="features?.product_export_templates"
        :isVendorAppEnabled="features?.vendor_app_feature"
        :plan="plan"
        @cancelFilter="cancelFilter"
        @reloadProducts="reloadProducts" />
    </div>
    <template-product @created="(tmpProduct) => addNewTmpProduct(tmpProduct, true)" />
    <!-- Products Block View Layout -->
    <div v-show="products.length >= 1" ref="wrap" class="tab-content wrap">
      <div class="tab-pane active">
        <div id="products_div" :class="['grid-block', 'main_content', viewType, reveal]">
          <product v-for="(product, index) in products" :key="'product_id' + product.id" :products="products"
            :product="product" :store-document-status="storeDocumentStatus" :product-types="productTypes"
            :valid-product-types="validProductTypes" :categories="categories" :mahly-data="mahlyData"
            :store-id="storeId" :product-class="productClass" :base-store-url="baseStoreUrl"
            :from-salla-app="fromSallaApp" :index="index" :permissions="permissions" :features="features"
            :languages="languages" :initiate-language-fields="initiateLanguageFields"
            :quantity-editable-types="quantity_editable_types" :store-have-mobile-app="storeHaveMobileApp" :plan="plan"
            :store-currency="storeCurrency" @removeProduct="removeProduct(index)" @deleteProduct="removeProduct"
            @propose-mahly-category="showMahlyPropose" />
        </div>
      </div>
      <mugen-scroll v-show="loading" :handler="loadMore" :should-handle="loading" class="align-center">
        <span class="loader loader--small mt-40"></span>
      </mugen-scroll>
      <options-and-quantity></options-and-quantity>
      <digital-card-options></digital-card-options>
      <notify-quantity-settings></notify-quantity-settings>
    </div>

    <!-- show this If there isn't any Products Or no searchable product  -->
    <div v-show="products.length === 0" class="rec-placeholder text-center mh-auto">
      <div class="rec-placeholder__icon">
        <i class="sicon-inbox"></i>
      </div>
      <h2 class="rec-placeholder__title">
        لا يوجد لديك منتجات حاليا
      </h2>
      <p class="rec-placeholder__desc">ابدأ رحلتك التجارية بإضافة اول منتج بالنقر على زر إضافة منتج جديد</p>
      <button class="btn btn-tiffany" @click="showTemplateProductModal">استخدم نموذج جاهز</button>
    </div>
    <!-- Add Category From Product -->
    <add-category :categories="categories" :languages="languages"></add-category>

    <!-- propose a mahly category -->
    <propose-mahly-category :product="productSelected" />
    <export-templates-modal :template="exportTemplate" :templates="exportTemplates" />
    <export-group-product-modal :templates="exportTemplates" />

  </div>
</template>

<script>
import initialData from "../../services/ProductService";
import Product from './components/Product';
import Controls from './components/Controls';
import AddProduct from './components/AddProduct';
import AddCategory from './components/addCategory';
import ProposeMahlyCategory from './components/ProposeMahlyCategory';
import MugenScroll from 'vue-mugen-scroll'
import Filters from './components/filters';
import OptionsAndQuantity from './../quantity-management/App';
import DigitalCardOptions from './../digital-card-management/App';
import Http from "../../utils/http";
import Modal from './components/layout/Modal';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import NotifyQuantitySettings from "../notify-quantity-settings/App";
import TemplateProduct from "./components/TemplateProduct/TemplateProductOptions"
import ExportTemplatesModal from './components/ExportTemplateModal';
import ExportGroupProductModal from './components/ExportGroupProductModal';
import { mapGetters, mapMutations, mapActions } from 'vuex'

export default {
  components: {
    'product': Product,
    'template-product': TemplateProduct,
    'controls': Controls,
    'add-product': AddProduct,
    'filters': Filters,
    'options-and-quantity': OptionsAndQuantity,
    'digital-card-options': DigitalCardOptions,
    'notify-quantity-settings': NotifyQuantitySettings,
    'mugen-scroll': MugenScroll,
    'modal': Modal,
    'v-treeselect': Treeselect,
    'add-category': AddCategory,
    'propose-mahly-category': ProposeMahlyCategory,
    'export-templates-modal': ExportTemplatesModal,
    'export-group-product-modal': ExportGroupProductModal,
  },
  data() {
    return {
      addToMain: false,
      storeId: null,
      viewType: "grid", // Products View Type
      fromSallaApp: false,
      errors: [],
      products: [],
      productTypes: [],
      validProductTypes: [],
      categories: [],
      baseStoreUrl: baseStoreUrl,
      servicesHTML: '',
      keyword: null, // search data
      closeToOut: 0,
      loaderNew: false,
      loading: true,
      // variables related to loadmore
      page: 1,
      status: 'all',
      category_id: null, // i think it is related to filteration
      type: null, // i think it is related to filteration
      newProductType: 'product',
      productSelected: null,
      quantity_editable_types: [],
      // Classes
      primary: 'primary',
      trans: 'trans',
      reveal: 'reveal',
      productClass: 'product-box',
      features: [],
      permissions: [],
      languages: [],
      storeDocumentStatus: null,
      plan: null,
      storeCurrency: null,
      exportTemplate: {},
      exportTemplates: [],
      storeSubscribe: null
    };
  },
  computed: {
    ...mapGetters({
      'countSelectedProducts': 'selectAll/countSelectedProducts',
      'countTotalProducts': 'selectAll/countTotalProducts',
      'isSelectAll': 'selectAll/isSelectAll',
      'getSelectedFilters': 'filters/getSelectedFilters',
      'hasSelectedFilters': 'filters/hasSelectedFilters',
    }),
  },
  watch: {
    products: function (newVal) {
      if (Array.isArray(newVal) && newVal.length && this.isSelectAll)
        this.checkBoxAllProducts(true)
    },
  },
  mounted() {
    if (this.features?.product_export_templates) {
      this.getExportTemplates()
    }
    Salla.event.addEventListener('products::submit-filters', this.submitFilters);
    Salla.event.addEventListener('products::reload-categories-list', this.reloadCategories);
    Salla.event.addEventListener('products::product-cloned', this.pushClonedProduct);
    Salla.event.addEventListener('product-details::update-quantity', this.syncProductQuantity)
    Salla.event.addEventListener('product-details::update-notify-quantity-settings', this.syncProductNotifyQuantitySetting)
    Salla.event.addEventListener('product::update-group-quantity', this.updateGroupQuantity)
    Salla.event.addEventListener('product-details::update-status', this.updateStatus)
    Salla.event.addEventListener('export-templates::export-modal', this.showExportModal)

  },
  beforeDestroy() {
    document.removeEventListener('product-details::update-quantity', this.syncProductQuantity);
    document.removeEventListener('product-details::update-notify-quantity-settings', this.syncProductNotifyQuantitySetting);
    document.removeEventListener('products::submit-filters', this.submitFilters);
    document.removeEventListener('products::reload-categories-list', this.reloadCategories);
    document.removeEventListener('products::product-cloned', this.pushClonedProduct);
    document.removeEventListener('product::update-group-quantity', this.updateGroupQuantity)
    document.removeEventListener('product-details::update-status', this.updateStatus)
    document.removeEventListener('export-templates::export-modal', this.showExportModal)
    this.storeSubscribe()
  },
  created() {
    this.viewType = initialData.getDatum('viewType', 'grid');
    this.productTypes = initialData.getDatum('productTypes', []);
    this.validProductTypes = initialData.getDatum('validProductTypes', []);
    this.storeId = initialData.getDatum('storeId', null);
    this.products = initialData.getDatum('products', []);
    this.categories = initialData.getDatum('categories', []);
    this.servicesHTML = initialData.getDatum('servicesHTML', '');
    this.filters = initialData.getDatum('filters', []) || [];
    this.keyword = initialData.getDatum('keyword', null) || null;
    this.loading = initialData.getDatum('hasMore', false) || false;
    this.closeToOut = initialData.getDatum('closeToOut', 0);
    this.permissions = initialData.getDatum('permissions', []);
    this.mahlyData = initialData.getDatum('mahly_data', {});
    this.wholesaleData = initialData.getDatum('wholesale_data', {});

    this.setMahallyCategories(this?.mahlyData?.categories);
    this.setMahally(this.mahlyData);
    // this.setWholesaleCategorsies(this?.wholesaleData?.categories);
    // this.setWholesale(this.wholesaleData);

    this.setCountTotalProducts(initialData.getDatum('total_count_products', this.products.length));

    this.storeSubscribe = this.$store.subscribe(({ type, payload }) => {

      let isSelectAll = type === 'selectAll/setSelectAll';

      if (isSelectAll) {
        this.checkBoxAllProducts(payload)
      }

    })

    let defaultLang = {
      "id": 1,
      "name": "العربية",
      "rtl": 1,
      "iso_code": "ar",
      "country_code": "sa",
      "flag": "https://assets.salla.sa/images/flags/ar.svg",
      "status": true,
      "auto_translate": false,
      "order": 0
    };
    this.languages = initialData.getDatum('languages', {
      "supported": [defaultLang],
      "current": defaultLang,
      "default": defaultLang,
      "iso_code": "ar",
      "feature": false
    });
    this.features = initialData.getDatum('features', [])
    this.quantity_editable_types = initialData.getDatum('quantityEditableTypes', [])
    this.fromSallaApp = initialData.getDatum('fromSallaApp', false);
    this.storeDocumentStatus = initialData.getDatum('storeDocumentStatus');
    this.plan = initialData.getDatum('plan');
    this.storeCurrency = initialData.getDatum('storeCurrency', 'ر.س');
    this.storeHaveMobileApp = initialData.getDatum('storeHaveMobileApp');
    this.setViewTypeValues(this.viewType, true);

    this.initialClipboard();
    this.fetchData();
  },
  methods: {

    checkBoxAllProducts(state) {
      this.products.forEach(ele => {
        ele.checked = state;
        ele.active = state ? 'active' : null
      });
    },
    saveModal() { // Methode fires when the save modal button pressed

    },
    async requestData(url) {
      const config = {
        'Content-Type': 'application/json',
        'X-API-KEY': window.token.key,
        's-store-id': store.id,
        ...(window.headers || {}),
      };
      axios.defaults.headers.common = { ...axios.defaults.headers.common, ...config };
      return await axios.get(url)
    },
    async getExportTemplates() {
      this.requestData(window.apiUrl + '/exports/submenu')
        .then(res => {
          this.exportTemplates = res.data.data
        })
        .catch((error) => {
          if (error.response && error.response.status === 401) {
            getFreshToken(true);
          }
          return laravel.errors.renderValidationErrorBag(error.response?.data.error.fields);
        });
    },
    showExportModal(e) {
      this.exportTemplate = e.detail.template
      $('#export-templates-modal').modal('show')
    },
    setViewTypeValues(type, init) {
      switch (type) {
        case ('grid'):
          this.primary = 'primary';
          this.trans = 'trans';
          this.reveal = init ? '' : 'conceal';
          setTimeout(() => {
            this.viewType = 'grid';
            this.reveal = init ? '' : 'reveal';
          }, 350)
          break;
        case ('list'):
          this.primary = 'trans';
          this.trans = 'primary';
          this.reveal = init ? '' : 'conceal';
          setTimeout(() => {
            this.viewType = 'list';
            this.reveal = init ? '' : 'reveal';
          }, 350)
          break;
        default:
          return
      }
    },
    switchViewProducts(type) { // Method To Switch Between Grid And List View
      this.setViewTypeValues(type);
      Http.post("products/layout", {
        view_type: type,
        _token: _token
      },
        ({ data }) => {
        },
        ({ error }) => {
        }
      );
    },

    // remove element from products array after DELETE request success
    removeProduct(index) {
      this.products.splice(index, 1);
    },

    showTemplateProductModal() {
      $('#create_template_product').modal('show');
    },

    addNewProduct(selectedType, modalOpened = false) {
      if (selectedType.value === 'template' && modalOpened == false) {
        this.showTemplateProductModal();
      } else {
        // Method To Add New Products
        this.loaderNew = true;
        setTimeout(() => {
          this.loaderNew = false;
          this.products.unshift(
            {
              // id: 0,
              id: -Math.floor((Math.random() * 1000000000) + 1),
              name: '',
              checked: selectedType.value === 'template' ? true : false,
              pinned: 0,
              active: null,
              ratings: null,
              type: selectedType.value,
              can_change_quantity: selectedType.editable_quantity,
              image: `${assetsBaseUrl}cp/assets/images/placeholder.png`,
              class: 'product-box product-box-new',
              productsType: this.productTypes,
              categories: [],
              notify_quantity: '',
              quantity: null, // initial quantity for new products
              updated_by_import: false,
              status: null,
              show_in_web: true,
              show_in_app: true,
              unlimited_quantity: false,
              isNewTranslation: true,
              translations: {
                [this.languages.iso_code]: {
                  name: '',
                },
              },
              advancable: false
            }
          )
          setTimeout(() => {
            $('#products_div .product-box:first .tooltip-toggle').addClass('visible');
            this.initiateLanguageFields();
          }, 100)
        }, 300);
      }
    },
    addNewTmpProduct(tmpProduct) {
      // Method To Add New Template Products
      let digitalAndServices = 'digital'
      let quantityForDgProduct = tmpProduct.type === digitalAndServices ? null : tmpProduct.quantity
      this.loaderNew = true;
      setTimeout(() => {
        this.loaderNew = false;
        this.products.unshift(
          {
            id: -Math.floor((Math.random() * 1000000000) + 1),
            name: `${tmpProduct.name} template`,
            checked: true,
            price: tmpProduct.price,
            pinned: 0,
            active: null,
            ratings: null,
            type: tmpProduct.type,
            can_change_quantity: tmpProduct.can_change_quantity,
            image: tmpProduct.image,
            productsType: tmpProduct.type,
            categories: [],
            notify_quantity: '',
            quantity: quantityForDgProduct,
            updated_by_import: false,
            status: null,
            unlimited_quantity: false,
            isNewTranslation: true,
            translations: {
              [this.languages.iso_code]: {
                name: tmpProduct.name,
              },
            },
            advancable: false
          }
        )
        setTimeout(() => {
          $('#products_div .product-box:first .tooltip-toggle').addClass('visible');
          $('#products_div .product-box:first').addClass('product-box--template');
          this.initiateLanguageFields();
        }, 100)
      }, 300);
    },
    checkAllProducts(val) { // Method To Check All The Products

      this.setSelectedProducts(val.target.checked ? this.products : [])
      this.checkBoxAllProducts(val.target.checked);
    },
    initiateLanguageFields() {
      setTimeout(() => {
        if (window.initFieldLang) {
          $('.rec-ls-field:not(.field-lang-initiated)')
            .each((index, fieldInput) => window.initFieldLang($(fieldInput)));
        }
      }, 100);
    },
    loadMore(prop) {
      this.page++;
      let data = {
        page: this.page,
      }

      let selectedLang = $('.product-box:first-of-type .rec-lsc input[type="radio"]:checked').val();

      // append the filters data
      if (this.filters && this.filters.filtering) {
        data = this.appendFilters(data);
      }

      // append search by keyword
      if (this.keyword) {
        data = this.appendSearchKey(data);
      }

      // if there is option_name to the query string
      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.has('option_name')) {
        data.option_name = new URLSearchParams(urlParams).get('option_name')
      }

      let resp

      $.ajax({
        url: "/products/",
        dataType: 'json',
        data: data,
        async: true, //changed async to "true" because "false" was causing issues with loader not showing
        success: (response) => {
          resp = response
        },
        complete: () => {
          // not clearing products until the request is completed
          if (prop === 'reloadProducts') {
            this.products = [];
          }
          // running after request success
          if (resp.success) {
            this.products.push(...(resp.data.products));
            if (resp.data.filters) {
              this.filters.push(resp.data.filters);
              Salla.event.createAndDispatch('products::set-selected-filters', {
                'selected_filters': resp.data.filters
              })

              this.setCountTotalProducts(resp.data.total_count_products);
            }
            this.loading = resp.data.hasMore;
            this.initiateLanguageFields();
            setTimeout(() => {
              $(`.product-box:nth-of-type(${this.products.length - 19}) .rec-lsc input[value=${selectedLang}]`).siblings('label').click();
              $(`.product-box:nth-of-type(${this.products.length - 19}) .rec-lsc`).removeClass('visible')
            }, 500);
          }
        }
      });
    },

    /**
     * append Filters data
     *
     * @param data
     * @returns {*}
     */
    appendFilters(data) {
      data.brands = this.filters.brands;
      data.branches = this.filters.branches;
      data.status = this.filters.status;
      data.categories = this.filters.categories;
      data.types = this.filters.types;
      data.mahly = this.filters.mahly;
      data.wholesale = this.filters.wholesale;
      data.filtering = 1
      return data;
    },
    /**
     * append Search Key
     *
     * @param data
     * @returns {*}
     */
    appendSearchKey(data) {
      data.keyword = this.keyword;
      return data;
    },

    /**
     * cancel product search by keyword
     */
    cancelSearch() {
      $('#searchbox').val('');
      this.keyword = null;
      this.reloadProducts();

    },
    initialClipboard() {
      let clipboard = new Clipboard('a.copy-product-link');
      clipboard.on('success', function (e) {
        e.clearSelection();
        laravel.alert('تم نسخ رابط المنتج', 'success')
      });
    },
    appendStatus(status) {
      this.page = 0;
      this.filters.filtering = 1;
      this.filters.status = [status];
      this.products = [];
      this.loadMore();
    },
    /**
     * Cancel Products filter
     * then reload all products
     */
    cancelFilter() {
      this.filters = [];
      Salla.event.createAndDispatch('products::reset-filters');
      this.reloadProducts();
      this.resetSelectAll();
      this.setSelectedFilters([])

    },

    /**
     * Reload products
     */
    reloadProducts() {
      this.page = 0;
      this.loadMore('reloadProducts')
    },
    submitFilters(e) {
      this.filters = [];
      this.filters.filtering = 1;
      this.filters.status = e.detail.status;
      this.filters.types = e.detail.types;
      this.filters.brands = e.detail.brands;
      this.filters.branches = e.detail.branches;
      this.filters.categories = e.detail.categories;
      this.filters.mahly = e.detail.mahly;
      this.filters.wholesale = e.detail.wholesale;
      this.reloadProducts();
    },


    /**
     * reload categories lists after a new category is added,
     */
    reloadCategories(e) {
      this.categories = e.detail.categories;
    },


    /**
     * push the new cloned product to this.products array instead of reloading the page after clone product
     */
    pushClonedProduct(e) {
      this.products.unshift(e.detail.product);
    },

    syncProductQuantity(e) {
      // find product where id = data.detail.productId
      let product = this.products.find(productObj => productObj.id === parseInt(e.detail.productId));
      product.quantity = e.detail.quantity;
      product.unlimited_quantity = e.detail.unlimited_quantity;
      product.can_change_quantity = e.detail.can_change_quantity;
    },

    syncProductNotifyQuantitySetting(e) {
      // find product where id = data.detail.productId
      let product = this.products.find(productObj => productObj.id === parseInt(e.detail.productId));
      product.notify_quantity = e.detail.notify_quantity;
    },

    updateGroupQuantity(e) {
      e.detail.parents.forEach(ele => {
        // find product where id = data.detail.productId
        let product = this.products.find(productObj => productObj.id === parseInt(ele.id));
        product.quantity = ele.quantity;
      });
    },

    updateStatus(e) {
      let product = this.products.find(productObj => productObj.id === parseInt(e.detail.productId));
      product.status = e.detail.status;
      product.show_in_web = e.detail.show_in_web;
      product.show_in_app = e.detail.show_in_app;

      // if (this.mahlyData.status) {
      //   product.show_in_mahly_app = e.detail.show_in_mahly_app;
      // }
    },

    filterProductsMahally(prop) {
      Salla.event.createAndDispatch('products::fetch-filters', { mahally: true, prop });
      Salla.event.createAndDispatch('products::set-mahally-filter', { mahally: true, prop });
      Salla.event.createAndDispatch('products::submit-filters', { mahly: prop });
      this.setSelectedFilters({ mahly: prop })
    },

    showMahlyPropose(product) {
      this.productSelected = product
      $('#propose-mahly-category').modal('show');
    },
    ...mapMutations('wholesaleMarketplace', ['setWholesaleCategories', 'setWholesale']),
    ...mapMutations('mahally', ['setMahallyCategories', 'setMahally']),
    ...mapMutations('selectAll', ['setCountTotalProducts']),
    ...mapMutations('selectAll', ['resetSelectAll']),
    ...mapMutations('selectAll', ['setSelectAll']),
    ...mapMutations('selectAll', ['setSelectedProducts']),
    ...mapMutations('filters', ['setSelectedFilters']),
    ...mapMutations('filters', ['resetFiltersAction']),
    ...mapActions('wholesaleMarketplace', ['fetchData']),

  },
}
</script>
