import Vue from 'vue'
import App from './App'
import * as Sen<PERSON> from "@sentry/browser";
import { Integrations } from "@sentry/tracing";
import store from './store'


// Sentry.init({
//     Vue,
//     dsn: "https://<EMAIL>/21",
//     autoSessionTracking: true,
//     integrations: [
//         new Integrations.BrowserTracing(),
//     ]
// });

new Vue({
    store,
    render: h => h(App),
}).$mount('#products_list_wrapper')
