<template>
  <div>
    <draggable tag="ul" v-bind="dragOptions" :list="pages" class="rec-list rec-list--vertical rec-list--draggable mb-0">
      <li v-for="(page, index) in pages" :key="index">
        <i class="sicon-menu move-handler"></i>
        <span class="text ml-20">{{ page.title }}</span>
      </li>
    </draggable>
  </div>
</template>

<script>

import draggable from "vuedraggable";

export default {
  props: ['pages'],
  
  data() {
    return {
      dragOptions: {
        handle: ".move-handler",
        easing: "cubic-bezier(0.86, 0, 0.07, 1)",
        animation: 500,
      },
    }
  },

  components: {
    draggable
  }
}
</script>
