<template>
  <div id="sort_informative_pages_modal" class="modal fade modal-backup">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h6 class="modal-title">
            <i class="sicon-list-reorder mr-10 v-align"></i>ترتيب عرض الصفحات التعريفية
          </h6>
        </div>
        <div class="modal-body">
          <div v-if="loading && !pages.length" class="align-center">
            <span class="loader loader--small"></span>
          </div>
          <div class="rec-placeholder text-center mh-auto" v-if="!loading && !pages.length">
              <div class="rec-placeholder__icon op-1">
                <i class="sicon-file-off font-60"></i>
              </div>
              <h2 class="rec-placeholder__title font-18">
                لا يوجد لديك صفحات تعريفية حاليا
              </h2>
              <p class="rec-placeholder__desc mb-0">
                قم بإضافة صفحات تعريفية جديدة وبالإمكان ترتيب عرضها من هنا
              </p>
          </div>
          <div v-if="pages.length">
            <article class="rec-article mb-20">
              <p>قم بالسحب والإفلات لترتيب عرض الصفحات التعريفية في أسفل المتجر</p>
            </article>
            <sorting-informative-pages :pages="pages"/>
          </div>
        </div>
        <div class="modal-footer align-left">
          <button class="btn btn-info btn-save" type="button" @click="save" v-if="pages.length" data-inline-loader>
              {{btnText}}
          </button>
          <button class="btn btn-info btn-close" type="button" data-dismiss="modal" >إلغاء</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import axios from "axios";

export default {
  data() {
    return {
      pages: [],
      btnText: 'حفظ التعديلات',
      loading: false
    }
  },

  methods: {
    async save (event) {
      showLoading(event)
      await axios.post('/pages/sort', {pages: this.pages})
      .then(response => {
        this.btnText = 'تم الحفظ';
        hideLoading();
        $('#sort_informative_pages_modal').modal('hide');
        setTimeout ( () => {
          loadPages();
          this.btnText = 'حفظ التعديلات';
        }, 500)
      })
      .catch(erreo => {
        hideLoading();
      })
    }
  },

  mounted() {
    const self = this;
    $('#sort_informative_pages_modal').on('show.bs.modal', function () {
      self.loading = true;
      axios.post('/pages/get_pages')
      .then(response => {
        const data = response.data.data.pages;
        self.pages = data;
        self.loading = false;
      })
      .catch(error => {})
    })
  },
}
</script>

