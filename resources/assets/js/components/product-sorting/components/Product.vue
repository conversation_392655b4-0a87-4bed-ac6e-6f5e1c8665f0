<template>
    <li class="sort-product-entry" :class="{'sticky': product.sticky, 'dimmed': !product.visible}" :data-id="product.sort" :data-sort="product.sort" :data-product-id="product.id" :key="product.id">
        <span v-show="product.sticky" class="sicon-pin sticky"></span>
        <div class="sort-thumb">
            <span class="move-handler">
                <svg width="512px" height="512px" viewBox="0 0 512 512">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="arrows" transform="translate(-0.001949, -0.001949)" fill="#000000" fill-rule="nonzero">
                            <path d="M507.459112,245.030606 L434.176554,171.749051 C428.117768,165.688261 418.291653,165.688261 412.233869,171.749051 C406.174081,177.808839 406.174081,187.633952 412.233869,193.691736 L459.026896,240.484763 L271.518133,240.484763 L271.518133,52.9770021 L318.31116,99.7700292 C321.340052,102.799923 325.310776,104.31487 329.282503,104.31487 C342.980149,104.31487 350.057917,87.6304139 340.253845,77.8263419 L266.97229,4.54378439 C260.915508,-1.51299794 251.088391,-1.51500184 245.029604,4.54378439 L171.748049,77.8263419 C165.688261,83.88613 165.688261,93.710241 171.748049,99.7690272 C177.806835,105.829817 187.63295,105.829817 193.690734,99.7690272 L240.483761,52.9760002 L240.483761,240.484763 L52.9770021,240.484763 L99.7700292,193.691736 C105.829817,187.63295 105.829817,177.807837 99.7700292,171.749051 C93.7132469,165.689263 83.887132,165.687259 77.8273438,171.749051 L4.54478634,245.030606 C-1.51199599,251.087389 -1.51399989,260.914506 4.54478634,266.973292 L77.8273438,340.254847 C83.8841262,346.313634 93.711243,346.315637 99.7700292,340.254847 C105.829817,334.195059 105.829817,324.369946 99.7700292,318.312162 L52.9770021,271.519135 L240.485765,271.519135 L240.485765,459.027898 L193.692738,412.234871 C187.634954,406.175083 177.808839,406.174081 171.750053,412.234871 C165.690265,418.294659 165.690265,428.11877 171.750053,434.177556 L245.031608,507.460114 C251.088391,513.516896 260.915508,513.5189 266.974294,507.460114 L340.255849,434.177556 C346.315637,428.117768 346.315637,418.293657 340.255849,412.234871 C334.197063,406.174081 324.370948,406.174081 318.313164,412.234871 L271.520137,459.027898 L271.520137,271.518133 L459.0289,271.518133 L412.235873,318.31116 C406.176085,324.369946 406.176085,334.195059 412.235873,340.253845 C418.293657,346.313634 428.120774,346.314636 434.178558,340.253845 L507.461116,266.97229 C513.515894,260.917511 513.517898,251.089393 507.459112,245.030606 Z"></path>
                        </g>
                    </g>
                </svg>
            </span>
            <img :src="product.url ? product.url : this.defaultThumb" :alt="product.name"/>
        </div>
        <div class="sort-content">
            <div class="sort-title">
                <i class="sicon-type-square"></i>
                <span class="name">{{product.name}}</span>
            </div>
            <ul class="sort-meta">
                <li>
                    الكمية:
                    {{product.qty}}
                </li>
                <li v-show="product.salable" class="available">متاح</li>
                <li v-show="!product.salable" class="unavailable">غير متاح</li>
            </ul>
        </div>
    </li>
</template>

<script>
    export default {
        name: "Product",
        props: ['product'],
        data() {
            return {
                defaultThumb: `${assetsBaseUrl}cp/assets/images/placeholder.png`
            }
        },
    }
</script>
