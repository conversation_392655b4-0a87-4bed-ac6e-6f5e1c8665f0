<template>
  <select
        class="v-dropdown-container selectpicker sort-select"
        v-model="selectedValue"
        v-show="options.length > 0"
        title="اختر التصنيف...."
        liveSearchPlaceholder="ابحث عن تصنيف محدد..."
        noneSelectedText="اختر التصنيف...."
        noneResultsText="لا يوجد تصنيف بنفس الاسم"
        data-live-search="true"
      >
        <template v-for="category in categories" :kay="category.value">
          <option
            class="parent"
            v-text="category.text"
            :value="category.value"
            :data-tokens="category.text"
          ></option>
          <option
            v-if="category.childrenRecursive.length > 0"
            v-for="categorySub in category.childrenRecursive"
            v-text="categorySub.text"
            :value="categorySub.value"
            class="child"
            :kay="categorySub.value"
            :data-tokens="categorySub.text">
          </option>
        </template>
      </select>
</template>

<script>
export default {
name: "CategorySelect",
  data() {
    return {
      selectedValue: null,
      categories:[],
    };
  },
  computed: {
    options() {
      return this.categories;
    }
  },
  methods: {
    async getCategories() {
      await axios.get('/products/sorting/categories')
          .then(({ data }) => {

            this.categories = data.data;

            $(this.$el).on('changed.bs.select', async () => {
              showLoading();
              this.$root.$emit('resetPage', {isTrue: true});
              const {data = [], status = 200} = await axios.get(`/products/sorting/categories/${this.selectedValue}/product`);
              this.$root.$emit("productCategory", [data, this.selectedValue]);
              hideLoading();
            });
          }).catch((data) => {

          });
    },
  },
  mounted: function() {
    $(this.$el).selectpicker();
    this.getCategories();
  },
  beforeDestroy: function() {
    $(this.$el)
        .off()
        .selectpicker('destroy')
  },
  updated() {
    $(this.$el).selectpicker();
    $(this.$el).selectpicker('refresh');
    $(this.$el).find('.sort-select').selectpicker('refresh');
  },
}
</script>
