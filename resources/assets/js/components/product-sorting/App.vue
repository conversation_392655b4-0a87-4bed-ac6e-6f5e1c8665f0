<template>
    <main>
        <a href="#" id="scroll_top"><i class="sicon-arrow-up"></i></a>
        <div id="sort-mode">
            <div id="sort_controls">
                <div v-if="isMobileApp" class="row">
                    <div class="col-xs-12 main-btn">
                        <a href="https://s.salla.sa/products" style="line-height: 46px;" class="btn-products-back">
                            <i class="sicon-caret-right-circle right-back-arrow"></i>
                            العودة إلى المنتجات
                        </a>
                    </div>
                </div>
              <div class="form-group">
                <div class="row">
                  <div class="col-md-10 col-xs-12">
                    <div class="input-group">
                      <span class="input-group-addon">
                        <i class="sicon-folder"></i>
                      </span>
                      <category-select />
                    </div>
                  </div>
                  <div class="col-md-2 col-xs-12" v-show="products.length > 0">
                    <button v-on:click="saveSorting($event)" class="btn btn-xlg btn-tiffany sort-save" data-inline-loader>حفظ الترتيب</button>
                  </div>
                </div>
              </div>
              </div>

          <div id="products_container"  v-if="products.length > 0">
            <div class="page-indicator" v-show="this.pagination.totalPages">
                <span class="page" v-for="page in pagination.totalPages" :key="page">
                    <small  v-text="page"></small>
                </span>
            </div>
            <draggable
                    v-model="products"
                    v-bind="dragOptions"
                    @start="onStart"
                    @end="onEnd"
                  >
                  <product v-for="(product, key) in products" v-bind:key="products.id" :product="product"/>
            </draggable>
            <infinite-loading
              :identifier="infiniteId"
              forceUseInfiniteWrapper="product-listing"
              @infinite="infiniteHandler">
              <div slot="no-more"></div>
              <div slot="no-results"></div>
            </infinite-loading>
          </div>
          <div v-else-if="category_id > 0" id="intro">
              <p class="text-muted text-center">لا توجد منتجات في هذا التصنيف</p>
              <img class="desktop" :src="`${assetBase}cp/assets/images/sortable.png`" alt="Sortable" />
              <img class="mobile" :src="`${assetBase}cp/assets/images/sortable-mobile.png`" alt="Sortable" />
          </div>
          <div v-else id="intro">
              <p class="text-muted text-center">اختر احد التصنيفات و قم بترتيب المنتجات عن طريق السحب و الافلات بسهولة</p>
              <img class="desktop" :src="`${assetBase}cp/assets/images/sortable.png`" alt="Sortable" />
              <img class="mobile" :src="`${assetBase}cp/assets/images/sortable-mobile.png`" alt="Sortable" />
          </div>
        </div>
    </main>
</template>

<script>
    import draggable from 'vuedraggable'
    import Product from './components/Product'
    import CategorySelect from './components/CategorySelect'
    export default {
        name: 'sorting',
        data() {
            return {
                products:  [],
                feature: [],
                drag: false,
                pagination: [],
                category_id: 0,
                infiniteId: +new Date(),
                showSaveButton: false,
                sorted: [],
                page: 2,
                elementHeight: 0,
                easing: "cubic-bezier(1, 0, 0, 1)",
                group:'sorting',
                introImage: 'sortable.png',
                assetBase: assetsBaseUrl,
                dragOptions: {
                    class: "product-listing",
                    filter: ".sticky",
                    handle: window.innerWidth < 767 ? ".move-handler" : null,
                    easing: "cubic-bezier(0.86, 0, 0.07, 1)",
                    tag: "ul",
                    animation: 500,
                    group: "product",
                    disabled: false,
                    ghostClass: "ghost"
                }
            }
        },
        components: {
            draggable,
            product: Product,
            'category-select':CategorySelect
        },
        methods: {
            getTotalPages: function () {
                this.pages = Math.floor(this.products.length / this.pagination.perPage);
            },
            onStart: function (evt) {
                this.drag = true;
            },
            onEnd: function (evt) {
                this.drag = true;
            },
            infiniteHandler($state) {
                let url = this.getNextPage();
                axios.get(url).then(({ data }) => {
                    if (data.data.length > 0) {
                        _.orderBy(this.products.push(...data.data),['sticky','created_at'],'desc')
                        this.pagination = data.pagination;
                        this.page = this.page + 1;
                        this.feature = data.feature;
                        $state.loaded();
                    } else {
                        $state.complete();
                    }
                });
            },
            getNextPage: function () {
                return `/products/sorting/categories/${this.category_id}/product?page=${this.page}`;
            },
            saveSorting: function (event) {

                if(! this.feature.isAccessible) {
                  swal({
                    title: this.feature.message,
                    type: 'error',
                    showConfirmButton: false,
                    timer: 1500
                  });

                    return true;
                }
                showLoading(event);
                axios.post('/products/sorting/index', {
                    sorting: this.getSortedProducts()
                }).then(({ data }) => {
                    hideLoading();
                   swal({
                     title: data.message,
                     type: 'success',
                     showConfirmButton: false,
                     timer: 1500
                   })
                }).catch(function () {
                    hideLoading();
                  swal({
                    title: 'عذراًُ حصل خطأ نرجو المحاولة مره أخرى',
                    type: 'error',
                    showConfirmButton: false,
                    timer: 1500
                  })
                });
            },
            getSortedProducts: function () {
                return this.products.map(function (item, index) {
                    return {product: item.id, sort: index}
                });
            }
        },
        mounted() {
            if (this.products.length) {
                this.getTotalPages();
            }

          this.$root.$on('resetPage', ({ isTrue }) => {
              this.page = isTrue ? 2 : this.page;
          });

          this.$root.$on('productCategory', (data) => {
            this.products = data[0].data;
            this.pagination = data[0].pagination;
            this.feature = data[0].feature;
            this.category_id = data[1];
            this.showSaveButton = false;
            this.infiniteId += 1;
            hideLoading();
          });
        },
		computed: {
        	isMobileApp: () => {
            if (typeof dataLayer !== 'undefined' && dataLayer.length) {
              return dataLayer[0].page.mobileApp;
            }
			}
		},
    }
</script>

