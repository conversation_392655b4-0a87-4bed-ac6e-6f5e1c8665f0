import Vue from 'vue'
import App from './App.vue'
import store from './store'
import _ from 'lodash'
import InfiniteLoading from 'vue-infinite-loading';

import Toasted from 'vue-toasted';


Vue.use(Toasted, {
  iconPack: 'sallaicons'
})
Vue.use(InfiniteLoading, {
  system: {
    throttleLimit: 150,
  },
})
window._ = _;
Vue.config.productionTip = false;

new Vue({
    store,
    render: h => h(App),
}).$mount('#sorting')
