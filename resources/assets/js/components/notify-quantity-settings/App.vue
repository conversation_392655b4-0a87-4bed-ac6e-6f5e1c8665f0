<template>
    <div v-if="showModal" id="modal_notify_quantity_settings" :class="{'custom-modal': true, 'fade': showModal}">
        <div :class="{'modal-dialog': true, 'show': showDialog}" >
            <div class="modal-content">
                <!----- modal Header -------->
                <div class="modal-header bg-info">
                    <button type="button" class="close" @click="closeModal">&times;</button>
                    <h6 class="modal-title">تنبيهات المنتج
                    - {{product !== null ? product.name : null}}</h6>
                </div>
                <!----- modal Body -------->
                <div class="modal-body">
                    <form class="mb-0">

                      <!----- Notify Quantity Field -------->
                      <div class="row product-data-row">
                        <div class="col-xs-12">
                          <div :class="{'form-group': true, 'has-error': errors.notify_quantity}">
                            <label class="mb-0">نبهني عند وصول كمية المنتج إلى</label>
                            <p class="text-muted text-size-small">سيتم إشعارك قبل انتهاء كمية المنتج</p>
                            <div class="input-group">
                              <span class="input-group-addon input-group-addon-small"> <i class="sicon-luggage-cart"></i> </span>
                              <input id="quantity_lowset_value" type="text" class="form-control _parseArabicNumbers"
                                     placeholder="0"
                                     autocomplete="off"
                                     @keyup="checkEnglishNumbers($event, 'notify_quantity')"
                                     v-model="product.notify_quantity">
                              <span class="input-group-addon">قطعة</span>
                            </div>
                            <!-- client and server side validation -->
                            <form-error v-if="errors.notify_quantity" class="help-block">
                              {{ errors.notify_quantity ? errors.notify_quantity.toString() : '' }}
                            </form-error>
                          </div>
                        </div>
                      </div>


                      <!----- Minimum Quantity Field -------->
                      <div class="row product-data-row">
                        <div class="col-xs-12">
                          <div :class="{'form-group': true, 'has-error': errors.minimum_notify_quantity}">
                            <label class="mb-0">تنبيه العملاء المشتركين في "أعلمني عند التوفر" عند توفر كمية للمنتج أكبر من</label>
                            <p class="text-muted text-size-small">لتجنب إرسال الإشعارات للعملاء عند توفر كمية بسيطة من المنتج، يمكنك تحديد الحد الأدنى المطلوب توفره للبدء في إرسال الإشعارات للعملاء، القيمة الافتراضية 15</p>
                            <div class="input-group">
                              <span class="input-group-addon input-group-addon-small"> <i class="sicon-megaphone"></i> </span>
                              <input id="quantity_notification_value" type="text" class="form-control _parseArabicNumbers"
                                     placeholder="15"
                                     autocomplete="off"
                                     v-model="product.minimum_notify_quantity" @keyup="checkEnglishNumbers($event, 'minimum_notify_quantity')">
                              <span class="input-group-addon">قطعة</span>
                            </div>
                            <!-- client and server side validation -->
                            <form-error v-if="errors.minimum_notify_quantity" class="help-block">
                              {{ errors.minimum_notify_quantity ? errors.minimum_notify_quantity.toString() : '' }}
                            </form-error>
                          </div>
                        </div>
                      </div>


                      <!----- Subscribers Percentage Field -------->
                      <div class="row product-data-row">
                        <div class="col-xs-12">
                          <div :class="{'form-group': true, 'mb-10': true, 'has-error': errors.subscribers_percentage || product.subscribers_percentage > 100}">
                            <label class="mb-0">نسبة العملاء المراد إشعارهم</label>
                            <p class="text-muted text-size-small">سيتم إرسال إشعار توفر المنتج لنسبة من العملاء المشتركين في "أعلمني عند التوفر". عدد العملاء المشتركين الآن {{product.total_subscribers}} عملاء.</p>
                            <div class="input-group">
                              <span class="input-group-addon input-group-addon-small"> <i
                                  class="sicon-special-discount"></i> </span>
                              <input id="number_of_clients" type="text" class="form-control _parseArabicNumbers"
                                     placeholder="100"
                                     autocomplete="off"
                                     v-model="product.subscribers_percentage"
                                     @keyup="checkEnglishNumbers($event, 'subscribers_percentage')">
                              <span class="input-group-addon">%</span>
                            </div>

                            <!-- client and server side validation -->
                            <form-error v-if="errors.subscribers_percentage || product.subscribers_percentage > 100" class="help-block">
                              {{ errors.subscribers_percentage ? errors.subscribers_percentage.toString() : 'لا يمكن أن تزيد قيمة حقل نسبة العملاء المراد إشعارهم عن 100' }}
                            </form-error>
                          </div>
                        </div>
                      </div>

                        <!----- Save button-------->
                        <button v-if="permissions.edit_product" class="btn btn-tiffany btn-full btn-xlg mt-15" type="button" @click="save">حفظ</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Http from "../../utils/http";
    import initialData from "../../services/ProductService";

    export default {
      data() {
          return {
              errors : [],
              product : null,
              showModal : false,
              permissions:[],
              showDialog: false
          }
        },
        mounted() {
            Salla.event.addEventListener('notify-quantity-settings::show-modal', this.show);
        },
        beforeDestroy() {
            document.removeEventListener('notify-quantity-settings::show-modal', this.show);
        },
        components: {

        },

        methods: {
            // Submit Form
            save() {
              this.errors = [];
              if ($('#accordion').find('.has-error').length) {
                this.showErr = true;
                setTimeout( () => {
                  this.showErr = false;
                }, 800)
              } else {
                this.showErr = false;
                showLoading();
                Http.put('/products/quantity/notification_setting/' + this.product.id, this.getPayload(), ({data}) => {
                  hideLoading();
                  laravel.ajax.successHandler(data)
                  // close the options and quantities modal
                  this.closeModal();

                  // Update product notification settings
                  Salla.event.createAndDispatch('product-details::update-notify-quantity-settings', {
                    'productId': this.product.id,
                    'notify_quantity': this.product.notify_quantity
                  });
                }, ({response}) => {
                  hideLoading();
                  // laravel.errors.renderValidation(response.data, ' ');
                  this.errors = response.data.error.fields;
                });
              }
            },

            // Load Modal Data
            show(){
                let data = initialData.getDatum('NotificationSettingData');
                this.product = data.product;
                this.permissions = data.permissions;
                this.showModal = true;

                $('body').addClass('modal-open').css('padding', '0 15px 0 0');

                setTimeout(() => {
                    this.showDialog = true;
                }, 500);
            },
            closeModal() {
                this.showDialog = false;
                $('body').removeClass('modal-open').css('padding', '0')
                setTimeout(() => {
                    this.showModal = false;
                    this.product = null;
                }, 500);
            },
            // Prepare Submit Payload
            getPayload() {
              return {
                'notify_quantity': this.product.notify_quantity,
                'minimum_notify_quantity': this.product.minimum_notify_quantity? Number(this.product.minimum_notify_quantity): 15,
                'subscribers_percentage': this.product.subscribers_percentage? Number(this.product.subscribers_percentage): 100,
              }
            },
            checkEnglishNumbers (e, field) {
              let val = e.target.value,
              regex = new RegExp(/^[0-9]*$/i);

              if (!regex.test(val)) {
                let e = '٠'.charCodeAt(0);
                val = val.replace(/[٠-٩]/g, function(t) {
                    return t.charCodeAt(0) - e;
                });
                this.product[field] = val;
              }

              if (e.key === '.') {
                this.product[field] = val.slice(0, -1);
              }
            }
        }
    }
</script>
