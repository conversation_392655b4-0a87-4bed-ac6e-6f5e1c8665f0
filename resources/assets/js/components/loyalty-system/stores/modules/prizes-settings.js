export default {
    state: {
        prizesType: [
            {
                id: 'discount',
                label: 'كوبون خصم'
            },
            {
                id: 'freeProduct',
                label: 'منتج مجاني'
            },
            {
                id: 'freeShipping',
                label: 'شحن مجاني'
            },
        ],
        prize: {
            prize_id: null,
            description: '',
            selectedPrize: null,
            requiredPoints: '',
            couponDiscountType: 'percent',
            couponPercentAmount: '',
            couponFixedAmount: '',
            couponSelectedCategory: [],
            couponPurchaseMiniAmount: '',
            couponDiscountImage: null,
            couponMaxAmount: '',
            freeProductSelected: null,
            freeProductImageUsed: 'current',
            freeProductImage: null,
            freeShippingCondition: false,
            freeShippingMaxPrice: '',
            active: true,
            translations: null
        },
        prizes: [],
        showErrorMessage: false,
    },
    mutations: {
        updateSelectedPrize (state, selected) {
            state.prize.selectedPrize = selected;
        },
        resetPrize (state, selected) {
            let resetPrize = {
                selectedPrize: selected,
                description: '',
                requiredPoints: '',
                couponDiscountType: 'percent',
                couponPercentAmount: '',
                couponFixedAmount: '',
                couponSelectedCategory: [],
                couponPurchaseMiniAmount: '',
                couponDiscountImage: null,
                couponMaxAmount: '',
                freeProductSelected: null,
                freeProductImageUsed: 'current',
                freeProductImage: null,
                freeShippingCondition: false,
                freeShippingMaxPrice: '',
                active: true,
                translations: null
            };
            state.prize = resetPrize;
            state.showErrorMessage = false;
        },
        updateSelectedCategory (state, selected) {
            state.prize.couponSelectedCategory.push(selected)
        },
        deselectedCategory (state, deselected) {
          const deselectedIndex =  state.prize.couponSelectedCategory.findIndex(ele => ele === deselected);
          state.prize.couponSelectedCategory.splice(deselectedIndex, 1);
        },
        updateSelectedProduct (state, selected) {
            state.prize.freeProductSelected = selected
        },
        prizesFieldsValidation (state) {
            switch (state.prize.selectedPrize) {
                case 'COUPON_DISCOUNT':
                    if ((state.prize.couponPercentAmount === '' && state.prize.couponDiscountType === 'percent') || (state.prize.couponFixedAmount === '' && state.prize.couponDiscountType !== 'percent') || ! state.prize.couponSelectedCategory.length || state.prize.couponPurchaseMiniAmount === '' ||
                        (state.prize.couponMaxAmount === '' && state.prize.couponDiscountType === 'percent') || state.prize.couponDiscountImage === null) {
                        state.showErrorMessage = true;
                    } else {
                        state.showErrorMessage = false;
                    }

                    if (state.prize.couponPercentAmount > 100 && state.prize.couponDiscountType === 'percent') {
                        state.showErrorMessage = true;
                    }
                break;
                case 'FREE_PRODUCT':
                    if (state.prize.freeProductSelected === null || (state.prize.freeProductImage === null && state.prize.freeProductImageUsed === 'new_image')) {
                        state.showErrorMessage = true;
                    } else {
                        state.showErrorMessage = false;
                    } 
                break;
                /*case 'FREE_SHIPPING':
                    state.prize.freeShippingMaxPrice === '' ? state.showErrorMessage = true : state.showErrorMessage = false;
                break;*/
            }

            if (state.prize.description === '' || state.prize.requiredPoints === '') {
                state.showErrorMessage = true;
            } 
        },
        addPrize (state) {
            state.prizes.push(state.prize);
        },
        setPrize (state, prize) {
            state.prize = JSON.parse(JSON.stringify(prize));
        },
        editPrize (state, index) {
            Object.keys(state.prizes[index]).forEach(function(key) {
                state.prizes[index][key] = state.prize[key];
            });
        },
        deletePrize (state, index) {
            state.prizes.splice(index, 1);
            $('.modal').modal('hide');
        },
        prizesActiveStatus (state, {index, statues}) {
            state.prizes[index].active = statues;
        },
    }
}