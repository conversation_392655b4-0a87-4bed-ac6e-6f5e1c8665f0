export default {
    state: {
        reminder: {
            selectedReminder: 'days',
            timePeriodValue: '',
            selectedChannel: [],
            reminderText: '',
            reminderTitle: '',
            active: false,
            translations:null
        },
        reminders: [],
        showErrorMessage: false,
    },
    mutations: {
        updateSelectedReminder (state, selected) {
            state.reminder.selectedReminder = selected;
        },
        selectedReminderChannel (state, selected) {
          state.reminder.selectedChannel.push(selected);
        },
        deselectedReminderChannel (state, deselected) {
          const deselectedIndex =  state.reminder.selectedChannel.findIndex(ele => ele === deselected);
          state.reminder.selectedChannel.splice(deselectedIndex, 1);
        },
        resetReminder (state) {
            let resetReminder = {
                selectedReminder: null,
                timePeriodValue: '',
                selectedChannel: [],
                reminderText: '',
                reminderTitle: '',
                active: false,
                translations:null
            };
            state.reminder = resetReminder;
            state.showErrorMessage = false;
        },
        remindersFieldsValidation (state) {
            if (!state.reminder.selectedChannel.length || 
                state.reminder.selectedReminder === null || 
                state.reminder.timePeriodValue === '' || 
                state.reminder.timePeriodValue == 0 ||
                state.reminder.reminderText === '' || 
                state.reminder.reminderTitle === '') {
                    state.showErrorMessage = true;
            } else {
                state.showErrorMessage = false;
            }
        },
        addReminder (state) {
            state.reminders.push(state.reminder);
        },
        setReminder (state, reminder) {
            state.reminder = JSON.parse(JSON.stringify(reminder));
        },
        editReminder (state, index) {
            Object.keys(state.reminders[index]).forEach(function(key) {
                state.reminders[index][key] = state.reminder[key];
            });
        },
        deleteReminder (state, index) {
            state.reminders.splice(index, 1);
            $('.modal').modal('hide');
        },
        reminderActiveStatus (state, {index, statues}) {
            state.reminders[index].active = statues;
        },
    }
}