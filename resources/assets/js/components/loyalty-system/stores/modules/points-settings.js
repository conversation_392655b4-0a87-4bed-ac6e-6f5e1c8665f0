export default {
  state: {
    pointsType: [],
    type: {
      point_id: '',
      selectedPoint: null,
      sharePoints: '',
      birthDatePoints: '',
      phonePoints: '',
      emailPoints: '',
      genderPoints: '',
      purchaseType: 'total_order',
      pointsPerAmountUnit: '',
      fixedAmount: '',
      pointsPerFixedAmount: '',
      storeRatingPoints: '',
      productRatingPoints: '',
      shippingRatingPoints: '',
      selectedChannel: [],
      active: false,
      icon: 'sicon-store',
      color: '#000',
    },
    points: [],
    completeProfileOptions: [],
    showErrorMessage: false,
  },

  mutations: {
    updateSelectedPoint(state, selected) {
      state.type.selectedPoint = selected
    },
    resetType(state, selected) {
      let resetType = {
        selectedPoint: selected,
        sharePoints: '',
        birthDatePoints: '',
        phonePoints: '',
        emailPoints: '',
        genderPoints: '',
        selectedChannel: [],
        purchaseType: 'total_order',
        pointsPerAmountUnit: '',
        fixedAmount: '',
        pointsPerFixedAmount: '',
        storeRatingPoints: '',
        productRatingPoints: '',
        shippingRatingPoints: '',
        active: false,
        icon: 'sicon-store',
        color: '#000',
      }
      state.type = resetType
      state.showErrorMessage = false
    },
    pointsFieldsValidation(state) {
      let pointFieldValidation = (state.type.purchaseType ==='total_order' && state.type.pointsPerAmountUnit === '') || (state.type.purchaseType !== 'total_order' && state.type.pointsPerFixedAmount === ''),
          completeInfoValidation  = state.type.birthDatePoints === '' || state.type.phonePoints === '' || state.type.emailPoints === '' || state.type.genderPoints === '',
          pointFieldAndSelectChannelValid= pointFieldValidation || state.type.selectedChannel.length === 0,
          rattingValidation  =state.type.storeRatingPoints === '' || state.type.productRatingPoints === '' || state.type.shippingRatingPoints === '' || state.type.selectedChannel.length === 0

      const conditions = {
        AFFILIATE_LINK: state.type.sharePoints === '',
        COMPLETE_PERSONAL_INFORMATION: completeInfoValidation,
        RATING: rattingValidation,
        BUY_FROM_STORE: pointFieldAndSelectChannelValid,
        FIRST_ORDER_STORE: pointFieldAndSelectChannelValid,
        ORDER_FROM_POS:pointFieldAndSelectChannelValid,
        ORDER_FROM_APP:pointFieldAndSelectChannelValid,
      };
      const selectedPointTypeForm = state.type.selectedPoint;
       state.showErrorMessage= conditions[selectedPointTypeForm]
    },

    selectedPointsChannel(state, selected) {
      state.type.selectedChannel.push(selected)
    },
    deselectedPointsChannel(state, deselected) {
      const deselectedIndex = state.type.selectedChannel.findIndex(
        (ele) => ele === deselected
      )
      state.type.selectedChannel.splice(deselectedIndex, 1)
    },
    addPoint(state) {
      state.pointsType.forEach((item) => {
        if (item.id === state.type.selectedPoint) {
          item.isDisabled = true
        }
      })
      state.points.push(state.type)
    },
    setType(state, point) {
      state.type = JSON.parse(JSON.stringify(point))
    },
    pointsActiveStatus(state, { index, statues }) {
      state.points[index].active = statues
    },
    editPoint(state, index) {
      Object.keys(state.points[index]).forEach(function (key) {
        state.points[index][key] = state.type[key]
      })
    },
    deletePoint(state, index) {
      //before delete enable the selected point
      state.pointsType.forEach((item) => {
        if (item.id === state.points[index].selectedPoint) {
          item.isDisabled = false
        }
      })
      state.points.splice(index, 1)
      $('.modal').modal('hide')
    },
  },
}
