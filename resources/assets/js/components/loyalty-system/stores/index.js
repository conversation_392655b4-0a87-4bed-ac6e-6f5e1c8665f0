import Vue from 'vue';
import Vuex from 'vuex';

import PointsSettings from './modules/points-settings';
import PrizesSettings from './modules/prizes-settings';
import RemindersSettings from './modules/reminders-settings';
import initialData from "../../shipping-role/RuleService";

Vue.use(Vuex);

export default new Vuex.Store({
    modules: {
        PointsSettings,
        PrizesSettings,
        RemindersSettings
    },

    state: {
        generalSettings: {
            loyalty_program_id: null,
            name: '',
            currency: 'ريال سعودي',
            description: null,
            status: false,
            oldStatus: false,
            pointsApprovedAfter: null,
            pointsValidityTimePeriod: null,
            pointsValidityTimePeriodValue: null,
            prizePromotionTitle: null,
            prizePromotionDescription: null,
            applyPrizeWithCouponOffer: false,
            completed_order_statuses: [],
            translations:[]
        },
        initSystem: false,
        categories: [],
        products: [],
        languages: {},
        timePeriodType: [
            {
                id: 'days',
                label: 'يوم'
            },
            {
                id: 'months',
                label: 'شهر'
            },
            {
                id: 'years',
                label: 'سنة'
            },
        ],
        sendingChannels: [],
        errors: null
    },

    getters: {

    },

    mutations: {
        initSystemStatus (state, status) {
            state.initSystem = status;
            state.generalSettings.status = status;
        },
        updateState: (state) => {
            let loyaltyProgram = initialData.getDatum('loyaltyProgram', []);
            let points = loyaltyProgram.points;
            let prizes = loyaltyProgram.prizes;
            let reminders = loyaltyProgram.reminders;

            delete loyaltyProgram.points;
            delete loyaltyProgram.prizes;
            delete loyaltyProgram.reminders;

            state.generalSettings = loyaltyProgram;
            state.generalSettings.currency = initialData.getDatum('currency', 'ريال سعودي');
            state.initSystem = loyaltyProgram.status;
            state.generalSettings.oldStatus = loyaltyProgram.status;

            state.PointsSettings.pointsType = initialData.getDatum('loyaltyProgramPointsTypes', []);
            state.PointsSettings.completeProfileOptions = initialData.getDatum('completeProfileOptions', []);
            state.PointsSettings.points = points;

            //disable selected points
            state.PointsSettings.points.forEach((point, index) => {
                state.PointsSettings.pointsType.forEach((item, index) => {
                    if (item.id === point.selectedPoint) {
                        item.isDisabled = 'true';
                    }
                });
            });

            state.PrizesSettings.prizesType = initialData.getDatum('loyaltyProgramPrizesTypes', []);
            state.PrizesSettings.prizes = prizes;

            state.categories = initialData.getDatum('categories', []);
            state.products = initialData.getDatum('products', []);
            state.languages = initialData.getDatum('languages', []);
            state.timePeriodType = initialData.getDatum('timePeriod', []);
            state.sendingChannels = initialData.getDatum('sendingChannel', []);
            state.ordersStatuses = initialData.getDatum('orders_statuses', []);

            state.RemindersSettings.reminders = reminders;
        }
    },

    actions: {
      initLanguages () {
          setTimeout(() => {
              if (window.initFieldLang) {
                  $('.rec-ls-field:not(.field-lang-initiated)')
                      .each((index, fieldInput)=> window.initFieldLang($(fieldInput)));
              }
          }, 100);
        },
        saveModal () {
            $('.modal').modal('hide');
        },
        updateState: (context, payload) => {
            context.commit('updateState', payload);
        },
        async saveLoyaltyProgram(context, loyaltyProgram) { // Fire when the type changes
            showLoading();


            await axios.post('/loyalty-system', loyaltyProgram).then(response => {
                hideLoading();

                swal({
                    text: 'تم الحفظ بنجاح',
                    type: 'success',
                    showConfirmButton: false,
                    timer: 1500
                }).catch(function(timeout) {
                    location.reload();
                });
            }).catch(response => {
                // if (response.response.data && response.response.data.error && response.response.data.error.data && response.response.data.error.data.locked || false) {
                //     hideLoading();
                //     return;
                // }

                hideLoading();
                context.state.errors = response.response.data.error.fields;
                if (context.state.errors['program.name'] || context.state.errors['program.description'] || context.state.errors['program.image']) {
                    $('html, body').animate({scrollTop: 0})
                } else {
                    laravel.ajax.errorHandler(response.response);
                }
            });

        },
    }

})
