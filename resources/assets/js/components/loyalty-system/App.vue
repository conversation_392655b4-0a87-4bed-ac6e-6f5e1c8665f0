<template>
     <div>
            <loyalty-system-subscription v-if="!isInstalledAppActive" :storePlan="storePlan" :isIos="isIos"/>

          <div v-else>
              <general-settings />

              <points-settings />

              <prizes-settings />

              <reminders-settings />

              <button class="btn btn-xl btn-full btn-tiffany" :disabled="isDisabled" @click="initialize">حفظ</button>
              <modal id="init_system" modalTitle="تدشين برنامج الولاء" saveBtn="تدشين"  @saveModal="initSystemHandler">
                  <div class="text-center">
                      <p class="mb-0 font-16" v-if="! this.generalSettings.name || ! this.points.length || !  this.prizes.length">هل أنت متأكد من تدشين برنامج الولاء؟ لم تزودنا بالمعلومات التالية</p>
                      <p class="mb-0 font-16" v-else>هل أنت متأكد من تدشين برنامج الولاء؟</p>
                      <p class="mb-0 font-16" v-if="! this.generalSettings.name || ! this.points.length || !  this.prizes.length">
                          (<span class="text-bold">
                              {{generalSettings.name === null ? ((points.length === 0 || prizes.length === 0) ? 'اسم البرنامج - ' : 'اسم البرنامج') : null}}
                              {{points.length === 0 ?  (prizes.length === 0 ? ' طرق الحصول علي النقاط - ' : ' طرق الحصول علي النقاط') : null}}
                              {{prizes.length === 0 ? 'الجوائز' : null}}
                          </span>)
                      </p>
                  </div>
              </modal>
          </div>
     </div>
</template>

<script>

import {mapState, mapMutations, mapActions} from 'vuex';

export default {
    computed: {
        ...mapState({
            initSystem: state => state.initSystem,
            generalSettings: state => state.generalSettings,
            points: state => state.PointsSettings.points,
            prizes: state => state.PrizesSettings.prizes,
            reminders: state => state.RemindersSettings.reminders,
        }),
        isDisabled() {
            return this.generalSettings.name === '' || this.points.length === 0 || this.prizes.length === 0;
        },
        isInstalledAppActive() {
          return window.initialData.isInstalledAppActive
        },
        storePlan() {
          return window.initialData.storePlan;
        },
        isIos() {
            return window.initialData.isIos;
        }
    },

    methods: {
        ...mapActions(['saveLoyaltyProgram']),
        initialize () {
            //if (this.generalSettings.name !== null && this.points.length && this.prizes.length) {
            if (this.generalSettings.status == true && this.generalSettings.status != this.generalSettings.oldStatus) {
              $('#init_system').modal('show');
            } else {
              this.initSystemHandler();
            }
        },

        initSystemHandler () {

            setTimeout ( () => {
                $('#init_system').modal('hide');

                this.saveLoyaltyProgram({
                  "program":this.generalSettings,
                  'points': this.points,
                  'prizes': this.prizes,
                  'reminders': this.reminders
                });
            }, 500);
        }
    },
  created() {
        this.$store.dispatch('updateState');
    },
}
</script>
