<template>
    <div class="panel panel-default panel-small-title">
        <div class="panel-heading sides">
            <h6 class="panel-title">التذكيرات</h6>
            <button class="btn btn-xs btn--outlined primary" data-toggle="modal" data-target="#reminder_settings_modal" v-if="reminders.length">
                <i class="sicon-add"></i>
                أضف تذكيرًا جديدًا
            </button>
          <span v-if="!initSystem" class="heading-label">اضف تذكيرا واحدا على الاقل قبل التفعيل</span>
        </div>
        <div class="panel-body no-padding">
            <section-placeholder 
                icon="time"
                title="التذكيرات"
                desc="اضف التذكيرات تلقائية الإرسال، لتنبه عملاءك على اقتراب نهاية صلاحية نقاطهم."
                btnText="أضف تذكيرًا جديدًا"
                modalOpen="#reminder_settings_modal"
                v-if="reminders.length === 0"
            />

            <div class="table-responsive" v-else>
                <table class="table table-less-padding table-medium-text">
                    <thead>
                        <tr class="active">
                            <th>نص التذكير</th>
                            <th>قناة الإرسال</th>
                            <th>وقت إرسال التذكير (تذكير باقتراب صلاحية النقاط على النفاد)</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="table-row" v-for="(reminder, index) in reminders" :key="index">
                            <td data-title="نص التذكير">
                                <a @click="setReminderHandler(reminder)" data-toggle="modal" :data-target="`#reminder_settings_modal${index}`"
                                   :class="{'text-danger': errors !== null && (errors[`reminders.${index}.reminderText`] || errors[`reminders.${index}.reminderTitle`])}">
                                    النقاط ستنتهي بعد {{getReminderTime(reminder)}}
                                </a>
                            </td>
                            <td data-title="قناة الإرسال">
                                <span v-for="(channel, index) in channelName(reminder.selectedChannel)" :key="index">
                                    {{channel}}
                                </span>
                            </td>
                            <td data-title="وقت إرسال التذكير (تذكير باقتراب صلاحية النقاط على النفاد)">
                                <span>
                                    {{getReminderTime(reminder)}}
                                </span>
                            </td>
                            <td class="text-right">
                                <toggle @change="reminderActiveStatus({index: index, statues: $event})" :value="reminder.active"/>
                            </td>
                            <modal :id="`reminder_settings_modal${index}`" 
                                modalTitle="إضافة تذكير" 
                                saveBtn="تعديل" 
                                :showDelete="true"
                                @closeModal="resetReminder" 
                                @saveModal="editReminderHandler(index)"
                                @delete="deleteReminderHandler(index)">
                                <reminders-customize :index="index"/>
                            </modal>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <modal id="reminder_settings_modal" modalTitle="إضافة تذكير" @closeModal="resetReminder" @saveModal="save">
            <reminders-customize />
        </modal>
    </div>
</template>

<script>

import {mapState, mapMutations, mapActions} from 'vuex';

export default {
    computed: {
        ...mapState({
            reminders: state => state.RemindersSettings.reminders,
            reminder: state => state.RemindersSettings.reminder,
            showErrorMessage: state => state.RemindersSettings.showErrorMessage,
            errors: state => state.errors,
            initSystem: state => state.initSystem,
        }),
    },

    methods: {
        ...mapMutations([
        'addReminder', 
        'resetReminder', 
        'setReminder', 
        'editReminder', 
        'deleteReminder', 
        'reminderActiveStatus', 
        'remindersFieldsValidation']),
        ...mapActions(['saveModal', 'initLanguages']),

        setReminderHandler (reminder) {
          this.setReminder(reminder);
          this.initLanguages();
        },

        channelName (names) {
          const changedNames = names.map(n => {
            let renames = n === 'email' ? n = 'البريد الالكتروني' : n === 'sms' ? 'رسالة نصية' : 'إشعارات التطبيق';
            return renames
          });
          return changedNames;
        },

        getReminderTime (time) {
            let selectedReminder;
            switch(time.selectedReminder) {
                case 'days':
                    selectedReminder = this.getReminderNumber(time.timePeriodValue, 'يوم واحد', 'يومان', 'ايام', 'يوم');
                break;
                case 'months':
                    selectedReminder = this.getReminderNumber(time.timePeriodValue, 'شهر واحد', 'شهران', 'اشهر', 'شهر');
                break;
                case 'years':
                    selectedReminder = this.getReminderNumber(time.timePeriodValue, 'سنة واحدة', 'سنتان', 'سنين', 'سنة');
                break;
            }
            return selectedReminder
        },

        getReminderNumber (number, val1, val2, val3, val4) {
            let _txt = "";
            if ((number !== null && number!= '') && 1 == number) {
                _txt = `${val1}`;
            } else if ((number !== null && number!= '') && 2 == number) {
                _txt = `${val2}`;
            } else if ((number !== null && number!= '') && number >= 3 && number <= 10) {
                _txt = (number) + ` ${val3}`;
            } else if ((number !== null && number!= '') && number > 10) {
                _txt = (number) + ` ${val4}`;
            } else {
                _txt = 'غير معروف';
            }

            return _txt;
        },

        save () {
          $('textarea[data-name="message_content"]').val('');
          this.remindersFieldsValidation();
          if (this.showErrorMessage === false) {
              this.addReminder();
              this.resetReminder();
          }
        },

        editReminderHandler (index) {
            this.remindersFieldsValidation();
            if (this.showErrorMessage === false) {
                this.editReminder(index);
                this.saveModal();
                this.resetReminder();
            }
        },

        deleteReminderHandler (index) {
            let self = this;
            swal({
                title: '<h2>'+ 'تحذير' +'</h2>',
                html:'<p>' + 'هل انت متاكد من حذف التذكير' + '</p>',
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: '#d33',
                confirmButtonText: 'موافق !',
                cancelButtonText: '<i class="fa fa-close"></i> إلغاء'
            })
            .then(function () {
                self.deleteReminder(index);
                self.resetReminder();
            });
        }
    }
}
</script>

