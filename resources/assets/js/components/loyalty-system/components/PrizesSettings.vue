<template>
    <div class="panel panel-default panel-small-title">
        <div class="panel-heading sides">
            <div>
              <h6 class="panel-title">المكافآت</h6>
              <span class="text-muted text-muted-small d-block">يرجى اضافة مكافئة على الأقل حتى يتم ظهور برنامج الولاء في متجرك</span>
            </div>
            <button class="btn btn-xs btn--outlined primary" data-toggle="modal" data-target="#prizes_settings_modal" v-if="prizes.length">
                <i class="sicon-add"></i>
                أضف مكافأة جديدة
            </button>
          <span v-if="!initSystem" class="heading-label">اضف مكافأة واحدة على الاقل قبل التفعيل</span>
        </div>
        <div class="panel-body no-padding">
            <section-placeholder
                icon="party-horn"
                title="المكافآت"
                desc="اضف المكافآت التي يمكن للعميل أن يستبدل نقاطه بها"
                btnText="اضافة مكافأة جديدة"
                modalOpen="#prizes_settings_modal"
                v-if="prizes.length === 0"
            />
            <div v-else>
                <div class="row p-20">
                    <div class="col-xs-12">
                            <!----------------- prizePromotionTitle -------------->
                        <LingualField
                            :events-load="generalSettings"
                            :id="generalSettings.loyalty_program_id"
                            :translations="generalSettings.translations"
                            :languages="languages"
                            :placeholder="`ادخل العنوان الترويجي للجائزة`"
                            :name="'prize_promotion_title'"
                            :isNew="!!generalSettings.isNewTranslation"
                            icon="sicon-chat-conversation-alt"
                            required="required"
                            :outerAttrs="{class:{'form-group': true, 'has-error': errors !== null && errors['program.prizePromotionTitle']}}"
                            :innerAttrs="{class:{'input-group': true}}"
                            @value-updated="prizePromotionTitleUpdated"
                            @value-changed="prizePromotionTitleUpdated"
                        >
                            <template v-slot:before-input>
                                <label>العنوان الترويجي</label>
                            </template>
                            <!-- client and server side validation -->
                            <form-error v-if="errors !== null && errors['program.prizePromotionTitle']">{{errors['program.prizePromotionTitle'][0]}}</form-error>
                        </LingualField>
                        <!--------------------------------------------->
                    </div>
                    <div class="col-xs-12">
                        <!----------------- prizePromotionDescription -------------->
                        <LingualField
                            :events-load="generalSettings"
                            :id="generalSettings.loyalty_program_id"
                            :translations="generalSettings.translations"
                            :languages="languages"
                            :placeholder="`ادخل الوصف الترويجي للجائزة`"
                            :name="'prize_promotion_description'"
                            :isNew="!!generalSettings.isNewTranslation"
                            required="required"
                            icon="sicon-chat-conversation-alt"
                            :outerAttrs="{class:{'form-group': true, 'mb-0': true, 'has-error': errors !== null && errors['program.prizePromotionDescription']}}"
                            :innerAttrs="{class:{'input-group': true}}"
                            @value-updated="prizePromotionDescriptionUpdated"
                            @value-changed="prizePromotionDescriptionUpdated"
                            type="textarea"
                        >
                            <template v-slot:before-input>
                                <label>الوصف الترويجي</label>
                            </template>
                            <!-- client and server side validation -->
                            <form-error v-if="errors !== null && errors['program.prizePromotionDescription']">{{errors['program.prizePromotionDescription'][0]}}</form-error>
                        </LingualField>
                        <!--------------------------------------------->
                    </div>
                </div>

                <div class="table-responsive" >
                    <table class="table table-less-padding table-medium-text">
                        <thead>
                            <tr class="active">
                                <th>الأسم</th>
                                <th>عدد النقاط</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-row" v-for="(prize, index) in prizes" :key="index">
                                <td data-title="الأسم">
                                    <a @click="setPrizeHandler(prize)" data-toggle="modal" :data-target="`#prizes_settings_modal_${index}`"
                                       :class="{'text-danger': errors !== null && errors[`prizes.${index}.description`]}">
                                        {{prizeName(prize)}}
                                    </a>
                                </td>
                                <td data-title="عدد النقاط">
                                    <span>
                                        {{prize.requiredPoints !== null && prize.requiredPoints != '' ? prize.requiredPoints : 0}} نقطة
                                    </span>
                                </td>
                                <td class="text-right">
                                    <toggle @change="prizesActiveStatus({index: index, statues: $event})" :value="prize.active"/>
                                </td>
                                <modal :id="`prizes_settings_modal_${index}`"
                                    modalTitle="إضافة مكافأة"
                                    saveBtn="تعديل"
                                    :showDelete="true"
                                    @closeModal="resetPrize"
                                    @saveModal="editPrizeHandler(index)"
                                    @delete="deletePrizeHandler(index)">
                                    <prizes-customize :id="`edit_${index}`" :index="index" />
                                </modal>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <modal id="prizes_settings_modal" modalTitle="اضافة مكافأة" :disabled="prize.selectedPrize === null || typeof(prize.selectedPrize) === 'undefined'" @closeModal="resetPrize" @saveModal="save">
                <prizes-customize id="create" :index="prize.length"/>
            </modal>
        </div>
    </div>
</template>


<script>
import {mapState, mapMutations, mapActions} from 'vuex';
import LingualField from "@salla.sa/languages/components/LingualField";

export default {
    components: {
        'LingualField': LingualField,
    },
    computed: {
        ...mapState({
            prizes: state => state.PrizesSettings.prizes,
            prize: state => state.PrizesSettings.prize,
            showErrorMessage: state => state.PrizesSettings.showErrorMessage,
            generalSettings: state => state.generalSettings,
            errors: state => state.errors,
            languages: state => state.languages,
            initSystem: state => state.initSystem,
        }),
    },

    methods: {
        ...mapMutations([
        'addPrize',
        'resetPrize',
        'setPrize',
        'editPrize',
        'deletePrize',
        'prizesActiveStatus',
        'prizesFieldsValidation']),
        ...mapActions(['saveModal', 'initLanguages']),

        setPrizeHandler(prize) {
          this.setPrize(prize);
          this.initLanguages();
        },

        prizeName (name) {
            let selectedName;
            switch(name.selectedPrize) {
                case 'COUPON_DISCOUNT':
                    if (name.couponDiscountType === 'percent') {
                        selectedName = `كوبون خصم: ${`${name.couponPercentAmount !== null && name.couponPercentAmount != '' ? `${name.couponPercentAmount}%` : ''}`}`
                    } else {
                        selectedName = `كوبون خصم: ${`${name.couponFixedAmount !== null && name.couponFixedAmount != '' ? `${name.couponFixedAmount}ر.س` : ''}`}`
                    }
                break;
                case 'FREE_PRODUCT':
                    selectedName= 'منتج مجاني: ' + name.freeProductSelected.label;
                break;
                case 'FREE_SHIPPING':
                    selectedName= 'شحن مجاني'/* + name.freeShippingMaxPrice + ' ر.س بحد أقصي'*/;
                break;
            }
            return selectedName
        },

        save () {
            this.prizesFieldsValidation();
            if (this.showErrorMessage === false) {
                this.addPrize();
                this.resetPrize();
                window?.parent?.postMessage({ event: "overlay.open" }, "*");
                $('.modal').modal('hide');
            }
        },

        editPrizeHandler (index) {
            this.prizesFieldsValidation();
            if (this.showErrorMessage === false) {
                this.editPrize(index);
                this.saveModal();
                this.resetPrize();
            }
        },

        deletePrizeHandler (index) {
            let self = this;
            swal({
                title: '<h2>'+ 'تحذير' +'</h2>',
                html:'<p>' + 'هل انت متاكد من حذف الجائزة' + '</p>',
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: '#d33',
                confirmButtonText: 'موافق !',
                cancelButtonText: '<i class="fa fa-close"></i> إلغاء'
            })
            .then(function () {
                self.deletePrize(index);
                self.resetPrize();
            });
        },

        prizePromotionTitleUpdated(event) {
            this.translations = event.translations;
            this.generalSettings.prizePromotionTitle = event.storeLangValue;
        },

        prizePromotionDescriptionUpdated(event) {
          this.translations = event.translations;
          this.generalSettings.prizePromotionDescription = event.storeLangValue;
        },
    }
}
</script>
