<template>
    <div class="panel panel-default panel-small-title">
      <div class="panel-heading sides">
        <h6 class="panel-title">الخيارات العامة</h6>
      </div>
      <div class="panel-body pt-20">
            <div class="rec-list rec-list--horizontal rec-list--space-between rec-list--align-center mb-20">
                <div>
                    <h6 class="mt-0 mb-0 text-bold font-16">تفعيل نظام الولاء</h6>
                    <span class="d-block text-muted mb-0 font-14">قم بتفعيل نظام الولاء لتتمكن من اسعاد عملائك و كسب ولائهم</span>
                </div>
                <toggle @change="initSystemStatus($event)" :value="initSystem"/>
            </div>
            <div class="row">
              <div class="col-md-12">
                <!----------------- Program Name -------------->
                <LingualField
                    :events-load="generalSettings"
                    :id="generalSettings.loyalty_program_id"
                    :translations="generalSettings.translations"
                    :languages="languages"
                    :placeholder="`أدخل اسم النظام`"
                    :name="'name'"
                    :isNew="!!generalSettings.isNewTranslation"
                    icon="sicon-format-text-alt"
                    required="required"
                    :outerAttrs="{class:{'form-group': true, 'has-error': errors !== null && errors['program.name']}}"
                    :innerAttrs="{class:{'input-group': true}}"
                    @value-updated="nameUpdated"
                    @value-changed="nameUpdated"
                >
                    <template v-slot:before-input>
                        <label>اسم النظام</label>
                    </template>
                    <!-- client and server side validation -->
                    <form-error v-if="errors !== null && errors['program.name']">{{errors['program.name'][0]}}</form-error>
                </LingualField>
                <!--------------------------------------------->

                <!----------------- Program Name -------------->
                <LingualField
                      icon="sicon-chat-conversation-alt"
                      :events-load="generalSettings"
                      :id="generalSettings.loyalty_program_id"
                      :translations="generalSettings.translations"
                      :languages="languages"
                      :placeholder="`أدخل كلمة ترويجية للنظام`"
                      :name="'description'"
                      :isNew="!!generalSettings.isNewTranslation"
                      required="required"
                      :outerAttrs="{class:{'form-group': true, 'has-error': errors !== null && errors['program.description']}}"
                      :innerAttrs="{class:{'input-group': true}}"
                      @value-updated="descriptionUpdated"
                      @value-changed="descriptionUpdated"
                      type="textarea"
                >
                  <template v-slot:before-input>
                    <label>الوصف الترويجي</label>
                  </template>
                  <!-- client and server side validation -->
                  <form-error v-if="errors !== null && errors['program.description']">{{errors['program.description'][0]}}</form-error>
                </LingualField>
                <!--------------------------------------------->
                <div class="form-group mb-0" :class="{'has-error': errors !== null && errors['program.image']}">
                      <filepond
                          :cropAspectRatio="'null'"
                          v-model="generalSettings.image"
                          :label="'صورة ترمز للنظام'"
                          :uploader="'generalSettings'"
                          pond-ref="owner"
                          :finishedUpload="generalSettings.image !== null"
                          :imgSrc="generalSettings.image"
                          :uploading="generalSettings.image == null"
                      />
                      <form-error v-if="errors !== null && errors['program.image']">{{errors['program.image'][0]}}</form-error>
                </div>
              </div>
            </div>
        </div>
    </div>
</template>


<script>

import {mapState, mapMutations} from 'vuex';
import LingualField from "@salla.sa/languages/components/LingualField";

export default {

    components: {
        'LingualField': LingualField,
    },
    computed: mapState(['generalSettings', 'errors', 'languages', 'initSystem']),
    mounted: () => {
            const items = [
                {label: 'الادوات التسويقيه'},
                {label: 'نظام الولاء'},
            ]
            window.parent?.postMessage({
            event: 'breadcrumb', data: items
            }, '*')
    },
    methods: {
        ...mapMutations(['initSystemStatus']),
        nameUpdated(event) {
            this.translations = event.translations;
            this.generalSettings.name = event.storeLangValue;
            this.generalSettings.translations = event.translations;
        },
        descriptionUpdated(event) {
            this.translations = event.translations;
            this.generalSettings.description = event.storeLangValue;
            this.generalSettings.translations = event.translations;
        }
    }
}
</script>
