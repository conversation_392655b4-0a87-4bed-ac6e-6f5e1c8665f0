<template>
    <div>
        <div class="rec-list rec-list--horizontal rec-list--space-between rec-list--align-center rec-list--warp mb-20">
            <div class="mb-15">
                <h6 class="mt-0 mb-0">وقت إرسال التذكير</h6> 
                <span class="text-muted text-muted-small">حدّد وقت إرسال التذكير بأن يكون قبل إنتهاء صلاحية النقاط`</span>
            </div>
            <div class="rec-list rec-list--horizontal rec-list--space-between mb-0">
                <div class="form-group mb-0 field" :class="{'has-error': showErrorMessage && reminder.timePeriodValue === ''}">
                    <input id="send_value" type="number" min="1" class="form-control full-bordered inline-field border-left-none _parseArabicNumbers" placeholder="5" v-model="reminder.timePeriodValue">
                    <form-error v-if="showErrorMessage && reminder.timePeriodValue === ''">{{requiredField}}</form-error>
                </div>
                <div class="form-group mb-0 field" :class="{'has-error': showErrorMessage && reminder.selectedReminder === null}">
                    <tree-select 
                        placeholder="يوم"
                        :options="timePeriodTypes"
                        @change="updateSelectedReminder($event)"
                        :value="reminder.selectedReminder"
                        class="inline-field"
                    />
                    <form-error v-if="showErrorMessage && reminder.selectedReminder === null">{{requiredFieldDropdown}}</form-error>
                </div>
            </div>
        </div>
        <div class="form-group" :class="{'has-error': showErrorMessage && !reminder.selectedChannel.length}">
            <label>قناة الإرسال</label>
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-paper-send"></i>
                </span>
                <tree-select 
                    placeholder="اختر قناة الإرسال"
                    :withIcon="true"
                    :options="sendingChannels"
                    @change="selectedReminderChannel($event)"
                    @deselect="deselectedReminderChannel($event)"
                    :value="reminder.selectedChannel"
                    :multiple="true"
                />
            </div>
            <form-error v-if="showErrorMessage && !reminder.selectedChannel.length">{{requiredFieldDropdown}}</form-error>
        </div>
        <!----------------- Reminder Title -------------->
        <div :class="{'form-group': true, 'has-error': showErrorMessage && reminder.reminderTitle === '' ||
                      errors !== null && errors[`reminders.${index}.reminderTitle`]}">
          <LingualField
            icon="sicon-book"
            :events-load="reminder"
            :id="reminder.reminder_id"
            :translations="reminder.translations"
            :languages="languages"
            :placeholder="`ادخل عنوان الرسالة البريدية`"
            :name="`message_title`"
            :isNew="!!reminder.isNewTranslation"
            required="required"
            :innerAttrs="{class:{'input-group': true}}"
            @value-updated="reminderTitleUpdated"
            @value-changed="reminderTitleUpdated"
        >
            <template v-slot:before-input>
                <label>عنوان الرسالة البريدية</label>
            </template>
            <!-- client and server side validation -->
            <form-error v-if="showErrorMessage && reminder.reminderTitle === ''">{{requiredField}}</form-error>
            <form-error v-if="errors !== null && errors[`reminders.${index}.reminderTitle`]">{{errors[`reminders.${index}.reminderTitle`][0]}}</form-error>
          </LingualField>
        </div>
        <!--------------------------------------------->
        <!----------------- Reminder Title -------------->
        <div :class="{'form-group': true, 'mb-0': true, 'has-error': showErrorMessage && reminder.reminderText === '' ||
                      errors !== null && errors[`reminders.${index}.reminderText`]}">
          <LingualField
            icon="sicon-chat-conversation-alt"
            :events-load="reminder"
            :id="reminder.reminder_id"
            :translations="reminder.translations"
            :languages="languages"
            :placeholder="`نص التذكير`"
            :name="`message_content`"
            :isNew="!!reminder.isNewTranslation"
            required="required"
            :innerAttrs="{class:{'input-group': true}}"
            @value-updated="reminderTextUpdated"
            @value-changed="reminderTextUpdated"
            type="textarea"
        >
            <template v-slot:before-input>
                <label>نص التذكير</label>
            </template>
            <!-- client and server side validation -->
            <form-error v-if="showErrorMessage && reminder.reminderText === ''">{{requiredField}}</form-error>
            <form-error v-if="errors !== null && errors[`reminders.${index}.reminderText`]">{{errors[`reminders.${index}.reminderText`][0]}}</form-error>
          </LingualField>
        </div>
        <!--------------------------------------------->
    </div>
</template>

<script>
import {mapState, mapMutations} from 'vuex';
import LingualField from "@salla.sa/languages/components/LingualField";

export default {
  props: ['index'],

  components: {
      'LingualField': LingualField,
  },
  data () {
      return {
          requiredField: 'الرجاء عدم ترك الحقل فارغا',
          requiredFieldDropdown: 'الرجاء الاختيار من القائمة',
          isValidValue: true,
      }
  },

  watch:{
    reminder:{
      handler(val){
        if(val.timePeriodValue == 0){
          this.reminder.timePeriodValue = '';
        }
      },
      deep: true
    }
  },

  computed: {
      ...mapState({
          timePeriodTypes: state => state.timePeriodType,
          sendingChannels: state => state.sendingChannels,
          reminder: state => state.RemindersSettings.reminder,
          showErrorMessage: state => state.RemindersSettings.showErrorMessage,
          languages: state => state.languages,
          errors: state => state.errors,
      }) 
  },

  methods: {
      ...mapMutations(['updateSelectedReminder', 'selectedReminderChannel', 'deselectedReminderChannel']),

      reminderTitleUpdated(event) {
          this.translations = event.translations;
          this.reminder.reminderTitle = event.storeLangValue;
          this.reminder.translations = event.translations;
      },

      reminderTextUpdated(event) {
          this.translations = event.translations;
          this.reminder.reminderText = event.storeLangValue;
          this.reminder.translations = event.translations;
      }
  }
}
</script>

