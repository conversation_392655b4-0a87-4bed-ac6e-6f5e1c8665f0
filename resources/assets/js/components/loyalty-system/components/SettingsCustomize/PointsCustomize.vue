<template>
    <div>
        <div class="row">
            <div class="col-sm-2">
                <div class="form-group" :class="{'mb-0': type.selectedPoint === null || typeof(type.selectedPoint) === 'undefined'}">
                    <label>الايقونة</label>
                    <div class="input-group">
                        <div class="input-group-addon rec-list rec-list--horizontal rec-list--align-center rec-list--nowrap wide border-left icon-color-picker-wrapper">
                            <icon-picker :value="type.icon" @input="iconChangeHandler"/>
                            <color-picker :value="type.color"  @input="colorChangeHandler" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-10">
                <div class="form-group" :class="{'mb-0': type.selectedPoint === null || typeof(type.selectedPoint) === 'undefined'}">
                    <label>طرق الحصول على النقاط</label>
                    <div class="input-group">
                        <span class="input-group-addon">
                            <i class="sicon-arrow-diverge"></i>
                        </span>
                        <tree-select
                            placeholder="اختر طريقة الحصول على النقاط"
                            :withIcon="true"
                            :options="pointsType"
                            @change="selectChangeHanlder"
                            :value="type.selectedPoint"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group" :class="{'has-error': showErrorMessage && type.sharePoints === ''}" v-if="type.selectedPoint === 'AFFILIATE_LINK'">
            <label>النقاط المستحقة لكل عملية شراء</label>
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-star-o"></i>
                </span>
                <input :id="`share_points_${id}`" type="text" class="form-control _parseArabicNumbers" @keyup="zeroValidation($event, 'sharePoints')" placeholder="اضف عدد النقاط" v-model="type.sharePoints">
            </div>
            <form-error v-if="showErrorMessage && type.sharePoints === ''">{{requiredField}}</form-error>
        </div>
        <div v-if="type.selectedPoint === 'COMPLETE_PERSONAL_INFORMATION'">
            <div class="form-group" :class="{'has-error': showErrorMessage && type.birthDatePoints === ''}">
                <label>النقاط المستحقة لإكمال تاريخ الميلاد</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`birth_date_points_${id}`" type="text" class="form-control _parseArabicNumbers" @keyup="zeroValidation($event, 'birthDatePoints')" placeholder="اضف عدد النقاط" v-model="type.birthDatePoints">
                </div>
                <form-error v-if="showErrorMessage && type.birthDatePoints === ''">{{requiredField}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.phonePoints === ''}">
                <label>النقاط المستحقة لإكمال رقم الجوال</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`phone_points_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" @keyup="zeroValidation($event, 'phonePoints')" v-model="type.phonePoints">
                </div>
                <form-error v-if="showErrorMessage && type.phonePoints === ''">{{requiredField}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.emailPoints === ''}">
                <label>النقاط المستحقة لإكمال البريد الإلكتروني</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`email_points_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" @keyup="zeroValidation($event, 'emailPoints')" v-model="type.emailPoints">
                </div>
                <form-error v-if="showErrorMessage && type.emailPoints === ''">{{requiredField}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.genderPoints === ''}">
                <label>النقاط المستحقة لإكمال الجنس</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`gender_points_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" v-model="type.genderPoints" @keyup="zeroValidation($event, 'genderPoints')">
                </div>
                <form-error v-if="showErrorMessage && type.genderPoints === ''">{{requiredField}}</form-error>
            </div>
        </div>
        <div v-if="type.selectedPoint === 'BUY_FROM_STORE'">
            <div class="form-group">
                <label>طريقة حصول العميل علي نقاط الشراء من المتجر</label>
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
                    <input :id="`total_order_points_${id}`" type="radio" :name="`purchase_type_${id}`" value="total_order" v-model="type.purchaseType">
                    <label :for="`total_order_points_${id}`" class="mb-10">
                        <span>عدد النقاط حسب اجمالي الطلب</span>
                    </label>
                </div>
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
                    <input :id="`total_fixes_amount_${id}`" type="radio" :name="`purchase_type_${id}`" value="fixed_amount" v-model="type.purchaseType">
                    <label :for="`total_fixes_amount_${id}`">
                        <!-- <span>عدد النقاط لمبلغ ثابت</span> -->
                        <span>عدد نقاط ثابتة</span>
                    </label>
                </div>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.pointsPerAmountUnit === '' && type.purchaseType === 'total_order'}" v-if="type.purchaseType === 'total_order'">
                <label v-text="`النقاط المستحقة لكل ${generalSettings.currency}`"></label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`points_per_amount_${id}`" type="text" class="form-control _parseArabicNumbers" @keyup="zeroValidation($event, 'pointsPerAmountUnit')" placeholder="اضف عدد النقاط" v-model="type.pointsPerAmountUnit">
                </div>
                <form-error v-if="showErrorMessage && type.pointsPerAmountUnit === '' && type.purchaseType === 'total_order'">{{requiredField}}</form-error>
            </div>
            <div v-else>
                <div class="form-group" :class="{'has-error': showErrorMessage && type.pointsPerFixedAmount === '' && type.purchaseType !== 'total_order'}">
                    <label>النقاط المستحقة</label>
                    <div class="input-group">
                        <span class="input-group-addon">
                            <i class="sicon-star-o"></i>
                        </span>
                        <input :id="`points_per_total_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" @keyup="zeroValidation($event, 'pointsPerFixedAmount')" v-model="type.pointsPerFixedAmount">
                    </div>
                    <form-error v-if="showErrorMessage && type.pointsPerFixedAmount === '' && type.purchaseType !== 'total_order'">{{requiredField}}</form-error>
                </div>
            </div>
        </div>
        <div v-if="type.selectedPoint === 'RATING'">
            <div class="form-group" :class="{'has-error': showErrorMessage && type.storeRatingPoints === ''}">
                <label>النقاط المستحقة عند تقييم المتجر</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`store_rating_${id}`" type="text" class="form-control _parseArabicNumbers" @keyup="zeroValidation($event, 'storeRatingPoints')" placeholder="اضف عدد النقاط" v-model="type.storeRatingPoints">
                </div>
                <form-error v-if="showErrorMessage && type.storeRatingPoints === ''">{{requiredField}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.productRatingPoints === ''}">
                <label>النقاط المستحقة عند تقييم المنتجات</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`product_rating_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" @keyup="zeroValidation($event, 'productRatingPoints')" v-model="type.productRatingPoints">
                </div>
                <form-error v-if="showErrorMessage && type.productRatingPoints === ''">{{requiredField}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.shippingRatingPoints === ''}">
                <label>النقاط المستحقة عند تقييم شركات الشحن</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
                    <input :id="`shipping_rating_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" @keyup="zeroValidation($event, 'shippingRatingPoints')" v-model="type.shippingRatingPoints">
                </div>
                <form-error v-if="showErrorMessage && type.shippingRatingPoints === ''">{{requiredField}}</form-error>
            </div>
        </div>
        <div v-if="type.selectedPoint ==='FIRST_ORDER_STORE' || type.selectedPoint==='ORDER_FROM_POS' || type.selectedPoint==='ORDER_FROM_APP'">
          <div class="form-group">
            <label>طريقة حصول العميل علي نقاط الشراء من المتجر</label>
            <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
              <input :id="`total_order_points_${id}`" type="radio" :name="`purchase_type_${id}`" value="total_order" v-model="type.purchaseType">
              <label :for="`total_order_points_${id}`" class="mb-10">
                <span>عدد النقاط حسب اجمالي الطلب</span>
              </label>
            </div>
            <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
              <input :id="`total_fixes_amount_${id}`" type="radio" :name="`purchase_type_${id}`" value="fixed_amount" v-model="type.purchaseType">
              <label :for="`total_fixes_amount_${id}`">
                <span>عدد نقاط ثابتة</span>
              </label>
            </div>
          </div>
          <div class="form-group" :class="{'has-error': showErrorMessage && type.pointsPerAmountUnit === '' && type.purchaseType === 'total_order'}" v-if="type.purchaseType === 'total_order'">
            <label v-text="`النقاط المستحقة لكل ${generalSettings.currency}`"></label>
            <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-star-o"></i>
                    </span>
              <input :id="`points_per_amount_${id}`" type="text" class="form-control _parseArabicNumbers" @keyup="zeroValidation($event, 'pointsPerAmountUnit')" placeholder="اضف عدد النقاط" v-model="type.pointsPerAmountUnit">
            </div>
            <form-error v-if="showErrorMessage && type.pointsPerAmountUnit === '' && type.purchaseType === 'total_order'">{{requiredField}}</form-error>
          </div>
          <div v-else>
            <div class="form-group" :class="{'has-error': showErrorMessage && type.pointsPerFixedAmount === '' && type.purchaseType !== 'total_order'}">
              <label>النقاط المستحقة</label>
              <div class="input-group">
                        <span class="input-group-addon">
                            <i class="sicon-star-o"></i>
                        </span>
                <input :id="`points_per_total_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" @keyup="zeroValidation($event, 'pointsPerFixedAmount')" v-model="type.pointsPerFixedAmount">
              </div>
              <form-error v-if="showErrorMessage && type.pointsPerFixedAmount === '' && type.purchaseType !== 'total_order'">{{requiredField}}</form-error>
            </div>
          </div>
        </div>
        <div
            class="form-group mb-0" :class="{'has-error': showErrorMessage && !type.selectedChannel.length ||
                      errors !== null && errors[`points.${index}.selectedChannel`]}"
              v-if="type.selectedPoint !== null && typeof(type.selectedPoint) !== 'undefined'">
            <label>قناة الإرسال</label>
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-paper-send"></i>
                </span>
                <tree-select
                    placeholder="اختر قناة الإرسال"
                    :withIcon="true"
                    :options="sendingChannels"
                    @change="selectedPointsChannel($event)"
                    @deselect="deselectedPointsChannel($event)"
                    :value="type.selectedChannel"
                    :multiple="true"
                />
            </div>
            <form-error v-if="showErrorMessage && !type.selectedChannel.length">{{requiredFieldDropdown}}</form-error>
            <form-error v-if="errors !== null && errors[`points.${index}.selectedChannel`]">{{errors[`points.${index}.selectedChannel`][0]}}</form-error>
        </div>
    </div>
</template>

<script>

    import {mapState, mapMutations} from 'vuex';

    export default {
        props: ['id', 'index'],

        data () {
            return {
                requiredField: 'الرجاء عدم ترك الحقل فارغا',
                requiredFieldDropdown: 'الرجاء الاختيار من القائمة',
            }
        },

        computed: {
            ...mapState({
                pointsType: state => state.PointsSettings.pointsType,
                selectedPoint: state => state.PointsSettings.selectedPoint,
                type: state => state.PointsSettings.type,
                completeProfileOptions: state => state.PointsSettings.completeProfileOptions,
                showErrorMessage: state => state.PointsSettings.showErrorMessage,
                sendingChannels: state => state.sendingChannels,
                errors: state => state.errors,
                generalSettings: state => state.generalSettings,
            }),
        },

        methods: {
            ...mapMutations(['updateSelectedPoint', 'resetType', 'selectedPointsChannel', 'deselectedPointsChannel']),

            selectChangeHanlder ($event) {
                this.updateSelectedPoint($event);
                this.resetType($event);
            },
            iconChangeHandler ($event) {
              this.type.icon = $event;
            },

            colorChangeHandler ($event) {
                this.type.color = $event;
            },

            zeroValidation(eve, type) {
              const value = eve.target.value;
              !/^[1-9][0-9]*$/.test(value) ? this.type[type] = '' : null;
            }
        }
    }

</script>
