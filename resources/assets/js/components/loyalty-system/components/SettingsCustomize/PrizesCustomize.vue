<template>
    <div>
        <div class="form-group" :class="{'mb-0': prize.selectedPrize === null || typeof(prize.selectedPrize) === 'undefined'}">
            <label>نوع المكافأة</label>
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-award-ribbon"></i>
                </span>
                <tree-select 
                    placeholder="اختر نوع المكافأة"
                    :withIcon="true"
                    :options="prizesType"
                    @change="selectChangeHanlder"
                    :value="prize.selectedPrize"
                />
            </div>
        </div>
        <!----------------- prize description -------------->
        <div :class="{'has-error': showErrorMessage && prize.description === '' 
                      || errors !== null && errors[`prizes.${index}.description`]}">
          <LingualField
            v-if="prize.selectedPrize !== null && typeof(prize.selectedPrize) !== 'undefined'"
            :events-load="prize"
            :id="prize.prize_id"
            :translations="prize.translations"
            :languages="languages"
            icon="sicon-chat-conversation-alt"
            :placeholder="`ادخل وصف الجائزة`"
            :name="`description`"
            :isNew="!!prize.isNewTranslation"
            required="required"
            :outerAttrs="{class:{'form-group': true,}}"
            :innerAttrs="{class:{'input-group': true}}"
            @value-updated="descriptionUpdated"
            type="textarea"
          >
              <template v-slot:before-input>
                  <label>الوصف</label>
              </template>
              <!-- client and server side validation -->
              <form-error v-if="showErrorMessage && prize.description === ''">{{requiredField}}</form-error>
              <form-error v-if="errors !== null && errors[`prizes.${index}.description`]">{{errors[`prizes.${index}.description`][0]}}</form-error>
          </LingualField>
        </div>
        <!--------------------------------------------->
        <div class="form-group" v-if="prize.selectedPrize !== null && typeof(prize.selectedPrize) !== 'undefined'"
            :class="{'has-error': showErrorMessage && prize.requiredPoints === ''}">
            <label>عدد النقاط المتطلبة</label>
            <div class="input-group">
                <span class="input-group-addon">
                    <i class="sicon-star-o"></i>
                </span>
                <input :id="`required_points_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="اضف عدد النقاط" v-model.lazy="prize.requiredPoints">
            </div>
            <form-error v-if="showErrorMessage && prize.requiredPoints === ''">{{requiredField}}</form-error>
        </div>
        <div v-if="prize.selectedPrize === 'COUPON_DISCOUNT'">
            <div class="form-group">
                <label>نوعية الخصم</label>
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
                    <input :id="`discount_type_percent_${id}`" type="radio" :name="`discount_type_${id}`" value="percent" v-model="prize.couponDiscountType" :checked="prize.couponDiscountType">
                    <label :for="`discount_type_percent_${id}`" class="mb-10">
                        <span>نسبة من المشتريات</span>
                    </label>
                </div>
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
                    <input :id="`discount_type_fixed_${id}`" type="radio" :name="`discount_type_${id}`" value="fixed_amount" v-model="prize.couponDiscountType" :checked="prize.couponDiscountType">
                    <label :for="`discount_type_fixed_${id}`">
                        <span>مبلغ ثابت</span>
                    </label>
                </div>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && ((prize.couponPercentAmount === '' && prize.couponDiscountType === 'percent') || (prize.couponFixedAmount === '' && prize.couponDiscountType !== 'percent') || (prize.couponPercentAmount > 100 && prize.couponDiscountType === 'percent'))}">
                <label>
                    {{prize.couponDiscountType === 'percent' ? 'نسبة الخصم' : 'قيمة المبلغ'}}
                </label>
                <div class="input-group">
                    <span class="input-group-addon"><i class="sicon-discount-calculator"></i></span>
                    <input :id="`percent_amount_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="ادخل نسبة الخصم" v-model.lazy="prize.couponPercentAmount" v-if="prize.couponDiscountType === 'percent'">
                    <input :id="`price_amount_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="100" v-model.lazy="prize.couponFixedAmount" v-else>
                    <span class="input-group-addon">
                        {{prize.couponDiscountType === 'percent' ? '%' : 'ر.س'}}
                    </span>
                </div>
                <form-error v-if="showErrorMessage && ((prize.couponPercentAmount === '' && prize.couponDiscountType === 'percent') || (prize.couponFixedAmount === '' && prize.couponDiscountType !== 'percent'))">{{requiredField}}</form-error>
                <form-error v-if="showErrorMessage && (prize.couponPercentAmount > 100 && prize.couponDiscountType === 'percent')">{{requiredFieldCouponPercentMaxAmount}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && !prize.couponSelectedCategory.length}">
                <label>التصنيفات المشمولة</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-list"></i>
                    </span>
                    <tree-select
                        placeholder="اختر تصنيفات"
                        :withIcon="true"
                        :options="categories"
                        @change="updateSelectedCategory($event)"
                        @deselect="deselectedCategory($event)"
                        :value="prize.couponSelectedCategory"
                        :normalizer="normalizer"
                        :multiple="true"
                    />
                </div>
                <form-error v-if="showErrorMessage && !prize.couponSelectedCategory.length">{{requiredFieldDropdown}}</form-error>
            </div>
            <div class="form-group" :class="{'has-error': showErrorMessage && prize.couponPurchaseMiniAmount === ''}">
                <label>اقل مبلغ للشراء</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-banknote-dollar"></i>
                    </span>
                    <input :id="`lowest_price_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="ادخل اقل مبلغ للشراء " v-model.lazy="prize.couponPurchaseMiniAmount">
                </div>
                <form-error v-if="showErrorMessage && prize.couponPurchaseMiniAmount === ''">{{requiredField}}</form-error>
            </div>
            <div class="form-group" v-if="prize.couponDiscountType === 'percent'"
                :class="{'has-error': showErrorMessage && prize.couponMaxAmount === '' && prize.couponDiscountType === 'percent'}">
                <label>أقصى قيمة للتخفيض الذي سيحصل عليه العميل</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-banknote-dollar"></i>
                    </span>
                    <input :id="`coupon_max_amount_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="ادخل اقصى مبلغ تخفيض" v-model.lazy="prize.couponMaxAmount">
                </div>
                <form-error v-if="showErrorMessage && prize.couponMaxAmount === '' && prize.couponDiscountType === 'percent'">{{requiredField}}</form-error>
            </div>
            <div class="form-group mb-0" :class="{'has-error': showErrorMessage && prize.couponDiscountImage === null}">
                <filepond
                    :cropAspectRatio="'null'"
                    v-model="prize.couponDiscountImage"
                    :label="'صورة ترمز للجائزة'"
                    :uploader="'prizeCouponDiscountImage'"
                    pond-ref="owner"
                    :finishedUpload="prize.couponDiscountImage !== null"
                    :imgSrc="prize.couponDiscountImage"
                    :uploading="prize.couponDiscountImage == null"
                />
                <form-error v-if="showErrorMessage && prize.couponDiscountImage === null">{{requiredFieldImage}}</form-error>
            </div>
        </div>
        <div v-if="prize.selectedPrize === 'FREE_PRODUCT'">
            <div class="form-group" :class="{'has-error': showErrorMessage && prize.freeProductSelected === null}">
                <label>المنتج المجاني</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-list"></i>
                    </span>
                    <tree-select
                      placeholder="اختر احد المنتجات"
                      :withIcon="true"
                      :value="prize.freeProductSelected"
                      :loadOptions="loadOptions"
                      :async="true"
                      :searchable="true"
                      :with-id="false"
                      valueFormat="object"
                      loadingText="جاري تحميل المنتجات"
                      @change="updateSelectedProduct($event)"
                    />
                </div>
                <form-error v-if="showErrorMessage && prize.freeProductSelected === null">{{requiredFieldDropdown}}</form-error>
            </div>
            <div class="form-group" :class="{'mb-0': prize.freeProductImageUsed !== 'new_image'}">
                <label>صورة المنتج</label>
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
                    <input :id="`current_image_${id}`" type="radio" :name="`product_image_${id}`" value="current" v-model="prize.freeProductImageUsed">
                    <label :for="`current_image_${id}`" class="mb-10">
                        <span>الصورة الحالية</span>
                    </label>
                </div>
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--light-primary">
                    <input :id="`product_image_2_${id}`" type="radio" :name="`product_image_${id}`" value="new_image" v-model="prize.freeProductImageUsed">
                    <label :for="`product_image_2_${id}`">
                        <span>ارفع صورة</span>
                    </label>
                </div>
            </div>
            <div class="form-group mb-0" v-if="prize.freeProductImageUsed === 'new_image'"
            :class="{'has-error': showErrorMessage && (prize.freeProductImage === null && prize.freeProductImageUsed === 'new_image')}">
              <filepond
                  :cropAspectRatio="'null'"
                  v-model="prize.freeProductImage"
                  :label="'صورة ترمز للمنتج الجائزة'"
                  :uploader="'prizeFreeProductImage'"
                  pond-ref="owner"
                  :finishedUpload="prize.freeProductImage !== null"
                  :imgSrc="prize.freeProductImage"
                  :uploading="prize.freeProductImage == null"
              />
              <form-error v-if="showErrorMessage && (prize.freeProductImage === null && prize.freeProductImageUsed === 'new_image')">{{requiredFieldImage}}</form-error>
            </div>
            <!-- needs enhancements
            <div class="form-group mb-0" v-if="prize.freeProductImageUsed !== 'new_image'">
                <img :src="prize.freeProductImage" />
            </div> -->
        </div>
        <div v-if="prize.selectedPrize === 'FREE_SHIPPING'">
            <div class="form-group">
                <div class="rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg">
                    <input type="checkbox" :id="`free_shipping_condition_${id}`" class="product-check" v-model="prize.freeShippingCondition"> 
                    <label :for="`free_shipping_condition_${id}`" class="pt-0">
                        <span class="ml-5 v-align">تطبيق شروط الشحن المجاني</span>
                    </label>
                </div>
            </div>

<!--            <div class="form-group mb-0" :class="{'has-error': showErrorMessage && prize.freeShippingMaxPrice === ''}">
                <label>الحد الأقصى لقيمة الشحن</label>
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="sicon-banknote-dollar"></i>
                    </span>
                    <input :id="`shipping_max_price_${id}`" type="text" class="form-control _parseArabicNumbers" placeholder="ادخل الحد الاقصى لقيمة الشحن" v-model="prize.freeShippingMaxPrice">
                </div>
                <form-error v-if="showErrorMessage && prize.freeShippingMaxPrice === ''">{{requiredField}}</form-error>
            </div>-->
        </div>
    </div>
</template>

<script>

import {mapState, mapMutations, mapActions} from 'vuex';
import LingualField from "@salla.sa/languages/components/LingualField";
import { ASYNC_SEARCH } from '@riophae/vue-treeselect';
import axios from 'axios';

export default {
    components: {
        'LingualField': LingualField,
    },
    props: ['id', 'index'],

    data() {
        return {
            requiredField: 'الرجاء عدم ترك الحقل فارغا',
            requiredFieldDropdown: 'الرجاء الاختيار من القائمة',
            requiredFieldImage: 'الرجاء رفع الصورة',
            requiredFieldCouponPercentMaxAmount: 'الرجاء نسبة الخصم لا تتعدي ال ١٠٠٪',
            products: [],
            normalizer(node) {
                return {
                  label: node.name,
                  value: node.id,
                }
            }
        }
    },
    computed: {
        ...mapState({
            prizesType: state => state.PrizesSettings.prizesType,
            prize: state => state.PrizesSettings.prize,
            showErrorMessage: state => state.PrizesSettings.showErrorMessage,
            categories: state => state.categories.reduce((acc, category) => {
              const push = (item, depth = 0) => {
                // Determine the prefix based on depth
                const prefix = Array(depth).fill('-').join('');

                // Add the item with the prefix
                acc.push({ id: item.id, name: `${prefix} ${item.name}`.trim() });

                // Recursively process sub_categories, increasing the depth
                if (Array.isArray(item.sub_categories) && item.sub_categories.length > 0) {
                  item.sub_categories.forEach(sub => push(sub, depth + 1));
                }
              };

              // Start by pushing the current category at depth 0
              push(category);

              return acc;
            }, []),
            languages: state => state.languages,
            errors: state => state.errors,
        }) 
    },

    watch: {
      'prize.selectedPrize': {
        handler(newVal, oldVal){
          newVal !== oldVal && oldVal !== null && typeof(oldVal) !== 'undefined' ? this.resetPrize(newVal) : null;
        },
      }
    },

    methods: {
        ...mapMutations([
            'updateSelectedPrize', 
            'resetPrize', 
            'updateSelectedCategory',
            'deselectedCategory',
            'updateSelectedProduct']),

        ...mapActions([
        'initLanguages']),

        selectChangeHanlder ($event) {
            this.updateSelectedPrize($event);
            this.initLanguages();
        },

        descriptionUpdated(event) {
            this.prize.description = event.storeLangValue;
            this.prize.translations = event.translations;
        },

        loadOptions ({ action, searchQuery, callback }) {
            if (action === ASYNC_SEARCH) {
                axios.get(`/order/product_search?q=${searchQuery}&without_donating_products=true`)
                .then(res => {
                     const products = res.data.map(i => ({
                        id: i.id,
                        label: i.name,
                    }))
                    callback(null, products)
                })
                .catch(e => {
                     callback(new Error('حدث خطأ ما، رجاءاً حاول مرةً ثانية'));
                });
            }
        }
    }
}

</script>
