<template>
  <div class="panel panel-default panel-small-title">
    <div class="panel-heading sides">
      <div>
        <h6 class="panel-title">النقاط</h6>
        <span class="text-muted text-muted-small d-block">يرجى اضافة طريقة للحصول على النقاط حتى يتم ظهور
              برنامج
            الولاء في متجرك</span>
      </div>

      <button class="btn btn-xs btn--outlined primary" data-toggle="modal" data-target="#points_settings_modal"  :disabled="points.length === pointsType.length" v-if="points.length">
        <i class="sicon-add"></i>
        أضف طريقة جديدة
      </button>
      <span v-if="!initSystem" class="heading-label">اضف طريقة واحدة على الاقل قبل التفعيل</span>
    </div>
    <div class="panel-body no-padding">
      <section-placeholder
        icon="page"
        title="طرق كسب النقاط"
        desc="اضف الطرق التي تريد للعميل أن يحصل على نقاط إذا قام بها، مع تحديد عدد النقاط وتاريخها لكل طريقة."
        btnText="أضف طريقة جديدة"
        modalOpen="#points_settings_modal"
        v-if="points.length === 0"
      />

      <div v-else>
        <!-- pointsApprovedAfter -->
        <div>
          <table class="table table-no-top-border table-borderless">
            <tbody>
            <tr>
              <td>
                <div>
                  <h6 class="m-0">عيّن متى يستحق العميل نقاط الولاء</h6>
                  <span class="text-muted text-muted-small hidden-xs">
                          ستُحسب الأيام المحددة من بعد تغيير حالة الطلب لأي حالة تدل على اكتمال الطلب (تم الشحن، تم التوصيل، جاري التوصيل، تم التنفيذ). افتراضيًا، ستُمنح النقاط فورًا بعد أي من الحالات المذكورة.
                        </span>
                  <div class="input-group mt-15">
                    <span class="input-group-addon">
                      <i class="sicon-list" />
                    </span>
                    <tree-select
                      :multiple="true"
                      :options="ordersStatuses"
                      :searchable="true"
                      placeholder="اختر حالات الطلب"
                      :with-id="true"
                      :value="generalSettings.completed_orders_statuses"
                      @change="handleStatusesChange"
                      @deselect="handleStatusesDeselect"
                    />
                  </div>
                </div>
              </td>
              <td style="vertical-align: bottom">
                <div class="form-group days f-left mb-0">
                  <div class="input-group text-right">
                    <input
                      id="points_approved_after"
                      v-model="generalSettings.pointsApprovedAfter"
                       style="border-right-style: solid;border-right-color: var(--color-gray-200);border-right-width: 1px;padding: 0 !important;text-align: center;" type="text" class="form-control full-bordered inline-field pl-15 pr-15 _parseArabicNumbers" placeholder="0" maxlength="3">
                    <span class="input-group-addon">يوم</span>
                  </div>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div>
                  <h6 class="m-0">صلاحية النقاط</h6>
                  <span class="text-muted text-muted-small hidden-xs">حدد هنا صلاحية النقاط بالأيام من لحظة وصولها إلى العميل.</span>
                </div>
              </td>
              <td class="text-right">
                <div class="form-group rec-list rec-list--horizontal nowrap mb-0 f-left">
                  <input id="date_value" type="text" class="form-control full-bordered inline-field _parseArabicNumbers border-left-none" placeholder="0" maxlength="3" v-model="generalSettings.pointsValidityTimePeriodValue">
                  <tree-select
                    class="inline-field"
                    placeholder="يوم"
                    :options="timePeriodTypes"
                    @change="pointsValidityTimePeriodChangeHandler"
                    :value="generalSettings.pointsValidityTimePeriod"
                  />
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="table-responsive">
          <table class="table table-less-padding table-medium-text">
            <thead>
            <tr class="active">
              <th>الأسم</th>
              <th>عدد النقاط</th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            <tr class="table-row" v-for="(point, index) in points" :key="index">
              <td data-title="الأسم">
                <a @click="setType(point)" data-toggle="modal" :data-target="`#points_settings_modal_${index}`"
                   :class="{'text-danger': errors !== null && errors[`points.${index}.selectedChannel`]}">
                  {{pointName(point)}}
                </a>
              </td>
              <td data-title="عدد النقاط">
                              <span>
                                  {{pointValue(point)}} نقطة
                              </span>
              </td>
              <td class="text-right">
                <toggle @change="pointsActiveStatus({index: index, statues: $event})" :value="point.active"/>
              </td>
              <modal :id="`points_settings_modal_${index}`"
                     modalTitle="إضافة طرق الحصول على النقاط"
                     saveBtn="تعديل"
                     :showDelete="true"
                     @closeModal="resetType"
                     @saveModal="edit(index)"
                     @delete="deleteHandler(index)">
                <points-customize :id="`edit_${index}`" :index="index"/>
              </modal>
            </tr>
            </tbody>
          </table>
        </div>
      </div>

      <modal id="points_settings_modal" modalTitle="إضافة طرق الحصول على النقاط" :disabled="type.selectedPoint === null || typeof(type.selectedPoint) === 'undefined'" @closeModal="resetType" @saveModal="save">
        <points-customize id="create"/>
      </modal>
    </div>
  </div>
</template>

<script>

import {mapState, mapMutations, mapActions} from 'vuex';

export default {
  computed: {
    ...mapState({
      points: state => state.PointsSettings.points,
      type: state => state.PointsSettings.type,
      timePeriodTypes: state => state.timePeriodType,
      pointsType:state => state.PointsSettings.pointsType,
      generalSettings: state => state.generalSettings,
      errors: state => state.errors,
      showErrorMessage: state => state.PointsSettings.showErrorMessage,
      initSystem: state => state.initSystem,
      ordersStatuses: state => state.ordersStatuses.map(({ order_status_id, name }) => ({ id: order_status_id, label: name })),
    }),
  },

  methods: {
    ...mapMutations([
      'addPoint',
      'resetType',
      'setType',
      'pointsActiveStatus',
      'deletePoint',
      'editPoint',
      'pointsFieldsValidation']),
    ...mapActions(['saveModal']),

    pointName (name) {
      const earnPointTypes = {
        AFFILIATE_LINK:'نشر رابط',
        COMPLETE_PERSONAL_INFORMATION:'اكمال البيانات الشخصية',
        BUY_FROM_STORE:'الشراء من المتجر',
        RATING: 'التقييم',
        FIRST_ORDER_STORE: 'أول طلب من المتجر',
        ORDER_FROM_POS: 'الطلب من خلال نظام نقاط البيع',
        ORDER_FROM_APP: 'الطلب من خلال التطبيق'
      }
      return earnPointTypes[name.selectedPoint] || ''
    },

    pointValue (values) {
      let totalValue = values.purchaseType=== 'total_order'? values.pointsPerAmountUnit :values.pointsPerFixedAmount,
        completeInfoVal = Number(values.birthDatePoints) + Number(values.emailPoints) + Number(values.genderPoints) + Number(values.phonePoints),
        ratingVal =  Number(values.storeRatingPoints) + Number(values.productRatingPoints) + Number(values.shippingRatingPoints)

      const selectedPointObject = {
        AFFILIATE_LINK:values.sharePoints,
        COMPLETE_PERSONAL_INFORMATION:completeInfoVal,
        RATING: ratingVal,
        BUY_FROM_STORE:totalValue,
        FIRST_ORDER_STORE:totalValue,
        ORDER_FROM_POS: totalValue,
        ORDER_FROM_APP: totalValue
      }
      return selectedPointObject[values.selectedPoint] || 0

    },

    save () {
      this.pointsFieldsValidation();
      if (this.showErrorMessage === false) {
        this.addPoint();
        this.resetType();
      window?.parent?.postMessage({ event: "overlay.open" }, "*");}
    },

    edit (index) {
      this.pointsFieldsValidation();
      if (this.showErrorMessage === false) {
        this.editPoint(index);
        this.saveModal();
        this.resetType();
      }
    },

    deleteHandler (index) {
      let self = this;
      swal({
        title: '<h2>'+ 'تحذير' +'</h2>',
        html:'<p>' + 'هل انت متاكد من حذف النقطة' + '</p>',
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: '#d33',
        confirmButtonText: 'موافق !',
        cancelButtonText: '<i class="fa fa-close"></i> إلغاء'
      })
        .then(function () {
          self.deletePoint(index);
          self.resetType();
        });
    },

    pointsValidityTimePeriodChangeHandler ($event) {
      this.generalSettings.pointsValidityTimePeriod = $event;
    },

    handleStatusesChange(selected) {
      this.generalSettings.completed_orders_statuses.push(selected);
    },
    handleStatusesDeselect(deselected) {
      this.generalSettings.completed_orders_statuses =
        this.generalSettings.completed_orders_statuses.filter(i => i !== deselected);
    }
  }
}
</script>

<style>
/*we might need to make this a component for multiple use*/
.form-control.inline-field {
  width: 40px;
}
.vue-treeselect__single-value {
  padding-right: 10px !important;
  padding-left: 0 !important;
  border-right: 1px solid #eee;
}

.vue-treeselect.inline-field {
  width: 70px;
}

.form-group.field .help-block {
  width: 60px;
}
</style>
