<template>
    <div class="color-picker"></div>
</template>

<script>
    import '@simonwep/pickr/dist/themes/nano.min.css'; 
    import Pickr from '@simonwep/pickr';
    import $notify from "../../../utils/notify";

    export default {
        name: "ColorPicker",
        props: {
            value: {
                type: String,
                default: '#000000'
            },
            exclude_color: {
                type: String,
                default: ''
            }
        },

        data () {
            return {
                pickr: null,
            };
        },

        watch: {
            value(val, oldVal) {
                this.pickr.setColor(val)
            }
        },

        mounted() {
            this.pickr = Pickr.create({
                el: '.color-picker',
                theme: 'nano',
                swatches: [
                    'rgba(244, 67, 54, 1)',
                    'rgba(233, 30, 99, 0.95)',
                    'rgba(156, 39, 176, 0.9)',
                    'rgba(103, 58, 183, 0.85)',
                    'rgba(63, 81, 181, 0.8)',
                    'rgba(33, 150, 243, 0.75)',
                    'rgba(3, 169, 244, 0.7)'
                ],
                default: this.value,
                defaultRepresentation: 'HEXA',
                position: 'bottom-start',
                components: {
                    preview: true,
                    opacity: true,
                    hue: true,
                    interaction: {
                        hex: false,
                        rgba: false,
                        hsva: false,
                        input: true,
                        clear: false,
                        save: true
                    }
                },
                 i18n: {
                    'btn:save': 'حفظ',
                }

            }).on('init', instance => {
                instance.setColor(this.value);
            }).on('save', (color, instance) => {
                if (this.exclude_color !== '' && color.toHEXA().toString().startsWith(this.exclude_color)) {
                    instance.setColor('#000000');
                    $notify('اللون غير متاح، الرجاء اختيار لون اخر.');
                } else {
                    this.$emit('input', color.toHEXA().toString());
                    instance.hide();
                }
            });

        }
    }
</script>
