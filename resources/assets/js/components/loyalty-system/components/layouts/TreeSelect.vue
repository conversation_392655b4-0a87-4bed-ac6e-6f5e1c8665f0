<template>
    <treeselect
        :multiple="multiple"
        :options="options"
        :searchable="searchable"
        class="vue-treeselect--custom vue-treeselect--without-effect"
        :class="{'vue-treeselect--with-icon': withIcon, 'vue-treeselect--enhanced-tags': multiple}"
        :placeholder="placeholder"
        @select="$emit('change', withId ? $event.id : $event)"
        @deselect="$emit('deselect' ,withId ? $event.id : $event)"
        :value="value"
        :normalizer="normalizer"
        :load-options="loadOptions"
        :async="async"
        noResultsText="لا توجد نتائج"
        noOptionsText="لا توجد خيارات"
        noChildrenText="لا توجد خيارات فرعية"
        retryText="حاول ثانية؟"
        retryTitle="اضغط هنا للمحاولة ثانيةً"
        searchPromptText="اكتب للبحث"
        :loadingText="loadingText"
        :valueFormat="valueFormat"
    />
</template>

<script>

import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
    props: {
        multiple: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: []
        },
        searchable: {
            type: Boolean,
            default: false
        },
        withIcon: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: ''
        },
        value: {
            default: null
        },
        loadOptions: {
            type: Function,
            default: null
        },
        async: {
            type: Boolean,
            default: false
        },
        loadingText: {
            type: String,
            default: null
        },
        valueFormat: {
            type: String,
            default: 'id'
        },
        withId: {
            type: Boolean,
            default: true
        },
        normalizer: {

        },
    },
    components: {
        Treeselect
    },
}
</script>