<template>
    <div class="rec-placeholder p-20">
        <h2 class="rec-placeholder__title font-16">{{title}}</h2>
        <p class="rec-placeholder__desc font-13 narrow-width">{{desc}}</p>
        <button class="btn btn-xs btn--outlined primary" data-toggle="modal" :data-target="modalOpen">
            {{btnText}}
        </button>
    </div>
</template>

<script>
    export default {
        props: ['title', 'desc', 'btnText', 'modalOpen']
    }
</script>

