<template>
    <button :data-icon="value" class="btn btn-default icon_picker d-block iconpicker no-border mr-10 no-padding" ref="picker" role="iconpicker"></button>
</template>

<script>
    export default {
        name: "IconPicker",
        props: {
            value: {
                type: String,
                default: 'sicon-store'
            }
        },

        data () {
            return {
                picker: null,
            };
        },

        watch: {
            value(val, oldVal) {
                this.picker.iconpicker('setIcon', val)
            }
        },

        mounted() {
            let self = this;

            this.picker = $(this.$refs.picker).iconpicker({
                iconset: {
                    iconClass: '',
                    iconClassFix: 'sicon-',
                    icons: [
                        'store', 'keypad', 'store2', 'cart', 'cart2', 'bell-ring', 'user-circle', 'shopping-bag',
                        'shopping-basket', 'shopping', 'dollar-cash', 'bag-dollar', 'banknote-dollar',
                        'crdit-card-alt', 'full-wallet', 'discount-coupon', 'special-discount', 'ticket-movie',
                        'headset', 'phone', 'iphone', 'help', 'love-letter',
                        'envelope', 'mail', 'mailbox', 'location', 'gift',
                        'map-grid', 'map-location', 'map-search', 'map', 'location-target',
                        'shipping', 'shipping-fast', 'watch', 'fitness-watch', 'flag-wave',

                        'fire', 'fork-knife', 'rocket', 'activity', 'airplane-mode',
                        'alarm-check', 'album-photo', 'archive', 'art-palette', 'back',
                        'bag-dollar', 'battery-charge', 'bell', 'binary', 'binoculars',
                        'birdhouse', 'boat', 'books-alt', 'box', 'braille-hand',
                        'briefcase-health', 'briefcase', 'cake', 'calendar-check', 'calendar-heart',
                        'calendar-time', 'calendar', 'camcorder', 'camera', 'car-alt',
                        'car-key', 'cctv-camera', 'chat-alert-alt', 'chat-bubbles-alt', 'chat-bubbles',

                        'chat-conversation', 'check-circle', 'check-square', 'check', 'chip',
                        'city', 'clapboard', 'cloud-check', 'coffee-togo', 'computer',
                        'contact-card', 'content', 'crown', 'cup-hot', 'cut',
                        'dashboard-high', 'device-image', 'devices', 'directional-pad', 'earth',
                        'edit', 'energy-circle', 'explode', 'favorite', 'feather-pen',
                        'file-download', 'HD-square', 'wrenches', 'hashtag', 'hammer-screwdriver',
                        'group', 'graduation-cap', 'gameboy', 'game-controller-alt', 'frame-image',

                        'double-tap', 'heart-arrow', 'hiking', 'history', 'house',
                        'image', 'journal', 'leaf-angle', 'lightbulb', 'macro',
                        'dress-long-sleeve', 'shirt', 'shirt-button-down', 't-shirt-long-sleeve', 'users',
                        'man-fancy', 'user-heart', 'turkey', 'trophy2', 'time',
                        'medal', 'theater-masks', 'plug-electric', 'whatsapp2', 'instagram2',
                        'happy', 'cool', 'face-id', 'doh', 'meh', 'frown',
                        'dead-mask', 'wheelchair-alt', 'file-download', 'forward',
                        
                        'calendar-cancel', 'refund', 'gold-badge', 'party-horn', 'packed-box', 'wallet',
                    ],
                },
                arrowClass: 'btn-default',
                arrowPrevIconClass: 'sicon-keyboard_arrow_left',
                arrowNextIconClass: 'sicon-keyboard_arrow_right',
                cols: 5,
                footer: false,
                header: true,
                rows: 7,
                search: false,
                selectedClass: 'btn-success',
                unselectedClass: 'btn-default'
            });

            $(this.$refs.picker).on('change', function (e) {
                self.$emit('input', e.icon);
            });
        }
    }
</script>
