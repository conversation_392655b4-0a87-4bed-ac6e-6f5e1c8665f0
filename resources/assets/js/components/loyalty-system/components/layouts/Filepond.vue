<template>
    <div>
      <label>
          {{label}}
          <span class="text-muted text-muted d-block mb-0 d-block font-12">اختر صورة مناسبة لتظهر في واجهة صفحة الولاء بابعاد لاتقل عن 1200 بكسل عرض في 250 بكسل ارتفاع</span>
        </label>
      <div>
          <file-pond
              v-bind:imageEditEditor="imageEditEditor"
              :ref="pondRef"
              label-idle='اسحب الصورة وأفلتها هنا <span class="filepond--label-action"> او تصفح من جهازك </span>'
              maxFileSize="1MB"
              labelMaxFileSizeExceeded="حجم الصورة كبير جداً"
              labelMaxFileSize="حجم الملف كبيرا الحد الاقصى هو {filesize}"
              labelFileTypeNotAllowed="نوع الملف غير صالح"
              fileValidateTypeLabelExpectedTypes="يجب ان يكون {allButLastType} او {lastType}"
              v-on:init="handleFilePondInit"
              @addfilestart="handleFilePondStart"
              maxFiles="1"
              :imageEditInstantEdit="! hasFile"
              :server="withServer"
              :files="files"
              :allowImageResize="true"
              :allowImagePreview="true"
              :allowImageEdit="true"
              accepted-file-types="image/jpg, image/jpeg, image/png"
              v-on:processfile="onprocessfile"
              v-on:preparefile="onpreparefile"
              v-on:updatefiles="onupdatefiles"
              v-on:addfile="onaddfile"
              v-on:removefile="deleteImage"
          />
        <small class="text-muted text-muted-small">
          يسمح بصورة فقط من نوع jpg, jpeg, png*
        </small>
        </div>
    </div>
</template>

<script>
import $notify from '../../../utils/notify';
import Http from "../../../utils/http";
import {create} from '../../../entity/plugins/doka/vue/esm/lib/doka.esm.min';
import vueFilePond from "vue-filepond";
import "filepond/dist/filepond.min.css";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImageEdit from "filepond-plugin-image-edit";
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import "filepond-plugin-image-edit/dist/filepond-plugin-image-edit.css";
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginImageCrop from "filepond-plugin-image-crop";
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginFileEncode from 'filepond-plugin-file-encode';
import FilePondPluginFileMetadata from 'filepond-plugin-file-metadata';
import FilePondPluginImageFilter from 'filepond-plugin-image-filter';
import FilePondPluginFilePoster from 'filepond-plugin-file-poster';
import 'filepond-plugin-file-poster/dist/filepond-plugin-file-poster.css';
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation';
import FilePondPluginFileRename from 'filepond-plugin-file-rename';
import FilePondPluginImageResize from "filepond-plugin-image-resize";
import FilePondPluginImageValidateSize from 'filepond-plugin-image-validate-size';
import FilePondPluginImageTransform from "filepond-plugin-image-transform";

const options = require('../../../utils/DokaOptions').default;

// Create component
const FilePond = vueFilePond(
    FilePondPluginImageExifOrientation,
    FilePondPluginImagePreview,
    FilePondPluginFileValidateType,
    FilePondPluginImageEdit,
    FilePondPluginImageCrop,
    FilePondPluginImageResize,
    FilePondPluginImageTransform,
    FilePondPluginFileRename,
    FilePondPluginImageFilter,
    FilePondPluginImageValidateSize,
    FilePondPluginFilePoster,
    FilePondPluginFileMetadata,
    FilePondPluginFileValidateSize,
    FilePondPluginFileEncode,
);

import {mapState} from 'vuex';

export default {
  props: {
    value: [Blob, String],
    width: {
      type: String,
      default: null
    },
    height: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: 'img-circle'
    },
    cropAspectRatio: {
      type: String,
      default: '1'
    },
    label: {
      type: String,
      default: null
    },
    uploader: {
      type: String,
      default: null
    },
    finishedUpload: {
      type: Boolean,
      default: false
    },
    imgSrc: {
      type: String,
      default: null
    },
    uploading: {
      type: Boolean,
      default: true
    },
    pondRef: {
      type: String,
      default: 'pond'
    }
  },
  data() {
    return {
      isFirstLoad: true,
      src: '',
      result: null,
      withImageEditInstantEdit: null,
      enabled: false,
      fileName: 'حمل الصورة',
      uploaded: false,
      doka: options,
      server: {
        process:(fieldName, file, metadata, load, error, progress, abort, transfer, options) => {
          this.result = file;
          const formData = new FormData();
          $.each(window.s3BrowserBasedUploads.fields, function(index, value) {
            formData.append(index, value);
          });
          const _uid = Math.random().toString(36).replace(/[^a-z]+/g, '').substr(2, 10);;

          formData.append('Content-Type', this.result.type);
          formData.append('file', this.result, _uid + '.' +  this.result.name.split('.').pop());

          showLoading();
          this.$root.$emit('image-uploading', this.result);
          let self = this;

          axios({
            method: 'POST',
            url: window.s3BrowserBasedUploads.endpoint_url,
            data: formData,
            config: {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              onUploadProgress: (event) => {
                progress(event.lengthComputable, event.loaded, event.total);
              }
            }
          }).then(function (response) {
            self.$emit('input', $(response.data).find('Location').text());
            self.createS3UploadToken();
            hideLoading();
            load(response);
          }).catch(({response}) => {
            let message = $(response.data).find('Message').text() || '';
            if (this.mappedMessage[message]) {
              message = this.mappedMessage[message]
            }
            $notify('فشل الرفع، الرجاء المحاولة مرة أخرى.', message);

            self.result = null;
            self.createS3UploadToken();
            self.setDefaultImage();
            hideLoading();
            error(message);
          }).then(() => {
            this.$root.$emit('image-uploaded');
          });
        },
        load: (source, load, error, progress, abort, headers) => {
          // let's fetch the image from s3
          fetch(new Request(source)).then(function(response) {
            response.blob().then(function(myBlob) {
              load(myBlob)
            });
          });
        },
        revert: null
      },
      imageEditEditor: create({...{
          onloaderror: this.handleDokaLoadError,
          oncancel: this.handleDokaClose,
          onconfirm: this.handleDokaClose,
        },...options}),
      mappedMessage: {
        'Your proposed upload exceeds the maximum allowed size': 'حجم الصورة كبير جداٌ كحد أقصى 1MB يرجى تقليل الصورة',
        'Invalid according to Policy: Policy Condition failed: ["starts-with", "$Content-Type", "image/"]': 'يسمح بصورة فقط من نوع jpg,jpeg,png',
      }
    };
  },
  created() {
    this.setDefaultImage();
  },
  computed: {
    ...mapState({
      generalSettings: state => state.generalSettings,
      prize: state => state.PrizesSettings.prize,
    }),
    getImageSrc() {
      if (this.result !== null) {
        return this.toUrl(this.result);
      }
      if (this.value !== null) {
        return this.toUrl(this.value);
      }
      if(this.imgSrc != null) {
        return this.toUrl(this.imgSrc);
      }

      return this.src;
    },
    files() {
      if (! this.hasFile || ! this.getImageSrc) {
        //console.log('no file for' + this.pondRef);
        return []
      }

      return [{
        source: this.getImageSrc,
        options: {
          type: "local"
        }
      }];
    },
    hasFile() {
      return this.withImageEditInstantEdit || ! [undefined, null, ''].includes(this.getImageSrc);
    },
    withServer() {
      //! this.hasFile
      return this.server
    }
  },
  components: {
    'DokaModal': require('../../../entity/plugins/doka/vue/esm/index').DokaModal,
    FilePond
  },
  mounted() {
    window.addEventListener("beforeunload", this.clearSessionStorage);
  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", this.clearSessionStorage);
  },
  methods: {
    clearSessionStorage() {
      sessionStorage.removeItem("isFirstLoad");
    },
    handleDokaClose(){
      this.enabled = false;
      this.setDefaultImage();
      window.parent.postMessage({ event: 'doka.close' }, '*');
    },
    handleDokaLoadError(image){
      if (image.status === 'IMAGE_MIN_SIZE_VALIDATION_ERROR' && image.hasOwnProperty('data') && image.data.hasOwnProperty('minImageSize')) {
        $notify(this.doka.labelStatusLoadImageError + ' الطول:' + image.data.minImageSize.height + ' العرض:' + image.data.minImageSize.width, '');
      } else {
        $notify(this.doka.labelStatusImageError + ' (' + image.status + ')', '');
      }
      this.enabled = false;
      this.setDefaultImage();
      window.parent.postMessage({ event: 'doka.close' }, '*');
    },
    openInputFile(event) {
      $(event.target).parent().find('input[type="file"]').click();
    },
    inputFile(e) {
      this.src = this.toUrl(e.target.files[0]);
      this.fileName = e.target.files[0].name;
      this.uploaded = true;
      this.enabled = true;
    },
    toUrl(src) {
      return src instanceof Blob ? URL.createObjectURL(src) : src;
    },
    setDefaultImage() {
      this.src = '';
      this.fileName = 'حمل صورة الحملة...';
      this.uploaded = false;
    },
    createS3UploadToken() {
      return new Promise((resolve, reject) => {
        Http.get(window.s3BrowserBasedUploads.refresh_credentials_url, ({ data }) => {
          window.s3BrowserBasedUploads.fields = data.fields;
          resolve(data)
        }, error => reject(error))
      })
    },
    deleteImage (error, file) {
      this.withImageEditInstantEdit = false;
      this.src = '';
      if (this.uploader === 'generalSettings') {
        this.generalSettings.image = null;
      } else if (this.uploader == 'prizeCouponDiscountImage') {
        this.prize.couponDiscountImage = null;
      } else {
        this.prize.freeProductImage = null;
      }
    },
    handleFilePondInit: function() {
      //console.log('FilePond has initialized as ', this.pondRef);

      // FilePond instance methods are available on `this.$refs.this.pondRef`
    },

    handleFilePondStart: function() {
      // FilePond instance methods are available on `this.$refs.this.pondRef`
      if (sessionStorage.getItem("isFirstLoad") === null) {
        sessionStorage.setItem("isFirstLoad", "false");
        return;
      }
      window.parent.postMessage({ event: 'doka.open' }, '*');
    },

    onprocessfile(e, file) {
      // console.log('onprocessfile ', e, file);
    },
    onpreparefile(e, file) {
      // console.log('onpreparefilee', e, file);
    },
    onupdatefiles(files) {
      // console.log('onupdatefiles', files);
    },
    onaddfile(e, file) {
      // console.log('onaddfile', e, file);
    }
  }
}
</script>