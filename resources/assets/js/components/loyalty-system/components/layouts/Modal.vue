<template>
    <div :id="id" class="modal fade modal-backup" data-backdrop="static">
        <div class="modal-dialog" style="direction: rtl;">
            <div class="modal-content">
                <div class="modal-header bg-info">
                    {{modalTitle}}
                    <button type="button" @click="$emit('closeModal')" class="close" data-dismiss="modal">×</button>
                </div>
                <div class="modal-body">
                    <slot></slot>
                </div>
                <div class="modal-footer no-icons">
                    <button type="button" @click="$emit('saveModal', id)" :disabled="disabled" class="btn btn-info btn-save">{{saveBtn}}</button>
                    <button class="btn btn-info btn-close" @click="$emit('closeModal')" data-dismiss="modal" v-if="!showDelete">{{closeBtn}}</button>
                    <button class="btn btn-info btn-danger" @click="$emit('delete')" v-else>حذف</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        id: {
            type: String,
            default: '',
        },
        modalTitle: {
            type: String,
            default: '',
        },
        saveBtn: {
            type: String,
            default: 'إضافة',
        },
        closeBtn: {
            type: String,
            default: 'خروج',
        },
        disabled: {
            type: Boolean,
            default: false
        },
        showDelete: {
            type: Boolean,
            default: false
        }
    }
}
</script>