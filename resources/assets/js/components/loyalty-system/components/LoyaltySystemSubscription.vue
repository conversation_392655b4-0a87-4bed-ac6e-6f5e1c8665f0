<template>
  <div class="panel feature-promo has-ribbon has-ribbon--lg has-ribbon--left" :data-ribbon-title="`${299} ر.س/شهريا`">

    <div class="panel-body p-20 align-center">
      <article class="rec-article feature-promo__heading mb-50">
        <h2 class="font-30 text-bold mb-20 mt-0 lh-1">نظام الولاء</h2>
        <p class="font-18 align-center m-0">روُج لمتجرك بطريقة يحبها عملاءك </p>
      </article>
      <div class="feature-promo__marketing mb-40">
        <div>
          <article class="rec-article mb-20">
            <p class="font-16">
              نظام ولاء متكامل خاص بمتجرك يقدم لعملائك نقاط يمكنهم استبدالها بمكافآت محددة من قبلك، مقابل عمليات الشراء
              وتقييم المتجر وإكمال الملف الشخصي ومشاركة رابط متجرك لأهلهم وأصدقائهم.
              <br/>
              يمكن للعملاء استبدال النقاط بمنتج من منتجاتك أو كوبونات خصم أو الحصول على شحن مجاني،
              بتحكم كامل منك في عدد النقاط المطلوبة لكل مكافأة وكيفية تحصيلها ومدة صلاحيتها،
              مع تقارير تفصيلية توضح كيفية استثمار عملائك لنقاط الولاء وأكثرهم جمعًا للنقاط وغيرها.
            </p>
          </article>
          <div v-if="!isIos" class="feature-promo__actions">
            <button class="btn btn-tiffany btn-xlg font-15" @click="openAppLink">اشترك</button>
          </div>
        </div>
        <div>
          <div class="feature-promo__badge hide-laptop">
            <h5 class="text-nowrap lh-1 font-15 m-0">
              3X
              مبيعات أكثر&ensp;🤩
            </h5>
          </div>
          <img src="/cp/assets/images/loyal-system.svg" class="wide" alt="loyal-system">
        </div>
      </div>
      <div class="grid-block swiper-wrapper feature-promo__features">
        <div v-for="(feature, index) in appFeatures" :key="index" class="feature-promo__feature">
          <h3 class="rec-flex align-right">
            <i class="valign-middle" :class="feature.icon"></i>
            <span>{{ feature.title }}</span>
          </h3>
          <p class="no-margin font-13 align-right">{{ feature.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: ['storePlan', 'isIos'],
  data() {
    return {
      isSafari: /^((?!chrome|android).)*safari/i.test(navigator.userAgent), // To pervent double click on the button
      appFeatures: [
        {
          title:'عزز ولاء عملاءك',
          icon:'sicon-user-heart',
          description: 'قدم مكافات لعملاءك  تحفزهم على الاستمرار بالشراء من متجرك'
        },
        {
          title: 'تحكم كامل',
          icon: 'sicon-tune',
          description: 'تحكم بكامل تفاصيل نظام الولاء ومكافآته وإشعاراته'
        },
        {
          title: 'تقارير تفصيلية',
          icon: 'sicon-content',
          description: 'تقارير تفصيلية للنقاط والجوائز لتسهيل إدارتك لنظام الولاء'
        },
        {
          title: 'تذكيرات تلقائية',
          icon: 'sicon-bell',
          description: 'ذكر عملاءك للاستفادة من نقاط ولائهم تلقائياً'
        },
      ],
    }
  },

  created() {

  },

  mounted: () => {
      const items = [
          {label: 'الادوات التسويقيه'},
          {label: 'نظام الولاء'},
      ]
      window.parent?.postMessage({
      event: 'breadcrumb', data: items
      }, '*')
  },

  computed: {
  },

  methods: {
    openAppLink() {
      if (this.storePlan === "basic") {
        swal({
          title: 'تنبيه',
          text: 'هذا المنتج غير متوافق مع الباقة الخاصة بك',
          type: 'error',
          showConfirmButton: false,
          timer: 4000
        });
      }
      else {
        window.parent?.postMessage({ event: "navigateTo", path: "https://apps.salla.sa/ar/app/1178176509" }, "*");
        if(sallaLegacy) {
          const urlLink = "https://apps.salla.sa/ar/app/1178176509";
          window.open(urlLink, "_self") ? window.open(urlLink, "_self") :
              window.location.href = urlLink
        }
      }
    },
  }
}
</script>
