<template>
  <div class="form-group mb-0">
    <DatePicker
      ref="datepicker"
      v-model="defaultValue"
      value-type="format"
      :clearable="false"
      title="نهاية التخفيض"
      placeholder="نهاية التخفيض (اختياري)"
      input-class="form-control"
      :append-to-body="false"
       :popup-style="{ left: 0, top: '100%' }"
      @change="handleChange"

    />
  </div>
</template>

<script>
import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/locale/ar-sa'
import 'vue2-datepicker/index.css'

export default {
  name: 'DatePickerEditor',
  components: { DatePicker },
  props: ['rowIndex', 'prop', 'model', 'close', 'save'],
  data: function () {
    return {
      defaultValue: undefined,
    }
  },

  mounted() {
    this.initValue()
  },

  updated() {
    // this.initValue()
  },
  
  beforeDestroy() {},
  destroyed() {
    // update row once user confirm
    this.save(this.defaultValue)
  },

  methods: {

    initValue() {
      this.defaultValue = this.model[this.prop]
    },
    handleChange(value) {
      this.defaultValue = value
    },
  },
}
</script>

<style scoped>
.rec-checkbox {
  margin: auto !important;
}
</style>
