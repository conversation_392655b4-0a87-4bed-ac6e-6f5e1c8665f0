<template>
  <div class="checkbox-wrapper full-width" :class="{'not-allowed': model.type === 'sku' ? true : false}">
      <div
      class="
        rec-checkbox
        rec-checkbox--default
        rec-checkbox--large
        rec-checkbox--solo
        rec-checkbox--primary-bg
      "
      :class="{'no-action': model.type === 'sku' ? true : false}"
      @click="handleChange"
    >
      <input
        ref="editorCheckbox"
        type="checkbox"
        name="checked"
        :checked="checked"
      />
      <label></label>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RadioboxEditor',
  props: ['rowIndex', 'prop', 'model', 'close', 'save'],
  data: function () {
    return {
      checked: false,
    }
  },

  mounted() {
    this.initValue();
  },

  updated() {},
  beforeDestroy() {},
  destroyed() {
    // update row once user confirm
    this.save(this.checked)
  },

  methods: {
    initValue() {
      this.checked = this.model[this.prop]
    },
    handleChange() {
      this.checked = !this.$refs.editorCheckbox.checked
    },
  },
}
</script>

<style scoped>
.rec-checkbox {
  margin: auto !important;
}

.not-allowed {
  cursor: not-allowed;
}
</style>
