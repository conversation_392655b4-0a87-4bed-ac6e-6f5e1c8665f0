<template>
  <InputEditor
    type="number"
    :row-index="rowIndex"
    :prop="prop"
    :model="model"
    :close="close"
    :save="save"
    :disabled="model.unlimited_quantity || 
               model.has_skus || 
               (model.product_type !== 'food' && prop === 'calories') ||
               (model.product_type !== 'food' && prop === 'calories') || 
               ((model.product_type === 'food' || model.product_type === 'service') && (prop === 'mpn' || prop === 'gtin'))"
  />
</template>

<script>
import InputEditor from '../input'
export default {
  name: 'NumbersInputEditor',
  components: {
    InputEditor,
  },
  props: ['rowIndex', 'prop', 'model', 'close', 'save'],
  data: function () {
    return {
      defaultValue: null,
    }
  },
}
</script>
