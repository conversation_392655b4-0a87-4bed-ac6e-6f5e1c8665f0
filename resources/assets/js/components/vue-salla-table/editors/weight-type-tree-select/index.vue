<template>
  <div dir="rtl" class="form-group mb-0">
    <Treeselect
      v-model="defaultValue"
      value-format="object"
      :multiple="false"
      :flat="false"
      :searchable="false"
      :show-count="false"
      :append-to-body="true"
      :default-expand-level="2"
      :clearable="false"
      :close-on-select="false"
      :loading-text="'جاري جلب البيانات...'"
      :no-children-text="'...'"
      :is-default-expanded="true"
      :clear-on-select="false"
      class="
        vue-treeselect--custom
        vue-treeselect--enhanced-tags
        vue-treeselect--without-effect
        vue-treeselect--full
      "
      placeholder="نوع الوزن"
      :options="weight_types"
      :normalizer="normalizer"
      :disabled="!model.can_has_weight"
      @close="handleChange"
    >
      <label slot="option-label" slot-scope="{ node }">
        {{ node.label }}
      </label>
    </Treeselect>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import ProductService from '../../../../services/ProductService'

export default {
  name: 'WeightTypeTreeSelectEditor',
  components: { Treeselect },
  props: ['rowIndex', 'prop', 'model', 'close', 'save'],
  data: function () {
    return {
      defaultValue: undefined,
      normalizer(node) {
        return {
          label: node.name,
          value: node.id,
        }
      },
      weight_types: [],
    }
  },

  mounted() {
    this.weight_types = ProductService.getDatum('weight_types');
console.log('weight_types');
console.log(this.weight_types);
    this.initValue()
  },

  updated() {
    // this.initValue()
  },
  beforeDestroy() {},
  destroyed() {
    // update row once user confirm
    this.save(this.defaultValue)
  },

  methods: {
    initValue() {
      this.defaultValue = this.model[this.prop]
    },
    handleChange(value) {
      this.defaultValue = value
    },
  },
}
</script>

<style>
.vue-treeselect--custom .vue-treeselect__control {
  height: 100%;
  border-color: #fff !important;
}
</style>
