<template>
  <div class="form-group mb-0">
    <input
      ref="inputEditor"
      :value="defaultValue"
      name="defaultValue"
      autofocus
      class="form-control"
      :type="type"
      :maxlength="inputLength"
      @focus="$event.target.select()"
      @keyup.enter="onEnter"
      @keyup="handleNumberMaxLength"
      :disabled="disabled || ((model.product_type === 'food' || model.product_type === 'service') && (prop === 'mpn' || prop === 'gtin'))"
    />
  </div>
</template>

<script>
import isEqual from 'lodash.isequal'
let timer

export default {
  name: 'InputEditor',
  props: ['rowIndex', 'prop', 'model', 'close', 'save', 'type', 'disabled'],
  data: function () {
    return {
      defaultValue: null,
    }
  },
  computed: {
    inputLength() {
      return this.prop === 'subtitle' ? 35 : (this.prop === 'promotion_title' ? 25 : '');
    },
  },
  mounted() {
    this.autoFocus()
    this.initValue()
  },

  updated() {},
  beforeDestroy() {},
  destroyed() {
    // update row once user confirm
    this.handleUpdate()
    clearTimeout(timer)
  },

  methods: {
    initValue() {
      this.defaultValue = this.model[this.prop]
    },
    handleChange(e) {
      this.defaultValue = e.target.value
    },
    onEnter() {
      this.handleUpdate()
    },
    handleNumberMaxLength (e) {
      const value = e.target.value;
      this.defaultValue = value;

      if (value > 100000 && this.model.unlimited_quantity === false) {
        this.defaultValue = 100000
      }
    },
    handleUpdate() {
      if (!isEqual(this.defaultValue, this.model[this.prop])) {
        this.save(this.defaultValue)
      }
    },
    autoFocus() {
      // TODO:: check why not working without timeout as expected
      timer = setTimeout(() => {
        this.$refs.inputEditor.focus()
      }, 200)
    },
  },
}
</script>

<style scoped>
.rec-checkbox {
  margin: auto !important;
}

input:disabled {
  color: #999;
  background: transparent;
}
</style>
