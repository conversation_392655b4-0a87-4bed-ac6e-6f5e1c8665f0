<template>
  <div dir="rtl" class="form-group mb-0">
    <Treeselect
      v-model="defaultValue"
      :multiple="false"
      :searchable="true"
      :show-count="false"
      :default-expand-level="2"
      :clearable="true"
      :always-open="true"
      :close-on-select="false"
      :append-to-body="true"
      :loading-text="'جاري جلب البيانات...'"
      :no-children-text="'...'"
      :is-default-expanded="true"
      :clear-on-select="false"
      :disable-branch-nodes="false"
      class="
        vue-treeselect--custom
        vue-treeselect--without-effect
        vue-treeselect--full
        vue-treeselect--show-clear
      "
      placeholder="اختر قسم محلي للمنتج"
      :options="mahlyCategories"
      :normalizer="normalizer"
      @close="handleChange"
    >
      <label class="vue-treeselect__label" :class="{'vue-treeselect__label-disabled': node.children.length > 0}" slot="option-label" slot-scope="{ node }">
        {{ node.label }}
      </label>
    </Treeselect>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import ProductService from '../../../../services/ProductService'

export default {
  name: 'MahlyTreeselectEditor',
  components: { Treeselect },
  props: ['rowIndex', 'prop', 'model', 'close', 'save'],
  data: function () {
    return {
      defaultValue: undefined,
      normalizer(node) {
        return {
          label: node.name,
          value: node.id,
          children: node.sub_categories,
        }
      },
      mahlyCategories: [],
    }
  },
  mounted() {
    this.mahlyCategories = ProductService.getDatum('mahly_data').categories

    this.initValue()
  },
  destroyed() {
    // update row once user confirm
    if(this.isLeafCategory(this.mahlyCategories, this.defaultValue)){
      this.save(this.defaultValue)
    }else if(this.defaultValue === undefined){
      // prevent value from becoming undefined when user clears
      this.defaultValue = null
      this.save(this.defaultValue)
    }
  },
  methods: {
    initValue() {
      this.defaultValue = this.model[this.prop]
    },
    isLeafCategory(categories, categoryId, isLeaf = false){
      categories.map((cat) => {
          if(cat.id === categoryId && cat.sub_categories.length === 0){
            isLeaf = true
            return isLeaf
          }
          if(cat.sub_categories.length){
            isLeaf = this.isLeafCategory(cat.sub_categories, categoryId, isLeaf)
          }
      })
      return isLeaf
    },
    handleChange(value) {
      if(this.isLeafCategory(this.categories, value))
      this.defaultValue = value
    },
  },
}
</script>

<style>
.vue-treeselect--custom .vue-treeselect__control {
  height: 100%;
  border-color: #fff !important;
}

.vue-treeselect--show-clear .vue-treeselect__control .vue-treeselect__x-container{
  display: table-cell;
}

</style>
