<template>
  <div dir="rtl" class="form-group mb-0">
    <Treeselect
      v-model="defaultValue"
      value-format="object"
      :multiple="false"
      :flat="false"
      :searchable="false"
      :show-count="false"
      :append-to-body="true"
      :default-expand-level="2"
      :clearable="false"
      :close-on-select="false"
      :loading-text="'جاري جلب البيانات...'"
      :no-children-text="'...'"
      :is-default-expanded="true"
      :clear-on-select="false"
      class="
        vue-treeselect--custom
        vue-treeselect--enhanced-tags
        vue-treeselect--without-effect
        vue-treeselect--full
      "
      placeholder="اختر الماركة"
      :options="brands"
      :normalizer="normalizer"
      @close="handleChange"
    >
      <label slot="option-label" slot-scope="{ node }">
        {{ node.label }}
      </label>
    </Treeselect>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import ProductService from '../../../../services/ProductService'

export default {
  name: 'BrandsTreeselectEditor',
  components: { Treeselect },
  props: ['rowIndex', 'prop', 'model', 'close', 'save'],
  data: function () {
    return {
      defaultValue: undefined,
      normalizer(node) {
        return {
          label: node.name,
          value: node.id,
        }
      },
      brands: [],
    }
  },

  mounted() {
    this.brands = ProductService.getDatum('brands')

    this.initValue()
  },

  updated() {
    // this.initValue()
  },
  beforeDestroy() {},
  destroyed() {
    // update row once user confirm
    this.save(this.defaultValue)
  },

  methods: {
    initValue() {
      this.defaultValue = this.model[this.prop]
    },
    handleChange(value) {
      this.defaultValue = value
    },
  },
}
</script>

<style>
.vue-treeselect--custom .vue-treeselect__control {
  height: 100%;
  border-color: #fff !important;
}
</style>
