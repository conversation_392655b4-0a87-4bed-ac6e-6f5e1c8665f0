import ProductService from '../../../services/ProductService';

export const renderCategories = (createElement, props) => {
  const categoriesNames = props.model[props.prop]?.map(({ name }) => name)
  return createElement(
    'div',
    {
      class: 'be-dropdown-cell be-categories-wrapper',
    },
    categoriesNames.length > 0
      ? categoriesNames.map((category) =>
          createElement('span', { style: { margin: 5 } }, category)
        )
      : 'اختر تصنيف المنتج'
  )
}

export const renderMahlyCategories = (createElement, props) => {
  let mahlyCategories = ProductService.getDatum('mahly_data').categories
  let categoryId = props.model[props.prop]

  const mapMahallyCategory = (categories, name = '') => {
    if(categoryId === null || categoryId === undefined){
      return null
    }

    if(categoryId === 0){
      name = mahlyCategories[mahlyCategories.length - 1].name
      return name
    }

    categories.map((cat) => {
        if(cat.id === categoryId){
            name = cat.name
        }
        
        if(cat.sub_categories.length){
          name = mapMahallyCategory(cat.sub_categories, name)
        }
    })
    return name
  }

  let categoryName = mapMahallyCategory(mahlyCategories)
  
  return createElement(
    'div',
    {
      class: 'be-dropdown-cell be-mahly-categories-wrapper',
    },
    categoryName ? categoryName : 'اختر تصنيف المنتج'
  )
}

export const renderBranches = (createElement, props) => {
  const branchesNames = props.model[props.prop]?.map(({ name }) => name);
  return createElement(
    'div',
    {
      class: 'be-dropdown-cell be-branches-wrapper',
    },
    branchesNames.length > 0
      ? branchesNames.map((branch) =>
          createElement('span', { style: { margin: 5 } }, branch)
        )
      : 'اختر الفروع للمنتج'
  )
}

export const renderBrands = (createElement, props) => {
  const brands = props.model[props.prop];
  return createElement(
    'div',
    {
      class: 'be-dropdown-cell be-brands-wrapper',
    },
    brands != null && brands.length
      ? props.model[props.prop].map((brand) =>
          createElement('span', { style: { margin: 5 } }, brand.name)
        )
      : brands != null ? createElement('span', { style: { margin: 5 } }, brands.name) : 
      'اختر الماركة للمنتج'
  )
}

export const renderWeightType = (createElement, props) => {
  const weight_types = props.model[props.prop];
  return createElement(
      'div',
      {
        class: 'be-dropdown-cell be-weight-type-wrapper',
      },
      weight_types != null && weight_types.length
          ? props.model[props.prop].map((weight_type) =>
              createElement('span', { style: { margin: 5 } }, weight_type.name)
          )
          : weight_types != null ? createElement('span', { style: { margin: 5 } }, weight_types.name) :
          'اختر نوع الوزن للمنتج'
  )
}

export const renderRadio = (createElement, props) => {
  // div > div > [input:checkbox, label]
  return createElement(
    'div',
    {
      style: {
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      },
    },
    createElement(
      'div',
      {
        class:
          'rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--solo rec-checkbox--primary-bg',
      },
      [
        createElement('input', {
          name: 'checked',
          type: 'checkbox',
          checked: props.model[props.prop],
        }),
        createElement('label', {}),
      ]
    )
  )
}

export const renderDate = (createElement, props) => {
  return createElement(
    'div',
    {
      style: { display: 'flex', alignItems: 'center' },
      className: 'mx-input-wrapper',
    },
    [
      createElement('i', {
        class: 'sicon-calendar',
        style: {
          paddingLeft: 10,
          color: '#d5d5d5',
        },
      }),
      createElement(
        'span',
        {
          style: {
            color: props.model[props.prop] ? undefined : '#d5d5d5',
          },
        },
        props.model[props.prop] || 'نهاية التخفيض (اختياري)'
      ),
    ]
  )
}

export const renderImage = (createElement, props) => {
  const src = props.model[props.prop]
  return createElement(
    'div',
    {
      class: 'img-wrapper',
    },
    createElement('img', {
      src,
    })
  )
}

export const inputNumber = (createElement, props) => {
  return createElement(
    'div',
    {
      class: 'form-group mb-0',
      style: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        right: 0
      }
    },
    [
      createElement('input', {
        name: 'input',
        type: 'number',
        class: 'form-control no-border',
        style: {
          height: '100%',
          background: 'transparent',
          color: props.model.unlimited_quantity ? '#999' : null
        }, 
        disabled: props.model.unlimited_quantity || (props.model.product_type !== 'food' && props.prop === 'calories') || !props.model.can_has_weight,
        value: props.model[props.prop],
      })
    ]
  )
}
