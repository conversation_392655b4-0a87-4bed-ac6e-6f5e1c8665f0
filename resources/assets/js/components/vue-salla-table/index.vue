<template>
  <div id="salla-table-wrapper" :class="{'dimmed-branches': dimmedBranches}" dir="ltr">
    <v-grid
      stretch="true"
      theme="compact"
      row-size="70"
      :editors="gridEditors"
      :range="range"
      :source="source"
      :columns="headers"
      :class="{'white-row': whiteRow, 'visible-v': visibleV}"
      @afteredit="handleAfterEdit"
    ></v-grid>
  </div>
</template>

<script>
import VGrid from '@mhmdaljefri/vue-datagrid'
export * from '@mhmdaljefri/vue-datagrid'

export default {
  components: {
    VGrid
  },
  props: ['columns', 'source', 'gridEditors', 'range', 'onReachedEnd', 'whiteRow', 'dimmedBranches', 'visibleV'],
  data() {
    return {
      headers: [],
      afterEditProduct: null,
    }
  },
  mounted() {
    window.addEventListener('viewportscroll', this.handleScroll)
    this.initializeContents()
  },
  updated() {
    this.initializeContents()
  },
  destroyed() {
    clearTimeout(this.timer)
    window.removeEventListener('viewportscroll', this.handleScroll)
  },
  methods: {
    initializeContents() {
      // revers columns to support rtl
      const columns = this.columns || []

      if (columns[0]?.prop && columns[0].prop !== this.headers[0]?.prop) {
        this.headers = columns.reverse()
      }

      // scroll to first column after rerender
      const grid = document.querySelector('revo-grid')
      this.timer = setTimeout(() => {
        grid.scrollToColumnIndex(this.columns?.length)
      }, 1)
    },
    handleScroll(e) {
      const { dimension, coordinate } = e.detail
      const { clientHeight } = e.target
      // table wrapper with max height
      const scrollWrpper = document.querySelector(
        '.vertical-inner > .content-wrapper'
      )
      const reachedEnd = coordinate >= scrollWrpper.clientHeight - clientHeight

      if (this.fetching || dimension === 'rgCol') {
        // if request is already in progress or yAxis scroll changed
        // no need to do anything yet.
        return
      }

      if (reachedEnd) {
        this.onReachedEnd?.()
      }
    },
    handleAfterEdit(data){
      this.$emit('afteredit', data)
    }
  },
}
</script>

<style>
/**
* override css of '@mhmdaljefri/vue-datagrid
*/

revo-grid {
  height: auto !important;
}

revo-grid[theme='compact'] revogr-data .rgCell.disabled {
  background-color: transparent;
}

revo-grid revogr-data {
  font-size: 13px;
}

revo-grid revogr-header {
  font-size: 13px;
  font-weight: 100 !important;
}

revogr-edit span {
  display: flex;
  align-items: center;
  height: 100%;
  border: 1px solid #eee;
  box-shadow: inset 0 0 0 9999px #5dd5c426;
}
revogr-edit input.form-control {
  height: 100%;
}

revogr-edit .form-group {
  width: 100%;
}
.rgHeaderCell .resizable {
  display: none !important;
}

revogr-edit .form-control,
revogr-edit input[type='text'],
revogr-edit input[type='number'] {
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
  font-size: 13px;
  text-align: right;
  padding: 0 10px;
}

/** overrid bootstrap [hidden] css */
revogr-temp-range[hidden] {
  display: block;
}

.colPinEnd .rgCell {
  white-space: normal !important;
  line-height: 1.6;
}

.rgRow:nth-child(2n) .rgCell {
  background-color: #fcfcfc;
}

.white-row .rgRow:nth-child(2n) .rgCell{
  background: transparent;
}

.rgRow.focused-rgRow {
  background: transparent !important;
}

revo-grid[theme='compact'] revogr-header .rgHeaderCell.focused-cell {
  background: #5ed5c414;
  color: #5ed5c4;
}

revo-grid[theme='compact'] revogr-data .rgCell {
  padding: 0 10px;
}

revogr-header .rgHeaderCell {
  border-top: none;
  padding: 0 10px !important;
  color: black;
  border-bottom: 1px solid #eee;
}

.rgCell,
.rgCell.disabled {
  border-bottom: 1px solid #eee;
}

revogr-data .rgCell,
revogr-header .rgHeaderCell {
  border-left: 1px solid #eee;
}

/** selection  */
revogr-overlay-selection .selection-border-range,
revogr-focus.focused-cell {
  box-shadow: -1px 0 0 #5dd5c4 inset, 1px 0 0 #5dd5c4 inset,
    0 -1px 0 #5dd5c4 inset, 0 1px 0 #5dd5c4 inset;
  background: rgba(93, 213, 196, 0.05);
}
revogr-overlay-selection .autofill-handle {
  background-color: #5dd5c4;
}
/** end selection */
revogr-scroll-virtual.vertical {
  min-width: 3px !important;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar {
  width: 3px;
}

revogr-scroll-virtual.horizontal {
  min-height: 3px !important;
}

revogr-scroll-virtual.horizontal::-webkit-scrollbar {
  height: 3px;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar-thumb,
revogr-scroll-virtual.horizontal::-webkit-scrollbar-thumb {
  border-radius: 0;
  background: #5dd5c4;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar-track,
revogr-scroll-virtual.horizontal::-webkit-scrollbar-track {
  background: #f8f8f8;
  border-radius: 0;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar-thumb:window-inactive,
revogr-scroll-virtual.horizontal::-webkit-scrollbar-thumb:window-inactive {
  background: #fcfcfc;
}

.dimmed-branches .rgCol .header-rgRow.actual-rgRow div:last-child {
  opacity: .3;
  pointer-events: none;
}

#salla-table-wrapper .content-wrapper {
  overflow: visible;
  display: block !important;
}

#salla-table-wrapper .content-wrapper,
#salla-table-wrapper .header-wrapper,
#salla-table-wrapper .vertical-inner.scroll-rgRow {
  padding-top: 0 !important;
  width: inherit;
}

#salla-table-wrapper .header-content,
#salla-table-wrapper .rgCell {
  direction: rtl;
  text-align: right;
  display: flex;
  align-items: center;
  white-space: nowrap;
  line-height: 15px;
  font-weight: 500;
}


#salla-table-wrapper .header-wrapper {
  background-color: #fcfcfc;
}

#salla-table-wrapper .mx-calendar-header-label {
  box-shadow: none;
  border: 0;
  text-align: center;
  display: block;
}
#salla-table-wrapper .vertical-inner {
  height: 100%;
}

.visible-v .vertical-inner {
  overflow: visible;
}

#salla-table-wrapper revogr-viewport-scroll.colPinEnd {
  box-shadow: -2px 3px 8px 0 rgb(70 70 70 / 10%);
}

.rgCell:hover {
  box-shadow: inset 0 0 0 9999px #5dd5c426;
}

.img-wrapper {
  width: 60px;
  max-width: 60px;
  height: auto;
  margin: 5px auto;
  border: 1px solid #eee;
  border-radius: 2px;
  overflow: hidden;
}
.img-wrapper img {
  display: block;
  width: 100%;
  max-height: 55px;
  object-fit: cover;
}

.be-dropdown-cell {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-right: 25px;
  flex-wrap: wrap;
}

.be-dropdown-cell:after,
.be-categories-wrapper:before,
.be-mahly-categories-wrapper:before,
.be-branches-wrapper:before,
.be-brands-wrapper:before,
.be-weight-type-wrapper:before {
  font-family: sallaicons;
  font-size: 15px;
  color: #ddd;
  display: inline-block;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.be-dropdown-cell:after {
  content: '\e96d';
  left: 10px;
}

.be-categories-wrapper:before {
  content: '\efeb';
  right: 10px;
}

.be-mahly-categories-wrapper:before{
  content: '\e9a8';
  right: 10px;
}

.be-branches-wrapper:before {
  content: '\e95f';
  right: 10px;
}

.be-brands-wrapper:before {
  content: '\e956';
  right: 10px;
}

.be-weight-type-wrapper:before {
  content: '\ed45';
  right: 10px;
}

revogr-edit .vue-treeselect__placeholder {
  line-height: 66px;
}

.vue-treeselect__list label {
  width: 100%;
  padding: 7px 10px 10px;
  margin: 0;
}

.be-categories-wrapper span, .be-mahly-categories-wrapper span {
  line-height: 1.6;
}

.be-categories-wrapper span:after, .be-mahly-categories-wrapper span:after {
  content: '،';
  display: inline-block;
  margin: 0 1px 0 5px;
}

.be-categories-wrapper span:last-child:after, .be-mahly-categories-wrapper span:last-child:after {
  display: none;
}

revo-grid .main-viewport {
  height: calc(90vh - 230px);
}

.rec-checkbox input[type=checkbox] + label:after {
  top: 4px;
  right: 1px;
}

</style>
