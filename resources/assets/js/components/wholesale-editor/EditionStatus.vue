<template>
  <div
    :class="{
      'edition-wrapper': true,
      static: isStatic,
      fixed: isFixed,
      absolute: isAbsolute,
    }"
  >
    <span class="font-13">
      عدد المنتجات المعدلة:
      <b>{{ editionCounter }}</b>
    </span>
    <button
      v-if="showSave"
      class="btn btn-tiffany text-white ml-15"
      title="حفظ التعديلات"
      @click="onSave"
    >
      حفظ التعديلات
    </button>
    <MiniLoader v-if="status === 'saving'" size="sm" title="جاري الحفظ" />
    <button v-if="showUndo" title="تراجع" class="btn btn--undo ml-15" @click="onUndo">
      <i class="sicon-back font-13" />
    </button>
  </div>
</template>

<script>
import MiniLoader from '../loaders/mini-loader'
export default {
  components: {
    MiniLoader,
  },
  props: {
    onSave: {
      type: Function,
      required: true,
    },
    onUndo: {
      type: Function,
      required: true,
    },
    editionCounter: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      default: 'defualt',
      validator: (val) =>
        ['update', 'saving', 'saved', 'default', 'faild'].includes(val),
    },
    variant: {
      type: String,
      default: 'static',
      validator: (val) => ['absolute', 'static', 'fixed'].includes(val),
    },
  },
  computed: {
    showSave() {
      return ['update', 'faild'].includes(this.status)
    },
    showUndo() {
      return ['saved', 'faild'].includes(this.status)
    },
    isStatic() {
      return this.variant === 'static'
    },
    isFixed() {
      return this.variant === 'fixed'
    },
    isAbsolute() {
      return this.variant === 'absolute'
    },
  },
}
</script>

<style scoped>
.edition-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 150px;
  box-sizing: border-box;
}
.edition-wrapper.fixed {
  position: fixed;
}
.edition-wrapper.absolute {
  position: absolute;
}
.edition-wrapper.absolute,
.edition-wrapper.fixed {
  bottom: 50px;
  left: 50px;
  z-index: 999;

  height: 60px;

  background: #ffffff;
  border: 1px solid #eeeeee;
  box-shadow: 0px 1px 6px rgba(0, 0, 0, 0.10495);
  border-radius: 30px;
  padding: 20px;
}
.edition-wrapper.static {
  position: relative;
}
.btn--undo {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  padding: 0;
  color: #eb5757;
  border: 1px solid #eb5757a1;
  background: transparent;
}
</style>
