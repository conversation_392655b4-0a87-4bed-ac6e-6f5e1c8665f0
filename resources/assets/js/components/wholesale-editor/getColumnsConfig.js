import {
    renderRadio,
    inputNumber
  } from '../vue-salla-table/cells/index.js'
  
/**
 *
 * @param {string[]} columns : a list of fields;
 * @param {boolean} isSmallScreen
 * @returns a list of revo grid columns config
 */
export default function getColumnsConfig(columns, isSmallScreen = false, screenSize) {
    return columns?.map(({ name, title }) => {
        const defaultOptions = {
            prop: name,
            name: title,
            size: columns.length > 4 ? 140 : 180,
            editor: 'inputNumberEditor',
            cellTemplate: inputNumber,
        }
        
        switch (name) {
            case 'name-sticky':
                return {
                    ...defaultOptions,
                    prop: 'name-sticky',
                    pin: isSmallScreen ? undefined : 'colPinEnd', // end means start in rtl
                    size: isSmallScreen ? 300 : (screenSize < 1645 ? ((screenSize  - 322) - 190) / 2  : 564),
                    editor: 'inputEditor',
                    readonly: true,
                    cellTemplate: null,
                }
        
            case 'category_name':
                return {
                ...defaultOptions,
                editor: 'inputEditor',
                size: isSmallScreen ? 300 : (screenSize < 1645 ? (((screenSize  - 322) - 190) / 2 ) - 10  : 560),
                readonly: true,
                cellTemplate: null,
            }
        
            case 'published':
                return {
                    ...defaultOptions,
                    prop: 'published',
                    editor: 'radioBoxEditor',
                    size: 190,
                    cellTemplate: renderRadio,
                }
            default:
                return defaultOptions
        }
    });
}
  