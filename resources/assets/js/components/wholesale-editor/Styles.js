export default {
	table: {
		tableWrapper: '',
		tableHeaderClass: '',
		tableBodyClass: '',
		tableClass: 'ui unstackable striped celled table',
		loadingClass: 'loading',
		ascendingIcon: 'blue chevron up icon',
		descendingIcon: 'blue chevron down icon',
		ascendingClass: 'sorted-asc',
		descendingClass: 'sorted-desc',
		sortableIcon: 'grey sort icon',
		handleIcon: 'grey sidebar icon',
	},

	pagination: {
		wrapperClass: 'rec-pagination rec-pagination--modified rec-list rec-list--horizontal v-pagination',
		activeClass: 'active large',
		disabledClass: 'disabled',
		pageClass: 'item',
		linkClass: 'icon item',
		paginationClass: 'ui bottom attached segment grid',
		paginationInfoClass: 'left floated left aligned six wide column',
		dropdownClass: 'ui search dropdown',
		icons: {
			first: 'sicon-caret-right-double',
			prev: 'sicon-caret-right',
			next: 'sicon-caret-left',
			last: 'sicon-caret-left-double',
		}
	},

	paginationInfo: {
		infoClass: 'left floated left aligned six wide column',
	}
}
