<template>
  <div>
    <div class="row btns-row">
      <div class="col-12">
        <a :href="getProductsUrl()" class="btn btn--trans btn--return">
          <i class="sicon-caret-right-circle right-back-arrow"></i>
          العودة الى المنتجات
        </a>
      </div>
  
    </div>

    <!-- Filter Products ---->
    <filters
      v-if="enableFilter && showSearch()"
      :extra-class="false"
      :export-url-param="'/export/product-quantities'"
    />

    <div class="panel panel-default">
      <!-- title for table -->
      <div class="panel-heading pt-15 pb-15 sides">
        <h6 class="panel-title panel-title--sub-title">
          إظهار / إخفاء في سوق الجملة
          <span v-if="!showSearch() && products.data">
            - المنتجات المحددة : {{ getProductsCount() }}</span
          >
        </h6>
        <edition-status
          v-if="preUpdatedProducts.length > 0"
          :on-save="handleUpdateCommit"
          :on-undo="handleUpdateUndo"
          :edition-counter="preUpdatedProducts.length"
          :status="editionStatus"
        />
      </div>
      <!-- / title for table -->

      <div class="panel-body no-padding">
        <!-- mini loader -->
        <div v-show="pageFetching">
          <mini-loader size="sm" :wide="true" :center="true" />
        </div>
        <!-- / mini loader -->

        <!-- Input Search -->
        <div
          v-if="showSearch()"
          :class="[
            'form-group search-field bulk-editor mb-0',
            isSearching ? 'loading' : '',
          ]"
        >
          <div v-show="false" class="loader-absolute">
            <mini-loader size="sm" />
          </div>
          <input
            v-model="keyword"
            v-debounce:1000ms="search"
            type="text"
            class="form-control"
            placeholder="ابحث بإسم المنتج ، او الـ SKU"
          />
        </div>
        <!-- / Input Search -->

        <!-- Data Table -->
        <VSallaTable
          :range="false"
          :grid-editors="gridEditors"
          :source="source"
          :columns="columns"
          :on-reached-end="handlePaginate"
          :white-row="true"
          :dimmed-branches="false"
        />
        <!-- / Data Table -->

        <!--  No Data Found Component  -->
        <no-data v-if="showNoDataBox" />
        <!-- / No Data Found Component  -->
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
// Local Files
import Filters from '../products-view/components/filters'
import getColumnsConfig from './getColumnsConfig'
import MiniLoader from '../loaders/mini-loader'
import EditionStatus from './EditionStatus'
import NoData from './NoData'
import Http from '../../utils/http'
import VSallaTable, { VGridVueEditor } from '../vue-salla-table'
import initialData from '../../services/ProductService'

// Libs
import Vue from 'vue'
import lodash from 'lodash'
import qs from 'query-string'
import vueDebounce from 'vue-debounce'

// Editors
import RadioboxEditor from '../vue-salla-table/editors/radiobox-editor'
// define editors

const radioBoxEditor = VGridVueEditor(
  Vue.component('RadioboxEditor', RadioboxEditor)
)

const editors = {
  radioBoxEditor,
}

Vue.use(vueDebounce, {
  lock: true,
  listenTo: ['keyup'],
  defaultTime: '900ms',
  fireOnEmpty: false,
})

export default {
  components: {
    NoData,
    Filters,
    MiniLoader,
    EditionStatus,
    VSallaTable,
  },
  data() {
    return {
      enableFilter: false,
      fields: [],
      has_branches: false,
      pageFetching: false,
      keyword: null,
      moreParams: {},
      source: [],
      products: [],
      columns: [],
      preUpdatedProducts: [],
      beforeUpdateProductsIds: [],
      gridEditors: editors,
      lastPage: 1,
      currentPage: 1,
      editionStatus: 'default', // can be one of ['update', 'saving', 'saved', 'default']
      wholesale_endpoint: '',
      headers: {},
      isSmallScreen: false,
      screenSize: 0,
    }
  },
  computed: {
    isMobileApp: () => {
      if (typeof dataLayer !== 'undefined' && window.dataLayer.length) {
        return window.dataLayer[0].page.mobileApp
      }
    },
    isSearching: function () {
      return this.pageFetching && this.keyword
    },
    showNoDataBox: function () {
      return this.source.length <= 0 && !this.pageFetching
    },
  },
  watch: {
    isSmallScreen() {
      this.updateColumns()
    },
    screenSize() {
      this.updateColumns()
    }
  },

  created() {
    this.wholesale_endpoint = initialData.getDatum('wholesale_endpoint', ''), // ? need to change it to wholesale
    this.headers = initialData.getDatum('headers', {})
    this.has_branches = !!initialData.getDatum('branches')
    this.fields = initialData.getDatum('branches', this.fields)
    this.moreParams = initialData.getDatum('params')
      ? initialData.getDatum('params')
      : []
    this.enableFilter =
      initialData.getDatum('enable_filters') && this.showSearch()

    this.appendExtraFields()
    this.handleWindowResize()
    this.updateColumns()
  },

  mounted() {
    this.getProducts()
    window.addEventListener('afteredit', this.handleEdit)
    window.addEventListener('beforeaange', this.rangeHandler)
    window.addEventListener('resize', this.handleWindowResize)
    window.Salla.event.addEventListener(
      'products::submit-filters',
      this.submitFilters
    )
  },

  beforeDestroy() {
    window.removeEventListener('afteredit', this.handleEdit)
    window.removeEventListener('beforeaange', this.rangeHandler)
    window.removeEventListener('resize', this.handleWindowResize)
    document.removeEventListener('products::submit-filters', this.submitFilters)
  },

  methods: {
    // Cancel Range edit if the product is SKU
    rangeHandler(event) {
      const newData = event.detail.newData,
        source = event.target.source
      for (const key in newData) {
        if (
          (source[key].type === 'sku' &&
            event.detail.oldProps[0] === 'show_hide_wholesale') ||
          (source[key].show_hide_wholesale &&
            event.detail.oldProps[0] !== 'show_hide_wholesale') ||
          (source[key].has_skus &&
            event.detail.oldProps[0] !== 'show_hide_wholesale')
        ) {
          event.preventDefault()
        }
      }

      if (
        event.detail.oldProps[0] === 'sku' ||
        event.detail.oldProps[0] === 'name-sticky' ||
        event.detail.oldProps[0] === 'branches'
      ) {
        event.preventDefault()
      }
    },

    // Update Columns
    updateColumns() {
      this.columns = getColumnsConfig(this.fields, this.isSmallScreen, this.screenSize)
    },

    // Handler to check the window size on DOM load
    handleWindowResize() {
      this.isSmallScreen = window.innerWidth <= 800;
      this.screenSize = window.innerWidth;
    },

    // Handle the cell edit group or single
    handleEdit(event) {
      const { model, models } = event.detail,
        updatedRows = model ? { 0: model } : models

      // empty list if user sent reuquest to backend (editionStatus === 'saved')
      const prevPreUpdatedProducts =
        this.editionStatus === 'saved' ? [] : this.preUpdatedProducts
      // uniq list of updated products
      const preUpdatedProducts = window._.uniqBy(
        [
          ...prevPreUpdatedProducts,
          ...Object.entries(updatedRows).map(([, value]) => value),
        ],
        'id'
      )

      this.preUpdatedProducts = preUpdatedProducts
      const preUpdatedProductsIds = preUpdatedProducts.map(({ id }) => id)
      this.beforeUpdateProductsIds = preUpdatedProductsIds
      this.updateStatus('update')
    },

    // Status Update
    updateStatus(status) {
      this.editionStatus = status
    },

    //sent updated prodcuts to BE
    handleUpdateCommit() {
      this.postProdcuts(this.preUpdatedProducts)
    },

    //revert products to prev version
    handleUpdateUndo() {
      const ids = this.beforeUpdateProductsIds
      const beforeUpdateProductsCopy = this.products.filter((product) =>
        ids.includes(product.id)
      )

      this.postProdcuts(beforeUpdateProductsCopy, true).then(() => {
        const products = this.source.map((product) => {
          const productIndex = ids.indexOf(product.id)
          const isUpdated = ids.includes(product.id)
          const updateProduct = beforeUpdateProductsCopy[productIndex]

          if (isUpdated) {
            return updateProduct
          }
          return product
        })

        this.updateSource(products)

        // sync products with latest version of source
        this.products = window._.cloneDeep(products)
      })
    },

    // Post for Updating the product
    async postProdcuts(products, undo = false) {
      this.updateStatus('saving')
      await axios
        .put(
          this.wholesale_endpoint,
          {
            channel_id: 1715387403, // encoded vendor_app_id
            products: products,
          },
          {
            headers: {
              ...this.headers,
              ...{
                'Content-Type': 'application/json',
                'X-API-KEY': window.token.key,
                's-store-id': initialData.getDatum('store_encode_id', null),
              },
            },
          }
        )
        .then(() => {
          this.updateStatus(undo ? 'default' : 'saved')
          // reset counter and more...
          if (undo) {
            this.beforeUpdateProductsIds = []
            this.preUpdatedProducts = []
          }
        })
        .catch(({ error }) => {
          this.updateStatus('update')
          window.laravel.ajax.errorHandler(error)
          const firstErrorMessage = Object.values(error.fields)?.[0]?.[0] || "حدث خطأ ما";
          swal({
            title: "حدث خطأ ما",
            text: firstErrorMessage,
            type: "error",
            cancelButtonText: "إغلاق",
            showCancelButton: true,
            showConfirmButton: false,
          });
        })
    },

    // Upsdate the source on updating the products
    updateSource(newSource) {
      this.source = window._.cloneDeep(newSource)
    },

    // Append three static fields on every quantity edit
    appendExtraFields() {
      this.fields.unshift(
        {
          name: 'name-sticky',
          title: 'اسم المنتج',
        },
        {
          name: 'category_name',
          title: 'التصنيف',
        },
        {
          name: 'published',
          title: 'إظهار / إخفاء في سوق الجملة',
        }
      )
    },

    getProductsUrl() {
      return window.baseUrl + '/products'
    },

    getBulkProductsUrl(params = {}) {
      const searchParams = qs.stringify(params, { arrayFormat: 'bracket' })
      return `${window.baseUrl}/products/wholesale/editor?${searchParams}`
    },

    showSearch() {
      return !window.location.href.includes('products[0]')
    },

    // fetch all filters data
    getFilters() {
      window.Salla.event.createAndDispatch('products::fetch-filters')
    },

    // Submit Filter
    submitFilters(e) {
      this.moreParams = { ...e.detail, filtering: 1 }
      this.getProducts()
    },

    // Search By Keyword
    search() {
      this.moreParams.keyword = this.keyword ? this.keyword : null
      this.getProducts()
    },

    // Cancel Filtration
    cancelFilter(event) {
      event.stopPropagation()
      this.resetFilters()
      window.Salla.event.createAndDispatch('products::reset-filters')
    },

    // Reset all filters parameters
    resetFilters() {
      this.moreParams = {}
      this.getProducts()
    },

    // Get Parent Products Count without skus
    getProductsCount() {
      let selected_products = window._.filter(this.products.data, function (p) {
        return !p.product_id // remove the child skus that has parent id
      })
      return selected_products.length
    },

    // convert product.status to boolean
    productNormalizer({ ...product }) {
      return {
        ...product,
      }
    },

    // handle paginations on scroll
    handlePaginate() {
      const canPaginate = this.currentPage < this.lastPage && !this.pageFetching

      if (canPaginate) {
        this.getProducts(this.currentPage + 1)
      }
    },

    // Fetch The Products
    async getProducts(page = 1, prePage = 40) {
      this.pageFetching = true
      try {
        const productsUrl = this.getBulkProductsUrl({
          per_page: prePage,
          page,
          ...this.moreParams,
        })

        const {
          data: { products },
        } = await Http.get(productsUrl)

        let allProducts = [...products.data]
        if (page > 1 && this.source.length < prePage * page) {
          allProducts = [...this.source, ...products.data]
        }

        const normalizeList = allProducts.map(this.productNormalizer)

        // prevent assing refernce with clonDeep
        this.source = lodash.cloneDeep(normalizeList)
        this.products = lodash.cloneDeep(normalizeList)
        this.lastPage = products.last_page
        this.currentPage = page

        return products.data
        // eslint-disable-next-line no-empty
      } catch (error) {
      } finally {
        this.pageFetching = false
      }
    },
  },
}
</script>

<style scoped lang="scss">
#salla-table-wrapper {
  position: sticky;
  top: 100px;
}

.search-field {
  border-top: none;
  position: relative;
  z-index: 10;
}

.loader-absolute {
  position: absolute;
  inset: 10px;
}
</style>