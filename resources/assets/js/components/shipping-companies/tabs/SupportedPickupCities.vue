<template>
  <div>
    <section v-for="region in supportedPickupCities" :key="region.id" class="rec-accordion rec-accordion--enhanced">
      <div class="panel panel-default mb-15">
        <div
          :id="'region-' + region.id"
          role="tab"
          :aria-expanded="false"
          data-toggle="collapse"
          data-parent="#accordion"
          :href="'#collapse-' + region.id"
          :aria-controls="'collapse-' + region.id"
          class="panel-heading rec-accordion__heading with-icon"
          @click="toggleCollapse(region.id)"
        >
          <div>
            <h4>
              <i class="sicon-map-location"></i>
              {{ region.name }}
            </h4>
          </div>
          <i class="sicon-keyboard_arrow_left arrow-rotate"></i>
        </div>
        <div
          :id="'collapse-' + region.id"
          role="tabpanel"
          :aria-labelledby="'region-' + region.id"
          class="panel-collapse rec-accordion__collapse collapse"
          :class="{ 'show': region.isExpanded }"
        >
          <div class="panel-body rec-accordion__body align-right scroll scroll--y p-0">
            <ul class="rec-list rec-list--vertical p-5">
              <li v-for="city in region.cities" :key="city.id" class="p-5">
                <span>{{ city.name }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  computed: {
    ...mapState({
      supportedPickupCities: (state) => state.supportedPickupCities.supportedPickupCities,
    }),
  },
  methods: {
    toggleCollapse(regionId) {
      const clickedRegion = this.supportedPickupCities.find((region) => region.id === regionId);
      if (clickedRegion) {
        clickedRegion.isExpanded = !clickedRegion.isExpanded;
      }
    },
  },
};
</script>
