<template>
  <div class="row">
    <div class="col-xs-12">
      <h6 class="mb-20 mt-0">{{$t('shipping.integration.note')}}</h6>
      <p class="mt-5" v-html="company?.note"></p>
      <div class="separate-line"></div>
      <TreeSelect
        v-if="showLinkOptions"
        v-model="linkType"
        select-id="link_options"
        :label="$t('shipping.integration.integration_type')"
        icon="sicon-chat-conversation-alt"
        :options="linkTypeOptions"
        :multiple="false"
        :searchable="false"
      />
      <component :is="getDynamicComponentName" v-bind="dynamicComponentProps" />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  props: {},
  data() {
    return {
      linkType: null,
      dynamicComponentProps: {},
      HasOnlySallaAccountCompanies: [
          'careem',
          'dal',
          'adwar',
          'pie_ship',
          'shipa',
          'imile',
          'future',
          'salasa_awb',
      ],
    }
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),

    linkTypeOptions() {
      return [
        { id: `salla_${this.company?.slug}`, label: `${this.$t('shipping.integration.salla_policies_link_option')}`, name:`${this.$t('shipping.integration.salla_policies_link_option')}`},
        { id: 'manual', label:`${this.$t('shipping.integration.manual_link_option')}`, name: `${this.$t('shipping.integration.manual_link_option')}`},
      ];
    },
    showLinkOptions(){
      return !this.company?.has_salla_polices_account && this.company?.support_salla_policies === true &&
              ! this.HasOnlySallaAccountCompanies.includes(this.company?.slug);
    },
    getDynamicComponentName() {
      const componentMapping = {
        'aramex'     : 'AramexCompany',
        'smsa'       : 'SmsaCompany',
        'saudi_post' : 'SaudiPostCompany',
        'aymakan'    : 'AymakanCompany',
        'dhl'        : 'DhlCompany',
        'barq'       : 'BarqCompany',
        'jnt'        : 'JntCompany',
        'redbox'     : 'RedBoxCompany',
        'flow'       : 'FlowExpressCompany',
      };

      const defaultComponent = 'AramexCompany';
      let componentName = '';

      if(this.HasOnlySallaAccountCompanies.includes(this.company?.slug)) {
        componentName = 'HasOnlySallaAccount';
      }else{
        componentName = componentMapping[this.company?.slug] || defaultComponent;
      }

      return componentName;
    },
  },
  watch: {
    company: {
      handler() {
        if (this.company?.integration_data) {
          this.linkType = this.company.integration_data.link_options ?? 'manual';
        }
      },
      immediate: true,
      deep: true,
    },
    linkType: {
      handler() {
        this.updateDynamicComponentProps();
      },
      immediate: true,
    },
  },
  methods: {
    updateDynamicComponentProps() {
      this.dynamicComponentProps.linkType = this.linkType;
    },
  },
};
</script>
