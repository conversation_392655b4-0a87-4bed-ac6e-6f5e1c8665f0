<template>
  <div v-if="countries.length > 0">
    <div v-if="company?.has_account">
      <ShippingCostAlert />
      <ExtraFeesAlert />
      <div>
        <div v-if="countries.length > 1">
          <section v-for="(country, countryIndex) in countries" :key="country.id" :countryIndex="countryIndex" class="rec-accordion rec-accordion--enhanced">
            <div class="panel panel-default mb-15">
              <div
                :id="'zone-' + country.id"
                role="tab"
                :aria-expanded="false"
                data-toggle="collapse"
                data-parent="#accordion"
                :href="'#collapse-' + country.id"
                :aria-controls="'collapse-' + country.id"
                class="panel-heading rec-accordion__companies with-icon p-0"
                @click="toggleCollapse(country.id)"
              >
                <div class="p-10">
                  <h4>
                    <i class="sicon-align-justify"></i>
                    {{ country.name }}
                  </h4>
                </div>
                <div v-show="(company?.has_salla_polices_account && company?.support_international) || ! company?.has_salla_polices_account">
                  <div class="d-flex align-items-center" @click.stop>
                    <i class="sicon-keyboard_arrow_down arrow-rotate"></i>
                    <div class="input-group-btn">
                      <button class="btn bg-danger btn-lg px-5" @click="deleteCountryZonesForm(country.id)">
                        <i class="sicon-trash-2 white-icon"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div :id="'collapse-' + country.id" role="tabpanel" :aria-labelledby="'zone-' + country.id" class="panel-collapse rec-accordion__collapse-companies collapse" aria-expanded="false" style="">
                <div class="panel-body rec-accordion__body align-right scroll scroll--y">
                  <div v-for="(zoneDetails, zoneDetailsIndex) in country?.zones" :key="zoneDetails.id" :class="country?.zones.length > 1 ? 'shipping_zones' : ''">
                    <ZoneForm :is-flexible="company.support_international" :country-index="countryIndex" :zone-details-index="zoneDetailsIndex" />
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
        <div v-else>
          <div v-for="(zoneDetails, zoneDetailsIndex) in countries[0]?.zones" :key="zoneDetails.id" :class="countries[0]?.zones.length > 1 ? 'shipping_zones' : ''">
            <ZoneForm :is-flexible="company.support_international" :country-index="0" :zone-details-index="zoneDetailsIndex" />
          </div>
        </div>

        <div v-if="addNewZone">
          <div v-for="(newZone, newZoneIndex) in newAddedZones" :key="newZoneIndex" class="shipping_zones">
            <ZoneForm is-new-added-zone :is-flexible="company.support_international" :zone-details-index="newZoneIndex" />
          </div>
        </div>

        <div v-if="! exceedZonesLimit && company?.support_international">
          <button class="btn btn-tiffany btn-full mt-20" @click="addZoneForm">
            <i class="sicon-add mr-5"></i>
            {{ $t('shipping.form.add_new_zone') }}
          </button>
        </div>
      </div>
    </div>
    <div v-else>
      <DefaultZone />
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions } from "vuex";
  import { showSwAlert } from "../utils/showSwAlert";

  export default {
    data() {
      return {
        addNewZone: false,
        supportsSallaInternationalShipping: ["aramex"]
      };
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        countries: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
        exceedZonesLimit() {
          let zoneLimit = window.initialData.shipping_custom_settings.shipping_zones_limit;
          let zoneCount = this.countries.reduce((count, zone) => count + zone.zones.length, 0);
          return zoneCount > zoneLimit;
        },
      }),
    },
    async created() {
      if (!this.countries.length) {
        let companyId = this.company.id;
        let serviceType = this.company?.integration_data?.service_type;
        await this.getShippingCompanyZones({ companyId, serviceType });
      }
  },
  methods: {
    ...mapActions(["deleteZonesByCompany", "getShippingCompanyZones"]),
    toggleCollapse(countryId) {
      const countryIndex = this.countries.findIndex((country) => country.id === countryId);
      if (countryIndex !== -1) {
        const updatedCountries = [...this.countries];
        const clickedCountry = { ...updatedCountries[countryIndex] };

        clickedCountry.isExpanded = !clickedCountry.isExpanded;
        updatedCountries[countryIndex] = clickedCountry;
        this.$store.commit('setShippingCompanyZones', updatedCountries);
      }
    },
    async addZoneForm() {
      let tempZones = JSON.parse(JSON.stringify(this.newAddedZones));
      const currentLang = window.dashboard_settings.languages.current.iso_code;
      const serviceType = this.company?.support_international && this.company?.has_salla_polices_account &&
          this.supportsSallaInternationalShipping.includes(this.company?.slug) ? 'salla_international_shipping' : 'shipping';

      const zoneObject = {
        id: -1,
        name: "كل الدول",
        zones: [
          {
            id: "",
            service_type: serviceType,
            status: "1",
            zone_code: null,
            city: [
              {
                id: -1,
                name: "كل المدن",
                name_en: "All Cities",
              },
            ],
            cities_excluded: [],
            fees: {
              company_cost: 0,
              amount: 0,
              currency: "SAR",
              type: "fixed",
              weight_unit: "kg",
              up_to_weight: (!this.company.support_international && this.company.slug == 'aymakan') ? 5 : 15,
              amount_per_unit: (!this.company.support_international && this.company.slug == 'aymakan') ? 3 : 2,
              per_unit: 1,
            },
            cash_on_delivery: {
              status: false,
              fees: "",
              salla_support_cod: false,
            },
            duration: "",
            coverage_distance: {
              status: false,
              value: null,
              min: 1,
              max: 15,
            },
            translation: {
              [currentLang]: {
                duration: "",
              },
            },
          },
        ],
      };

      if (currentLang === 'en') {
        zoneObject.zones[0].translation['ar'] = {
          duration: "",
        };
      }
      
      tempZones.push(zoneObject);
      await this.$store.commit("setNewAddedZones", [...tempZones]);
      this.addNewZone = true;
    },

    async deleteCountryZonesForm(zoneId) {
      swal({
        title: "تنبيه ",
        text: "هل انت متاكد من هذا الاجراء ؟",
        type: "error",
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: "تأكيد",
        cancelButtonText: "إلغاء",
      }).then(async () => {
        try {
          await this.deleteZonesByCompany({
            companyId: this.company.id,
            countryId: zoneId,
            serviceType: this.company?.integration_data?.service_type
          })
            .then(() => {
              const filteredZones = this.countries.filter((zone) => zone.id !== zoneId);
              this.$store.commit("setShippingCompanyZones", filteredZones);
              showSwAlert("success", "تم الحذف بنجاح", undefined, false, 3000);
            });
        } catch (error) {
          showSwAlert("error", "حدث خطأ غير متوقع, الرجاء المحاولة لاحقًا.", undefined, false, 3000);
        }
      });
    },
  },
};
</script>
