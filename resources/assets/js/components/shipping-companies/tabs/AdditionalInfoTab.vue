<template>
  <div class="row">
    <div class="col-xs-12">
      <div>
        <h6 class="mb-20 mt-0">{{$t('shipping.additional_info.contact')}}</h6>

        <div v-if="company?.telephone" class="form-group mb-10">
          <label class="text-semibold">{{$t('shipping.additional_info.telephone')}}</label>
          <span class="pull-right-sm">{{company?.telephone}}</span>
        </div>

        <div v-if="company?.mobile" class="form-group mb-10">
          <label class="text-semibold">{{$t('shipping.additional_info.mobile')}}</label>
          <span class="pull-right-sm">{{company?.mobile}}</span>
        </div>

        <div v-if="company?.email" class="form-group mb-10">
          <label class="text-semibold">{{$t('shipping.additional_info.email')}}</label>
          <span class="pull-right-sm"><a href="#">{{company?.email}}</a></span>
        </div>

        <div v-if="company?.website" class="form-group no-margin-bottom">
          <label class="text-semibold">{{$t('shipping.additional_info.website')}}</label>
          <span class="pull-right-sm"><a href="#" target="_blank">{{company?.website}}</a></span>
        </div>
      </div>
      <div class="seperate-line"></div>
      <div v-if="(company?.faqs).length">
        <h6 class="mb-20 mt-0">{{$t('shipping.additional_info.common_questions')}}</h6>

        <div class="rec-faq rec-faq--basic">
         <div v-for="faq in company?.faqs" :key="faq.id" class="card mb-0 rounded-bottom-0">
           <div class="card-header wide">
              <h6 class="card-title m-0">
                <i class="sicon-information position-left"></i>
                <a data-toggle="collapse" class="text-default collapsed mb-0 px-10 py-20" :href="'#collapsible-item-shipping-faq-' + faq.id">
                  <div v-html="faq.question"></div>
               </a>
             </h6>
            </div>
            <div :id="'collapsible-item-shipping-faq-' + faq.id" class="collapse wide pt-15">
             <div class="card-body">
                <div v-html="faq.answer"></div>
              </div>
           </div>
         </div>
        </div>
      </div>

      <p class="mt-10">
        {{$t('shipping.additional_info.more_common_questions')}}
        <a class="text-underline" target="_blank" href="https://help.salla.sa/article/2089545426">
          {{$t('shipping.salla_help_center')}}
        </a>
      </p>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  data() {
    return {
      
    };
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  },
  mounted() { 
  },
};
</script>

