/* We will need this file when we started apply changing locales in the dashboard */
const i18nService = {
  defaultLanguage: "ar",
  languages: [
    {
      lang: "en",
      name: "English",
    },
    {
      lang: "ar",
      name: "Arabic",
    },
  ],
  /**
   * Keep the active language in the localStorage
   * @param lang
   */
  setActiveLanguage(lang) {
    localStorage.setItem("language", lang);
  },
  /**
   * Get the current active language
   * @returns {string | string}
   */
  getActiveLanguage() {
    return localStorage.getItem("language") || this.defaultLanguage;
  },
};
export default i18nService;
