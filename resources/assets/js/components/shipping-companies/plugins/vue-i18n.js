import Vue from "vue";
import VueI18n from "vue-i18n";
Vue.use(VueI18n);

// get current selected language
const lang = localStorage.getItem("language") || "ar";

let messages = {};

const languageData = {
  [lang]: window.initialData.messages  // dynamically set the variable based on the language
};
messages = { ...messages, ...languageData };

const i18n = new VueI18n({
  locale: lang,
  messages,
});
export default i18n;