export function showSwAlert(type, title, text, showConfirmButton, timer = 3000) {
  swal({
    type: type || 'success' ,
    title: title || 'Success',
    text: text ? text : undefined,
    showConfirmButton: showConfirmButton || false,
    timer: timer || 3000,
  });
}

export function showSwAlertAction(type, title, text, titleHtml, showCancelButton) {
  swal({
    title: title || 'Success',
    text: text ? text : undefined,
    html: titleHtml || undefined,
    type: type || 'success',
    showCancelButton: showCancelButton || false,
    confirmButtonColor: "#F44336",
    confirmButtonText: "موافق",
    cancelButtonText: "إغلاق",
  }).then(() => {
    window.location.reload();
  });
}