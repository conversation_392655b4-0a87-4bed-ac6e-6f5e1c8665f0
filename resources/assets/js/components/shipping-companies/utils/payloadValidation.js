const nullableFields = {
  aramex: ["show_order_items_on_police"],
  dhl: ["labelTemplate"],
  smsa: ["auto_print_awb",  ! window.initialData.permissions.is_smsa_restfull_enabled ? "smsa_api_key" : "smsa_pass_key"],
  barq: ["always_open"],
};

import i18n from "../plugins/vue-i18n";

export function validatePayload(companySlug, payload) {
  const errors = {};

  for (const key in payload) {
    if (!payload[key] || (typeof payload[key] === "string" && payload[key].trim() === "")) {
      if ((nullableFields[companySlug] && nullableFields[companySlug].includes(key)) || typeof payload[key] === "boolean" || key === "display_name") {
        continue; // Skip validation for nullable fields
      }
      if (!errors[key]) {
        errors[key] = [];
      }
      const keyTranslation = i18n.t(`shipping.` + companySlug + `.` + key) || key;
      errors[key].push(`حقل ${keyTranslation} مطلوب.`);
    }
  }
  return errors;
}

export function validateCountries(countries, isOldZone) {
    const errors = {};

  function validateField(value) {
    if (value === undefined || value === null || value === "") {
      return "الحقل مطلوب";
    } else if (isNaN(value) || value < 0 || value > 9999) {
      return `القيمة لهذا الحقل يجب أن تكون رقمية وتكون بين 0 و9999`;
    } else {
      return undefined; // No error
    }
  }

  function validateFees(fees, path) {
    const validationRules = {
      fixed: ["company_cost", "amount"],
      rate: ["company_cost", "amount", "up_to_weight", "amount_per_unit", "per_unit"],
      automatic: ["company_cost"],
    };

    const fieldsToValidate = validationRules[fees.type];
    if (!fieldsToValidate) {
      return;
    }

    fieldsToValidate.forEach((field) => {
      const fieldValue = fees[field];
      const error = validateField(fieldValue);
      if (error) {
        errors[`${path}.${field}`] = [error];
      }
    });
  }

    countries.forEach((country, countryIndex) => {
    let errorPrefix = isOldZone ? "zone" : "country";
      
    errorPrefix+= `.${countryIndex}`;  
    if (!country.id) {
      errors[`${errorPrefix}.id`] = ["الحقل مطلوب"];
    }

    country.zones.forEach((zone, zoneIndex) => {
      if (!zone.service_type || typeof zone.service_type !== "string" || !zone.status || !zone.city || !Array.isArray(zone.city) || zone.city.length < 1 || !zone.duration || zone.duration.length === 0) {
        const fieldsToCheck = ["service_type", "status", "city", "duration"];
        fieldsToCheck.forEach((field) => {
          if (!zone[field] || (field === "city" && zone.city.length < 1)) {
            errors[`${errorPrefix}.zones.${zoneIndex}.${field}`] = ["الحقل مطلوب"];
          }
        });
      }

      validateFees(zone.fees, `${errorPrefix}.zones.${zoneIndex}.fees`);

      const cashOnDelivery = zone.cash_on_delivery;
      if (cashOnDelivery && cashOnDelivery.status && (cashOnDelivery.fees === undefined || cashOnDelivery.fees < 0 || cashOnDelivery.fees > 9999)) {
        errors[`${errorPrefix}.zones.${zoneIndex}.cash_on_delivery.fees`] = ["القيمة لهذا الحقل يجب أن لا تقل عن 0 ولا تزيد عن 9999"];
      }
    });
  });

  return errors;
}
