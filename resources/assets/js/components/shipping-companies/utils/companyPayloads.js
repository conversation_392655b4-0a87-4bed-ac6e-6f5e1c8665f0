export const display_form_payload = {
  display_name: null,
  company_name: "",
  sender_name: "",
  mobile: "",
  link_options: "",
  sync_shipment_status: false,
};
/******* Aramex ********/

export const aramex_policies_payload = {
  ...display_form_payload,
  link_options: "salla_aramex",
  shipping_type: "",
  show_order_items_on_police: null
};

export const aramex_payload = {
  ...display_form_payload,
  link_options : "manual",
  AccountEntity: "",
  AccountNumber: "",
  AccountPin: "",
  UserName: "",
  Password: "",
  payment_type: "",
  payment_currency: "",
  product_group_in: "",
  product_type_in: "",
  product_group_out: "",
  product_type_out: "",
  shipping_type: "",
  show_order_items_on_police: null
};

/****** DHL **********/
export const dhl_policies_payload ={
  link_options : "salla_dhl",
  labelTemplate: null,
  display_name: null,
  company_name: "",
  sync_shipment_status: null,
};
export const dhl_payload = {
  ...dhl_policies_payload,
  site_id : "",
  account_number: "",
  password:"",
};
dhl_payload.link_options = "manual";

/****** SMSA **********/
export const smsa_policies_payload = {
  link_options : "salla_smsa",
  ...display_form_payload,
  name: "",
  auto_print_awb : null
};
delete smsa_policies_payload.sender_name;
export const smsa_payload = {
  link_options: "manual",
  ...display_form_payload,
  name: "",
  smsa_pass_key : "",
  smsa_api_key : "",
  auto_print_awb : null
};
delete smsa_payload.sender_name;
/****** Aymakan **********/
export const aymakan_policies_payload = {
  link_options : "salla_aymakan",
  ...display_form_payload
};
export const aymakan_payload = {
  ...display_form_payload,
  link_options: "manual",
  token: "",
};

/****** Barq **********/
export const barq_policies_payload = {
  ...display_form_payload,
  link_options : "salla_barq"
};
export const barq_payload = {
  ...display_form_payload,
  link_options : "manual",
  email: "",
  password: "",
  opening_time: "",
  closing_time: "",
  always_open: null,
  start_day: "",
  end_day: ""
};

/****** (Careem ,Dal, Adwar ,PieShip, Shipa, Imile) **********/
export const careem_policies_payload = {...display_form_payload};
delete careem_policies_payload.link_options;
export const dal_policies_payload ={...display_form_payload};
delete dal_policies_payload.link_options;
export const adwar_policies_payload = {...display_form_payload};
delete adwar_policies_payload.link_options;
export const shipa_policies_payload = {...display_form_payload};
delete shipa_policies_payload.link_options;
export const pie_ship_policies_payload = {...display_form_payload};
delete pie_ship_policies_payload.link_options;
export const imile_policies_payload = {...display_form_payload};
delete imile_policies_payload.link_options;
export const future_policies_payload = {...display_form_payload};
delete future_policies_payload.link_options;
export const salasa_awb_policies_payload = {...display_form_payload};
delete salasa_awb_policies_payload.link_options;

/********** J&T ***********/
export const jnt_policies_payload = {
  ...display_form_payload,
  link_options: "salla_jnt"
};

export const jnt_payload = {
  ...display_form_payload,
  link_options : "manual",
  apiAccount: "",
  privateKey: "",
  customerCode: "",
  CustomerPassword: ""
};

/********** Redbox ***********/
export const redbox_policies_payload = {
  ...display_form_payload,
  link_options: "salla_redbox"
};

export const redbox_payload = {
  link_options : "manual",
};

/********** Flow Express ***********/
export const flow_policies_payload = {
  ...display_form_payload,
  link_options: "salla_flow"
};

export const flow_payload = {
  ...display_form_payload,
  link_options : "manual",
  api_key: "",
};

export function getCompanyPayload(payloadSlug) {
  const payloadName = `${payloadSlug}_payload`;

  const payloadObjects = {
    aramex_policies_payload,
    aramex_payload,
    dhl_policies_payload,
    dhl_payload,
    smsa_policies_payload,
    smsa_payload,
    aymakan_policies_payload,
    aymakan_payload,
    barq_policies_payload,
    barq_payload,
    careem_policies_payload,
    dal_policies_payload,
    jnt_policies_payload,
    jnt_payload,
    redbox_policies_payload,
    redbox_payload,
    adwar_policies_payload,
    shipa_policies_payload,
    pie_ship_policies_payload,
    flow_policies_payload,
    flow_payload,
    imile_policies_payload,
    future_policies_payload,
    salasa_awb_policies_payload,
  };
  
  return payloadObjects[payloadName];
};
