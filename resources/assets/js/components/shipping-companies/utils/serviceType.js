const typeTranslations = {
  economy: "الشحن الاقتصادي",
  delivery: "التوصيل الفوري",
  shipping_supported_cities: "الشحن للمدن المدعومة",
  NULL: "الشحن",
  shipping: "الشحن",
  "on-demand": "الشحن",
  salla_aramex_shipping: "الشحن",
  salla_aymakan_shipping: "الشحن",
  salla_shipping_service: "الشحن",
  salla_international_shipping: "الشحن الدولي",
};

export function getTypeServiceFromTranslation(translation) {
  for (const [type, trans] of Object.entries(typeTranslations)) {
    if (trans === translation) {
      return type;
    }
  }
  return "shipping";
}

export function getTranslationServiceFromType(type) {
  return typeTranslations[type] || "NULL";
}