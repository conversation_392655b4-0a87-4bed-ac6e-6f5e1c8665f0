<template>
    <div id="feedback_popup_wrapper" :class="{'minimized': isMinimized, 'hidden': isHidden}">
        <div id="feedback_popup" class="rec-list rec-list--vertical gap-20">
            <div id="feedback_popup_title"
                class="w-full rec-list justify-content-between cursor-pointer"
                @click="setIsMinimized(! isMinimized)">
                <span>{{ $t('shipping.feedback.title') }}</span>
                <div>
                    <i :class="isMinimized ? 'sicon-add' : 'sicon-minus text-red'"></i>
                </div>
            </div>

            <div v-show="! isMinimized" id="feedback_popup_content">
                <div id="feedback_popup_subtitle">
                    {{ $t('shipping.feedback.subtitle') }}
                </div>

                <div id="feedback_popup_companies" class="rec-list rec-list--vertical">
                    <div v-for="company in companies" :key="company.id" class="company-row-wrapper">
                        <div class="company-row rec-list justify-content-between align-items-center gap-10">
                            <div class="logo">
                                <img :src="company.logo" alt="" />
                            </div>
                            <div class="flex-1">
                                <div class="company-name">
                                    <span>{{ company.name }}</span>
                                </div>
                                <div class="shipments-count-wrapper">
                                    <span>عدد الشحنات </span> <span class="shipments-count-value">({{ company.shipments_count ?? 0 }})</span>
                                </div>
                            </div>
                            <a :class="`feedback-btn btn ${company.is_satisfied === true ? 'active' : 'inactive'}`" @click="setSatisfied(company)">
                                <div class="rec-list gap-5">
                                    <i class="sicon-thumbs-up" />
                                    <span class="feedback-value" v-if="company.satisfied_count">{{ renderCount(company.satisfied_count) }}</span>
                                </div>
                            </a>
                            <a :class="`feedback-btn btn ${company.is_satisfied === false ? 'active' : 'inactive'}`" @click="setNotSatisfied(company)">
                                <i class="sicon-thumbs-down" />
                            </a>
                        </div>
                        <div v-if="company.is_satisfied == false" class="feedback-reasons">
                            <div class="title">{{ $t('shipping.feedback.reasons_title') }}</div>
                            <div v-for="reason in reasons" :key="reason.value" class="reason-row">
                                <div class="checker">
                                    <span :class="company.reasons?.includes(reason.value) ? 'checked' : ''">
                                        <input
                                        type="checkbox"
                                        class="styled check_cart"
                                        :value="1"
                                        :checked="true"
                                        @click="toggleCompanyReason(company, reason.value)"
                                        />
                                    </span>
                                </div>
                                <span>{{ reason.label }}</span>
                            </div>
                            <div v-show="company.reasons?.includes('other')" class="other-reasons">
                                <div class="title">{{ $t('shipping.feedback.other_reasons_title') }} <span class="text-red">*</span></div>
                                <div class="textarea-wrapper">
                                    <i class="sicon-page-content"></i>
                                    <textarea 
                                        :id="`other_reason_${company.id}`"
                                        v-model="company.other_reason"
                                        class="form-control" :placeholder="$t('shipping.feedback.other_reasons_title')"
                                        @keyup="addOtherReason(company)"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="feedback_popup_footer" class="text-center rec-list rec-list--vertical justify-content-center align-items-center gap-10">
                    <button :class="`btn btn-tiffany btn-full mt-20 ${canSubmit ? '' : 'disabled'}`" @click="handleSubmit">{{ $t('shipping.feedback.submit') }}</button>
                    <span>{{ $t('shipping.feedback.footer_text') }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
    data() {
        return {
            reasons: [
                {
                    label: "سرعة التوصيل للعميل",
                    value: 'speed'
                },
                {
                    label: "جودة المحافظة على الشحنات",
                    value: 'quality'
                },
                {
                    label: "مرونة التعامل مع الاستفسارات والمشكلات",
                    value: 'flexibility'
                },
                {
                    label: "أخرى",
                    value: 'other'
                }
            ]
        }
    },
    computed: {
        ...mapState({
            companies: state => state.feedback.companies,
            isHidden: state => state.feedback.isHidden,
            isMinimized: state => state.feedback.isMinimized,
            canSubmit: state => state.feedback.canSubmit
        })
    },
    watch: {
        companies: {
            deep: true,
            handler() {
                const isAllFeedback = this.companies.every(company => company.is_satisfied !== null);
                const missingOtherReason = this.companies.some(company => company.reasons?.includes('other') && (company.other_reason?.trim() === '' || company.other_reason === null));
                if (! isAllFeedback || missingOtherReason) {
                    this.setCanSubmit(false);
                } else {
                    this.setCanSubmit(true);
                }
            }
        }
    },
    created() {
        this.getCompanies();
    },
    methods: {
        ...mapActions({
            getCompanies: 'getCompanies',
            sendFeedback: 'sendFeedback',
            setSatisfied: 'setSatisfied',
            setNotSatisfied: 'setNotSatisfied',
            setCanSubmit: 'setCanSubmit',
            setIsMinimized: 'setIsMinimized'
        }),
        isChecked(id) {
            const checkedInput = document.getElementById(`checked-${id}`);
            if (!checkedInput.parentElement.classList.contains('checked')) {
                checkedInput.parentElement.classList.add('checked');
                checkedInput.checked = true;
            } else {
                checkedInput.parentElement.classList.remove('checked');
                checkedInput.checked = false;
            }
        },
        addOtherReason(company) {
            const id = company.id
            const textarea = document.getElementById(`other_reason_${id}`);
            const parent = textarea.parentElement;
            const childIcon = parent.querySelector('.sicon-page-content');
            if (textarea.value.trim() !== '') {
                childIcon.style.opacity = '0';
                childIcon.style.pointerEvents = 'none';
            } else {
                childIcon.style.opacity = '1';
                childIcon.style.pointerEvents = 'auto';
            }
        },
        toggleCompanyReason(company, reason) {
            const index = company.reasons.indexOf(reason);
            if (index > -1) {
                company.reasons.splice(index, 1);
            } else {
                company.reasons.push(reason);
            }
        },
        renderCount(count) {
            if (count == 0) return '';
            
            if (count > 1000) {
                return (count / 1000).toFixed(1) + ' ألف';
            }
            return count
        },
        handleSubmit() {
            if (! this.canSubmit) return;

            this.sendFeedback();
        },
        showMessage( type = 'error', title = '', text = null, reload = false) {
            swal({
                title,
                text: text ? text : undefined,
                type,
                showConfirmButton: false,
                timer: 1500
            }).catch(function() {
                if (reload) {
                    window.location.href = '/shipping';
                }
            });
        },
        showErrorMessage(errorMessage = null) {
            this.showMessage('error', 'حدث خطأ', errorMessage ? errorMessage : 'يرجى المحاولة مرة أخرى')
        }
    }
}
</script>