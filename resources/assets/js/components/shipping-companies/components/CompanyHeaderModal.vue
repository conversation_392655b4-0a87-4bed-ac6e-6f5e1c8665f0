<template>
  <div>
<!--    Disabled it for now -->
<!--    <UseSallaAccountAlert/>-->
    <div v-show="ShippingOptionsActive && company?.integration_data?.api_id != '0'" class="py-10 mb-10">
      <div class="row">
        <div class="col-xs-10">
          <div class="d-flex align-items-center">
            <img
                v-if="company && company.logo"
                class="shipping-logo"
                :alt="company.name"
                :src="company.logo"
                style="width: 120px; height: 50px; max-height: 50px"
            />
            <ShippingRate
                v-if="company"
                :show-number-of-reviews-and-name="true"
                :company_name="company.name"
                :rating="companyRating"
                :number-of-reviews="company?.review?.statistics?.reviews_count || 0"
            />
          </div>
        </div>
        <!--        <div class="col-xs-2 text-right">-->
        <!--          <div class="vue-toggle">-->
        <!--            <ToggleButton v-if="company && company.status" v-model="companyStatus" :sync="true" :css-colors="true"/>-->
        <!--          </div>-->
        <!--        </div>-->
      </div>

      <div class="row">
        <div class="col-xs-12 mt-20">
          <div v-show="company?.support_on_demand_delivery">
            <OnDemandDeliveryAlert/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { mapState } from "vuex";
import ToggleButton from 'vue-js-toggle-button';
import ShippingRate from './layouts/ShippingRate';

Vue.use(ToggleButton);

export default {
  components: {
    ShippingRate
  },
  data() {
    return {
      companyStatus: this.company ? this.company.status : false,
    };
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
    companyRating() {
    return this.company && this.company.review ? parseInt(this.company.review.statistics.rating_avg) : 0;
    },
    ShippingOptionsActive() {
        return window.initialData.permissions.shipping_option_active
    },
    // watch: {
    //   companyStatus() {
    //     this.$emit('update:company', { ...this.company, status: newStatus });
    //   }
    // }
  },
}
</script>
