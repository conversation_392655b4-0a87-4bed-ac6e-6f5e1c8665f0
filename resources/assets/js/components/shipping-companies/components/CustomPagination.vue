<template>
  <div class="simple-pagination panel-footer py-10 px-20 border-none w-auto gap-5">
    <div class="d-flex gap-5 align-items-center flex-1">
      <button class="previous btn btn--primary btn-sm p-5" :disabled="previousDisabled" @click="customPageChange($event,'previous')">
        <i class="previous sicon-keyboard_arrow_right"></i>
        السابق
      </button>

      <button class="next btn btn--primary btn-sm p-5" :disabled="!hasMorePages" @click="customPageChange($event,'next')">
        التالي
        <i class="next sicon-keyboard_arrow_left"></i>
      </button>
    </div>
    <div class="select-page-wrapper d-flex align-items-center">
      <p class="text-muted m-0">عدد الصفوف في صفحة واحدة &nbsp;</p>
      <select :disabled="!hasMorePages" class="select-pagination" :value="per_page" @change="customPerPageChange($event)">
        <option value="10">10</option>
        <option value="20">20</option>
        <option value="30">30</option>
        <option value="40">40</option>
        <option value="50">50</option>
      </select>
    </div>
  </div>
</template>

<script>
  export default {
    name: "CustomPagination",
    props: {
      pageChanged: {
        type: Function,
      },
      isSelectedPerPage: {
        type: Number,
        default: 10,
      },
      current_page: {
        type: Number,
        default: 1,
      },
      per_page: {
        type: Number,
        default: 10,
      },
      hasMorePages: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
        nextDisabled: false,
        currentPage: 1,
        firstPage: 1,
      };
    },
    computed: {
      previousDisabled() {
        return this.current_page === this.firstPage;
      },
    },
    // and...
    methods: {
      customPageChange(event, clickType) {
        if (this.hasMorePages && clickType === "next") {
          this.pageChanged({ currentPage: this.current_page + 1 });
          this.$emit("onChangePage", this.current_page + 1);
        }
        if (clickType === "previous") {
          this.pageChanged({ currentPage: this.current_page - 1 });
          this.$emit("onChangePage", this.current_page - 1);
        }
      },
      customPerPageChange(customPerPage) {
        this.$emit("onChangePerPage", { perPage: customPerPage.target.value });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .select-pagination {
    border: none;
    background-color: transparent;
    outline: none;
  }
  .simple-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    button.btn--primary {
      background-color: white;
      border: 1px solid #eee;
    }
  }
  body.dark {
    .simple-pagination {
      button.btn--primary {
        background: #2c2c2c;
        border-color: #333;
      }
    }
  }

  @media (max-width: 415px) {
    .simple-pagination {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
    }
  }
</style>
