<template>
  <div id="div_shipping_settings" class="panel panel-default panel-small-title" >
    <div class="panel-heading">
      <h6 class="panel-title">
        <i class="sicon-brightness-low"></i>
        إعدادات شركات الشحن
      </h6>
    </div>
    <div class="panel-body p-0">
      <ul class="rec-list rec-list--vertical rec-list--bordered light-border">
        <li v-if="canManageFreeShipping" class='p-20'>
          <a :class="{ 'card card--horizontal card--clickable': true, 'load_free_shipping': true }">
            <div class="card__header">
              <i class="sicon-free-sign text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">الشحن المجاني</h6>
              <span class="font-13 text-muted sub-title" dir="ltr">تحكم بإعدادات وشروط الشحن المجاني لعملاء متجرك</span>
            </div>
          </a>
        </li>
        <li v-if="canManageCashOnDelivery" class='p-20'>
          <a :class="{
            'card card--horizontal card--clickable': true,
            'cash_on_delivery_settings': showCashOnDeliveryFeature
          }"
            @click="!showCashOnDeliveryFeature ? alertForPackages() : null">
            <div class="card__header">
              <i class="sicon-donation text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">الدفع عند الإستلام
                <div v-if="storePlan == 'basic'" class="d-inline-block ml-5">
                  <span class="badge badge--warning lh-1">
                    بلس/ برو
                  </span>
                </div>
              </h6>
              <span class="font-13 text-muted sub-title" dir="ltr">حدد شروط الدفع عند الاستلام والمنتجات او التصنيفات
                المستثناه من الخدمة</span>
            </div>
          </a>
        </li>
        <li class='p-20'>
          <a :class="{ 'card card--horizontal card--clickable': true, 'load_shipping_settings': true }">
            <div class="card__header">
              <i class="sicon-shipping text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">خيارات شركات الشحن</h6>
              <span class="font-13 text-muted sub-title" dir="ltr">امنح شركات الشحن إذن تحديث حالة الطلب ومزامنة الكميات
                وصلاحيات اخري</span>
            </div>
          </a>
        </li>
        <li v-if="canManageShippingRules" class='p-20'>
          <a
            class="card card--horizontal card--clickable"
            href="#"
            @click.prevent="navigateTo('/shipping/rules')"
            >
            <div class="card__header">
              <i class="sicon-memo text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">قيود شركات الشحن
                <div v-if="!isShippingRulesFeaturesEnabled" class="d-inline-block ml-5">
                  <span class="badge badge--warning lh-1">
                    سبيشل / برو
                  </span>
                </div>
              </h6>
              <span class="font-13 text-muted sub-title" dir="ltr">أضف قيود محددة لشركة الشحن المدعومة في متجرك</span>
            </div>
          </a>
        </li>
        <li class='p-20'>
          <a class="card card--horizontal card--clickable" data-toggle="modal" data-target="#shipping_calculator_modal">
            <div class="card__header">
              <i class="sicon-calculator2 text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">حاسبة أسعار الشحن</h6>
              <span class="font-13 text-muted sub-title" dir="ltr">حدد وزن الشحنة ووجهتها لحساب تكلفة الشحن
                التقريبية</span>
            </div>
          </a>
        </li>
      </ul>
    </div>
    <shipping-rate-calculator />
  </div>
</template>

<script>

export default {
  data() {
    return {
      isLegacy: window.sallaLegacy
    };
  },
  computed: {
    canManageShippingRules() {
        return window.initialData.permissions.management && this.isShippingRulesFeaturesEnabled;
    },
    canManageCashOnDelivery() {
        return window.initialData.permissions.management;
    },
    canManageFreeShipping() {
        return window.initialData.permissions.management;
    },
    showCashOnDeliveryFeature() {
      return window.initialData.permissions.cash_on_delivery_feature;
    },
    storePlan() {
      return window.initialData.store_plan;
    },
    isShippingRulesFeaturesEnabled() {
      return window.initialData.permissions.is_shipping_rules_features_enabled
    }
  },
  methods: {
    alertForPackages() {
      swal({ text: 'هذه الخاصية متاحة في باقة (سلة بلس و سلة برو و سلة سبيشل). يمكن ترقية الباقة من خلال باقة المتجر', type: 'info', showConfirmButton: false, timer: 3000 })
      },
      navigateTo(path) {
        if (this.isLegacy) {
          window.location = path;
        } else {
          window.parent?.postMessage({ event: "navigateTo", path: path }, "*");
        }
    }
  }

};
</script>
