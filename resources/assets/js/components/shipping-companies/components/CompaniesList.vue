<template>
  <div class="p-15">
    <div
        v-for="(section, index) in companiesSections"
        :key="index"
        class="pb-10"
    >
      <div v-if="section?.companies.length > 0" class="pb-10">
        <div class="rec-responsive-list">
          <h6 class="font-20 text-primary font-bold">
            <i :class="`font-25 sicon-${section.icon} pr-10`"></i>
            {{ section.label }}
          </h6>
          <small class="badge badge--primary-no-border rec-list-margined-small">
            {{ section.duration }}
          </small>
        </div>
        <span class="text-muted">{{ section.short_description }}</span>
      </div>
      <div class="grid-block grid-block--col-3 grid-block--gap-20 salla-policies-list">
        <div
            v-for="(company, companyIndex) in section?.companies"
            :key="companyIndex"
            class="store-addon__item store-addon__item--app store-addon__item--app div-app-card relative overflow-hidden"
        >
          <div v-if="company?.is_special_price" class="company-special-price-badge badge-danger">
            <span>أفضل سعر</span>
          </div>
          <div class="store-addon__item__body">
            <div class="media-left">
              <a class="company-details-container" :data-company-id="company.id" @click="handleCompanyClick(company.id,section.name)">
                <img :src="company.logo" class="company-details-logo" />
              </a>
            </div>
            <div class="media-body">
              <div class="d-flex gap-10 justify-between store-addon__item__title-container">
                <h6 class="store-addon__item__title mb-0 mt-0 font-15">
                  <a
                      :data-company-id="company.id"
                      @click="handleCompanyClick(company.id,section.name)"
                  >{{ company.name }}</a
                  >
                </h6>
                <div>
                  <div class="vue-toggle">
                    <ToggleButton :key="`toggle-${company.id}-${companyStatus[index][company.id]}`" v-model="companyStatus[index][company.id]" @change="toggleClicked(company.id,index,section.name)" />
                  </div>
                </div>
              </div>
              <div class="rec-list my-5">
                <a @click.prevent="handleOnClickRating(company, section)">
                  <shipping-rating
                      :rating="company?.review?.statistics?.rating_avg"
                      :reviews="company?.review?.statistics?.reviews_count"
                      :flat="true"
                  />
                </a>

                <a
                    v-if="company.review.status === 'need_review'"
                    href="#"
                    class="text-underline ml-10"
                    @click.prevent="handleClickCompanyRating(company)"
                >أضف تقيمك</a
                >
              </div>
              <div class="store-addon__item__meta">
                <small v-for="(tag, indexTag) in company.tags" :key="indexTag" class="badge badge--grey mr-5 mt-5"> {{ tag.name }} </small>
              </div>
            </div>
          </div>
        </div>
      </div>
      <rating-modal :company="ratingCompany" />
    </div>
  </div>
</template>

<script>
import ToggleButton from 'vue-js-toggle-button'
import Vue from 'vue'
import { mapActions, mapState } from 'vuex'

import api from '../config/axios.config'

Vue.use(ToggleButton)

export default {
  data() {
    return {
      companyStatus: {},
      ratingCompany: null,
    };
  },
  computed: {
    ...mapState({
      companiesSections: (state) => state.sallaPolicies.companiesSections,
    }),
  },
  watch: {
    companiesSections: {
      immediate: true,
      handler(newCompaniesSections) {
        const companyStatuses = {};
        newCompaniesSections.forEach((section, index) => {
          companyStatuses[index] = {};
          for (const company of section.companies) {
            companyStatuses[index][company.id] = company.status;
          }
        });
        this.companyStatus = companyStatuses;
      },
    },
  },
  methods: {
    ...mapActions(["getCompanyDetails", "getSupportedPickupCities"]),
    async handleCompanyClick(companyId,serviceType) {
      window.showLoading();
      await this.getCompanyDetails({companyId,serviceType}).then((response) => {
        $("#company_details_modal").modal("show");
        window.hideLoading();
        if (response.data.has_pickup_zones) {
          this.getSupportedPickupCities(companyId);
        }
      });
    },
    async changeCompanyStatus(companyId, payload) {
      window.showLoading();
      await api.post(`/company/change-status/${companyId}`, payload).then(() => {});
      window.hideLoading();
    },
    async toggleClicked(companyId, sectionIndex,serviceType) {
      let payload = {
        status: this.companyStatus[sectionIndex][companyId],
        service_type: serviceType
      };

      this.changeCompanyStatus(companyId, payload);
    },
    updateToggleValue(companyId, sectionIndex) {
      this.companyStatus[sectionIndex][companyId] = !this.companyStatus[sectionIndex][companyId];
      this.$forceUpdate();
    },
    handleClickCompanyRating(company) {
      this.ratingCompany = company
      $('#rating_modal').modal('show')
    },
    handleOnClickRating(company, section) {
      console.log(section);
      if (!company.support_rating) {
        return false
      }

      this.$router.push({
        name: 'company-rating',
        params: { companyId: company.id },
        query: { service_type: section.name },
      });
    },
  },
}
</script>