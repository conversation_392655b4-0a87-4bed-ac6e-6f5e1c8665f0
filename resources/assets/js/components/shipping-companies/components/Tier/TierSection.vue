<template>
  <div class="panel panel-default">
    <div class="panel-body no-padding">
      <div class="p-20">
        <div class="font-16">
          <span class="font-18 text-bold text-primary d-block mb-3">
           وفر اكثر مع بوليصات سلة
          </span>
          <span class="text-normal">
            استمتع بتكلفة شحن
             <span class="text-danger">مُخفضة</span>
            كلما زاد عدد البوليصات الصادرة شهرياً من متجرك
            <a class="text-underline" href="#" @click.prevent="openStoreTiersData">اعرف اكثر</a>
          </span>
        </div>
        <div class="pt-50 pb-30">
          <div class="container-fluid">
            <ul class="list-unstyled multi-steps">
              <li v-for="(tier, index) in tierPricingList" :key="index" :class="{ 'is-active': tier.id === currentTier}">
                <div class="step-container">
                  <span>{{formattedShippingCost(tier.shipping_cost)}} ر.س</span>
                  <span :class="{'step-container step-badge': true, 'dimmed': isDimmed(index)}">{{formattedPoliciesNumber(tier.num_of_policies,index)}} بوليصة </span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <tier-modal/>
  </div>
</template>

<script>
  import { mapState, mapActions } from "vuex";

  export default {
    data() {
      return {
      };
    },
    computed: {
      ...mapState({
        tierPricingList: (state) => state.tierSection.tierPricingList,
      }),
      currentTier(){
        return window.initialData.store_tier
      },
    },
    mounted() {
    },
    methods: {
       ...mapActions(["getStoreTier"]),
        openStoreTiersData() {
            window.showLoading();
        this.getStoreTier().then((response)=>{
            $('#tier_modal').modal('show');
            window.hideLoading();
        })
      },
      formattedShippingCost(cost) {
        return parseFloat(cost).toFixed(1).replace(/\.0+$/, '');
      },
      formattedPoliciesNumber(policiesNum,index) {
        let formattedTxt = '';
        if (index === 0) {
          formattedTxt = 'أقل من  ' + policiesNum;
        } else if (index === 1) {
          formattedTxt = 'حتى ' + policiesNum;
        } else {
          formattedTxt = '+' + 3000;
        }
        return formattedTxt;
      },
      isDimmed(index){
        const tierIndex = (this.tierPricingList).findIndex(tier => tier.id === this.currentTier);
        return index < tierIndex;
      }
    },
  };
</script>
