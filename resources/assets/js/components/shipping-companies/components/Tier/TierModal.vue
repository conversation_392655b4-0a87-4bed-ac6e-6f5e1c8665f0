<template>
  <modal id="tier_modal" modal-title="بوليصات سلة" :show-save="false">
    <div v-if="showPoliciesTable">
        <h6 class="text-primary">جدول البوليصات</h6>
        <span>يستعرض الجدول عدد البوليصات التي تم إصدارها من متجرك خلال الأشهر الثلاثة الماضية، حيث يتم تحديد فئتك الحالية بناءً على الشهر الذي شهد أعلى نسبة إصدار للبوليصات.</span>

        <div class="panel panel-default mt-20 mb-0">
        <div class="table-responsive">
            <table class="table table-medium-text">
            <thead>
                <tr class="active">
                <th>الشهر</th>
                <th>عدد البوليصات الصادرة</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(tier, index) in storeTier?.statistics" :key="index" class="table-row">
                <td data-title="الشهر">
                    <span class="text-primary">
                    {{tier.month}}
                    <small v-if="tier.shipments_count === tierCategoryIndex" class="badge badge--light-warning ml-5">أعلى عدد بوليصات !🎉</small>
                    </span>
                </td>
                <td data-title="عدد البوليصات الصادرة">
                    <span>
                    {{tier.shipments_count}} بوليصة
                    </span>
                </td>
                </tr>
            </tbody>
            </table>
        </div>
        </div>

        <div class="seperate-line"></div>
    </div>
    
    <div class="py-5">
      <h6 class="text-bold">سياسة تسعير البوليصات بحسب فئة التاجر</h6>
      <p class="pb-10">
        تسعى سلة لتوفير اسعار شحن منافسة بالتعاون مع شركات الشحن و يتم ذلك عبر السياسة التالية :
      </p>
      <div class="text-size-medium">
        <ul>
          <li>يتم تعيين سعر البوليصة للتاجر بحسب أعلى عدد بوليصات من الثلاث الاشهر الاخيرة</li>
          <li>يتم تطبيق التسعيرة على الشحن المحلي فقط</li>
          <li>يتم تحديث فئة التاجر بداية كل شهر ميلادي</li>
          <li>الأسعار المعروضة غير شاملة ضريبة القيمة المضافة</li>
        </ul>
      </div>
    </div>
    <div class="seperate-line"></div>
    <span>
      هل تحتاج مساعدة في معرفة سياسة تسعير بوليصات سلة؟
      <a class="text-underline" href="http://help.salla.sa/article/1239347931" target="_blank">انتقل لمركز المساعدة</a>
    </span>
  </modal>
</template>

<script>
  import { mapState } from "vuex";

  export default {
    props: {},
    data() {
      return {};
    },
    computed: {
      ...mapState({
        storeTier: (state) => state.tierSection.storeTier,
      }),
      tierCategoryIndex() {
        const largestShipments = this.storeTier?.statistics.reduce((prev, current) => {
          if (prev.shipments_count === current.shipments_count) {
            return prev; 
          } else {
            return prev.shipments_count > current.shipments_count ? prev : current;
          }
        });
        return largestShipments.shipments_count;
        },
        isTiersFeatureEnabled() {
            return window.initialData.permissions.is_shipping_tiers_feature_enabled;
        },
        showPoliciesTable() {
            return this.isTiersFeatureEnabled && window.initialData.has_at_least_one_policy_shipment && this.storeTier?.statistics?.length > 0;
        },
    },
  };
</script>
