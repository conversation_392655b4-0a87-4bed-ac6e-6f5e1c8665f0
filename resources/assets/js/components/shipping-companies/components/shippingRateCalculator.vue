<template>
    <modal id="shipping_calculator_modal" modalTitle="حاسبة أسعار الشحن" :showSave="false">
        <div class="form-group mb-0">
            <label> تفاصيل عنوان الشحن</label>
            <div class="row">
                <div id="shippingCompanyCol" :class="{ 'col-sm-4': showCities, 'col-sm-6': !showCities }">
                    <div class="form-group" :class="{ 'has-error': errors && errors.company_id }">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="sicon-shipping"></i></span>
                            <div class="select-wrapper">
                                <v-treeselect :options="acceptedCompanies" v-model="selectedCompany" :normalizer="companyNormalizer" @input="updateSelectedCompany()" :flat="false" :multiple="false" :searchable="false" :value-format="'object'" placeholder="اختر شركة الشحن" class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full vue-treeselect--with-icon" />
                            </div>
                        </div>
                        <form-error v-if="errors && errors.company_id" :errors="errors">{{ errors.company_id[0].toString() }}</form-error>
                    </div>
                </div>
                <!-- #EEE COUNTRY -->
                <div id="countryCol" :class="{ 'col-sm-4': showCities, 'col-sm-6': !showCities }">
                    <div class="form-group" :class="{ 'has-error': errors && errors.country_code }">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="sicon-location"></i></span>
                            <div class="select-wrapper">
                                <shipping-dropdown :disabled="false" :close-on-select="true" :deselect-from-dropdown="false" :value="selectedCountry" :multiple="false" label="name" :options="localCountries" placeholder="اختر الدولة" @search="fetchCountries" @input="updateSelectedCountry" />
                            </div>
                        </div>
                        <form-error v-if="errors && errors.country_code" :errors="errors">{{ errors.country_code[0].toString() }}</form-error>
                    </div>
                </div>

                <div v-if="showCities" id="cityCol" class="col-sm-4">
                    <div class="form-group" :class="{ 'has-error': errors && errors.city_id }">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="sicon-location"></i></span>
                            <div class="select-wrapper">
                                <shipping-dropdown :disabled="!selectedCountry" :value="selectedCity" :multiple="false" label="name" :options="cities" placeholder="اختر المدينة" @input="updateSelectedCity" @search="fetchCities" />
                            </div>
                        </div>
                        <form-error v-if="errors && errors.city_id" :errors="errors">{{ errors.city_id[0].toString() }}</form-error>
                    </div>
                </div>
            </div>
        </div>
        <div class="pt-50 pb-30">
            <div class="slider-range-container">
                <vue-slider v-model="weight" @change="updateWeight" :min=0.5 :max=70 direction="rtl" :interval="0.5" height="8px" tooltip="always" :lazy="true" :dotStyle="{ backgroundColor: '#76E8CD', borderColor: '#FFFFFF' }" :railStyle="{ backgroundColor: '#EEEEEE' }" :process-style="{ backgroundColor: '#76E8CD' }" :tooltip-style="{ backgroundColor: '#FFFFFF', borderColor: '#FFFFFF', color: '#000000' }" />
            </div>
            <price-indecator :weight="weight" :price="price" />
        </div>
        <section class="alert-box alert-box--warning m-0">
            <i class="sicon-info"></i>
            <article>
                <p>التكلفة اعلاه تعتمد على الوزن فقط وقد تختلف عند الشحن اعتماد على الوزن الحجمي
                    للشحنة،ابعاد الشحنة (ارتفاع - عرض - طول)
                    للمزيد
                    <a target="_blank" href="https://help.salla.sa/article/1478470046">اضغط هنا</a>
                </p>
            </article>
        </section>
    </modal>
</template>

<script>

import { mapActions, mapState } from "vuex";
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import VueSlider from 'vue-slider-component'
import 'vue-slider-component/theme/antd.css'

export default {
    components: {
        'v-treeselect': Treeselect,
        VueSlider,
    },
    data() {
        return {
            weight: 0.5,
            price: 0.0,
            showCities: true,
            errors: {},
            selectedCompany: null,
            selectedCountry: null,
            selectedCity: null,
            localCountries: [],
            companyNormalizer(node) {
                return {
                    label: node.name,
                    value: node.id,
                }
            },
        };
    },
    computed: {
        ...mapState({
            companies: (state) => state.sallaPolicies.companies,
            countries: (state) => state.shippingRateCalculator.countries,
            cities: (state) => state.shippingRateCalculator.cities,
        }),
        acceptedCompanies() {
            // we only need aramex
            return this.companies.filter((company) => {
                return [*********, *********].includes(company.id);
            });
        }
    },
    mounted() {
        //
    },
    methods: {
        ...mapActions(['getCountriesList', 'getCountryCitiesList', 'calculateShippingRateHandler']),

        updateSelectedCompany() {
            this.$root.$store.commit('setCountriesList', []);
            this.$root.$store.commit('setCitiesList', []);
            
            this.selectedCountry = null;
            this.selectedCity = null;
            this.showCities = null;
            this.localCountries = [];
            
        },
        
        updateSelectedCountry(country) {
            this.selectedCountry = country;

            if (this.selectedCountry && this.selectedCountry.code === 'SA') {
                this.showCities = true;
            } else {
                this.showCities = false;
            }

            this.calculateShippingRate()
        },
        updateSelectedCity(selectedCity) {
            this.selectedCity = selectedCity
            this.calculateShippingRate()
        },
        async fetchCountries(searchQuery = {}) {
            const response = await this.getCountriesList({ ...searchQuery });            
            this.localCountries = (this.selectedCompany && this.selectedCompany.id === *********)
                ? response.filter((country) => country.code !== 'SA')
                : response;
        },
        fetchCities(searchQuery = {}) {
            this.selectedCountry && this.getCountryCitiesList({ id: this.selectedCountry.id, ...searchQuery });
        },
        updateWeight() {
            this.calculateShippingRate()
        },
        async calculateShippingRate() {
            this.errors = {};

            if (!this.selectedCompany) {
                this.errors = {
                    'company_id': ['حقل شركة الشحن مطلوب']
                }

                return;
            }

            if ("undefined" == typeof this.selectedCountry || !this.selectedCountry) {
                this.errors = {
                    'country_code': ['حقل الدولة مطلوب']
                }

                return;
            }

            let city = null;
            if (this.selectedCity) {
                city = this.selectedCity.id;
            }

            if (this.selectedCountry.code !== 'SA') {
                city = this.selectedCountry.capital;
            }

            if (!city) {
                this.errors = {
                    'city_id': ['حقل المدينة مطلوب']
                }

                return;
            }

            window.showLoading()
            let payload = {
                'company_id': this.selectedCompany.id,
                'country_code': this.selectedCountry.code,
                'city': city,
                'weight': this.weight
            }

            const response = await this.calculateShippingRateHandler(payload);
            window.hideLoading();

            if (response.success) {
                this.price = response.data.price;
                try {laravel.ajax.successHandler(response.data)} catch (_) { /* empty */ }
            } else {
                if (response.data.error.hasOwnProperty('fields')) {
                    this.errors = response.data.error.fields;
                } else if (response.data.error.hasOwnProperty('message')) {
                    this.errors = {
                        'company_id': [response.data.error.message]
                    }
                }
            }
        },
    },

};

</script>
