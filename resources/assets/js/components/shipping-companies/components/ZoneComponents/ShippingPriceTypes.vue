<template>
  <div>
    <div v-if="priceType == 'rate'">
      <FormInput
        v-if="(!company?.has_salla_polices_account || (!company?.support_international))"
        id="fees.company_cost"
        :label="$t('shipping.form.company_shipping_cost')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.company_cost`"
        :placeholder="$t('shipping.form.input_company_shipping_cost')"
        :is-numeric="true"
        with-symbol
        :symbol="$t('shipping.SAR')"
        :value="selectedZoneData?.fees?.company_cost"
        :disabled="company?.has_salla_polices_account"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />

      <FormInput
        id="fees.amount"
        :label="$t('shipping.form.customer_shipping_cost')"
        :note="$t('shipping.form.customer_shipping_cost_note')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.amount`"
        :placeholder="$t('shipping.form.company_shipping_hint')"
        :is-numeric="true"
        with-symbol
        :symbol="$t('shipping.SAR')"
        :value="rateAmount"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />

      <FormInput
        id="fees.up_to_weight"
        :label="$t('shipping.form.weight')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.up_to_weight`"
        :placeholder="$t('shipping.form.weight_hint')"
        :is-numeric="true"
        with-symbol
        :symbol="$t('shipping.KG')"
        :value="upToWeight"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />

      <FormInput
        id="fees.amount_per_unit"
        :label="$t('shipping.form.the_increased_cost')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.amount_per_unit`"
        :placeholder="$t('shipping.form.charge_cost_hint')"
        :is-numeric="true"
        with-symbol
        :symbol="$t('shipping.SAR')"
        :value="amountPerUnit"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />

      <FormInput
        id="fees.per_unit"
        :label="$t('shipping.form.for_every')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.per_unit`"
        :placeholder="$t('shipping.form.for_every_hint')"
        :is-numeric="true"
        with-symbol
        :symbol="$t('shipping.KG')"
        :value="perUnit"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />

      <div class="pricing-summary">
        <div class="col-xs-5 d-flex flex-column">
          <div class="mb-10">
            -
            <span class="text-muted">
              {{$t('shipping.form.shipping_cost')}}
            </span>
            {{ (rateAmount ?? "0") +" " + $t('shipping.SAR')}}
          </div>
          <div class="mb-10">
            -
            <span class="text-muted">
              {{$t('shipping.form.the_increased_cost')}}
            </span>
            {{ (amountPerUnit ?? "2") +" " +$t('shipping.SAR')}}
          </div>
        </div>
        <div class="col-xs-2 icon-container">
          <i class="sicon-arrow-left"></i>
        </div>
        <div class="col-xs-5 d-flex flex-column">
          <div class="mb-10">
            <span class="text-muted">
              {{$t('shipping.form.for_the_first')}}
            </span>
            {{ (upToWeight ?? "15" ) + " " + $t('shipping.KG')}}
          </div>
          <div class="mb-10">
            <span class="text-muted">
              {{$t('shipping.form.for_every')}}
            </span>
            {{ (perUnit ?? "1") + " " + $t('shipping.more_kg')}}
          </div>
        </div>
        <div class="summary-header">{{$t('shipping.form.pricing_summary')}}</div>
      </div>
    </div>
    <div v-if="priceType == 'fixed'">
      <FormInput
        v-if="(!company?.has_salla_polices_account || (!company?.support_international))"
        id="fees.company_cost"
        :label="$t('shipping.form.company_shipping_cost')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.company_cost`"
        :placeholder="$t('shipping.form.input_company_shipping_cost')"
        with-symbol
        :symbol="$t('shipping.SAR')"
        :value="selectedZoneData?.fees?.company_cost"
        :disabled="company?.has_salla_polices_account"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />
      <FormInput
        id="fees.amount"
        :label="$t('shipping.form.customer_shipping_cost')"
        :note="$t('shipping.form.customer_shipping_cost_note')"
        icon="sicon-banknote-dollar"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.amount`"
        :placeholder="$t('shipping.form.input_customer_shipping_cost')"
        with-symbol
        :is-numeric="true"
        :symbol="$t('shipping.SAR')"
        :value="selectedZoneData?.fees?.amount"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
        required
      />
    </div>
    <div v-if="priceType == 'automatic' && ! company?.has_salla_polices_account">
      <FormInput
          id="fees.company_cost"
          :label="$t('shipping.form.company_shipping_cost')"
          icon="sicon-banknote-dollar"
          :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.company_cost`"
          :placeholder="$t('shipping.form.input_company_shipping_cost')"
          with-symbol
          :symbol="$t('shipping.SAR')"
          :value="selectedZoneData?.fees?.company_cost"
          :disabled="company?.has_salla_polices_account"
          is-zone
          :country-index="countryIndex"
          :zone-details-index="zoneDetailsIndex"
          :is-new-added-zone="isNewAddedZone"
          required
      />
    </div>
  </div>
</template>

<script>
  import { mapState } from "vuex";

  export default {
    props: {
      priceType: {
        type: String,
        default: "fixed",
      },
      isFlexible: {
        type: Boolean,
        default: false,
      },
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        selectedZoneData: null,
        rateAmount: null,
        upToWeight: null,
        amountPerUnit: null,
        perUnit: null
      };
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
      }),
    },
    watch: {
      zones: {
        handler() {
          this.updateSelectedZoneData();
        },
        immediate: true,
        deep: true,
      },
      newAddedZones: {
        handler() {
          this.updateSelectedZoneData();
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      updateSelectedZoneData() {            
        const zoneData = this.isNewAddedZone ? this.newAddedZones[this.zoneDetailsIndex]?.zones[0] : this.zones[this.countryIndex]?.zones[this.zoneDetailsIndex];
        if (zoneData) {
          this.selectedZoneData = zoneData;
          this.rateAmount = this.selectedZoneData?.fees?.amount ?? "0";
          this.upToWeight  = this.selectedZoneData?.fees?.up_to_weight ?? "15";
          this.amountPerUnit = this.selectedZoneData?.fees?.amount_per_unit ?? "2";
          this.perUnit = this.selectedZoneData?.fees?.per_unit ?? "1";
        }
      },
    },
  };
</script>
