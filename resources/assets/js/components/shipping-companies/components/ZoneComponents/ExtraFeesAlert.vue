<template>
<div v-show="company?.slug == 'dal'">
  <div class="alert-box alert-box--warning mb-15">
    <i class="sicon-info"></i>
    <article>
      <p> {{  $t('shipping.extra_fees') }}</p>
    </article>
  </div>
</div>
</template>

<script>
import {mapState} from "vuex";

export default {
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
}
</script>
