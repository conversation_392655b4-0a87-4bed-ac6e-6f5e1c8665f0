<template>
  <div v-if="selectedZoneData?.coverage_distance?.status">
    <FormInput
      id="coverage_distance.value"
      :label="$t('shipping.form.coverage_distance')"
      :placeholder="$t('shipping.form.coverage_distance')"
      icon="sicon-map-location"
      is-numeric
      :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.coverage_distance.value`"
      :value="selectedZoneData?.coverage_distance.value"
      disabled
      is-zone
      :country-index="countryIndex"
      :zone-details-index="zoneDetailsIndex"
      :is-new-added-zone="isNewAddedZone"
      :min="selectedZoneData?.coverage_distance.min"
      :max="selectedZoneData?.coverage_distance.max"
      with-symbol
      symbol="KM"
    />
    <span v-if="selectedZoneData?.coverage_distance.max" class="d-block text-muted text-muted-smaller relative">
      بحد أقصى {{selectedZoneData?.coverage_distance.max }} كيلو
    </span>
  </div>
</template>
<script>
  import { mapState } from "vuex";

  export default {
    props: {
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        selectedZoneData: null,
      };
    },
    computed: {
      ...mapState({
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
      }),
    },
    watch: {
      zones: {
        handler() {
          this.updateSelectedZoneData();
        },
        immediate: true,
        deep: true,
      },
      newAddedZones: {
        handler() {
          this.updateSelectedZoneData();
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      updateSelectedZoneData() {
        const zoneData = this.isNewAddedZone 
          ? this.newAddedZones[this.zoneDetailsIndex]?.zones[0] 
          : this.zones[this.countryIndex]?.zones[this.zoneDetailsIndex];

        if (zoneData) {
          this.selectedZoneData = zoneData;
        }
      },
    },
  };
</script>
