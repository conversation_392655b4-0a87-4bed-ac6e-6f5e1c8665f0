<template>
  <section v-if="! company?.has_salla_polices_account && company?.slug != 'dhl'" class="alert-box alert-box--info mb-15">
    <i class="sicon-info"></i>
    <article>
      <p> {{$t('shipping.form.shipping_cost_alert')}}
        <a class="text-underline" target="_blank" href="http://help.salla.sa/article/**********">
          {{$t('shipping.help_center')}}
        </a>
      </p>
    </article> 
    <a class="btn btn-action" target="_blank" :href="buttonUrl">
      {{$t('shipping.report_preview')}}
    </a>
  </section>
</template>

<script>
import {mapState} from "vuex";

export default {
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),

    buttonUrl() {
        return window.baseUrl + "/reports";
    },
  }
}
</script>
