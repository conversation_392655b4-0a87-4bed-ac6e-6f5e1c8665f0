<template>
  <div v-if="isSupportCOD">
    <div class="seperate-line"></div>
    <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
    <CheckboxInput
      :id="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.cash_on_delivery.status`"
      :label="$t('shipping.form.activate_cod')"
      name="cash_on_delivery.status"
      :checked="selectedZoneData?.cash_on_delivery?.status ? true : false"
      is-zone
      :country-index="countryIndex"
      :zone-details-index="zoneDetailsIndex"
      :is-new-added-zone="isNewAddedZone"
    />
    <FormInput
      v-if="selectedZoneData?.cash_on_delivery?.status"
      id="cash_on_delivery.fees"
      :label="$t('shipping.form.cash_on_delivery_commission')"
      :placeholder="$t('shipping.form.cash_on_delivery_commission')"
      icon="sicon-dollar-money"
      is-numeric
      :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.cash_on_delivery.fees`"
      :value="selectedZoneData?.cash_on_delivery?.fees"
      is-zone
      :country-index="countryIndex"
      :zone-details-index="zoneDetailsIndex"
      :is-new-added-zone="isNewAddedZone"
    />
    <div v-if="selectedZoneData?.cash_on_delivery?.status && selectedZoneData?.cash_on_delivery?.salla_support_cod" class="d-block text-muted text-muted-smaller mb-5">
      <span>{{$t('shipping.form.cod_fees')}}</span>
      <!-- Has a specific offer -->
      <span v-if="parseFloat(selectedZoneData?.cash_on_delivery?.salla_cod_custom_fees) > 0
                && parseFloat(selectedZoneData?.cash_on_delivery?.salla_cod_custom_fees) < parseFloat(selectedZoneData?.cash_on_delivery?.salla_cod_fees)"
      >
        {{selectedZoneData?.cash_on_delivery?.salla_cod_custom_fees}}&nbsp;{{$t('shipping.SAR').trim()}}&nbsp;{{$t('shipping.instead_of').trim()}}
      </span>
      
      <span v-if="parseFloat(selectedZoneData?.cash_on_delivery?.salla_cod_fees) > 0">
          {{selectedZoneData?.cash_on_delivery?.salla_cod_fees}}&nbsp;{{$t('shipping.SAR').trim()}}&nbsp;
      </span>
      
      <span>
        <span v-if="selectedZoneData?.cash_on_delivery?.salla_cod_percentage">
            <span v-if="parseFloat(selectedZoneData?.cash_on_delivery?.salla_cod_fees) > 0">+</span>
           &nbsp;{{selectedZoneData?.cash_on_delivery?.salla_cod_percentage}}%&nbsp;{{$t('shipping.cod_percentage_fees').trim()}}
        </span>
        <span v-else>
           {{$t('shipping.without_tax').trim()}}
        </span>
      </span>
    </div>
  </div>
</template>

<script>
  import { mapState } from "vuex";

  export default {
    props: {
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        selectedZoneData: null,
      };
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
      }),
      isSupportCOD() {
        return (this.company?.has_salla_polices_account && this.selectedZoneData?.cash_on_delivery?.salla_support_cod)
            || (this.company?.has_salla_polices_account && this.company.slug == 'dhl' && this.selectedZoneData?.cash_on_delivery?.salla_support_cod)
            || (! this.company?.has_salla_polices_account && this.company.support_international);
      },
    },
    watch: {
      zones: {
        handler() {
          this.updateSelectedZoneData();
        },
        immediate: true,
        deep: true,
      },
      newAddedZones: {
        handler() {
          this.updateSelectedZoneData();
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      updateSelectedZoneData() {
        const zoneData = this.isNewAddedZone 
          ? this.newAddedZones[this.zoneDetailsIndex]?.zones[0] 
          : this.zones[this.countryIndex]?.zones[this.zoneDetailsIndex];

        if (zoneData) {
          this.selectedZoneData = zoneData;
        }
      },
    },
  };
</script>
