<template>
  <div class="row">
    <div class="col-xs-12">
      <FormInput
        v-if="! isFlexible"
        id="service_type"
        :label="$t('shipping.form.shipping_type')"
        icon="sicon-map-location"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.service_type`"
        :placeholder="$t('shipping.form.shipping_type')"
        :value="getTranslationServiceFromType(selectedZoneData?.service_type ?? 'shipping')"
        readonly
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
      />

      <TreeSelect
        v-if="! isFlexible"
        v-model="pricingStatus"
        select-id="status"
        :label="$t('shipping.form.status')"
        :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.status`"
        icon="sicon-shipping-fast"
        :options="pricingStatusOptions"
        :is-new-added-zone="isNewAddedZone"
        is-zone
        :country-index="countryIndex"
        :zone-details-index="zoneDetailsIndex"
      />

      <!--Start Shipping prices types -->
      <div v-if="(isPaid || company.has_salla_polices_account) && !supportOneType">
        <TreeSelect
          v-if="! supportOneType"
          v-model="priceType"
          select-id="fees.type"
          :label="$t('shipping.form.price_type')"
          :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.type`"
          icon="sicon-wallet"
          :options="getPricingTypeOptions"
          is-zone
          :country-index="countryIndex"
          :zone-details-index="zoneDetailsIndex"
          :is-new-added-zone="isNewAddedZone"
          required
        />

        <ShippingPriceTypes
          :price-type="priceType"
          :is-flexible="isFlexible"
          :country-index="countryIndex"
          :zone-details-index="zoneDetailsIndex"
          :is-new-added-zone="isNewAddedZone"
        />
      </div>
      <div v-else>
        <FormInput
          v-if="supportOneType"
          id="fees.type"
          :label="$t('shipping.form.price_type')"
          icon="sicon-wallet"
          :name="`countries.${[countryIndex]}.zones.${[zoneDetailsIndex]}.fees.type`"
          :value="getTranslationShippingPriceType(selectedZoneData?.fees?.type)"
          disabled
          is-zone
          :country-index="countryIndex"
          :zone-details-index="zoneDetailsIndex"
          :is-new-added-zone="isNewAddedZone"
          required
        />
        <ShippingPriceTypes
          :price-type="selectedZoneData?.fees?.type"
          :country-index ="countryIndex"
          :is-flexible="isFlexible"
          :zone-details-index="zoneDetailsIndex"
          :is-new-added-zone="isNewAddedZone"
        />
      </div>
      <!--End Shipping prices types -->

      <!--Start Zones Cities  -->
      <div v-if="showZoneCities">
        <div class="seperate-line"></div>
        <h6 class="mb-20 mt-0">{{$t('shipping.form.shipping_supported_cities')}}</h6>
        <!-- #FFF COUNTRY  -->
        <div v-if="isFlexible" :class="{ 'form-group': true, 'has-error': shouldShowCountryError }">
            <label>
                {{ $t('shipping.form.country') }} 
                <span class="text-danger">*</span>
            </label>
            <span class="d-block text-muted text-muted-smaller pb-10">{{ $t('shipping.form.country_sub_title') }}</span>
            <div class="input-group">
                <span class="input-group-addon"><i class="sicon-map-location"></i></span>
                    <div class="select-wrapper">
                        <shipping-dropdown 
                            :disabled="!isNewAddedZone"
                            :close-on-select="true"
                            :deselect-from-dropdown="false"
                            :value="selectedCountry" 
                            :multiple="false" 
                            label="name"
                            :options="countries" 
                            placeholder="اختر الدولة"
                            @search="fetchCountries"
                            @input="handleCountrySelect"
                        />
                </div>
            </div>
            <FormError v-if="shouldShowCountryError">{{ zoneErrors[countryKey][0] }}</FormError>
        </div>
        
        <!-- #FFF CITIES  -->
        <div :class="{ 'form-group': true, 'has-error': shouldShowCityError }">
            <label>
                {{ $t('shipping.form.supported_cities') }}
                <span class="text-danger">*</span>
            </label>
            <span class="d-block text-muted text-muted-smaller pb-10">{{ $t('shipping.form.supported_cities_with_excluded_cities_description') }}</span>
            <div class="input-group">
                <span class="input-group-addon"><i class="sicon-map-location"></i></span>
                    <div class="select-wrapper">
                        <shipping-dropdown 
                            :disabled="((!isNewAddedZone && isFlexible) || !selectedCountry)"
                            :close-on-select="false"
                            :deselect-from-dropdown="true"
                            :value="selectedCities" 
                            :multiple="true" 
                            label="name"
                            :options="citiesOptions" 
                            :placeholder="$t('shipping.form.search_for_cities')"
                            @search="fetchCities"
                            @input="handleCitiesSelect"
                        />
                </div>
            </div>
            <FormError v-if="shouldShowCityError">{{ zoneErrors[cityKey][0] }}</FormError>
        </div>
        <!-- #FFF EXCLUDED CITIES  -->
        <div v-if="hasAllCites" :class="{ 'form-group': true, 'has-error': false }">
            <label>
                {{ $t('shipping.form.excluded_cities') }}
            </label>
            <span class="d-block text-muted text-muted-smaller pb-10">{{ $t('shipping.form.excluded_cities_sub_title') }}</span>
            <div class="input-group">
                <span class="input-group-addon"><i class="sicon-map-location"></i></span>
                    <div class="select-wrapper">
                        <shipping-dropdown 
                            :disabled="!isNewAddedZone && isFlexible"
                            :close-on-select="false"
                            :deselect-from-dropdown="true"
                            :value="excludedCities" 
                            :multiple="true" 
                            label="name" 
                            :options="excludedCitiesOptions" 
                            :placeholder="$t('shipping.form.search_for_cities')" 
                            @search="fetchExcludedCities"
                            @input="handleExcludedCitiesSelect"
                        />
                </div>
            </div>
        </div>
      </div>
      <!--End Zones Cities  -->

      <lingual-field
        id="duration"
        :placeholder="$t('shipping.form.duration_for_3-5_days')"
        icon="sicon-watch-smart"
        type="input"
        :class="{'ling-field': true,'has-error': shouldShowDurationError}"
        name="duration"
        v-bind="languagesBinding"
        :translations="selectedZoneData?.translation"
        @value-updated="durationUpdatedInputs"
      >
        <template #before-input>
          <label>
            {{$t('shipping.form.duration')}}
            <span class="text-danger">*</span>
          </label>
        </template>
        <FormError v-if="shouldShowDurationError"> {{ zoneErrors[durationKey][0] }}</FormError>
      </lingual-field>

      <CODComponent
        :country-index ="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
      />

      <CoverageDistance
        :country-index ="countryIndex"
        :zone-details-index="zoneDetailsIndex"
        :is-new-added-zone="isNewAddedZone"
      />

    </div>
    <button v-if="isFlexible" class="btn btn-danger delete-zone mb-5" @click="deleteZoneForm">
      <i class="sicon-cancel"></i>
      {{ $t('shipping.form.delete_shipping_pricing')}}
    </button>
  </div>
</template>
<script>
  import { mapActions, mapState } from "vuex";
  import LingualField from "@salla.sa/languages/components/LingualField";
  import { showSwAlert } from "../../utils/showSwAlert";
  import { getTranslationServiceFromType } from "../../utils/serviceType";
  import { getTranslationShippingPriceType } from "../../utils/shippingPriceType";

  export default {
    components: {
      LingualField,
    },
    props: {
      isFlexible: {
        type: Boolean,
        default: false,
      },
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        SupportOnlyOnePriceType: ["careem", "dal", "barq"],
        companiesSupportAutomaticRate: ["aramex", "barq", "aymakan","dhl"],
        serviceTypesSupportShowCities: ["shipping", "shipping_supported_cities", "salla_international_shipping"],
        pricingTypeOptions: [
          { id: "fixed", label: `${this.$t("shipping.form.price_type_fixed")}`, name: `${this.$t("shipping.form.price_type_fixed")}` },
          { id: "rate", label: `${this.$t("shipping.form.price_type_by_weight")}`, name: `${this.$t("shipping.form.price_type_by_weight")}` },
        ],
        pricingStatusOptions: [
          { id: "1", label: `${this.$t("shipping.form.active")}`, name: "status_1" },
          { id: "0", label: `${this.$t("shipping.form.not_active")}`, name: "status_0" },
        ],
        priceType: null,
        pricingStatus: null,
        selectedCountry: {},
        selectedCities: [],
        excludedCities: [],
        city_pricing: [],
        citiesOptions: [],
        excludedCitiesOptions: [],
        hasAllCites: true,
        countryNormalizer(node) {
          return {
            id: node.id,
            label: node.name,
          };
        },
        selectedZoneData: null,
        languages: null,
      };
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
        countries: (state) => state.shippingCompanyZones.countries,
        zoneErrors: (state) => state.shippingCompanyZones.zoneErrors,
      }),
      supportOneType() {
        return this.company.has_salla_polices_account && this.SupportOnlyOnePriceType.includes(this.company?.slug);
      },
      isPaid() {
        return window.initialData.is_paid;
      },
      getPricingTypeOptions() {
        const isAutomaticRate = (this.companiesSupportAutomaticRate.includes(this.company?.slug) && (!this.company?.has_salla_polices_account || (this.company?.has_salla_polices_account && this.company?.support_international)));
        const automaticArray = { id: "automatic", label: `${this.$t("shipping.form.automatic")}`, name: `${this.$t("shipping.form.automatic")}` };
        const options = [...this.pricingTypeOptions];
        
        if (isAutomaticRate) {
          options.push(automaticArray);
        }
        return options;
      },
      languagesBinding() {
        if (this.languages.supported.length > 0) {
          return {
            languages: this.languages
          };
        } else {
          return {};
        }
      },
        durationKey() {
            if (this.isNewAddedZone) {
                return `country.${this.zoneDetailsIndex}.zones.0.duration`;
            } else {
                return `zone.${this.countryIndex}.zones.${this.zoneDetailsIndex}.duration`;
            }
        },
        shouldShowDurationError() {
            return Object.keys(this.zoneErrors).length > 0 && this.zoneErrors[this.durationKey];
        },
        countryKey() {
            if (this.isNewAddedZone) {
                return `country.${this.zoneDetailsIndex}.id`;
            } else {
                return `zone.${this.countryIndex}.id`;
            }
        },
        shouldShowCountryError() {
          return Object.keys(this.zoneErrors).length > 0 && this.zoneErrors[this.countryKey];
      },
        cityKey() {
            if (this.isNewAddedZone) {
                return `country.${this.zoneDetailsIndex}.zones.0.city`;
            } else {
                return `zone.${this.countryIndex}.zones.${this.zoneDetailsIndex}.city`;
            }
        },
        shouldShowCityError() {
          return Object.keys(this.zoneErrors).length > 0 && this.zoneErrors[this.cityKey];
      },
      showZoneCities() {
        return this.isFlexible || this.serviceTypesSupportShowCities.includes(this.selectedZoneData?.service_type);
      },

    },
    watch: {
      zones: {
        handler() {
          this.updateSelectedZoneFromZones();
        },
        immediate: true,
        deep: true,
      },
        newAddedZones: {
            handler() {
                this.updateSelectedZoneFromNewAddedZones();
            },
            immediate: true,
            deep: true,
        },
    },
    created() {
      this.languages = window.dashboard_settings.languages;
    },
    mounted() {
       $('.rec-ls-field:not(.field-lang-initiated)').each((index, fieldInput) => window.initFieldLang($(fieldInput)));
    },
    methods: {
      ...mapActions(["getZonesCitiesList", "getBaseInTypeCitiesList", "deleteZone", "getZonesCountriesList"]),
      getTranslationServiceFromType: getTranslationServiceFromType,
      getTranslationShippingPriceType: getTranslationShippingPriceType,
        updateSelectedZoneFromZones() {
            if (! this.isNewAddedZone) {
                this.updateSelectedZoneData(this.zones[this.countryIndex]?.zones[this.zoneDetailsIndex], {
                    id: this.zones[this.countryIndex]?.id,
                    name: this.zones[this.countryIndex]?.name,
                });
            }
       },
        updateSelectedZoneFromNewAddedZones() {
            if (this.isNewAddedZone) {
                this.updateSelectedZoneData(this.newAddedZones[this.zoneDetailsIndex]?.zones[0], {
                    id: this.newAddedZones[this.zoneDetailsIndex].id,
                    name: this.newAddedZones[this.zoneDetailsIndex].name,
                });
            }
         },
        updateSelectedZoneData(zone, zoneCountry) {
            if (zone) {
                this.selectedZoneData = zone;
                this.pricingStatus = zone.status ?? "1";
                this.priceType = zone.fees?.type;
                let currentCities = [], currentExcludedCities = [];

                !Array.isArray(zone.city) && (zone.city = []);

                currentCities = zone.city.map((item) => {
                    return {
                        id: item.id,
                        name: item.name,
                    };
                });

                !Array.isArray(zone.cities_excluded) && (zone.cities_excluded = []);

                currentExcludedCities = zone.cities_excluded?.map((item) => {
                    return {
                        id: item.id,
                        name: item.name,
                    };
                });

                this.selectedCountry = (zoneCountry && 'name' in zoneCountry && zoneCountry.name) ? {...zoneCountry} : null;
                this.selectedCities = [...currentCities];
                this.excludedCities = [...currentExcludedCities];

                this.hasAllCites = currentCities.some((city) => city.id == -1) && zoneCountry?.id != -1;
            }
        },
        async fetchExcludedCities(data = {}) {
            let cities = await this.fetchCitiesListBasedOnCondition(data);
            let selectedCitiesIds = [...this.selectedCities].map((city) => city.id);
            cities = [...this.excludedCities, ...cities].filter((value, index, self) => {
                if (value.id == -1 || selectedCitiesIds.includes(value.id)) {
                    return false;
                }

                return index === self.findIndex((t) => (
                    t.id === value.id
                ))
            });

            this.excludedCitiesOptions = [...cities];
        },
        fetchCountries(data) { 
            this.getZonesCountriesList({slug: this.company.slug, ...data});
        },
        async fetchCities(data) {
            let cities = await this.fetchCitiesListBasedOnCondition(data);
            cities = [
                ...this.selectedCities,
                ...cities
            ].filter((value, index, self) =>
                index === self.findIndex((t) => (
                    t.id === value.id
                ))
            );

            const hasAllCites = cities.some((city) => city.id == -1);

            if (hasAllCites) {
                cities = cities.filter((city) => city.id != -1);

                cities = [
                    {
                        code: "-",
                        id: -1,
                        name: "كل المدن",
                        name_en: "All Cities",
                    },
                    ...cities,
                ];
            }

            this.citiesOptions = [...cities];
        },

        async fetchCitiesListBasedOnCondition(data = {}) {
            let citiesListPromise;
            if (this.isFlexible) {
                citiesListPromise = this.getZonesCitiesList({ countryId: this.selectedCountry?.id, ...data });
            } else {
                citiesListPromise = this.getBaseInTypeCitiesList({ zoneId: this.selectedZoneData?.id, ...data });
            }

            try {
                const citiesList = await citiesListPromise;
                return citiesList;
            } catch (error) {
                throw new Error("Error while fetching cities list");
            }
        },
        handleCitiesSelect(cities) {
            const hasAllCites = cities.some((city) => city.id == -1) && this.selectedCountry?.id != -1;
            this.hasAllCites = hasAllCites;
            this.selectedCities = cities;
            this.excludedCities = [];

            this.updateZoneCities("city", cities);
        },
        handleExcludedCitiesSelect(cities) {
            this.excludedCities = cities;
            this.updateZoneCities("cities_excluded", cities);
        },
        handleCountrySelect(country) {
            if (!this.isNewAddedZone) {
                return;
            }
            
            this.selectedCities = [];
            this.excludedCities = [];
            this.citiesOptions = [];
            this.excludedCitiesOptions = [];

            const newZonesCopy = JSON.parse(JSON.stringify(this.newAddedZones));
            
            const newZoneDetailToUpdate = {
                ...newZonesCopy[this.zoneDetailsIndex],
                zones: newZonesCopy[this.zoneDetailsIndex].zones.map((zone) => ({
                    ...zone,
                    city: [],
                    cities_excluded: [],
                })),
                id: country ? country.id : null,
                name: country ? country.name : null,
            };
            
            newZonesCopy[this.zoneDetailsIndex] = newZoneDetailToUpdate;

            this.$store.commit("setNewAddedZones", [...newZonesCopy]);
        },
        updateZoneCities(columnName, value) {
            if (this.isNewAddedZone) {
                let newAddedZones = JSON.parse(JSON.stringify(this.newAddedZones));
                newAddedZones[this.zoneDetailsIndex]['zones'][0][columnName] = value;
                this.$store.commit("setNewAddedZones", [...newAddedZones]);
                return;
            }

            const newZonesCopy = JSON.parse(JSON.stringify(this.zones));
            const zoneToUpdate = newZonesCopy[this.countryIndex];
            const zoneDetailsCopy = [...zoneToUpdate.zones];
            const zoneDetailToUpdate = {
                ...zoneDetailsCopy[this.zoneDetailsIndex],
                [columnName]: value
            };

            zoneDetailsCopy[this.zoneDetailsIndex] = zoneDetailToUpdate;
            zoneToUpdate.zones = zoneDetailsCopy;
            newZonesCopy[this.countryIndex] = zoneToUpdate;
            this.$store.commit("setShippingCompanyZones", newZonesCopy);
        },
        
      durationUpdatedInputs(event) {
        const storeIsoCode = window.dashboard_settings.languages.current.iso_code
        const langCode = event.isoCode;
        if (this.isNewAddedZone) {
          const newZonesCopy = JSON.parse(JSON.stringify(this.newAddedZones));
          const zoneToUpdate = newZonesCopy[this.zoneDetailsIndex];
          const newZoneDetailsCopy = [...zoneToUpdate.zones];
          const newTranslation = {
            ...newZoneDetailsCopy[0].translation,
            [langCode]: {
              duration: event.value,
            },
          };

          if (langCode !== 'ar') {
            newTranslation['ar'] = {
              duration: event.value,
            };
          }

          const newZoneDetailToUpdate = {
            ...newZoneDetailsCopy[0],
            translation: newTranslation,
          };

          if (langCode === storeIsoCode || ! storeIsoCode) {
            newZoneDetailToUpdate.duration = event.value
          }          

          newZoneDetailsCopy[0] = newZoneDetailToUpdate;
          zoneToUpdate.zones = newZoneDetailsCopy;
          newZonesCopy[this.zoneDetailsIndex] = zoneToUpdate;
          this.$store.commit("setNewAddedZones", newZonesCopy);
        } else {
          const newZonesCopy = JSON.parse(JSON.stringify(this.zones));
          const zoneToUpdate = newZonesCopy[this.countryIndex];
          const zoneDetailsCopy = [...zoneToUpdate.zones];
          const zoneDetailToUpdate = {
            ...zoneDetailsCopy[this.zoneDetailsIndex],
            translation: {
              ...zoneDetailsCopy[this.zoneDetailsIndex].translation,
              [langCode]: {
                duration: event.value,
              },
            },
          };
          if (langCode === storeIsoCode || ! storeIsoCode) {
            zoneDetailToUpdate.duration = event.value
          }
          zoneDetailsCopy[this.zoneDetailsIndex] = zoneDetailToUpdate;
          zoneToUpdate.zones = zoneDetailsCopy;
          newZonesCopy[this.countryIndex] = zoneToUpdate;
          this.$store.commit("setShippingCompanyZones", newZonesCopy);
        }
      },
      async deleteZoneForm() {
        if (this.isNewAddedZone) {
          let copyNewAddedZones = JSON.parse(JSON.stringify(this.newAddedZones));
          copyNewAddedZones.splice(this.zoneDetailsIndex, 1);
          this.$store.commit("setNewAddedZones", copyNewAddedZones);
        } else {
          const updatedZones = this.zones;
          if (updatedZones[this.countryIndex] && updatedZones[this.countryIndex].zones && updatedZones[this.countryIndex].zones[this.zoneDetailsIndex] && updatedZones[this.countryIndex].zones[this.zoneDetailsIndex].id) {
            const zoneId = updatedZones[this.countryIndex]?.zones[this.zoneDetailsIndex]?.id;

            swal({
              title: "تنبيه ",
              text: "هل انت متاكد من هذا الاجراء ؟",
              type: "error",
              showConfirmButton: true,
              showCancelButton: true,
              confirmButtonText: "تأكيد",
              cancelButtonText: "إلغاء",
            }).then(async () => {
              try {
                await this.deleteZone(zoneId).then(() => {
                  updatedZones[this.countryIndex].zones.splice(this.zoneDetailsIndex, 1);

                  if (updatedZones[this.countryIndex].zones.length === 0) {
                    updatedZones.splice(this.countryIndex);
                  }
                  this.$store.commit("setShippingCompanyZones", updatedZones);
                  showSwAlert("success", "تم الحذف بنجاح", undefined, false, 3000);
                });
              } catch (error) {
                showSwAlert("error", "حدث خطأ غير متوقع, الرجاء المحاولة لاحقًا.", undefined, false, 3000);
              }
            });
          }
        }
      },
    },
  };
</script>
