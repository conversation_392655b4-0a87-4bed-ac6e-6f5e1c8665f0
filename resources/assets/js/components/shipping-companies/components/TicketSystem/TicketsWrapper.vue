<template>
  <div class="panel panel-default">
    <div class="panel-body p-0">
      <ul class="rec-list rec-list--vertical rec-list--bordered light-border">
        <li class="p-20">
          <a href="#" class="card card--horizontal card--clickable" @click.prevent="navigateTo('/shipping/tickets')">
            <div class="card__header">
              <i class="sicon-chat-bubbles-alt text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">
                بوابة الجودة
              </h6>
              <span class="text-muted text-muted-small">
                تواصل مع شركات الشحن في بوليصات سلة لرفع شكاوى الشحن ومتابعتها.
              </span>
            </div>
            <small v-show ="notificationsCount > 0" class="badge badge--danger no-border top left">
              {{notificationsCount}}
            </small>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        isLegacy: window.sallaLegacy
      };
    },
    computed: {
      notificationsCount() {
        return window.initialData.complaints_notification_count
      },
    },
    methods: {
        navigateTo(path) {
          if (this.isLegacy) {
            this.$router.push(path);
          } else {
            window.parent?.postMessage({ event: "navigateTo", path: path }, "*");
          }
        }
    },
  };
</script>
