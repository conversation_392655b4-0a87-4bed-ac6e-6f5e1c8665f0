<template>
  <div ref="filter" class="rec-filter-wrapper">
    <div class="rec-filter-cont">
      <form>
        <div class="rec-filters">
          <h1 class="title title--small">
            <i class="sicon-filter"></i>
            {{is_packaging ? 'تصفية الطلبات' : 'تصفية الشكاوى'}}
            <button id="filter_close" type="button" class="btn btn--circular" @click="removedClass"></button>
          </h1>

          <!-- complaint status filter -->
          <div class="rec-filter__section">
            <button type="button" data-target="#sorted_by" data-toggle="collapse" aria-expanded="true" class="btn filter-head">
              <span>
                 {{is_packaging ? 'حالة الطلب' : 'حالة الشكوى'}}
              </span>
            </button>
            <div id="sorted_by" aria-expanded="true" class="collapse filter-content collapse in">
              <ul class="rec-list rec-list--vertical">
                <li v-for="(status, index) in filteredData.states" :key="index">
                  <div class="rec-checkbox rec-checkbox--default light">
                    <input :id="status.name" v-model="selectedStatus" type="radio" :name="status.name" :value="status.name" class="groups_filters" />
                    <label :for="status.name">
                      <span>{{ status.label }}</span>
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          
          <!-- complaint created by filter -->
          <div v-if="!is_packaging && filteredData.created_by" class="rec-filter__section">
            <button type="button" data-target="#created_by_section" data-toggle="collapse" aria-expanded="true" class="btn filter-head">
              <span>
                مصدر الشكوى
              </span>
            </button>
            <div id="created_by_section" aria-expanded="true" class="collapse filter-content collapse in">
              <ul class="rec-list rec-list--vertical">
                <li v-for="(filter, index) in filteredData.created_by" :key="`created-by-${index}`">
                  <div class="rec-checkbox rec-checkbox--default light">
                    <input :id="`created-by-${filter.value}`" v-model="selectedCreatedBy" type="radio" name="created_by" :value="filter.value" class="groups_filters" />
                    <label :for="`created-by-${filter.value}`">
                      <span>{{ filter.label }}</span>
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <div class="rec-filter__section">
            <button type="button" data-target="#shipping_company" data-toggle="collapse" aria-expanded="true" class="btn filter-head">
              <span>شركة الشحن</span>
            </button>
            <div id="shipping_company" aria-expanded="true" class="collapse filter-content collapse in">
              <ul class="rec-list rec-list--vertical">
                <li v-for="(company, index) in filteredData.companies" :key="index">
                  <div class="rec-checkbox rec-checkbox--default light">
                    <input :id="company.id" v-model="selectedCompanies" name ="company" type="checkbox" :value="company.id" class="groups_filters" />
                    <label :for="company.id" class="rec-list align-items-center justify-content-start">
                      <div class="logo-box py-0">
                        <img :src="company.logo" class="company-logo" />
                      </div>
                      <span class="ml-10">{{ company.name }}</span>
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <div class="rec-filter__section">
            <button type="button" data-toggle="collapse" data-target="#filter_date_options" class="btn filter-head" aria-expanded="true">
              <span>
                <i class="sicon-calendar-dates"></i>
                {{is_packaging ? ' تاريخ الطلب' : ' تاريخ الشكوى'}}
              </span>
            </button>

            <div id="filter_date_options" class="filter-content collapse in" aria-expanded="true" style="">
              <div
                :class="{
                    'form-group mb-5': true,
                  }"
              >
                <date-picker
                  id="from_date"
                  v-model="fromDate"
                  :style="{ width: '100%' }"
                  value-type="format"
                  :clearable="false"
                  class="mx--right-position"
                  :append-to-body="true"
                  :popup-style="{zIndex: 9999999999}"
                  name="from_date"
                  placeholder="من"
                  input-class="form-control full"
                />
              </div>
              <div
                :class="{
                    'form-group mb-5': true,
                  }"
              >
                <date-picker
                  id="to_date"
                  v-model="toDate"
                  :style="{ width: '100%' }"
                  value-type="format"
                  :clearable="false"
                  class="mx--right-position"
                  :append-to-body="true"
                  :popup-style="{zIndex: 9999999999}"
                  name="to_date"
                  placeholder="إلى"
                  input-class="form-control full"
                />
              </div>
            </div>
          </div>

          <div class="rec-filter__submit">
            <button type="button" class="btn btn-tiffany btn-filter-submit" @click="submit">
              عرض النتائج
            </button>
            <button type="reset" class="btn btn-outline-dark btn-filter-reset" @click="onReset">
              إعادة تعيين
            </button>
          </div>
          
          <div class="rec-filter__section mt-10">
            <div class="btn-group rec-btn-group full-width" role="group">
              <button
                      id="filterOptions"
                      type="button"
                      class="btn btn-wide btn-default btn-icon-prepend rec-fvm"
                      :disabled="is_disabled"
                      @click="onExport">
                <i class="sicon-mail-download"></i>
                تصدير النتائج
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
  import DatePicker from "vue2-datepicker";
  import "vue2-datepicker/index.css";
  import { mapActions, mapState } from 'vuex'

  export default {
    name: "SideBarFilter",
    components: {
      DatePicker,
    },
    props: {
      is_disabled: {
        type: Boolean,
        default: false
      },
      is_packaging:{
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        ticketStatus: [],
        selectedStatus: "",
        selectedCreatedBy: "",
        selectedCompanies: [],
        fromDate: '',
        toDate: '',
      };
    },
    computed: {
      ...mapState({
        ticketDataFilter: (state) => state.ticketSystem.ticketDataFilter,
        packagingFilter: (state) => state.packaging.packagingFilter,
      }),
      filteredData() {
        return this.is_packaging ? this.packagingFilter : this.ticketDataFilter;
      }
    },
    methods: {
      removedClass() {
        this.$refs.filter.classList.remove("reveal");
      },
      onReset() {
        this.$refs.filter.classList.remove("reveal");
        this.selectedStatus = "";
        this.selectedCreatedBy = "";
        this.selectedCompanies = [];
        this.fromDate = '';
        this.toDate = '';

        this.$emit('resetFilter', {
          state : "",
          companies : [],
          from_date: '',
          to_date: ''
        });
      },
      submit() {
        this.$emit('addFilter', {
          state: this.selectedStatus,
          created_by: this.selectedCreatedBy,
          companies: [...this.selectedCompanies],
          from_date: this.fromDate,
          to_date: this.toDate
        });

       this.$refs.filter.classList.remove("reveal");
      },
      onExport() {
        this.$emit('exportFilter', {
          state: this.selectedStatus,
          created_by: this.selectedCreatedBy,
          companies: [...this.selectedCompanies],
          from_date: this.fromDate,
          to_date: this.toDate
        });

       this.$refs.filter.classList.remove("reveal");
      },
    },
  };
</script>
