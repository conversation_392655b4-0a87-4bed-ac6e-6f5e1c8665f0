<template>
  <modal id="creeat_ticket_modal" modal-title="إرسال شكوى جديدة" :show-save="true" save-btn="إرسال الشكوى" :show-rest="false" :disabled="disableSaveBtn ? true : false" @saveModal="handleCreateTicket">
    <div :class="{'form-group': true,'has-error': Object.keys(creatTicketErrors).length > 0 && creatTicketErrors['shipping_number']}">
      <label>
        رقم بوليصة الشحن
        <span class="text-danger">*</span>
      </label>
      <select id="shipping_number_searchbox" placeholder="ابحث برقم بوليصة الشحن..." class="form-control searchbox searchbox--with-icon selectized" tabindex="-1" style="display: none;">
        <option value="" selected="selected"></option>
      </select>
      <div class="selectize-control form-control searchbox single plugin-no_results rtl">
        <div class="form-group">
          <div class="search-group d-flex input-group">
            <div class="selectize-input items not-full has-options">
              <input 
                id="shipping_number" 
                v-model="searchQuery" 
                type="text" 
                autocomplete="off" 
                tabindex="" 
                placeholder="ابحث برقم بوليصة الشحن..." 
                style="width: 244px; opacity: 1; position: relative; left: 0px;" 
                @keyup.enter="searchOnEnter" 
              />
            </div>
            <button class="btn btn-tiffany input-group-btn pl-30" @click="searchOnEnter">
              <i class="sicon-search"></i>
            </button>
          </div>
        </div>
        <FormError v-if="Object.keys(creatTicketErrors).length > 0 && creatTicketErrors['shipping_number']">{{ creatTicketErrors['shipping_number'][0] }}</FormError>
        <div
          v-show="orderNotFound && !hideOrderDetailsSection"
          ref="orderNotFoundSection"
          class="selectize-dropdown form-control searchbox searchbox--with-icon selectized dropdown-empty-message"
          style="top: 42px; left: 0px; visibility: visible;"
        >
          <div class="selectize-dropdown-content rec-list rec-list--vertical align-items-center text-muted">
            <i class="sicon-page-search mb-10"></i>
            لم يتم العثور على شحنة تتطابق مع رقم البوليصة المُدخل
          </div>
        </div>

        <div
          v-show="Object.keys(orderDetails).length > 0 && !hideOrderDetailsSection"
          ref="orderDetailsSection"
          class="selectize-dropdown single form-control searchbox searchbox--with-icon plugin-no_results"
          style="top: 42px; left: 0px; visibility: visible;"
        >
          <div class="selectize-dropdown-content">
            <div class="media pt-5 pb-5" @click="selectOrder(orderDetails)">
              <div class="media-left">
                <img src="cp/assets/images/avatar_male.png" class="img-circle" alt="" />
              </div>
              <div class="media-body">
                <div class="order-customer-name">
                  {{orderDetails.receiver?.name}}
                </div>
                <div class="order-number ml-5">
                  #{{orderDetails.order_id}}
                </div>
                <div class="order-city ml-5">
                  <i class="sicon-location"></i>
                  {{orderDetails?.receiver?.country}}
                </div>
                <div class="order-source ml-5">
                  <i class="sicon-shipping"></i>
                  {{orderDetails.company?.name}}
                </div>
                <div class="order-status ml-5 p-0">
                  <i class="sicon-content"></i>
                  {{orderDetails.shipping_number}}
                </div>
                <div v-if="!orderDetails?.is_salla_policy" class="text-danger mt-10">
                  لا يمكن إرسال شكوى تخص هذا الطلب حيث تم شحنه عبر شركة شحن خاصة.
                </div>
                <div v-else-if="!orderDetails?.company?.support_ticket_system" class="text-danger mt-10">
                  شركة الشحن غير متاحة حاليًا في بوابة الجودة. يُرجى التواصل مع الشركة مباشرة لمعالجة شكاوى الشحن.
                </div>
              </div>
              <div class="media-right media-middle"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="my-10">
      <div class="grid-container">
        <div class="grid-item">
          <i class="sicon-newspaper text-muted"></i>
          <span class="text-muted ml-10">رقم البوليصة</span>
          <span class="ml-20">{{selectedOrder?.shipping_number || "-"}}</span>
        </div>
        <div class="grid-item">
          <i class="sicon-user text-muted"></i>
          <span class="text-muted ml-10">اسم العميل</span>
          <span class="ml-20">{{selectedOrder?.receiver?.name || "-"}}</span>
        </div>
        <div class="grid-item">
          <i class="sicon-phone text-muted"></i>
          <span class="text-muted ml-10">جوال العميل</span>
          <span class="ml-20">{{selectedOrder?.receiver?.mobile || "-"}}</span>
        </div>
        <div class="grid-item align-items-center">
          <i class="sicon-shipping text-muted"></i>
          <span class="text-muted ml-10">شركة الشحن</span>
          <div class="rec-list align-items-center ml-20">
            <div v-if="selectedOrder?.company?.logo" class="logo-box">
              <img :src="selectedOrder?.company?.logo" class="company-logo" />
            </div>
            <span class="ml-10">{{selectedOrder?.company?.name || "-"}}</span>
          </div>
        </div>
        <div class="grid-item">
          <i class="sicon-shipping text-muted"></i>
          <span class="text-muted ml-10">رقم الفاتورة</span>
          <span class="ml-20">{{selectedOrder?.invoice_id || "-"}}</span>
        </div>
        <div class="grid-item">
          <i class="sicon-shipping text-muted"></i>
          <span class="text-muted ml-10">رقم الطلب</span>
          <span class="ml-20">{{selectedOrder?.order_id || "-"}}</span>
        </div>
      </div>
    </div>

    <div v-show="Object.keys(selectedOrder).length > 0">
      <TreeSelect 
        v-model="selectedTicketType" 
        select-id="type_id" 
        label="نوع الشكوى" 
        placeholder="اختر نوع الشكوى" 
        :options="ticketTypes" 
        :multiple="false" 
        :searchable="false" 
        :required="true" 
        :is-ticket-system="true"
      />
      <div v-if="selectedTicketType === '1' || selectedTicketType === '2' || selectedTicketType === '3'" :class="{'form-group mb-0': true,'has-error': creatTicketErrors && creatTicketErrors['attachment_urls']}">
        <filepond 
          v-model="shippingImg"
          :crop-aspect-ratio="'null'" 
          label="اضافة صورة للشحنة" 
          pond-ref="owner" 
          :finished-upload="shippingImg !== null" 
          :img-src="shippingImg" 
          :uploading="shippingImg == null" 
          :required="true" 
        />
        <formError v-if="Object.keys(creatTicketErrors).length > 0 && creatTicketErrors['attachment_urls']">{{creatTicketErrors['attachment_urls'][0]}}</formError>
      </div>

      <div v-if="selectedTicketType === '1' || selectedTicketType === '2'" :class="{'form-group': true,'has-error': creatTicketErrors && creatTicketErrors['cost']}">
        <label>
          تكلفة المنتجات الأساسية
          <span class="text-danger">*</span>
        </label>
        <div class="input-group">
          <input 
            id="cost" 
            v-model="productsCost" 
            type="number" 
            class="form-control full-bordered _parseArabicNumbers" 
            @input="handleCost" 
          />
          <span class="input-group-addon">رس</span>
        </div>
        <FormError v-if="Object.keys(creatTicketErrors).length > 0 && creatTicketErrors['cost']">{{ creatTicketErrors['cost'][0] }}</FormError>
      </div>
      <div :class="{'form-group': true,'has-error': creatTicketErrors && creatTicketErrors['description']}">
        <h6 class="font-15">
          تفاصيل الشكوى
          <span v-if="selectedTicketType == 5" class="text-danger">*</span>
          <span v-else class="text-muted">(اختياري)</span>
        </h6>
        <textarea
          id="description"
          :value="ticketDetailsText"
          name="ticket_details"
          rows="3"
          class="form-control"
          placeholder="أدخل تفاصيل المشكلة التي واجهتك في عملية الشحن..."
          @input="handleDescription"
        ></textarea>
        <FormError v-if="Object.keys(creatTicketErrors).length > 0 && creatTicketErrors['description']">{{ creatTicketErrors['description'][0] }}</FormError>
      </div>
    </div>
  </modal>
</template>

<script>
  import { mapState, mapActions } from "vuex";
  export default {
    props: {},
    data() {
      return {
        ticketTypes: [
          { id: '1', label: "ضياع الشحنة", name: "ضياع الشحنة" },
          { id: '2', label: "تلف الشحنة", name: "تلف الشحنة" },
          { id: '3', label: "احتساب تكلفة إضافية", name: "احتساب تكلفة إضافية" },
          { id: '4', label: "الإشكاليات المتعلقة بالاستلام والتسليم", name: "الإشكاليات المتعلقة بالاستلام والتسليم" },
          { id: '6', label: "إلغاء البوليصة", name: "إلغاء البوليصة"},
          { id: '5', label: "سبب آخر", name: "سبب آخر" },
        ],
        selectedTicketType: null,
        ticketDetailsText: "",
        productsCost: 0,
        searchQuery: "",
        selectedOrder: {},
        orderDetails: {},
        hideOrderDetailsSection: true,
        orderNotFound: false,
        shippingImg: null,
      };
    },

    computed: {
      ...mapState({
        ordersList: (state) => state.ticketSystem.ordersList,
        creatTicketErrors: (state) => state.ticketSystem.creatTicketErrors,
      }),
      isSearchQueryEmpty() {
        return this.searchQuery === "";
      },
      disableSaveBtn() {
        return this.isShippingNumberEmpty;
      },
    },

    isShippingNumberEmpty() {
      return typeof this.selectedOrder.shipping_number === "string" && this.selectedOrder.shipping_number.trim() === "";
    },

    mounted() {
      document.addEventListener("click", this.handleOutsideClick);
    },
    destroyed() {
      document.removeEventListener("click", this.handleOutsideClick);
    },
    methods: {
      ...mapActions(["setOrdersList", "createTicket"]),
      validatePayload(payload) {
        const requiredFields = ["shipping_number", "type_id"];
        const errors = {};
        if (this.selectedTicketType === "1" || this.selectedTicketType === "2" || this.selectedTicketType === "3") {
          requiredFields.push("attachment_urls");
        }

        if (this.selectedTicketType === "1" || this.selectedTicketType === "2") {
          requiredFields.push("cost");
        }

        if (this.selectedTicketType === "5") {
          requiredFields.push("description");
        }
                
        for (const field of requiredFields) {
          if (!payload[field] || payload[field] === null || payload[field].length < 1 || (typeof payload[field] === "string" && payload[field].trim() === "") || (field === "attachment_urls" && payload[field][0] == null)) {
            if (!errors[field]) {
              errors[field] = [];
            }
            errors[field].push(`الحقل مطلوب.`);
          }
        }

        return errors;
      },
      handleCreateTicket() {
        const payload = {
          order_id: this.selectedOrder?.order_id,
          order_invoice_id: this.selectedOrder?.invoice_id,
          company_id: this.selectedOrder?.company?.id,
          shipping_number: this.selectedOrder?.shipping_number,
          description: this.ticketDetailsText,
          type_id: this.selectedTicketType,
        };
        if (this.selectedTicketType === "1" || this.selectedTicketType === "2" || this.selectedTicketType === "3") payload["attachment_urls"] = this.shippingImg;
        if (this.selectedTicketType === "1" || this.selectedTicketType === "2") payload["cost"] = this.productsCost;

        const errors = this.validatePayload(payload);
        this.$store.commit("setCreatTicketErrors", errors);

        if (Object.keys(errors).length === 0) {
          this.createTicket(payload).then(data => {
            if (data?.success) {
              window.location.reload();
            }
          });
        }
      },
      updateErrors(id) {
        const errorsCopy = { ...this.$store.state.ticketSystem.creatTicketErrors };
        delete errorsCopy[id];
        this.$store.commit("setCreatTicketErrors", errorsCopy);
      },
      searchOnEnter() {
        this.updateErrors("shipping_number");
        this.hideOrderDetailsSection = true;
        this.orderDetails = {};
        this.selectedOrder = {};

        const payload = {
          shipping_number: this.searchQuery,
        };

        this.setOrdersList(payload)
          .then((response) => {
            if (!response.data?.success && response.data?.error?.fields?.shipping_number) {
              this.hideOrderDetailsSection = false;
              this.orderNotFound = true;
              this.orderDetails = {};
              this.selectedOrder = {};
            } else {
              this.hideOrderDetailsSection = false;
              this.orderNotFound = false;
              this.orderDetails = response.data;
            }
          }).catch(() => {});
      },
      handleCost() {
        this.updateErrors("cost");
      },
      handleDescription(event) {
        this.updateErrors("description");
        this.ticketDetailsText = event.target.value
      },
      selectOrder(order) {
        if (this.orderDetails?.company?.support_ticket_system && this.orderDetails?.is_salla_policy) this.selectedOrder = order;
      },
      handleOutsideClick(event) {
        const orderDetailsSection = this.$refs.orderDetailsSection;
        const orderNotFoundSection = this.$refs.orderNotFoundSection;
        if ((orderDetailsSection && !orderDetailsSection.contains(event.target)) || (orderNotFoundSection && !orderNotFoundSection.contains(event.target))) {
          this.hideOrderDetailsSection = true;
        }
      },
    },
  };
</script>
