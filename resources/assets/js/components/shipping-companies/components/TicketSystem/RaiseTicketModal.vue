<template>
  <modal id="raise_ticket_modal" modal-title="تصعيد الشكوى" :show-save="true" save-btn="تصعيد الشكوى" :show-rest="false" :disabled="disableSaveBtn ? true : false" @saveModal="raise">

    <TreeSelect v-model="reason" select-id="reason" label="سبب التصعيد" placeholder="اختر سبب التصعيد  *" :options="escalationTypes" :multiple="false" :searchable="false" :required="true" :is-ticket-system="true" />


    <div v-if="reason == 3" :class="{ 'form-group': true, 'has-error': false }">
      <label>
        اذكر سبب التصعيد
        <span v-if="reason == 3" class="text-danger">*</span>
      </label>
      <textarea id="notes" :value="notes" placeholder="سبب التصعيد" name="notes" rows="2" class="form-control" @input="notes = $event.target.value"></textarea>
    </div>

  </modal>
</template>

<script>
import { mapActions } from "vuex";
import { showSwAlert } from "../../utils/showSwAlert";

export default {
  props: {
    ticketId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      reason: "1",
      notes: "",
      escalationTypes: [
        { id: '1', label: "لم يتم حل الشكوى", name: "لم يتم حل الشكوى" },
        { id: '2', label: "الحل غير مرض", name: "الحل غير مرض" },
        { id: '3', label: "أخرى", name: "أخرى" },
      ],
    };
  },

  computed: {
    disableSaveBtn() {
      return !this.reason || (this.reason == 3 && !(("" + this.notes.trim()).length));
    },
  },
  methods: {
    ...mapActions(["raiseTicket"]),
    resetForm() {
      this.reason = "1";
      this.notes = "";
    },
    validateForm() {
      if (!this.reason || (this.reason == 3 && !(("" + this.notes.trim()).length))) {
        return false;
      }
      return true;
    },
    raise() {
      if (!this.validateForm()) {
        console.error("Validation failed");
        return;
      }
      
      const payload = {
        reason_for_escalation: this.escalationTypes.find(item => item.id == this.reason).id == 3 ? this.notes : this.escalationTypes.find(item => item.id == this.reason).name,
      };
      
      window.showLoading();
      
      this.raiseTicket({ticketId: this.ticketId, payload}).then(() => {
        window.hideLoading();
        this.$emit("ticketRaised");
        $('#raise_ticket_modal').modal('hide');
        this.resetForm();
        showSwAlert("success", "تم تصعيد الشكوي بنجاح", undefined, false, 3000);
      }).catch(error => {
        console.error("Error raising ticket:", error);
        window.hideLoading();
        showSwAlert("error", "حدث خطأ , برجاء المحاوله في وقت لاحق", undefined, false, 3000);
      });
    },
  },
};
</script>
