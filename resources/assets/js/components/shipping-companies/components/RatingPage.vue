<template>
  <div>
    <div v-if="loaded" class="panel panel-default">
      <div class="panel-body no-padding">
        <div class="p-20 bg-gray-50">
          <div class="card card--horizontal">
            <div class="card__header mr-20">
              <div class="company-details-container mr-10">
                <img :src="company?.logo" class="company-details-logo" style="width: 52px; height: 52px" />
              </div>
            </div>
            <div class="card__content mr-20">
              <article class="rec-list rec-list--vertical">
                <h2 class="font-18 d-block text-primary m-0">
                  {{ company?.name }}
                </h2>
                <shipping-rating :rating="parseInt(company?.review?.statistics?.rating_avg)" :reviews="parseInt(company?.review?.statistics?.reviews_count)" :flat="true" />
              </article>
            </div>
            <div v-if="company?.review?.status != 'not_review'" class="card__footer">
              <a v-if="
                company?.review?.status == 'need_review' ||
                (company?.review?.status == 'reviewed' && company?.review?.feedback)
              " class="btn btn-tiffany" href="#" data-toggle="modal" data-target="#rating_modal">
                <span v-if="company?.review?.status == 'need_review'">أضف تقييمك</span>
                <span v-if="
                  company?.review?.status == 'reviewed' && company?.review?.feedback
                ">تعديل التقييم</span>
              </a>
            </div>
          </div>
        </div>
        <div class="p-20 rating-tab">
          <rating-item :reviews-count="company?.review?.vote" :count="parseInt(company?.review?.statistics?.reviews_count)" :rating="parseInt(company?.review?.statistics?.rating_avg)" />

          <div id="feedback_comments" ref="scrollContainer" class="theme-comments">
            <h2 class="mb-40 font-20">
              التقييمات ({{ company?.review?.statistics?.reviews_count }})
            </h2>
            <div v-if="feedbacksLoaded" class="comments-list">
              <template v-if="feedbacks.length">
                <comments-item v-for="(commentItem, index) in feedbacks" :key="index" :comment="commentItem" />
              </template>
              <template v-else>
                <div class="rec-placeholder text-center mh-auto py-50 mt-50 mb-50">
                  <div class="rec-placeholder__icon small">
                    <i class="sicon-chat-bubbles icon"></i>
                  </div>
                  <h2 class="rec-placeholder__title">لا توجد تعليقات</h2>
                  <p class="rec-placeholder__desc">لم يتم التقييم حتى الآن</p>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <rating-modal v-if="company?.review?.status != 'not_review'" :company="company" />
    </div>
    <!-- Load more -->
    <div v-if="hasNextFeedbackPage" ref="sentinel" class="align-center">
      <span class="loader loader--small mt-10"></span>
    </div>
  </div>
</template>

<script>
import api from '../config/axios.config'
export default {
  data() {
    return {
      loaded: false,
      company: {},
      feedbacksLoaded: false,
      feedbacks: [],
      feedbacksPage: 0,
      hasNextFeedbackPage: true,
      observer: null
    }
  },
  mounted() {
    this.getCompany();
    this.getCompanyFeedbacks();

    this.observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && this.hasNextFeedbackPage) {
        this.getCompanyFeedbacks()
      }
    }, {
      root: this.$refs.scrollContainer,
      threshold: 0.1 // Fire when at least 10% is visible
    })

    this.$nextTick(() => {
      this.observer.observe(this.$refs.sentinel)
    })

  },
  methods: {
    getCompany() {
      api
        .get(`company/${this.$route.params.companyId}?service_type=${this.$route.query.service_type || 'normal'}`)
        .then((res) => {
          this.company = res.data.data
          this.loaded = true
        })
        .catch(() => {
          //
        })
    },
    getCompanyFeedbacks() {
      api
        .get(`company/${this.$route.params.companyId}/rating/feedbacks?per_page=10&page=${++this.feedbacksPage}&?service_type=${this.$route.query.service_type || 'normal'}`)
        .then((res) => {
          this.hasNextFeedbackPage = res.data.pagination.links && res.data.pagination.links.next
          this.feedbacks.push(...res.data.data)
          this.feedbacksLoaded = true
        })
        .catch(() => {
          //
        })
    },
  },
}
</script>

<style scoped>
.comments-list-item:last-child {
  border-bottom: 0 !important;
  padding-bottom: 0 !important;
}
</style>
