<template>
  <modal
      id="new_packaging_order_modal"
      modal-title="طلب مواد تغليف"
      :show-save="true"
      :disabled="false"
      save-btn="إرسال الطلب"
      :show-rest="false"
      @saveModal="handleCreateNewPackagingOrder"
      @closeModal="resetModalData"
  >
      <div v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['quantity_limit']" class="alert-box alert-box--warning">
          <i class="sicon-info"></i>
          <article>
              <h6 class="mb-5">تجاوز الكمية المسموحة</h6>
              <p>عزيزي التاجر لقد تجاوزت الكمية المسموحة لمواد التغليف وهي متوسط عدد الشحنات لمتجرك لمدة اسبوعين. يمكنك تعديل الكمية لإرسال الطلب.</p>
          </article>
      </div>

      <div :class="{'form-group': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['company_id']}">
          <label>
              شركة الشحن
              <span class="text-danger">*</span>
          </label>
          <div class="input-group">
              <div class="select-wrapper">
                  <v-treeselect
                      id="company_id"
                      v-model="selectedShippingCompany"
                      name="company_id"
                      :options="packagingShippingCompanies"
                      class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full vue-treeselect--enhanced-tags"
                      placeholder="اختر شركة الشحن"
                      :normalizer="companyNormalizer"
                      @select="handleSelectCompany"
                  >
                      <div slot="value-label" slot-scope="{ node }" class="rec-list align-items-center">
                          <img :src="node.raw.logo" style="width: 25px; height: 25px;" />
                          <span class="ml-10">{{ node.label }}</span>
                      </div>
                      <label slot="option-label" slot-scope="{ node, labelClassName}" :class="labelClassName">
                          <div class="rec-list align-items-center">
                              <img :src="node.raw.logo" style="width: 25px; height: 25px;" />
                              <span class="ml-10">{{ node.label }}</span>
                          </div>
                      </label>
                  </v-treeselect>
              </div>
          </div>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['company_id']">{{ createPackagingOrderErrors['company_id'][0] }}</FormError>
      </div>
      <div class="pb-20">
          <div class="mb-20">
              <label>
                  مواد التغليف
                  <span class="text-danger">*</span>
              </label>
              <span class="d-block text-muted text-muted-smaller pb-10">اختر مواد التغليف المناسبة مع تحديد الكميات المطلوبة</span>
              <div class="col-lg-8 col-sm-12 p-0">
                  <div :class="{'form-group': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['materials']}">
                      <div class="input-group">
                          <div class="select-wrapper">
                              <v-treeselect
                                  id="materials_List"
                                  v-model="selectedMaterialValue"
                                  name="materials_List"
                                  :options="packagingMaterialsList"
                                  class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full vue-treeselect--enhanced-tags"
                                  placeholder="أختر نوع مواد التغليف المطلوبة"
                                  :normalizer="materialsNormalizer"
                                  @select="handleSelectMaterial"
                              />
                          </div>
                      </div>
                      <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['materials']">{{ createPackagingOrderErrors['materials'][0] }}</FormError>
                  </div>
              </div>
              <div class="col-lg-2 col-sm-6 col-xs-6">
                  <input id="quantity" v-model="materialQuantity" type="number" name="quantity" class="form-control full-bordered _parseArabicNumbers" placeholder="ادخل الكمية" min="1" />
              </div>
              <div class="col-lg-2 col-sm-6 col-xs-6">
                  <button class="btn btn-tiffany pl-30 pr-30 mr-10" @click="handleAddMaterial">
                      <i class="sicon-add"></i>
                      إضافة
                  </button>
              </div>
          </div>
      </div>
      
      <div class="clearfix"></div>
      
      <div class="pricing-summary justify-content-between pt-20">
          <div class="summary-header">ملخص التغليف</div>
          <div v-if="materialsSummary?.length > 0" class="wide rec-list rec-list--vertical justify-content-center align-items-center my-20 mx-10">
              <div v-for="(material, index) in materialsSummary" :key="index" class="wide rec-list justify-content-between align-items-center my-5">
                  <span class="text-muted">- {{ material.label }}</span>
                  <div class="rec-list justify-content-between align-items-center">
                      <div class="icon-container"><i class="sicon-arrow-left"></i></div>
                      <span class="text-muted mx-40">
                          {{ material.quantity }} قطعة
                      </span>
                      <button class="btn btn-danger" @click="removeMaterial(index)">
                          <i class="sicon-trash-2"></i>
                      </button>
                  </div>
              </div>
          </div>
          <div v-else class="wide rec-list justify-content-center align-items-center my-20">
              <span class="font-14 text-muted">لم يتم تحديد المواد المطلوبة حتى الان</span>
          </div>
      </div>

      <!-- branch -->
      <div v-if="(packagingBranchesList?.branches_list)?.length > 0" :class="{'form-group mt-20': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_name']}">
          <label>
              المستودع
              <span class="text-danger">*</span>
          </label>
          <span class="d-block text-muted text-muted-smaller pb-10">حدد المستودع لإستلام مواد التغليف</span>
          <div class="input-group">
              <div class="select-wrapper">
                  <v-treeselect
                      id="branches_list"
                      v-model="selectedBranchValue"
                      name="branches_list"
                      :options="packagingBranchesList.branches_list"
                      class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full vue-treeselect--enhanced-tags"
                      placeholder="اختر المستودع"
                      :normalizer="branchesNormalizer"
                      @select="handleSelectBranch"
                  />
              </div>
          </div>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_name']">{{ createPackagingOrderErrors['branch_name'][0] }}</FormError>
      </div>
      
      <!-- city -->
      <div v-show="(packagingBranchesList?.branches_list)?.length === 0 || selectedBranchObject" :class="{'form-group mt-20': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_city']}">
          <label>
              المدينة
              <span class="text-danger">*</span>
          </label>
          <div class="input-group">
              <div class="select-wrapper">
                  <v-treeselect
                      id="city"
                      v-model="selectedCityId"
                      name="city"
                      :options="packagingCountryCities"
                      class="vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full vue-treeselect--enhanced-tags"
                      placeholder="اختر المدينة"
                      :normalizer="cityNormalizer"
                      @select="handleSelectCity"
                  />
              </div>
          </div>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_city']">{{ createPackagingOrderErrors['branch_city'][0] }}</FormError>
          
      </div>
      
      <!-- address -->
      <div v-show="(packagingBranchesList?.branches_list)?.length === 0 || selectedBranchObject" :class="{'form-group mt-20': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_address']}">
          <label>
              العنوان
              <span class="text-danger">*</span>
          </label>
          <span class="d-block text-muted text-muted-smaller pb-10">سيتم شحن مواد التغليف للعنوان المدرج</span>
          <input id="address" v-model="address" type="text" name="address" class="form-control full-bordered" placeholder="ادخل العنوان" />
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_address']">{{ createPackagingOrderErrors['branch_address'][0] }}</FormError>
      </div>
      
      <!-- local -->
      <div v-show="(packagingBranchesList?.branches_list)?.length === 0 || selectedBranchObject" :class="{'form-group mt-20': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_local']}">
          <label>
              الحي
              <span class="text-danger">*</span>
          </label>
          <input id="local" v-model="local" type="text" name="local" class="form-control full-bordered" placeholder="ادخل اسم الحي"/>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_local']">{{ createPackagingOrderErrors['branch_local'][0] }}</FormError>
      </div>
      
      <!-- street -->
      <div v-show="(packagingBranchesList?.branches_list)?.length === 0 || selectedBranchObject" :class="{'form-group mt-20': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_street']}">
          <label>
              الشارع
              <span class="text-danger">*</span>
          </label>
          <input id="street" v-model="street" type="text" name="street" class="form-control full-bordered" placeholder="ادخل اسم الشارع"/>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_street']">{{ createPackagingOrderErrors['branch_street'][0] }}</FormError>
      </div>
      
      <!-- postal_code -->
      <div v-show="(packagingBranchesList?.branches_list)?.length === 0 || selectedBranchObject" :class="{'form-group mt-20': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_postal_code']}">
          <label>
              الرمز البريدي
          </label>
          <input id="postal_code" v-model="postal_code" type="text" name="postal_code" class="form-control full-bordered" placeholder="ادخل الرمز البريدي "/>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['branch_postal_code']">{{ createPackagingOrderErrors['branch_postal_code'][0] }}</FormError>
      </div>
      
      <div :class="{'form__field anime-item form__phone': true,'has-error': Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['phone_number']}">
          <label class="register__label" for="user-phone">رقم التواصل</label>
          <span class="text-danger">*</span>
          <span class="d-block text-muted text-muted-smaller pb-10">ادخل رقم التواصل لإستلام مواد التغليف</span>
          <vue-tel-input
              id="user-phone"
              v-model="phone"
              :input-options="inputOptions"
              required="true"
              :dropdown-options="dropdownOptions"
              :valid-characters-only="true"
              :auto-format="false"
              :default-country="'SA'"
              :dynamic-placeholder="true"
              :style="{direction:'ltr'}"
              mode="national"
              @input="onChangePhone"
          ></vue-tel-input>
          <FormError v-if="Object.keys(createPackagingOrderErrors).length > 0 && createPackagingOrderErrors['phone_number']">
              {{ createPackagingOrderErrors['phone_number'][0] }}
          </FormError>
      </div>
  </modal>
</template>
<script>
  import { mapState, mapActions } from "vuex";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import { VueTelInput } from "vue-tel-input";
  import "vue-tel-input/dist/vue-tel-input.css";

  export default {
      components: {
          "v-treeselect": Treeselect,
          "vue-tel-input": VueTelInput,
      },
      data() {
          return {
            isLegacy: window.sallaLegacy,
              companyNormalizer(node) {
                  return {
                      label: node.name,
                      value: node.id,
                      logo: node.logo,
                  };
              },
              cityNormalizer(node) {
                  return {
                      id: node.id,
                      label: node.name,
                      value: node.name,
                  };
              },
              selectedShippingCompany: null,
              selectedBranchValue: null,
              selectedBranchObject: null,
              selectedCityId: null,
              branchesNormalizer(node) {
                  return {
                      label: node.name,
                      value: node.id,
                  };
              },
              selectedMaterialValue: null,
              selectedMaterialObject: null,
              materialQuantity: 1,
              materialsSummary: [],
              materialsNormalizer(node) {
                  return {
                      label: node.label,
                      value: node.id,
                  };
              },
              dropdownOptions: {
                  showDialCodeInSelection: true,
                  showFlags: true,
                  showSearchBox: true,
              },
              inputOptions: {
                  placeholder: "أدخل رقم التواصل",
              },
              city: "",
              local: "",
              street: "",
              address: "",
              postal_code: "",
              phone: "",
          };
      },
      computed: {
          ...mapState({
              packagingShippingCompanies: (state) => state.packaging.packagingShippingCompanies,
              packagingBranchesList: (state) => state.packaging.packagingBranchesList,
              packagingMaterialsList: (state) => state.packaging.packagingMaterialsList,
              packagingCountryCities: (state) => state.packaging.packagingCountryCities,
              createPackagingOrderErrors: (state) => state.packaging.createPackagingOrderErrors,
          }),
      },
      mounted() {
          this.fetchCountryCities(1473353380); // SA country id
          this.fetchPackagingShippingCompanies();
          this.fetchPackagingBranchesList();
      },
      methods: {
          ...mapActions(["getPackagingShippingCompanies", "getPackagingBranchesList", "getPackagingMaterialsList", "createNewPackagingOrder", "getPackagingCountryCities"]),
          resetModalData() {
              this.selectedShippingCompany = null;
              this.selectedMaterialValue = null;
              this.selectedMaterialObject = null;
              this.selectedBranchValue = null;
              this.selectedBranchObject = null;
              this.materialQuantity = 1;
              this.materialsSummary = [];
              this.city = "";
              this.local = "";
              this.street = "";
              this.address = "";
              this.postal_code = "";
              this.phone = "";
              
              this.$store.commit("setCreatePackagingOrderErrors", {});
          },

          fetchPackagingShippingCompanies() {
              this.isLoading = true;
              this.getPackagingShippingCompanies();
          },
          fetchPackagingBranchesList() {
              this.isLoading = true;
              this.getPackagingBranchesList()
                  .then((res) => {
                      this.isLoading = false;
                    // set the initial address
                    if (res.branches_list.length === 0) {
                        this.address = res.address;
                        this.city = res.city;
                        this.phone = res.phone;
                        this.setSelectedCity();
                    }
                  })
                  .catch(() => {
                      this.isLoading = false;
                  });
          },

          fetchCountryCities(countryId) {
              this.isLoading = true;
              this.getPackagingCountryCities(countryId)
                  .then(() => {
                      this.isLoading = false;
                  })
                  .catch(() => {
                      this.isLoading = false;
                  });
          },
          fetchPackagingMaterialsList(companyId) {
              window.showLoading();
              this.getPackagingMaterialsList(companyId)
                  .then(() => {
                      window.hideLoading();
                  })
                  .catch(() => {
                      window.hideLoading();
                  });
          },
        handleSelectCompany(value) {
              // reset the selection
              this.selectedMaterialValue = null;
              this.selectedMaterialObject = null;
              this.selectedBranchValue = null;
              this.selectedBranchObject = null;
              this.materialQuantity = 1;
              this.materialsSummary = [];
              
              this.updateErrors("company_id");
              this.selectedShippingCompany = value.id;
              this.fetchPackagingMaterialsList(value.id);
          },
          handleSelectBranch(value) {
              this.updateErrors("branch_name");
              this.updateErrors("branch_address");
              this.updateErrors("branch_local");
              this.updateErrors("branch_street");
              this.updateErrors("branch_postal_code");
              this.updateErrors("phone_number");
              this.selectedBranchValue = value.id;
              this.selectedBranchObject = {...value};
              this.city = value.city;
              this.address = value.address;
              this.local = value.local;
              this.street = value.street;
              this.postal_code = value.postal_code;
              this.phone = value.phone;              
              this.setSelectedCity();
          },
          setSelectedCity() {
            this.selectedCityId = null;
            if (this.city && ("" + this.city).length) {
                const selectedCity = this.packagingCountryCities.find(city => city.name === this.city);

                selectedCity && (this.selectedCityId = selectedCity.id);
            }
          },
          handleSelectCity(selectedCity) {
              this.updateErrors("branch_city");
              this.city = selectedCity.name;
              this.selectedCityId = selectedCity.id;
          },
          handleSelectMaterial(value) {
              this.selectedMaterialValue = value.id;
              this.selectedMaterialObject = value;
          },
          handleAddMaterial() {
              if (this.selectedMaterialValue && this.materialQuantity > 0) {
                  this.updateErrors("materials");
                  this.updateErrors("quantity_limit");
                  this.materialsSummary.push({
                      ...this.selectedMaterialObject,
                      quantity: Number(this.materialQuantity),
                  });
                  this.selectedMaterialValue = null;
                  this.selectedMaterialObject = null;
                  this.materialQuantity = 1;
              }
          },
          removeMaterial(index) {
              this.materialsSummary.splice(index, 1);
          },
          validatePayload(payload) {
              const requiredFields = [
                  "company_id",
                  "materials",
                  "branch_city",
                  "branch_address",
                  "branch_local",
                  "branch_street",
                  "phone_number",
              ];
              
              const errors = {};

              for (const field of requiredFields) {
                  if (!payload[field] || payload[field] === null || payload[field].length < 1 || (typeof payload[field] === "string" && payload[field].trim() === "")) {
                      if (!errors[field]) {
                          errors[field] = [];
                      }
                      errors[field].push(`الحقل مطلوب.`);
                  }
              }
              return errors;
          },
          handleCreateNewPackagingOrder() {
              const errors = this.validatePayload({
                  company_id: this.selectedShippingCompany,
                  materials: this.materialsSummary,
                  branch_city: this.city,
                  branch_address: this.address,
                  branch_local: this.local,
                  branch_street: this.street,
                  branch_postal_code: this.postal_code,
                  phone_number: this.phone,
              });

              this.$store.commit("setCreatePackagingOrderErrors", errors);

              if (Object.keys(errors).length === 0) {
                  const payload =  {
                    company_id: this.selectedShippingCompany,
                    materials: this.materialsSummary,
                    phone_number: this.phone,
                    branch_city: this.city,
                    branch_name: `${this.selectedBranchObject ? (this.selectedBranchObject.name + ', ') : ''}${this.address}, ${this.local}, ${this.street} ${this.postal_code ? (', ' + this.postal_code) : ''}`
                  };
                  this.createNewPackagingOrder(payload).then((data) => {
                      if (data?.success) {
                          swal({
                              text: "تم ارسال طلب مواد التغليف بنجاح",
                              type: "success",
                              showConfirmButton: true,
                              showCancelButton: true,
                              confirmButtonText: "تفاصيل الطلب",
                              cancelButtonText: "إغلاق",
                          })
                              .then((result) => {
                                  if (result) {
                                      this.$emit("closeModal");
                                      const modalBackdrops = document.querySelectorAll(".modal-backdrop");
                                      modalBackdrops.forEach((backdrop) => backdrop.remove());

                                      document.body.classList.remove("modal-open");
                                      if (this.isLegacy) {
                                          this.$router.push({ name: "packaging-order-details", params: { orderId: data.data?.order_info?.id } });
                                      } else {
                                        window.location = `/shipping/packaging_orders/${data.data?.order_info?.id}`;
                                      }
                                  }
                              })
                              .catch(() => {
                                  this.$emit("closeModal");
                                  window.location.reload();
                              });
                      }
                  });
              }
          },
          updateErrors(id) {
              const errorsCopy = { ...this.$store.state.packaging.createPackagingOrderErrors };
              delete errorsCopy[id];
              this.$store.commit("setCreatePackagingOrderErrors", errorsCopy);
          },
          onChangePhone(number) {
              this.phone = number;
              this.updateErrors("phone_number");
          },
      },
  };
</script>
