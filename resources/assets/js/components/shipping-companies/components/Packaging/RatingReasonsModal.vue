<template>
  <modal id="rating_reasons_modal" modal-title="تحديد سبب التقييم" :show-save="true" save-btn="ارسال التقييم" :show-rest="false" @saveModal="handleSendRating">
    <span class="font-13">فضلًا حدِّد سبب التقييم من فضلك :</span>

    <ul class="rec-list rec-list--vertical mt-10">
      <li v-for="reason in reasons" :key="reason.value" class="my-10">
        <div class="rec-checkbox rec-checkbox--default light">
          <input :id="`type_${reason.value}`" v-model="selectedReason" type="radio" name="type" :value="reason.value" />
          <label :for="`type_${reason.value}`">
            <span>{{ reason.label }}</span>
          </label>
        </div>
      </li>
    </ul>

    <div class="form-group mt-20">
      <label class="font-13">ملاحظات على الطلب</label>
      <textarea v-model="noteText" name="note" rows="3" class="form-control" placeholder="اكتب أي ملاحظات تود مشاركتها..."></textarea>
    </div>
  </modal>
</template>

<script>
  import { mapState, mapActions } from "vuex";
  import { showSwAlert } from "../../utils/showSwAlert";

  export default {
    props: {
      orderId: {
        type: String,
        required: true,
      },
      ratingStars: {
        type: Number,
        required: true,
      },
    },
    data() {
      return {
        noteText: "",
        selectedReason: "late_reply",
        reasons: [
          { label: "تأخير في الرد", value: "late_reply" },
          { label: "الرد غير مناسب", value: "inappropriate_response" },
          { label: "لم يتم الطلب بنجاح", value: "unsuccessful_order" },
          { label: "تجربة سيئة مع شركة الشحن", value: "bad_experience" },
          { label: "أخرى", value: "others" },
        ],
      };
    },
    computed: {
      ...mapState({
        packagingOrderDetails: (state) => state.packaging.packagingOrderDetails,
      }),
    },
    methods: {
      ...mapActions(["ratePackagingOrderExperience"]),

      handleSendRating() {
        const payload = {
          intercom_user_id: this.packagingOrderDetails?.intercom_user_id,
          rating: this.ratingStars,
          reason: this.selectedReason,
          order_note: this.noteText
        };
        window.showLoading();

        this.ratePackagingOrderExperience({ orderId: this.orderId, payload }).then((res) => {
          window.hideLoading();
          if (res.success) {
            showSwAlert("success", "تم التقييم بنجاح", undefined, false, 3000);
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else {
            showSwAlert("error", "حدث خطأ , برجاء المحاوله في وقت لاحق", undefined, false, 3000);
          }
        });
      },
    },
  };
</script>
