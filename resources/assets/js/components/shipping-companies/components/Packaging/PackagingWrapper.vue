<template>
  <div class="panel panel-default">
    <div class="panel-body p-0">
      <ul class="rec-list rec-list--vertical rec-list--bordered light-border">
        <li class="p-20">
          <a href="#" class="card card--horizontal card--clickable" @click.prevent="navigateTo('/shipping/packing')">
            <div class="card__header">
              <i class="sicon-packed-box text-dark-100 font-25 mr-15"></i>
            </div>
            <div class="card__content">
              <h6 class="font-15 lh-1 m-0">
                مواد التغليف
                <small class="badge badge--primary ml-5"> جديد </small>
              </h6>
              <span class="text-muted text-muted-small">
                اطلب مواد تغليف لشحنات متجرك من شركات الشحن بكل سهولة.
              </span>
            </div>
        </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        isLegacy: window.sallaLegacy
      };
    },
    computed: {},
    methods: {
        navigateTo(path) {
          if (this.isLegacy) {
            this.$router.push("/shipping/packaging_orders");
          } else {
            window.parent?.postMessage({ event: "navigateTo", path: path }, "*");
          }
        }
    },
  };
</script>
