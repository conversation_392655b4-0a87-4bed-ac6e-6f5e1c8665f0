<template>
  <Modal
    id="company_details_modal"
    :modal-title="company?.name"
    :show-save="checkPermissions"
    :show-rest="activeTab === 'shipping-zones-tab' && company?.has_account"
    @saveModal="saveModal"
    @closeModal="resetActiveTab"
    @restModal="resetModal"
  >
    <CompanyHeaderModal />

    <Tabs-item :tabs="computedTabs" :active-tab="activeTab" :set-active-tab="setActiveTab">
      <template v-if="activeTab === 'integration-data-tab'" #integration-data-tab>
        <IntegrationDataTab />
      </template>
      <template v-if="activeTab === 'shipping-zones-tab'" #shipping-zones-tab>
        <ShippingZonesTab />
      </template>
      <template v-if="activeTab === 'supported-pickup-cities-tab'" #supported-pickup-cities-tab>
        <SupportedPickupCities />
      </template>
      <template v-if="activeTab === 'additional-info-tab'" #additional-info-tab>
        <AdditionalInfoTab />
      </template>
    </Tabs-item>
  </Modal>
</template>

<script>
  import { mapActions, mapState } from "vuex";
  import { validatePayload, validateCountries } from "../utils/payloadValidation";
  import { getCompanyPayload } from "../utils/companyPayloads";
  import { showSwAlert } from "../utils/showSwAlert";

  export default {
    data() {
      return {
        activeTab: "integration-data-tab",
        tabs: [
          {
            id: "integration-data-tab",
            title: `${this.$t("shipping.integration.integration_tab")}`,
            icon: "sicon-file-archive",
            onClick: () => this.setActiveTab("integration-data-tab"),
          },
          {
            id: "shipping-zones-tab",
            title: `${this.$t("shipping.integration.pricing_tab")}`,
            icon: "sicon-banknote-dollar",
            onClick: () => this.setActiveTab("shipping-zones-tab"),
          },
          {
            id: "supported-pickup-cities-tab",
            title: `${this.$t("shipping.integration.supported_pickup_cities_tab")}`,
            icon: "sicon-briefcase",
            onClick: () => this.setActiveTab("supported-pickup-cities-tab"),
          },
          {
            id: "additional-info-tab",
            title: `${this.$t("shipping.integration.additional_info_tab")}`,
            icon: "sicon-file-archive",
            onClick: () => this.setActiveTab("additional-info-tab"),
          },
        ],
        companiesHasDefaultZone: ["aramex"],
        companiesSupportSave: ["saudi_post", "redbox"],
      };
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        integrationData: (state) => state.companyDetails.integrationData,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
        errors: (state) => state.companyDetails.errors,
      }),
      computedTabs() {
        const filteredTabs = this.tabs.filter((tab) => {
          if (tab.id === "supported-pickup-cities-tab") {
            return this.company?.has_pickup_zones;
          }

          if (tab.id === "shipping-zones-tab") {
            return this.company?.has_account || (!this.company?.has_account && this.hasDefaultZone);
          }

          return true;
        });

        return filteredTabs.map((tab) => ({
          ...tab,
          isActive: tab.id === this.activeTab,
        }));
      },
      checkPermissions() {
        const permissionsObj = window.initialData.permissions;
        const hasPermissions = (permissionsObj.add_option || permissionsObj.edit_option) && (!this.company?.support_one_click_activation
              || ( this.company?.support_one_click_activation && this.companiesSupportSave.includes(this.company?.slug) ));
        return hasPermissions;
      },
      hasDefaultZone() {
        return this.companiesHasDefaultZone.includes(this.company?.slug);
      },
    },
    mounted() {
      const companyDetailsModal = $('#company_details_modal');
      companyDetailsModal.on('hidden.bs.modal', () => {
        this.resetActiveTab();
      });
    },
    methods: {
      ...mapActions(["saveCompanyDetails", "updateCompanyDetails", "resetShippingCompanyZoneDetails", "disconnectCompanyConnection", "saveShippingCompanyZoneDetails"]),
      setActiveTab(tabId) {
        this.activeTab = tabId;
      },
      resetActiveTab() {
        this.activeTab = "integration-data-tab";
        this.$store.commit("setCountriesList", []);
        this.$store.commit("setShippingCompanyZones", []);
        this.$store.commit("setCountriesList", []);
        this.$store.commit("setCitiesList", []);
        this.$store.commit("setExcludedCitiesList", []);
      },
      
      async handleOperation(isUpdate) {
        let payload = null;
        if (this.company.has_salla_polices_account || (this.integrationData && this.integrationData?.link_options === `salla_${this.company?.slug}`)) {
          // if change link_options then send policies payload
          payload = getCompanyPayload(`${this.company?.slug}_policies`);
        } else {
          payload = getCompanyPayload(this.company?.slug);
        }

        if (this.integrationData) {
          for (const key in payload) {
            payload[key] = this.integrationData[key];
          }
        }

        const errors = validatePayload(this.company?.slug, payload);
        await this.$store.commit("setErrors", errors);

        if (Object.keys(errors).length === 0) {
          const operationFunction = isUpdate ? this.updateCompanyDetails : this.saveCompanyDetails;

          operationFunction(payload);
        }
      },
        getAvailableDuration(translations) {
        const storeIsoCode = window.dashboard_settings.languages.current.iso_code
        if (storeIsoCode && translations[storeIsoCode] && translations[storeIsoCode].duration && translations[storeIsoCode].duration.trim() !== '') {
            return translations[storeIsoCode].duration;
        }    

        for (let lang in translations) {
          if (translations[lang].duration && translations[lang].duration.trim() !== '') {
            return translations[lang].duration;
          }
        }
        return ''; 
      },

      transformData(originalData) {
        const transformedData = originalData.map((country) => {
          return {
            id: country.id.toString(),
            zones: country.zones.map((zone) => {
              const translations = Object.entries(zone.translation).reduce((acc, [lang, values]) => {
                acc[lang] = { duration: values.duration };
                return acc;
              }, {});
              return {
                id: zone.id.toString(),
                service_type: zone.service_type || 'shipping',
                status: zone.status || "1",
                city: zone.city.map((city) => city.id.toString()) || [],
                city_excluded: zone.cities_excluded.map((city) => city.id.toString()) || [],
                cost: zone.fees.amount || "0",
                company_cost: zone.fees.company_cost || "0",
                type: zone.fees.type || "",
                charge_cost: zone.fees.amount_per_unit || ((! this.company.support_international && this.company.slug == 'aymakan' ) ? "3" : "2"),
                to_weight: zone.fees.up_to_weight || ((! this.company.support_international && this.company.slug == 'aymakan' )  ? "5" : "15"),
                per_weight: zone.fees.per_unit || "1",
                duration: this.getAvailableDuration(translations),
                ...translations,
                cod_enable: zone.cash_on_delivery.status ? "1" : "0",
                cod_cost: zone.cash_on_delivery.fees || "0",
                coverage_distance: zone.coverage_distance.value > 0 ? zone.coverage_distance.value : 1,
              };
            }),
          };
        });

        return transformedData;
      },
      addedNewZone(zonesData) {
        this.newAddedZones.forEach((zoneObject) => {
          let matchingZone = zonesData.find((zone) => zone.id == zoneObject.id);
          if (matchingZone) {
            matchingZone.zones.push(zoneObject.zones[0]);
          } else {
            zonesData.push(zoneObject);
          }
        });
        return zonesData;
      },

      async saveZoneValue() {
        let zonesData = this.zones;
        const zoneErrors = validateCountries(this.zones, true);
        let newZonesErrors = {};
        if (this.newAddedZones.length) {
          newZonesErrors = validateCountries(this.newAddedZones);
        }
        
        const errors = { ...zoneErrors, ...newZonesErrors };
        await this.$store.commit("setZoneErrors", errors);

        if (Object.keys(errors).length === 0) {
          zonesData = this.addedNewZone(zonesData);
          let transformedData = this.transformData(zonesData);
          const transformedPayload = { countries: transformedData };
          await this.saveShippingCompanyZoneDetails({
            companyId: this.company.id,
            payload: transformedPayload,
          });
        }
      },

      async saveModal() {
        if (this.activeTab === "shipping-zones-tab") {
          this.saveZoneValue();
        } else {
          if (!this.company.has_account) {
            await this.handleOperation(false);
          } else {
            await this.handleOperation(true);
          }
        }
      },
      async resetModal() {
        swal({
          title: "تنبيه ",
          text: "سيتم اعادة التسعيرة الافتراضية لشركة الشحن ، هل أنت متأكد من هذا الإجراء؟",
          type: "error",
          showConfirmButton: true,
          showCancelButton: true,
          confirmButtonText: "تأكيد",
          cancelButtonText: "إلغاء",
        }).then(async () => {
          try {
            await this.resetShippingCompanyZoneDetails(this.company.id).then(() => {
              showSwAlert("success", "تم اعادة التعيين بنجاح", undefined, false, 3000);
            });
          } catch (error) {
            showSwAlert("error", "حدث خطأ غير متوقع, الرجاء المحاولة لاحقًا.", undefined, false, 3000);
          }
        });
      },
    },
  };
</script>
