<template>
  <div class="border-b rating-comment">
    <div class="rating-comment__wrapper">
      <img class="review-avatar rounded bg-center" :src="comment.avatar" :alt="comment.name">
      <div class="rating-comment__content">
        <div class="wide mb-20">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="text-lg font-medium m-0">{{comment.name}}</h6>
            <shipping-rating :rating="parseFloat(comment.rating)" :reviews="null" :flat="true" />
          </div>
          <div class="mt-5">
            <span class="block text-start leading-5 text-gray-300 font-light text-xs">{{comment.date}}</span>
            <small v-if="!comment.is_published" class="label label-striped label-rounded">غير منشور</small>
          </div>
          <div class="d-flex justify-content-between align-items-center">
            <p v-if="comment.comment" class="font-13 mt-10 mb-0 review-text leading-5">{{comment.comment}}</p>
            <p v-else class="text-muted text-muted-small mt-10 mb-0 review-text leading-5 pr-20">{{comment.name}} قيّموا التاجر بـ {{ comment.rating }} من 5</p>
          </div>
        </div>
      </div>
    </div> 
    <div class="rounded-xl mb-4 bg-primary/10"></div>
  </div>
</template>

<script>
export default {
  props: {
    comment:{
      type: Object,
      required: true
    }
  },
}
</script>