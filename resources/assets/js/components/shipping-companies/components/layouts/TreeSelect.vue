<template>
  <div :class="{'form-group': true,'has-error': shouldShowError}">
    <label>
      {{ label }}
      <span v-if="required" class="text-danger">*</span>
    </label>
    <span v-if="subTitle" class="d-block text-muted text-muted-smaller pb-10">{{ subTitle }}</span>
    <div class="input-group">
      <span v-if="icon" class="input-group-addon"><i :class="icon"></i></span>
      <div class="select-wrapper">
        <v-treeselect
          :id="selectId"
          :name="name"
          :value="value"
          :options="options"
          :multiple="multiple"
          :searchable="searchable"
          :class="{'vue-treeselect--custom vue-treeselect--without-effect vue-treeselect--full vue-treeselect--enhanced-tags vue-treeselect--overflow-x': true , ' vue-treeselect--with-icon': icon ? true : false}"
          :placeholder="placeholder"
          :normalizer="normalizer ? normalizer : undefined"
          :async="async"
          :auto-load-root-options="false"
          :load-options="loadOptions"
          v-bind="$attrs"
          @input="handleSelect"
        />
      </div>
    </div>
    <FormError v-if="shouldShowError">{{ isTicketSystem ? creatTicketErrors[selectId][0] : (isZone ? zoneErrors[name][0] : errors[selectId][0]) }}</FormError>
  </div>
</template>

<script>
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import { mapState } from "vuex";

  export default {
    components: {
      "v-treeselect": Treeselect,
    },
    props: {
      label: {
        type: String,
        default: "",
      },
      subTitle: {
        type: String,
        default: null,
      },
      selectId: {
        type: String,
        default: '',
      },
      name: {
        type: String,
        default: "",
      },
      icon: {
        type: String,
        default: null,
      },
      value: [String, Array, Object],
      options: {
        type: Array,
        default: [],
      },
      multiple: Boolean,
      searchable: Boolean,
      placeholder: {
        type: String,
        default: "",
      },
      required: Boolean,
      isZone: {
        type: Boolean,
        default: false,
      },
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
      async: {
        type: Boolean,
        default: false,
      },
      loadOptions: {
        type: Function,
        default: null,
      },
      onChange: Function,
      normalizer: {
        type: Function,
        default: null,
      },
      isTicketSystem: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        integrationData: (state) => state.companyDetails.integrationData,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
        errors: (state) => state.companyDetails.errors,
        zoneErrors: (state) => state.shippingCompanyZones.zoneErrors,

        creatTicketErrors: (state) => state.ticketSystem.creatTicketErrors,
      }),
      shouldShowError() {
        if (this.isTicketSystem) {
          return Object.keys(this.creatTicketErrors).length > 0 && this.creatTicketErrors[this.selectId];
        } else {
          const errorObj = this.isZone ? this.zoneErrors : this.errors;
          const key = this.isZone ? this.name : this.selectId;
          return Object.keys(errorObj).length > 0 && errorObj[key];
        }
      },
    },
    methods: {
      handleSelect(value) {
        this.$emit("input", value);
        if (this.onChange) {
          this.onChange(value);
        }
        if (this.isTicketSystem) {
          this.updateTicketSystemErrors();
        } else {
          if (this.isZone) {
            if (this.isNewAddedZone) {
              this.addNewZoneValue(value);
            } else if (this.selectId !== "country") {
              this.updateZoneValue(value);
            }
          } else {
            this.updateIntegrationDataValue(value);
          }
        }
      },
      addNewZoneValue(value) {
        if (this.selectId === "country") {
          const newZonesCopy = JSON.parse(JSON.stringify(this.newAddedZones));
          const newZoneDetailToUpdate = {
            ...newZonesCopy[this.zoneDetailsIndex],
            id: value.id,
            name: value.name,
            zones: newZonesCopy[this.zoneDetailsIndex].zones.map((zone) => ({
              ...zone,
              city: [],
              cities_excluded: [],
            })),
          };
          newZonesCopy[this.zoneDetailsIndex] = newZoneDetailToUpdate;
          this.$store.commit("setNewAddedZones", newZonesCopy);
        } else {
          const newZonesCopy = JSON.parse(JSON.stringify(this.newAddedZones));
          const zoneToUpdate = newZonesCopy[this.zoneDetailsIndex];
          const newZoneDetailsCopy = [...zoneToUpdate.zones];
          const newZoneDetailToUpdate = this.updateNestedProperty(newZoneDetailsCopy[0], this.selectId, value);
          newZoneDetailsCopy[0] = newZoneDetailToUpdate;
          zoneToUpdate.zones = newZoneDetailsCopy;
          newZonesCopy[this.zoneDetailsIndex] = zoneToUpdate;
          this.$store.commit("setNewAddedZones", newZonesCopy);
        }
      },
      updateNestedProperty(object, path, value) {
        const keys = path.split(".");
        let prop = object;
        for (let i = 0; i < keys.length - 1; i++) {
          prop = prop[keys[i]];
        }
        prop[keys[keys.length - 1]] = value;
        return object;
      },
      updateZoneValue(value) {
        const zonesCopy = [...this.zones];
        const zoneToUpdate = zonesCopy[this.countryIndex];
        const zoneDetailsCopy = [...zoneToUpdate.zones];
        const zoneDetailToUpdate = this.updateNestedProperty(zoneDetailsCopy[this.zoneDetailsIndex], this.selectId, value);

        zoneDetailsCopy[this.zoneDetailsIndex] = zoneDetailToUpdate;
        zoneToUpdate.zones = zoneDetailsCopy;
        zonesCopy[this.countryIndex] = zoneToUpdate;

        this.$store.commit("setShippingCompanyZones", zonesCopy);
        this.updateZonesErrors();
      },

      updateIntegrationDataValue(value) {
        const companyCopy = { ...this.company };
        companyCopy.integration_data = {
          ...companyCopy.integration_data,
          [this.selectId]: value,
        };
        this.$store.commit("setCompany", companyCopy);

        const integrationDataCopy = { ...this.integrationData };
        integrationDataCopy[this.selectId] = value;
        this.$store.commit("setIntegrationData", integrationDataCopy);

        this.updateErrors();
      },
      updateErrors() {
        const errorsCopy = { ...this.$store.state.companyDetails.errors };
        delete errorsCopy[this.selectId];
        this.$store.commit("setErrors", errorsCopy);
      },
      updateZonesErrors() {
        const errorsCopy = { ...this.$store.state.shippingCompanyZones.zoneErrors };
        delete errorsCopy[this.name];
        this.$store.commit("setZoneErrors", errorsCopy);
      },

      updateTicketSystemErrors() {
        const errorsCopy = { ...this.$store.state.ticketSystem.creatTicketErrors };
        delete errorsCopy[this.selectId];
        this.$store.commit("setCreatTicketErrors", errorsCopy);
      },
    },
  };
</script>
