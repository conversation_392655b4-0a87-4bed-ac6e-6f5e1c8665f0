<template>
  <div class="rating-block d-flex">
    <div
      class="rating-block__pref d-flex flex-column justify-content-center align-items-center"
    >
      <p class="mb-10">تقييم التجار</p>
      <div class="rating-stars__wrap mb-10 bg-warning-100 rounded p-5 py-10">
        <shipping-rating :rating="parseInt(rating)" :reviews="null" :flat="true" />
      </div>
      <p class="text-muted text-muted-small mb-0">
        <span v-if="count !== 1 && count !== 2">{{ count }}</span> {{ pluralizeRatings(count) }}  من التجار
      </p>
    </div>
    <div class="rating-block__details flex-grow-1">
      <div
        v-for="(item, index) in reviewsCount"
        :key="index"
        class="details-item d-flex align-items-center mb-20"
      >
        <shipping-rating :rating="parseInt(item.rating)" :reviews="null" :flat="true" />
        <div class="progress-bg flex-grow-1">
          <span
            class="progress-value"
            :style="`width: ${(item.count / count) * 100}%`"
          ></span>
        </div>
        <span class="lh-1">{{ item.count }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "RatingItem",
  props: {
    reviewsCount: {
    type: Array,
    default: () => []
    },
    count: {
      type: Number,
      default: 0
    },
    rating: {
      type: Number,
      default: 0
    }
  },
  methods: {

    pluralizeRatings(count) {
      if (count === 0) {
        return "تقييمات";
      } else if (count === 1) {
        return "تقييم واحد";
      } else if (count === 2) {
        return "تقييمين";
      } else if (count >= 3 && count <= 10) {
        return "تقييمات";
      } else {
        return "تقييم";
      }
    },
  },
};
</script>
