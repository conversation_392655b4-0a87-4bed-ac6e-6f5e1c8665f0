<template>
  <div :class="{'form-group': true,'has-error': shouldShowError}">
    <label>{{ label }}
      <span v-if="required" class="text-danger">*</span>
    </label>
    <span v-if="subTitle" class="d-block text-muted text-muted-smaller pb-10">{{ subTitle }}</span>
    <div class="input-group">
      <span class="input-group-addon"><i :class="icon"></i></span>
      <input 
        :id="id" 
        :type="isNumeric ? 'number' : 'text'" 
        :name="name" 
        :class="{'form-control': true, 'full-bordered ': withSymbol}" 
        :placeholder="placeholder" 
        v-bind="$attrs" 
        @input="handleInput" 
        @wheel.prevent
      />
      <span v-if="withSymbol" class="input-group-addon">{{symbol}}</span>
    </div>
    <span v-if="note" class="d-block text-muted text-muted-smaller pb-10 pt-5">{{$t('shipping.form.customer_shipping_cost_note')}}
      <a :href="buttonNoteUrl" target="_blank">اضغط هنا</a>
    </span>
    <FormError v-if="shouldShowError">{{ isZone ? zoneErrors[name][0] : errors[id][0] }}</FormError>
  </div>
</template>

<script>
  import { mapState } from "vuex";

  export default {
    props: {
      label: {
        type: String,
        default: ""
      },
      subTitle: {
        type: String,
        default: ""
      },
      note: {
        type: String,
        default: ""
      },
      icon: {
        type: String,
        default: ""
      },
      id: {
        type: String,
        default: ""
      },
      name: {
        type: String,
        default: ""
      },
      placeholder: {
        type: String,
        default: ""
      },
      isNumeric: {
        type: Boolean,
        default: false
      },
      withSymbol: Boolean,
      symbol: {
        type: String,
        default: ""
      },
      required: Boolean,
      isZone: {
        type: Boolean,
        default: false,
      },
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
      onChange: {
        type: Function,
        default: () => {},
      },
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        integrationData: (state) => state.companyDetails.integrationData,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
        errors: (state) => state.companyDetails.errors,
        zoneErrors: (state) => state.shippingCompanyZones.zoneErrors,
      }),

        
      shouldShowError() {
        const errorObj = this.isZone ? this.zoneErrors : this.errors;
        const key = this.isZone ? this.name : this.id;
        return Object.keys(errorObj).length > 0 && errorObj[key];
      },
      buttonNoteUrl() {
        return window.baseUrl + "/settings/component/taxes";
      },
    },
    methods: {
      handleInput(event) {
        let value = event.target.value;
        this.$emit("input", value);
        if (this.onChange) {
          this.onChange(value);
        }
        this.$emit("change", value);

        if(this.company?.slug === 'dhl' && this.id === 'company_name'){
          value = value.replace(/[^A-Za-z\s]/g, '');
        }

        if (this.isZone) {
          if(this.isNewAddedZone){
            this.addNewZoneValue(value);
          }
          else{
            this.updateZoneValue(value);
          }
        } else {
          this.updateIntegrationDataValue(value);
        }
      },
      addNewZoneValue(value) {
        const newZonesCopy = JSON.parse(JSON.stringify(this.newAddedZones));
        const zoneToUpdate = newZonesCopy[this.zoneDetailsIndex];
        const newZoneDetailsCopy = [...zoneToUpdate.zones];
        const newZoneDetailToUpdate = this.updateNestedProperty(newZoneDetailsCopy[0], this.id, value);
        newZoneDetailsCopy[0] = newZoneDetailToUpdate;
        zoneToUpdate.zones = newZoneDetailsCopy;
        newZonesCopy[this.zoneDetailsIndex] = zoneToUpdate;
        this.$store.commit("setNewAddedZones", newZonesCopy);
      },
      updateNestedProperty(object, path, value) {
        const keys = path.split(".");
        let prop = object;
        for (let i = 0; i < keys.length - 1; i++) {
          prop = prop[keys[i]];
        }
        prop[keys[keys.length - 1]] = value;
        return object;
      },
      updateZoneValue(value) {
        const zonesCopy = [...this.zones];
        const zoneToUpdate = zonesCopy[this.countryIndex];
        const zoneDetailsCopy = [...zoneToUpdate.zones];
        const zoneDetailToUpdate = this.updateNestedProperty(zoneDetailsCopy[this.zoneDetailsIndex], this.id, value);

        zoneDetailsCopy[this.zoneDetailsIndex] = zoneDetailToUpdate;
        zoneToUpdate.zones = zoneDetailsCopy;
        zonesCopy[this.countryIndex] = zoneToUpdate;

        this.$store.commit("setShippingCompanyZones", zonesCopy);
        this.updateZonesErrors();
      },

      updateIntegrationDataValue(value) {
        const companyCopy = { ...this.company };
        companyCopy.integration_data = {
          ...companyCopy.integration_data,
          [this.id]: value,
        };
        this.$store.commit("setCompany", companyCopy);

        const integrationDataCopy = { ...this.integrationData };
        integrationDataCopy[this.id] = value;
        this.$store.commit("setIntegrationData", integrationDataCopy);

        this.updateErrors();
      },
      updateErrors() {
        const errorsCopy = { ...this.$store.state.companyDetails.errors };
        delete errorsCopy[this.id];
        this.$store.commit("setErrors", errorsCopy);
      },
      updateZonesErrors() {
        const errorsCopy = { ...this.$store.state.shippingCompanyZones.zoneErrors };
        delete errorsCopy[this.name];
        this.$store.commit("setZoneErrors", errorsCopy);
      },
    },
  };
</script>
