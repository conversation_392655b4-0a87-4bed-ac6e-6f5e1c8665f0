<template>
  <div class="form-group">
    <div class="rec-checkbox rec-checkbox--primary">
      <input 
        :id="id" 
        type="checkbox" 
        :name="name" 
        value="value" 
        :checked="checked" 
        @change="handleCheckboxChange" 
      />
      <label class="font-15" :for="id">{{ label }}</label>
    </div>
  </div>
</template>

<script>
  import { mapState } from "vuex";

  export default {
    props: {
      label: {
        type: String,
        default: '',
      },
      id: {
        type: String,
        default: '',
      },
      name: {
        type: String,
        default: '',
      },
      value: {
        type: String,
        default: '',
      },
      checked: Boolean,
      isZone: {
        type: Boolean,
        default: false,
      },
      countryIndex: {
        type: Number,
        default: null,
      },
      zoneDetailsIndex: {
        type: Number,
        default: null,
      },
      isNewAddedZone: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
        integrationData: (state) => state.companyDetails.integrationData,
        zones: (state) => state.shippingCompanyZones.zones,
        newAddedZones: (state) => state.shippingCompanyZones.newAddedZones,
      }),
    },
    methods: {
      handleCheckboxChange(event) {
        const isChecked = event.target.checked;

        this.$emit("checkbox-change", isChecked);

        if (this.isZone) {
          if (this.isNewAddedZone) {
            this.addNewZoneValue(isChecked);
          } else {
            this.updateZoneValue(isChecked);
          }
        } else {
          this.updateIntegrationDataValue(isChecked);
        }
      },
      addNewZoneValue(isChecked) {
        const newZonesCopy = JSON.parse(JSON.stringify(this.newAddedZones));
        const zoneToUpdate = newZonesCopy[this.zoneDetailsIndex];
        const newZoneDetailsCopy = [...zoneToUpdate.zones];
        const newZoneDetailToUpdate = this.updateNestedProperty(newZoneDetailsCopy[0], this.name, isChecked);
        newZoneDetailsCopy[0] = newZoneDetailToUpdate;
        zoneToUpdate.zones = newZoneDetailsCopy;
        newZonesCopy[this.zoneDetailsIndex] = zoneToUpdate;
        this.$store.commit("setNewAddedZones", newZonesCopy);
      },
      updateNestedProperty(object, path, value) {
        const keys = path.split(".");
        let prop = object;
        for (let i = 0; i < keys.length - 1; i++) {
          prop = prop[keys[i]];
        }
        prop[keys[keys.length - 1]] = value;
        return object;
      },
      updateZoneValue(isChecked) {
        const zonesCopy = [...this.zones];
        const zoneToUpdate = zonesCopy[this.countryIndex];
        const zoneDetailsCopy = [...zoneToUpdate.zones];
        const zoneDetailToUpdate = this.updateNestedProperty(zoneDetailsCopy[this.zoneDetailsIndex], this.name, isChecked);

        zoneDetailsCopy[this.zoneDetailsIndex] = zoneDetailToUpdate;
        zoneToUpdate.zones = zoneDetailsCopy;
        zonesCopy[this.countryIndex] = zoneToUpdate;

        this.$store.commit("setShippingCompanyZones", zonesCopy);
      },

      updateIntegrationDataValue(isChecked) {
        const companyCopy = { ...this.company };
        companyCopy.integration_data = {
          ...companyCopy.integration_data,
          [this.id]: isChecked,
        };
        this.$store.commit("setCompany", companyCopy);

        const integrationDataCopy = { ...this.integrationData };
        integrationDataCopy[this.id] = isChecked;
        this.$store.commit("setIntegrationData", integrationDataCopy);
      },
    },
  };
</script>
