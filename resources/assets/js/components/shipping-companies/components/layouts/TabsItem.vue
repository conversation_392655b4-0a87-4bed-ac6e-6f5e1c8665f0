<template>
  <div class="d-flex flex-column">
    <ul class="nav nav-tabs nav-tabs-solid nav-tabs-component nav-justified">
      <li v-for="tab in tabs" :key="tab.id" :class="{ 'active': tab.isActive, 'disabled': tab.disabled }">
        <a @click="setActiveTab(tab.id)">
          <i :class="[tab.icon, 'position-left']"></i>
          {{ tab.title }}
        </a>
      </li>
    </ul>
    <div class="tab-content">
      <div v-for="tab in tabs" :key="tab.id" :class="{ 'tab-pane': true, 'active': tab.isActive }">
        <slot :name="tab.id"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TabsItem",
  props: {
    tabs: {
      type: Array,
      required: true,
    },
    activeTab: {
      type: String,
      required: true,
    },
    setActiveTab: {
      type: Function,
      required: true,
    }
  },
  computed: {
  },
  methods: {
    
  },
  
};
</script>
