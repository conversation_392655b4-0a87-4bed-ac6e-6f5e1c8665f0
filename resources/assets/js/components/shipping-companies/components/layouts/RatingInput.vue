<template>
	<div class="rating">
		<ul class="list">
			<li
				v-for="star in maxStars"
        :key="star.stars"
				:class="{ active: star <= stars }"
				class="star"
        @click="rate(star)"
			>
				<i class="sicon-star2"></i>
			</li>
		</ul>
		<div v-if="hasCounter" class="info counter">
			<span class="score-rating">{{ stars }}</span>
			<span class="divider">/</span>
			<span class="score-max">{{ maxStars }}</span>
		</div>
	</div>
</template>
<script>
	export default {
		name: "RatingInput",
		props: {
			grade: {
        type: Number,
        default: 0
      },
      maxStars: {
        type: Number,
        default: 0
      },
      hasCounter: {
        type: Boolean,
        default: false
      }
    },
		data() {
			return {
				stars: this.grade,
			};
		},
    watch:{
      grade(val){
        this.stars = val;
      }
    },  
		methods: {
			rate(star) {
				if (typeof star === "number" && star <= this.maxStars && star >= 0) {
					this.stars = this.stars === star ? star - 1 : star;
          this.$emit('onRating', this.stars);
				}
			},
		},
	};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
	.rating {
		display: flex;
		flex-direction: column;
		align-items: center;
    color: #ddd;

		.list {
			padding: 0;

			&:hover {
				.star {
					color: #ffc62a;
				}
			}

			.star {
				display: inline-block;
				transition: all 0.2s ease-in-out;
				cursor: pointer;

        i{
          font-size: 25px;
        }

				&:hover {
					~ .star:not(.active) {
						color: inherit;
					}
				}

				&:first-child {
					margin-left: 0;
				}

				&.active {
					color: #ffc62a;
				}
			}
		}

		.info {
			margin-top: 15px;
			font-size: 40px;
			text-align: center;
			display: table;

			.divider {
				margin: 0 5px;
				font-size: 30px;
			}

			.score-max {
				font-size: 30px;
				vertical-align: sub;
			}
		}
	}
</style>
