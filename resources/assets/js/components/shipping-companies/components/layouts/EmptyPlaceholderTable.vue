<template>
  <div class="p-50 d-flex flex-column align-items-center mx-auto">
    <i :class="`${icon} font-70 text-muted`" style="color: #dddddd;"></i>
    <h2>{{ header }}</h2>
    <span>{{ description }}</span>
  </div>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      required: true
    },
    header: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    }
  }
};
</script>
