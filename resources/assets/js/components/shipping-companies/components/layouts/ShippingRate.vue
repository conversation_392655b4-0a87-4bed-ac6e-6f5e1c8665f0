<template>
  <span :class="{ 'ml-5': true, 'rec-star-rate': !showNumberOfReviewsAndName, 'rec-star-rate--detail mt-15': showNumberOfReviews }">
    <span v-if="showNumberOfReviewsAndName" class="text-default">{{ companyName }}</span>
    <span v-else>
      <i v-for="i in 5" :key="i" class="sicon-star2" :class="{ 'active': i <= rating }"></i>
    </span>
    <span v-if="showNumberOfReviews" class="font-14 mt-10">{{ numberOfReviews }} تقيم</span>

    <span v-else-if="showNumberOfReviewsAndName">
      <div :class="{ 'd-flex flex-wrap': true, 'mt-5': showNumberOfReviewsAndName }">
        <span class="sicon-star2 icon text-gold"></span>
        <span class="rating-value text-default mx-5">{{ rating.toFixed(1) }}</span>
        <span class="rating-count text-muted">
         ({{ numberOfReviews }} تقييمات)
        </span>
      </div>
    </span>
  </span>
</template>

<script>
export default {
  props: {
    showNumberOfReviews: Boolean,
    showNumberOfReviewsAndName: Boolean,
    companyName: {
      type: String,
      default: ''
    },
    rating: {
      type: Number,
      default: 0
    },
    numberOfReviews: {
      type: Number,
      default: 0
    }
  }
};
</script>
