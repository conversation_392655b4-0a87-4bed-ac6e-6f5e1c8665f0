<template>
    <div>
      <label>
          {{label}}
          <span v-if="required" class="text-danger">*</span>
        </label>
        <small class="text-muted text-muted-small d-block mb-0 d-block font-12">
          يسمح بصورة فقط من نوع jpg, jpeg, png*
        </small>
      <div>
        
          <file-pond
              :ref="pondRef"
              v-bind:image-edit-editor="imageEditEditor"
              label-idle='اسحب الصورة وأفلتها هنا <span class="filepond--label-action"> او تصفح من جهازك </span>'
              max-file-size="5000000B"
              label-max-file-size-exceeded="حجم الصورة كبير جداً"
              label-max-file-size="حجم الملف كبيرا الحد الاقصى هو {filesize}"
              label-file-type-not-allowed="نوع الملف غير صالح"
              file-validate-type-label-expected-types="يجب ان يكون {allButLastType} او {lastType}"
              :allow-image-resize="true"
              :allow-image-preview="true"
              max-files="1"
              :image-edit-instant-edit="! hasFile"
              :server="withServer"
              :files="files"
              :allow-image-edit="!isMobile"
              accepted-file-types="image/jpg, image/jpeg, image/png"
              @removefile="deleteImage"
          />
        
        </div>
    </div>
</template>

<script>
import $notify from '../../../utils/notify';
import Http from "../../../utils/http";
import {create} from '../../../entity/plugins/doka/vue/esm/lib/doka.esm.min';
import vueFilePond from "vue-filepond";
import "filepond/dist/filepond.min.css";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImageEdit from "filepond-plugin-image-edit";
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import "filepond-plugin-image-edit/dist/filepond-plugin-image-edit.css";
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginImageCrop from "filepond-plugin-image-crop";
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginFileEncode from 'filepond-plugin-file-encode';
import FilePondPluginFileMetadata from 'filepond-plugin-file-metadata';
import FilePondPluginImageFilter from 'filepond-plugin-image-filter';
import FilePondPluginFilePoster from 'filepond-plugin-file-poster';
import 'filepond-plugin-file-poster/dist/filepond-plugin-file-poster.css';
import FilePondPluginImageExifOrientation from 'filepond-plugin-image-exif-orientation';
import FilePondPluginFileRename from 'filepond-plugin-file-rename';
import FilePondPluginImageResize from "filepond-plugin-image-resize";
import FilePondPluginImageValidateSize from 'filepond-plugin-image-validate-size';
import FilePondPluginImageTransform from "filepond-plugin-image-transform";


const options = require('../../../utils/DokaOptions').default;


// Create component
const FilePond = vueFilePond(
    FilePondPluginImageExifOrientation,
    FilePondPluginImagePreview,
    FilePondPluginFileValidateType,
    FilePondPluginImageEdit,
    FilePondPluginImageCrop,
    FilePondPluginImageResize,
    FilePondPluginImageTransform,
    FilePondPluginFileRename,
    FilePondPluginImageFilter,
    FilePondPluginImageValidateSize,
    FilePondPluginFilePoster,
    FilePondPluginFileMetadata,
    FilePondPluginFileValidateSize,
    FilePondPluginFileEncode,
);

import {mapState} from 'vuex';

export default {
  components: {
    FilePond
  },
  props: {
    value: [Blob, String],
    width: {
      type: String,
      default: null
    },
    height: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: 'img-circle'
    },
    cropAspectRatio: {
      type: String,
      default: '1'
    },
    label: {
      type: String,
      default: null
    },
    uploader: {
      type: String,
      default: null
    },
    finishedUpload: {
      type: Boolean,
      default: false
    },
    imgSrc: {
      type: String,
      default: null
    },
    uploading: {
      type: Boolean,
      default: true
    },
    pondRef: {
      type: String,
      default: 'pond'
    },
    required: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      src: '',
      result: null,
      withImageEditInstantEdit: null,
      enabled: false,
      fileName: 'حمل الصورة',
      uploaded: false,
      doka: options,
      isMobile: window?.innerWidth <= 768,
      server: {
        process:(fieldName, file, metadata, load, error, progress, abort, transfer, options) => {
          this.result = file;
          const formData = new FormData();
          $.each(window.s3BrowserBasedUploads.fields, function(index, value) {
            formData.append(index, value);
          });
          const _uid = Math.random().toString(36).replace(/[^a-z]+/g, '').substr(2, 10);;

          formData.append('Content-Type', this.result.type);
          formData.append('file', this.result, _uid + '.' +  this.result.name.split('.').pop());

          window.showLoading();
          this.$root.$emit('image-uploading', this.result);
          let self = this;

          axios({
            method: 'POST',
            url: window.s3BrowserBasedUploads.endpoint_url,
            data: formData,
            config: {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              onUploadProgress: (event) => {
                progress(event.lengthComputable, event.loaded, event.total);
              }
            }
          }).then(function (response) {
            self.$emit('input', $(response.data).find('Location').text());
            self.createS3UploadToken();
            window.hideLoading();
            load(response);
          }).catch(({response}) => {
            let message = $(response.data).find('Message').text() || '';
            if (this.mappedMessage[message]) {
              message = this.mappedMessage[message]
            }
            $notify('فشل الرفع، الرجاء المحاولة مرة أخرى.', message);

            self.result = null;
            self.createS3UploadToken();
            self.setDefaultImage();
            window.hideLoading();
            error(message);
          }).then(() => {
            this.$root.$emit('image-uploaded');
          });
        },
        load: (source, load) => {
          // let's fetch the image from s3
          fetch(new Request(source)).then(function(response) {
            response.blob().then(function(myBlob) {
              load(myBlob)
            });
          });
        },
        revert: null
      },
      imageEditEditor: create({...{
          onloaderror: this.handleDokaLoadError,
          oncancel: this.handleDokaCancel
        },...options}),
      mappedMessage: {
        'Your proposed upload exceeds the maximum allowed size': 'حجم الصورة كبير جداٌ كحد أقصى 1MB يرجى تقليل الصورة',
        'Invalid according to Policy: Policy Condition failed: ["starts-with", "$Content-Type", "image/"]': 'يسمح بصورة فقط من نوع jpg,jpeg,png',
      }
    };
  },
  computed: {
    getImageSrc() {
      if (this.result !== null) {
        return this.toUrl(this.result);
      }
      if (this.value !== null) {
        return this.toUrl(this.value);
      }
      if(this.imgSrc != null) {
        return this.toUrl(this.imgSrc);
      }

      return this.src;
    },
    files() {
      if (! this.hasFile || ! this.getImageSrc) {
        //console.log('no file for' + this.pondRef);
        return []
      }

      return [{
        source: this.getImageSrc,
        options: {
          type: "local"
        }
      }];
    },
    hasFile() {
      return this.withImageEditInstantEdit || ! [undefined, null, ''].includes(this.getImageSrc);
    },
    withServer() {
      //! this.hasFile
      return this.server
    }
  },
  created() {
    this.setDefaultImage();
  },
  methods: {
    handleDokaCancel(){
      this.enabled = false;
      this.setDefaultImage();
    },
    handleDokaLoadError(image){
      if (image.status === 'IMAGE_MIN_SIZE_VALIDATION_ERROR' && image.hasOwnProperty('data') && image.data.hasOwnProperty('minImageSize')) {
        $notify(this.doka.labelStatusLoadImageError + ' الطول:' + image.data.minImageSize.height + ' العرض:' + image.data.minImageSize.width, '');
      } else {
        $notify(this.doka.labelStatusImageError + ' (' + image.status + ')', '');
      }
      this.enabled = false;
      this.setDefaultImage();
    },
    openInputFile(event) {
      $(event.target).parent().find('input[type="file"]').click();
    },
    inputFile(e) {
      this.src = this.toUrl(e.target.files[0]);
      this.fileName = e.target.files[0].name;
      this.uploaded = true;
      this.enabled = true;
    },
    toUrl(src) {
      return src instanceof Blob ? URL.createObjectURL(src) : src;
    },
    setDefaultImage() {
      this.src = '';
      this.fileName = 'حمل صورة الحملة...';
      this.uploaded = false;
    },
    createS3UploadToken() {
      return new Promise((resolve, reject) => {
        Http.get(window.s3BrowserBasedUploads.refresh_credentials_url, ({ data }) => {
          window.s3BrowserBasedUploads.fields = data.fields;
          resolve(data)
        }, error => reject(error))
      })
    },
    deleteImage () {
      this.withImageEditInstantEdit = false;
      this.src = '';
    },
  }
}
</script>