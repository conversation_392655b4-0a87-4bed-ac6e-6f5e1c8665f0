<template>
    <div :id="id" class="modal fade modal-backup" data-backdrop="static">
        <div class="modal-dialog" style="direction: rtl;">
            <div class="modal-content">
                <div class="modal-header bg-info">
                  <h6 class="modal-title">{{modalTitle}}</h6>
                  <button type="button" class="close" data-dismiss="modal" @click="$emit('closeModal')">×</button>
                </div>
                <div :class="withoutPadding ? 'modal-body no-padding' : 'modal-body'">
                    <slot></slot>
                </div>
                <div v-if="showSave || showClose" class="modal-footer no-icons">
                    <button v-if="showSave" type="button" :disabled="disabled" class="btn btn-info btn-save" @click="$emit('saveModal', id)">{{saveBtn}}</button>
                    <button v-if="showRest" class="btn btn-default btn-icon-prepend pull-right ml-10" @click="$emit('restModal')"><i class="sicon-sync"></i>{{$t('shipping.form.reset')}}</button>
                    <button v-if="showClose" class="btn btn-info btn-close" data-dismiss="modal" @click="$emit('closeModal')">{{closeBtn}}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        id: {
            type: String,
            default: '',
        },
        modalTitle: {
            type: String,
            default: '',
        },
        saveBtn: {
            type: String,
            default: 'حفظ',
        },
        closeBtn: {
            type: String,
            default: 'خروج',
        },
        disabled: {
            type: Boolean,
            default: false
        },
        withoutPadding: {
            type: Boolean,
            default: false
        },
        showSave: {
            type: [Function, Boolean],
            default: true,
        },
        showClose: {
          type: Boolean,
          default: true
        },
        showRest: {
            type: Boolean,
            default: false,
        },
    }
}
</script>