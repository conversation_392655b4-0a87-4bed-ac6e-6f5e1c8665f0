<template>
  <div>
    <div v-if="flat" class="d-flex">
      <span class="sicon-star2 icon text-gold"></span>
      <span class="rating-value text-default mx-5">{{ rating }}</span>
      <span v-if="reviews != null" class="rating-count text-muted">({{reviews}})</span>
    </div>

    <div v-else class="rec-star-rate rec-star-rate--detail mt-10">
      <i v-for="i in 5" :key="i" :class="{'sicon-star2 mr-5 ': true, 'font-25': bigIcon, 'active': i <= rating}"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    rating: {
      type: Number,
      default: 0,
    },
    reviews: {
      type: Number,
      default: 0,
    },
    flat: {
      type: Boolean,
      default: false,
    },
    bigIcon: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
};
</script>