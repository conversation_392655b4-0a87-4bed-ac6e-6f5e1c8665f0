<template>
    <div class="shipping-dropdown">
        <v-select dir="rtl" :disabled="disabled" :deselect-from-dropdown="deselectFromDropdown" :clear-search-on-select="true" :close-on-select="closeOnSelect" :loading="loading" :value="localValue" :label="label" :multiple="multiple" class="vue-treeselect--with-icon" :filterable="false" :options="localOptions" :placeholder="placeholder" :dropdown-should-open="({ noDrop, open, mutableLoading }) => noDrop ? false : open" @search="onSearch" @input="setSelected" @open="handleOpen">
            <template #no-options>
                <span v-if="!loading">لا يوجد نتائج</span>
                <span v-else>جاري جلب البيانات...</span>
            </template>

            <template #selected-option-container="{ option, deselect }">
                <div v-if="multiple" class="vue-treeselect__multi-value-item-container" @click="!disabled && deselect(option)">
                    <div class="vue-treeselect__multi-value-item">
                        <span class="vue-treeselect__multi-value-label"> {{ option[label] }} </span>
                        <span v-if="!disabled" class="vue-treeselect__icon vue-treeselect__value-remove">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 348.333 348.333">
                                <path d="M336.559 68.611L231.016 174.165l105.543 105.549c15.699 15.705 15.699 41.145 0 56.85-7.844 7.844-18.128 11.769-28.407 11.769-10.296 0-20.581-3.919-28.419-11.769L174.167 231.003 68.609 336.563c-7.843 7.844-18.128 11.769-28.416 11.769-10.285 0-20.563-3.919-28.413-11.769-15.699-15.698-15.699-41.139 0-56.85l105.54-105.549L11.774 68.611c-15.699-15.699-15.699-41.145 0-56.844 15.696-15.687 41.127-15.687 56.829 0l105.563 105.554L279.721 11.767c15.705-15.687 41.139-15.687 56.832 0 15.705 15.699 15.705 41.145.006 56.844z"></path>
                            </svg></span>
                    </div>
                </div>
            </template>
        </v-select>
    </div>
</template>

<script>
import _ from 'lodash';
import axios from 'axios';
import vSelect from 'vue-select';
export default {
    components: {
        'v-select': vSelect
    },
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        value: {
            type: [String, Object, Number, Array],
            default: null,
        },
        options: {
            type: Array,
            default: () => [],
        },
        label: {
            type: String,
            default: 'name',
        },
        placeholder: {
            type: String,
            default: 'Select an option...',
        },
        closeOnSelect: {
            type: Boolean,
            default: true,
        },
        deselectFromDropdown: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            localOptions: this.options,
            localValue: this.value,
            searchQuery: '',
            cancelTokenSource: null,
            loading: false,
        };
    },
    watch: {
        value: {
            handler(newVal, oldVal) {
                if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
                    this.localValue = newVal;
                }
            },
            deep: true,
        },
        localValue(newValue) {
            this.$emit('input', newValue);
        },
        options() {
            this.localOptions = this.options;
            this.loading = !true;
        },
        searchQuery: {
            handler: _.debounce(function (newQuery) {
                // Cancel previous request if it exists
                if (this.cancelTokenSource) {
                    this.cancelTokenSource.cancel('Request canceled due to new input.');
                }

                if (newQuery.length > 0 || this.localOptions.length === 0) {
                    this.fetchOptions(newQuery);
                } else {
                    this.loading = false;
                }
            }, 300),
            immediate: true,
        },
    },
    methods: {
        onSearch(searchQuery) {

            if (searchQuery.length > 0 || this.localOptions.length === 0) {
                this.loading = true;
            }

            this.searchQuery = searchQuery;
        },
        setSelected(newValue) {
            this.localValue = newValue;
        },
        fetchOptions(searchQuery = '') {
            if (this.disabled) {
                return;
            }
            this.cancelTokenSource = axios.CancelToken.source();
            this.$emit('search', { query: searchQuery, cancelToken: this.cancelTokenSource.token });
        },
        handleOpen() {
            if (this.localOptions.length === 0) {
                this.loading = true;
                this.fetchOptions();
            }
        },
    },

};
</script>

<style lang="scss">
@import 'vue-select/dist/vue-select.css';

.select-wrapper {
    height: 36px;
}

.shipping-dropdown {

    .vue-treeselect__multi-value-item {
        background: rgba(186, 243, 230, .25) !important;
        border-radius: 4px;
        color: #004d5a !important;
        margin-left: 0;
        padding: 2px 0;

        .vue-treeselect__value-remove {
            color: #004d5a !important;
        }

        .vue-treeselect__multi-value-item:hover .vue-treeselect__value-remove,
        .vue-treeselect__value-remove:hover {
            color: #e53935 !important;
        }

        svg {
            fill: currentColor;
        }
    }

    .v-select {
        display: table !important;
        table-layout: fixed !important;

        &::after {
            color: #999;
            content: "\e96e";
            font-family: sallaicons;
            font-size: 15px;
            left: 1px;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            height: 34px;
            width: 29px;
            text-align: center;
            padding-top: 6px;
            pointer-events: none;
            border-radius: 4px;
        }

        &.vs--disabled {
            cursor: var(--vs-disabled-cursor) !important;

            &::after {
                background-color: #f9f9f9 !important;
            }

            .vs__selected,
            .vue-treeselect__multi-value-item-container {
                padding-right: 5px;
            }

            .vs__selected-options,
            .vue-treeselect__multi-value-item-container,
            .vue-treeselect__multi-value-item {
                cursor: var(--vs-disabled-cursor) !important;
            }
        }
    }


    .vs__dropdown-option--highlight,
    .vs__dropdown-option--selected {
        background-color: rgba(186, 243, 230, 0.15) !important;
        color: #004d5a !important;
    }

    .vs__dropdown-option--selected:hover {
        background-color: rgba(186, 243, 230, 0.15) !important;
    }

    .vs__dropdown-menu .vs__dropdown-option:hover {
        background-color: rgba(186, 243, 230, .15) !important;
    }

    .vs__dropdown-toggle {
        appearance: none;
        padding: 0;
        background: none;
        border: 0px solid transparent !important;
        border-radius: 4px;
        white-space: normal;
        position: relative;
    }

    .v-select .vs__selected-options {
        direction: rtl;
        border-radius: 4px;
        border-right: none !important;
        border-top-right-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
        display: inline-block;
        white-space: nowrap;
        padding-left: 30px;
        padding-right: 0 !important;

        input {
            font-size: 12px;
            padding: 0px;
            display: inline-block;
            width: 120px;

            &::placeholder {
                color: #999 !important;
            }
            
            &[disabled="disabled"] {
                padding-right: 5px;
            }
        }

        &::after {
            display: none !important;
        }
    }

    .v-select.vs--multiple .vs__selected-options {
        overflow-x: scroll;
    }

    .v-select .vs__selected {
        padding-left: 0px !important;
        display: inline-block;
        margin-right: 0 !important;
        padding-right: 0;
        margin-top: 0px !important;
        box-sizing: border-box;
        top: 7px !important;
        right: 0 !important;
    }

    .vs__search,
    .vs__search:focus {
        appearance: none;
        background: none !important;
        border: 1px solid transparent !important;
        border-right: none !important;
        box-shadow: none !important;
        line-height: 1px !important;
        margin: 5px 0 0 !important;
        max-width: 100% !important;
        outline: none !important;
        padding: 0;
        z-index: 1 !important;
        direction: rtl !important;
        height: auto !important;
    }

    .v-select .vs__actions {
        display: inline-block !important;
        padding: 0 !important;
        margin: 0 !important;

        .vs__clear,
        .vs__open-indicator {
            display: none !important;
        }

        .vs__spinner {
            z-index: 2;
            position: absolute;
            left: 7px;
            top: 9px;
            background-color: #fff;
            font-size: 4px;
            border: 0.7em solid hsla(0, 0%, 39%, .1) !important;
            border-left-color: rgba(60, 60, 60, .45) !important;
            animation: vSelectSpinner 0.44s linear infinite;
            width: 4.5em !important;
            height: 4.5em !important;

            &:after {
                width: 4.5em !important;
                height: 4.5em !important;
            }
        }
    }

    .vs--disabled .vs__selected-options {
        background-color: #f9f9f9;
    }

    .v-select[open-direction="top"] .vs__dropdown-menu {
        top: auto;
        bottom: 100%;
        transform: translateY(0px);
        box-shadow: none;
        border-top-style: solid;
        border-bottom-style: none;
    }

    .v-select .vs__dropdown-menu {
        box-shadow: none !important;
        border-color: #eeeeee !important;
        max-height: 300px !important;
        padding: 0;
        top: calc(100% - 3px);
    }

    .vs__dropdown-menu .vs__dropdown-option {
        padding: 4px 10px !important;
        display: block;
        position: relative;
        padding: 4px 10px !important;
        min-height: 36px;
        font-size: 12px;
        line-height: 28px;
        margin: 0;
    }

}

.has-error .v-select .vs__selected-options {
    border-color: #d84315 !important;
}

.vs--single.vs--loading .vs__selected,
.vs--single.vs--open .vs__selected {
    top: 7px !important;
    right: 0 !important;
}

.vs--single.vs--searching .vs__selected {
    display: none !important;
}

#div_shipping_settings .form-group.has-error .vue-treeselect__control {
    border-color: #d84315 !important;

    &:hover {
        border-color: #d84315 !important;
    }
}
</style>
