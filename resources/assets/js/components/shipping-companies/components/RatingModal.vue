<template>
  <modal id="rating_modal" :modal-title="`تقييم شركة الشحن ( ${company?.name} )`" :show-save="true" save-btn="نشر التقييم" :show-rest="false" @saveModal="addReview">
    <div class="rec-list align-items-center mb-20">
      <div class="company-details-container mr-10">
        <img :src="company?.logo" class="company-details-logo" style="width: 52px; height: 52px;" />
      </div>
      <div>
        <span>{{company?.name}}</span>
        <shipping-rating :rating="company?.review?.statistics?.rating_avg" :reviews="company?.review?.statistics?.reviews_count" :flat="true" />
      </div>
    </div>
    <div class="rec-list justify-content-between align-items-center pt-10">
      <span class="font-15">تقييمك لشركة الشحن</span>
      <div class="rating-area theme-rating">
        <rating-input :grade="rating" :max-stars="5" :has-counter="false" @onRating="onRating"/>
        <!-- <small v-if="submitClicked && !rating" class="error-text d-block text-center mt-5 pl-20 absolute wide">تقييم النجوم مطلوب</small> -->
      </div>
    </div>
    <div class="seperate-line"></div>
    <div class="form-group">
      <h6 class="font-15">إضافة تعليق على خدمة شركة الشحن</h6>
      <div class="input-group">
        <span class="input-group-addon" style="vertical-align: top; padding-top: 12px;">
          <i class="sicon-content"></i>
        </span>
        <textarea 
        :value="comment" name="review" rows="3" class="form-control" placeholder="أضف تعليقك"
        @input="comment = $event.target.value" 
        >
      </textarea>
      </div>
    </div>
  </modal>
</template>

<script>
import { showSwAlertAction, showSwAlert } from "../utils/showSwAlert";
import api from "../config/axios.config";
  export default {
    props: {
      company: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
          rating: this.company ? (this.company.review?.feedback?.rating || 0) : 0,
          comment: this.company ? (this.company.review?.feedback?.comment || '') : '',
      };
    },
    computed: {},
    methods: {
      onRating (stars) { 
        this.rating = stars;
      },
        addReview() {
            const payload = {
                rating: this.rating,
                comment: this.comment
            };

            api.post(`company/${this.company.id}/rating`, payload)
                .then(() => {
                    const titleHtml = `<p>سوف يتم مراجعة التقييم ونشرة في صفحة 
                        <a class="text-underline" href="/shipping/company/${this.company.id}/rating">تقييمات شركة الشحن</a>
                    </p>`;
                    showSwAlertAction("success", " تم ارسال التقييم بنجاح", undefined, titleHtml, false);
                })
                .catch(e => {
                    let fields = e?.response?.data?.error?.fields;
                    let error;
                    if (fields) {
                        error = Object.values(fields);
                        error = error.length ? error[0][0] : null;
                    }
                    showSwAlert("error", "خطأ",error || `حدث خطأ, حاول مرة اخرى`);
                })
      },
    },
  };
</script>
