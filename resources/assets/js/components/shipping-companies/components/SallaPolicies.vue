<template>
  <div class="panel panel-default">
    <div class="panel-body no-padding">
      <div class="p-20 bg-gray-50">
        <div class="card card--horizontal">
          <div class="card__header mr-20">
            <img src="https://cdn.salla.sa/dRWP/3qyWTSA1lAivrucjsr9Tl5XiiFzhGIFCUmmj2cV9.png" class="d-block m-0 initial s-gateway s-gateway--medium"
                 alt="Salla">
          </div>
          <div class="card__content mr-20">
            <article class="rec-article">
              <h2 class="font-18 d-block text-primary mb-0 lh-1">بوليصات سلة</h2>
              <p v-if="!isSallaPoliciesActive" class="font-15 d-block m-0">فعل بوليصات سلة لتسهيل عملية الشحن وبأقل الرسوم</p>
              <p v-else class="font-15 d-block m-0">تسهيل عملية الشحن وبأقل الرسوم</p>
            </article>
          </div>
          <div v-if="isSallaPoliciesActive && isLegacy && canManageShipmentsHistory" class="card__footer">
            <a class="btn btn--outlined primary ml-20" :href="buttonUrl" target="_self">
              <i v-if="ShowArchiveIcon" class="sicon-file-archive mr-5"></i>
              {{ buttonText }}
            </a>
          </div>
        </div>
      </div>
      <companies-list v-if="isSallaPoliciesActive"/>

      <div v-else class="p-20">
        <div class="salla-policies-container">
          <div>
            <h3 class="font-18 font-bold mt-0 mb-10">تجربة شحن تسهِّل تجارتك</h3>
            <h2 class="font-16 font-normal mt-0 text-muted">ودِّع تعقيدات الشحن مع حلول لوجيستية متكاملة في بوليصات سلة:</h2>
            <ul class="rec-list rec-list--vertical rec-list--wide my-15">
              <li v-for="(advantage, index) in advantages" :key="index" class="py-5">
                - {{ advantage }}
              </li>
            </ul>
            <button v-if="!isSallaPoliciesActive && hasDefaultBranch" class="btn btn-xlg btn-tiffany btn-active-salla-pay" @click="onActivationClick()">فعّل بوليصات سلة</button>
            <p class="mt-10 text-primary">هل تود معرفة معلومات أكثر عن بوليصات سلة؟ <a class="text-underline" href="https://help.salla.sa/" target="_blank">انتقل لمركز المساعدة.</a></p>
          </div>
          <div class="supported-companies">
            <div class="grid-block grid-block--gap-20 salla-policies-companies">
              <div v-for="(column, columnIndex) in columns" :key="columnIndex" class="column">
                <div class="row">
                  <div v-for="(company, index) in column" :key="index" class="company-label">
                    <img :src="company.logo" :alt="company.name" />
                  </div>
                </div>
              </div>
              <div v-if="companies.length > 8" class="column">
                <div class="row">
                  <button data-toggle="modal" data-backdrop="static" data-target="#more_shipping_companies" class="btn btn-lg btn--outlined primary company">
                    +{{ companies.length - 8 }} اخرى
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <company-details />
  </div>
</template>

<script>
  import { mapActions, mapState } from "vuex";
  import CompanyDetails from "./CompanyDetails";

  export default {
    components: {
      "company-details": CompanyDetails,
    },
    data() {
      return {
        advantages: [
          "ربط فوري مع نخبة من شركات الشحن بدون أي عقود!",
          "خدمات شحن مخصَّصة لمختلف أنواع المنتجات.",
          "وصول أوسع عبر شركات شحن محلي ودولي.",
          "قبول الدفع عند الاستلام.",
          "خدمات توصيل سريع وتخزين وتوفير مواد تغليف الشحنات وغيرها.",
        ],
        ShowArchiveIcon: window.innerWidth > 1200,
        isLegacy: window.sallaLegacy
      };
    },
    computed: {
      ...mapState({
            companies: (state) => state.sallaPolicies.companies,
            companiesSections: (state) => state.sallaPolicies.companiesSections,
      }),
      canManageShipmentsHistory: () => window.initialData.permissions.management,
      isSallaPoliciesActive() {
        return window.initialData.is_salla_policies_active;
      },
      canAddNewCompany() {
        return window.initialData.permissions.add;
      },
      hasDefaultBranch() {
        return window.initialData.has_default_branch;
      },
      columns() {
        const companiesCount = this.companies.length;
        const columnsCount = Math.min(4, Math.ceil(companiesCount / 2));
        const companiesPerColumn = Math.ceil(companiesCount / columnsCount);

        const columns = [];
        let startIndex = 0;

        for (let columnIndex = 0; columnIndex < columnsCount; columnIndex++) {
          let endIndex = startIndex + companiesPerColumn;

          // Adjust the number of companies in specific columns
          if (columnIndex === 0) {
            endIndex = startIndex + 1;
          } else if (columnIndex === 1 || columnIndex === 3) {
            endIndex = startIndex + 2;
          } else if (columnIndex === 2) {
            endIndex = startIndex + 3;
          }

          columns.push(this.companies.slice(startIndex, endIndex));
          startIndex = endIndex;
        }

        return columns;
      },
      buttonText() {
        return this.ShowArchiveIcon ? "أرشيف البوليصات" : "الأرشيف";
      },
      buttonUrl() {
        return window.baseUrl + "/shipping/shipment_history";
      },
    },
    mounted() {
      // Add a window resize listener to update the state based on screen width
      window.addEventListener("resize", this.updateWindowWidth);
    },
    beforeDestroy() {
      window.removeEventListener("resize", this.updateWindowWidth);
    },
    methods: {
      ...mapActions(["activateSallaPolicies"]),
      onActivationClick() {
        window.showLoading();
        this.activateSallaPolicies();
        window.hideLoading();
      },
      updateWindowWidth() {
        this.ShowArchiveIcon = window.innerWidth > 1200;
      },
    },
  };
</script>
