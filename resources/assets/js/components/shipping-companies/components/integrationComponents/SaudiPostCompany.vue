<template>
  <div>
      <div v-show="company?.has_account == false">
          <div class="seperate-line"></div>
          <OneClickActivation/>
      </div>
      <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>
  </div>
</template>

<script>
import { mapState } from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: "SaudiPostCompany",
  components: {
    SyncShipmentStatus
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
}
</script>
