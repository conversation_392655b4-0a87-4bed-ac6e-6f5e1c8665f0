<template>
  <div>
    <div v-show="linkType === 'manual'" class="aramex-account-info">

      <PaidPlanPermission/>

      <FormInput 
        id="accountentity"
        :label="$t('shipping.aramex.account_entity')"
        icon="sicon-office"
        name="AccountEntity"
        :placeholder="$t('shipping.aramex.account_entity') + ' : RUH'"
        :value= "company?.integration_data?.AccountEntity"
        required />

      <FormInput 
        id="accountnumber"
        :label="$t('shipping.aramex.account_number')"
        icon="sicon-envelope"
        name="AccountNumber"
        :placeholder="$t('shipping.aramex.account_number') + ' : 20016'"
        :value= "company?.integration_data?.AccountNumber"
        required/>

      <FormInput 
        id="accountpin"
        :label="$t('shipping.aramex.account_pin')"
        icon="sicon-envelope"
        name="AccountPin"
        :placeholder="$t('shipping.aramex.account_pin') + ' : 331421'"
        :value= "company?.integration_data?.AccountPin"
        required />

      <FormInput 
        id="username"
        :label="$t('shipping.aramex.username')"
        icon="sicon-user"
        name="UserName"
        :placeholder="$t('shipping.aramex.username') + ' : <EMAIL>'"
        :value= "company?.integration_data?.UserName"
        required />

      <FormInput 
        id="password"
        :label="$t('shipping.aramex.password')"
        icon="sicon-key"
        name="Password"
        :placeholder="$t('shipping.aramex.password')"
        :value= "company?.integration_data?.Password"
        required />

      <TreeSelect
          v-model="paymentType"
          select-id="payment_type"
          :label="$t('shipping.aramex.payment_type')"
          icon="sicon-cash-payment"
          :options="paymentTypes"
          :multiple="false"
          :searchable="false" />

      <TreeSelect
          v-model="paymentCurrency"
          select-id="payment_currency"
          :label="$t('shipping.aramex.payment_currency')"
          icon="sicon-cash-payment"
          :options="paymentCurrencies"
          :multiple="false"
          :searchable="false" />

      <div class="aramex-account-settings">
        <div class="alert alert-info no-border">
          <span class="text-semibold text-muted2">{{$t('shipping.aramex.domestic_settings') }}</span>
        </div>

        <FormInput 
          id="product_group_in"
          :label="$t('shipping.aramex.product_group')"
          icon="sicon-box-bankers"
          name="product_group_in"
          :placeholder="$t('shipping.aramex.product_group')"
          :value= "company?.integration_data?.product_group_in ?? 'DOM'"
          required />

        <FormInput 
          id="product_type_in"
          :label="$t('shipping.aramex.product_type')"
          icon="sicon-box-bankers"
          name="product_type_in"
          :placeholder="$t('shipping.aramex.product_type')"
          :value= "company?.integration_data?.product_type_in ?? 'ONP'"
          required />

        <div class="alert alert-info no-border">
          <span class="text-semibold text-muted2">{{$t('shipping.aramex.international_settings') }}</span>
        </div>

        <FormInput 
          id="product_group_out"
          :label="$t('shipping.aramex.product_group')"
          icon="sicon-box-bankers"
          name="product_group_out"
          :placeholder="$t('shipping.aramex.product_group')"
          :value= "company?.integration_data?.product_group_out ?? 'EXP'"
          required />

        <FormInput 
          id="product_type_out"
          :label="$t('shipping.aramex.product_type')"
          icon="sicon-box-bankers"
          name="product_type_out"
          :placeholder="$t('shipping.aramex.product_type')"
          :value= "company?.integration_data?.product_type_out ?? 'EPX'"
          required />

      </div>
    </div>

    <DisplayName/>
    <BasicDisplayForm/>

    <TreeSelect
        v-model="shippingType"
        select-id="shipping_type"
        :label="$t('shipping.integration.shipping_type')"
        icon="sicon-house-door"
        :options="shippingTypes"
        :multiple="false"
        :searchable="false" />

    <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
    <div v-show="company?.has_salla_polices_account !== company?.support_international">
       <CheckboxInput
          id="show_order_items_on_police"
          :label="$t('shipping.integration.show_order_items_on_police')"
          name="show_order_items_on_police"
          value="1"
          :checked="company?.integration_data?.show_order_items_on_police ? true : false"/>
    </div>
    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>

    <SallaPoliciesHelpText/>
  </div>
</template>

<script>
import { mapState } from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: 'AramexCompany',
  components: {
    SyncShipmentStatus
  },
  props: {
    linkType: {
      type: String,
      default: "salla_aramex"
    },
  },
  data() {
    return {
      paymentTypes: [
        { id: 'P', label: 'Payment type P', name: 'payment_type_P' },
        { id: '3', label: 'Payment type 3', name: 'payment_type_3' },
      ],
      paymentCurrencies: [
        { id: 'SAR', label: `${this.$t('shipping.aramex.sar')}`, name: 'payment_currency_SAR' },
        { id: 'USD', label: `${this.$t('shipping.aramex.usd')}`, name: 'payment_currency_USD' },
        { id: 'OMR', label: `${this.$t('shipping.aramex.omr')}`, name: 'payment_currency_OMR' },
        { id: 'KWD', label: `${this.$t('shipping.aramex.kwd')}`, name: 'payment_currency_KWD' },
        { id: 'AED', label: `${this.$t('shipping.aramex.AED')}`, name: 'payment_currency_AED' },
      ],
      shippingTypes: [
        { id: 'withoutPickUp', label: `${this.$t('shipping.aramex.withoutPickUp')}`, name: 'shipping_type_withoutPickUp' },
        { id: 'withPickUp', label: `${this.$t('shipping.aramex.withPickUp')}`, name: 'shipping_type_withPickUp' },
      ],
      paymentType: null,
      paymentCurrency: null,
      shippingType: null,
    }
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  },
  watch: {
    company: {
      handler() {
        if (this.company?.integration_data) {
          this.paymentType     = this.company.integration_data.payment_type ?? 'P';
          this.paymentCurrency = this.company.integration_data.payment_currency ?? 'SAR';
          this.shippingType    = this.company.integration_data.shipping_type ?? 'withPickUp';
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  },
};
</script>
