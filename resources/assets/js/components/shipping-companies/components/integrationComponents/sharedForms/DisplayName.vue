<template>
  <FormInput
    id="display_name"
    :label="$t('shipping.integration.display_name')"
    :sub-title="$t('shipping.integration.display_name_sub_title')"
    icon="sicon-envelope"
    name="display_name"
    :value = "company?.integration_data?.display_name"
    :placeholder="$t('shipping.integration.display_name_placeholder')"
    maxlength="128"
  />
</template>

<script>
import {mapState} from "vuex";

export default {
  props: {},
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
};
</script>
