<template>
  <div>
    <h6 class="mb-20 mt-0">{{ $t('shipping.integration.display_data_in_policy') }}</h6>

    <FormInput 
      id="company_name"
      :label="$t('shipping.integration.store_name')"
      icon="sicon-capitol-building"
      name="company_name"
      :placeholder="$t('shipping.integration.store_name')"
      :value = "company?.integration_data?.company_name"
      required/>

    <FormInput 
      :id="company?.slug == 'smsa' ? 'name' : 'sender_name'"
      :label="$t('shipping.integration.sender_name')"
      icon="sicon-user-square"
      :name="company?.slug == 'smsa' ? 'name' : 'sender_name'"
      :placeholder="$t('shipping.integration.sender_name')"
      :value = "company?.slug == 'smsa' ? company?.integration_data?.name
                         : company?.integration_data?.sender_name"
      required/>

      <FormInput 
        id="mobile" 
        :label="$t('shipping.integration.mobile')"
        icon="sicon-phone"
        name="mobile"
        :placeholder="$t('shipping.integration.mobile')"
        :is-numeric="true"
        :value = "company?.integration_data?.mobile"
        required/>

  </div>
</template>

<script>
import {mapState} from "vuex";

export default {
  props: {},
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
};
</script>
