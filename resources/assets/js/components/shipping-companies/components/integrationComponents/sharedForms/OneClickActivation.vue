<template>
  <div>
    <p class="mb-20">{{ $t('shipping.integration.one_click_activation_label') }}</p>
    <div class="row">
      <div class="col-md-12">
        <div v-if="hasAddPermission || hasEditPermission">
          <button 
            class="btn btn-info pull-left btn-save" 
            type="button" 
            @click="handleClick">
            {{ $t('shipping.integration.one_click_activation_btn_label') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapActions } from "vuex";

  export default {
    computed: {
      hasAddPermission() {
        return window.initialData.permissions.add_option;
      },
      hasEditPermission() {
        return window.initialData.permissions.edit_option;
      },
    },
    methods: {
      ...mapActions(["saveCompanyDetails"]),
      async handleClick() {
        event.preventDefault();
        this.saveCompanyDetails();
      },
    },
  };
</script>
