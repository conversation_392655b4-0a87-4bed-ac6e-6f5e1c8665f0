<template>
  <div>
    <LocationRequiredAlert />
    <CompanyIntegrationInstructions v-show="linkType === 'manual'"/>
    <div v-show="linkType === 'manual'" class="account-info">
      <FormInput 
        id="email" 
        :label="$t('shipping.barq.email')" 
        icon="sicon-envelope" 
        name="email" 
        :placeholder="$t('shipping.barq.email')" 
        :value="company?.integration_data?.email" required />

      <FormInput 
        id="password" 
        :label="$t('shipping.barq.password')" 
        icon="sicon-key" 
        name="password" 
        :placeholder="$t('shipping.barq.password')" 
        :value="company?.integration_data?.password" required />

      <TreeSelect 
        v-model="AlwaysOpen" 
        select-id="always_open" 
        :label="$t('shipping.barq.is_always_open')" 
        icon="sicon-calendar-alt" 
        :options="AlwaysOpenOptions" 
        :multiple="false" :searchable="false" />

      <div class="row">
        <div class="col-xs-6">
          <TreeSelect 
            v-model="openingTime" 
            select-id="opening_time" 
            :label="$t('shipping.barq.opening_time')"
            icon="sicon-time" 
            :options="timesOptions" 
          />
        </div>
        <div class="col-xs-6">
          <TreeSelect 
            v-model="closingTime" 
            select-id="closing_time" 
            :label="$t('shipping.barq.closing_time')" 
            icon="sicon-time" 
            :options="timesOptions" 
          />
        </div>
      </div>

      <TreeSelect 
        v-model="StartDay"
        select-id="start_day" 
        :label="$t('shipping.barq.start_day')" 
        icon="sicon-calendar-alt" 
        :options="Days" 
        :multiple="false" 
        :searchable="false" />

      <TreeSelect 
        v-model="EndDay" 
        select-id="end_day" 
        :label="$t('shipping.barq.end_day')" 
        icon="sicon-calendar-alt" 
        :options="Days" 
        :multiple="false" 
        :searchable="false" />
    </div>

    <DisplayName />
    <BasicDisplayForm />

    <div v-show="canShowHints">
      <div class="alert alert-info no-border account-info-toggler">
        <span class="text-semibold text-muted">
          {{$t('shipping.barq.has_no_account')}}
          <a href="#" @click="onBarqCreateMerchantAccountClick">{{$t('shipping.barq.has_no_account_click_here')}}</a>
        </span>
      </div>
    </div>
    <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>
    <SallaPoliciesHelpText />
  </div>
</template>

<script>
  import { mapActions, mapState } from "vuex";
  import SyncShipmentStatus from './SyncShipmentStatus.vue';
 
  export default {
    name: 'BarqCompany',
    components: {
      SyncShipmentStatus
    },
    props: {
      linkType: {
        type: String,
        default: 'salla_barq',
      },
    },
    data() {
      return {
        AlwaysOpenOptions: [
          { id: "0", label: `${this.$t("shipping.barq.disable")}`, name: "always_open_0" },
          { id: "1", label: `${this.$t("shipping.barq.activate")}`, name: "always_open_1" },
        ],
        Days: [
          { id: "0", label: `${this.$t("shipping.barq.days.sunday")}`, name: "start_day_0" },
          { id: "1", label: `${this.$t("shipping.barq.days.monday")}`, name: "start_day_1" },
          { id: "2", label: `${this.$t("shipping.barq.days.tuesday")}`, name: "start_day_2" },
          { id: "3", label: `${this.$t("shipping.barq.days.wednesday")}`, name: "start_day_3" },
          { id: "4", label: `${this.$t("shipping.barq.days.thursday")}`, name: "start_day_4" },
          { id: "5", label: `${this.$t("shipping.barq.days.friday")}`, name: "start_day_5" },
          { id: "6", label: `${this.$t("shipping.barq.days.saturday")}`, name: "start_day_6" },
        ],
        openingTime: "",
        closingTime: "",
        timesOptions: [],
        AlwaysOpen: null,
        StartDay: null,
        EndDay: null,
        canShowHints: this.company?.has_account === false && this.company?.has_salla_polices_account === false,
      };
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
      }),
    },
    watch: {
      company: {
        handler() {
          if (this.company?.integration_data) {
            this.AlwaysOpen = this.company.integration_data.always_open ?? "0";
            this.StartDay = this.company.integration_data.start_day ?? "0";
            this.EndDay = this.company.integration_data.end_day ?? "6";
            this.openingTime = this.company.integration_data.opening_time ?? '07:00';
            this.closingTime = this.company.integration_data.closing_time ?? '23:00';
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {
      this.generateTimeOptions();
    },
    methods: {
      ...mapActions(["barqCreateMerchantAccount"]),
      generateTimeOptions() {
        for (let hour = 0; hour < 24; hour++) {
          const time = hour.toString().padStart(2, '0') + ":00";
          this.timesOptions.push({ id: time, label: time, name: time });
        }
      },
      async onBarqCreateMerchantAccountClick(event) {
        event.preventDefault();
        window.showLoading();
        this.barqCreateMerchantAccount();
      },
      onSyncShipmentStatusChange(data) {
        this.$emit('input', data);
      },
    },
  };
</script>
