<template>
  <div>
    <div v-show="linkType === 'manual'" class="aramex-account-info">
      <FormInput
          id="apiAccount"
          label="Api Account"
          icon="sicon-envelope"
          name="apiAccount"
          placeholder="Api Account"
          :value="company?.integration_data?.apiAccount" required />

      <FormInput
          id="privateKey"
          label="Private key"
          icon="sicon-envelope"
          name="privateKey"
          placeholder="Private key"
          :value="company?.integration_data?.privateKey" required />

      <FormInput
          id="customerCode"
          label="Customer code"
          icon="sicon-envelope"
          name="customerCode"
          placeholder="Customer code"
          :value="company?.integration_data?.customerCode" required />

      <FormInput
          id="CustomerPassword"
          label="Customer Password"
          icon="sicon-envelope"
          name="CustomerPassword"
          placeholder="Customer Password"
          :value="company?.integration_data?.CustomerPassword" required />
    </div>

    <DisplayName />
    <BasicDisplayForm />
    <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>
    <SallaPoliciesHelpText />
  </div>
</template>

<script>
import {mapState } from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: "JntCompany",
  components: {
    SyncShipmentStatus
  },
  props: {
    linkType: {
      type: String,
      default: "salla_jnt"
    },
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  },
};
</script>
