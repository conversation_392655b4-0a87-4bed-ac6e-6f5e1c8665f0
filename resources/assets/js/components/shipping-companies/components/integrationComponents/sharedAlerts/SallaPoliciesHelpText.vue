<template>
  <div v-show="company?.has_salla_polices_account">
    <div class="seperate-line"></div>
    <p class="mt-10">
     {{  $t('shipping.need_salla_polices_help') }}
      <a class="text-underline" target="_blank" href="https://help.salla.sa/article/*********">{{ $t('shipping.salla_help_center') }}</a>
    </p>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
}
</script>