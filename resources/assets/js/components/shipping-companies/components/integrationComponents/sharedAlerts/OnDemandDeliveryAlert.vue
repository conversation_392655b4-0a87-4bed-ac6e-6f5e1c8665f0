<template>
  <div>
    <div v-if="shippingCreditType" class="alert-box alert-box--info">
      <i class="sicon-info"></i>
      <article>
        <p>{{ $t('shipping.on_demand_balance_alert') }}</p>
      </article>
    </div>
    <div v-show="hasNoGeolocation">
      <div class="alert-box alert-box--warning mb-5">
        <i class="sicon-info"></i>
        <article>
          <p>
            {{ $t('shipping.on_demand_location_alert') }}
            <a href="/branches" target="_blank">{{$t('shipping.shipping_branches')  }}
          </a>
          </p>
        </article>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    hasNoGeolocation() {
      return ! window.initialData.has_geolocation
    },
    shippingCreditType(){
      return window.initialData.shipping_custom_settings.deduct_shipping_credit_type;
    }
  }
}
</script>
