<template>
<div>
  <div>
      <div v-show="! company?.has_account && ! company?.has_salla_polices_account" class="alert alert-info no-border account-info-toggler">
        <p class="text-semibold text-muted">
          {{$t('shipping.integration.register_first_step')}}
          <a href="company?.website" target='_blank'> {{company?.name}}</a>
          {{$t('shipping.integration.register_second_step')}}
        </p>
      </div>
  </div>

</div>
</template>

<script>
import { mapState } from "vuex";

export default {
  props: {},
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
}
</script>
