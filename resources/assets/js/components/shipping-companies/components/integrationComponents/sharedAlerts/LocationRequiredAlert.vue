<template>
  <div v-show="company?.required_location === true">
      <section class="alert-box alert-box--warning">
        <i class="sicon-alert"></i>
        <article>
          {{$t('shipping.integration.required_location')}}
          <a href="/settings/component/options" target="_blank">
            {{$t('shipping.integration.store_options')}}
          </a> .
        </article>
      </section>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  }
}
</script>
