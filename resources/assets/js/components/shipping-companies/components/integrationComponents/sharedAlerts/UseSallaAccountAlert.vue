<template>
  <div 
    v-if="(company?.support_salla_policies && ! company?.has_salla_polices_account) &&  (
      (company?.slug != 'aymakan' || company?.slug != 'barq') ||
      (
        (company?.slug == 'aymakan' && AymakanPoliciesAllawed ) || (company?.slug == 'barq' && BarqPoliciesAllawed)
      )
    ) ">

    <div v-if="company?.salla_special_price">
      <section class="alert-box alert-box--warning mb-15">
        <i class="sicon-info"></i>
        <article>
          <p>
            {{ $t('shipping.salla_polices_discount') }}
            ({{ company?.salla_special_price?.discount_percentage }})
            {{ $t('shipping.new_price') }}
            {{ company?.salla_special_price?.new_price }}
            {{ $t('shipping.SAR') }}
            {{ $t('shipping.instead_of') }}
            {{ company?.salla_special_price?.old_price }}
            {{ $t('shipping.SAR') }}
            {{ $t('shipping.more_details') }}
            <a href="https://help.salla.sa/article/**********">{{  $t('shipping.press_here') }}</a>
          </p>
        </article>
      </section>
    </div>

    <div v-else>
      <div class="row mb-20">
        <div class="col-sm-12">
          <div>
            <section class="alert-box alert-box--warning mb-15">
              <i class="sicon-info"></i>
              <article>
                <p>
                  {{ $t('shipping.use_salla_account') }}  {{ $t('shipping.more_details') }}
                  <a href="https://help.salla.sa/article/**********">{{  $t('shipping.press_here') }}</a>
                </p>
              </article>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {mapState} from "vuex";

export default {
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
    AymakanPoliciesAllawed(){
      return window.initialData.shipping_custom_settings.aymakan_policies_allowed && this.company.slug == 'aymakan' ;
    },
    BarqPoliciesAllawed(){
      return window.initialData.shipping_custom_settings.barq_policies_allowed && this.company.slug == 'barq' ;
    }
  }
}
</script>
