<template>
  <div>
    <div v-show="linkType === 'manual'" class="aramex-account-info">
      <FormInput
          id="api_key"
          label="Api Key"
          icon="sicon-key"
          name="api_key"
          placeholder="Api Key"
          :value="company?.integration_data?.api_key" required />
    </div>

    <DisplayName />
    <BasicDisplayForm />
    <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>
    <SallaPoliciesHelpText />
  </div>
</template>

<script>
import {mapState } from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: "FlowExpressCompany",
  components: {
    SyncShipmentStatus
  },
  props: {
    linkType: {
      type: String,
      default: "salla_flow"
    },
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  },
};
</script>
