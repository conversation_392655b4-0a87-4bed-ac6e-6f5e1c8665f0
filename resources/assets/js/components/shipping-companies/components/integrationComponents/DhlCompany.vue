<template>
<div>
  <div v-if="dhlSettings.has_dh_api">
      <div v-show="linkType === 'manual'" class="aramex-account-info">
          <FormInput 
            id="site_id"
            :label="$t('shipping.dhl.site_id')"
            icon="sicon-laptop"
            name="site_id"
            :placeholder="$t('shipping.dhl.site_id')"
            :value= "company?.integration_data?.site_id"
            required />

          <FormInput 
            id="account_number"
            :label="$t('shipping.dhl.account_number')"
            icon="sicon-envelope"
            name="account_number"
            :placeholder="$t('shipping.dhl.account_number')"
            :value= "company?.integration_data?.account_number"
            required />


          <FormInput 
            id="password"
            :label="$t('shipping.dhl.password')"
            icon="sicon-key"
            name="password"
            :placeholder="$t('shipping.dhl.password')"
            :value= "company?.integration_data?.password"
            required />
      </div>
  </div>
  <div v-else>
    <PaidPlanPermission/>
  </div>

  <DisplayName/>

  <h6 class="mb-20 mt-0">{{ $t('shipping.integration.display_data_in_policy') }}</h6>

  <FormInput 
    id="company_name"
    :label="$t('shipping.integration.store_name')"
    icon="sicon-store"
    name="company_name"
    :placeholder="$t('shipping.dhl.options.store_name_en')"
    :value= "company?.integration_data?.company_name"
    required
    maxlength="35" 
  />

  <div v-show="dhlSettings.dhl_show_thermal_policies">
      <TreeSelect
          v-model="LabelTemplate"
          select-id="labelTemplate"
          icon="sicon-house-door"
          :options="LabelTemplates"
          :multiple="false"
          :searchable="false" />
  </div>
  
  <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
  <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>

  <SallaPoliciesHelpText/>
</div>
</template>

<script>
import { mapState } from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: "DhlCompany",
  components: {
    SyncShipmentStatus
  },
  props: {
    linkType: {
      type: String,
      default: "salla_dhl"
    },
  },
  data() {
    return {
        LabelTemplates: [
        // ... 
        { id: 'ECOM26_A6_001', label:`${this.$t('shipping.dhl.options.normal_print')}`, name: 'labelTemplate_ECOM26_A6_001' },
        { id: '8X4_thermal', label: `${this.$t('shipping.dhl.options.thermal_print')}`, name: 'labelTemplate_8X4_thermal' },
        
      ],
      LabelTemplate: null,
    }
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
    dhlSettings() {
      return window.initialData.permissions
    },
  },
  watch: {
    company: {
      handler() {
        if (this.company?.integration_data) {
          this.LabelTemplate = this.company.integration_data.labelTemplate ?? 'ECOM26_A6_001';
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  },
}
</script>
