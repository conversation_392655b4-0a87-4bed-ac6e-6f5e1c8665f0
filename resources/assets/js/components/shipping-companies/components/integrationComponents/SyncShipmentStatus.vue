<template>
  <CheckboxInput
    id="sync_shipment_status"
    :label="$t('shipping.integration.sync_shipment_status')"
    name="sync_shipment_status"
    :value="!!company?.integration_data?.sync_shipment_status"
    :checked="!!company?.integration_data?.sync_shipment_status"
    @input="onChange"
  />
</template>

<script>
import { mapState } from 'vuex'
import CheckboxInput from '../layouts/CheckboxInput.vue'

export default {
  name: 'SyncShipmentStatus',
  components: { CheckboxInput },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  },
  methods: {
    onChange(value) {
      this.$emit('input', {
        name: 'sync_shipment_status',
        value: value ? '1' : '0',
      })
    },
  },
}
</script>