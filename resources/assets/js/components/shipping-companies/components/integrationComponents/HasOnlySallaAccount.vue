<template>
<div>
  <div class="seperate-line"></div>
  <LocationRequiredAlert/>
  <DisplayName/>
  <BasicDisplayForm/>
  <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
  <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>
  <SallaPoliciesHelpText/>
</div>
</template>

<script>
import { mapState } from "vuex";

export default {
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  },
}
</script>