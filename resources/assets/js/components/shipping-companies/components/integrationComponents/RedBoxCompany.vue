<template>
  <div>
    <div v-if="company?.has_account == false && linkType != 'salla_redbox'">
      <p class="mb-20">{{ $t('shipping.integration.one_click_activation_label') }}</p>
    </div>

    <div v-show="linkType === 'salla_redbox'" class="aramex-account-info">
      <DisplayName />

    <div class="row">
        <div class="col-sm-6">
            <div class="d-flex flex-column mt-10 mb-30" style="align-items: start;">
                <h6 class="mt-10 mb-20">خدمة الإيداع الذاتي لتاجر سلة</h6>
                <div class="d-flex flex-rowd justify-content-center align-items-center mb-20">
                    <p class="mb-0 mr-10">رمز بطاقة الإيداع الذاتي</p>
                    <span class="color-box-wrapper">
                        <span class="sicon-padlock icon pr-5" style="color: #eeeeee;"></span>
                        <span>BGUOWVWT</span>
                    </span>
                </div>
                <p>
                    <strong>
                        <span class="sicon-info icon pr-5"></span>
                        <span>تعليمات لإيداع الشحنة</span>
                    </strong>
                    <a class="text-underline" href="#" @click.prevent="qrTip = !qrTip">
                        <span v-show="!qrTip">عرض التعليمات</span>
                        <span v-show="qrTip">اخفاء التعليمات</span>
                    </a>
                </p>
                <ul v-show="qrTip">
                    <li class="pb-5 text-muted">أذهب الي الخزانة الذكية</li>
                    <li class="pb-5 text-muted">أضغط علي "تسجيل الدخول التاجر أو المندوب"</li>
                    <li class="pb-5 text-muted">ادخل الكود الخاص بك</li>
                    <li class="pb-0 text-muted">اتبع التعليمات بالشاشة</li>
                </ul>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="d-flex flex-column" style="align-items: end;">
                <img src="https://cdn.assets.salla.network/prod/admin/images/shipping-companies/self-dropoff-code-small.png" alt="Redbox QR Code" style="width: 120px;"> 
                <a href="https://cdn.assets.salla.network/prod/admin/images/shipping-companies/self-dropoff-code.png" download="Redbox-QR-Code.png" class="btn btn-flat btn-default btn-sm" style="margin: 15px 0px 0px 6px" @click.prevent="e => downloadQrCode(e.target)">
                    <span class="sicon-download icon"></span>
                    تحميل الرمز
                </a>
            </div>   
        </div>
    </div>
            
      <BasicDisplayForm />
      <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
      <SallaPoliciesHelpText />
    </div>
    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>
  </div>
</template>

<script>

import {mapState} from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: "RedBoxCompany",
  components: {
    SyncShipmentStatus
  },
  props: {
    linkType: {
      type: String,
      default: "salla_redbox"
    },
  },
    data() { 
        return {
            qrTip: false,
        }
    },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
    }),
    },
    methods: {
        onSyncShipmentStatusChange(data) {
            this.$emit('input', data);
        },
        downloadQrCode(target) {
            fetch(target.href)
                .then(response => response.blob())
                .then(blob => {
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = 'Redbox-self-dropoff-code.png';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch(error => console.error('Error downloading the image:', error));
        }
  }
};
</script>