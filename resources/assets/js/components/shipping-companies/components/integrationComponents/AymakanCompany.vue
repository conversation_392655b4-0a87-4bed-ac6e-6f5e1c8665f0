<template>
  <div>
    <CompanyIntegrationInstructions v-show="linkType === 'manual'"/>
    <div v-show="linkType === 'manual'" class="aramex-account-info">
      <FormInput 
        id="token" 
        :label="$t('shipping.aymakan.token')" 
        icon="sicon-envelope" 
        name="token" 
        :placeholder="$t('shipping.aymakan.token')" 
        :value="company?.integration_data?.token" required />
    </div>

    <DisplayName />
    <BasicDisplayForm />
    <div v-show="linkType === 'manual'" class="account-info-toggler">
      <div class="alert alert-info no-border">
        <span class="text-semibold text-muted">
          {{$t('shipping.aymakan.activate_webhook_hint')}}
          <a href="#" @click="onAymakanCreateWebhookClick">
            {{$t('shipping.aymakan.click_here')}}
          </a>
          {{$t('shipping.aymakan.activate_webhook')}}
        </span>
      </div>
    </div>
    <h6 class="mb-20 mt-0">{{$t('shipping.integration.additional_options')}}</h6>
    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>

    <SallaPoliciesHelpText />
  </div>
</template>

<script>
  import { mapActions, mapState } from "vuex";
  import SyncShipmentStatus from './SyncShipmentStatus.vue';

  export default {
    name: 'AymakanCompany',
    components: {
      SyncShipmentStatus
    },
    props: {
      linkType: {
        type: String,
        default: "salla_aymakan"
      },
    },
    computed: {
      ...mapState({
        company: (state) => state.companyDetails.company,
      }),
    },
    methods: {
      ...mapActions(["aymakanCreateWebhook"]),
      onSyncShipmentStatusChange(data) {
        this.$emit('input', data);
      },
      async onAymakanCreateWebhookClick(event) {
        event.preventDefault();
        window.showLoading();
        this.aymakanCreateWebhook();
      },
    },
  };
</script>
