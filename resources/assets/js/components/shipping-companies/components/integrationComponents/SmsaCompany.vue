<template>
  <div>
    <div v-show="linkType === 'manual'" class="account-info">
      <FormInput
          id="smsa_pass_key"
          :label="$t('shipping.smsa.smsa_pass_key')"
          icon="sicon-key"
          name="pass_key"
          v-if="isSmsaRestfullEnabled ? !company?.integration_data?.smsa_api_key : true"
          :placeholder="$t('shipping.smsa.smsa_pass_key')"
          :value="company?.integration_data?.smsa_pass_key" />

      <FormInput
        id="smsa_api_key"
        :label="$t('shipping.smsa.smsa_api_key')"
        icon="sicon-key"
        name="api_key"
        :placeholder="$t('shipping.smsa.smsa_api_key')"
        v-if="isSmsaRestfullEnabled"
        :value="company?.integration_data?.smsa_api_key" required />
    </div>
    <DisplayName/>
    <BasicDisplayForm/>

    <h6 class="mb-20 mt-0"> {{$t('shipping.integration.additional_options')}}</h6>
    <CheckboxInput
                   id="auto_print_awb"
                   :label="$t('shipping.integration.auto_print_awb')"
                   name="auto_print_awb"
                   value="1"
                   :checked="company?.integration_data?.auto_print_awb ? true : false "/>

    <SyncShipmentStatus @input="onSyncShipmentStatusChange"/>

    <SallaPoliciesHelpText/>

  </div>
</template>

<script>
import { mapState } from "vuex";
import SyncShipmentStatus from './SyncShipmentStatus.vue';

export default {
  name: "SmsaCompany",
  components: {
    SyncShipmentStatus
  },
  props: {
    linkType: {
      type: String,
      default: "salla_smsa"
    },
  },
  computed: {
    ...mapState({
      company: (state) => state.companyDetails.company,
      isSmsaRestfullEnabled() {
        return window.initialData.permissions.is_smsa_restfull_enabled;
      },
    }),
  },
  methods: {
    onSyncShipmentStatusChange(data) {
      this.$emit('input', data);
    }
  }
}
</script>