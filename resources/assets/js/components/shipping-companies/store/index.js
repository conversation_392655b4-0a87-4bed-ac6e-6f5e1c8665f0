import Vue from "vue";
import Vuex from "vuex";
import customCompanies from "./modules/customCompanies";
import sallaPolicies from "./modules/sallaPolicies";
import shippingRateCalculator from "./modules/shippingRateCalculator";
import companyDetails from "./modules/companyDetails";
import supportedPickupCities from "./modules/supportedPickupCities";
import shippingCompanyZones from "./modules/shippingCompanyZones";
import tierSection from "./modules/tierSection";
import ticketSystem from "./modules/ticketSystem";
import packaging from "./modules/packaging";
import feedback from "./modules/feedback";

Vue.use(Vuex);

export default new Vuex.Store({
    modules: {
        customCompanies,
        sallaPolicies,
        shippingRateCalculator,
        companyDetails,
        supportedPickupCities,
        shippingCompanyZones,
        tierSection,
        ticketSystem,
        packaging,
        feedback
    }
});
