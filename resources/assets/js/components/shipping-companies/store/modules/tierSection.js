import api from "../../config/axios.config";
import { showSwAlert } from "../../utils/showSwAlert";

export default {
  state: {
    tierPricingList: [],
    storeTier: []
  },
  mutations: {
    setTierPricingList(state, tierList) {
      state.tierPricingList = tierList;
    },
    setStoreTier(state, tierList) {
      state.storeTier = tierList;
    },
  },
  actions: {
    async getTierPricingList({ commit }) {
      return new Promise((resolve) => {
        api.get(`tiers_pricing`)
          .then((res) => {
            commit("setTierPricingList", res.data.data);
            resolve(res.data);
          })
          .catch((err) => {
            
          });
      });
    }, 
    async getStoreTier({ commit }) {
      return new Promise((resolve) => {
        window.showLoading();
        api.get(`store_tier`)
          .then((res) => {
            window.hideLoading();
            commit("setStoreTier", res.data.data);
            resolve(res.data);
          })
          .catch((err) => {
            window.hideLoading();
            const errorObj = err?.response?.data?.error;
            showSwAlert("error", errorObj?.message, undefined, false, 3000);
          });
      });
    },
  },
};
