import api from "../../config/axios.config";
import { showSwAlert } from "../../utils/showSwAlert";

export default {
  state: {
    company: [],
    integrationData: {},
    companyActivation: false,
    companyDeactivation: false,
    branchesList: [],
    aymakanWebhook: [],
    merchantData: [],
    refreshBranches: [],
    errors: {},
    serviceType: null,
  },
  mutations: {
    setCompany(state, company) {
      state.company = company;
    },
    setIntegrationData(state, integrationData) {
      state.integrationData = integrationData;
    },
    setErrors(state, errors) {
      state.errors = errors;
    },
    setCompanyDeactivation(state, status) {
      state.companyDeactivation = status;
    },
    setAjeekBranchesList(state, branches) {
      state.branchesList = branches;
    },
    setAymakanCreateWebhook(state, data) {
      state.aymakanWebhook = data;
    },
    setBarqCreateMerchantAccount(state, accountData) {
      state.merchantData = accountData;
    },
    setSaeeCreateMerchantAccount(state, accountData) {
      state.merchantData = accountData;
    },
    setServiceType(state, serviceType) {
      state.serviceType = serviceType;
    },
  },
  actions: {
    async getCompanyDetails({ commit }, { companyId,serviceType }) {
      return new Promise((resolve) => {
        api
          .get(`company/${companyId}`,{
            params: {
              service_type: serviceType
            }
          }).then((res) => {
            commit("setCompany", res.data.data);
            commit("setIntegrationData", res.data.data?.integration_data);
            commit("setErrors", {});
            commit("setServiceType", serviceType);
            resolve(res.data);
          })
          .catch((err) => {
            const errorObj = err?.response?.data?.error;
            showSwAlert("error", errorObj?.message, undefined, false, 3000);
            window.hideLoading();
          });
      });
    },
    async updateCompanyDetails({ commit, state }, payload) {
      window.showLoading();
      const companyId = state.company.id;
      payload.service_type = state.serviceType;
      await api
        .put(`company/${companyId}`, payload)
        .then((res) => {
          commit("setIntegrationData", {});
          commit("setErrors", {});
          window.hideLoading();
          showSwAlert("success", res.data.data?.message, undefined, false, 3000);
          $("#company_details_modal").modal("hide");
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;

          if (errorObj.fields) {
            commit("setErrors", errorObj.fields);
          }
          showSwAlert("error", errorObj?.message ?? errorObj?.code, undefined, false, 3000);

          window.hideLoading();
        });
    },
    async saveCompanyDetails({ commit, state }, payload = null) {
      window.showLoading();
      const companyId = state.company.id;
      payload.service_type = state.serviceType;
      await api
        .post(`company/${companyId}`, payload)
        .then((res) => {
          commit("setIntegrationData", {});
          commit("setErrors", {});
          window.hideLoading();
          showSwAlert("success", res.data.data?.message, undefined, false, 3000);
          $("#company_details_modal").modal("hide");
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;

          if (errorObj.fields) {
            commit("setErrors", errorObj.fields);
          }
          showSwAlert("error", errorObj?.message ?? errorObj?.code, undefined, false, 3000);

          window.hideLoading();
        });
    },
    async disconnectCompanyConnection({ commit, state }) {
      window.showLoading();
      const companyId = state.company.id;
      await api
        .delete(`company/${companyId}`)
        .then((res) => {
          commit("setCompanyDeactivation", res.status);
          window.hideLoading();
          showSwAlert("success", "تم إلغاء الربط بنجاح", undefined, false, 3000);
            $("#company_details_modal").modal("hide");
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;
          showSwAlert("error", errorObj?.message ?? errorObj?.code, undefined, false, 3000);
          window.hideLoading();
        });
    },
    async aymakanCreateWebhook({  commit, state  }) {
      const integrationData = state.company?.integration_data;
      const payload = {
        webhook_id: integrationData.webhook_id,
        webhook_url: integrationData.webhook_url,
        webhook_token: integrationData.webhook_token,
      };
      await api
        .post(`companies/aymakan/webhooks/create`, payload)
        .then((res) => {
          commit("setAymakanCreateWebhook", res.status);
          window.hideLoading();
          showSwAlert("success", "تم التحديث بنجاح", undefined, false, 3000);
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;
          showSwAlert("error", errorObj?.message, undefined, false, 3000);
          window.hideLoading();
        });
    },
    async barqCreateMerchantAccount({ commit }) {
      await api
        .get(`companies/barq/register`)
        .then((res) => {
          commit("setBarqCreateMerchantAccount", res.data.data);
          window.hideLoading();
          showSwAlert("success", "تم الحفظ بنجاح", undefined, false, 3000);
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;
          showSwAlert("error", errorObj?.message, undefined, false, 3000);
          window.hideLoading();
        });
    },
    async saeeCreateMerchantAccount({ commit }) {
      await api
        .get(`companies/saee/register`)
        .then((res) => {
          commit("setSaeeCreateMerchantAccount", res.data.data);
          window.hideLoading();
          showSwAlert("success","تم الحفظ بنجاح", undefined, false, 3000);
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;
          showSwAlert("error", errorObj?.message, undefined, false, 3000);
          window.hideLoading();
        });
    },
  },
};
