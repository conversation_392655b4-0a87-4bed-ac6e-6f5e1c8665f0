import api from "../../config/axios.config";
import { showSwAlert } from "../../utils/showSwAlert";

export default {
  state: {
    zones: [],
    zoneDetails: [],
    resetZoneDetails: false,
    countries: [],
    cities: [],
    excludedCities: [],
    selectedCountry: [],
    selectedCities: [],
    selectedExcludedCities: [],
    zoneErrors: {},
    newAddedZones: [],
    serviceType: null,
  },
  mutations: {
    setShippingCompanyZones(state, zones) {
      state.zones = zones;
    },
    setShippingCompanyZoneDetails(state, zoneDetails) {
      state.zoneDetails = zoneDetails;
    },
    setNewAddedZones(state, zones) {
      state.newAddedZones = zones;
    },
    setZoneErrors(state, errors) {
      state.zoneErrors = errors;
    },
    resetShippingCompanyZoneDetails(state, status) {
      state.resetZoneDetails = status;
    },
    setCountriesList(state, countries) {
      state.countries = countries;
    },
    setCitiesList(state, cities) {
      state.cities = cities;
    },
    setExcludedCitiesList(state, cities) {
      state.excludedCities = cities;
    },
    setSelectedCountry(state, country) {
      state.selectedCountry = country;
    },
    setSelectedCities(state, cities) {
      state.selectedCities = cities;
    },
    setServiceType(state, serviceType) {
      state.serviceType = serviceType;
    },
  },
  actions: {
    async getShippingCompanyZones({ commit }, {companyId,serviceType}) {
      window.showLoading();
      return new Promise((resolve) => {
        let params = {};

        if (serviceType) {
          params.service_type = serviceType;
        }

        api
          .get(`company/${companyId}/zones`,{
            params: params
          })
          .then((res) => {
            commit("setShippingCompanyZones", res.data.data);
            commit("setZoneErrors", {});
            commit("setNewAddedZones", []);
            commit("setServiceType", serviceType);
            resolve(res.data.data);
            window.hideLoading();
          })
          .catch(() => {
            window.hideLoading();
          });
      });
    },
    async saveShippingCompanyZoneDetails({ commit, state }, { companyId, payload }) {
      window.showLoading();
      payload.service_type = state.serviceType;
      await api
        .post(`company/${companyId}/zones`, payload)
        .then(() => {
          window.hideLoading();
          showSwAlert("success", "تم الحفظ بنجاح", undefined, false, 3000);
          $("#company_details_modal").modal("hide");
          // commit("setShippingCompanyZoneDetails", res.data.data);
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;
          if (errorObj.fields) {
            commit("setErrors", errorObj.fields);
          }
          let errorMessage = errorObj?.message;
          errorMessage = errorMessage == 'alert.invalid_fields' ? 'يرجى التحقق من البيانات المدخلة' : (errorObj?.message ?? errorObj?.code);

          showSwAlert("error", errorMessage, undefined, false, 3000);
          window.hideLoading();
        });
    },
    async deleteZone(_, zoneId) {
      window.showLoading();
      await api
        .delete(`company/zones/${zoneId}`)
        .then(() => {
          window.hideLoading();
        })
        .catch(() => {
          window.hideLoading();
        });
    },
    async deleteZonesByCompany(_, { companyId, countryId, serviceType }) {
      let params = {};

      if (serviceType) {
        params.service_type = serviceType;
      }

      window.showLoading();
      await api
        .delete(`zones/companies/${companyId}/countries/${countryId}`,{
          params: params
        })
        .then(() => {
          window.hideLoading();
        })
        .catch(() => {
          window.hideLoading();
        });
    },
    async resetShippingCompanyZoneDetails({ commit,state }, companyId) {
      window.showLoading();
      await api
        .post(`company/${companyId}/zones/reset`,{service_type: state.serviceType})
        .then((res) => {
          commit("resetShippingCompanyZoneDetails", res.status);
          window.hideLoading();
          window.location.reload();
        })
        .catch((err) => {
          const errorObj = err?.response?.data?.error;
          showSwAlert("error", errorObj?.message, undefined, false, 3000);
          window.hideLoading();
        });
    },
    async getZonesCountriesList({ commit }, data) {
      await api.get(`/countries`, { params: { q: data.query || '' } , cancelToken: data.cancelToken || null}).then((res) => {
        let countriesList = res.data.data;
        if (data.slug === "dhl") {
          countriesList = countriesList.filter(country => country.code !== "SA");
        }
        const allCountriesObject = {
          code: "-",
          id: -1,
          name: "كل الدول",
          name_en: "All Countries",
        };
        countriesList.unshift(allCountriesObject);
        commit("setCountriesList", countriesList);
        commit("setCitiesList", []);
        commit("setExcludedCitiesList", []);
      });
    },
      async getZonesCitiesList({ commit }, data) {
      return new Promise((resolve) => {
        if (data.countryId == -1) {
          const citiesList = [
            {
              code: "-",
              id: -1,
              name: "كل المدن",
              name_en: "All Cities",
            },
          ];
          commit("setCitiesList", citiesList);
          commit("setExcludedCitiesList", []);
          resolve(citiesList);
        } else {
        //   window.showLoading();
          api.get(`countries/${data.countryId}/cities`, { params: { q: data.query || '' } , cancelToken: data.cancelToken || null})
            .then((res) => {
              const citiesList = res.data.data;
              const excludedCitiesList = [...citiesList];;
              
              const allCitiesObject = {
                code: "-",
                id: -1,
                name: "كل المدن",
                name_en: "All Cities",
              };
              citiesList.unshift(allCitiesObject);
              commit("setCitiesList", citiesList);
              commit("setExcludedCitiesList", excludedCitiesList);
              resolve(citiesList);
              window.hideLoading();
            })
            .catch(() => {
              window.hideLoading();
            });
        }
      });
    },
    async getBaseInTypeCitiesList({ commit }, data) {
      return new Promise((resolve) => {
        api.get(`zones/${data.zoneId}/cities`, { params: { q: data.query || '' } , cancelToken: data.cancelToken || null}).then((res) => {
          const citiesList = res.data.data;
          const filteredList = citiesList.filter(city => city.id !== -1);

          const excludedCitiesList = [...filteredList];
          commit("setCitiesList", citiesList);
          commit("setExcludedCitiesList", excludedCitiesList);
          resolve(citiesList);
          window.hideLoading();
        })
        .catch(() => {
          window.hideLoading();
        });
      });
    },
  },
};
