import api from '../../config/axios.config'

export default {
  state: {
    companies: [],
    companiesPage: 1,
    totalCompanies: 0,
    hasNextPage: true,
    perPage:10,
    company: {},
    delegateAppealStatus: null,
  },
  mutations: {
    setCustomCompanies(state, companies) {
      state.companies.push(...companies);
    },
    setCompaniesPage(state, companiesPage) {
      state.companiesPage = companiesPage;
    },
    setTotalCompanies(state, totalCompanies) {
      state.totalCompanies = totalCompanies;
    },
    setHasNextPage(state, hasNextPage) {
      state.hasNextPage = hasNextPage;
    },
    setDelegatesAppealStatus(state, appealStatus) {
      state.delegateAppealStatus = appealStatus;
    },
  },
  actions: {
    async getCustomCompaniesList({ commit, state }) {
      return new Promise((resolve) => {
        api.get(`/delegate?perPage=${state.perPage}&page=${state.companiesPage}`)
          .then(res => {
            commit('setCustomCompanies', res.data.data);
            commit('setTotalCompanies', res.data.pagination.total);
            commit('setHasNextPage', res.data.pagination.links && res.data.pagination.links.next);

            resolve(res.data);
          })
          .catch(err => {
            commit('setHasNextPage', false);
            if (err.response.status === 401) {
              getFreshToken(true);
            }
            
            if (err.response.status === 403) {
              commit('setDelegatesAppealStatus', err.response.data.error.code);
            }
        })
      })
    },
  }
}
