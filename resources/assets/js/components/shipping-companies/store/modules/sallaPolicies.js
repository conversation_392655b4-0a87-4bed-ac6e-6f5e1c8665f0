import api from "../../config/axios.config";

export default {
  state: {
    companies: [],
    companiesSections: [],
    companiesPage: 1,
    totalCompanies: null,
    isActive: false,
  },
  mutations: {
    setSallaPoliciesCompaniesList(state, companies) {
      state.companies = [...companies];
    },
    setSallaPoliciesSectionsList(state, companies) {
      state.companiesSections = [...companies];
    },
    setSallaPoliciesToggleIsActive(state, isActive) {
      state.isActive = isActive;
    },
  },
  actions: {
    async getSallaPoliciesCompaniesList({ commit }) {
      await api
        .get(`/salla_policy_company`)
        .then((res) => {
          commit("setSallaPoliciesCompaniesList", res.data.data.companies);
        })
        .catch((err) => {
          if (err.response.status === 401) {
            getFreshToken(true);
          }
        });
    },
    async getSallaPoliciesSectionsList({ commit }) {
      await api
        .get(`/salla_policy_sections`)
        .then((res) => {
            commit("setSallaPoliciesSectionsList", res.data.data.companies_sections);
            // set the companies list from companies sections
            commit("setSallaPoliciesCompaniesList", res.data.data.companies_sections
                .flatMap(item => item.companies)
                .reduce((acc, company) => {
                    if (!acc.some(existingCompany => existingCompany.id === company.id)) {
                        acc.push(company);
                    }
                    return acc;
                }, [])
            );
        })
        .catch((err) => {
          if (err.response.status === 401) {
            getFreshToken(true);
          }
        });
    },
    async activateSallaPolicies({ commit }) {
      await api.post(`/salla_policy_company/activate`).then((res) => {
        commit("setSallaPoliciesToggleIsActive", true);
      });
    },
    async changeCompanyStatus(_, { companyId, payload }) {
      await api.post(`/company/change-status/${companyId}`, payload).then((res) => { });
    },
  },
};