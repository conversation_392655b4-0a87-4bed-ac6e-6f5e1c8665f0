import api from "../../config/axios.config";
import { showSwAlert } from "../../utils/showSwAlert";
import { savingPaginationObject } from "../../utils/helperFunctions";

export default {
  state: {
    ticketsList: [],
    ticketListPages: {},
    ordersList: {},
    creatTicketErrors: {},
    ticketDetails: {},
    ticketDataFilter:{},
  },
  mutations: {
    setTicketsList(state, tickets) {
      state.ticketsList = tickets;
    },
    setTicketListPages(state, pages) {
      state.ticketListPages = pages;
    },
    setOrdersList(state, orders) {
      state.ordersList = orders;
    },
    setCreatTicketErrors(state, errors) {
      state.creatTicketErrors = errors;
    },
    setTicketDetails(state, ticket) {
      state.ticketDetails = ticket;
    },
    setTicketDataFilter(state, data) {
      state.ticketDataFilter = data;
    },
  },
  actions: {
    async getTicketsList({ commit }, {ticketParams}) {
        if (ticketParams.starting_after === null) {
          delete ticketParams.starting_after;
        }

      return new Promise((resolve) => {
        api
          .get( "/tickets",{params: ticketParams})
          .then((res) => {
            commit("setTicketsList", res.data?.data);
            commit("setTicketListPages", res.data.pagination);
            savingPaginationObject(res.data?.pagination?.currentPage, res.data.pagination?.next?.starting_after || null);

            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async setOrdersList({ commit }, payload) {
      return new Promise((resolve) => {
        window.showLoading();
        api
          .post("/tickets/shipment_details", payload)
          .then((res) => {
            commit("setOrdersList", res.data.data);
            resolve(res.data);
            window.hideLoading();
          })
          .catch((err) => {
            window.hideLoading();
            resolve(err.response);
          });
      });
    },
    async createTicket({ commit }, payload) {
      return new Promise((resolve) => {
        window.showLoading();
        api
          .post("/tickets", payload)
          .then((res) => {
            window.hideLoading();
            if (!res.data?.success) {
              commit("setCreatTicketErrors", res.data?.error?.fields);
            } else {
              showSwAlert("success", 'تم اضافه الشكوي بنجاح', undefined, false, 3000);
            }
            resolve(res.data);
          })
          .catch((err) => {
            window.hideLoading();
            const errorObj = err?.response?.data?.error;
            if (errorObj?.fields && Object.keys(errorObj?.fields)) {
              commit("setCreatTicketErrors", errorObj?.fields);
            }
            else {
              showSwAlert("error", errorObj?.message ?? errorObj?.code, undefined, false, 3000);
            }
            resolve(err.response);
          });
      });
    },
    async getTicketDetails({ commit }, ticketId) {
      return new Promise((resolve) => {
        api
          .get(`/tickets/${ticketId}`)
          .then((res) => {
            commit("setTicketDetails", res.data.data);
            resolve(res.data?.data);
          })
          .catch((err) => {
            const errorCode = err.response?.data?.error?.code
            const errorStatus = err.response?.data?.status
            if(errorCode === 404 || errorStatus === 404) window.location.href= '/404';
            resolve(err.response);
          });
      });
    },
    async raiseTicket(_, {ticketId, payload}) {
      return new Promise((resolve) => {
        api
          .post(`/tickets/${ticketId}/raise`, payload)
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async rateTicketExperience(_,{ ticketId, payload }) {
      return new Promise((resolve) => {
        api
          .post(`/tickets/${ticketId}/rating`, payload)
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async addReply({ commit }, { ticketId, payload }) {
      return new Promise((resolve) => {
        api
          .post(`/tickets/${ticketId}/reply`, payload)
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async getTicketDataFilter({ commit }) {
      return new Promise((resolve) => {
        api
          .get(`/complaints/filter`)
          .then((res) => {
            commit("setTicketDataFilter", res.data.data);
            resolve(res.data?.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async exportTicketsFilter(_,filters) {
      return new Promise((resolve) => {
        api
          .post(`/complaints/export`,filters)
          .then((res) => {
              window.hideLoading();
              showSwAlert("success", "سوف يتم ارسال النتائج الى بريدك الإلكتروني بعد قليل", undefined, false, 3000);
              resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
  },
};
