import api from '../../config/axios.config'

export default {
    state: {
        countries: [],
        cities: [],
    },
    mutations: {
        setCountriesList(state, countries) {
            state.countries = countries;
        },
        setCitiesList(state, cities) {
            state.cities = cities;
        },
    },
    actions: {
        async getCountriesList({ commit }, { query, cancelToken }) {
            return new Promise((resolve) => {
                api.get(`/countries`, { params: { q: query || '' }, cancelToken: cancelToken || null }).then(res => {
                    commit('setCountriesList', res.data.data);
                    resolve(res.data.data);
                });
            });
        },
        async getCountryCitiesList({ commit }, { id, query, cancelToken }) {
            await api.get(`countries/${id}/cities`, { params: { q: query || '' }, cancelToken: cancelToken || null }).then(res => {
                commit('setCitiesList', res.data.data);
            })
            .catch(() => {
                //
            })
        },
        async calculateShippingRateHandler(_, payload) {
            try {
                const response = await api.post(
                    `rate_calculator`, payload);
                if (response.data.success) {
                    return {
                        success: true,
                        data: { ...response.data.data }
                    }
                }
            } catch (error) {
                if (error) {
                    return {
                        success: false,
                        data: error.response.data
                    }
                } else {
                    return {
                        success: false,
                        errors: {}
                    }
                }
            }
        },
    }
}
