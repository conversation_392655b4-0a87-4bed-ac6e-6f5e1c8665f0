import api from '../../config/axios.config'

export default {
  state: {
    supportedPickupCities: [],
  },
  mutations: {
    setSupportedPickupCities(state, cities) {
      state.supportedPickupCities = cities;
    },
  },
  actions: {
    async getSupportedPickupCities({ commit }, companyId) {
      return new Promise((resolve) => {
        api.get(`company/${companyId}/supported_pickup_cities`)
          .then(res => {
            commit('setSupportedPickupCities', res.data.data);
            resolve(res.data);
          })
          .catch(err => {
            if (err.response.status === 401) {
              getFreshToken(true);
            }
          })
      })
    },
  }
}
