import api from "../../config/axios.config";
import { showSwAlert } from "../../utils/showSwAlert";

export default {
    state: {
        companies: [],
        isHidden: true,
        isMinimized: false,
        canSubmit: false
    },
    mutations: {
        setCompanies(state, payload) {
            state.companies = payload;
            if (state.companies?.length > 0) {
                state.isHidden = false;
            }
        },
        setIsHidden(state, payload) {
            state.isHidden = payload;
        },
        setIsMinimized(state, payload) {
            state.isMinimized = payload;
        },
        setCanSubmit(state, payload) {
            state.canSubmit = payload;
        }
    },
    actions: {
        async getCompanies({ commit }) {
            if (! window.initialData.feedback_required) {
                commit('setIsHidden', true);
                return
            }
            
            return new Promise((resolve) => {
                api
                .get( "/feedbacks")
                .then((res) => {
                    let comapnies = res.data.data?.map(company => {
                        return {
                            ...company,
                            is_satisfied: null,
                            reasons: [],
                            other_reason: null
                        }
                    });
                    commit("setCompanies", comapnies);
                    resolve(res.data?.data);
                })
                .catch((err) => {
                    resolve(err.response);
                });
            });
        },
        async sendFeedback({ commit, state }) {
            return new Promise((resolve) => {
              window.showLoading();

                let companies = state.companies.map(company => {
                    return {
                        ...company,
                        is_satisfied: company.is_satisfied === true ? 1 : 0
                    }
                })

                api
                    .post("/feedbacks", {
                        companies
                    })
                    .then((res) => {
                        window.hideLoading();
                        if (!res.data?.success) {
                            showSwAlert("error", res.data?.error?.message, undefined, false, 3000);
                        } else {
                            showSwAlert("success", 'شكرا لك على وقتك', 'تم إرسال التقييم بنجاح', false, 3000);
                            commit('setIsHidden', true);
                        }
                        resolve(res.data);
                    })
                    .catch((error) => {
                        window.hideLoading();
                        const errorMessage = Object.values(error.response?.data?.error?.fields)[0][0] ?? null;
                        showSwAlert("error", errorMessage, undefined, false, 3000);
                    
                        resolve(error.response);
                    });
            });
          },
          async setSatisfied({ state }, payload) {
            let company = state.companies.find(c => c.id === payload.id);
    
            if (!company) return
            if (company.is_satisfied === true) return
    
            company.reasons = []
            company.is_satisfied = true;
            
            const satisfiedCount = company.satisfied_count ? parseInt(company.satisfied_count) : 0;
            company.satisfied_count = satisfiedCount + 1;
        },
        setNotSatisfied({ state }, payload) {
            let company = state.companies.find(c => c.id === payload.id);
            if (!company) return
            if (company.is_satisfied === false) return
    
            company.is_satisfied = false;
            if (company.satisfied_count && parseInt(company.satisfied_count) > 0) {
                company.satisfied_count = parseInt(company.satisfied_count) - 1;
            }
        },
        setCanSubmit({ commit }, payload) {
            commit("setCanSubmit", payload);
        },
        setIsMinimized({ commit }, payload) {
            commit("setIsMinimized", payload);
        }
    },
};
