import api from "../../config/axios.config";
import { showSwAlert } from "../../utils/showSwAlert";
import { savingPaginationObject } from "../../utils/helperFunctions";

export default {
  state: {
    packagingOrdersList: [],
    packagingOrdersListPages: {},
    packagingOrderDetails: {},
    packagingShippingCompanies: [],
    packagingBranchesList: {},
    packagingMaterialsList: [],
    createPackagingOrderErrors: {},
    packagingFilter: {},
    packagingCountryCities: [],
  },
  mutations: {
    setPackagingOrdersList(state, orders) {
      state.packagingOrdersList = orders;
    },
    setPackagingOrdersListPages(state, pages) {
      state.packagingOrdersListPages = pages;
    },
    setPackagingOrderDetails(state, orderDetails) {
      state.packagingOrderDetails = orderDetails;
    },
    setPackagingShippingCompanies(state, companies) {
      state.packagingShippingCompanies = companies;
    },
    setPackagingBranchesList(state, companies) {
      state.packagingBranchesList = companies;
    },
    setPackagingMaterialsList(state, companies) {
      state.packagingMaterialsList = companies;
    },
    setCreatePackagingOrderErrors(state, errors) {
      state.createPackagingOrderErrors = errors;
    },
    setPackagingFilter(state, data) {
      state.packagingFilter = data;
    },
    setPackagingCountryCities(state, data) {
      state.packagingCountryCities = data;
    },
  },
  actions: {
    async getPackagingOrdersList({ commit }, {orderParams}) {
      if (orderParams.starting_after === null) {
        delete orderParams.starting_after;
      }
      return new Promise((resolve) => {
        api.get("/packaging_orders",{params: orderParams}).then((res) => {
            commit("setPackagingOrdersList", res.data?.data);
            commit("setPackagingOrdersListPages", res.data.pagination);
            savingPaginationObject(res.data?.pagination?.currentPage, res.data.pagination?.next?.starting_after || null);

            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async getPackagingShippingCompanies({ commit }) {
      return new Promise((resolve) => {
        api
          .get("/packaging_orders/companies")
          .then((res) => {
            commit("setPackagingShippingCompanies", res.data.data);
            resolve(res.data?.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async getPackagingBranchesList({ commit }) {
      return new Promise((resolve) => {
        api
          .get("/packaging_orders/branches")
          .then((res) => {
            commit("setPackagingBranchesList", res.data.data);
            resolve(res.data?.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async getPackagingCountryCities({ commit }, countryId) {
        return new Promise((resolve) => {
            api.get(`countries/${countryId}/cities?show=all`)
            .then((res) => {
              commit("setPackagingCountryCities", res.data.data );
              resolve(res.data.data);
            })
            .catch((err) => {
              resolve(err.response);
            });
        });
    },
    async getPackagingMaterialsList({ commit }, companyId) {
      return new Promise((resolve) => {
        api
          .get(`/packaging_orders/companies/${companyId}/materials`)
          .then((res) => {
            commit("setPackagingMaterialsList", res.data.data);
            resolve(res.data?.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async createNewPackagingOrder({ commit }, payload) {
      return new Promise((resolve) => {
        window.showLoading();
        api
          .post("packaging_orders/store", payload)
          .then((res) => {
            window.hideLoading();
            if (!res.data?.success) {
              commit("setCreatePackagingOrderErrors", res.data?.error?.fields);
            }
            resolve(res.data);
          })
          .catch((err) => {
            window.hideLoading();
            const errorObj = err?.response?.data?.error;
            if (errorObj?.fields && Object.keys(errorObj?.fields)) {
              commit("setCreatePackagingOrderErrors", errorObj?.fields);
            }
            else {
              if(errorObj.code === 5) {
                const errorFields = {};
                errorFields['quantity_limit'] = true;
                commit("setCreatePackagingOrderErrors", errorFields);
              }
              else
              showSwAlert("error", errorObj?.message ?? errorObj?.code, undefined, false, 3000);
            }
            resolve(err.response);
          });
      });
    },
    async getPackagingOrderDetails({ commit }, orderId) {
      return new Promise((resolve) => {
        api
          .get(`/packaging_orders/${orderId}`)
          .then((res) => {
            commit("setPackagingOrderDetails", res.data.data);
            resolve(res.data?.data);
          })
          .catch((err) => {
            const errorCode = err.response?.data?.error?.code
            const errorStatus = err.response?.data?.status
            if(errorCode === 404 || errorStatus === 404) window.location.href= '/404';
            resolve(err.response);
          });
      });
    },
    async raiseOrderToSalla(_,orderId) {
      return new Promise((resolve) => {
        api
          .post(`/packaging_orders/${orderId}/raise`)
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async ratePackagingOrderExperience(_,{ orderId, payload }) {
      return new Promise((resolve) => {
        api
          .post(`/packaging_orders/${orderId}/rating`, payload)
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            resolve(err.response);
          });
      });
    },
    async getPackagingFilter({ commit }) {
      return new Promise((resolve) => {
        api
            .get(`/packaging/filter`)
            .then((res) => {
              commit("setPackagingFilter", res.data.data);
              resolve(res.data?.data);
            })
            .catch((err) => {
              resolve(err.response);
            });
      });
    },
    async exportPackagingOrders(_,filters) {
      return new Promise((resolve) => {
        api
            .post(`/packaging/export`,filters)
            .then((res) => {
              window.hideLoading();
              showSwAlert("success", "سوف يتم ارسال النتائج الى بريدك الإلكتروني بعد قليل", undefined, false, 3000);
              resolve(res.data);
            })
            .catch((err) => {
              resolve(err.response);
            });
      });
    },
  },
};
