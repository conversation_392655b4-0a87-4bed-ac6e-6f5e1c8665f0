import Vue from 'vue';
import store from './store';
import App from './App.vue';
import i18n from "./plugins/vue-i18n"
import VueI18n from 'vue-i18n';
import router from './router/index';
import EnglishOnly from './utils/english-only';

const importComponent = (componentContext) => {
  componentContext.keys().forEach((fileName) => {
    const componentConfig = componentContext(fileName);
    const componentName = fileName
        .split("/")
        .pop()
        .replace(/\.\w+$/, "");
    Vue.component(componentName, componentConfig.default || componentConfig);
  });
};

// @codeCoverageIgnoreStart
// Globally register components from /component directory
const componentsContext = require.context("./components", true, /\.vue$/i);
importComponent(componentsContext);

// Globally register components from /views directory
const viewsContext = require.context("./views", true, /\.vue$/i);
importComponent(viewsContext);

// Globally register components from /tabs directory
const tabsContext = require.context("./tabs", true, /\.vue$/i);
importComponent(tabsContext);

Vue.directive('english-only', EnglishOnly);
Vue.use(VueI18n);

new Vue({
  store,
  router,
  i18n,
  render: (h) => h(App),
}).$mount("#shipping_companies")

// @codeCoverageIgnoreEnd
