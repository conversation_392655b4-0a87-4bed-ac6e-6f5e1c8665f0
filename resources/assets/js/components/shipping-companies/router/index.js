import Vue from 'vue';
import Router from 'vue-router';
import RatingPage from "../components/RatingPage.vue";
import ShippingHomeWrapper from "../views/ShippingHomeWrapper.vue";
import TicketSystem from "../views/TicketSystem.vue";
import TicketDetails from "../views/TicketDetails.vue";
import CustomCompanies from "../views/CustomCompanies.vue";
import Packaging from "../views/Packaging.vue";
import PackagingOrderDetails from "../views/PackagingOrderDetails.vue";

Vue.use(Router);

const router = new Router({
    mode: 'history',
    routes: [
        {
            path: '/shipping',
            component: ShippingHomeWrapper,
            name: 'shipping-default'
        },
        {
            path: '/shipping/tickets',
            component: TicketSystem,
            name: 'tickets'
        },
        {
            path: '/shipping/tickets/:ticketId',
            component: TicketDetails,
            name: 'ticket-details',
            props: true
        },
        {
            path: '/shipping/custom-companies',
            component: CustomCompanies,
            name: 'custom-companies'
        },
        {
            path: '/shipping/company/:companyId/rating',
            component: RatingPage,
            name: 'company-rating',
            props: true
        },
        {
            path: '/shipping/packaging_orders',
            component: Packaging,
            name: 'packaging_orders'
        },
        {
            path: '/shipping/packaging_orders/:orderId',
            component: PackagingOrderDetails,
            name: 'packaging-order-details',
            props: true
        },
    ],
    scrollBehavior() {
        return { x: 0, y: 0, behavior: 'smooth' };
    },
    linkActiveClass: 'current',
    linkExactActiveClass: 'exact-current',
});

export default router;
