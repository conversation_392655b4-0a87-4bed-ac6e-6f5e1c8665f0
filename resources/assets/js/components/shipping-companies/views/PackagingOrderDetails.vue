<template>
  <div id="packaging_order_details">
    <div v-if="isLoading" class="p-20 align-center">
      <span class="loader loader--small"></span>
    </div>
    <div v-else>
      <div>
        <h6>تفاصيل طلب رقم #{{orderId}}</h6>
        <div class="text-muted mb-20">تاريخ إرسال الطلب:{{packagingOrderDetails?.order_info?.created_at}}</div>

        <div class="panel panel-default">
          <div class="panel-body">
            <div class="rec-list justify-content-start align-items-center gap-40">
              <div v-for="(status, index) in packagingOrderDetails?.order_status_history" :key="index" class="rec-list justify-content-start align-items-center pr-20">
                <div class="progress-check">
                  <div class="check">
                    <i class="sicon-check font-17"></i>
                  </div>
                </div>
                <div class="rec-list rec-list--vertical">
                  <span class="font-10">{{ status.created_at || '-' }}</span>
                  <span class="font-14 font-weight-700">{{ status.state }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="panel panel-default">
          <div class="panel-heading sides">
            <h6 class="panel-title">معلومات الطلب</h6>
            <button v-if="packagingOrderDetails?.order_info?.can_raise" class="btn btn--outlined primary" @click="raiseToSallaClicked">
              {{"تصعيد إلى شركة سلة"}}
            </button>
            <button v-else-if="packagingOrderDetails?.order_info?.is_raised" class="btn btn--outlined primary">
              <i class="sicon-check"></i>
              {{"تم تصعيد الطلب"}}
            </button>
          </div>

          <div class="panel-body">
            <div class="row font-14 mb-20">
              <div class="col-md-12 col-lg-6 mb-20">
                <h6 class="font-16 mb-30 font-weight-700">طلب رقم #{{orderId}}</h6>
                <span class="text-nowrap my-10" :style="{ color: getColorForStatus(packagingOrderDetails?.order_info?.status) }">
                  ●&nbsp;{{packagingOrderDetails?.order_info?.status}}
                </span>
                <div class="rec-list align-items-center my-10">
                  <i class="sicon-calendar font-14"></i>
                  <span class="ml-10">تاريخ الطلب: </span>
                  <span class="ml-5">{{packagingOrderDetails?.order_info?.created_at}}</span>
                </div>
                <div class="rec-list align-items-center my-10">
                  <i class="sicon-shipping font-14"></i>
                  <span class="ml-10">شركة الشحن: </span>
                  <div class="logo-box ml-10">
                    <img :src="packagingOrderDetails?.order_info?.company.logo" class="company-logo" />
                  </div>
                  <span class="ml-10">{{packagingOrderDetails?.order_info?.company.name}}</span>
                </div>
              </div>
              <div class="col-md-12 col-lg-6">
                <div class="font-15 mb-30 font-bold">معلومات الاستلام</div>
                <div class="rec-list align-items-center my-10">
                  <i class="sicon-user-heart text-muted font-14"></i>
                  <span class="ml-10">المدينة: </span>
                  <span class="ml-10">{{packagingOrderDetails?.receiving_info?.city}}</span>
                </div>
                <div class="rec-list align-items-center my-10">
                  <i class="sicon-store text-muted font-14"></i>
                  <span class="ml-10">المستودع او الفرع: </span>
                  <span class="ml-10">{{packagingOrderDetails?.receiving_info?.branch_name}}</span>
                </div>
                <div class="rec-list align-items-center my-10">
                  <i class="sicon-phone text-muted font-14"></i>
                  <span class="ml-10">رقم التواصل: </span>
                  <span class="ml-10">{{packagingOrderDetails?.receiving_info?.phone_number}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel panel-default">
        <div class="panel-heading">
          <h6 class="panel-title">المواد المطلوبة مع الكميات</h6>
        </div>

        <div class="panel-body">
          <div v-for="(material, index) in packagingOrderDetails?.packaging_materials" :key="index" class="rec-list justify-content-between align-items-center font-14 my-20">
            <span>{{ material.name }}</span>
            <span class="text-muted">x{{ material.quantity }}</span>
          </div>
        </div>
      </div>

      <div v-if="packagingOrderDetails?.reply" class="panel panel-default">
        <div class="panel-heading">
          <h6 class="panel-title">رد شركة الشحن</h6>
        </div>

        <div class="panel-body">
          <div v-html="packagingOrderDetails.reply.body"></div>
        </div>
      </div>

      <div v-if="packagingOrderDetails?.can_rate">
        <div class="content-divider">
          <span class="rounded-label">تم إغلاق الطلب</span>
        </div>
        <div class="rec-list rec-list--vertical align-items-center my-20 py-20">
          <span class="font-15">كيف كانت تجربتك؟</span>
          <div class="rating-area theme-rating">
            <shipping-rating v-if="packagingOrderDetails?.rating > 0" :rating="packagingOrderDetails?.rating" :big-icon="true" />
            <rating-input v-else :grade="ratingValue" :max-stars="5" :has-counter="false" @onRating="onRating" />
            <div class="rec-list justify-content-between align-items-center m-5">
              <span class="font-14 text-muted">ضعيف</span>
              <span class="font-14 text-muted">رائع!</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <RatingReasonsModal :order-id="orderId" :rating-stars="rating" />
    <CreatePackagingOrderModal />
    
  </div>
</template>

<script>
  import { mapState, mapActions } from "vuex";
  import { showSwAlert } from "../utils/showSwAlert";

  const statusColors = {
    "تحت المراجعة": "#FFAF44",
    "مكتمل": "#666666",
    "جديد": "#5196F3",
    "مرفوض": "#F55157",
  };

  export default {
    props: {
      orderId: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        isLoading: true,
        rating: 0,
      };
    },
    computed: {
      ...mapState({
        packagingOrderDetails: (state) => state.packaging.packagingOrderDetails,
      }),
      ratingValue() {
        return this.packagingOrderDetails?.order_info?.rating || 0;
        },
        canCreatePackage() {
            return window.initialData.permissions.is_shipping_packaging_system_enabled;
        },
    },
    created() {
        this.fetchPackagingOrderDetails();

        if (this.canCreatePackage) {
            window.parent?.postMessage({
                event: 'nav.primary-action',
                title: "طلب جديد",
                url: "#"
            }, '*');
        }
    },
    mounted() {
      window.addEventListener('message', ({data: {event}}) => {
        if (event !== 'nav.primary-action.clicked') {
            return;
        }

        $('#new_packaging_order_modal').modal();
      });
    },
    methods: {
      ...mapActions(["getPackagingOrderDetails", "raiseOrderToSalla", "ratePackagingOrderExperience"]),

      getColorForStatus(status) {
        return statusColors[status];
      },
      fetchPackagingOrderDetails() {
        this.isLoading = true;
        this.getPackagingOrderDetails(this.orderId)
          .then((response) => {
            const errorCode = response?.data?.error?.code;
            const errorStatus = response?.data?.status;
            if (errorCode !== 404 || errorStatus !== 404) this.isLoading = false;
          })
          .catch(() => {
            this.isLoading = false;
          });
      },

      async raiseToSallaClicked() {
        if (!this.packagingOrderDetails?.order_info?.is_raised) {
          try {
            const confirmed = await this.showConfirmationDialog();
            if (confirmed) {
              window.showLoading();
              const res = await this.raiseOrderToSalla(this.orderId);
              window.hideLoading();

              if (res.success) {
                showSwAlert("success", "تم تصعيد الطلب بنجاح", undefined, false, 3000);
                window.location.reload();
              } else {
                showSwAlert("error", "حدث خطأ , برجاء المحاوله في وقت لاحق", undefined, false, 3000);
              }
            }
          } catch (error) {
            window.hideLoading();
            const errorMessage = error?.response?.data?.error?.message || "An error occurred";
            showSwAlert("error", errorMessage, undefined, false, 3000);
          }
        }
      },
      showConfirmationDialog() {
        return new Promise((resolve) => {
          window
            .swal({
              title: "تأكيد الطلب إلى شركة سلة",
              text: "سيتم إرسال تفاصيل الطلب إلى فريق سلة للتحقُّق من معالجتها والرد عليك في أقرب وقت.",
              type: "warning",
              showCancelButton: true,
              confirmButtonColor: "#F44336",
              confirmButtonText: "تصعيد الطلب",
              cancelButtonText: "إلغاء",
            })
            .then((result) => {
              if (result) {
                resolve(true);
              } else {
                resolve(false);
              }
            });
        });
      },

      onRating(stars) {
        this.rating = stars;
        if (stars <= 3) {
          $("#rating_reasons_modal").modal("show");
        } else {
          const payload = {
            intercom_user_id: this.packagingOrderDetails?.intercom_user_id,
            rating: this.rating,
          };
          window.showLoading();

          this.ratePackagingOrderExperience({ orderId: this.orderId, payload }).then((res) => {
            window.hideLoading();
            if (res.success) {
              showSwAlert("success", "تم التقييم بنجاح", undefined, false, 3000);
              setTimeout(() => {
                window.location.reload();
              }, 3000);
            } else {
              showSwAlert("error", "حدث خطأ , برجاء المحاوله في وقت لاحق", undefined, false, 3000);
            }
          });
        }
      },
    },
  };
</script>
