<template>
  <div id="complaint_section">
    <div v-if="pageLoading" class="p-20 align-center">
      <span class="loader loader--small"></span>
    </div>
    <div v-else>
      <a v-if="isLegacy" id="create_ticket_btn" class="btn btn-tiffany primary mb-20" href="#" data-toggle="modal" data-target="#creeat_ticket_modal">
        <i class="sicon-add"></i>
        شكوى جديدة
      </a>

      <div v-show="!pageLoading" class="panel panel-default">
        <div class="panel-heading sides">
          <h6 class="panel-title">
            <i class="sicon-chat-bubbles-alt"></i>&nbsp; شكاوى الشحن
            <small class="text-muted">({{totalCounts}})</small>
          </h6>
          <div>
            <button id="rec_filter_toggle" type="button" class="btn btn--outlined primary btn-xs" aria-expanded="true" @click="showSideBar">
              <i class="flip-x sicon-filter"> </i>
              تصفية
            </button>
          </div>
        </div>
        <div>
          <div class="form-group mb-0">
            <div class="input-group">
              <span class="input-group-addon no-border border-bottom"> <i class="sicon-search"></i></span>
              <input v-model="searching" type="text" class="form-control no-border border-bottom" placeholder="ابحث برقم الشكوى أو رقم بوليصة الشحن..." @change="onSearch" />
            </div>
          </div>
          <vue-good-table
            ref="my-table"
            :columns="columns"
            :rows="ticketsList"
            mode="remote"
            :rtl="true"
            style-class="vgt-table"
            :is-loading.sync="isLoading"
            :search-options="{
            enabled: true,
            externalQuery: searching,
          }"
            :pagination-options="{
             enabled: true,
             lastLabel : '',
             nextLabel: 'التالي',
             prevLabel: 'السابق',
             ofLabel: 'من',
             dropdownAllowAll: false,
             rowsPerPageLabel: 'عدد الصفوف في الصفحه الواحدة',
             perPage:perPage
         }"
          >
            <template slot="table-row" slot-scope="props">
              <div v-if="props.column.field==='complaint_number'" class="d-flex align-items-baseline gap-10">
                <a href="#" class="text-primary" @click.prevent="navigateTo(`/shipping/tickets/${props.row.id}`)">
                  #{{ props.row.id }}
                </a>
                <small v-show="props.row.created_by === 'company'" class="badge badge--grey">مستلمة من شركة الشحن</small>
                <small v-show="props.row.unread_count > 0" class="badge badge--danger">{{props.row.unread_count}}</small>
              </div>
              <div v-if="props.column.field==='complaint_type'">
                <span>{{props.row.type}}</span>
              </div>
              <div v-if="props.column.field==='shipping_company'" class="rec-list align-items-center">
                <div class="logo-box">
                  <img :src="props.row.company?.logo" class="company-logo" />
                </div>
                <span class="ml-10">{{props.row.company?.name}}</span>
              </div>
              <div v-if="props.column.field==='complaint_date'">
                <span>{{props.row.created_at}}</span>
              </div>
              <p v-if="props.column.field==='complaint_status'">
                <span class="text-muted text-nowrap" :style="{ color: getColorForStatus(props.row.status) }">
                  ●&nbsp;{{props.row.status}}
                </span>
              </p>
            </template>
            <template slot="pagination-bottom" slot-scope="props">
              <custom-pagination
                :total="ticketListPages.totalPages"
                :current_page="currentPage"
                :per_page="perPage"
                :has-more-pages="ticketListPages?.totalPages > currentPage"
                :page-changed="props.pageChanged"
                :per-page-changed="props.perPageChanged"
                @onChangePage="onChangePage"
                @onChangePerPage="onPerPageChange"
              >
              </custom-pagination>
            </template>
            <div slot="loadingContent" class="loader"></div>
            <div slot="emptystate">
              <empty-placeholder-table
                  icon="sicon-chat-bubbles-alt"
                  header="لا توجد أي شكوى"
                  description="عالج تحديات الشحن التي تواجهك عبر إرسال شكوى لشركة الشحن المعنية."
              />
            </div>
          </vue-good-table>
          <side-bar-filter
            :is_disabled="! ticketsList.length"
            @addFilter="addFilter"
            @resetFilter="resetFilter"
            @exportFilter="exportFilter"
          />
        </div>
      </div>
    </div>
    <CreateTicketModal />
  </div>
</template>

<script>
  import { VueGoodTable } from "vue-good-table";
  import { mapState, mapActions } from "vuex";
  import SideBarFilter from "../components/TicketSystem/SideBarFilter";
  import { getStartingAfterForPage } from '../utils/helperFunctions'

  export default {
    components: {
      VueGoodTable,
      SideBarFilter
    },
    data() {
      return {
        isLegacy: window.sallaLegacy,
        isLoading: false,
        currentPage: 1,
        pageLoading: false,
        columns: [
          {
            label: "رقم الشكوى",
            field: "complaint_number",
            sortable: false,
          },
          {
            label: "نوع الشكوى",
            field: "complaint_type",
            sortable: false,
          },
          {
            label: "شركة الشحن",
            field: "shipping_company",
            sortable: false,
          },
          {
            label: "تاريخ الشكوى",
            field: "complaint_date",
            sortable: false,
          },
          {
            label: "حالة الشكوى",
            field: "complaint_status",
            sortable: false,
          },
        ],
        statusColors: {
          "تمت المعالجة": "#666666",
          "بانتظار الرد من العميل": "#797df5",
          "معلقة": "#F55157",
          "مصعدة إلى شركة سلة": "#5196F3",
          "جديدة": "#76E8CD",
          "جاري المعالجة": "#FFAF44",
        },
        searching: "",
        pagination: {},
        serverParams: {
          columnFilters: {},
        },
        page: 1,
        perPage: 10,
        totalRecords: 0,
        filter: {
          state : "",
          companies : [],
          from_date: '',
          to_date: ''
        },
      };
    },
    computed: {
      ...mapState({
        ticketsList: (state) => state.ticketSystem.ticketsList,
        ticketListPages: (state) => state.ticketSystem.ticketListPages,
      }),
      totalCounts() {
        return this.ticketListPages?.total;
        },
        canCreateTicket() {
            return window.initialData.permissions.is_shipping_ticket_feature_enabled;
        },
    },
    created() {
      this.canCreateTicket && window.parent?.postMessage({
        event: 'nav.primary-action',
        title: "شكوى جديدة",
        url: "#"
      }, '*');
    },
    mounted() {
      this.getTicketsData();
      this.fetchDataFilter();
      window.addEventListener('message', ({data: {event}}) => {
        if (event !== 'nav.primary-action.clicked') {
            return;
        }
        
        $('#creeat_ticket_modal').modal();
      });
    },
    methods: {
      ...mapActions(["getTicketsList","exportTicketsFilter","getTicketDataFilter"]),
      getTicketsData() {
        this.pageLoading = true;
        this.isLoading = true;
        this.fetchTicketsList(true);
      },
      getColorForStatus(status) {
        return this.statusColors[status];
      },
      fetchTicketsList(firstLoad) {
        this.isLoading = true;
        let ticketParams = {
          per_page: this.perPage ?? 10,
          starting_after : getStartingAfterForPage(this.currentPage ?? 1),
          state: this.filter?.state,
          companies: this.filter?.companies,
          from_date: this.filter.from_date ?? null,
          to_date: this.filter.to_date ?? null,
          created_by: this.filter.created_by ?? null,
          id: this.searching ?? null,
        };
        this.getTicketsList({ticketParams})
          .then((res) => {
            this.isLoading = false;
            this.pageLoading = false;
            this.currentPage = res?.pagination?.currentPage;
            this.checkPopupState();
          })
          .catch(() => {
            this.isLoading = false;
          }) .finally(() => {
            this.isLoading = false;
            if (firstLoad) this.pageLoading = false;
        });
      },
      exportTickets() {
        let exportFilters = {
          per_page: this.totalCounts ?? this.perPage ?? 10,
          starting_after : getStartingAfterForPage(this.currentPage ?? 1),
          state: this.filter?.state,
          companies: this.filter?.companies,
          from_date: this.filter.from_date ?? null,
          to_date: this.filter.to_date ?? null,
          created_by: this.filter.created_by ?? null,
          service_type: 'complaints'
        };
        this.exportTicketsFilter(exportFilters);
      },
      onSearch() {
        this.currentPage = 1;
        this.fetchTicketsList();
      },
      onChangePage(params) {
        this.currentPage = params;
        this.fetchTicketsList();
      },
      onPerPageChange(params) {
        this.perPage = params.perPage;
        this.fetchTicketsList();
      },
      checkPopupState() {
        this.$nextTick(() => {
          const urlParams = new URLSearchParams(window.location.search);
          if (urlParams.has("popup") && urlParams.get("popup") === "open") {
            this.removeQueryParam("popup");
            const btn = document.getElementById("create_ticket_btn");
            btn && btn.click();
          }
        });
      },
      removeQueryParam(paramKey) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.delete(paramKey);

        const baseUrl = window.location.origin + window.location.pathname;
        const newUrl = baseUrl + (urlParams.toString() ? "?" + urlParams.toString() : "");
        window.history.replaceState({}, document.title, newUrl);
      },
      showSideBar() {
        document.querySelector("#rec_filter_toggle").classList.add("active");
        document.querySelector(".rec-filter-wrapper").classList.add("reveal");
      },
      async fetchDataFilter() {
        await this.getTicketDataFilter();
      },
      addFilter(dataFilter) {
        this.currentPage = 1;
        this.filter = {
          ...dataFilter,
        };
        this.fetchTicketsList();
      },
      resetFilter(dataFilter) {
        this.currentPage = 1;
        this.filter = {
          ...dataFilter,
        };
        this.fetchTicketsList();
      },
      exportFilter(dataFilter) {
        this.currentPage = 1;
        this.filter = {
          ...dataFilter,
        };
        this.exportTickets();
        },
        navigateTo(path) {
            if (this.isLegacy) {
                this.$router.push(path);
            } else {
                window.location.href = `${path}?mode=iframe`;
            }
        }
    },
  };
</script>
