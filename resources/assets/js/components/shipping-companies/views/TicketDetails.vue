<template>
  <div id="ticket_details">
    <div v-if="isLoading" class="p-20 align-center">
      <span class="loader loader--small"></span>
    </div>
    <div v-else>
      <h6>تفاصيل شكوى رقم #{{ticketId}}</h6>
      <div class="text-muted mb-20">تاريخ إرسال الشكوى:{{ticketDetails?.created_at}}</div>

      <div class="row">
        <div class="col-md-12 col-lg-9 dashboard-right-col">
          <div v-if="ticketDetails?.description" class="bg padded-block mb-20">
            <div>
              {{ticketDetails?.description}}
            </div>
          </div>
          <div class="panel panel-default">
            <div class="panel-heading">
              <h6 class="panel-title"><i class="sicon-chat-bubbles-alt"></i>&nbsp; سجل المحادثة</h6>
            </div>
            <div class="panel-body p-0">
              <div class="px-20">
                <div v-for="(comment,index) in ticketDetails?.comments" :key="index" class="media mb-10">
                  <div class="media-left">
                    <img :src="comment.author.avatar ||`cp/assets/images/avatar_male.png`" class="img-circle" alt="" />
                  </div>
                  <div class="media-body">
                    <div class="rec-list justify-content-between align-items-center">
                      <span>{{comment.author.name}}</span>
                      <span>{{comment.created_at}}</span>
                    </div>
                    <div class="bg padded-block mt-10">
                      <div class="rec-list">
                        <i class="sicon-chat-message-alt mr-10"></i>
                        <div v-html="comment.body"></div>
                      </div>
                      <a v-if="(comment.attachment_urls).length" :href="comment?.attachment_urls[0]" class="rec-list" target="_blanck" download>
                        <div class="d-flex justify-content-center align-items-center rounded mr-5" style="width: 30px; height: 30px; background-color: #dddddd;">
                          <i class="sicon-file-archive"></i>
                        </div>
                        تحميل الملف
                        <i class="sicon-download ml-5"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="ticketDetails?.can_rate">
                <div class="content-divider">
                  <span class="bg-gray p-10 px-20">تم إغلاق الشكوى</span>
                </div>
                <div class="rec-list rec-list--vertical align-items-center my-20 py-20">
                  <span class="font-15">كيف كانت تجربتك؟</span>
                  <div class="rating-area theme-rating">
                    <shipping-rating v-if="ticketDetails?.rating > 0 || rating > 0" :rating="ticketDetails?.rating || rating" :big-icon="true" />
                    <rating-input v-else :grade="ratingValue" :max-stars="5" :has-counter="false" @onRating="onRating" />
                  </div>
                </div>
              </div>

              <div v-else class="bg padded-block mt-20">
                <div :class="{'form-group': true,'has-error': replyTextError}">
                  <h6 class="font-15">
                    إضافة رد
                  </h6>
                  <textarea :value="replyText" name="reply" rows="3" class="form-control" placeholder="أضف ردك..." @input="handleReplyText"></textarea>
                  <FormError v-if="replyTextError">حقل إضافة رد مطلوب</FormError>

                  <div v-if="enableUploadImg" class="mt-10">
                    <filepond 
                      v-model="attachmentImg"
                      :crop-aspect-ratio="'null'" 
                      label="إرفاق صورة" 
                      pond-ref="owner" 
                      :finished-upload="attachmentImg !== null" 
                      :img-src="attachmentImg" 
                      :uploading="attachmentImg == null" 
                    />
                  </div>
                  <div class="mt-20 align-left">
                    <button class="btn btn--outlined primary" @click="handleUploadImg">
                      <i class="sicon-attachment"></i>
                      إرفاق صورة
                    </button>
                    <button class="btn btn-tiffany" @click="addReplyclicked">
                      إضافة رد
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-12 col-lg-3 dashboard-left-col">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h6 class="panel-title">معلومات الشكوى</h6>
            </div>
            <div class="panel-body">
              <h6>شكوى رقم #{{ticketId}}</h6>
              <div class="rec-list justify-content-between align-items-center mb-20">
                <span class="text-muted text-nowrap" :style="{ color: getColorForTicketStatus(ticketDetails?.status) }">
                  ●&nbsp;{{ticketDetails?.status}}
                </span>
                <template v-if="ticketDetails?.can_raise || ticketDetails?.is_raised">
                  <a v-if="!ticketDetails?.is_raised" class="btn btn--outlined primary" href="#" data-toggle="modal" data-target="#raise_ticket_modal">
                    {{"تصعيد إلى شركة سلة"}}
                  </a>
                  <a v-else class="btn btn--outlined primary" href="#" @click.prevent="void(0)">
                    <i class="sicon-check"></i>
                    {{"تم تصعيد الشكوى"}}
                  </a>
                </template>
              </div>
              <div class="my-5">
                <i class="sicon-page-content"></i>
                <span>نوع الشكوى: </span>
                <span class="ml-5">{{ticketDetails?.type}}</span>
              </div>
              <div class="my-5">
                <i class="sicon-calendar"></i>
                <span>تاريخ الشكوى: </span>
                <span class="ml-5">{{ticketDetails?.created_at}}</span>
              </div>

              <div class="font-15 mb-10 mt-20">معلومات الطلب</div>
              <div class="rec-list align-items-center my-5">
                <i class="sicon-shipping text-muted"></i>
                <span class="text-muted ml-10">رقم البوليصة: </span>
                <span class="ml-10">{{ticketDetails?.shipment_details?.shipping_number || "-"}}</span>
              </div>
              <div class="rec-list align-items-center my-5">
                <i class="sicon-shipping text-muted"></i>
                <span class="text-muted ml-10">شركة الشحن: </span>
                <div class="logo-box ml-10">
                  <img :src="ticketDetails?.shipment_details?.company.logo" class="company-logo" />
                </div>
                <span class="ml-10">{{ticketDetails?.shipment_details?.company.name}}</span>
              </div>
              <div class="rec-list align-items-center my-5">
                <i class="sicon-shipping text-muted"></i>
                <span class="text-muted ml-10">رقم الطلب: </span>
                <span class="ml-10">{{ticketDetails?.shipment_details?.order_id || "-"}}</span>
              </div>
              <div class="rec-list align-items-center my-5">
                <i class="sicon-shipping text-muted"></i>
                <span class="text-muted ml-10">رقم الفاتورة: </span>
                <span class="ml-10">{{ticketDetails?.shipment_details?.invoice_id || "-"}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="ticketDetails?.can_raise && !ticketDetails?.is_raised">
        <RaiseTicketModal :ticket-id="ticketId" @ticketRaised="handleTicketRaised"/>
      </template>
    </div>
    <CreateTicketModal />
  </div>
</template>

<script>
  import { mapState, mapActions } from "vuex";
  import { showSwAlert } from "../utils/showSwAlert";

  export default {
    props: {
      ticketId: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        isLoading: false,
        rating: 0,
        replyText: "",
        attachmentImg: null,
        enableUploadImg: false,
        replyTextError: false,
      };
    },
    computed: {
      ...mapState({
        ticketDetails: (state) => state.ticketSystem.ticketDetails,
      }),
      ratingValue() {
        return this.ticketDetails?.rating || 0;
        },
        canCreateTicket() {
            return window.initialData.permissions.is_shipping_ticket_feature_enabled;
        },
    },
    created() {
      this.isLoading = true;
      this.fetchReplies(); 
      this.intervalId = setInterval(this.fetchReplies, 30000); 

      this.canCreateTicket && window.parent?.postMessage({
            event: 'nav.primary-action',
            title: "شكوى جديدة",
            url: "#"
      }, '*');
    },
    mounted() {
      window.addEventListener('message', ({data: {event}}) => {
        if (event !== 'nav.primary-action.clicked') {
            return;
        }

        $('#creeat_ticket_modal').modal();
      });
    },
    beforeDestroy() {
      clearInterval(this.intervalId);
    },
    methods: {
      ...mapActions(["getTicketDetails", "rateTicketExperience", "addReply"]),
      handleTicketRaised() {
        this.ticketDetails.is_raised = true;
        this.ticketDetails.can_raise = false;
        this.ticketDetails.can_rate  = false;
      },
      fetchReplies() {
      this.getTicketDetails(this.ticketId)
        .then((response) => {
          const errorCode = response?.data?.error?.code
          const errorStatus = response?.data?.status
          if(errorCode !== 404 || errorStatus !== 404) this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },
      getColorForTicketStatus(status) {
        const statusColors = {
          "تمت المعالجة": "#666666",
          "بانتظار الرد من العميل": "#797df5",
          "معلقة": "#F55157",
          "مصعدة إلى شركة سلة": "#5196F3",
          "جديدة": "#76E8CD",
          "جاري المعالجة": "#FFAF44",
        };
        return statusColors[status];
      },
      handleReplyText(event) {
        this.replyTextError = false;
        this.replyText = event.target.value;
      },
      addReplyclicked() {
        if (this.replyText.trim().length === 0) {
          this.replyTextError = true;
        } else {
          const payload = {
            intercom_user_id: this.ticketDetails?.intercom_user_id,
            body: this.replyText,
          };
          if (this.attachmentImg) payload["attachment_urls"] = [this.attachmentImg];

          window.showLoading();
          this.addReply({ ticketId: this.ticketId, payload }).then((res) => {
            window.hideLoading();
            if (res.success) {
              this.replyText = "";
              this.enableUploadImg = false;
              this.attachmentImg = null;
              showSwAlert("success", "تم اضافه الرد بنجاح", undefined, false, 3000);

              const newComment = res?.data;
              const updatedComments = [...this.ticketDetails.comments, newComment];

              const updatedTicketDetails = {
                ...this.ticketDetails,
                comments: updatedComments,
              };

              this.$root.$store.commit("setTicketDetails", updatedTicketDetails);
            } else {
              const errorTxt = res?.data?.error?.fields?.body[0];
              let message =  errorTxt ?? "حدث خطأ , برجاء المحاوله في وقت لاحق";
              showSwAlert("error",message , undefined, false, 3000);
            }
          });
        }
      },
      handleUploadImg() {
        this.enableUploadImg = true;
      },
      onRating(stars) {
        this.rating = stars;
        const payload = {
          rating: stars,
        };
        window.showLoading();

        this.rateTicketExperience({ ticketId: this.ticketId, payload }).then((res) => {
          window.hideLoading();
          if (res.success) {
            showSwAlert("success", "تم التقييم بنجاح", undefined, false, 3000);
          } else {
            showSwAlert("error", "حدث خطأ , برجاء المحاوله في وقت لاحق", undefined, false, 3000);
          }
        });
      },
    },
  };
</script>