<template>
  <div>
    <section v-if="! hasDefaultBranch" class="alert-box alert-box--warning">
      <i class="sicon-warning"></i>
      <article>
        <h6 class="mt-0 font-16 mb-5">
          يرجى إضافة عنوان للمتجر لتتمكن من تفعيل بوليصات سلة، من الاعدادات الاساسية اختر اضافة عنوان مقر
          <a href="/settings/component/basic">انقر هنا </a>
        </h6>
      </article>
    </section>
    <div v-if="! hideLoader" class="p-20 align-center">
      <span class="loader loader--small"></span>
    </div>
    <div v-else>
      <tier-section v-if="showStoreTierStatistics" />
      <!-- <commerial-video-section v-if="showTierInfoVideo"/> -->
      <salla-policies />
      <tickets-wrapper v-if="canManageTickets && isTicketSystemFeatureEnabled" />
      <packaging-wrapper v-if="canManagePackaging && isPackagingFeatureEnabled"/>
      <custom-companies-wrapper />
      <shipping-settings />

      <feedback v-if="feedbackRequired" />
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions } from "vuex";
  import TierSection from "../components/Tier/TierSection";
  // import CommerialVideoSection from "../components/Tier/CommerialVideoSection";
  import SallaPolicies from "../components/SallaPolicies";
  import CustomCompaniesWrapper from "../components/CustomCompanies/CustomCompaniesWrapper";
  import ShippingSettings from "../components/ShippingSettings";
  import TicketsWrapper from "../components/TicketSystem/TicketsWrapper";
  import PackagingWrapper from "../components/Packaging/PackagingWrapper";
  import Feedback from "../components/Feedback";
  
  export default {
    components: {
      "tier-section": TierSection,
      // "commerial-video-section": CommerialVideoSection,
      "salla-policies": SallaPolicies,
      "tickets-wrapper": TicketsWrapper,
      "packaging-wrapper": PackagingWrapper,
      "custom-companies-wrapper": CustomCompaniesWrapper,
      "shipping-settings": ShippingSettings,
      "feedback": Feedback
    },
    data() {
      return {};
    },
    computed: {
      ...mapState({
        sallaPoliciesCompanies: (state) => state.sallaPolicies.companies,
        sallaPoliciesSections: (state) => state.sallaPolicies.companiesSections,
      }),
      hasDefaultBranch() {
        return window.initialData.has_default_branch;
      },
      isSallaPoliciesActive() {
        return window.initialData.is_salla_policies_active;
      },
      hideLoader() {
        const sallaPolicyData = (this.isSallaPoliciesActive && this.sallaPoliciesSections.length) || (!this.isSallaPoliciesActive && this.sallaPoliciesCompanies.length);
        return sallaPolicyData;
      },
      isTiersFeatureEnabled() {
        return window.initialData.permissions.is_shipping_tiers_feature_enabled;
      },
      showTierInfoVideo() {
        return this.isTiersFeatureEnabled && !window.initialData.has_at_least_one_policy_shipment;
      },
      showStoreTierStatistics() {
        return this.isTiersFeatureEnabled;
        },
        canManageTickets() {
          return window.initialData.permissions.management
        },
      isTicketSystemFeatureEnabled() {
        return window.initialData.permissions.is_shipping_ticket_feature_enabled;
        },
        canManagePackaging() {
          return window.initialData.permissions.management
        },
      isPackagingFeatureEnabled() {
        return window.initialData.permissions.is_shipping_packaging_system_enabled;
      },
      feedbackRequired() {
        return window.initialData.feedback_required;
      }
    },
    created() {
      if (this.showStoreTierStatistics) {
        this.getTierPricingList();
      }
      if (this.isSallaPoliciesActive) {
        this.getSallaPoliciesSectionsList();
      } else {
        this.getSallaPoliciesCompaniesList();
      }
    },
    methods: {
      ...mapActions(["getSallaPoliciesSectionsList", "getSallaPoliciesCompaniesList", "getTierPricingList"]),
    },
  };
</script>
