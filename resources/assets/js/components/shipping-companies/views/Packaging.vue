<template>
  <div id="packaging_orders_section">
    <div v-if="pageLoading" class="p-20 align-center">
      <span class="loader loader--small"></span>
    </div>
    <div v-else>
      <a v-if="isLegacy" id="new_packaging_order_btn" :disabled="!canCreatePackage" class="btn btn-tiffany primary mb-20" href="#" data-toggle="modal" data-target="#new_packaging_order_modal">
        <i class="sicon-add"></i>
        طلب جديد
      </a>
      <div v-if="!canCreatePackage" class="alert-box alert-box--info">
        <i class="sicon-info"></i>
        <article>
          <h6 class="mb-5">لا يمكن رفع طلب جديد.</h6>
          <p>عزيزي التاجر يمكنك رفع طلب جديد لمواد التغليف عند تفعيلك احدى بوليصات سلة.</p>
        </article>
       </div>
      <div v-show="!pageLoading" class="panel panel-default">
        <div class="panel-heading sides">
          <h6 class="panel-title"><i class="sicon-packed-box"></i>طلبات مواد التغليف</h6>
          <div>
            <button id="rec_filter_toggle" type="button" class="btn btn--outlined primary btn-xs" aria-expanded="true" @click="showSideBar">
              <i class="flip-x sicon-filter"> </i>
              تصفية
            </button>
          </div>
        </div>
        <div>
          <div class="form-group mb-0">
            <div class="input-group">
              <span class="input-group-addon no-border border-bottom"> <i class="sicon-search"></i></span>
              <input v-model="searching" type="text" class="form-control no-border border-bottom" placeholder="ابحث برقم الطلب ..." @change="onSearch" />
            </div>
          </div>
          <vue-good-table
            ref="my-table"
            :columns="columns"
            :rows="packagingOrdersList"
            mode="remote"
            :rtl="true"
            style-class="vgt-table"
            :is-loading.sync="isLoading"
            :search-options="{
            enabled: true,
            externalQuery: searching,
          }"
            :pagination-options="{
             enabled: true,
             lastLabel : '',
             nextLabel: 'التالي',
             prevLabel: 'السابق',
             ofLabel: 'من',
             dropdownAllowAll: false,
             rowsPerPageLabel: 'عدد الصفوف في الصفحه الواحدة',
             perPage:perPage
         }"
          >
            <template slot="table-row" slot-scope="props">
              <div v-if="props.column.field==='order_number'" class="d-flex align-items-baseline gap-10">
                <a href="#" class="text-primary" @click.prevent="navigateTo(`/shipping/packaging_orders/${props.row.id}`)">
                  #{{ props.row.id }}
                </a>
              </div>
              <div v-if="props.column.field==='shipping_company'" class="rec-list align-items-center">
                <div class="logo-box">
                  <img :src="props.row.company?.logo" class="company-logo" />
                </div>
                <span class="ml-10">{{props.row.company?.name}}</span>
              </div>
              <div v-if="props.column.field==='order_date'">
                <span>{{props.row.created_at}}</span>
              </div>
              <p v-if="props.column.field==='order_status'">
                <span class="text-muted text-nowrap" :style="{ color: getColorForStatus(props.row.status) }">
                  ●&nbsp;{{props.row.status}}
                </span>
              </p>
            </template>
            <template slot="pagination-bottom" slot-scope="props">
              <custom-pagination
                :total="packagingOrdersListPages.totalPages"
                :current_page="currentPage"
                :per_page="perPage"
                :has-more-pages="packagingOrdersListPages?.totalPages > currentPage"
                :page-changed="props.pageChanged"
                :per-page-changed="props.perPageChanged"
                @onChangePage="onChangePage"
                @onChangePerPage="onPerPageChange"
              >
              </custom-pagination>
            </template>
            <div slot="loadingContent" class="loader"></div>
            <div slot="emptystate">
              <empty-placeholder-table />
<!--              v-if="! packagingOrdersList.length"-->
              <empty-placeholder-table
                  icon="sicon-packed-box"
                  header="لا توجد لديك طلبات"
                  description="اطلب مواد التغليف المناسبة لشحنات متجرك بكل سهولة و وسع تجارتك."
              />
            </div>
          </vue-good-table>
          <side-bar-filter
              :is_disabled="! packagingOrdersList.length"
              :is_packaging="true"
              @addFilter="addFilter"
              @resetFilter="resetFilter"
              @exportFilter="exportFilter"
          />
        </div>
      </div>
      <CreatePackagingOrderModal/>
    </div>
  </div>
</template>

<script>
  import { VueGoodTable } from "vue-good-table";
  import { mapState, mapActions } from "vuex";
  import SideBarFilter from "../components/TicketSystem/SideBarFilter.vue";
  import {getStartingAfterForPage} from "../utils/helperFunctions";

  const statusColors = {
    "تحت المراجعة": "#FFAF44",
    "مكتمل": "#666666",
    "جديد": "#5196F3",
    "مرفوض": "#F55157",
  };

  export default {
    name: 'PackagingPage',
    components: {
      VueGoodTable,
      SideBarFilter
    },
    data() {
      return {
        isLegacy: window.sallaLegacy,
        isLoading: false,
        currentPage: 1,
        pageLoading: false,
        columns: [
          { label: "رقم الطلب", field: "order_number", sortable: false },
          { label: "شركة الشحن", field: "shipping_company", sortable: false },
          { label: "تاريخ الطلب", field: "order_date", sortable: false },
          { label: "حالة الطلب", field: "order_status", sortable: false },
        ],
        searching: "",
        pagination: {},
        serverParams: {
          columnFilters: {},
        },
        page: 1,
        perPage: 10,
        totalRecords: 0,
        filter: {
          state : "",
          companies : [],
          from_date: '',
          to_date: ''
        },
      };
    },
    computed: {
      ...mapState({
        packagingOrdersList: (state) => state.packaging.packagingOrdersList,
        packagingOrdersListPages: (state) => state.packaging.packagingOrdersListPages,
      }),
      canCreatePackage() {
        return window.initialData.permissions.is_shipping_packaging_system_enabled;
      }
    },
    created() {
      this.fetchPackagingOrdersList();

      if (this.canCreatePackage) {
        window.parent?.postMessage({
          event: 'nav.primary-action',
          title: "طلب جديد",
          url: "#"
        }, '*');
      }
    },
    mounted() {
      this.getOrdersData();
      window.addEventListener('message', ({data: {event}}) => {
        if (event !== 'nav.primary-action.clicked') {
          return
        }

        $('#new_packaging_order_modal').modal();
      });
    },
    methods: {
      ...mapActions(["getPackagingOrdersList","exportPackagingOrders","getPackagingFilter"]),
      getOrdersData() {
        this.pageLoading = true;
        this.isLoading = true;
        this.fetchPackagingOrdersList(true);
      },
      getColorForStatus(status) {
        return statusColors[status];
      },
      fetchPackagingOrdersList(firstLoad) {
        this.isLoading = true;
        let orderParams = {
          per_page: this.perPage ?? 10,
          starting_after : getStartingAfterForPage(this.currentPage ?? 1),
          state: this.filter?.state,
          companies: this.filter?.companies,
          from_date: this.filter.from_date ?? null,
          to_date: this.filter.to_date ?? null,
          id: this.searching ?? null,
        };
        this.getPackagingOrdersList({ orderParams })
          .then((res) => {
            this.isLoading = false;
            this.pageLoading = false;
            this.currentPage = res?.pagination?.currentPage;
          })
          .catch(() => {
            this.isLoading = false;
          }).finally(() => {
            this.isLoading = false;
            if (firstLoad) this.pageLoading = false;
          });
      },
      onChangePage(params) {
        this.currentPage = params;
        this.fetchPackagingOrdersList();
      },
      onPerPageChange(params) {
        this.perPage = params.perPage;
        this.fetchPackagingOrdersList();
      },
      showSideBar() {
        document.querySelector("#rec_filter_toggle").classList.add("active");
        document.querySelector(".rec-filter-wrapper").classList.add("reveal");
        this.fetchDataFilter();
      },
      async fetchDataFilter() {
        await this.getPackagingFilter();
      },
      addFilter(dataFilter) {
        this.currentPage = 1;
        this.filter = {
          ...dataFilter,
        };
        this.fetchPackagingOrdersList();
      },
      resetFilter(dataFilter) {
        this.currentPage = 1;
        this.filter = {
          ...dataFilter,
        };
        this.fetchPackagingOrdersList();
      },
      exportFilter(dataFilter) {
        this.currentPage = 1;
        this.filter = {
          ...dataFilter,
        };
        this.exportOrders();
      },
      exportOrders() {
        let exportFilters = {
          per_page: this.packagingOrdersListPages?.total ?? this.perPage ?? 10,
          starting_after : getStartingAfterForPage(this.currentPage ?? 1),
          state: this.filter?.state,
          companies: this.filter?.companies,
          from_date: this.filter.from_date ?? null,
          to_date: this.filter.to_date ?? null,
          service_type: 'packaging'
        };
        this.exportPackagingOrders(exportFilters);
      },
      onSearch() {
        this.currentPage = 1;
        this.fetchPackagingOrdersList();
        },
        navigateTo(path) {
            if (this.isLegacy) {
                this.$router.push(path);
            } else {
                window.location.href = `${path}?mode=iframe`;
            }
        }
    },
  };
</script>
