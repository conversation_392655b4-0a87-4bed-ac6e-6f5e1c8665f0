<template>
  <div>
    <div class="panel panel-default">
      <div class="panel-heading sides">
        <div>
          <h6 class="panel-title">
            <i class="sicon-shipping"></i>
            المناديب وشركات الشحن الخاصة
          </h6>
          <div class="panel-subtitle">
            <span class="text-muted text-muted-small">
              أضف مندوب توصيل أو شركة شحن خاصة ضمن خيارات الشحن لعملائك.
            </span>
          </div>
        </div>
        <a v-if="canAddNewCompany && isLegacy" class="load-data-bank btn btn&#45;&#45;outlined primary load_custom_company_create_form ml-20">
          <b><i class="sicon-add mr-5"></i></b>
          إضافة مندوب أو شركة شحن
        </a>
      </div>
      <div class="panel-body p-0">
          <div v-if="isAppealPendingOrDenied">
            <div class="d-flex justify-content-center align-items-center wide mt-40">
              <img src="/cp/assets/images/shipping/delegates_access_denied.svg" alt="access" width="200"/>
            </div>
            <div class="rec-placeholder text-center mh-auto py-40 px-20">
              <h2 class="rec-placeholder__title font-20 mt-10 mb-10">
                {{
                  delegateAppealStatus === 1001 ?
                      'تم تعطيل الوصول إلى المناديب بسبب مخالفة السياسات'
                      : 'تعذر تفعيل الميزة بسبب مخالفة الأنظمة'
                }}
              </h2>
              <p class="rec-placeholder__desc font-14">
                {{
                  delegateAppealStatus === 1001 ? 'تم رفع طلب مراجعة و سيتواصل معك فريق الشحن قريباً.'
                      : ' إذا لاحظت وجود خطأ يمكنك رفع طلب مراجعة تفعيل الميزة. سيتم مراجعة طلبك قريبًا.'
                }}
              </p>
              <button v-show="delegateAppealStatus === 1002" type="button" class="btn btn--outlined primary submit m-10 font-16" @click="createAppealRequest">تقديم طلب مراجعة</button>
              <a href="https://help.salla.sa/article/1890676593" class="d-block m-10">
                <span>اعرف المزيد عن سياسة الاستخدام</span>
              </a>
            </div>

          </div>
          <div v-else>
            <template v-if="!loading">
              <ul v-if="companies.length" class="rec-list rec-list--vertical rec-list--bordered light-border">
                <li v-for="(company, index) in companies" :key="index" class="p-20">
                  <a class="card card--horizontal card--clickable load-data-company" :data-company-id="company.id" data-company-type="custom">
                    <div class="card__content">
                      <h6 class="font-15 lh-1 m-0">{{ company.name }}</h6>
                    </div>
                    <div class="card__footer" @click.stop>
                      <div class="rec-list">
                        <shipping-rating :rating="parseFloat(company.rating?.rating)" :reviews="company.rating?.reviews" :flat="true" />
                        <div class="vue-toggle ml-20" style="width: auto;">
                          <ToggleButton v-model="company.status" @change="toggleClicked(company.id,company.status)" />
                        </div>
                      </div>
                    </div>
                  </a>
                </li>
              </ul>
              <!-- Empty state -->
              <div v-else class="rec-placeholder text-center mh-auto py-40 px-20">
                <div class="rec-placeholder__icon mb-20">
                  <i class="sicon-shipping font-80"></i>
                </div>
                <h2 class="rec-placeholder__title font-18">أضف أول مندوب لأسطولك الخاص</h2>
                <p class="rec-placeholder__desc font-14">يمكنك إضافة مناديب توصيل أو شركات شحن خاصة وعرضها لعملائك في صفحة إتمام الطلب.</p>
              </div>
            </template>

            <!-- Load more -->
            <div v-show="hasNextPage" ref="loadTrigger" class="align-center">
              <span class="loader loader--small mt-10"></span>
            </div>

          </div>

      </div>
    </div>
  </div>
</template>

<script>
  import Vue from "vue";
  import { mapActions, mapState } from "vuex";
  import ToggleButton from "vue-js-toggle-button";
  import api from "../config/axios.config";
  import {showSwAlert} from "../utils/showSwAlert";

  Vue.use(ToggleButton);

  export default {
    data() {
      return {
        page: 1,
        loading: true,
        isLegacy: window.sallaLegacy
      };
    },
    computed: {
      ...mapState({
        companies: (state) => state.customCompanies.companies,
        companiesPage: (state) => state.customCompanies.companiesPage,
        totalCompanies: (state) => state.customCompanies.totalCompanies,
        hasNextPage: (state) => state.customCompanies.hasNextPage,
        delegateAppealStatus: (state) => state.customCompanies.delegateAppealStatus,
      }),
      canAddNewCompany() {
        return window.initialData.permissions.add;
      },
      isAppealPendingOrDenied() {
        return [1001, 1002].includes(this.delegateAppealStatus);
      }
    },
  mounted() {
      window.addEventListener('message', ({data: {event}}) => {
        if (event !== 'nav.primary-action.clicked') {
          return
        }
        // event.preventDefault();

        $('#company_form_div').html('');

        $.ajax({
          url: "/shipping/customs/companies/create",
          type: 'GET',
          data: {},
          cache: false,
          success: function (resp) {

            $('#company_form_div').html(resp);
            $('#modal_company_form').modal();
          },
          error: function () {},
        });
      });

      this.getCustomCompaniesList().then(() => {
        this.loading = false;
        this.observeScroll();
      });
      
      if (this.canAddNewCompany) {
        window.parent?.postMessage({
          event: 'nav.primary-action',
          title: "إضافة مندوب أو شركة شحن",
          url: "#"
        }, '*');
      }
    },
    methods: {
      ...mapActions(["getCustomCompaniesList", "changeCompanyStatus"]),
      loadMore() {
        this.page++;
        this.$root.$store.commit("setCompaniesPage", this.page);
        this.getCustomCompaniesList(); /*.then((response) => {
        if (response.cursor.next === null) {
          this.loading = false;
        }
      })*/
      },
      async toggleClicked(companyId, company_status) {
        const payload = {
          status: company_status,
        };

        window.showLoading();

        await api.post(`/company/change-status/${companyId}`, payload).then(() => {
          window.hideLoading();
        }).catch((err) => {
          showSwAlert("error", err?.response?.data?.error?.message, undefined, false, 3000);
          window.hideLoading();
        });
      },
    observeScroll() {
      const observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          this.loadMore();
        }
      });

      observer.observe(this.$refs.loadTrigger);
    },
    async createAppealRequest() {
      window.showLoading();
      await api.post(`/delegates/appeal`).then(() => {
        showSwAlert("success", "تم ارسال طلب مراجعة بنجاح، سيتواصل معك فريق الشحن عن طريق البريد الالكتروني", undefined, false, 3000);
        window.hideLoading();
        window.location.reload();
      }).catch((err) => {
        showSwAlert("error", err?.response?.data?.error?.message, undefined, false, 3000);
        window.hideLoading();
      });
    },
  },
};
</script>