export const locale = {

  /*
  |--------------------------------------------------------------------------
  | Shipping Companies Language Lines
  |--------------------------------------------------------------------------

  */

  COMPANY: {
    enabled: 'تم تفعيل شركة :company بنجاح',
    invalid_inputs: 'الرجاء التحقق من المدخلات.',
    settings: {
      saved: 'تم حفظ الاعدادات',
    },
  },

  // companies translations *only convenient to translate*
  COMPANIES: {
    smsa: 'سمسا',
    saudi_post: 'البريد السعودي | سبل',
    aramex: 'أرامكس',
    dhl: 'DHL',
  },

  ARAMEX: {
    account_entity: 'AccountEntity',
    account_number: 'AccountNumber',
    account_pin: 'AccountPin',
    username: 'User<PERSON><PERSON>',
    password: 'Password',
    payment_type: 'Payment Type',
    payment_currency: 'Payment currency',
    product_group: 'Product Group',
    product_type: 'Product Type',
  },
  SHIPPINGRULE: {
    shipping_rules: 'قيود شركات الشحن',
    create: 'اضافة قيد شركة شحن جديد',
    edit: 'تعديل قيد شركة شحن',
    delete: 'تم حذف القيد بنجاح',
    save: 'تم الحفظ بنجاح'
  },
  FORM: {
    shipping_type: 'نوع الشحن',
    status: 'الحالة',
    active: 'مفعلة',
    not_active: 'معطلة',
    supported_cities: 'المدن المدعومة',
    excluded_cities: 'المدن المستثناه',
    supported_cities_with_excluded_cities_description: 'اختيار المدن المدعومة ضمن هذه التسعيرة٫ يمكنك اختيار كل المدن ومن ثم استخدام ميزة الاستثناء',
    select_supported_cities_within_this_pricing: 'اختيار المدن المدعومة ضمن هذه التسعيرة',
    excluded_cities_description: 'عند اختيار "كل المدن" في المدن المدعومة يمكنك استثناء بعض المدن',
    price_type: 'نوع التسعيرة',
    price_type_fixed: 'ثابتة',
    price_type_by_weight: 'حسب الوزن',
    shipping_cost: 'تكلفة الشحن',
    the_cost: 'التكلفة',
    the_first: 'أول',
    the_first_kilogram: 'أول كيلو جرام',
    the_increased_cost: 'تكلفة الزيادة',
    for_every: 'لكل',
    weight_cost: 'التكلفة بالوزن',
    duration: 'مدة الشحن',
    duration_for_3_5_days: 'مدة الشحن (مثلا ٣-٥ أيام)',
    cash_on_delivery: 'الدفع عند الاستلام ؟',
    available: 'متوفر',
    not_available: 'غير متوفر',
    cash_on_delivery_commission: 'عمولة الدفع عند الاستلام',
    save: 'حفظ',
    reset: 'إعادة تعيين',
    reset_confirmation: 'سيتم اعادة التسعيرة الافتراضية لشركة الشحن ، هل أنت متأكد من هذا الإجراء؟',
    add_shipping_pricing: 'إضافة تسعيرة شحن',
    delete_shipping_pricing: 'حذف التسعيرة',
    country: 'الدولة',
    all_countries: 'كل الدول',
    all_cities: 'كل المدن',
    no_excluded_cities: 'لا يوجد مدن مستثناه',
    delete_shipping_company: 'حذف شركة الشحن',
  },
  INTEGRATION: {
    display_data_in_policy: 'بيانات العرض في البوليصة',
    integration_type: 'طريقة الربط',
    company_name: 'اسم الشركة',
    sender_name: 'اسم المرسل',
    order_description: 'وصف نوع المنتجات',
    mobile: 'رقم الجوال',
    shipping_type: 'نوع الشحن',
    store_name: 'اسم المتجر',
    english_street_name: 'اسم الشارع باللغة الإنجليزية',
    english_neighborhood_name_with_zip_code: 'اسم الحي مع الرمز البريدي باللغة الإنجليزية',
    english_additional_description_title: 'وصف إضافي للعنوان باللغة الإنجليزية',
    merchant_name: 'اسم التاجر',
    company_integration_instructions: 'يجب التسجيل بموقع :company_name_link ثم تعبئة البيانات لإتمام الربط',
    display_name: 'اسم العرض',
    display_name_placeholder: 'شركة شحن سريع',
    display_name_sub_title: "يمكنك تخصيص اسم شركة الشحن الذي يتم عرضه للعملاء في قائمة شركات الشحن المتاحة لكل عميل في صفحة انهاء الطلب",
    show_order_items_on_police: 'إظهار عناصر الطلب عند طباعة البوليصة',
    save: 'حفظ',
  },
};
