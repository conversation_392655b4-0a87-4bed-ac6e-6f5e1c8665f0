$direction: 'rtl';
$fonts: ();
@import "partials/vars";
@import "partials/mixins";

// full calendar lib ---
.fc {
  $fc-root: &;

  &#{$fc-root} {
    // override standard theme ---
    &-theme-standard {

      #{$fc-root} {

        // header ---
        &-header-toolbar,
        &-toolbar {
          position: relative;

          #{$fc-root} {
            &-toolbar-title {
              font-size: $text-xx-medium;
            }
          }
        }

        // buttons ---
        &-button-group {
          .fc {
            &-button {
              padding: 0 4px 3px;
              color: $color-primary;
              font-size: $text-small;
              background: white !important;
              border-color: $color-primary;

              .fc-icon {
                &:before {
                  font-family: $font-sallaIcon, serif;
                }
              }

              &.fc-prev-button,
              &.fc-next-button {
                background-color: $color-primary !important;
                border-color: $color-primary !important;

                &:hover {
                  background-color: $color-primary !important;
                  border-color: $color-primary !important;
                }

                .fc-icon {
                  color: $color-white;
                }
              }

              &.fc-prev-button {
                @include b-radius(0 $b-radius-sm $b-radius-sm 0);
                margin-left: 2px;

                .fc-icon {
                  &:before {
                    content: '\e96c'
                  }
                }
              }

              &.fc-next-button {
                @include b-radius($b-radius-sm 0 0 $b-radius-sm);

                .fc-icon {
                  &:before {
                    content: '\e970'
                  }
                }
              }

              &:disabled {
                opacity: .3;
              }

              &:hover {
                background-color: white !important;
                border-color: $color-primary !important;
              }

              &:active,
              &:focus {
                background-color: $color-primary !important;
                border-color: $color-primary !important;
                box-shadow: none !important;
                color: $color-white;
              }

              &:first-child {
                @include b-radius(0 $b-radius-sm $b-radius-sm 0);
              }

              &:last-child {
                @include b-radius($b-radius-sm 0 0 $b-radius-sm);
              }

              &-active {
                background-color: $color-primary !important;
                border-color: $color-primary !important;
                color: $color-white;

                &:hover {
                  color: $color-white;
                  background-color: $color-primary !important;
                  border-color: $color-primary !important;
                }
              }
            }

            &-dayGridMonth-button,
            &-timeGridWeek-button,
            &-timeGridDay-button,
            &-listWeek-button {
              padding: 2px 12px 4px;
              line-height: initial;
            }

            &-today-button {
              margin-left: 10px;
              padding: 2px 12px 4px;
              line-height: initial;
              @include b-radius($b-radius-sm !important);
              border-color: $color-gray-200;
              background-color: transparent;

              &:not([disabled]) {
                color: $color-primary !important;
                border-color: $color-primary !important;

                &:hover {
                  color: white !important;
                  background: $color-primary !important;
                }
              }
            }
          }
        }

        // tables ---
        &-col-header {
          .fc {
            &-col-header-cell,
            &-timegrid-axis-frame {
              padding: 10px 0;

              &-cushion {
                font-size: $text-small;
                color: $color-dark-300;
                padding: 0;
              }

              &:first-child {
                @include b-radius(0 $b-radius-sm 0 0)
              }

              &:last-child {
                @include b-radius($b-radius-sm 0 0 0)
              }
            }
          }
        }

        &-view-harness {
          margin-bottom: 4px;
        }

        &-timegrid-axis-cushion {
          font-size: $text-small;
          white-space: nowrap;
        }

        // main view ---
        &-view {
          height: calc(100% + 2px);
          border-top: 1px solid $color-gray-200;
          border-left: 1px solid $color-gray-200;
          @include b-radius($b-radius-sm);

          > table {
            min-width: unset;
            border-color: $color-gray-200;
          }
        }

        &-list-table {
          table-layout: auto;
        }

        &-scrollgrid {
          border: none;

          > thead {
            background: $color-gray-50;
          }

          &-section,
          &-section-body {
            .fc {
              &-daygrid {

                &-day {
                  &.selected {
                    background-color: #f551574f;

                    .fc-daygrid-day-number {
                      color: $color-danger;
                    }
                  }
                }

                &-day-top {
                  padding-left: 5px;
                }

                &-day-frame {
                  display: flex;
                  flex-direction: column;
                }

                &-day-number {
                  font-size: $text-x-small;
                  color: $color-dark-300;
                  padding: 0 5px;
                }

                &-day-events {
                  font-size: $text-x-small;
                  margin-top: 30px;

                  .fc {
                    &-daygrid-event-dot {
                      transform: translateY(6px);
                    }

                    &-event-title {
                      color: $color-gray-25;
                      white-space: normal;
                    }

                    &-event-time {
                      color: #fff;
                      color: var(--fc-event-text-color, #fff);
                    }
                  }
                }

                &-day-disabled {
                  background-color: $color-gray-50;
                }
              }

              &-day-today {
                background-color: rgba($color-warning, 0.1);

                .fc-daygrid-day-top {
                  * {
                    color: darken($color-warning, 35%) !important;
                  }
                }
              }

              &-non-business {
                background-color: rgba($color-primary, 0.15);
              }
            }
          }
        }

        &-timegrid-slot-label {
          &-cushion {
            float: right;
            font-size: $text-x-small;
            padding: 0 10px 8px;
          }
        }

        &-daygrid-week-number {
          font-size: $text-xx-small;
          color: $color-dark-100;
          padding: 0 5px 4px;
          background-color: $color-gray-200;
          direction: ltr;
        }
      }
    }
  }

  // boxed version ---
  &--boxed {
    &#{$fc-root} {
      // override standard theme ---
      &-theme-standard {

        #{$fc-root} {

          // header ---
          &-header-toolbar,
          &-toolbar {
            #{$fc-root} {
              &-toolbar-chunk {
                #{$fc-root} {
                  &-button-group {
                    position: initial;

                    .fc-button {
                      position: absolute;
                      background: transparent !important;
                      box-shadow: none !important;
                      border: none;
                      padding: 0;
                      top: 0;

                      .fc-icon {
                        font-size: $text-xx-larger;
                        color: $color-dark-300;
                      }

                      &.fc-prev-button {
                        right: 0;
                      }

                      &.fc-next-button {
                        left: 0;
                      }

                      &:disabled {
                        opacity: 0.35;
                      }
                    }
                  }
                }
              }
            }
          }

          &-daygrid {
            &-day-top {
              padding: 0;
              flex: auto;
              align-items: center;
              justify-content: center;
            }

            &-day-events {
              display: none;
            }
          }
        }
      }
    }

    @include mediaMaxWidth(576px) {
      .fc-header-toolbar {
        .fc-toolbar-chunk {
          &:first-of-type {
            width: auto;
          }
        }
      }
    }
  }

  &.fc-theme-standard td,
  &.fc-theme-standard th,
  &.fc-theme-standard .fc-list {
    border-color: $color-gray-200 !important;
  }

  .fc-daygrid-event {
    background-color: transparent;
    border: none;
    color: $color-dark-300;

    &:hover {
      background-color: transparent;
    }

    .fc-daygrid-event-dot {
      border-color: $color-primary;
    }
  }

  .fc-h-event,
  .fc-event,
  .fc-v-event {
    background-color: $color-primary;
    border-color: $color-primary;
    margin: 0 !important;
    @include b-radius(0 !important);
    padding: 1px 5px 5px;

    &:hover {
      background-color: $color-primary;
    }
  }

  .fc-popover {

    .fc-popover-header {

      background-color: $color-primary;

      padding: 7px 6px;

      span {
        font-size: $text-x-small;
        color: white;

        &.fc-popover-close {
          &:after {
            content: none;
          }
        }
      }
    }
    .fc-popover-body {
      .fc-daygrid-event-harness {
        margin-top: 10px;
        &:first-child {
          margin-top: 0;
        }
      }
    }
  }

  .fc-list-day-cushion {
    background-color: $color-gray-50;
  }

  .fc-list-event {
    background: transparent;
    border: none;
    color: $color-dark-300 !important;
    display: table-row;

    .fc-list-event-dot {
      border-color: $color-primary;
    }
  }

  @include mediaMaxWidth(576px) {
    &:not(.fc--boxed) {
      min-width: 800px;

      .fc-header-toolbar {
        flex-wrap: wrap;
        flex-direction: row !important;
        margin-bottom: 10px !important;
        overflow: auto;

        .fc-toolbar-chunk {
          &:first-of-type {
            width: 100%;
            margin-top: 15px;
            order: 1;

            .fc-button-group {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
