
*,
:after,
:before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  height: 100%;
  background: #ffffff !important;
  direction: rtl;
  margin: 0;
  max-height: 100vh; /* Fallback for browsers that do not support Custom Properties */
  max-height: calc(var(--vh, 1vh) * 100);
  align-items: center;
  justify-content: center;
  overflow: auto;
}

h1,
h2,
h3,
h4,
h5,
h6,
b,
strong {
  font-family: 'DINNextLTArabic-Medium';
  font-weight: normal;
}

div,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
img,
i,
ol,
ul,
li,
form,
label,
hr,
input {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}
button {
  color: white;
  background: #FFB703;
  border-radius: 5px;
  height: 50px;
  cursor: pointer;
  outline: none;
  border: none;
  font-size: 15px;
  width: 100%;
}
mark{
  background-color: rgba(255, 183, 3, 0.38);
  padding: 0 2px 0 4px;
  box-shadow: 0px 4px 35px rgba(0, 0, 0, 0.08);
  display: inline-block;
  height: 20px;
  line-height: 14px;
  border-radius: 1px;
  // color: #FFB703;
}

button:disabled {
  cursor: not-allowed !important;
  pointer-events: all !important;
  opacity: 0.5;
}

.wrapper-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-helper{
  height: 100vh;
}

.d-flex{
  display: flex;
}

.store-pick-wrapper {
  max-width: 520px;
  min-width: 520px;
  margin: auto;
  width: 100%;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0px 4px 35px rgba(0, 0, 0, 0.08);
}

.store-pick-wrapper hr {
  background-color: #eaeaea;
  height: 1px;
}

.store-picker-header {
  background: #f2f2f2;
  border-radius: 20px 20px 0px 0px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.header-logo {
  height: 63px;
  max-width: 157px;
  > img {
    border-top-right-radius: 21px;
  }
}

.header-content {
  > h4 {
    font-size: 18px;
    line-height: 26px;
  }
  > p {
    font-size: 15px;
    line-height: 22px;
    max-width: 400px;
  }
}

.store-picker-body {
  > div:first-child {
    position: relative;
    padding: 20px;
    font-size: 17px;
    > span {
      color: #9b9b9b;
    }
    > svg {
      position: absolute;
    }
    #top-vector {
      right: 46px;
      top: 21px;
    }
    #bottom-vector {
      right: 250px;
      bottom: 42px;
      transform: rotate(180deg);
    }
    > h5 {
      padding-bottom: 3px;
      position: relative;
      font-weight: 700;
      line-height: 23px;
      > svg {
        position: absolute;
      }
      #top-vector {
        top: 0;
        right: unset;
        margin-right: -2px;
        margin-top: 0;
      }
    }
  }
}

.store-picker-list {
  direction: ltr;
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 410px;
  overflow-y: auto;
  margin-right: 8px;
  padding-right: 7px;
  padding-left: 20px;
  max-height: 420px;
  margin-bottom: 20px;
}

.store-picker-list::-webkit-scrollbar {
  width: 5px;
}

.store-picker-list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 100px;
}

.store-picker-list::-webkit-scrollbar-thumb {
  background-color: #a9a9a9;
  outline: none;
  border-radius: 100px;
}

.store-picker-list div {
  direction: rtl;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
  position: relative;

  &.checked {
    background-color: #f5f5f5;
    > span {
      > svg {
        display: block;
      }
    }
  }

  > img {
    border-radius: 8px;
    margin-right: 5px;
    object-fit: cover;
  }
  > input {
    position: absolute;
    min-width: 18px;
    height: 18px;
    opacity: 0;
  }
  > span {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    border: 1.25px solid #555555;
    border-radius: 2px;
    > svg {
      color: #555555;
      display: none;
      font-size: 10px;
    }
  }
  >label{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.store-picker-body {
  > div:last-child {
    padding: 20px;
  }
}

.checkbox {
  margin-bottom: 0 !important;
}

.success-container {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 6px;
  > h3 {
    font-size: 18px;
    font-weight: 500;
  }
  > span {
    font-size: 15px;
    color: #9b9b9b;
  }

  > button {
    width: 139px;
    margin: 25px auto auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  > div {
    margin: 10px auto;
    position: relative;
    min-height: 132px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    > img {
      position: absolute;
      width: 100%;
      min-width: 473px;
      min-height: 132px;
    }
  }
}

.cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 15px;
  font-size: 15px;
  text-align: center;

  > div {
    background-color: #f5f5f5;
    width: 100%;
    max-width: 200px;
    overflow: hidden;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  img {
    width: 100%;
    height: 100%;
  }

  span{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 23px;
    min-height: 47px;
  }

  & .store-card {
    padding: 15px;
    > img {
      max-height: 62px;
      object-fit: cover;
      border-radius: 10px;
    }
    > span {
      padding-top: 10px;
    }
  }
  & .product-card {
    > img {
      min-height: 100px;
      max-height: 123px;
      object-fit: cover;
      border-radius: 10px 10px 0 0;
    }
    > span {
      border: 10px solid transparent;
    }
  }
}

.stats-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  margin: 10px;
  font-size: 17px;

  > div:first-child {
    align-items: center;
    aspect-ratio: 1;
    background-color: #f5f5f5;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    font-size: 23px;
    font-weight: 600;
    padding: 5px 11px 9px;
    min-width: 56px;
  }
}

.download-links-wrapper {
  margin-top: 10px;
  padding: 0 !important;
  > p {
    text-align: center;
    font-weight: 600;
    font-size: 17px;
    line-height: 21px;
    padding: 0 20px;
    margin: 0 auto 20px auto;
    max-width: 400px;
    > span {
      color: #636f93;
    }
  }
  > div:nth-child(2) {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 0 10px 20px 10px;

    > a {
      max-height: 45px;
      max-width: 160px;
      &:nth-child(1){
        margin-right: auto;
      }
      &:nth-child(2){
        margin-left: auto;
      }
      > img {
        width: 100%;
        height: 100%;
      }
    }
  }
  > div:nth-child(3){
    padding-bottom: 20px;
    text-align: center;
    >a{
      text-decoration: none;
      color: #9b9b9b;
      &:hover{
        color: #FFB703;
      }
    }
  }
}

@media only screen and (max-width: 600px) {
  .store-pick-wrapper{
    min-width: unset;
  }
}

@media only screen and (min-height: 815px){
  .center-helper{
    display: flex;
  }
}

@media only screen and (max-width: 520px) {
  @media only screen and (max-height: 680px){
    .full-screen-mobile{
      >div{
        min-width: 450px;
      }
    }
  }
  @media only screen and (max-height: 520px){
    .full-screen-mobile{
      >div{
        min-width: 520px;
      }
    }
  }
}

@media only screen and (max-height: 546px) {
  .d-flex{
    display: block;
  }
}
