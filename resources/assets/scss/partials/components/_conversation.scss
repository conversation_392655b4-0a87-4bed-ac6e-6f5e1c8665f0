.rec-app-log-entry {
  width: 100%;
  padding: 15px 55px 15px 0;
  border-bottom: 1px solid $color-gray-100;
  position: relative;

  p {
    font-size: $text-small;
    color: $color-dark-200;
    text-align: right;
    margin: 0 0 5px 0;
  }

  .avatar-wrapper {
    flex: 1 0 40px;
    display: block;
    width: 40px;
    height: 40px;
    @include b-radius(50%);
    position: absolute;
    top: 20px;
    right: 0;
    border: 1px solid $color-gray-200;
    overflow: hidden;
    background-color: $color-gray-25;
    background-size: 77%;
    background-position: bottom;
    background-repeat: no-repeat;

    img {
      display: block;
      width: 100%;
    }
  }

  small {
    display: block;
    font-size: $text-xx-small;
    color: $color-dark-100;

    &:before {
      content: '\ea29';
      font-family: $font-sallaIcon;
      font-size: $text-xx-small;
      color: $color-dark-100;
      display: inline-block;
      vertical-align: middle;
      margin: 0 0 0 10px;
    }
  }

  &__attachment {
    flex-wrap: wrap;
    margin: 10px 0 0 0;
  }

  &:first-child {
    padding-top: 0;

    .avatar-wrapper {
      top: 0;

    }
  }

  &:last-child {
    border: none;
    padding-bottom: 0;
  }

  @include mediaMaxWidth($screen-phones) {
    @include flexable(flex-start, flex-start, column);
    p {
      font-size: $text-x-small;
    }
    padding: 65px 0 15px !important;
    .avatar-wrapper {
      top: 15px !important;
      right: 50%;
      transform: translateX(50%);
    }
  }

  &--err-log {
    padding: 15px 68px 15px 0;

    &:not(:last-of-type) {
      border-bottom: 1px solid $color-gray-200;
    }

    .avatar-wrapper {
      width: 50px;
      height: 50px;
    }

    p, small {
      font-size: $base-size;
      color: $color-dark-300;
    }

    small {
      color: $color-dark-200
    }

    @include mediaMaxWidth($screen-phones) {
      display: block;
      padding: 15px 68px 15px 0 !important;
      .avatar-wrapper {
        top: 20px !important;
        right: 24px;
      }
    }
  }
}

.add-comment-wrapper {
  margin: 25px 0 0;

  h4 {
    font-size: $base-size;
    color: $color-dark-300;
    font-weight: bold;
    margin: 0 0 15px 0;
  }

  .form-control {
    border: 1px solid $color-gray-300;
    height: 111px;
    padding: 15px;

    &::placeholder {
      color: $color-dark-300;
      display: block;
    }
  }

  .btn {
    font-size: $text-small;
    padding: 5px 0 7px;

    &::before {
      content: '\ea96';
      font-family: $font-sallaIcon;
      margin-left: 15px;
      position: relative;
      top: 1px
    }
  }
}