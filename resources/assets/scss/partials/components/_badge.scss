.badge {
  @include b-radius(50px);
  padding: 5px 8px;
  line-height: 1;
  cursor: pointer;
  max-height: 27px;

  &-new {
    width: 10px;
    height: 10px;
    background-color: $color-white;
    @include b-radius(50%);
    position: absolute;
    top: 9px;
    right: 10px;
    @include flexable(center, center);

    &__inner {
      width: 6px;
      height: 6px;
      @include b-radius(50%);
    }
  }

  i {
    font-size: $text-xxx-small;
    display: inline-block;
  }

  &--grey {
    color: $color-dark-200;
    background: $color-gray-200 !important;

    &:hover, &:active, &:focus {
      color: $color-dark-100 !important;
      background: $color-gray-200;
    }

    &.without-hover {
      cursor: text;

      &:hover, &:active, &:focus {
        color: $color-dark-100;
        background: $color-gray-200;
      }
    }
  }

  &--small {
    font-size: $text-xxx-small;
  }

  &--medium {
    font-size: $text-x-small;
  }


  &--large {
    font-size: $text-x-small;
    padding: 1px 15px 3px;
  }

  &--primary {
    color: $color-primary-l !important;
    background-color: $color-secondary-50 !important;

    &-light {
      color: $color-primary-l;
      border-color: $color-secondary-50;
      background-color: rgba($color-secondary-50, 0.25);
    }
  }

  &--warning {
    color: #856205; //$color-brown;
    background-color: $color-warning-light !important;
  }

  &--outline {
    border: 1px solid $color-gray-200;
    background-color: $color-white;

    &,
    &.badge:focus {
      color: $color-dark-300;
    }

    &:hover,
    &:active,
    &.is-active {
      color: $color-white !important;
      border-color: $color-primary-l;
      background-color: $color-secondary-50;
    }
  }

  &--primary-outline {
    color: $color-primary-l;
    border: 1px solid rgba($color-secondary-50, 0.5);
    background-color: rgba($color-secondary-50, 0.01);

    &:hover {
      color: $color-primary-l !important;
      border-color: $color-secondary;
      background-color: $color-secondary;
    }

    &:focus {
      color: $color-primary-l !important;
    }

    &.default {
      &:hover, &:focus {
        color: $color-primary-l;
        border: 1px solid rgba($color-secondary-50, 0.5);
        background-color: rgba($color-secondary-50, 0.01);
      }
    }
  }

  &--danger {
    color: $color-white !important;
    background-color: $color-danger !important;
  }

  &--light {
    &-danger {
      background: #f551571a; //colorAvilabilty
      color: $color-danger !important;
      border: 1px solid #f5515733; // colorAvilabilty
      * {
        color: $color-danger !important;
      }
    }

    &-warning {
      background: #FFC62A1a; //colorAvilabilty
      color: $color-warning !important;
      border: 1px solid #FFC62A33; // colorAvilabilty
      * {
        color: $color-warning !important;
      }
    }
  }

  &--large {
    font-size: 13px;
    padding: 1px 15px 3px;
  }

  &--light-grey {
    color: $color-dark-100;
    border-color: $color-gray-200;
    background-color: #8888880d;
  }

  &--primary-light {
    color: $color-primary;
    border-color: $color-primary;
    background-color: rgba($color-primary, 0.1);
  }

  &--transparent {
    border: 0;
    background: transparent;
  }

  &--danger-text {
    color: #F55157;
  }

  &--primary-no-border {
    color: $color-primary;
    border-color: rgba($color-primary, 0.1);
    background-color: rgba($color-primary, 0.1);
  }

  &--danger {
    background-color: #FFEAEA;
    color: #F55156;
    border: 1px solid rgba(245, 81, 86, .15)
  }

  &--cursor-default {
    cursor: default;
  }

  &--pe-none {
    pointer-events: none;
  }

  &.es-default {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  &--coupon-share {
    line-height: 1;
    margin-right: 5px;
    padding: 2px 8px 3px;

    span {
      line-height: 1;
      font-size: $text-xxxx-small;
    }

    &:before {
      content: '\ef25';
      font-family: $font-sallaIcon;
      font-size: $text-xxxx-small;
      @include right-icon-el(5px);
    }

    @include mediaMaxWidth($screen-phablet) {
      display: table-cell;
      margin: 10px 0 0 0;
      padding: 2px 10px 4px 6px;
    }
  }

  &--with-btn {
    .btn {
      padding: 0;
      width: 15px;
      height: 15px;
      @include transi();
      @include b-radius(50%);
      line-height: 11px;
      top: 0;
      left: -2px;
      margin-right: 7px;
      position: relative;

      &:before {
        content: '';
        font-family: $font-sallaIcon;
        color: $color-white;
        font-size: $text-xxxx-small;
      }
    }
  }

  &--clear {
    padding: 0;
    background: transparent;
    color: $color-dark-300;
  }

  &.es-default {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  &.orders {
    display: none;
    position: absolute;
    top: 1px;
    right: 9.3rem;

    span {
      display: inline-block;
      transform: translateY(1px);
      font-size: $text-xxx-small;
    }

    .btn {
      background-color: $color-danger;

      &:hover {
        background-color: $color-danger;
      }

      &:before {
        content: '\ea47';
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      position: relative;
      right: 0;
    }
  }
}

.tag {
  &--order-source {
    display: inline-block;
    width: 22px;
    height: 22px;
    vertical-align: middle;
    @include b-radius(50%);
    text-align: center;
    transform: translateY(-2px);

    i {
      display: inline-block;
      transform: translateY(-3px);
      font-size: $text-x-small;
    }

    // override ---
    &.tooltip-toggle.primary {
      background: $color-gray-200 !important;
      border: none !important;

      i {
        color: $color-dark-300 !important;
      }
    }

    @include mediaMaxWidth($screen-phones) {
      i {
        transform: translateY(-1px);
      }
    }
  }
}

.rec-list--tags li.dark {
  background-color: $color-gray-200 !important;
}
