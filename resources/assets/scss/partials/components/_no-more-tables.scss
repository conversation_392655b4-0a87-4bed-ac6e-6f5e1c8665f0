.no-more-tables {
	&--enhanced {
		tr {
			&.table-row {
				@include mediaMaxWidth($screen-phones) {
					td{
						white-space: normal !important;

						.rec-list{
							text-align: right;
						}
					}
				}
				@include mediaMaxWidth($screen-tablet-p) {
					td {
						padding: 10px 15px;
						height: auto;
						text-align: left;
						text-align: -webkit-left;

                        &.text-left {
                          text-align: right !important;
                        }

						&:before {
							float: right;
							right: unset;
							position: relative;
							padding-left: 20px;
							max-width: 50%;
						}
						&:after {
							content: "";
							clear: both;
							display: table;
						}
						&.title-hidden {
							display: none;
						}
            &.title-header-hidden-xs {
              @include mediaMaxWidth($screen-tablet-p) {
                &::before {
                  display: none;
                }
              }
            }
					}
				}
			}
		}
		@include mediaMaxWidth($screen-phones) {
			tbody {
				tr.table-row {
					padding: 0;
				}
			}
		}
	}

	> table {
	tr.table-break-word {
      td {
        &:before {
          word-break: break-word !important;
        }
      }
    }

	tr.table-row {
		td {
			&:before {
				width: 140px;
				word-break: break-all;
			}
            &.without-before{
              td {
                &:before {
                  display: none;
                }
              }
            }

          &.row-delete-overlay{
            position: absolute;
            top: 0;
            left: 0;
            background-color: rgba(255,255,255,0.8);
            border: 1px solid #f1eeee;
            width: 100%;
            height: 100%;
            &--hide{
              display:none;
            }
            &--show{
              display: block;
            }

           .delete-wrapper{
             display: flex;
             align-items: baseline;
             position: absolute;
             justify-content: end;
             gap: 10px;
             top: 30%;
             left: 10px;
             margin-left: 14px;
       }
      }

    }
}


@include mediaMaxWidth($screen-tablet-p) {
  > table tbody {
    tr td {
      &.td-switcher {
        width: 60px;
        padding-right: 0
      }

      &.products-group {
        padding-left: 20px !important;
      }
    }
  }
}
@include mediaMaxWidth($screen-tablet-p) {
  > table tbody tr {
    td {
      &.td-switcher {
        width: auto;
      }
    }

    &.table-row {
      td {
        a.title {
          &--link {
            text-align: left;
          }
        }
      }
    }

  }
}
}

&--medium-screen {
@include mediaMaxWidth(1300px) {
  display: block;
  > table thead {
    display: none;
  }
  > table thead tr.table-row tr {
    position: absolute;
    top: -9999px;
    right: -9999px;
  }
  > table tbody {
    border-top: 1px solid $color-gray-200;
    display: block;

    tr {
      &.table-row {
        position: relative !important;
        border-bottom: 1px solid $color-gray-200;
        padding-top: 15px;
        padding-bottom: 20px;
        display: block;

        td {
          border: none;
          position: relative;
          padding-right: 85px;
          text-align: right;
          height: 35px;
          padding-top: 7px;
          padding-bottom: 7px;
          text-overflow: ellipsis;
          font-size: $text-small;
          display: block;

          &.customer-td {
            height: auto !important;
            padding: 10px 18px 10px 0 !important;
          }

          &.td-cod {
            padding-right: 145px;
            text-align: left;
          }

          &::before {
            content: attr(data-title);
            position: absolute;
            right: 20px;
            padding-left: 10px;
            text-align: right;
            color: $color-dark-100;
          }
        }
      }
    }
  }
}
}

@include mediaMaxWidth($screen-phones) {
  > table {
    display: block;
    > tbody,
    > thead > tr,
    > tbody > tr,
    > tbody > tr > td  {
      display: block;
    }
  }
}
}
