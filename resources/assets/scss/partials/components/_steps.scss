.rec-step {
  counter-reset: rec-step-counter;

  .rec-step-import {
    min-width: 500px;
    position: relative;
    counter-increment: rec-step-counter;
    margin: 0 0 15px 0;
    padding: 15px;
    @include b-radius($b-radius-sm);
    background: $color-white;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    p {
      color: $color-dark-300 !important;
    }

    .btn {
      &.btn-primary {
        margin: 10px 0 0;
        @include b-radius($b-radius-sm);
        background-color: $color-primary;
        border: none;

        i {
          display: inline-block;
          vertical-align: middle;
          margin-left: 5px;
          transform: translateY(-2px);
        }
      }
    }

    .rec-upload-label {
      label {
        margin: 0 !important;
        white-space: nowrap !important;

        i {
          display: inline-block;
          vertical-align: middle;
          margin-left: 5px;
          transform: translateY(-1px);
        }
      }

      @include mediaMinMaxWidth(769px, $screen-tablet-l) {
        label {
          width: 180px;
          padding: 12px;
        }
      }
    }

    &:before {
      content: counter(rec-step-counter);
      font-family: <PERSON><PERSON><PERSON>, serif;
      font-size: $text-small;
      font-weight: bold;
      color: $color-white;
      display: block;
      width: 30px;
      height: 30px;
      line-height: 30px;
      @include b-radius(50%);
      position: absolute;
      top: 50%;
      right: 15px;
      transform: translateY(-50%);
      background: $color-primary;
    }

    &:last-child {
      margin-bottom: 0;
    }

    @include mediaMaxWidth($screen-phablet) {
      min-width: auto;
    }

    @include mediaMaxWidth(350px) {
      padding-top: 50px;
      &:before {
        @include centerX;
        top: 12px;
        right: auto;
      }
    }

    &.second {
      label {
        padding: 0 32px;
        height: 48px;
        line-height: 48px;
      }

      .stepy-finish {
        height: 48px;
      }
    }
  }

  &--flat {
    li {
      width: 100%;
    }

    .rec-step-import {
      text-align: right;
      box-shadow: none;
      margin-bottom: 15px;
      padding: 0 40px 0 0;

      small {
        font-size: $text-xx-small;
        color: $color-dark-100
      }

      &::before {
        z-index: 55;
        width: 25px;
        height: 25px;
        top: 15px;
        text-align: center;
        line-height: 25px;
        right: 0;
        font-size: $text-xx-small;
        font-weight: normal;
      }
    }

    &.order-edit {

      @include mediaMaxWidth(680px) {
        .rec-step-import {
          &.second {
            .excel-upload-section {
              width: 100%;

              button {
                padding: 9px 8px
              }
            }
          }
        }
      }
    }
  }


  @include mediaMaxWidth(600px) {
    .rec-step-import {
      &.second {
        label {
          width: 300px
        }
      }
    }
  }

  @include mediaMaxWidth($screen-phablet) {
    .rec-step-import {
      &.second {
        label {
          width: 130px
        }
      }
    }
  }

  &--wizard {
    flex: initial;
    margin: 0 0 10px 15px;

    li {
      width: auto;
    }

    a {
      @include flexable(center, flex-start, row);
      width: 100%;
      height: auto;
      position: relative;
      @include b-radius(50px);
      @include transi();
      padding: 10px 15px 10px 25px;
      font-size: $text-small;
      font-weight: 500;
      color: $color-primary-l;
      background-color: $color-gray-50;
      counter-increment: rec-wizard-counter;
      pointer-events: none;
      box-shadow: none;

      &:before {
        content: counter(rec-wizard-counter) !important;
        font-size: $text-small;
        color: $color-primary;
        display: inline-block;
        vertical-align: middle;
        width: 30px;
        height: 30px;
        line-height: 30px;
        margin: 2px 0 0 0;
        text-align: center;
        @include b-radius(50%);
      }
    }

    // active step ---
    &.active {
      flex: auto;

      a {
        color: $color-primary-l;
        background-color: $color-secondary-50;

        &:before {
          color: $color-secondary;
          margin-left: 10px;
          background-color: $color-primary-l;
          border-color: $color-primary;
        }
      }

      &.has-notifications {
        a {
          padding-left: 50px;

          .rec-step-notification {
            display: inline-block;
            position: absolute;
            left: 15px;
            font-size: $text-xx-medium;

            &:before {
              content: attr(data-notification-count);
              background-color: $color-social-youtube;
              position: absolute;
              top: 0;
              right: -10px;
              font-size: $text-xxx-small;
              @include b-radius(50%);
              width: 18px;
              height: 18px;
              text-align: center;
              padding-right: 1px;
            }
          }
        }
      }

      &.has-error {
        a {
          background: $color-gray-50;
          color: $color-danger;
          border: 1px solid rgba($color-danger, 0.45);

          &:before {
            background-color: $color-danger;
            box-shadow: none;
          }
        }
      }
    }

    // finished step should have no mouse actions ---
    &.done {
      a {
        color: $color-primary-l;
        background-color: $color-white;
        padding-left: 50px;
        pointer-events: none;

        &:after {
          content: '\ea9d';
          font-family: $font-sallaIcon;
          line-height: 22px;
          color: $color-primary-l;
          text-align: center;
          display: inline-block;
          width: 25px;
          height: 25px;
          padding: 1px 0 0 0;
          @include b-radius(50%);
          vertical-align: middle;
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          background-color: $color-secondary-50;
        }
      }
    }

    &:last-child {
      margin: 0;
    }

    @include mediaMaxWidth($screen-desktop-small) {
      margin: 0 0 10px 10px;
      a {
        font-size: $text-x-small;
        padding: 5px 5px 5px 17px;

        &:before {
          font-size: $text-x-small;
        }
      }
      &.active {
        a {
          padding-right: 7px;

          &:before {
            margin-top: 0;
          }
        }
      }
      &.done {
        a {
          padding-left: 40px;

          &:after {
            left: 7px;
          }
        }
      }
    }
    @include mediaMaxWidth($screen-phones) {
      flex: 1 0 100%;
      margin: 0 0 10px !important;
    }
  }

  &--labels {
    > a {
      counter-increment: rec-wizard-counter;
      color: $color-dark-300;
      font-weight: bold;
      padding: 25px 20px;
      width: 100%;
      display: block;
      &:before {
        height: 30px;
        width: 30px;
        content: counter(rec-wizard-counter) !important;
        @include b-radius(50px);
        border: 1px solid $color-gray-200;
        margin-left: 15px;
        display: inline-block;
        text-align: center;
        line-height: 30px;
        font-weight: 400;
      }
    }

    &.open {
      > a {
        background: $color-secondary-50;
        color: $color-primary-l;
        &:before {
          border-color: $color-primary-l;
        }
      }
    }

    &.complete {
      > a {
        &:before {
          content: "\ea9d" !important;
          font-family: "sallaicons";
          color: $color-primary-l;
          background-color: $color-secondary-50;
          flex-direction: row;
          box-shadow: 0 4px 11px 0 $color-secondary-d;
          border: 0;
          font-size: $text-x-medium;
          line-height: 31px;
        }
      }
    }

    .inner-steps {
      display: none;
      li {
        a {
          color: $color-dark-100;
          padding: 30px 20px 10px;
          width: 100%;
          display: block;
          font-weight: 500;
          @include flexable(center, flex-start, row);
          background-color: $color-gray-25;
          position: relative;
          &:before {
            content: '';
            height: 20px;
            width: 20px;
            background-color: $color-gray-200;
            @include b-radius(50px);
            margin-left: 15px;
            display: inline-block;
          }
          &::after {
            content: '';
            height: 25px;
            width: 1px;
            background-color: $color-gray-200;
            position: absolute;
            right: 30px;
            top: 0;
          }

          .tooltip-content {
            bottom: 55%;
          }
        }

        &.complete {
          a {
            color: $color-dark-300;
            &::before, &::after {
              background-color: $color-secondary-l !important;
            }
          }
        }

        &:last-of-type {
          a {
            padding-bottom: 20px;
          }
        }
      }
    }
  }

  &--linear {
    flex: auto;
    max-width: 300px;
    position: relative;
    > a {
      counter-increment: rec-wizard-counter;
      color: $color-dark-200;
      @include flexable(center, center, column);
      cursor: unset;
      position: relative;
      &:before {
        height: 40px;
        width: 40px;
        content: counter(rec-wizard-counter) !important;
        @include b-radius(50px);
        border: 1px solid $color-gray-300;
        color: $color-dark-200;
        display: inline-block;
        text-align: center;
        line-height: 40px;
        position: relative;
        z-index: 2;
      }

      &:after {
        content: '';
        position: absolute;
        height: 40px;
        width: 60px;
        background-color: $color-white;
        top: 0;
        @include centerX;
        z-index: 1
      }
    }
    &.active {
      > a {
        span {
          color: $color-primary-l;
        }
        &:before {
          border: none;
          background-color: $color-secondary-50;
          color: $color-primary-l;
          font-weight: bold;
        }
      }
    }
    &.complete {
      > a {
        &:before {
          content: "\ea9d" !important;
          font-family: "sallaicons";
          color: $color-primary;
        }
      }
    }
    &.has-error {
      > a {
        &:before {
          background-color: $color-danger-light;
          color: $color-danger;
          border: none;
        }
        span {
          color: $color-danger
        }
      }
    }
    &:not(:last-of-type) {
      &:after {
        content: '';
        position: absolute;
        top: 20px;
        right: 45%;
        background-color: $color-gray-300;
        height: 1px;
        width: 100%
      }
    }
  }
}
