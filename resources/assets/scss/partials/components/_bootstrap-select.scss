.bootstrap-select {
  .dropdown-menu {
    padding: 0 !important;

    li {
      a {
        span {
          &.text {
            small {
              &.text-muted {
                display: inline-block;
                font-size: $text-xx-small;
                color: $color-gray-400 !important;
                margin: 0 5px 0 0;
              }
            }
          }
        }
      }

      &.no-results {
        margin-bottom: 0;
      }
    }
  }

  select {
    display: none !important;
  }

  &--initial {
    position: initial;
  }

  &--no-border {
    .btn {
      border: none;
    }
  }

  &--xs {
    .btn {
      padding: 5px 12px
    }
  }

  &--product-search {
    .dropdown-menu {
      width: 100% !important;

      .no-results {
        display: none
      }
    }
  }

  &--options-group {
    .dropdown-menu {
      li {
        &.dropdown-header {
          @include flexable(center, flex-start, row);

          .text {
            font-weight: bold;
            color: $color-dark-300;
            margin-left: 10px;
          }

          &:after {
            content: '';
            width: 100%;
            height: 1px;
            background: $color-gray-200;
          }
        }

        > a {
          padding-right: 40px !important;

          &:before {
            content: '';
            font-family: $font-sallaIcon;
            width: 18px;
            height: 18px;
            @include b-radius($b-radius-sm);
            border: 1px solid $color-primary-l;
            display: inline-block;
            position: absolute;
            right: 14px;
            @include centerY;
            line-height: 17px;
            font-size: $text-xx-small;
            text-align: center;
          }

          .check-mark {
            display: none;
          }
        }

        &.divider {
          display: none;
        }

        &.selected {
          a {
            &:before {
              content: '\ea9d';
              color: $color-primary-l;
            }
          }
        }
      }
    }
  }

  &-branches {
    .dropdown-menu.inner {
      li {
        a {
          padding: 10px;
          @include flexable(center, space-between, row);

          > span {
            font-size: $text-xx-small;

            &:first-of-type {

              > span {
                display: block;
                font-size: $text-xxx-small;
              }
            }

            &:nth-of-type(2) {
              color: $color-primary;

              b {
                font-weight: bold;
              }
            }

            &:last-of-type {
              display: none;
            }

            &.empty {
              color: $color-danger
            }
          }
        }
      }
    }
  }

  &.labeless-divider {
    .divider {
      margin: 0;
    }

    .dropdown-header {
      display: none;

      ~ li {
        a {
          padding-right: 15px;
        }
      }
    }
  }

  &.dropup {
    .dropdown-toggle {
      .bs-caret {
        .caret {
          &:after {
            content: '\e96e' !important;
            font-family: $font-sallaIcon !important;
          }
        }
      }
    }
  }

  //.bootstrap-select.btn-group .dropdown-header~li>a
  &.input-group-btn {
    .btn {
      padding-right: 10px;
      .filter-option{
        white-space: wrap;
      }
    }
  }

  &--solo {
    .dropdown-toggle {
      border-right: 1px solid !important;
    }
  }

  &--full {
    width: 100% !important;
    border-right: 1px solid $color-gray-200 !important;
  }

  &--with-icon {
    .btn.dropdown-toggle {
      position: relative;
    }
  }

  .bootstrap-select.btn-group .dropdown-header ~ li > a .dropdown-menu {
    padding: 0 !important;

    li {
      a {
        span {
          &.text {
            small {
              &.text-muted {
                display: inline-block;
                font-size: $text-xx-small;
                color: $color-gray-400 !important;
                margin: 0 5px 0 0;
              }
            }
          }
        }
      }
    }
  }

  .bs-searchbox {
    input {
      padding-right: 30px;
      border-color: $color-gray-200 !important;
    }
  }

  &.right-border {
    border-right: none !important;

    .dropdown-toggle {
      border-right: 1px solid $color-gray-200 !important;
    }
  }

  &.labeless-divider {
    .divider {
      margin: 0;
    }

    .dropdown-header {
      display: none;

      ~ li {
        a {
          padding-right: 15px;
        }
      }
    }
  }

  &.dropup {
    .dropdown-toggle {
      .bs-caret {
        .caret {
          color: $color-dark-100;

          &:after {
            content: '\e96d' !important;
            font-family: $font-sallaIcon !important;
          }
        }
      }
    }
  }


  &--solo {
    .dropdown-toggle {
      border-right: 1px solid !important;
    }
  }

  &--full {
    width: 100% !important;
    border-right: 1px solid $color-gray-200 !important;
  }

  &--full-dropdown {
    .dropdown-menu {
      width: 100% !important;
    }
  }

  &--initial {
    position: initial;
  }

  &--borderless {
    .dropdown-toggle {
      border: 0
    }
  }

  &--dark {
    width: auto !important;
    min-width: 130px;

    .btn {
      &.btn-default {
        &.dropdown-toggle {
          border: 1px solid $color-gray-200;
          @include b-radius($b-radius-sm);
          @include transi();

          .caret {
            top: 53%;
            left: 8px;
          }
        }
      }
    }

    .dropdown-menu {
      margin: 0;
      padding: 0;
      top: 100%;
      @include b-radius(0 0 5px 5px);

      > .dropdown-menu {
        background: rgba($color-secondary, 0.1);
        max-height: 150px !important;

        > li {
          background: $color-secondary;
          border-bottom: 1px solid rgba($color-gray-200, 0.08);
          margin: 0;

          > a {
            padding: 8px 12px 10px;
            line-height: 1;
            color: $color-white;
            background: $color-primary-l;
          }

          &.selected {
            a {
              color: $color-primary-l !important;
              background-color: rgba($color-secondary-50, 0.1) !important;
            }
          }

          &:hover {
            a {
              color: $color-primary-l;
            }
          }

          &:last-child {
            border: none;
          }
        }
      }
    }

    &.open {
      .btn {
        &.btn-default {
          &.dropdown-toggle {
            @include b-radius($b-radius-sm $b-radius-sm 0 0);
            color: $color-white;
            background: $color-primary-l;
            border-color: $color-primary-l !important;
          }
        }
      }
    }
  }
}
