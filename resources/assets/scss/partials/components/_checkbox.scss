.rec-checkbox {
  $checkbox-root: &;
  width: auto;
  margin: 0 !important;
  padding: 0;

  input[type=checkbox],
  input[type=radio] {
    display: none;

    +label {
      position: relative;
      cursor: pointer;
      padding-right: 30px;
      font-size: $text-small;
      margin: 0;

      .product-price {
        color: $color-dark-300;
      }

      // main checkbox layout ---
      &:before,
      &:after {
        content: '';
        display: inline-block;
        vertical-align: middle;
        position: absolute;
        top: 0;
        right: 0;
      }

      &:before {
        width: 20px;
        height: 20px;
        border: 1px solid $color-primary-l;
        background: transparent;
        right: 0;
      }

      &:after {
        opacity: 0;
        @include transi();
      }
    }

    &:checked {
      +label {
        &:after {
          opacity: 1;
        }
      }
    }

    &:disabled {
      +label {
        cursor: default;
      }
    }
  }

  input[type=checkbox] {
    +label {
      &:before {
        @include b-radius($b-radius-sm);
      }

      &:after {
        width: 20px;
        height: 20px;
        content: "\ea9d";
        font-family: $font-sallaIcon;
        font-size: $text-small;
        text-align: center;
        color: $color-primary-l;
        top: 0;
        right: 0;
        outline: none;
      }
    }
  }

  input[type=radio] {
    +label {
      &:before {
        @include b-radius(50%);
      }

      &:after {
        width: 10px;
        height: 10px;
        @include b-radius(50%);
        background-color: $color-primary-l;
        top: 5px;
        right: 5px;
      }
    }
  }

  &--small {
    input[type=checkbox] {
      + label {
        font-size: 12px;

        &:before {
          width: 17px !important;
          height: 17px !important;
        }

        &:after {
          width: 17px !important;
          height: 17px !important;
          font-size: 12px;
        }
      }
    }
  }

  // primary radio large
  &--primary-large {
    input[type=radio] {
      +label {
        font-weight: bold;
      }
    }
  }

  &--narrow {

    input[type=checkbox],
    input[type=radio] {
      +label {
        padding-right: 25px;
      }
    }
  }

  // as buttons ---
  &--buttons {
    margin: 0 !important;

    input[type=checkbox],
    input[type=radio] {
      +label {
        padding: 3px 15px 8px;
        text-align: center;
        border: 1px solid $color-gray-200;
        @include b-radius($b-radius-sm);
        margin: 0 5px;
        @include transi();

        &:before,
        &:after {
          display: none;
        }

        &:hover {
          border-color: $color-primary-l;
        }
      }

      &:checked {
        +label {
          color: $color-primary-l;
        }
      }
    }
  }

  &--disabled {
    opacity: .5;

    label {
      cursor: not-allowed !important
    }
  }

  &--active-dashed {
    &.large {
      input[type="checkbox"] {
        +label {
          &:before {
            width: 18px !important;
            height: 18px !important;
          }

          &:after {
            font-size: 14px !important;
            right: 1px !important;
            top: 11.5px !important;
            color: #fff !important;
          }
        }
      }
    }

    input[type=checkbox] {
      +label {

        &:after {
          content: '\ed91';
          opacity: 1;
          visibility: visible;
        }
      }

      &:checked {
        +label {
          &:after {
            content: '\ea9d'
          }
        }
      }
    }
  }


  // only checkbox without label text ---
  &--solo {
    &.rec-checkbox {
      &--large {
        width: 22px;
        height: 22px;

        input[type=checkbox],
        input[type=radio] {
          +label {
            padding-right: 22px;
            height: 22px;

            &:before {
              width: 22px;
              height: 22px;
            }
          }
        }
      }
    }
  }

  // primary radio large
  &--primary-large {}

  // Boxed Radio
  &--boxed {
    input[type=radio] {
      +label {
        &:after {
          top: 14px;
          right: 14px;
        }
      }
    }

    input[type=checkbox] {
      +label {
        &:after {
          top: 9px;
          right: 9px;
        }
      }
    }

    input[type=checkbox],
    input[type=radio] {
      +label {
        @include flexable();
        width: 100%;
        height: 100%;
        min-height: 90px;
        @include b-radius($b-radius-sm);
        border: 1px solid $color-gray-200;
        padding: 30px 0;
        text-align: center;
        margin: 0;

        img {
          max-width: 60px;
          max-height: 40px;
        }

        &:before {
          top: 9px;
          right: 9px;
        }
      }

      &:checked {
        +label {
          border-color: rgba($color-primary-l, 0.5);
        }
      }
    }

    &.default-card {

      input[type=checkbox],
      input[type=radio] {
        +label {
          min-width: 280px;
          max-height: 80px;
          justify-content: flex-start;

          &:before,
          &:after {
            content: none
          }
        }

        &:checked {
          +label {
            border-color: $color-secondary;

            .title,
            .icon {
              color: $color-secondary;
            }
          }
        }
      }
    }
  }

  // boxed border radio
  &--bordered-option {

    input[type=checkbox],
    input[type=radio] {
      display: none;

      +label {
        @include flexable(center, flex-start, row);
        min-height: 60px;
        border: 1px solid $color-gray-300;
        @include b-radius($b-radius-sm);
        @include transi();
        text-align: center;
        margin: 0;
        padding-right: 50px;
        padding-left: 15px;
        cursor: pointer;

        .payment-logos {
          height: 40px;
          background-size: 100% !important;

          &.mada,
          &.visa-master,
          &.apple-pay {
            height: 30px;
          }

          &.visa-master {
            width: 90px;
          }

          &.mada {
            width: 60px;

            &.mada--margin {
              margin-right: 5px;
            }
          }

          &.apple-pay {
            width: 55px;
          }
        }

        &:before,
        &:after {
          content: "";
          top: 50%;
          transform: translateY(-50%);
          @include b-radius(50%);
          @include transi();
        }

        &:before {
          width: 22px;
          height: 22px;
          right: 13px;
          border-color: $color-secondary;
        }

        &:after {
          visibility: visible;
          background: $color-secondary;
          width: 12px;
          height: 12px;
          right: 18px;
          opacity: 0;
        }
      }

      &:checked {
        +label {
          background-color: rgba($color-secondary, 0.05);
          border-color: $color-secondary;

          &:before {
            border-color: $color-secondary;
          }

          &:after {
            opacity: 1;
          }
        }
      }
    }
  }

  &--light-primary {

    input[type=checkbox],
    input[type=radio] {
      &:checked {
        +label {
          &:before {
            border-color: $color-secondary-l;
          }

          &:after {
            background-color: $color-secondary-l;
          }
        }
      }
    }
  }
}

.checkbox {

  &.multiple-product-options {
    .checker {
      span {
        top: -4px
      }
    }
  }
}

#modal_coupon {
  .checker {
    top: 9px;
  }
}
