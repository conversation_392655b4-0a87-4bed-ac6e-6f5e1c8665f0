#heading_help_btn {
  i {
    transition: transform 0.3s;
    display: inline-block;
  }

  &.opened {
    i {
      transform: rotate(180deg);
    }
  }
}

// panel
.help {
  position: absolute;
  background: #fff;
  top: 40px;
  left: 0px;
  z-index: 6666;
  width: 400px;
  min-height: 550px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.05);
  border-radius: var(--b-radius);
  ;
  visibility: hidden;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s;

  @media (max-width: 650px) {
    min-width: 300px;
    width: calc(100vw - 46px) !important;
    left: -5px;

    #helpExpand {
      display: none;
    }
  }

  &.active {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }

  .loader-wrap {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: calc(50% - 40px);
    right: 0;
    width: 100%;
    transform: translateY(-50%);
  }

  #helpExpand {
    margin-right: auto;

    .text::before {
      font-size: 14px;
      content: 'تكبير';
    }
  }

  &.expanded {
    width: 600px;

    #helpExpand {
      .icon::before {
        content: '\e947';
      }

      .text::before {
        content: 'تصغير';
      }
    }

    .single-title {
      max-width: initial;
    }
  }

  &__single-heading {
    display: flex;
    align-items: center;

    .single-title {
      max-width: 210px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .btn {
      padding: 0;
    }

    i {
      font-size: 24px;

      &.sicon-share {
        font-size: 15px;
        margin-right: 5px;
      }
    }
  }

  &__heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--color-primary);
    background: var(--color-secondary-50);
    padding: 8px 20px;
    min-height: 59px;

    &.disabled {
      opacity: .7;
      pointer-events: none;
    }
  }

  &__search {
    position: relative;
    padding: 20px;
    box-shadow: 0px 5px 20px rgba(17, 17, 17, 0.03);

    .input-group-btn {
      position: absolute;
      margin-right: -10px;
    }

    input[type='text'] {
      max-width: calc(100% - 38px);
      height: 38px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      transition: width 0.3s;
    }

    .clear-btn {
      display: none;
      position: absolute;
      top: 20px;
      left: 17px;
      color: #f55157;
    }

    .searched-icon {
      display: none;
      position: absolute;
      top: 30px;
      right: 30px;
      color: #666;
    }

    &.searched {
      .input-group-btn {
        display: none !important;
      }

      .searched-icon {
        display: block !important;
      }

      .clear-btn {
        display: block;
      }

      input[type='text'] {
        max-width: 100%;
        padding-right: 35px;
        padding-left: 43px;
      }
    }
  }

  &__body {
    padding: 20px;

    li {
      margin-bottom: 0;
      font-size: 13px;
    }

    a {
      display: block;
      color: #444;
      margin-bottom: 10px;
      transition: all 0.3s;
      line-height: 24px;
      transition: color 0.3s;

      &:hover {
        color: var(--color-secondary) !important;

        i {
          color: var(--color-secondary) !important;
        }
      }
    }

    small {
      color: #999999;
      font-size: 13px;
    }
  }

  &__main-sections {
    // ul
    max-height: 530px;
    overflow: auto;
    padding-left: 15px;

    i {
      color: #999999;
      margin-left: 16px;
      font-size: 15px;
      transition: color 0.3s;

      &.collapse-icon {
        margin: 0;
      }
    }

    >li {
      border-bottom: 1px solid var(--color-gray-200);
      border-width: 0 0 1px 0;
      border-radius: 0;
      font-size: 15px;
      padding: 15px 0;
      margin: 0;

      &:last-child {
        border: none;
        padding-bottom: 0;
      }

      >ul {
        padding-right: 36px;
      }

      >a {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &[aria-expanded='true'] {
          color: var(--color-secondary);

          i {
            color: var(--color-secondary);
          }

          .collapse-icon::before {
            content: '\ed91';
          }
        }
      }
    }
  }

  &__results {
    .title {
      margin-bottom: 0;
    }

    small {
      display: block;
      margin-bottom: 20px;
    }

    ul {
      max-height: 300px;
      overflow: auto;
    }

    li {
      font-size: 15px;
      padding: 5px 0;
      margin: 0;
    }
  }

  &__no-results {
    min-height: 390px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    i {
      font-size: 50px;
      color: #ddd;
      margin: 0 0 10px 0;
    }

    h3 {
      font-size: 18px;
      margin: 0;
      line-height: 35px;
    }
  }

  &__single {
    max-height: calc(100vh - 251px);
    min-height: 100px;
    overflow: auto;
    padding-left: 10px;

    p,
    strong {
      line-height: 24px;
    }

    img {
      max-width: 100%;
      height: auto;
    }

    .post-content a {
      color: var(--color-secondary) !important;
      transition: all 0.3s ease 0s;
    }

    span.fr-video iframe {
      width: 100% !important
    }
  }

  // custom scroll bar
  ::-webkit-scrollbar {
    width: 3px;
  }

  ::-webkit-scrollbar-track {
    border-radius: 0;
    background: #eee;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 0px;
    background: var(--color-secondary);
  }
}


// help btn in vue apps
.custome-help-btn {
  display: flex;
  justify-content: flex-end;
  height: 0;

  .relative {
    top: 18px;
    z-index: 11;
  }

  &.extra-help {
    @include mediaMinWidth($screen-tablet-p) {
      >div {
        height: 36px;
        @include flexable(center, center, row);
        border: 1px solid $color-secondary-50;
        @include b-radius(999px);
        padding: 5px 12px 5px 5px;

        .help-title {
          display: block !important;
          font-weight: 500;
          margin-left: 15px;
        }
      }
    }
  }
}