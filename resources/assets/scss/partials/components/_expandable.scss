.expandable-cell {
  $expandable-root: &;

  &__head {
    @include flexable(center, flex-start, row);
    gap: 5px;
    padding-right: 30px !important;
    position: relative;
    cursor: pointer;

    span {
      b, small {
        display: inline-block;
        vertical-align: middle;
      }

      small {
        max-width: 80px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    &:before {
      content: '\e90a';
      font-family: 'sallaicons', serif;
      color: $color-primary;
      font-size: 18px;
      position: absolute;
      top: 2px;
      right: 0;
      opacity: 0.8;
    }

    @include mediaMinMaxWidth(770px, 1310px) {
      &:after {
        top: 30px;
      }
    }
  }

  &__content {
    @include flexable(flex-start, flex-start, column);
    padding: 10px 30px 0 0;
    position: relative;
    max-width: 280px;
    white-space: normal;
    overflow: hidden;
    display: none;
    text-align: right;

    &.has-waypoint {
      gap: 10px;

      .pickup,
      .destination {
        position: relative;
      }

      .pickup {
        &:before,
        &:after {
          content: '';
          display: block;
          position: absolute;
        }

        &:before {
          width: 10px;
          height: 10px;
          @include b-radius(50px);
          background: $color-gray-400;
          top: 10px;
          right: -24px;
          z-index: 2;
        }

        &:after {
          width: 1px;
          height: calc(100% - 10px);
          border-right: 1px dashed $color-gray-200;
          top: 25px;
          right: -20px;
        }
      }

      .destination {
        &:before {
          content: '';
          display: block;
          position: absolute;
          width: 10px;
          height: 10px;
          @include b-radius(50px);
          background: $color-gray-300;
          top: 10px;
          right: -24px;
          z-index: 2;
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      max-width: unset;
    }
  }

  &.expanded {
    #{$expandable-root} {
      &__head {
        &:before {
          content: '\ed8f';
          opacity: 1;
        }

        @include mediaMinMaxWidth(770px, 1310px) {
          &:after {
            top: 18px;
          }
        }
      }

      &__content {
        display: flex;
      }
    }
  }
}