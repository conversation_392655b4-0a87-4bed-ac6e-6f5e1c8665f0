// plans colors - optional to change later ---
$basic-color: #76E8CD;
$pro-color: #f9c02e;
$plus-color: #37a8d4;

.navbar-collapse {
  .btn {
    &-header-nav {
      height: 38px;
      width: 38px;
      text-align: center;
      line-height: 14px;

      .changelog-headsup {
        &::before, &::after {
          top: -2px;
        }
      }
    }
  }

  @include mediaMaxWidth($screen-laptop-small) {
    .btn-top-nav {
      display: none
    }
  }

  @include mediaMinWidth($screen-laptop-small) {
    .extra-links {
      display: none;
    }
  }
}

.sidebar-user {
  padding: 20px 18px;

  .media {
    @include flexable(center, center, column);

    > div {
      width: 100%;
    }

    &-left {
      position: relative;
      @include flexable(flex-start, flex-start, row);

      .store-img-wrapper {
        width: 65px;
        position: relative;
        margin-left: 15px;
        flex: 0 0 65px;
      }

      .badge {
        &.plan-name {
          position: absolute;
          bottom: -5px;
          right: 50%;
          transform: translateX(50%);
          padding: 2px 10px 4px;
          font-size: $text-xx-small;
          line-height: 12px;
          opacity: 1 !important;
          &:hover {
            color: $color-primary-l;
          }
        }
      }
    }

    &-body {
      margin: 5px 0 8px;
    }

    &-footer {
      .btn {
        &-link {
          @include transi();
          text-decoration: underline;
          font-size: $text-x-small;
          color: $color-white;
        }
      }
    }

    .store-name-side {
      line-height: 1.4;
    }

    .store-link {
      @include flexable(center, center, row);
      width: fit-content;
      margin: 10px 0 0;
      @include b-radius(50px);
      border: 1px solid $color-secondary-50;

      .dropdown-menu {
        min-width: unset;
        width: 100%;
        padding: 0;
        overflow: hidden;
        @include b-radius($b-radius-sm);
        margin-top: 5px;

        .dropdown-link {
          margin: 0;

          a {
            font-size: $text-xx-small;
            padding: 8px 10px;

            i {
              display: inline-block;
              vertical-align: middle;
              font-size: inherit;
              margin-left: 5px;
              opacity: 0.8;
            }
          }

          &:not(:last-child) {
            border-bottom: 1px solid $color-gray-200;
          }
        }
      }

      .btn {
        * {
          color: $color-white;
        }

        i {
          font-size: $base-size;
        }

        &.visit-store, &.share {
          height: 26px;
        }

        &.visit-store {
          padding: 3px 10px 3px 5px;
          font-size: $text-xx-small;
          color: $color-white;
          @include b-radius(0 25px 25px 0);
          border: none;
          @include transi();
          background-color: transparent;

          i {
            display: inline-block;
            font-size: $text-x-medium;
          }
        }

        &.share {
          padding: 0 10px;
          @include b-radius(25px 0 0 25px);
          background: $color-secondary-50;
          border-right: 1px solid rgba($color-white, 0.2);

          i {
            display: inline-block;
            font-size: $text-x-small;
            transform: translateY(-1px);
            color: $color-primary;
            @include transi();
          }

          &.copied {
            i {
              &:before {
                content: '\ea9d';
              }
            }
          }
        }
      }
    }
  }

  &--basic {
    .media {
      &-left .badge {
        background-color: $basic-color;
      }
    }
  }

  &--plus {
    .media {
      &-left .badge {
        background-color: $plus-color;
      }

      &-body .store-link {
        .btn {

          &.visit-store {
            background-color: $plus-color;
          }

          &.share {
            background-color: darken($plus-color, 10%);
          }
        }
      }
    }
  }

  &--pro {
    .media {
      &-left .badge {
        background-color: $pro-color !important;
        color: $color-dark-300;
      }

      &-body .store-link {
        .btn {
          * {
            color: $color-dark-300;
          }

          &.visit-store {
            background-color: $pro-color !important;
            color: $color-dark-300;
          }

          &.share {
            background-color: darken($pro-color, 10%);
          }
        }
      }
    }
  }

  &--special {
    @extend .sidebar-user--pro;

    .media {
      &-left {
        flex-direction: column;
        align-items: center;

        .store {
          &-img-wrapper {
            margin: 0 0 5px 0;

            img {
              border: 2px dashed $pro-color;
              @include b-radius(50%);
            }

            .badge {
              bottom: auto;
              top: -5px;
              color: darken($pro-color, 50%) !important;

              &:before {
                content: '\eb20';
                font-family: $font-sallaIcon;
                margin-left: 5px;
                display: inline-block;
                transform: translateY(2px);
              }
            }
          }

          &-name-plan {
            h6 {
              font-size: $text-medium;
              text-align: center;
            }

            > div {
              margin-top: 10px;
              @include flexable(center, space-between, row);

              .store-link {
                margin-top: 0;
              }

              .tooltip-toggle {
                > span {
                  height: 28px;
                  padding: 3px 10px;
                  border: 1px solid rgba($color-white, 0.2);
                  @include b-radius(50px)
                }

                .tooltip-content {
                  width: 230px;
                  box-shadow: 0 0 10px 10px rgba(black, 0.05);

                  .text-plain {
                    word-break: break-word;
                  }

                  &:after {
                    right: 30px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
