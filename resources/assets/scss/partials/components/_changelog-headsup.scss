.changelog-headsup {
  margin-right: 15px;
  a {
    display: block;
    padding: 0 15px 0 0 !important;
    margin: 18px 0 0 5px;
    font-size: 15px;

    &:hover {
      color: darken($color-danger, 10%) !important;
      background: transparent !important;
    }
  }

  &:before, &:after {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    @include b-radius(50%);
    background: $color-danger;
    margin-left: 5px;
    position: absolute;
    top: 25px;
    right: 0;
    border: 1px solid darken($color-danger, 10%);
    pointer-events: none;
  }

  &:after {
    animation: fadeScale infinite 1s;

  }

  @include mediaMaxWidth($screen-laptop) {
    margin: 0;
    a {
      display: none !important;
    }
    &:before, &:after {
      top: 15px;
      right: 15px;
      z-index: 99;
    }
  }
  @include mediaMaxWidth($screen-tablet-p) {
    &:before, &:after {
      right: 5px;
    }
  }


}
