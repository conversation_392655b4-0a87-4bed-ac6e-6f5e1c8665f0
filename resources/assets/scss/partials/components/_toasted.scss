
  .toasted-container.top-left{
    top: 20px !important;
    left: 30px !important;
    z-index: 9999999999999 !important;
    @media (max-width: 600px){
      left: 0;
      bottom: 70px;
      top: auto;

      .toasted.toasted-primary {
        min-height: 30px;
        padding: 4px 20px 7px;
        line-height: 1.4;
        font-size: 13px;
        position: absolute;
        top: 4px;
        width: 100%;
        margin: 0;
        box-shadow: none;
      }
    }
  }

  .toasted{
    padding: 6px 20px 10px;

    i{
      margin-left: 5px;
    }
  }


