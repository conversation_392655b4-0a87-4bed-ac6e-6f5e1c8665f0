@import 'vue-good-table/dist/vue-good-table.css';

table.vgt-table {
  border: none !important;
  font-size: $text-small;

  thead th {
    background: $color-gray-25 ;
    font-size: $text-x-small;
    border-bottom: $color-gray-200;

    &.sortable {
      button {
        left: unset;
      }
    }
  }

  tr {
    td {
      padding: 16px 20px 16px 20px;
      border-bottom: 1px solid $color-gray-200;
      vertical-align: unset !important;
      color: $color-dark-300;
    }

  }

  thead {
    .vgt-right-align {
      span {
        white-space: nowrap;
      }
    }

    th.sorting-asc button {
      &:after {
        border-bottom: 5px solid $color-secondary;
      }

    }

    th.sorting-desc button {
      &:before {
        border-top: 5px solid $color-secondary;
      }
    }

  }

  .products-left {
    position: relative;

    &>li {
      background-size: cover;
      background-position-x: center;
      border-radius: 50%;
      height: 30px;
      margin-left: -10px;
      position: relative;
      width: 30px;
      border: 2px solid $color-gray-200;
    }
  }

  @media (max-width: 767px) {

    th {
      display: none;
    }

    tr {
      border-bottom: 1px $color-gray-200 solid;
      padding: 20px !important;
    }

    td {
      display: flex;
      border-bottom: none !important;

      &:not(:first-child) {
        justify-content: space-between;
        align-items: center;
        padding: 7.5px 20px !important;
      }

    }

  }

  body.dark table.vgt-table {
    background-color: #272626;
    color: $color-gray-400;

    thead th {
      background: #2c2c2c;
      color: $color-gray-400;
    }

    td {
      color: $color-gray-400;

    }

    tr td {
      border-bottom: 1px solid $color-dark-400;
    }
  }


  // pagination
  .vgt-wrap__footer {
    background: $color-gray-50;
    border: none;
    font-size: unset;

    .footer__row-count {
      &__select {
        padding-left: 15px;
        padding-right: 5px;
      }

      &:after {
        right: unset;
        left: 8px;
        top: 40%;
      }
    }

    .footer__navigation__page-btn .chevron {
      color: #444;

      &.left {
        &:after {
          border-right: 6px solid $color-secondary-50;
        }
      }

      &.right {
        &:after {
          border-left: 6px solid $color-secondary-50;
        }
      }
    }

    .footer__row-count {
      display: flex;
      align-items: center;

      label {
        margin: 0;
      }
    }


  }

  body.dark .vgt-wrap__footer {
    background: #2c2c2c;
    color: $color-gray-400;

  }
}

.logo-box {
  border-radius: $b-radius-sm;
  background-color: $color-white;
  border: 1px solid $color-gray-50;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.company-logo {
  width: 20px;
  height: 20px;
  @include b-radius($b-radius-sm);
  object-fit: contain;
  background-repeat: no-repeat;
}