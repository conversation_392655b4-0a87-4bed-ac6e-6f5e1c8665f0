.rec-list { // @osama - needs revisit ---
  @include flexable(flex-start, flex-start);
  @include strip-ul();
  position: relative;

  &--table {
    width: 100%;

    > div > li {
      border-bottom: 1px dashed $color-gray-200;

      &:not(:last-child) {
        border-bottom: 1px dashed $color-gray-200;
      }
    }

    li {
      width: 100%;
      @include flexable(center, flex-start, row);

      > * {
        padding: 10px 20px;
        flex: 1;

        @each $opt in 10, 15, 25, 50, 75 {
          &.w-#{$opt} {
            flex: 0 0 #{$opt}+ '%';
          }
        }
      }

      &.table-head {
        color: $color-dark-100;
        background: $color-gray-100;
        border: none !important;
      }

      &:last-child {
        border: none;
      }

      &.empty-sides {
        @include mediaMaxWidth($screen-tablet-l) {
          > * {
            &:first-child {
              padding-right: 0;
            }

            &:last-child {
              padding-left: 0;
            }
          }
        }
      }
    }

    // table list customization ---
    &.invoice-list {
      margin: 0 0 30px 0;

      li {
        &.table-head {
          border: none;
        }

        &.table-footer {
          flex-direction: column;
          align-items: flex-start;
          padding-right: 60%;
          white-space: nowrap;
          border: none;

          > * {
            @include flexable(center, space-between, row);
            width: 100%;
            border-bottom: 1px dashed $color-gray-200;
          }

          @include mediaMaxWidth($screen-desktop-small) {
            padding-right: 50%;
          }

          @include mediaMaxWidth($screen-phones) {
            padding-right: 0;
          }
        }
      }
    }

    // responsive version --- for mobile use
    &.responsive {
      @include mediaMaxWidth($screen-laptop-small) {
        > div > li {
          flex-direction: column;
          @include b-radius($b-radius-sm);
          margin-bottom: 20px;
          text-align: left;
          padding: 5px;

          > * {
            width: 100%;
            @include flexable(flex-start, space-between, row);

            &:before {
              content: attr(data-title);
              display: inline-block;
              font-size: $text-small;
              text-align: right;
              color: $color-dark-200;
              padding-left: 10px;
            }
          }
        }

        .table-head {
          display: none;
        }
      }
    }

    &.subscriptions-list {
      > li {
        position: relative;

        @include mediaMaxWidth($screen-desktop-small) {
          span {
            flex: 1 !important;
          }
        }

        @include mediaMaxWidth($screen-phones) {
          flex-direction: column;
          padding: 5px;
          @include b-radius($b-radius-sm);
          border: 1px solid $color-gray-200 !important;
          margin-bottom: 10px;

          span {
            width: 100%;
            padding: 5px;
            font-size: $text-xx-small;
            text-align: right !important;

            .rec-price-wrapper {
              justify-content: flex-start !important;
            }

            &.more-opt {
              text-align: left !important;
              position: absolute;
              top: 5px;
              left: 5px;
            }

            &:first-child {
              font-size: $text-x-small;
              padding: 5px 5px 10px 40px;
              border-bottom: 1px solid $color-gray-200;
            }
          }

          &.table-head {
            display: none;
          }

          &:last-child {
            margin: 0;
          }
        }
      }
    }

    &-style {
      li {
        border-bottom: 1px solid $color-gray-200;

        > div {
          padding: 12px 20px;
          flex: initial;

          &:first-child {
            flex: auto;
            padding-left: 10px;
          }

          @include mediaMaxWidth($screen-phones) {
            flex-direction: column;
            padding: 5px;
            @include b-radius($b-radius-sm);
            border: 1px solid $color-gray-200 !important;
            margin-bottom: 10px;

            span {
              width: 100%;
              padding: 5px;
              font-size: $text-xx-small;
              text-align: right !important;

              .rec-price-wrapper {
                justify-content: flex-start !important;
              }

              &.more-opt {
                text-align: left !important;
                position: absolute;
                top: 5px;
                left: 5px;
              }
            }
          }

          &:last-child {
            padding-right: 10px;
          }
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  &--vertical {
    flex-direction: column;
    -webkit-flex-direction: column;
  }

  &--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
  }

  &--align-left {
    justify-content: flex-end;
  }

  &--wizard {
    counter-reset: rec-wizard-counter;
    flex-wrap: wrap;

    @include mediaMaxWidth($screen-phones) {
      margin-bottom: 10px !important;
    }

    &.smaller {
      .rec-step {
        a {
          padding: 5px 10px 7px 20px;
        }

        &.done {
          a {
            padding-left: 50px;
          }
        }
      }
    }
  }

  &--f-auto {
    div,
    li {
      flex: auto;
    }
  }

  &--f-unset {
    flex: unset !important;
  }

  &--warp {
    flex-wrap: wrap;
  }

  &--unwrap {
    flex-wrap: unset;
  }

  &--start {
    > * {
      width: auto;
    }
  }

  &--space-between {
    justify-content: space-between;
  }

  &--justify-center {
    justify-content: center;
  }

  &--space-around {
    justify-content: space-around;
  }

  &--align-center {
    align-items: center;
  }

  &--no-wrap {
    flex-wrap: nowrap;
  }

  &--row-reverse {
    flex-direction: row-reverse;
  }

  &--wide {
    li {
      width: 100%;
    }
  }

  &-shipping-role {
    li {
      padding: 18px 20px;

      &:not(:last-of-type) {
        border-bottom: 1px solid $color-gray-25;
      }
    }
  }

  &.customer-request-info {
    li {
      width: 100%;
      padding: 15px 0;
      border-bottom: 1px solid $color-gray-200;

      img {
        &:not(:last-of-type) {
          margin-left: 34px;
        }
      }
    }
  }

  &-qty {
    li {
      @include flexable(center, space-between, row);
      width: 100%;

      span {
        font-size: $text-x-small;
        color: $color-dark-300;
      }

      border-top: 1px solid $color-gray-200;
      padding: 12px 0;

      &:last-of-type {
        padding-bottom: 0;
      }
    }
  }

  &--draggable {
    margin-bottom: 20px;

    > li {
      width: 100%;
      position: relative;
      @include flexable(center, flex-start, row);
      padding: 8px 13px;
      @include b-radius(2px);
      border: 1px solid $color-gray-300;

      &:not(:last-of-type) {
        margin-bottom: 15px;
      }

      &:before {
        content: '';
        position: absolute;
        background-color: $color-gray-200;
        width: 1px;
        height: 60%;
        @include center-v;
        right: 33px;
      }

      .text {
        font-size: $text-x-small;
        color: $color-dark-200;
        margin-right: 10px;
      }

      > i {
        font-size: $text-x-small;
        color: $color-dark-200;

        &.move-handler {
          cursor: pointer;
          font-size: 11px;
        }
      }

      &.sortable-chosen {
        border-color: $color-gray-200;
        background: $color-gray-25;
      }
    }
  }

  &--numeric {
    counter-reset: rec-list--numeric;

    li {
      position: relative;
      counter-increment: rec-list--numeric;
      margin-bottom: 7px;

      > * {
        display: inline-block;
        margin: 0;
      }

      &:before {
        content: counter(rec-list--numeric);
        display: inline-block;
        vertical-align: top;
        margin-left: 10px;
      }
    }

    &.checked-list {
      li {
        padding-right: 25px;

        &:before {
          content: '\ea9b';
          font-family: $font-sallaIcon;
          font-size: $base-size;
          line-height: 1;
          vertical-align: middle;
          position: absolute;
          top: 6px;
          right: 0;
          margin: 0;
        }
      }
      &.lg {
        li {
          padding-right: 40px;
          margin-bottom: 15px;
          &:before {
            font-size: 20px;
            top: 4px;
          }
        }
      }

      &.light {
        li {
          padding-right: 30px;
          &::before {
            content: '\ea9d';
            color: $color-primary !important;
            background-color: $color-secondary-25;
            font-size: $text-small;
            width: 20px;
            height: 20px;
            border-radius: 999px;
            text-align: center;
            line-height: 20px;

          }

          &.plus-item {
            &:before {
              content: '\e90c';
              background-color: #FFE7C7;
              color: #A46F29 !important;
            }
          }
        }
      }
    }
  }


  &__note {
    font-size: $text-xx-small;
    color: $color-danger;

    li {
      width: 100%;
    }

    &:before {
      content: '\ecdf';
      font-family: $font-sallaIcon;
      margin-left: 10px;
      font-size: $base-size;
      position: relative;
      top: 3px;
    }
  }

  &--bordered {
    > li {
      width: 100%;
      padding: 20px 10px;
      border-bottom: 1px solid $color-gray-200;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      &:last-of-type {
        border-bottom: none;
        padding-bottom: 0;
      }

      &:first-child {
        padding-top: 0;
      }
    }

    &.light {
      > li {
        padding: 1px 0;
      }
    }

    &.wrapped {
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm);

      > li {
        padding: 10px !important;
      }
    }

    &.thick-border {
      border-width: 2px;
    }

    &.list-unpadded {
      padding: 20px;

      > li {
        padding: 0 !important;
      }
    }

    &.light-border {
      border-color: $color-gray-200;

      > li {
        border-color: $color-gray-200;
      }
    }

    &.tight {
      > li {
        padding: 10px 0;
      }
    }
  }

  &--bg {
    padding: 20px 15px;
    @include b-radius($b-radius-sm);
    background: $color-gray-25;

    &.light {
      padding: 10px 15px;
    }
  }

  &--relaxed {
    > li {
      padding: 15px 0 10px;
      border-bottom: 1px solid $color-gray-200;

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
        border-bottom: none;
      }
    }
  }


  &--products {
    & > span {
      font-size: $text-small;
      &::before {
        display: inline-block;
        content: '\ed91';
        font-family: $font-sallaIcon !important;
        color: $color-dark-100;
        position: relative;
        top: 3px;
        margin-left: 5px;
      }
    }
  }

  &--grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;
  }

  &--step-static {
    &__step {
      @include flexable(center, flex-start, row);
      > span {
        font-size: $text-small;
        width: 40px;
        height: 40px;
        margin-left: 10px;
        background-color: white;
        border: 1px solid $color-gray-300;
        display: inline-block;
        @include b-radius(50%);
        text-align: center;
        line-height: 40px;
        b {
          color: $color-dark-200;
          font-weight: bold;
        }
        i {
          display: none;
          color: $color-primary;
        }
      }

      > p {
        color: $color-dark-200;
        display: inline-block;
        font-size: $text-medium;
        font-weight: 500;
        margin: 0;
      }

      &.active {
        > span {
          border-color: $color-secondary-50;
          background-color: $color-secondary-50;
          b{
            color: $color-primary; 
          }
        }
        > p {
          color: $color-primary;
        }
      }

      &.done {
        > span {
          b {
            display: none;
          }
          i {
            display: block;
          }
        }
        > p {
          color: $color-primary;
        }
      }
    }
  }

  &.controls {
    &.fixed-width {
      @include mediaMinWidth($screen-desktop-large) {
        max-width: 1000px;
      }
    }

    .btn {
      &-save {
        flex: auto;
      }

      &-delete {
        flex: 0 020%;
        margin-right: 20px;
      }
    }

    &-sm {
      @include mediaMaxWidth($screen-phablet) {
        flex-direction: column;
        .btn {
          &-delete {
            flex: auto;
            margin: 15px 0 0;
          }
        }
      }
    }
  }

  &.rec-sp {
    li {
      display: inline-flex;
      justify-content: space-between;
    }
  }

  &.products-control {
    justify-content: flex-end;
    flex-wrap: wrap;

    .nav {
      display: block !important;
    }

    .products-checked .dropdown-backdrop {
      display: none !important;
    }

    @include mediaMaxWidth(1350px) {
      > button {
        display: none;
      }
    }
  }

  &.product-options-list {
    max-width: 600px;

    .text-muted {
      color: $color-dark-100 !important;
    }

    @include mediaMaxWidth($screen-laptop) {
      li.rec-list {
        flex-direction: column;
        align-items: flex-start;

        span:first-of-type {
          margin-bottom: 10px;
        }
      }
    }
  }

  &.b-bottom {
    li {
      border-bottom: 1px solid $color-gray-100;

      &:last-child {
        border-bottom: 0;
      }
    }
  }

  &.orders-export,
  &.orders-delete {
    padding-left: 10px;

    li {
      padding-bottom: 10px;
      margin-bottom: 10px;
    }
  }

  //note: could be delete but it used
  &.nowrap {
    flex-wrap: nowrap !important;
  }

  &--nowrap {
    flex-wrap: nowrap;
  }
}

.product-action-btn-list {
  > li {
    a {
      &:hover {
        background: transparent !important;
      }
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {
    li {
      .btn.switch {
        display: none;
      }
    }
  }
}

.product-group-list {
  @include strip-ul();

  li {
    list-style: none;
    font-size: $text-x-small;
    font-weight: bold;
    color: $color-dark-200;
    position: relative;
    padding-right: 33px;

    &:not(:last-of-type) {
      margin-bottom: 12px;
    }

    img {
      width: 40px;
      height: 40px;
      margin-left: 10px !important;
    }

    a {
      background-color: $color-danger;
      @include b-radius(50%);
      padding: 4px;
      position: absolute;
      right: 0;
      @include centerY();

      i {
        color: $color-white;
        font-size: $text-xx-small;
      }
    }
  }
}

.flex-auto {
  flex: auto !important;
}
