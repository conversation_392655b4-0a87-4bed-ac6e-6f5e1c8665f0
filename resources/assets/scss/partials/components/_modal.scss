.modal {
  iframe {
    width: 100% !important;
  }

  .payment-logo,
  .salla-pay-logo {
    width: 90px;
  }

  .pickr {
    right: -5px;

    button {
      width: 17px;
      height: 17px;
    }
  }

  // override legacy code ---
  .form-group {
    label {
      padding-top: 0;
    }
  }

  .modal {
    &-content {
      // to hide icons from tab nav in small screen
      @include mediaMaxWidth($screen-phones) {
        .nav {
          li i {
            display: none;
          }
        }
      }
    }

    &-header {
      &.bg-info {
        padding: 15px 20px !important
      }
    }

    &-footer {
      .btn {
        &:before,
        [class^="sicon-"] {
          display: none !important;
        }
      }

      .btn-save {
        color: var(--color-primary-l) !important;
        background-color: var(--color-secondary-50);
        border-color: var(--color-secondary-50);
      }
    }
  }

  &#modal {
    &_coupon {
      .checkbox {
        label {
          padding-top: 6px;
        }

        .checker {
          top: 7px;
        }
      }

      .selectize-input {
        padding: 0;

        input {
          margin: 0;
        }
      }

      .ui.calendar {
        padding: 0;
      }
    }

    &_page {
      .form-group {
        &.edit {
          > p {
            position: absolute;
            top: 34px;
            right: 12px;
            background: $color-white;
            width: 97%;
            pointer-events: none;
          }
        }
      }
    }

    //modal_bank_account
    &_bank_account {
      .sarie {
        img {
          max-width: 40px;
          position: relative;
          top: -2px;
        }
      }
    }

    //Upload product image
    &_product_photos {
      .filepond--list-scroller {
        height: 40px !important;
        overflow-y: auto;
        -webkit-mask: unset;
        margin-right: 10px;
      }

      .filepond--credits {
        display: none
      }
    }

    // Products Categories
    &_product_category {
      .form-group .input-group {
        @include flexable(center, flex-start, row);

        .input-group-addon {
          width: 32px;
        }

        .form-control {
          flex: auto;
        }

        .input-group-btn {
          @include flexable(flex-start, flex-start, row);
          width: auto;

          .rec-checkbox {
            position: relative;
            top: 6px;
            right: 7px;
          }
        }
      }
    }

    //Tax number
    &_store_tax_number {
      #tax_number_review span {
        letter-spacing: 1px;
      }
    }

    // Shipping Companies Modal
    &_company_form {
      .form-group {
        label {
          padding-top: 0;
        }
      }

      .ui.search {
        .default.text {
          position: absolute;
          top: 9px
        }

        .text {
          top: 4px;
        }

        .dropdown.icon {
          opacity: 1;
          left: 12px !important;

          &:before {
            left: 3px !important;
            color: $color-dark-100;
            font-size: $text-medium;
          }
        }
      }

      img {
        &.shipping-logo {
          width: auto !important;
          margin-left: 1rem !important;
        }
      }
    }

    // Product Options
    &_product_options {

      .option-wrapper {
        .form-group {
          flex: 0 0 20%;
          margin: 0 0 0 20px;

          &:first-of-type {
            flex: auto;
            margin: 0 0 10px 20px
          }
        }

        .remove-button-option {
          right: 0;
        }
      }

      .form-group {
        margin-bottom: 10px;

        label {
          color: $color-dark-300;
          font-size: $text-x-small;
        }
      }

      .modal-footer {
        padding: 14px 12px;
      }

      #digital_list {
        .option-section {
          border: 1px solid $color-gray-200;
          @include transi(border-color);

          &.new {
            border-color: $color-primary;
          }
        }

        .btn-primary {
          background: $color-primary;
          border-color: $color-primary;
          cursor: pointer;

          i {
            &:before {
              content: '\eae0';
              font-family: $font-sallaIcon !important;
              position: relative;
              top: -3px
            }
          }

          &:active,
          &:focus {
            background: $color-primary !important;
            border-color: $color-primary;
          }
        }

        .bootstrap-select {
          width: 25%;

          .btn {
            border-left: none;
            border-right: 1px solid;
            padding: 8px 39px 7px;
          }
        }

        .file-loading {
          position: relative;
          background-image: none;

          .selected-method-icon {
            color: $color-gray-400;
            position: absolute;
            top: 12px;
            right: 14px;
            z-index: 5;
            font-size: $text-small;
          }

          .dropdown-menu {
            li {
              a {
                &:before {
                  content: '\eae0';
                  font-family: $font-sallaIcon;
                  margin-left: 10px;
                  position: relative;
                  top: 1px;
                  font-size: $text-small;
                  color: $color-gray-400
                }
              }

              &:last-of-type {
                a {
                  &:before {
                    content: '\ed2c'
                  }
                }
              }

              &.selected {
                a {
                  &:before {
                    color: inherit;
                  }

                  &:hover {
                    &:before {
                      color: inherit;
                    }
                  }
                }
              }
            }
          }

          .file-link {
            border-right: 1px solid $color-gray-200;
            height: 37px;
            display: none;
            padding-right: 15px;
            font-size: $text-xx-small;
            color: $color-dark-100;
          }

          .upload-file-wrapper {
            position: relative;

            .uploaded-successfully {
              position: absolute;
              left: 110px;
              top: 9px;
              color: $color-primary;
              font-size: $text-xx-small;
              z-index: 9999;
              display: none;

              i {
                font-size: $text-xx-small;
                margin-left: 3px;
                position: relative;
                top: 2px;
              }
            }

            .file-name {
              font-size: $text-xx-small;
              color: $color-dark-100;
              position: absolute;
              right: 15px;
              top: 9px;
              z-index: 55;
              display: none;
              width: 30%;
              white-space: nowrap;
              overflow: hidden;
            }
          }

          .file-caption-name {
            font-size: $text-xx-small;
            color: $color-dark-100;

            i {
              display: none;
            }
          }

          .file-input {
            position: relative;

            .kv-upload-progress {
              position: absolute;
              z-index: 5555;
              width: 96%;
              height: 3px !important;
              right: 20px;
              top: 66px;
              margin: 0;

              .progress {
                height: 100%;

                &-bar {
                  background-image: none !important;
                }
              }

              &:before {
                content: 'جاري رفع الملف';
                font-size: $text-xx-small;
                color: $color-dark-200;
                position: absolute;
                right: -96px;
                top: -10px;
              }
            }

            .kv-fileinput-caption {
              background: $color-white !important;
            }
          }
        }

        #remove_digital_file {
          padding: 7px 5px 7px 7px;
          font-size: $text-xx-small;

          &:after {
            content: 'حذف الملف'
          }

          i {
            font-size: $text-small;
            margin-left: 6px;
            position: relative;
            top: -1px;
          }
        }

        @include mediaMaxWidth($screen-tablet-l) {
          #remove_digital_file {
            &:after {
              content: 'حذف'
            }
          }
          .file-loading .file-input .kv-upload-progress {
            width: 88%;
            right: 45px;
          }
        }

        @include mediaMaxWidth($screen-phones) {
          .file-loading .upload-file-wrapper .uploaded-successfully {
            left: 60px !important
          }
        }

        @include mediaMaxWidth(410px) {
          .file-loading .upload-file-wrapper .uploaded-successfully {
            top: 12px;
            left: 50px;

            span {
              display: none
            }
          }

        }
      }

      .digital-list-rules {
        li {
          color: $color-dark-200;
          font-size: $text-x-small;

          &:not(:last-of-type) {
            margin-bottom: 4px;
          }

          &:not(:first-of-type) {
            padding-right: 4px;
          }

          span {
            font-weight: bold;
          }
        }
      }

      @include mediaMaxWidth(410px) {
        .option-wrapper {
          .form-group {
            flex: 0 0 50%;
            margin: 0 0 0 10px;

            &:first-of-type {
              margin: 0 0 10px 10px
            }
          }
        }
      }
    }

    &__block {
      #parent_mobile {
        .iti {
          &--container {
            top: 31px !important;
            left: 0 !important;
            right: auto;
            position: absolute;
          }

          &__country-list {
            width: 100%;
            max-height: 300px;
          }
        }
      }
    }
  }

  .image-preview {
    position: relative;

    .btn {
      position: absolute;
      top: -11px;
      @include centerX
    }

    &.file {
      .btn {
        top: -14px;
      }
    }
  }

  .view-file {
    i {
      font-size: 80px;
    }
  }

  .sarie img {
    max-width: 40px;
    position: relative;
    top: -2px;
  }

  .image-preview {
    img {
      max-height: 200px;
      cursor: pointer;
    }
  }

  // order edit quantity
  &.order-edit-quantity {
    .bootstrap-select {
      &-branches {
        .dropdown-menu.inner li > a {
          span {
            font-size: $text-xx-small;

            &:first-of-type > span {
              font-size: $text-xxx-small;
            }
          }
        }
      }
    }
  }

  &-loader {
    text-align: center;
    min-height: 150px;

    @include flexable(center, center, column);
  }

  &-light {
    $lm-padding: 20px;
    $lm-b-radius: 10px;

    .modal-dialog {
      .modal-content {
        .modal-header {
          background: $color-white;
          border: none;
          padding: 20px $lm-padding 0;
          // margin-bottom: $lm-padding;
          @include b-radius($lm-padding $lm-padding 0 0);

          .modal-title {
            font-family: $font-main;
            font-size: $text-xx-medium;
            color: $color-primary-l;
            line-height: 33px;

            i {
              display: inline-block;
              vertical-align: middle;
              margin: 0 0 0 5px;
              transform: translateY(-2px);
              font-size: 22px;
            }
          }

          button.close {
            width: 50px;
            height: 50px;
            padding: 10px;
            top: 12px;
            left: 10px;
            z-index: 999;

            &:before {
              content: '\ea47';
              font-family: $font-sallaIcon;
              font-size: $text-large;
              color: $color-primary-l;
              @include transi();
            }

            &:hover {
              &:before {
                color: $color-dark-200;
              }
            }
          }
        }

        .modal-body {

          // who would do that ????
          label {
            padding-top: 0;
          }
        }

        .modal-footer {
          padding: 0 $lm-padding $lm-padding;
          @include b-radius(0 0 $lm-padding $lm-padding);
          background: $color-white;

          &.text-left{
            text-align: left;
          }

          .btn-xlg{
            padding-top: 9px;
            padding-bottom: 9px;
          }

          .footer-buttons {
            @include flexable(center, space-between, row);

            > * {
              margin: 0 5px;

              &:first-child {
                margin-right: 0;
              }

              &:last-child {
                margin-left: 0;
              }

              &.wide {
                flex: 1;
              }
            }
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      .modal-dialog {
        .modal-content {
          .modal-header {
            padding: 20px 15px 0;
            @include b-radius($b-radius $b-radius 0 0);

            button.close {
              top: 5px;
              left: 0;
            }
          }

          .modal-footer {
            padding: 0 15px 15px;
            @include b-radius(0 0 $b-radius $b-radius);
            background: $color-white;
          }
        }
      }
    }
  }

  &-small {
    div.modal-dialog {
      max-width: 600px;
    }
  }

  &-vertical-align {
    @include flexable(center, center);
  }

  &-transparent {
    .modal-dialog {
      .modal-content {
        background: transparent;
        box-shadow: none;

        .modal-header {
          background: transparent;
        }
      }
    }
  }

  &-payment {
    .modal-dialog {
      .modal-content {
        .modal-body {
          padding: 0 !important;

          .fields-wrapper {
            padding: 0 20px;
          }
        }
      }
    }
  }

  //Backup modal
  &-backup {
    .modal-header {
      padding: 15px 20px;
    }


    .condition-wrapper {
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm);
      padding: 18px
    }

    .modal-body {
      &.no-padding {
        padding: 0 !important;
      }

      &.min-h-unset {
        min-height: unset;
      }
    }

    .modal-footer {
      @include flexable(center, space-between, row);

      &:before,
      &:after {
        content: none;
      }

      .btn {
        &::before {
          content: '\ea9d';
          font-family: $font-sallaIcon;
          margin-left: 8px;
          position: relative;
          top: 1px;
          display: inline-block;
          vertical-align: middle;
        }

        &-close {
          color: $color-dark-200;
          background-color: $color-gray-300;
          border: none;
          margin-right: auto;
        }

        .btn-close {
          background-color: $color-gray-300;
          border: none;

          &::before {
            content: '\ea47'
          }
        }

        &--custom-icon {
          &::before {
            content: none !important
          }
        }

        &.no-icon {
          &::before {
            content: none;
          }
        }
      }
    }
  }

  &#abandoned_carts_discount_modal {
    input#free_shipping {
      left: -36px;
      z-index: 5;
      opacity: 0;
      display: inline !important;
    }
  }

  .notify-clients {
    max-height: 300px;
    overflow-y: auto;
    @include scrollBar(2px, $color-primary, $color-gray-200);

    .rec-list {
      > li {
        @include flexable(center, flex-start, row);
        width: 100%;

        &:not(:last-of-type) {
          margin-bottom: 15px;
        }

        img {
          width: 45px;
          height: 45px;
          object-fit: contain;
          @include b-radius(50%)
        }

        > div {
          margin-right: 13px;

          h6 {
            font-size: $base-size;
            margin: 0;
          }

          span {
            font-size: $text-xxx-small;
            color: $color-dark-200;

            i {
              font-size: 11px;
            }
          }
        }
      }
    }
  }

  //Upload product image
  &#modal_product_photos {
    .filepond--list-scroller {
      height: 40px !important;
      overflow-y: auto;
      -webkit-mask: unset;
      margin-right: 10px;
    }

    .filepond--credits {
      display: none
    }
  }


  // Products Categories
  &#modal_product_category {
    .form-group .input-group {
      @include flexable(center, flex-start, row);

      .input-group-addon {
        width: 32px;
      }

      .form-control {
        flex: auto;
      }

      .input-group-btn {
        @include flexable(flex-start, flex-start, row);
        width: auto;

        .rec-checkbox {
          position: relative;
          top: 6px;
          right: 7px;
        }
      }
    }
  }

  //Tax number
  &#modal_store_tax_number {
    #tax_number_review span {
      letter-spacing: 1px;
    }
  }

  // Shipping Companies Modal
  &#modal_company_form {
    .form-group {
      label {
        padding-top: 0;
      }
    }

    .ui.search {
      .default.text {
        position: absolute;
        top: 9px
      }

      .text {
        top: 4px;
      }

      .dropdown.icon {
        opacity: 1;
        left: 12px !important;

        &:before {
          left: 3px !important;
          color: $color-dark-100;
          font-size: 16px;
        }
      }
    }
  }

  // Partners Modal
  &#company_info_modal {
    p {
      width: 100% !important;
      background-color: transparent !important;
    }

    video {
      width: 100% !important;
    }

    img {
      max-width: 100% !important;
    }
  }

  // Order case update
  &#edit_order_case {
    .details {
      color: $color-dark-300;
      font-size: $text-small;
      margin: 0 0 30px;
    }

    .message {
      h4 {
        font-size: $text-small;
        margin: 0 0 -5px;
      }

      span {
        font-size: $text-xx-small;
        color: $color-dark-100
      }
    }

    .progress-bar {
      &__background-line {
        width: 30%;
      }
    }
  }

  // External Services Modal
  &#external_services_modal {
    .google-verified-img {
      margin: 20px 0;
      text-align: center;

      img {
        width: 100px;
      }
    }

    div.form-control {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &#assign_multi_order_modal {
    &.hide-scroll {
      ::-webkit-scrollbar {
        display: none;
      }
    }

    .modal {
      &-header {
        padding: 16px 20px;
      }

      &-body {
        padding: 20px 20px 0 20px;

        .no-more-tables {
          max-height: 380px;
          overflow-y: scroll;
          @include scrollBar(2px, $color-primary, $color-gray-200);
        }
      }
    }

    h4 {
      margin: 0;
      color: $color-dark-200;
      font-size: $text-small;
    }

    .table {
      tr {
        &:hover {
          background-color: transparent;
        }
      }

      td {
        border-top: none
      }
    }

    .order-customer {
      padding: 15px 0 15px 0 !important;
      width: 100%;

      .media-left {
        &:first-child {
          padding-right: 0 !important;
          padding-left: 13px !important;
        }

        &:last-of-type {
          padding-left: 0;

          div:not(:first-of-type) {
            color: $color-dark-200 !important
          }

          div:nth-of-type(2) {
            color: $color-dark-300 !important;
            font-weight: bold;
          }

          .rec-btn {
            position: absolute;
            left: 0;
            @include centerY;
            border: 1px solid $color-danger;
            width: 20px;
            height: 20px;
            @include b-radius(50%);
            padding: 0;

            i {
              font-size: $text-x-small;
            }

            &:hover {
              background-color: transparent;
              opacity: .8;
            }
          }
        }
      }
    }

    .form-group {
      margin: 15px 0 0 0;

      .filter-option {
        font-size: $text-xx-small;
      }
    }
  }

  .modal-footer {
    &.no-icons {
      .btn {
        &:before {
          display: none;
        }
      }
    }

    .btn-save {
      color: var(--color-primary-l);
      background-color: $color-secondary-50;
      border: none;
    }
  }


  &__tabs {
    display: flex;
    margin: 0;

    @include b-radius($b-radius-sm);
    border: 1px solid $color-primary;
    overflow: hidden;

    + .order-plan {
      @include b-radius(0 0 $b-radius-sm $b-radius-sm);
      padding: 0;
    }

    &-tabitem {
      background-color: $color-white;
      margin: 0 !important;
      flex: 1 0 0;
      padding: 10px 10px 15px;

      label {
        display: block;
        transition: 0.3s;
      }

      // hide radio or checkbox options to look like tabs ---
      .rec-checkbox {
        &--primary {
          label {
            padding-right: 0 !important;

            &:before,
            &:after {
              display: none !important;
            }
          }
        }
      }

      &:not(.is-active) {
        label:hover {
          color: $color-primary;
        }
      }

      &.is-active {
        background-color: $color-primary;

        * {
          color: $color-white !important;
        }

        .rec-checkbox {
          &--primary {
            label {
              &:before {
                border-color: $color-white;
              }

              &:after {
                background-color: $color-white;
              }
            }
          }
        }

        label {
          cursor: default;
        }
      }
    }
  }

  // Product Options
  &#modal_product_options {

    .option-wrapper {
      .form-group {
        flex: 0 0 20%;
        margin: 0 0 0 20px;

        &:first-of-type {
          flex: auto;
          margin: 0 0 10px 20px
        }
      }

      .remove-button-option {
        right: 0;
      }
    }

    .form-group {
      margin-bottom: 10px;

      label {
        color: $color-dark-300;
        font-size: 13px;
      }
    }

    .modal-footer {
      padding: 14px 12px;
    }

    #digital_list {
      .option-section {
        border: 1px solid $color-gray-200;
        @include transi(border-color);

        &.new {
          border-color: $color-primary;
        }
      }

      .btn-primary {
        cursor: pointer;

        i {
          &:before {
            content: '\eae0';
            font-family: $font-sallaIcon !important;
            position: relative;
            top: -3px
          }
        }

        &:active,
        &:focus {
          background: $color-secondary !important;
          border-color: $color-secondary;
        }
      }

      .bootstrap-select {
        width: 25%;

        .btn {
          border-left: none;
          border-right: 1px solid;
          padding: 8px 39px 7px;
        }
      }

      .file-loading {
        position: relative;
        background-image: none;

        .selected-method-icon {
          color: $color-gray-400;
          position: absolute;
          top: 12px;
          right: 14px;
          z-index: 5;
          font-size: 14px;
        }

        .dropdown-menu {
          li {
            a {
              &:before {
                content: '\eae0';
                font-family: $font-sallaIcon;
                margin-left: 10px;
                position: relative;
                top: 1px;
                font-size: 14px;
                color: $color-gray-400
              }
            }

            &:last-of-type {
              a {
                &:before {
                  content: '\ed2c'
                }
              }
            }

            &.selected {
              a {
                &:before {
                  color: inherit;
                }

                &:hover {
                  &:before {
                    color: inherit;
                  }
                }
              }
            }
          }
        }

        .file-link {
          border-right: 1px solid $color-gray-200;
          height: 37px;
          display: none;
          padding-right: 15px;
          font-size: 12px;
          color: $color-gray-400;
        }

        .upload-file-wrapper {
          position: relative;

          .uploaded-successfully {
            position: absolute;
            left: 110px;
            top: 9px;
            color: $color-secondary;
            font-size: 12px;
            z-index: 9999;
            display: none;

            i {
              font-size: 12px;
              margin-left: 3px;
              position: relative;
              top: 2px;
            }
          }

          .file-name {
            font-size: 12px;
            color: $color-gray-400;
            position: absolute;
            right: 15px;
            top: 9px;
            z-index: 55;
            display: none;
            width: 30%;
            white-space: nowrap;
            overflow: hidden;
          }
        }

        .file-caption-name {
          font-size: 12px;
          color: $color-gray-400;

          i {
            display: none;
          }
        }

        .file-input {
          position: relative;

          .kv-upload-progress {
            position: absolute;
            z-index: 5555;
            width: 96%;
            right: 20px;
            top: 66px;
            height: 3px !important;
            margin: 0;

            .progress {
              height: 100%;

              &-bar {
                background-image: none !important;
              }
            }

            &:before {
              content: 'جاري رفع الملف';
              font-size: 12px;
              color: $color-dark-50;
              position: absolute;
              right: -96px;
              top: -10px;
            }
          }

          .kv-fileinput-caption {
            background: $color-white !important;
          }
        }
      }

      #remove_digital_file {
        padding: 7px 5px 7px 7px;
        font-size: 12px;

        &:after {
          content: 'حذف الملف'
        }

        i {
          font-size: $text-small;
          margin-left: 6px;
          position: relative;
          top: -1px;
        }
      }

      @include mediaMaxWidth($screen-tablet-l) {
        #remove_digital_file {
          &:after {
            content: 'حذف'
          }
        }
        .file-loading .file-input .kv-upload-progress {
          width: 88%;
          right: 45px;
        }
      }

      @include mediaMaxWidth($screen-phones) {
        .file-loading .upload-file-wrapper .uploaded-successfully {
          left: 60px !important
        }
      }

      @include mediaMaxWidth(410px) {
        .file-loading .upload-file-wrapper .uploaded-successfully {
          top: 12px;
          left: 50px;

          span {
            display: none
          }
        }

      }
    }

    .digital-list-rules {
      li {
        color: $color-dark-200;
        font-size: 13px;

        &:not(:last-of-type) {
          margin-bottom: 4px;
        }

        &:not(:first-of-type) {
          padding-right: 4px;
        }

        span {
          font-weight: bold;
        }
      }
    }

    @include mediaMaxWidth(410px) {
      .option-wrapper {
        .form-group {
          flex: 0 0 50%;
          margin: 0 0 0 10px;

          &:first-of-type {
            margin: 0 0 10px 10px
          }
        }
      }
    }
  }
}

// confirm box ---
.jconfirm {
  .jconfirm-box {
    padding: 0 !important;

    div.jconfirm-title-c {
      padding: 10px 15px;
      margin: 0 !important;
      font-size: $base-size;
      line-height: unset;
      color: $color-primary-l;
      background: $color-secondary-50;

      .jconfirm-title {
        padding: 0 !important;
        font-size: 15px !important;
      }
    }

    .jconfirm {
      &-content-pane {
        margin: 0 !important;

        .jconfirm-content {
          padding: 15px;
        }
      }

      &-buttons {
        padding: 10px 15px !important;
        background: $color-gray-25;
        @include flexable(center, space-between, row);

        > button.btn.btn-default {
          padding: 10px 25px !important;
          font-size: $text-small;
          color: $color-white;
          line-height: 1;
          background-color: $color-dark-100;
          margin: 0 !important;
          @include b-radius($b-radius-sm);
        }
      }
    }
  }

  // show close icon ---
  &-rtl {
    .jconfirm-box {
      div.jconfirm-closeIcon {
        // display: block !important;
        z-index: 999;
        color: $color-white;
        opacity: 0.8;
        top: 13px;
        left: 10px;
      }
    }
  }
}

#modal_block {
  #parent_mobile {
    .iti {
      &--container {
        top: 31px !important;
        left: 0 !important;
        right: auto;
        position: absolute;
      }
    }
  }
}
