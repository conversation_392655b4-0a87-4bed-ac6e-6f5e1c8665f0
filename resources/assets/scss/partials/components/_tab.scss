// @osama - needs revisit ---
.product-control-nav {
  a[ data-toggle="tab"] {
    font-size: $text-small;
    font-weight: bold;
  }
}

.theme-tab-bar {

  &-light {
    position: initial;
    margin: 0 0 20px;
    padding: 0;

    ul {
      li {
        a {
          font-size: $text-small;
          font-weight: 500;
        }

        &.active {
          &.no-radius {
            a {
              @include b-radius(0 !important)
            }
          }
        }
      }
    }
  }
}

.nav {
  &-tabs-light {
    @include flexable(flex-start, flex-start, row);
    height: 42px;
    background: transparent;
    border: none;
    border-bottom: 2px solid $color-secondary-50;
    margin: 0;
    padding: 0 !important;
    overflow: hidden;
    overflow-x: auto;

    li {
      margin: 0 2px;

      a {
        min-width: 100px;
        height: 40px;
        padding: 0 get-vw(50px);
        margin: 0;
        text-align: center;
        line-height: 40px;
        @include b-radius($b-radius-sm $b-radius-sm 0 0);
        background: $color-gray-200;
        border: none !important;
      }

      &:first-child {
        margin-right: 0;
      }

      &:last-child {
        margin-left: 0;
      }

      &.active {
        a {
          color: $color-primary-l;
          background: $color-secondary-50;

          &:hover, &:active, &:focus {
            color: $color-primary-l;
            background: $color-secondary-50;
          }
        }
      }

      &:hover {
        a {
          background: $color-secondary;
        }
      }
    }

    &.centered {
      justify-content: center;
    }

    &:before, &:after {
      display: none;
    }

    @include mediaMaxWidth($screen-tablet-p) {
      li {
        flex: auto;
        width: auto;
      }
    }

    &--center-links {
      justify-content: center;
    }
  }

  &.nav-tabs-large {
    li {
      a {
        font-size: $text-small;
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {
      li {
        a {
          font-size: $text-small;
        }
      }
    }
  }
}

.tab-content {
  &-bg {
    background: $color-white;
    border: 1px solid $color-gray-200;
    border-top: none;
  }

  &-padded {
    padding: 30px;
    @include mediaMaxWidth($screen-tablet-l) {
      padding: 20px;
    }
  }
}


// vertical tabs ---
.tabs-wrapper {
  &-vertical {
    @include b-radius($b-radius-sm);
    background: $color-gray-25;
    border: 1px solid $color-gray-200;
    overflow: hidden;
    grid-template-columns: 250px 1fr;

    > * {
      min-height: 100%;
    }

    .tab-nav {
      &-vertical {
        padding-bottom: 50px;

        .nav {
          width: 100%;
          @include flexable(flex-start, flex-start, column);

          li {
            width: 100%;

            a {
              @include flexable(center, flex-start, row);
              font-size: $text-small;
              color: $color-dark-200;
              padding: 15px 20px;
              border-bottom: 1px solid $color-gray-200;
              @include transi();

              .img-placeholder {
                margin: 0 0 0 20px;
                width: 45px;
                height: 45px;
                flex: 0 0 45px;
                transform: translateY(2px);
              }
            }

            &.active {
              a {
                color: $color-primary;
                background: $color-white;

                .img-placeholder {
                  color: $color-white;
                  background: $color-primary;
                }

                &:hover, &:active, &:focus {
                  color: $color-primary;
                  background: $color-white;
                }
              }
            }

            &:hover {
              a {
                color: darken($color-dark-200, 10%);
                background-color: transparent;
              }
            }
          }
        }
      }
    }

    .tab-content {
      box-shadow: 1px 0 10px rgba(0, 0, 0, 0.05);
    }

    @include mediaMaxWidth($screen-desktop-large) {
      grid-template-columns: 2fr 5fr;
    }
    @include mediaMaxWidth($screen-laptop) {
      .tab-nav {
        &-vertical {
          .nav {
            li {
              a {
                font-size: $text-small;

                .img-placeholder {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: 1fr;
      > * {
        min-height: auto;
      }
      .tab-nav {
        padding: 0;

        &-vertical {
          .nav {
            li {
              a {
                padding: 10px;
              }
            }
          }
        }
      }
    }
  }
}
