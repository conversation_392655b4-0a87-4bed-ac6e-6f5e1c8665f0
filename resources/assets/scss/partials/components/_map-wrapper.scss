.rec-map-wrapper {
  position: relative;

  .store-map-current-button {
    height: 40px;
    width: 40px;
    text-align: center;
    position: absolute;
    z-index: 5;
    left: 0;
  }

  /*.map-search-control {
    max-width: 200px;
    border: 1px solid $color-primary !important;
    margin: 0;
    padding: 0 11px 0 13px;
    height: 36px;
    @include b-radius(50px);
    font-family: $font-main;
    font-size: $text-xx-small;
    background-color: $color-white;
  }*/

  &__controls {
    @include flexable(center, center, row);
    width: 100%;
    height: auto;
    position: absolute;
    gap: 10px;
    z-index: 9;
    font-family: $font-main;
    font-size: $text-xx-small;
    background-color: $color-white;
    z-index: 10;
    top:10px !important;

    &.full {
      left: 65px!important;
      max-width: unset;
      padding-bottom: 4px;
      right: unset;
      top: 10px!important;
      width: calc(100% - 130px);
    }

    @include mediaMaxWidth($screen-phones) {
      left: 55px !important;
    }
  }

  &__controls {
    @include flexable(center, center, row);
    width: 100%;
    height: auto;
    position: absolute;
    gap: 10px;
    z-index: 9;
  }

  .map-placeholder {
    @include flexable();
    width: 100%;
    height: 400px;
    background: $color-gray-25;

    h4 {
      font-size: 2rem;
      color: $color-gray-400;
    }
  }

  .map-order {
    width: 100%;
    height: 80px;
    background: $color-gray-25;
    @include b-radius($b-radius-sm);
    overflow: hidden;

    h4 {
      font-size: $text-xx-small;
      color: $color-gray-400;
    }
  }

  &--large {
    .map-placeholder {
      height: 350px
    }
  }

  .map-location-copy {
    @include flexable(center, flex-start, row);
    width: 90%;

    .map-search-control {
      position: initial;
      max-width: unset;
      @include b-radius(0 50px 50px 0);
      border-left: 0 !important;
    }

    button.copy-location {
      @include b-radius(50px 0 0 50px);
    }
  }

  .map-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px 10px 0;
    background-color: rgba(51, 51, 51, 0.7);
  }
}