.rec {
  &-order-assignee {
    min-width: 130px;
    min-height: 46px;
    position: absolute;
    top: -46px;
    right: -1px;
    padding: 5px 10px 5px 25px;
    @include b-radius(25px 5px 0 0);
    background: $color-gray-50;
    border: 1px solid $color-gray-200;
    border-bottom: none;
    z-index: 99;

    &__title {
      font-size: $text-small;
      color: $color-dark-100;
      float: right;
      margin: 5px 0 0 10px;
    }

    &__assigned-users {
      > li {
        width: 30px;
        height: 30px;
        @include b-radius(50%);
        position: relative;
        background-size: cover;
        margin-left: -10px;

        > span {
          display: block;
          padding: 5px 10px;
          position: absolute;
          top: 110%;
          right: 50%;
          transform: translateX(50%);
          @include b-radius($b-radius-sm);
          background: $color-white;
          font-size: $text-xx-small;
          text-align: center;
          color: $color-dark-100;
          white-space: nowrap;
          @include transi();
          opacity: 0;
          visibility: hidden;
        }

        > button {
          display: none;
          width: 18px;
          height: 18px;
          min-width: unset;
          min-height: unset;
          @include b-radius(50%);
          position: absolute;
          top: -5px;
          right: -5px;
          padding: 0;
          background: $color-danger;

          &:before {
            content: '\EA47';
            font-family: $font-sallaIcon;
            font-size: $text-xxxx-small;
            color: $color-white;
            line-height: 20px;
            position: absolute;
            top: 55%;
            transform: translateY(-50%) translateX(50%);
            right: 50%;
          }

          &:hover {
            background: darken($color-danger, 10%);
          }
        }

        &#assign_user {
          background: $color-white;
          z-index: 99;
          cursor: pointer;
          @include transi();
          border: 1px solid $color-secondary;

          .rec-dropdown {
            display: none;
            position: absolute;
            top: 115%;
            right: -80px;
            z-index: 999;
            cursor: pointer;
          }

          &:after {
            content: '\e90c';
            display: inline-block;
            font-family: $font-sallaIcon;
            font-size: $text-small;
            color: $color-primary-l;
            position: absolute;
            top: 52%;
            right: 48%;
            transform: translate(50%, -50%);
          }

          ul {
            border: 1px solid $color-gray-200;
          }
        }

        &:hover {
          > span {
            opacity: 1;
            visibility: visible;
            top: 100%;
          }

          > button {
            display: block;
          }
        }
      }
    }

    &:before {
      content: '';
      display: block;
      width: 71px;
      height: 46px;
      background: url("/cp/assets/images/panel_curve.svg") center no-repeat;
      position: absolute;
      top: 0;
      left: -45px;
      z-index: 0;
      border-bottom: 1px solid $color-white;
    }
  }
}
