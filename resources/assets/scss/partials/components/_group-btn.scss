.rec {
  &-btn-group {
    .dropdown-toggle {
      width: 100%;
      padding-left: 30px;

      &:after {
        content: "\e96e";
        font-family: $font-sallaIcon;
        font-size: $base-size;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
      }
    }

    .dropdown-menu {
      min-width: 150px;
      width: 100%;
      height: auto;
      padding: 0 5px;
      margin: 5px 0;
      border: 1px solid $color-gray-200;

      .dropdown-item {
        display: block;
        width: 100%;
        padding: 5px 5px 7px;
        border-bottom: 1px solid $color-gray-200;
        font-size: $text-x-small;

        i {
          @include right-icon-el(5px);
          font-size: $text-xx-small;
        }

        &:last-child {
          border-bottom: none;
        }

        &:hover, &.active {
          color: $color-secondary;
        }
      }
    }

    &.open {
      .dropdown-toggle {
        &:after {
          content: '\e96d';
        }
      }
    }

    &.wide {
      width: 100%;
    }
  }
}