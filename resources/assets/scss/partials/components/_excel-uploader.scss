.form-group {
  &.excel-upload-section {
    &--inline {
      width: 100%;
      @include flexable(center, flex-start, row);
      background: none;
      box-shadow: none;

      .rec-upload-label {
        .form-control {
          cursor: pointer;

          &:focus {
            + label {
              background-color: $color-gray-50;
              color: $color-gray-400;
            }
          }
        }

        label {
          &:before {
            @include right-icon-el(10px);
            transform: translateY(-1px);
            content: '\ebec';
            font-family: $font-sallaIcon, serif;
            font-size: $base-size;
            color: $color-gray-400;
            margin-left: 10px;
          }
        }
      }

      > div {
        &:first-of-type {
          width: 100%;
          height: 40px;
        }
      }
      label {
        width: 100%;
        height: 40px;
        border-left: 0;
        @include b-radius(0 $b-radius-sm $b-radius-sm 0);
        border-style: dashed;
        background-color: $color-gray-50;
        padding: 8px 10px;
        font-size: $text-x-small;
        color: $color-gray-400;
      }

      button {
        padding: 9px 20px;
        background-color: $color-secondary-50;
        border-color: $color-secondary-50;
        @include b-radius($b-radius-sm 0 0 $b-radius-sm);
        cursor: pointer;
      }

      &.unloaded {
        label {
          border-style: dashed;
          background-color: $color-gray-50;
          padding: 8px 10px;
          font-size: $text-x-small;
          color: $color-gray-400;

          i {
            @include right-icon-el(5px);
            transform: translateY(-2px);
            color: $color-gray-400;
          }
        }
      }
    }
  }
}
