.sort {
  span,
  a {
    font-size: $text-xx-small;

    &:before {
      font-family: $font-sallaIcon !important;
      margin-left: 8px;
      position: relative;
      top: 2px;
    }
  }

  a {
    display: inline-block;
    margin-right: 2px;

    &:before {
      content: '';
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-left: 5px;
      text-align: center;
      line-height: 18px;
      @include b-radius(50%);
      background-color: $color-gray-200;
    }
  }
}

//For Warehouse And Branches
#pickup-branches {
  .sort {
    span {
      color: $color-dark-200;

      &:before {
        content: '\e960'
      }

      &.warehouse {
        &:before {
          content: '\efba'
        }
      }
    }

    a {
      color: $color-gray-400;
    }

    &.default {
      span {
        color: $color-dark-100;
      }

      a {
        color: $color-primary-l; //colorAvilability
        text-decoration: underline;

        &:before {
          content: '\ea9d';
          background-color: $color-primary-l; //colorAvilabilty
          color: $color-secondary-50;
          font-size: 10px;
          top: 0;
        }
      }
    }
  }
}