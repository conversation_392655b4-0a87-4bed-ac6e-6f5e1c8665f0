#floating_menu_wrapper {
  position: fixed;
  bottom: -40px;
  opacity: 0;
  visibility: hidden;
  left: 20px;
  z-index: 99999;
  @include transi(all, 0.75s);

  .notification-badge {
    display: block;
    min-width: 22px;
    height: 22px;
    padding: 0 4px;
    position: absolute;
    top: 10px;
    right: -10px;
    z-index: 90;
    @include b-radius($b-radius);
    font-size: $text-xx-small;
    line-height: 20px;
    text-align: center;
    color: $color-primary-l;
    background: $color-danger;
  }

  .btn {
    @include flexable();
    @include b-radius(50%);
    background: $color-secondary-50;
    padding: 0;
    border: none !important;

    svg {
      display: block;
      width: 25px;
      height: 25px;
    }

    &#fm_toggle {
      width: 60px;
      height: 60px;
      z-index: 10;

      svg, i {
        position: absolute;
        top: 50%;
        right: 50%;
        transform: translateY(-50%) translateX(50%);
        @include transi();
      }

      svg {
        transform: translateY(-50%) translateX(50%) scale(1);
      }

      i {
        font-size: $text-xxx-larger;
        color: $color-primary-l;
        transform: translateY(-50%) translateX(50%) scale(0);
      }

      &.open {
        background: $color-secondary-50;

        svg {
          transform: translateY(-50%) translateX(50%) scale(0);
        }

        i {
          transform: translateY(-50%) translateX(50%) scale(1);
        }
      }
    }

    &.fm-item {
      @include transi();
      width: 50px;
      height: 50px;
      margin-bottom: 10px;

      &:before,
      &:after {
        position: absolute;
        transform: translateX(10px);
        @include transi();
        opacity: 0;
      }

      &:before {
        content: attr(data-label);
        display: block;
        min-height: 25px;
        color: $color-primary-l;
        font-size: $text-xx-small;
        text-align: center;
        padding: 5px 10px;
        @include b-radius($b-radius-sm);
        left: calc(100% + 10px);
        background-color: $color-secondary-50;

      }

      &:after {
        content: '';
        left: calc(100% + 10px);
        top: 50%;
        width: 0;
        height: 0;
        border: 5px solid transparent;
        border-right-color: $color-secondary-50;
        border-left: 0;
        border-left: 0;
        margin-top: -5px;
        margin-left: -5px;
      }
    }

    &:hover {
      &:before,
      &:after {
        opacity: 1;
        transform: translateX(0);
      }
    }
  }

  .fm-items {
    @include transi();
    width: 60px;
    position: absolute;
    bottom: 0;
    align-items: center;
    transform-origin: center bottom;
    transform: scale(0) translateY(-120px);
    opacity: 0;
    transition-duration: 0.5s;
    z-index: 5;

    &.reveal {
      transform: scale(1) translateY(-60px);
      opacity: 1;
    }
  }

  &.reveal {
    opacity: 1;
    visibility: visible;
    bottom: 20px;
  }

  // for demo version only --
  &.demo {
    bottom: 80px;
  }
}

iframe.intercom-launcher-frame, .intercom-launcher {
  display: none !important;
}
