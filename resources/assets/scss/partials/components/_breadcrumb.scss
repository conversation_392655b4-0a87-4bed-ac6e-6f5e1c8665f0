#page_header_box {
  .nav-breadcrumb {

    ol {
      padding: 0;

      li {
        font-size: $text-x-small;
        margin: 0 0 4px 4px;

        a {
          color: $color-primary;

          i {
            display: inline-block;
            vertical-align: middle;
            transform: translateY(-2px);
            margin-left: 5px;
            color: $color-primary;
          }

          small {
            font-size: $text-x-small;
          }
        }

        &:before {
          color: rgba($color-primary, 0.7);
        }

        &.active {
          color: $color-gray-400;
        }
      }

      //get last child
      &:last-child {
        color: #bbb;
      }
    }

    @include mediaMaxWidth($screen-phones) {
      padding-left: 30px;
    }
  }

  .breadcrumb-title-wrapper {
    .page-title {
      padding: 0;
      margin: 0;

      small {
        font-size: $text-larger;
        margin: 0;

        &:before {
          display: none;
        }
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {
      .page-title {
        small {
          font-size: $text-large;
        }
      }
    }
  }

  @include mediaMaxWidth($screen-phones) {
    padding-left: 20px;
    .heading-elements.heading-help {
      padding-top: 0 !important;
      top: 0;
    }
  }
}
