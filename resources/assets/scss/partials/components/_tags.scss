.rec-list--tags li.dark {
  background-color: $color-gray-200 !important;
}

.rec {
  &-tags {
    position: relative;

    .form-control {
      padding-left: 60px;
    }

    .btn {
      &.btn-add-tag {
        position: absolute;
        left: 0;
        z-index: 99;
        @include transi();
        @include b-radius($b-radius-sm 0 0 $b-radius-sm);

        &.inactive {
          opacity: 0;
          visibility: hidden;
        }
      }
    }
  }

  &-list {
    &--tags {
      margin: 10px 0 0 0 !important;
      flex-wrap: wrap;

      li {
        position: relative;
        margin: 0 0 8px 8px !important;
        display: inline-flex;
        padding: 5px 10px 7px 30px;
        @include b-radius(50px);
        @include transi();
        font-size: 12px;
        line-height: 1;
        white-space: nowrap;
        background-color: $color-gray-100;

        &.dark {
          background-color: $color-primary;
        }

        i {
          @include flexable();
          width: 18px;
          height: 18px;
          font-size: 12px;
          color: $color-danger;
          position: absolute;
          left: 5px;
          top: 50%;
          transform: translateY(-50%);
          @include b-radius(50%);
          border: 1px solid $color-danger;
          cursor: pointer;
        }

        &:before {
          content: '\efeb';
          font-family: $font-sallaIcon;
          display: inline-block;
          vertical-align: middle;
          margin: 0 0 0 8px;
          opacity: 0.5;
        }

        &:last-child {
          margin: 0 0 8px 0 !important;
        }

        &.placeholder {
          background: transparent;
          color: $color-dark-100;
          padding: 0 !important;
          margin: 0 !important;

          &:before {
            display: none;
          }
        }

        &.delete {
          border-color: $color-danger;
          background-color: rgba($color-danger, 0.15);
          color: $color-danger;
          animation: delete 0.5s forwards;
        }

        &.title {
          padding-right: 0;
          padding-left: 0;
          background: transparent !important;
        }

        &.input-wrapper {
          padding: 0;
          background: transparent;

          .form-control {
            border: 1px solid $color-secondary !important;
            color: $color-primary-l;
            padding: 0 10px 0 30px !important;
            width: 100px;
            height: 24px;
            font-size: 12px;
            @include b-radius(50px !important);
            @include transi();

            + .btn.btn-add-tag {
              width: 18px;
              height: 18px;
              position: absolute;
              top: 3px;
              left: 4px;
              padding: 0;
              z-index: 5;
              @include b-radius(50px);
              line-height: 15px;

              i {
                background: transparent;
                color: $color-primary-l !important;
                font-size: 12px;
                padding: 0;
                left: -1px;
                border: none;
              }
            }

            &::placeholder {
              color: $color-primary-l;
            }

            &:focus,
            &.touched {
              width: 150px;
              background: $color-secondary;
              color: $color-primary-l;

              &::placeholder {
                color: $color-primary-l;
              }
            }
          }
        }
      }

      &--icon-less {
        li {
          &:before {
            display: none;
          }
        }
      }

      &--dark {
        li {
          background-color: $color-gray-200;

          &.input-wrapper {
            .form-control {
              &:focus,
              &.touched {
                background: $color-gray-200;
              }
            }
          }
        }
      }
    }
  }
}
