.feature-promo {
  overflow: visible;
  &__marketing {
    display: grid;
    grid-template-columns: 3fr 1fr;
    align-items: center;

    > div {
      position: relative;
    }


    img {
      max-width: 300px;
    }


    @include mediaMaxWidth($screen-desktop-small) {
      grid-template-columns: 1fr 1fr;
    }

    @include mediaMaxWidth($screen-desktop-small){
      > div {
        article{
          p{
            text-align: right;
          }
        }
      }
    }
    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: 1fr;
      > div {
        &:first-child {
          order: 1;
          padding: 20px 0 0 0;

          * {
            text-align: center;
          }
        }

        &:last-child {
          order: 0;
        }
      }
    }
  }

  &__actions {
    display: flex;
    width: 100%;
    max-width: 400px;

    .btn, >div {
      flex: 1;
    }

    >div.tooltip-toggle .tooltip-content {
      width: 215px;
    }

    @include mediaMaxWidth($screen-laptop) {
      margin: 0 auto;
    }
    @include mediaMaxWidth($screen-phones) {
      max-width: unset;
    }
  }

  &__badge {
    display: flex;
    align-items: center;
    background: white;
    box-shadow: 0 4px 8px 0 rgba(black, 0.15);
    @include b-radius($b-radius-sm);
    position: absolute;
    top: -20px;
    @include centerX();

    h5 {
      padding: 5px 10px;
    }

    &:before {
      display: flex;
      align-items: center;
      justify-content: center;
      content: '\ec72';
      font-family: 'sallaicons', serif;
      font-size: 20px;
      color: $color-primary;
      padding: 2px 10px;
    }

  
    &.center-y {
      @include centerY
    }

    &.right {
      right: auto;
      left: 100%;
      @include mediaMaxWidth($screen-phones) {
        right: auto;
        @include centerXY
      }
    }

    &.hide-laptop {
      @include mediaMaxWidth($screen-laptop) {
        display: none;
      }
    }
  }

  &__features {
    padding: 20px;
    box-sizing: border-box !important;
    background: $color-gray-25;
    grid-template-columns: repeat(auto-fill, minmax(260px,1fr));
    grid-auto-flow: column;
    grid-auto-columns: minmax(260px,1fr);
    grid-gap: 20px;
    overflow-x: scroll;
		overflow-y: hidden;

    &::-webkit-scrollbar {
			height: 0;
		}

    @include mediaMaxWidth($screen-phones) {
      padding: 0;
      background: transparent;
      grid-gap: 10px;
    }
  }

  &__feature {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 20px;
    border: 1px solid $color-gray-200;
    @include b-radius($b-radius-sm);
    background: white;

    h3 {
      font-size: 16px;
      margin: 0 0 8px 0;

      i {
        font-size: 25px;
        color: $color-primary;
        margin-left: 10px;
      }
    }

    p {
      margin: 0;
    } 
  }

  @include mediaMaxWidth($screen-tablet-l) {
    .panel-body {
      padding-top: 75px !important
    }
  }

  &.has-ribbon--lg:before {
    font-size: 17px;
    height: 50px;
    width: 200px;
    line-height: 35px;
  }

  &.has-ribbon:before {
    text-align: right;
    padding-right: 15px;
    color: rgba(101, 76, 8, 1);
  }

  .tabs-review {
    position: absolute;
    top: 26px;
    left: 10px;
    z-index: 5;
    &__tabs {
      border-color: rgba(101, 76, 8, 0.5);
      background: $color-white;
    }
  }
  .top-5{
    top:5px;
  }
}