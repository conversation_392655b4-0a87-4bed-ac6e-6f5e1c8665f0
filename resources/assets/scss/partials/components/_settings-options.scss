.rec-settings-options {
  width: 100%;
  flex-wrap: wrap;

  >li {
    display: inline-flex;
    align-items: center;
    width: 100%;
    padding: 10px 0;
    border-bottom: 1px solid $color-gray-25;

    >div {
      width: 50%;
      flex: 0 0 50%;

      >i {
        display: inline-block;
        vertical-align: middle;
        margin: 0 0 0 5px;
        opacity: 0.8;
      }
    }

    &.rec-so-optional {
      padding: 6px;
      background-color: $color-gray-25;
      border: none;
      @include b-radius($b-radius);
    }
  }

  &.made-in-ksa-logo {
    width: 48px;
    height: 48px;
    // background: url('/cp/assets/images/made-in-ksa-minimal-small.svg');
    background: url('https://cdn.assets.salla.network/dash/cp/assets/images/made-in-ksa-minimal-small.svg');
  }
}