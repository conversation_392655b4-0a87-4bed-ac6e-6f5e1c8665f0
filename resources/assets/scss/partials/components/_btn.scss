.rec-btn {
  $btn-min-height: unset;
  @include flexable();
  flex-wrap: wrap;
  width: auto;
  height: auto;
  min-height: $btn-min-height;
  position: relative;
  line-height: normal;
  text-align: center;
  border: none;
  cursor: pointer;
  outline: none !important;
  @include transi();

  &--trans {
    background-color: transparent !important;
  }

  &--bold {
    font-weight: bold !important;
  }

  &--white {
    background-color: $color-white !important;
  }

  &--warning {
    background-color: $color-warning-2 !important;

    &.custom {
      padding: 10px;
      color: #444 !important;
      border-radius: 3px;
    }
  }

  &--with-border {
    border: 1px solid $color-gray-200;

    &:hover {
      border-color: $color-gray-200;
    }
  }

  &--rounded {
    @include b-radius(50% !important);
  }

  &--primary {
    color: $color-primary-l;
    background-color: $color-secondary-50;

    * {
      color: $color-primary-l;
    }

    &:hover {
      color: $color-primary-l;
      background-color: $color-secondary;
    }
  }

  &--icon {
    padding: 3px 6px 4px;

    i {
      color: $color-white;
      font-size: $text-small;
      margin-left: 5px;
    }
  }

  &--remove-el {
    height: 23px;
    color: $color-danger;
    background: transparent;
    @include b-radius($b-radius-sm);
    padding: 1px 3px 0;

    &:hover {
      color: $color-danger;
    }
  }

  &--white {
    background-color: $color-white !important;
  }

  &--with-border {
    border: 1px solid $color-gray-200;

    &:hover {
      border-color: $color-gray-200;
    }
  }

  &--icon {
    padding: 4px 6px;

    i {
      color: $color-white;
      font-size: $text-small;
      margin-left: 5px;
    }
  }

  &--remove-el {
    height: 23px;
    color: $color-danger;
    background: transparent;
    @include b-radius($b-radius-sm);
    padding: 1px 3px;

    &:hover {
      color: $color-danger;
    }
  }

  &--remove-el-light {
    border-color: $color-danger !important;

    i {
      color: $color-danger;
    }
  }

  &--with-border {
    border: 1px solid $color-gray-200;
  }

  &--circular {
    @include b-radius(50%);
  }

  &--delete-alt {
    width: 30px;
    height: 30px;
    border: 1px solid $color-danger;

    &:before {
      content: "\e907";
      font-family: $font-sallaIcon;
      color: $color-danger;
    }
  }
  &--delete-cart {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background-color: rgba(245, 81, 87, 0.2);
    vertical-align: middle;

    border: 0 !important;
    &:before {
      content: "\ef39";
      font-family: $font-sallaIcon;
      color: $color-danger;
      font-size: $text-xx-small !important;
    }
  }
}

.btn {
  @include transi(background);

  &.dark-gray {
    background-color: #979797;
    border: none;
    color: #fff;
  }

  &.rec-fvm {
    padding-top: 5px;
    padding-bottom: 8px;

    &.short {
      padding-top: 1px;
      padding-bottom: 4px;
    }
  }
  &.dropdown-arrow {
    @include flexable(center, center, column);
    border: 1px solid $color-gray-300;
    color: $color-dark-200;
    background-color: $color-white;
    @include b-radius(50% !important);
    width: 18px;
    height: 18px;
    padding: 0;

    &:before {
      content: "\e96e";
      font-family: "sallaicons", serif;
      font-size: 16px;
      line-height: 1;
      color: inherit;
    }

    &[aria-expanded="true"] {
      &:before {
        content: "\e96d";
      }
    }

    &.primary {
      border-color: $color-primary;
      color: $color-primary;
    }
  }

  &-order-edit {
    &:hover {
      color: $color-secondary;
    }
  }

  &.btn-add-field {
    color: $color-dark-200;
    background: transparent;
    border: 1px dashed $color-gray-300;
    width: 20px;
    height: 20px;
    padding: 0;
    flex: 0 0 20px;
    line-height: 1;
    border-radius: 50%;
    margin-right: 10px;

    &:before {
      content: "\e90b";
      font-family: $font-sallaIcon !important;
      font-size: $text-xx-small;
      position: absolute;
      top: 50%;
      left: 53%;
      transform: translateY(-50%) translateX(-50%);
    }
  }

  &.btn-delete-field {
    width: 20px;
    height: 20px;
    padding: 0;
    flex: 0 0 20px;
    line-height: 1;
    border-radius: 50%;
    margin-right: 10px;

    &:before {
      content: "\ed91";
      font-family: $font-sallaIcon !important;
      font-size: $text-xx-small;
      position: absolute;
      top: 50%;
      left: 53%;
      transform: translateY(-50%) translateX(-50%);
    }
  }

  &-warning {
    background-color: $color-warning-light !important;
    color: $color-dark-300 !important;
    border-color: $color-warning-light !important;

    &:hover,
    &:focus,
    &:active {
      background-color: $color-warning-light !important;
      border-color: $color-warning-light !important;
    }
  }

  &-outer-link {
    &:after {
      content: "";
      background: url("/cp/assets/images/outer_link.svg");
      display: inline-block;
      width: 11px;
      height: 11px;
      background-size: cover;
      margin-right: 5px;
    }
  }

  &.short {
    padding-top: 1px;
    padding-bottom: 4px;
  }

  //
  &.btn-link {
    color: $color-primary;

    &--external {
      padding: 0;

      &:after {
        content: "\f08c";
        font-family: $font-awesome;
        font-size: $text-xxx-small;
        display: inline-block;
        margin: 0 5px 0 0;
        transform: scaleX(-1);
      }

      &:hover {
        color: $color-green;
      }

      &.with-icon {
        i {
          display: inline-block;
          vertical-align: middle;
        }
      }
    }

    &--violation {
      background-color: $color-danger;
      font-size: $text-small;
      color: $color-white;
      padding: 2px 8px 5px;

      &::after {
        content: "\e943";
        font-family: $font-sallaIcon;
        display: inline-block;
        margin-right: 15px;
        position: relative;
        top: 2px;
        color: rgba($color-white, 0.5);
      }
    }

    &--underline {
      text-decoration: underline;
    }

    &--small {
      font-size: $text-small !important;
    }
  }

  &-check-entity {
    width: 200px;
    height: 42px;
    @include mediaMaxWidth($screen-phablet) {
      width: 100%;
    }
  }

  &--dashed {
    border-bottom: 1px dashed;
  }

  &--sm-hidden-text {
    @include mediaMaxWidth($screen-phablet) {
      span {
        display: none;
      }

      i {
        margin: 0 !important;
        font-size: $text-large;
      }
    }
  }

  &-reset {
    margin-right: 15px;

    i {
      font-size: $text-small;
      margin-left: 10px;
      position: relative;
      top: -1px;
    }
  }

  &.btn-tiffany {
    &:hover {
      background: $color-secondary;
    }
  }

  &--tooltip-toggle {
    .tooltip {
      @include b-radius($b-radius-sm);
      box-shadow: 0 0 4px rgba($color-black, 0.25);
      padding: 8px;
      background: $color-white;
      color: $color-dark-200;
      font-size: $text-xxxx-small;
      top: 30px;
      @include centerX;
      word-break: break-all;
    }
    &.btn-icon {
      background-color: transparent !important;
      border: 0 !important;
      i {
        color: $color-dark-50 !important;
        &:hover {
          color: $color-primary !important;
        }
      }
    }
  }

  &--center {
    margin-right: auto;
    margin-left: auto;
    display: block;
  }

  &.button-report-export {
    background-color: $color-white;
  }

  &-print {
    background-color: $color-gray-50;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.08) !important;
    top: -1px;

    &:hover,
    &:focus {
      background-color: $color-gray-50;
      box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.08) !important;
    }

    + .dropdown {
      right: 50%;
      transform: translateX(50%);
    }
  }

  // for buttons with icons in start
  &-icon-prepend {
    i,
    svg img {
      display: inline-block;
      vertical-align: middle;
      margin-left: 5px;
      font-size: inherit;
    }
  }

  &--small {
    font-size: $text-xx-small;
    padding: 3px 10px 3px;
  }

  &--tooltip-toggle {
    .tooltip {
      @include b-radius($b-radius-sm);
      box-shadow: 0 0 4px rgba($color-black, 0.25);
      padding: 8px;
      background: $color-white;
      color: $color-dark-200;
      font-size: $text-xxxx-small;
      top: 30px;
      @include centerX;
      word-break: break-all;
    }
  }

  // three dots btn -- for more options or nav -- dark and light
  &--more-nav {
    padding: 15px;
    border: none;
    text-align: center;
    background: $color-gray-25;
    @include b-radius(15px);

    .dot,
    .dot:before,
    .dot:after {
      width: 3px;
      height: 3px;
      @include b-radius(50%);
      background-color: $color-dark-300;
      position: absolute;
    }

    .dot {
      top: 50%;
      right: 50%;
      transform: translateX(50%) translateY(-50%);

      &:before,
      &:after {
        content: "";
      }

      &:before {
        right: 6px;
      }

      &:after {
        left: 6px;
      }
    }

    .more-options {
      display: none;
      min-width: 100px;
      position: absolute;
      top: 100%;
      left: 0;
      @include b-radius($b-radius-sm);
      margin-top: 5px;
      z-index: 99;
      background-color: $color-white;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.07);

      li {
        width: 100%;

        a {
          display: block;
          width: 100%;
          height: auto;
          padding: 6px 15px;
          font-size: $text-xx-small;
          text-align: right;
          @include transi(background);

          &:hover {
            background-color: $color-gray-25;
          }
        }

        &:last-child {
          border: none !important;
        }
      }

      &.reveal {
        display: block;
      }
    }

    // wider version {
    &.wider {
      .more-options {
        min-width: 200px;

        li {
          a {
            padding: 10px;
          }
        }
      }
    }

    &.active {
      background-color: $color-gray-200;

      &:hover,
      &:focus,
      &:active {
        background-color: $color-gray-200;
      }
    }
  }

  &--outlined {
    color: $color-dark-300;
    border: 1px solid $color-gray-200;
    background: transparent;
    text-decoration: none !important;

    &.danger {
      color: $color-danger !important;
      border-color: $color-danger !important;

      &:hover {
        border-color: $color-danger !important;
      }
    }

    &.primary {
      color: $color-primary-l !important;
      border-color: $color-secondary !important;

      * {
        color: $color-primary-l !important;
      }

      &:hover {
        background-color: transparent !important;
        color: $color-primary-l !important;
        border-color: $color-secondary !important;

        * {
          color: $color-primary-l !important;
        }
      }
    }

    &.trans {
      border: none !important;
    }

    &:hover {
      border-color: $color-gray-200;
    }
  }

  &--active {
    background-color: $color-primary-l;
    pointer-events: none;

    &:focus,
    &:active {
      background-color: $color-primary-l !important;
      border-color: $color-primary-l !important;
    }
  }

  &.button-report-export {
    background-color: $color-white;
  }

  &-print {
    background-color: $color-gray-50;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.08) !important;
    top: -1px;

    &:hover,
    &:focus {
      background-color: $color-gray-50;
      box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.08) !important;
    }

    + .dropdown {
      right: 50%;
      transform: translateX(50%);
    }
  }

  // for buttons with icons in start
  &-icon-prepend {
    i,
    svg img {
      display: inline-block;
      vertical-align: middle;
      margin-left: 5px;
      font-size: inherit;
    }
  }

  &--tooltip-toggle {
    .tooltip {
      @include b-radius($b-radius-sm);
      box-shadow: 0 0 4px rgba($color-black, 0.25);
      padding: 8px;
      background: $color-white;
      color: $color-dark-300;
      font-size: $text-xxxx-small;
      top: 30px;
      @include centerX;
      word-break: break-all;
    }
  }

  &-copy {
    &:not(.without-icon){
      &:before {
        content: '\efd3';
        font-family: $font-sallaIcon;
        margin-left: 5px;
      }
    }

    &.solo {
      &:before {
        margin-left: 0;
      }

      &.copied {
        &:before {
          content: "\ea9d";
        }
      }

      &:hover {
        border-color: #eee;
      }

      &.without-hover {
        &:hover {
          color: $color-primary;
          border-color: $color-primary;
          background-color: transparent;
        }
      }
    }
  }

  &--dropdown {
    padding: 7px 10px 9px 10px !important;
    font-size: $text-small;
    margin-top: 6px;
    text-align: center;
    min-width: 125px;

    &:before {
      content: "\e90c";
      font-family: $font-sallaIcon;
      color: $color-secondary-50;
      position: relative;
      top: 2px;
      display: inline-block;
      background-color: $color-primary-l;
      @include b-radius(50%);
      font-size: $text-small;
      width: 24px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      margin-left: 10px;
    }

    &:after {
      content: "إضافة منتج جديد";
      font-size: $text-small;
      color: $color-primary-l;
    }

    i {
      margin-right: 8px;
      font-size: $text-large;
      top: 3px;
      float: left;
      position: relative;
      display: inline-block;
      @include transi();
    }

    &[aria-expanded="true"] {
      i {
        transform: rotate(180deg);
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {
      &:after {
        content: "منتج جديد";
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      margin-top: 11px;
      font-size: $text-small;

      &:before {
        width: 22px;
        height: 22px;
        line-height: 23px;
        font-size: $text-small;
      }

      i {
        right: 0;
      }
    }
  }

  &--dropdown-add-field {
    padding: 8px 10px 8px 10px !important;

    i {
      top: 1px;
    }

    &:before {
      font-size: $text-xxx-small;
      width: 20px;
      height: 20px;
      line-height: 21px;
      top: 1px;
    }

    &:after {
      content: "اضافة حقل جديد" !important;
      font-size: $text-x-small;
    }

    @include mediaMaxWidth($screen-phablet) {
      width: 100%;
      text-align: right;
    }
  }

  &-filter-toggle {
    color: $color-dark-300;
    font-size: $base-size;
    position: relative;
    top: -2px;

    i {
      color: $color-dark-300;
      margin-left: 4px;
      font-size: $base-size;
    }

    .sicon-cancel {
      display: inline-block;
      width: 22px;
      height: 22px;
      color: $color-white;
      line-height: 23px;
      @include b-radius(50px);
      background: $color-danger;
    }
  }

  &-help-center {
    background-color: $color-gray-25;
    color: $color-dark-200;
    border: none;
    font-size: $text-small !important;

    i {
      color: inherit;
      font-size: $text-small !important;
      position: relative;
      top: -1px;
    }
  }

  &.button-report-export {
    background-color: $color-white;
  }

  &-option-groupe {
    padding: 0;
    width: fit-content;
    margin-right: auto;
    border: 1px solid $color-gray-300;
    @include b-radius($b-radius-sm);
    position: relative;

    .btn {
      color: $color-dark-300;
      font-size: $base-size;

      &:first-of-type {
        padding: 4px 7px 6px 5px;

        i {
          margin-left: 5px;
        }
      }

      &:last-of-type {
        padding: 7px 8px 7px;

        &:hover,
        &:focus {
          background-color: rgba($color-gray-400, 0.5) !important;
        }
      }
    }

    &::before {
      content: "";
      width: 1px;
      height: 100%;
      position: absolute;
      left: 35px;
      top: 0;
      background-color: $color-gray-300;
    }
  }

  &-add-option-group {
    background-color: $color-gray-25;
    border-color: $color-gray-200;
    font-size: $text-small;
    font-weight: 500;
    color: $color-dark-200 !important;
    padding: 10px;
    margin: 0 !important;

    i {
      color: $color-dark-200;
      margin-left: 5px;
      font-size: $text-medium;
    }
  }

  &-upload-file {
    color: $color-dark-200;

    i {
      color: $color-gray-400;
    }
  }

  &.glyphicon {
    line-height: unset !important;
  }

  &.load_help_center_modal {
    @include mediaMaxWidth($screen-phones) {
      position: absolute;
      left: -20px;
      margin: 0;
      @include b-radius(0 50px 50px 0);
      padding-left: 5px;

      span {
        display: none;
      }
    }
  }

  &-add {
    padding: 9px 12px;
    font-size: $text-small;

    i {
      margin-left: 10px;
      position: relative;
      font-size: $text-medium;
    }
  }

  &-search {
    padding: 7px 19px;

    i {
      margin-left: 5px;
    }
  }

  &-nav {
    color: $color-dark-300;
    font-size: $text-x-small;
    @include b-radius(100px);
    background-color: transparent;
    border: 1px solid $color-gray-300;

    &:before,
    &:after {
      font-family: $font-sallaIcon;
      color: $color-dark-300;
      font-size: $text-x-small;
      position: relative;
      top: 1px;
      margin-left: 5px;
    }

    &-prev {
      &:before {
        content: "\e945";
      }
    }

    &-next {
      &:after {
        content: "\e943";
        margin: 0 5px 0 0;
      }
    }
  }

  &-order-nav {
    font-size: $text-x-small;
    padding: 8px 16px 8px 12px;
    @include transi();

    i {
      font-size: $text-small;
      @include transi();
    }

    &.next {
      i {
        margin-right: 5px;
      }
    }

    &.prev {
      padding: 8px 12px 8px 16px;

      i {
        margin-left: 5px;
      }
    }

    &.disabled {
      &:hover {
        cursor: not-allowed;
      }
    }
  }

  &-outline {
    &-primary {
      background-color: transparent;
      color: $color-primary-l !important;
      border-color: $color-secondary-50;

      &:hover,
      &:focus,
      &:active {
        color: $color-primary !important;
      }
    }
  }

  &[disabled="disabled"] {
    opacity: 0.3;
    pointer-events: none;
  }

  &.btn-apple-pay {
    margin: 0;
    background: $color-white url("/cp/assets/images/apple_pay_logo.svg") center no-repeat;
    background-size: 42px;
    border: 1px solid $color-black;

    &--large {
      height: 52px;
    }

    &--small {
      height: 36px;
    }

    &:focus,
    &:active {
      box-shadow: none !important;
      outline: none !important;
    }
  }

  &.disabled {
    opacity: 0.3;
    pointer-events: none;
  }

  &--return {
    @include transi();
    padding: 5px 0;
    font-size: $base-size;

    i,
    svg {
      display: inline-block;
      vertical-align: middle;
      margin-left: 10px;
    }

    &:hover {
      color: $color-primary-d;

      * {
        color: $color-primary-d;
      }
    }
  }

  &.btn-file {
    input[type="file"] {
      width: 100%;
      min-width: unset;
      left: 0;
    }
  }

  &-danger {
    background-color: transparent !important;
    border-color: $color-danger !important;
    color: $color-danger !important;

    &.outlined {
      color: $color-danger !important;
      background-color: transparent !important;
    }

    // if flat ---
    &.btn-flat {
      color: $color-danger !important;
      background-color: transparent !important;
    }
  }

  &-confirm {
    background: $color-primary;
    color: $color-white;
  }

  &.load_help_center_modal {
    @include mediaMaxWidth($screen-phones) {
      position: absolute;
      left: -20px;
      margin: 0;
      @include b-radius(0 50px 50px 0);
      padding-left: 5px;

      span {
        display: none;
      }
    }
  }

  &-add {
    padding: 9px 12px;
    font-size: 14px;

    i {
      margin-left: 10px;
      position: relative;
      font-size: $text-medium;
    }
  }

  &-search {
    padding: 7px 19px;

    i {
      margin-left: 5px;
    }
  }

  &-delete {
    width: 25px;
    height: 25px;
    background-color: $color-danger;
    border-color: $color-danger;
    text-align: center;
    @include b-radius($b-radius-sm);
    padding: 0;
    border: 1px solid $color-danger;

    i {
      color: $color-white;
      font-size: $text-xx-small;
      position: absolute;
      @include centerXY;
    }

    &:hover {
      background-color: darken($color-danger, 10%);
    }

    &-circle {
      @include b-radius(50%);
    }

    &--dark {
      background: $color-dark-300;

      &:hover {
        background-color: $color-black;
      }
    }

    &--small {
      width: 18px;
      height: 18px;

      i {
        font-size: $text-xxx-small;
      }
    }

    &--light {
      background-color: transparent;

      i {
        color: $color-danger;
      }

      &:hover {
        i {
          color: $color-white;
        }
      }
    }
  }

  &-edit {
    width: 25px;
    height: 25px;
    background-color: $color-primary;
    text-align: center;
    @include b-radius($b-radius-sm);
    padding: 0;
    border: 1px solid $color-primary;

    i {
      color: $color-white;
      font-size: $text-xx-small;
      position: absolute;
      @include centerXY;
    }

    &:hover {
      background-color: darken($color-primary, 10%);
    }

    &-circle {
      @include b-radius(50%);
    }

    &--outlined {
      background-color: transparent;
      border: 1px solid $color-secondary;

      i {
        color: $color-primary-l;
      }

      &:hover {
        background-color: transparent;
      }
    }
  }

  &-nav {
    color: $color-dark-300;
    font-size: $text-x-small;
    @include b-radius(9999px);
    background-color: transparent;
    border: 1px solid $color-gray-300;

    &:before,
    &:after {
      font-family: "sallaicons";
      color: $color-dark-300;
      font-size: $text-x-small;
      position: relative;
      top: 1px;
      margin-left: 5px;
    }

    &-prev {
      &:before {
        content: "\e945";
      }
    }

    &-next {
      &:after {
        content: "\e943";
        margin: 0 5px 0 0;
      }
    }
  }

  &.btn-apple-pay {
    margin: 0;
    background: $color-white url("/cp/assets/images/apple_pay_logo.svg") center no-repeat;
    background-size: 42px;
    border: 1px solid $color-black;

    &--large {
      height: 52px;
    }

    &--small {
      height: 36px;
    }

    &--danger {
      * {
        color: $color-danger;
      }
    }

    &:focus,
    &:active {
      box-shadow: none !important;
      outline: none !important;
    }
  }

  &--return {
    @include transi();
    padding: 5px 0;
    font-size: 15px;

    i,
    svg {
      display: inline-block;
      vertical-align: middle;
      margin-left: 10px;
    }

    &:hover {
      color: $color-primary-d;

      * {
        color: $color-primary-d;
      }
    }
  }

  &.btn-file {
    input[type="file"] {
      width: 100%;
      min-width: unset;
      left: 0;
    }
  }

  &[disabled] {
    pointer-events: none;
  }

  &-yellow {
    background: #ff9e011a !important;
    color: $color-warning !important;
    border-color: rgba($color-warning, 0.5) !important;
  }

  &--has-loading {
    display: flex;
    align-items: center;
    position: relative;
    transition: 0.3s;

    .loader {
      width: 16px;
      height: 16px;
      border-width: 2px;
      position: absolute;
      left: 10px;
      opacity: 0;
      visibility: hidden;
      transition: 0.3s;
    }

    &:after {
      content: "";
      @include flexable();
      width: 18px;
      height: 18px;
      display: inline-block;
      padding: 0;
      @include b-radius(100%);
      border: 2px solid;
      border-top-color: darken($color-primary, 6%);
      border-bottom-color: $color-white;
      border-left-color: darken($color-primary, 6%);
      border-right-color: $color-white;
      animation: loader 0.8s ease-in-out infinite;
      position: absolute;
      left: 16px;
      top: 11px;
      opacity: 0;
      visibility: hidden;
    }

    &.small-btn {
      &:after {
        top: 7px;
      }
    }
  }

  &--is-loading {
    padding-left: 30px;

    &[data-exculde-date],
    &[data-exclude-date-confirm] {
      min-width: 80px;
    }

    &:after {
      visibility: visible;
    }

    pointer-events: none !important;

    .loader {
      display: block !important;
      opacity: 1;
      visibility: visible;
    }
  }
}

.apply-shortcut-msg {
  @include transi(opacity);
  text-transform: lowercase;

  &:disabled {
    opacity: 0.5;
    pointer-events: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

.copy-referral-link {
  &.copied {
    &:before {
      content: "\ea9d";
    }
  }
}

.sicon-spin {
  animation: sicon-spin 2s infinite linear;
  -webkit-animation: sicon-spin 2s infinite linear;
}

@keyframes sicon-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

#heading_help_btn {
  @include mediaMaxWidth($screen-phones) {
    position: absolute;
    left: -20px;
    @include b-radius(0 50px 50px 0);
    padding: 3px 5px;
    margin: 0;
    z-index: 99999;

    > span {
      display: none;
    }
  }
}

// inline loader ---
[data-inline-loader] {
  .loader.loader--smaller {
    width: 17px !important;
    height: 17px !important;
    margin-top: -3px;
  }

  &.loading {
    pointer-events: none !important;
  }
}

.btn-info.btn-tiffany,
.btn-info.btn-save {
  background-color: $color-secondary-50;
  border-color: $color-secondary-50;
  color: $color-primary-l;
}

.btn-info.btn-close {
  background-color: $color-dark-100;
  border-color: $color-dark-100;
}
