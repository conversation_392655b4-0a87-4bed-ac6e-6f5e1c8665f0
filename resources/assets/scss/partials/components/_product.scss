#product_images {
  .img-controls {
    position: absolute;
    @include b-radius(50%);
    top: 17px;
    right: 11px;
    width: 30px;
    height: 30px;
    text-align: center;
    cursor: pointer;
    background: rgba($color-black, 0.2);
    z-index: 9;
    color: $color-white;

    i {
      font-size: $text-xx-small;
      position: absolute;
      @include centerXY();
    }

    &.three-d {
      right: 50px;

      i {
        font-size: $text-xx-medium;
      }

      .loader {
        display: none
      }

      .filepond {
        opacity: 0;

        input {
          width: 30px;
          height: 30px;
          cursor: pointer;
          position: absolute;
          z-index: 999;
        }
      }
    }

    &.uploading {
      background-color: transparent;

      .loader {
        display: block;
        position: relative;
        top: 5px;
        right: 5px;
      }

      i {
        display: none;
      }
    }

    &.uploaded {
      background-color: $color-primary;
      pointer-events: none;

      .loader {
        display: none;
      }

      i {
        color: $color-white;
      }
    }

    &.disabled {
      pointer-events: none;
      opacity: .3;
    }
  }

  .video {
    .img-controls.three-d {
      display: none;
    }
  }

  .slim-area {
    position: relative;

    .watermark {
      display: none;
      font-size: 100px;
      color: $color-white;
      width: 100px;
      height: 100px;
      opacity: 0.8;
      cursor: pointer;
      position: absolute;
      @include centerXY();
    }

    .slim-result {
      &-video {
        position: relative;

        .watermark {
          display: block;
        }
      }
    }

    .product-photo-seo {
      width: calc(100% - 30px);
      position: absolute;
      left: 11px;
      bottom: 50px;
      z-index: 55;

      i {
        position: absolute;
        top: 10px;
        left: 13px;
        font-size: $text-xx-small;
        font-weight: bold;
        font-style: normal;
        color: $color-white;
        z-index: 9;
        pointer-events: none;

      }

      input {
        &.form-control {
          &.seo-tags {
            display: block;
            width: 40px;
            height: 40px;
            position: absolute;
            top: 0;
            left: 0;
            border: none;
            padding: 0;
            @include transi(all, 500ms, cubic-bezier(0.86, 0, 0.07, 1));
            transform-origin: left;
            background-color: rgba($color-black, 0.2);
            color: transparent;
            font-weight: bold;
            cursor: pointer;
            @include b-radius(20px);

            &::placeholder {
              @include transi(all, 500ms, cubic-bezier(0.86, 0, 0.07, 1));
              color: transparent;
            }

            &:hover {
              background-color: rgba($color-black, 0.3);
            }

            &:focus, &:active {
              width: 100%;
              padding: 0 1.5rem 4px 40px;
              color: rgba($color-white, 0.95);
              cursor: auto;
              background-color: rgba($color-black, 0.5);

              &::placeholder {
                color: rgba($color-white, 0.5);
              }
            }

            &.touched {
              background: rgba($color-primary, 0.9);
            }
          }
        }
      }

      @include mediaMaxWidth($screen-phones) {
        width: calc(100% - 12px);
        left: 6px;
        bottom: 35px;
        i {
          top: 5px;
          left: 8px;
          font-size: $text-xx-small;
        }
        input {
          &.form-control {
            &.seo-tags {
              width: 30px;
              height: 30px;
              @include b-radius(15px);
              font-size: 12px;

              &:focus, &:active {
                width: 100%;
                padding: 0 1rem 4px 30px;
              }
            }
          }
        }
      }
    }
  }

  &.custom {
    ._slim {
      @include flexable(flex-start, flex-start, column);

      .slim-area {
        flex: auto;
        width: 100%;
        @include flexable(flex-start, flex-start, column);

        .slim-result {
          height: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
            @include b-radius(4px 4px 0 0);
          }
        }
      }

      .product-photo-meta {
        width: 100%;
        text-align: left;

        i {
          position: initial;
          display: inline-block;
        }
      }
    }
  }
}

.product-form {
  .thumbnail .thumb {
    width: 100%;
    padding-top: 100%;
    overflow: hidden;
    position: relative;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
    }
  }

  .form-group {
    .input-group {
      .form-control {
        position: relative;

        &.ltr {
          unicode-bidi: plaintext;
        }
      }
    }
  }
}