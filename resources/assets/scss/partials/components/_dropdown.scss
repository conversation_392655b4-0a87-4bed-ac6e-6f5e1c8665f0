.rec {
  &-dropdown {
    $dropdown-root: rec-dropdown;
    min-width: 200px;
    @include b-radius($b-radius);
    background: $color-white;
    @include transi();

    .#{$dropdown-root} {
      &__search,
      &__list {
        width: 100%;
        height: auto;
      }

      &__search {
        padding: 10px;

        .form-control {
          width: 100%;
        }
      }

      &__list {
        max-height: 150px;
        overflow-y: scroll;
        @include scrollBar(2px, $color-gray-200, $color-gray-100);

        .rec-dropdown__item {
          @include flexable(center, flex-start, row);
          width: 100%;
          height: auto;
          padding: 10px;
          border-top: 1px solid $color-gray-200;
          @include transi();

          &__thumb {
            margin: 0 0 0 7px;
            overflow: hidden;

            img {
              width: 25px;
              height: auto;
              @include b-radius(50%);
              object-fit: cover;
            }
          }

          &__label {
            font-size: $text-xx-small;
          }

          &:hover {
            background-color: $color-gray-25;
          }

          &.selected {
            background-color: rgba($color-primary, 0.1);
            color: $color-primary;
          }
        }
      }
    }
  }
}


.three-dots-dropdown {
  display: inline-flex;

  .dropdown-toggle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    line-height: 30px;
    color: $color-dark-300;
    transition: 0.3s;
  }

  .dropdown-toggle:hover,
  &.open .dropdown-toggle {
    color: $color-secondary-d;
  }

  .dropdown-menu {
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.09);

    li {
      margin: 0;

      &:not(:last-child) {
        border-bottom: 1px solid $color-gray-25;
      }

      a {
        padding: 6px 15px 10px;

        &:hover {
          background-color: $color-gray-50;
        }

        &.is-disabled {
          opacity: 0.5;
          pointer-events: none;

          .icon {
            color: gray;
          }
        }
      }
    }

    .icon {
      color: $color-secondary;
    }
  }
}

.dropdown-menu {
  &.dropdown-menu-right,
  &.dropdown-menu-left {
    &.mobile-top {
      z-index: 99;

      .delete-product {
        color: $color-danger !important;

        * {
          color: $color-danger !important;
        }
      }

      @include mediaMaxWidth($screen-phones) {
        transform: translate3d(0, -112%, 0px);
      }
    }
  }
}