.label {
  &-rounded {
    &.inline {
      line-height: 1;
      padding: 1px 5px 3px;
    }
  }

  &-plus,
  &-team {
    line-height: 1 !important;
    padding: 2px 5px 4px !important;
  }

  &-plus {
    background: $color-plan-plus;
    border-color: $color-plan-plus;
  }

  &-team {
    background: $color-plan-team;
    border-color: $color-plan-team;
  }

  &-info {
    color: $color-info;
    border-color: rgba($color-info, 0.5);
    background: rgba($color-info, 0.15);
  }

  &--rounded {
    @include b-radius(999px);
  }

  &--primary-bg {
    background-color: $color-primary;
    color: $color-white !important
  }

  &--bold {
    font-weight: bold !important;
  }

  &#value_wrap {
    width: fit-content;
    padding: 5px 15px;
  }

  &-danger {
    color: $color-danger;
    border-color: rgba($color-danger, 0.5);
    background: rgba($color-danger, 0.05);
  }
  
  &-warning-100 {
    border-color: rgba($color-warning, 0.2);
    background: rgba($color-warning, 0.1);
    color: $color-warning;
  }
}