.cc-entry {
  $cc-entry-root: &;
  input[type="radio"]{display: none;}

  &__wrapper {
    @include flexable(center, flex-start, row);
    padding: 8px 50px 8px 8px !important;
    position: relative;
    @include b-radius($b-radius-sm);
    border: 1px solid $color-gray-200;
    margin: 0;

    &:before,
    &:after {
      top: 50%;
      transform: translateY(-50%);
      @include b-radius(50%);
      position: absolute;
    }

    &:before {
      content: "";
      width: 22px;
      height: 22px;
      right: 13px;
      border: 1px solid $color-primary-l;      
    }

    &:after {
      content: '\ea9d';
      font-family: 'sallaicons', serif;
      visibility: visible;
      color: $color-primary-l;
      font-size: 13px;
      right: 17px;
      top: 20px;
    }
  }

  &__logo {
    max-width: 45px;
    max-height: 24px;
  }

  &__number {
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    letter-spacing: 2px;

    span {
      letter-spacing: unset;
    }

    &:after {
      content: '****';
      display: inline-block;
      letter-spacing: 2px;
      font-weight: bold;
      transform: translateY(3px);
      margin-right: 5px;
    }
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: row;
    margin-right: auto;
  }

  &--clickable {
    #{$cc-entry-root} {
      &__wrapper {
        cursor: pointer;
      }
      &__logo {
        max-width: 45px;
        max-height: 24px;
      }

      &__number {
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        letter-spacing: 2px;

        span {
          letter-spacing: unset;
        }

        &:after {
          content: '****';
          display: inline-block;
          letter-spacing: 2px;
          font-weight: bold;
          transform: translateY(3px);
          margin-right: 5px;
        }
      }

      &__status {
        justify-self: flex-end;
        margin-right: auto;
      }
    }
  }

  input:checked + {
    #{$cc-entry-root} {
      &__wrapper {
        &:before {
          border-color: $color-primary-l;
        }

        &:after {
          opacity: 1;
        }
      }
    }
  }
}