form {
  &.form {
    &--no-m {
      @include mediaMaxWidth($screen-phones) {
        .form-group {

          div[class*="col-sm-"],
          div[class*="col-md-"] {
            margin-top: 0 !important;
          }
        }
      }

      @include mediaMaxWidth($screen-laptop-small) {
        .form-group {
          div[class*="col-md-"] {
            margin-top: 0 !important;
          }
        }
      }
    }

    &--inline {
      @include flexable(center, flex-start, row);

      > * {
        flex: 1 0 0;
        margin: 0 0 0 10px;

        &:last-child {
          margin: 0;
        }
      }
    }

    &--light {
      .form-group {
        label {
          font-size: $text-small;
          margin: 0 0 10px 0;
        }

        .form-control,
        .bootstrap-select .btn.dropdown-toggle.btn-default {
          min-height: 40px;
          padding: 10px 15px;
          @include b-radius($b-radius-sm);
          border: 1px solid $color-gray-200;

          &:hover,
          &:active,
          &:focus {
            border-color: $color-gray-300;
          }

          &[type='number'] {
            -moz-appearance: textfield;

            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }
          }
        }

        .bootstrap-select {
          display: block;
          width: 100% !important;
          min-height: 40px;

          .btn.dropdown-toggle.btn-default {
            border-color: $color-gray-200 !important;
          }

          select {
            pointer-events: none;
          }
        }

        .input-group {
          .input-group-addon {
            @include b-radius(0 3px 3px 0);
            border-color: $color-gray-200;
          }

          .form-control {
            border-right: none !important;
            @include b-radius(3px 0 0 3px);
            padding-right: 5px;
          }

          // addon is at the end ---
          &.input-group-append {
            width: 100%;

            .form-control {
              border: 1px solid $color-gray-200;
              padding: 8px 15px 10px 45px;
              @include b-radius($b-radius-sm);

              &:hover,
              &:active,
              &:focus {
                border-color: $color-gray-300;
              }
            }

            .input-group-addon {
              border: none;
              padding: 0;
              position: absolute;
              top: 18px;
              transform: translateY(-50%);
              left: 25px;
              z-index: 99;

              @include mediaMaxWidth($screen-phones) {
                top: 16px;
              }
            }
          }
        }
      }

      @include mediaMaxWidth($screen-phones) {
        .form-group {
          label {
            font-size: $text-x-small;
            margin: 0 0 5px 0;
          }

          .form-control,
          .bootstrap-select .btn.dropdown-toggle.btn-default {
            min-height: 36px;
          }

          .bootstrap-select {
            min-height: 36px;
          }
        }
      }
    }

    // coupon filter form ----
    &.coupon-filter {
      #report_filter_data {
        @include flexable(center, flex-start);
        margin-left: 0;

        > div {
          flex: auto;
        }
      }

      .report_filters {
        margin: 0 0 0 10px !important;

        &.type {
          flex: 0 0 150px !important;

          &.wide {
            width: 100%;
            flex: auto !important;
          }
        }

        &#report_date_month {
          @include flexable(center, flex-start, row);

          > * {
            flex: 1;

            .bootstrap-select {
              width: 100%;
            }
          }
        }
      }

      .submit {
        flex: 0 0 80px;
        margin: 0 !important;
      }

      @include mediaMaxWidth($screen-phablet) {
        flex-direction: column;

        .report_filters {
          margin: 0 0 10px 0 !important;
          flex: 1 1 auto;
          width: 100%;

          &.type {
            flex: 1 0 auto;
          }
        }

        .submit {
          flex: 1 0 auto;
        }
      }
    }
  }

  .field-section {
    background: $color-white !important;
    padding: 0;
    @include b-radius($b-radius);
    border: 1px solid $color-gray-200;
    margin-bottom: 0;

    &:not(:first-of-type) {
      margin-top: 20px !important;
    }

    > div.field-row-header,
    .field-row-header-splitter {
      background-color: $color-gray-50;
      padding: 0;
      margin-bottom: 0 !important;
      border-bottom: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm $b-radius-sm 0 0);
      @include flexable(center, space-between, row);

      .field-required-col,
      .field-label-col {
        padding: 0;
      }

      .field-label-col {
        .label-info {
          background-color: transparent !important;
          border: none !important;
          font-size: $text-small;
          color: $color-dark-300;
          line-height: 0;
          margin: 0;

          i {
            position: relative;
            font-size: $text-medium;
            margin-left: 5px;
          }
        }
      }

      .field-required-col {
        @include flexable(center, flex-end, row);
        position: relative;

        .btn-group {
          .btn {
            border: none;
            width: 30px;
            height: 35px;
            padding: 0;
            background-color: transparent;

            &:hover,
            &:focus,
            &:active {
              z-index: 0;
            }

            &.remove-button {
              background-color: $color-danger !important;
              @include b-radius(4px 0 0 0);
              margin-left: -1px;

              i {
                position: relative;
                top: -3px;
                color: $color-white
              }
            }
          }
        }
      }
    }

    .field-row-header-splitter {
      position: relative;
      height: 35px;
      border-bottom: none !important;

      &.row {
        margin-right: 0;
      }

      .btn-group {
        .btn {
          border: none;
          width: 30px;
          height: 35px;
          padding: 0;
          background-color: $color-white;

          &:hover,
          &:focus,
          &:active {
            z-index: 0;
          }
        }

        .remove-button {
          background: $color-danger;

          i {
            position: relative;
            top: -3px;
          }
        }
      }

      &:after,
      &:before {
        content: '';
        height: 100%;
        width: 1px;
        position: absolute;
        top: 0;
        background: $color-gray-200;
        z-index: 5;
      }

      &:after {
        left: 97px
      }

      &:before {
        left: 68px
      }
    }

    .field-row-header {
      height: 35px;
      margin-bottom: 0;
    }

    .fields-wrapper {
      padding: 18px 12px 15px 12px;
    }

    h5 {
      font-size: $text-small;
      margin-top: 0;
    }

    .add_option {
      background-color: $color-primary !important;

      i {
        margin-left: 8px;
      }
    }

    .checkbox-inline {
      margin: 0 0 20px 0;
      position: relative;
      font-size: $text-small;

      .checker {
        top: 2px;
      }

      &.time-check {
        display: block;
        margin-right: 0;
      }

      &-upper {
        margin: 0 0 0 12px;
      }

      &-bottom {
        margin-bottom: 0;
        margin-right: 2px;
      }
    }

    .option-wrapper {
      padding: 0 8px;

      > div {
        padding-left: 0;
      }
    }

    .details-wrapper {
      > div:first-of-type {
        padding-left: 0;
      }
    }

    .rec-checkbox {
      margin-right: 2px !important;
    }

    .remove-button-option {
      background: $color-danger;
      border-color: $color-danger;
      padding: 0 !important;
      height: 20px;
      flex: 0 0 20px;
      position: relative;
      top: 8px;
      @include b-radius(50%);

      i {
        color: $color-white;
        font-size: $text-xxx-small;
        position: absolute;
        @include centerXY;
      }
    }

    .form-group {
      margin-bottom: 10px;
    }

    @include mediaMaxWidth($screen-tablet-l) {
      .remove-button-option {
        right: 0
      }
    }

    @include mediaMaxWidth($screen-phones) {
      .details-wrapper {
        > div:first-of-type {
          padding-left: 10px
        }
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      .option-wrapper {
        margin-left: 0;

        .remove-button-option {
          right: -5px
        }
      }
    }

    @include mediaMaxWidth(350px) {

      > div.field-row-header,
      .field-row-header-splitter {
        .field-required-col {
          .btn {
            width: 22px !important
          }

          &:before {
            left: 43px
          }

          &:after {
            left: 64px
          }
        }
      }

      .checkbox-inline {
        font-size: $text-xx-small !important;
      }

      .field-label-col {
        .label-info {
          font-size: $text-xx-small !important;
        }
      }
    }

    &--disabled {
      opacity: 0.8;
      cursor: not-allowed !important;

      label {
        cursor: not-allowed !important;
      }

      .input-group-addon {
        background: transparent !important;
      }

      .fields-wrapper {
        background: $color-gray-50;
      }
    }
  }

  .dropdown-menu > li > a:focus,
  .dropdown-menu > li > a:hover {
    background-color: $color-gray-100 !important;
  }

  .multiple-select-wrapper {
    .btn-group {
      width: 100% !important;
      //display: block !important;
    }
  }

  small {
    &.info {
      display: block;
      font-size: $text-xx-small;
      color: $color-dark-100;
      margin: 3px 0 0 0;
    }
  }

  &#activitiesForm {
    .input-group {
      @include flexable(center, flex-start, row);

      &-addon {
        width: 38px;
        padding: 8px 0 7px
      }

      .bootstrap-select {
        overflow: hidden;
      }
    }
  }
}

.form-group {

  input[disabled] {
    color: $color-dark-100;
    background: $color-gray-50;
  }

  .bootstrap-maxlength {
    position: absolute;
    @include centerX;
  }

  &.verified {
    &:after {
      content: '\ea9d';
      font-family: $font-sallaIcon;
      background: $color-primary;
      height: 18px;
      width: 18px;
      color: $color-white;
      font-size: $text-xx-small;
      position: absolute;
      @include center-v;
      left: 10px;
      text-align: center;
      z-index: 5;
      @include b-radius(50%)
    }

    &--with-label {
      &:after {
        top: 40px;
        transform: none;
      }
    }
  }

  &.coupon-field {
    .form-control {
      padding-right: 45px !important;

      &:focus,
      &:active {
        border-color: $color-primary;
        box-shadow: 0 0 4px rgba($color-primary, 0.05);
      }
    }

    &:before {
      content: '\e932';
      display: block;
      font-family: $font-sallaIcon;
      font-size: $text-large;
      color: $color-gray-200;
      position: absolute;
      @include centerY();
      right: 15px;
    }

    .coupon-submit {
      height: 78%;
      position: absolute;
      top: 10%;
      left: 5px;
      line-height: 1;
    }

    .coupon-delete {
      display: none;
      position: absolute;
      top: 18%;
      left: 8px;
      font-size: $text-xx-small;
      color: $color-white;
      padding: 3px 10px 5px;
      @include b-radius($b-radius-sm);

      @include mediaMaxWidth($screen-phones) {
        top: 15%;
        left: 6px;
      }
    }

    &--added {
      .form-control {
        pointer-events: none;
        padding-left: 180px !important;
      }

      .coupon-submit {
        background: transparent;
        border-color: transparent;
        color: $color-primary !important;
        left: 50px;
        pointer-events: none;

        * {
          color: $color-primary !important;
        }
      }

      .coupon-delete {
        display: block;
      }
    }
  }

  .bootstrap-maxlength {
    position: absolute;
    @include centerX;

    &.bottom {
      bottom: -14px;
    }
  }

  .searchbox {
    &--with-icon {
      .selectize-input {
        padding: 2px 45px 2px 12px;
        position: relative;

        &:after {
          content: '\ef09';
          font-family: $font-sallaIcon;
          color: $color-dark-300;
          font-size: $text-xx-medium;
          position: absolute;
          @include centerY;
          right: 11px !important;
          left: auto !important;
          margin-top: 0 !important;
        }
      }
    }
  }

  .vue-treeselect {
    &__placeholder {
      font-size: $text-x-small;
      color: $color-dark-100;

    }
  }

  .v-select-placeholder {
    color: $color-dark-100;
    font-size: $text-small;
    position: absolute;
    top: 8px;
    width: 90%;
    height: auto;
    right: 10px;
    z-index: 999;
    pointer-events: none;

    &--with-icon {
      right: 32px
    }
  }

  .input-group {
    input[disabled] ~ .input-group-addon {
      background: $color-gray-50;
    }

    .ui.dropdown {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }

    &--d-flex {
      //@include flexable(flex-start, flex-start, row);

      .input-group-addon {
        flex: 0 0 40px;
        height: 36px;
        padding: 0 0 0 10px;

        .btn-default {
          border: none;
          background-color: transparent;
        }
      }
    }
  }

  .input-group-addon {
    border-color: $color-gray-200;

    &.icon-color-picker-wrapper {
      border-right: 1px solid $color-gray-200;
      padding: 7px 5px;
    }

    &--dropdown {
      padding: 0;

      .bootstrap-select {
        width: 65px !important;

        .dropdown-menu--products-type > li > a:focus,
        .dropdown-menu--products-type > li > a:hover {
          background-color: rgba(93, 213, 196, .13) !important;
        }

        .multiple-select-wrapper {
          .btn-group {
            width: 100% !important;
          }
        }

        small {
          &.info {
            font-size: $text-medium;
            color: $color-dark-100;
          }
        }

        &.addon {
          .btn {
            padding: 0 10px 0 0 !important;
            border: 1px solid $color-gray-200;
            height: 36px;
          }
        }
      }

      .btn {
        padding: 0 10px 0 0;
        border: none;

        .caret {
          top: 54%;
          left: 5px
        }
      }
    }
  }

  // style sui dropdown like bootstrap dropdown if in input group adon
  &.search-field {
    .form-control {
      padding: 0 50px 0 0;
      height: 45px;
      font-size: $base-size;
      @include b-radius(0);

      &::placeholder {
        padding-right: 0;
        color: $color-gray-400;
      }
    }

    &:before {
      content: '\ef09';
      font-family: $font-sallaIcon;
      color: $color-gray-400;
      font-size: $text-medium;
      position: absolute;
      transform-origin: 0 0;
      @include centerY();
      right: 20px;
      transition: 0.3s;
    }

    .loader {
      position: absolute;
      right: 12px;
      top: 12px;
      visibility: hidden;
      opacity: 0;
    }

    &.with-border {
      border-top: 1px solid $color-gray-200;
      border-bottom: 1px solid $color-gray-200;
    }

    &.is-loading {
      &:before {
        opacity: 0;
        transform: rotate(20deg) translateY(-50%);
      }

      .loader {
        opacity: 1;
        visibility: visible;
      }
    }

    &.bulk-editor {
      .form-control {
        border: none;
        border-top: 1px solid $color-gray-200;
        border-bottom: 1px solid $color-gray-200;
      }
    }
  }

  &--initial {
    position: initial;
  }

  &--has-btn {
    .btn {
      position: absolute;
      top: 27px;
      left: 0;
      @include b-radius($b-radius-sm 0 0 $b-radius-sm);
      z-index: 5;
      height: 36px;
    }

    .form-control {
      padding-left: 110px;
    }
  }

  // search-field ---
  &.search-field {
    background-color: $color-gray-25;
    border-top: 1px solid $color-gray-200;

    .form-control {
      padding: 0 40px 0 0 !important;
      height: 40px;
      border: 1px solid $color-gray-200;
      font-size: $text-x-small;

      &:hover,
      &:focus,
      &:active {
        border-color: $color-gray-200;
      }
    }

    &:before {
      content: '\ef09';
      font-family: $font-sallaIcon;
      color: $color-gray-400;
      font-size: $text-medium;
      position: absolute;
      @include centerY;
      right: 10px
    }

    &::placeholder {
      padding-right: 0;
    }

    &.loading {
      &:before {
        display: none;
      }
    }
  }

  .file-uploader {
    .help-block {
      color: $color-danger;
    }

    .input-group-btn {
      .help-block {
        display: none;
      }
    }
  }

  .intl-tel-input {
    .flag-container {
      border-right: 0;
    }
  }

  .form-control {
    &.right-border {
      border-right: none !important
    }
  }

  .ui.dropdown {
    &.bs-dropdown {
      border: 1px solid $color-gray-200 !important;
      border-right: none !important;
    }

    &--outer-list {
      a.ui.label {
        opacity: 0;
        display: none !important;
      }
    }
  }

  .bootstrap-select {
    .btn.dropdown-toggle.btn-default {
      border-right: none !important;
      padding-right: 0;
    }

    &.full {
      .btn.dropdown-toggle.btn-default {
        border-right: 1px solid #eee !important;
        padding-right: 10px;
      }
    }
  }

  .controls {
    position: absolute;
    @include centerY;
    left: 5px;
    z-index: 5;
  }

  &--initial {
    position: initial;
  }

  .form-control {
    padding-right: 10px;

    &--d-flex {
      @include flexable(flex-start, flex-start, row);

      .input-group-addon {
        flex: 0 0 40px;
        height: 36px;
        padding: 0 0 0 10px;
      }
    }

    &--icon-picker {
      .input-group-addon {
        width: auto;
        height: auto;
        padding-left: 0;
      }

      .btn {
        &.icon_picker {
          border: none
        }

        &.iconpicker {
          min-width: 60px;
        }
      }
    }
  }

  .form-control {
    padding-right: 10px;

    &:focus {
      border-right-color: $color-gray-200;
    }
  }

  .rec-input-addon {
    position: relative;

    > i {
      position: absolute;
      @include centerY();
      right: 10px;
      font-size: $text-xx-small;
      color: $color-dark-100;
    }

    > input {
      padding-right: 30px;
    }

    .form-control {

      &.code_content {
        unicode-bidi: plaintext;
        text-align: right;
      }

      &[type='number'] {
        -moz-appearance: textfield;

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
      }

      &--transparent {
        padding: 0;
        background: transparent;
        border: none;
      }

      &--as-label {
        height: auto;
        color: $color-dark-200;
        font-size: $text-xx-small;
      }
    }
  }

  &.message {
    .form-control {
      border-bottom-left-radius: 0;
    }

    .input-group-addon {
      border-bottom-right-radius: 0;
    }
  }

  .message-block {
    padding: 4px 8px 0;
    border: 1px solid $color-gray-200;
    border-top: none;
    @include b-radius(0 0 3px 3px);
    background-color: $color-gray-50;
    height: 28px;

    .form-control {
      background: transparent;
      line-height: 1;
    }
  }


  &--with-border {
    border: 1px solid $color-gray-200;
    padding: 9px;
  }

  &--intl-tel-full {
    .input-group,
    .flag-container {
      position: initial !important;
    }

    .intl-tel-input {
      @include flexable(stretch, flex-start, row-reverse !important);
      top: auto !important;
      left: 0 !important;
      bottom: auto !important;
      right: auto !important;

      .flag-container {
        padding: 0;
      }

      .form-control {
        border-left: 0;
        padding-left: 0 !important;
        @include b-radius(0 2px 2px 0);
      }

      .country-list {
        width: 100%;
        max-height: 200px !important;
        bottom: auto !important;

        li {
          @include flexable(center, flex-start, row-reverse);
        }
      }
    }

    .iti-container {
      position: absolute !important;
    }

  }

  &.inline {
    @include flexable(center, space-between, row);

    label {
      padding-left: 15px;
    }
  }

  &--cont {
    padding: 10px 10px 20px;
    background-color: $color-gray-50;
    @include b-radius($b-radius);
    margin: 10px 0 0 0 !important;
  }

  &--small-margin {
    margin-bottom: 10px;
  }

  &--padded {
    padding: 10px 15px;

    @include mediaMaxWidth($screen-phones) {
      padding: 10px;
    }
  }

  #copy_key {
    border-top: none;
    @include b-radius(0 0 $b-radius-sm $b-radius-sm);
    padding-bottom: 8px;
  }

  // custom styling for zpier key ---
  &.zapier-key {

    div.form-control {
      display: table;
      word-break: break-all;
      @include b-radius($b-radius-sm $b-radius-sm 0 0);
    }

    #copy_key {
      border-top: none;
      @include b-radius(0 0 $b-radius-sm $b-radius-sm);
      padding-bottom: 8px;

      &:hover,
      &:focus,
      &:active {
        border-color: $color-gray-200;
        background-color: $color-gray-25;
      }
    }

    @include mediaMaxWidth($screen-phones) {
      div.form-control {
        font-size: $text-xx-small;
      }
    }
  }

  .label-small {
    font-size: $text-x-small;
  }

  &.has-error {
    .selectized-wrapper {
      border: 1px solid $color-danger
    }

    .vpd-icon-btn {
      border-color: $color-danger !important;
    }

    .vue-treeselect {
      &__control {
        border-color: $color-danger !important;
      }
    }

    .form-control {
      color: $color-danger !important;
    }

    .ql-toolbar {
      border-top-color: $color-danger !important;
      border-left-color: $color-danger !important;
      border-right-color: $color-danger !important;
    }

    .ql-container {
      border-bottom-color: $color-danger !important;
      border-left-color: $color-danger !important;
      border-right-color: $color-danger !important;
    }

    .vue-tel-input {
      &.fullWidth {
        .vti__dropdown, input {
          border-color: $color-danger !important;
          border-right: 0;
        }
      }
    }
  }

  // if used as label, and can't be within form-group
  label {
    font-size: $text-x-small;
    margin-bottom: 6px;

    &.input-label {
      font-size: $text-small;
      color: $color-dark-200;
    }
  }

  .form-control {
    &.c-pointer {
      cursor: pointer;
    }

    &.code_content {
      unicode-bidi: plaintext;
      text-align: right;
    }

    &--small {
      font-size: $text-xx-small !important;

      &::placeholder {
        font-size: $text-xx-small !important;
      }
    }

    &.bold {
      font-weight: bold;
    }

    &--transparent {
      padding: 0;
      background: transparent;
      border: none;
    }

    &--as-label {
      height: auto;
      color: $color-dark-100;
      font-size: $text-xx-small;
    }
  }

  &.inline-actions {
    @include flexable(flex-start, flex-start);
    border: 1px solid $color-gray-200;
    @include b-radius($b-radius-sm);

    > * {
      border: none;

      &.form-control {
        border: none !important;
      }

      &:first-child {
        flex: 0 0 1;
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      &:last-child {
        flex-shrink: 0;
      }
    }

    .actions {
      @include flexable(flex-start, flex-start);

      * {
        border: none !important;
        min-height: 36px;

        &:last-child {
          @include b-radius(3px 0 0 3px);
        }
      }
    }

    // rounded version ---
    &.rounded {
      > * {
        &:first-child {
          @include b-radius(0 50px 50px 0);
        }

        &:last-child {
          @include b-radius(50px 0 0 50px);

          > * {
            &:last-child {
              @include b-radius(50px 0 0 50px);
            }
          }
        }
      }
    }
  }

  &.bold {
    font-weight: bold;
  }


  &:focus {
    border-color: $color-gray-200;
  }

  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  // Mozilla --
  input[type=number] {
    -moz-appearance: textfield;
  }


  input.search {
    position: absolute !important;
    width: 100% !important;
    margin: 0 !important;
  }

  .text {
    display: block !important;
  }

  .control-label {
    &--small {
      font-size: $text-small !important;
    }
  }

  .option-label {
    display: block;
    font-family: $font-main;
    font-weight: bold;
    font-size: $base-size;
    color: $color-dark-300 !important;
    line-height: 1.2;
  }

  @include mediaMaxWidth($screen-phones) {
    .bootstrap-select.full-width {
      width: 100% !important;
    }
  }


  .rec {
    &-search-group {
      @include flexable(flex-start, flex-start, row);
      width: 100%;
      height: auto;
      margin: 0 auto;

      > * {
        height: auto;
        min-height: 50px;
        border: 1px solid $color-gray-200;
        @include b-radius(0);

        &:first-child {
          @include b-radius(0 $b-radius-sm $b-radius-sm 0);
          border-left: none !important;
        }

        &:last-child {
          @include b-radius(4px 0 0 4px);
          border-right: none !important;
        }
      }

      .wide {
        flex: auto;
      }
    }

    &-input-group-wrapper {
      padding: 15px;
      background: $color-gray-25;
      @include b-radius($b-radius-sm);
      margin: 0 0 20px 0;
      @include mediaMaxWidth($screen-tablet-l) {
        padding: 10px;
      }
    }
  }

  .input-group {
    &.rec {
      &-input-group {
        margin: 0 0 10px 0;
        width: 100%;

        .form-control {
          height: 45px;
          padding: 5px 15px 7px;
          font-size: $text-medium;
          color: $color-dark-200;
          border-color: $color-gray-200;

          &.ltr-el {
            text-align: left;
            letter-spacing: 1px;
          }
        }

        .btn {
          height: 45px;
          padding: 3px 15px 7px;
          border-color: $color-gray-200;

          i,
          img {
            display: inline-block;
            vertical-align: middle;
            width: 15px;
            height: auto;
            margin: 0 0 0 5px;
            opacity: 0.7;
          }

          &:hover {
            background: $color-gray-200;
          }
        }
      }
    }

    input.form-control {
      padding-right: 0 !important;
    }

    input.bordered {
      padding-right: 10px !important;
    }

    .multiselect-vue {
      .multiselect__tags {
        border-right: none;
        border-right: 0;
      }
    }
  }

  .favicon-wrapper {
    .favicon_style {
      display: block;
      width: 120px !important;
      height: 120px;
      @include b-radius($b-radius-sm);
      background: $color-gray-25;
      border: 1px dashed $color-gray-200;
      margin: 0 auto 10px;
    }

    #favicon_label {
      line-height: 1;
      width: auto;
      height: auto;
      background: transparent;

      &:before {
        content: '';
        @extend .favicon_style;
        width: 36px !important;
        height: 36px;
        display: inline-block;
        margin: 0 0 0 10px;
        border: 1px dashed $color-gray-200;
      }

      &.uploaded {
        &:before {
          display: none;
        }
      }
    }

    .media {
      margin: 8px 0 0 0;
    }

    .favicon_image_class {
      &.uploaded {
        position: absolute;
      }
    }

    .favicon-img {
      @extend .favicon_style;
      width: 36px !important;
      height: 36px;
      display: inline-block;
      margin: 0 0 0 10px;
      object-fit: contain;
      overflow: hidden;
      border: 1px dashed $color-gray-200;
    }

    .remove-product-photo {
      width: 18px;
      height: 18px;
      cursor: pointer;
      text-align: center;
      line-height: 18px;
      transform: translateY(-50%) translateX(50%);
      top: 0;
      right: 9%;
      left: unset;
      font-size: $text-xx-small;
      background: $color-danger;
      @include b-radius(50%);
      color: $color-white;
    }

    &.rec-list {
      justify-content: space-between;
      align-items: center;

      .img-dimension {
        font-size: $text-small;
        color: $color-dark-100
      }

      .media {
        margin: 8px 0 0 0;

        h5 {
          font-size: $text-medium;
          margin-bottom: 3px;
          color: $color-dark-300;
        }

        .img-dimension {
          font-size: $text-small;
          color: $color-dark-100
        }

        .media {
          margin: 8px 0 0 0;

          &-center {
            margin-bottom: 0;

            label {
              margin-bottom: 0;
              @include flexable(center, initial, row)
            }
          }
        }

        @include mediaMaxWidth($screen-phablet) {
          flex-direction: column !important;
          align-items: flex-start;
          .media {
            margin-top: 20px
          }
        }
      }
    }

    .setting-weight-input {
      width: 53px;
      float: left;
    }

    &.has-error {
      .selectized-wrapper {
        border: 1px solid $color-danger
      }

      .vpd-icon-btn {
        border-color: $color-danger !important;
      }
    }
  }

  // iti country code UI fix
  .iti {
    width: 100%;
    @include flexable(unset, unset, row-reverse);
    position: initial;
    &__flag-container {
      position: initial;
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm 0 0 $b-radius-sm);
      border-right: 0;
      padding: 0;
    }
    &__country-list {
      left: 0;
      width: 100%;
      top: 100%;
    }
    &__selected-dial-code {
      margin: 0 6px !important;
    }
    &__dropdown-content {
      width: 100% !important;
    }
    .form-control {
      padding-left: 10px !important;
      @include b-radius(0);
      border-left: 0;
    }
  }
}

.has-error {
  .input-group-btn {
    span {
      &.btn {
        border: 1px solid $color-danger !important;
      }
    }
  }
}



// custom tel input
.iti__country-list{
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.07) !important;
  border: unset !important;
}

.iti__country {
  outline: 0;
  align-items: center;
  direction: rtl;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding: 5px 10px;
}

span.iti__country-name {
  flex: auto;
  text-align: right;
}

.iti__flag{
  margin-left: 5px;
}
// END: custom tel input
.resize-none {
  resize: none;
}
