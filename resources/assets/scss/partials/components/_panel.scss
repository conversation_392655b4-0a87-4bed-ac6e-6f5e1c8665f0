.panel {
  .panel-heading {
    .heading-label {
      display: inline-block;
      vertical-align: middle;
      @include b-radius(50px);
      padding: 4px 12px;
      font-size: $text-xx-small;
      white-space: nowrap;
      color: $color-dark-100 !important;
      background-color: $color-gray-200;

      b {
        display: inline-block;
        vertical-align: middle;
        margin: 0 0 0 6px;
        transform: translateY(-3px);
      }

      &.small {
        font-family: $font-main;
        font-weight: bold;
        font-size: $text-xxx-small;
        padding: 4px 15px 0;
        margin: 0 10px 0 0;
        @include mediaMaxWidth($screen-phones) {
          margin: 5px 0 0 0;
          padding: 1px 10px 0;
          font-size: $text-xxxx-small;
        }
      }

      &.primary {
        color: $color-white;
        background-color: $color-primary;

        b {
          color: rgba($color-white, 0.8);
        }
      }

      @include mediaMaxWidth($screen-phones) {
        padding: 2px 10px 5px;
        font-size: 9px;
      }
    }

    .heading-elements {
      &.rec-mobile {
        @include mediaMaxWidth($screen-phones) {
          position: unset !important;
        }
      }
    }

    &.sides {
      @include flexable(center, space-between, row);
      @include mediaMaxWidth(576px) {
        .sides-text {
          font-size: $base-size !important;
          color: $color-danger;

          > span {
            display: none;
          }
        }
      }

      &.wrap {
        flex-wrap: wrap;
        @include mediaMaxWidth($screen-phablet) {
          > * {
            &:nth-child(2) {
              margin-top: 15px;
            }
          }
        }
      }
    }

    &.with-icon {
      i {
        display: inline-block;
        vertical-align: middle;
        margin: 0 0 0 5px;
        position: relative;
        top: -2px
      }
    }

    .panel-heading-subtitle {
      font-size: $text-x-small;
      color: $color-dark-100;

      &.inline {
        display: inline-block;
        margin-right: 5px;
      }
    }

    .panel-title {
      > i {
        display: inline-block;
        vertical-align: middle;
        transform: translateY(-2px);
        margin-left: 5px;
      }

      &--bold {
        font-weight: bold;
        font-size: $text-small !important;
      }

      small {
        font-size: $text-xxxx-small;
        color: $color-dark-100
      }
    }

    @media screen and (min-color-index: 0) and (-webkit-min-device-pixel-ratio: 0) {
      .panel-title {
        display: flex;
        align-items: center;

        > i {
          transform: translateY(0);
        }
      }
    }

    &--large {
      h6 {
        font-weight: bold;
      }
    }

    .panel-subtitle {
      display: block;
      margin: 8px 0 -8px 0;
      font-size: $text-x-small;
      color: $color-dark-100;
    }

    .panel-img {
      width: 35px;
      height: 35px;
      @include b-radius(50%)
    }

    .img-replacement {
      height: 50px;
      width: 50px;
      background: $color-gray-25;
      @include b-radius(50%);
      text-align: center;
      line-height: 50px;
      border: 1px solid $color-gray-200;

      i {
        font-size: $text-large;
      }
    }
  }
  .panel-sub-header {
    padding: 15px 20px;
    font-size: $base-size;
    font-weight: 500;
    background-color: $color-gray-25;
  }

  &--sticky-header .panel-heading {
    position: sticky;
    top: 0;
    margin-bottom: -1px;
    border-bottom-color: $color-gray-100;
    z-index: 999;
    background-color: $color-gray-50;


    @include mediaMaxWidth($screen-tablet-p) {
      top: 56px;
    }
  }

  &--fixed-width {
    @include mediaMinWidth($screen-desktop-large) {
      max-width: 1000px;
    }
  }

  .panel-body {
    .rec-title--large {
      color: $color-dark-300;
    }

    .text-dark {
      color: $color-dark-300;
      font-size: $text-medium;
    }

    &-list {
      margin: 15px 0 25px;

      li {
        font-size: $text-small;
        color: $color-dark-300;

        &:not(:last-of-type) {
          padding-left: 25px;
        }

        &::before {
          content: '';
          font-family: $font-sallaIcon;
          margin-left: 12px;
          font-size: $base-size;
          position: relative;
          top: 1px;
        }

        &.violation-status {
          &::before {
            content: '\ec07';
          }

          span {
            &.open {
              color: $color-danger; //colorAvilabilty
            }

            &.close {
              color: $color-primary;
              font-size: inherit;
              line-height: unset;
              opacity: 1;
              cursor: unset;
              float: initial;
            }
          }
        }

        &.violation-date {
          &::before {
            content: '\ea29'
          }
        }
      }
    }

    &.extra-spaces {
      padding: 50px;
    }

    #counter {
      text-align: center;
      @include flexable(flex-start, flex-start, row-reverse);

      b {
        font-weight: normal;
        font-family: $font-main;
        color: $color-dark-200;
        letter-spacing: 6px;
      }

      small {
        display: block;
        font-size: $text-medium;
        color: $color-dark-100;
        margin-top: -5px;
      }

      @include mediaMaxWidth($screen-phablet) {
        b {
          font-size: 40px !important;
        }
      }
    }

    &.padded {
      padding: 50px;
      @include mediaMaxWidth($screen-tablet-l) {
        padding: 30px 15px;
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      &-list {
        flex-direction: column;
        margin: 15px 0;

        li {
          padding-left: 0 !important;

          &:not(:last-of-type) {
            padding-bottom: 8px;
          }
        }
      }
      .rec-title--large {
        font-size: $text-medium;
      }
      .text-dark {
        font-size: $text-small;
      }
    }
  }


  // -- light panel ---
  &.panel-light {
    box-shadow: 0 2px 2px rgba($color-black, 0.05);
    border: none;

    .panel-heading {
      background-color: $color-white;

      h1 {
        margin: 0;
        font-size: $text-medium;
        color: $color-dark-100;
      }
    }
  }

  // leftover cart
  &#leftover_cart {
    .panel-heading {
      &.sides {
        @include mediaMaxWidth(576px) {
          flex-direction: column;
          align-items: flex-start;
          h6 {
            margin-bottom: 10px;
          }
        }
        @include mediaMaxWidth(360px) {
          .btn {
            &-fast-forward {
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }

  &.feedback {
    @include mediaMaxWidth(840px) {
      .panel-heading {
        .order-track {
          display: block;
          border: none;
          padding: 0 !important;
          margin: 0 !important;
          font-size: 13px !important;
        }

        .tooltip-toggle {
          width: 30px;
          height: 30px;
          display: inline-block !important;

          i {
            position: absolute;
            @include centerXY;
            color: $color-dark-300 !important;
          }
        }

        .badge {
          display: none;
        }
      }
    }
  }

  .panel-heading {
    &.heading-sb {
      @include flexable(center, space-between, row);

      > * {
        display: inline-flex;
      }

      @include mediaMaxWidth($screen-phones) {
        flex-direction: column;
        align-items: flex-start;
        > * {
          flex-direction: column;
          align-items: flex-start;

          &.heading-label {
            margin-top: 8px;
          }
        }
      }
    }

    &.heading-sb-ini {
      @include flexable(initial, space-between, row);
    }
  }

  &.dashboard-api {
    h5 {
      font-weight: bold;
      font-size: $text-medium;
      margin-top: 0;
    }

    p {
      font-size: $text-small;
      margin: 0 0 18px 0;
    }

    .btn-helper {
      border: 1px solid $color-gray-300;
      padding: 6px 23px 8px;
      color: $color-primary;
      border-color: $color-primary !important;

      i {
        position: relative;
        right: -7px;
        top: -1px;
        font-size: $text-small;
        color: $color-primary
      }
    }

    .api-key {
      .rec-list {
        margin-bottom: 10.5rem;

        > div:first-of-type {
          flex: auto;
          border: 1px solid $color-gray-300;
          border-left: none;
          height: 50px;
          line-height: 46px;
          padding: 0 20px 0 15px;
          color: $color-dark-100;
          @include b-radius(0 4px 4px 0);
          overflow: hidden;

          p {
            margin: 0;
            font-size: $base-size;
            white-space: nowrap;
            overflow: hidden;
          }
        }

        .btn {
          height: 50px;
          padding: 0 49px !important;
          line-height: 47px;
          font-size: $base-size;
          @include b-radius(4px 0 0 4px);

          &:before {
            content: '\ecfd';
            font-family: $font-sallaIcon;
            margin-left: 10px;
            position: relative;
            top: 2px;
            font-size: $text-xx-medium;
          }

          &-copy {
            background: $color-gray-50;
            border-right: 1px solid $color-gray-300;
            border-color: $color-gray-300;
            font-size: $text-small;
            color: $color-dark-300;
            min-width: 150px;
            padding: 0px 35px !important;

            &:before {
              content: '\ec26'
            }
          }
        }
      }

      @include mediaMaxWidth($screen-phones) {
        .rec-list {
          margin-bottom: 3rem;;
          flex-direction: column;
          margin-top: 5px;

          > div:first-of-type {
            flex: unset;
            padding: 15px 10px;
            word-break: break-word;
            line-height: initial;
            border-left: 1px solid $color-gray-300;
            width: 100%;
            height: auto;
            overflow: visible;
            border-bottom: none;
            @include b-radius($b-radius-sm $b-radius-sm 0 0);

            p {
              overflow: visible;
              white-space: normal;
            }
          }

          .api {
            width: 100%;
          }

          .btn {
            height: 33px;
            width: 100%;
            line-height: unset;
            @include b-radius(0 0 4px 4px);
          }
        }
      }
      @include mediaMaxWidth(375px) {
        .rec-list {
          > div:first-of-type {
            p {
              font-size: 13px !important;
            }
          }
        }
      }
    }
  }

  &.flat-bottom {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  &.wallet__balance {
    .wallet-panel__body {
      flex-direction: column;

      > div {
        width: 100%;
        flex-grow: 1;
      }
    }
  }

  &.has-placeholder {
    .panel-body {
      height: 100%;
      display: flex;
      flex-direction: column;

      .wallet-panel__body,
      .placeholder-wrap {
        flex-grow: 1;
        @include flexable(center, center, column);
      }
    }
  }

  .loader-wrap {
    justify-content: center;
    align-items: center;
    min-height: 60px;
    display: none;
  }

  &.is-loading {
    .loader-wrap {
      display: flex;
    }
  }

  // panel with steps ---
  &.has-steps {
    .panel-heading {
      padding-left: 100px;

      .panel-title {
        &:before {
          content: attr(data-step);
          display: inline-block;
          vertical-align: middle;
          width: 25px;
          height: 25px;
          font-size: $text-small;
          text-align: center;
          line-height: 21px;
          @include b-radius(50%);
          border: 1px solid $color-dark-100;
          margin-left: 10px;
          transform: translateY(-1px);
        }
      }

      &:after {
        content: 'تم التحقق';
        display: inline-block;
        padding: 7px 14px 8px;
        font-size: $text-xx-small;
        line-height: 1;
        color: $color-primary;
        @include transi();
        opacity: 0;
        visibility: hidden;
        position: absolute;
        left: 20px;
        @include center-v();
      }
    }

    &.step {
      &-active {
        .panel-heading {
          .panel-title {
            color: $color-primary;

            &:before {
              border-color: $color-primary;
            }
          }
        }
      }

      &-checked {
        .panel-heading {
          .panel-title {

            &:before {
              background-color: $color-primary;
              color: $color-white;
              border-color: $color-primary;
            }
          }

          &:after {
            opacity: 1;
            visibility: visible;
          }
        }

      }
    }
  }

  &-flat {
    .panel-heading {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  //panel lighter
  &-lighter {
    .panel-heading {
      h6 {
        color: $color-dark-200;
      }
    }
  }

  &--light-gray {
    border-color: $color-gray-100;

    .panel-body {
      background-color: $color-gray-25;

      &:before, &:after {
        content: none
      }
    }
  }

  &--full-height {
    height: 100%;
  }

  &--padded {
    .panel-body {
      padding: 30px;
    }

    @include mediaMaxWidth($screen-tablet-l) {
      .panel-body {
        padding: 20px;
      }
    }
  }

  &--bordered {
    box-shadow: none !important;
    border: 1px solid $color-gray-200 !important;
  }


  &--narrow {
    @include mediaMaxWidth($screen-tablet-l) {
      .panel-body {
        padding: 10px;
      }
    }
  }

  &--disabled {
    opacity: .3;
    pointer-events: none;
  }


  &--light-shadow {
    box-shadow: 0 5px 20px rgba(1, 1, 1, 0.03) !important;
  }
}


.rec {
  &-panel-desc {
    padding: 0 100px;
    margin: 0 0 20px 0;

    * {
      text-align: center !important;
      font-size: $text-x-small;
      color: $color-dark-200;
      margin: 0;
    }

    @include mediaMaxWidth($screen-desktop-small) {
      padding: 0 30px;
    }
    @include mediaMaxWidth($screen-phones) {
      padding: 0;
    }
  }
}

.customers-row {
  .heading-sb-ini {
    @include mediaMaxWidth(840px) {
      flex-direction: column !important;
      align-items: flex-start !important;
      h6 {
        margin-bottom: 15px;
      }
    }
  }
}

