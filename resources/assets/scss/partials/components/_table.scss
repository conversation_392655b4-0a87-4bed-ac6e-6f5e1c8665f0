.table {
  &.table {
    &-less-padding {
      tr {
        td,
        th {
          padding: 10px 15px !important;
        }
      }
    }

    &-large-padding {
      tr {
        td,
        th {
          padding: 25px 20px !important;
        }
      }
    }

    &-hover {
      tbody tr:hover {
        background-color: $color-gray-50;
      }
    }
  }

  &.has-status-error {
    border-right: 1px solid $color-danger;
    background-color: $color-gray-50;

    &:hover {
      background-color: $color-gray-50 !important;
    }
  }

  &.has-accordion {
    border-collapse: inherit;

    .hiddenRow {
      padding: 0 !important;
    }

    .acc-state {
      @include flexable();
      width: 22px;
      height: 22px;
      color: $color-dark-100;
      @include b-radius(50%);
      @include transi();
    }

    tr[aria-expanded="true"] {
      background: $color-gray-50;

      .acc-state {
        background: $color-gray-300;
      }
    }
  }

  #table_list_customer {

    .notification {
      &:before {
        right: 32px;
        top: 2px;
      }
    }
  }

  &.special-compare{
    border: 1px solid $color-gray-25;
    th{
      color: $color-dark-300;
      font-size: $text-xx-medium;
      border: none !important;
    }
    td{
      border-color: $color-gray-25 !important;
      font-size: $text-xx-small;
      border-bottom: 1px solid $color-gray-25;
      border-top: none;
    }
    small{
      color: $color-dark-200;
    }
    p{
      margin-bottom: 0;
    }
  }

  &-headings {
    background-color: $color-primary;
    border-bottom: 3px solid #388a7e;

    tr {
      td {
        padding: 16px 20px;
        font-size: $text-large;
        color: $color-white;
        font-weight: bold;

        i {
          color: $color-white;
          font-size: $text-large;
          margin-left: 5px;
          position: relative;
          top: 2px;
        }
      }

      td:first-of-type {
        padding-right: 60px;
      }
    }
  }

  &--bordered {
    thead tr th, tbody tr td {
      &:not(:last-of-type) {
        border-bottom: 1px solid $color-gray-200;
        border-left: 1px solid $color-gray-200;
      }
    }

    tbody tr {
      &:last-of-type {
        td {
          border-bottom: none;
        }
      }
    }
  }

  tbody {
    tr td {
      a.title {
        white-space: normal;
        display: block;

        .receipt_image {
          &.small {
            max-height: 50px;
            max-width: 50px;
            border: none
          }
        }

        &.no-wrap {
          white-space: normal;
        }

        &--link {
          direction: ltr;
          text-align: right;
        }
      }

      &.no-wrap {
        white-space: normal;
      }

      &.td-cod {

        &--medium {
          span {
            font-size: $text-small;
          }

          i {
            font-size: $text-x-small;
          }
        }
      }
    }

    .controls-wrapper {
      .controls {
        @include flexable(center, flex-end, row);
      }

      .copy-btn {
        display: none;

        &:before {
          content: '\efd2';
          font-family: $font-sallaIcon;
          font-size: $text-x-small;
          position: relative;
          top: 1px;
          margin-left: 5px;
        }
      }

      @include mediaMaxWidth($screen-phones) {
        height: auto;
        padding-left: 15px;

        .controls {

          .tooltip-toggle {
            display: none;
          }

          .copy-btn {
            display: block;
          }
        }
      }
    }
  }


  &-medium-text {
    td {
      font-size: $text-x-small;
    }
  }


  &--with {
    &-border {
      border: 1px solid $color-gray-200
    }

    &-img {
      img {
        object-fit: contain;
        height: 50px;
      }
    }
  }


  &--fixed-header {
    thead {
      display: none;
    }
  }

  &-fixed {
    table-layout: fixed;
  }

  &-cells-top {
    tbody tr td {
      vertical-align: top;
    }
  }

  &-fixed-header {
    max-height: 250px;
    overflow-x: auto;
  }

  &-orders-edit {
    tbody tr td {
      &:nth-of-type(2) {
        width: 60%;
      }
    }

    &-header {
      border-bottom: none
    }
  }

  &--border {
    border: 1px solid $color-gray-200
  }

  &--tiffany-head {
    thead {
      background-color: $color-primary;

      td {
        color: $color-white;
      }
    }
  }

  &.branches-management {
    thead {
      td {
        font-size: $base-size;
        font-weight: bold;
        padding: 10px 13px;

        &:first-of-type {
          width: 80%
        }
      }
    }

    tbody {
      td {
        padding: 10px 13px;
        border-top-color: $color-gray-200;

        span {
          color: $color-dark-300;
          font-size: $base-size;
        }
      }
    }
  }

  .disabled {
    opacity: .6;
    pointer-events: none;
  }

  //verfication requests table
  &.table-verification{
    border-radius: var(--b-radius);;
    border: 1px solid #eee !important;

    .table{
      border-radius: var(--b-radius);
    }

    .dark &{
      border-color: $color-dark-400 !important;

      .table{
        background-color: #272626;
      }
    }


    th{
      padding-top: 15px;
      padding-bottom: 15px;
      font-size: 16px;

      &:first-child{
        border-top-right-radius: var(--b-radius);;
      }

      &:last-child{
        border-top-left-radius: var(--b-radius);;
      }
    }

    tr:last-child{
      .more-options{
        bottom: 100%;
        top: auto;
      }
    }

    td{
      font-size: 15px !important;
    }

    .status{
      position: relative;
      &::after{
        content: "";
        width: 8px;
        height: 8px;
        background-color: inherit;
        display: block;
        position: absolute;
        top: 7px;
        right: -13px;
        background: #ddd;
        border-radius: 50%;
      }
    }

    .approved{
      color: $color-success;
      &::after{
        background-color: $color-success;
      }
    }

    .under_review{
      color: $color-warning;
      &::after{
        background-color:$color-warning;
      }
    }

    .canceled{
      color: #5196F3;
      &::after{
        background-color: #5196F3;
      }
    }


    .rejected{
      color: $color-danger;
      &::after{
        background-color: $color-danger;
      }
    }

  }

  // order edit quantity
  &.order-quantity-edit {
    thead tr {
      th {
        color: $color-dark-300;
        font-size: $text-x-small;
      }
    }

    tbody {
      tr {
        &:not(:last-of-type) {
          border-bottom: 1px solid $color-gray-200;
        }

        &:last-of-type {
          border-top: 1px solid $color-gray-200;
        }

        td {
          small {
            font-size: $text-xxxx-small;
          }

          input.form-control {
            width: 40px;
            height: 30px;
          }

          h6, p {
            margin: 0;
            display: inline-block;
          }

          h6, b {
            font-size: 14px;
          }
        }
      }
    }

    &.branchless {
      td {
        padding: 14px !important;
      }
    }

    &.weights-details {
      tbody {
        tr {
          td {
            input.form-control {
              width: 75px;
              border-right: 1px solid $color-gray-200;
              padding-right: 7px !important;

              &:focus {
                border-color: $color-gray-300;
              }
            }
          }

          &.disabled-tr {
            opacity: .5;

            td {
              input.form-control {
                pointer-events: none;
              }
            }
          }
        }
      }
    }

  }

  // leveled table ---
  &-has-levels {
    tr {
      &.child {
        td {
          &:first-child {
            padding-right: 25px !important;

            &:after {
              content: '';
              display: inline-block;
              vertical-align: middle;
              float: right;
              width: 15px;
              height: 2px;
              @include b-radius(2px);
              background: $color-gray-300;
              margin: 13px 0 0 15px;
            }
          }
        }

        @include mediaMaxWidth($screen-phones) {
          td {
            &:first-child {
              padding-right: 20px !important;

              &:after {
                margin: -5px 0 0 15px;
              }
            }
          }
        }
      }
    }
  }

  // financial log ---
  &-financial-log {
    tbody {
      td {
        .financial-entry {
          &.red {
            color: $color-danger;

            &:before {
              color: $color-danger;
            }
          }

          &.green {
            color: $color-primary;

            &:before {
              color: $color-primary;
            }
          }

          &.with-icon {
            &:before {
              content: '';
              font-family: $font-sallaIcon;
              font-size: $text-xx-small;
              display: inline-block;
              vertical-align: middle;
              margin-left: 5px;
              transform: translateY(-2px);
            }

            &.green {
              &:before {
                content: '\e94a';
              }
            }

            &.red {
              &:before {
                content: '\e941';
              }
            }
          }
        }
      }
    }
  }


  &#orders_detail_table {
    .f-column-info {
      min-width: 200px;
    }


    @include mediaMaxWidth($screen-phablet) {
      .s-column-info {
        display: block;
        margin-top: 15px;
      }
    }
  }



  @include mediaMaxWidth(370px) {
    tbody {
      tr td a.title {
        width: 150px;
      }
    }
  }
}

.cell-tight {
  padding: 6px 20px 8px !important;
}

// bootstrap override ---
.table > tbody > tr > td,
.table > tbody > tr > th,
.table > tfoot > tr > td,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > thead > tr > th {
  @include mediaMaxWidth($screen-phones) {
    padding: 12px 15px;
  }
}

