.policy-entry {
  flex-wrap: wrap;
  width: 100%;

  &:not(:last-of-type) {
    margin-bottom: 15px;
  }

  .info {
    > span {
      font-size: $text-x-small;
      color: $color-dark-300;
      @include flexable(center, flex-start, row);
    }
  }

  .controls {
    .btn {
      span {
        text-decoration: underline;
      }

      i {
        font-size: $text-x-small;
        margin-left: 3px;
      }
    }
  }
}