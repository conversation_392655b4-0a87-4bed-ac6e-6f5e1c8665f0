.thumbnail {
  &--list {
    padding: 15px 56px 3px 13px !important;
    @include flexable(center, space-between, row);

    .caption-small {
      width: 78%;
      @include flexable(initial, initial, row);

      .product-thumb-wrapper {
        @include flexable(initial, initial, row);

        .thumb {
          width: 84px;
          height: 84px;
          overflow: visible;
          margin-left: 13px;
          min-width: 84px;
          padding: 0;

          + .row {
            padding: 0 !important
          }

          .product-check {
            top: 30px;
            right: -38px;
          }

          .product_image_btn {
            display: none
          }
        }

        margin-left: 12px;
      }

      .product-fields-wrapper {
        @include flexable(initial, initial, row);
        padding: 0 !important;
        flex: auto;

        .price_quantity_row {
          margin-right: 0;
          width: 100%;

          .rec-list {
            flex-direction: row;
            justify-content: space-between;

            > div:first-of-type {
              margin-left: 12px
            }
          }
        }
      }

      .pin_btn {
        top: 10px;
        right: 10px !important;
      }
    }

    .options-wrapper {
      display: none !important
    }

    .btn-option-groupe {
      display: block !important;

      .btn:first-of-type {
        padding: 4px 7px 6px 13px;
      }

      .dropdown-menu {
        top: 37px
      }
    }

    .save-product {
      display: block;
      width: 166px;
      margin: 12px auto;
      font-weight: 400 !important;
      padding: 4px !important;
      @include b-radius(2px !important)
    }

    &-adding {
      background-color: $color-gray-50;
      border: 1px dashed $color-gray-300;

      img {
        border: 1px dashed $color-gray-300;
      }
    }

    @include mediaMaxWidth($screen-laptop) {
      .caption-small {
        flex-direction: column;
        width: 68%;

        .product-thumb-wrapper {
          .thumb {
            margin-left: 5px;
          }

          .product-check {
            top: 84px !important;
          }

          .row {
            width: 100%;
            margin: 0;

            > div {
              padding-left: 0;
            }
          }

          margin-left: 0;
        }

        .price_quantity_row {
          width: 100%;

          > div:not(:last-of-type) {
            padding-right: 0
          }
        }
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {
      flex-direction: column;
      position: relative;
      padding: 48px 13px 88px 13px !important;
      .caption-small {
        width: 100%;

        .product-thumb-wrapper {
          padding: 0 !important;
          margin: 0 !important;

          .product-check {
            top: -35px !important;
            right: 0 !important;
          }

          .thumb {
            position: initial;

            img {
              position: initial;
            }

            .pin_btn {
              left: 0;
              top: -35px !important;
              right: auto !important;
            }
          }
        }
      }
      > div:last-of-type {
        position: absolute;
        bottom: 0;
        width: 100%;

        .options-wrapper {
          display: flex !important;
        }

        .btn-option-groupe {
          display: none !important;
        }

        .save-product {
          font-weight: bold !important;
          width: 100%;
          height: 40px;
          margin: 0;
          @include b-radius(0 0 2px 2px !important)
        }
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      .rec-list {
        flex-direction: column !important;
      }
    }
  }

  .dropdown-toggle.btn-default {
    small {
      display: none;
    }
  }
}

.product-box {
  &-new {
    .options-wrapper {
      .options {
        &:last-of-type {
          button {
            display: none;
          }
        }
      }
    }
  }
}

.load-more-wrapper {
  .btn,
  span {
    font-size: $text-xx-medium;
    font-weight: bold;
  }

  .btn {
    padding: 12px 31px;
  }

  span {
    color: $color-dark-300;
    display: inline-block;
    margin-left: 10px
  }
}
