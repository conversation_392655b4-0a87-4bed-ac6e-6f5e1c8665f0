.loader {
  display: inline-block;
  width: 25px;
  height: 25px;
  padding: 0;
  @include b-radius(100%);
  text-align: center;
  line-height: 1;
  border: 3px solid;
  border-top-color: $color-secondary-d !important;
  border-bottom-color: $color-gray-200 !important;
  border-left-color: $color-secondary-d !important;
  border-right-color: $color-gray-200 !important;
  animation: loader 1s ease-in-out infinite;
  margin: auto 0;

  &--small {
    width: 20px;
    height: 20px;
    border-width: 2px;

    &:before {
      font-size: $text-medium;
    }
  }

  &--smaller {
    width: 16px !important;
    height: 16px !important;
    border-width: 2px !important;

    &:before {
      font-size: $text-medium;
    }
  }
}


.loader-wrapper {
  position: relative;
  @include transi();

  .message {
    font-size: $text-small;
    font-weight: 600;
  }

  &--wide {
    width: calc(100% - 20px);
    height: 100%;
    position: absolute;
    top: 0;
    right: 10px;
    bottom: 0;
    left: 0;
    background-color: rgba($color-white, 0.85);
    z-index: 99995;
  }

  &--min-height {
    min-height: 500px;
  }

  &--center {
    @include flexable();
  }

  &:before {
    font-family: $font-sallaIcon;
    width: 18px;
    height: 18px;
    @include b-radius(50%);
    margin: 3px 0 0 10px;
    font-size: $text-xx-small;
    font-weight: bold;
    text-align: center;
    line-height: 20px;
    color: $color-white;
  }

  &.success {
    &::before {
      content: '\ea9d';
      background-color: $color-primary;
    }
  }

  &.error {
    &::before {
      content: '\ea47';
      background-color: $color-danger;
    }
  }
}

.overlay-loader {
  @include flexable(center, center, column);
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 99999;
  top: 0;
  right: 0;

  .loader {
    margin: 15px;
  }

  * {
    display: block;
    font-size: $text-small;
    color: $color-dark-200;
    text-align: center;
  }

  &.light {
    background: rgba(255, 255, 255, .85);

    * {
      color: $color-dark-200;
    }
  }

  &.primary {
    background: rgba($color-primary, .85);

    * {
      color: $color-white;
    }
  }

  &.dark {
    background: rgba(39, 39, 39, .85);

    * {
      color: $color-white;
    }
  }
}

.page-load-status {
  text-align: center;
}


// Infinit scroll spinner -----------
.infinite-message {
  @include flexable(center, center, column);
  padding: 40px 10px;
  line-height: 30px;
  color: $color-gray-300;

  .icon {
    font-size: $text-larger;
    color: $color-gray-300;
  }
}

.infinite-status-prompt,
.lang-loading-wrap {
  min-height: 100px;
  @include flexable(center, center);
}

.locales-loading-wrap {
  position: absolute;
  width: 100%;
  height: 100%;
}

.loading-spiral {
  border-color: $color-primary $color-primary rgb(245, 245, 245) rgb(245, 245, 245) !important;
  width: 25px;
  height: 25px;
  border-width: 2px;
}
