.file-uploader,
.fileinput-cancel.fileinput-cancel-button {

  input[type="file"] {
    width: 100%;
    z-index: 999;
    cursor: pointer;
  }

  &--light {
    .input-group-btn {
      background: $color-white;

      .btn {
        &.btn-primary {
          min-height: 36px;
          padding-top: 6px;
          background-color: $color-primary;
          border-color: $color-primary;
        }
      }
    }

    .input-group {
      .file-caption {
        height: 36px !important;
        padding-top: 6px;
        border-right: 1px solid $color-gray-200;
      }

      .file-caption-disabled {
        background-color: $color-gray-50;

        .file-caption-name {
          color: $color-dark-100;

          .glyphicon {
            transform: translateY(1px);
          }
        }
      }
    }

    .has-error {
      .file-caption {
        border-right-color: $color-danger !important;
      }

      .file-caption-name {
        .glyphicon {
          transform: translateY(0px);
        }
      }
    }

    .kv-upload-progress,
    .upload-progress {
      position: absolute;
      z-index: 0999;
      width: 60%;
      margin: 0;
      top: 10px;
      right: 10px;
    }
  }

  &--single {
    .btn.dropdown-toggle {
      border-left: 0;
    }

    .form-control.kv-fileinput-caption {
      height: 36px !important;
      padding-right: 0;
      @include b-radius(0);
    }

    .file-input {
      position: relative;

      .kv-upload-progress {
        position: absolute;
        top: -25px;
        z-index: 55;
        width: 100%;
      }

      .file-caption-main .input-group-btn {
        .btn-primary.btn-file {
          background: $color-primary;
          border-color: $color-primary;

          i {
            &:before {
              content: '\eae0';
              font-family: $font-sallaIcon !important;
              position: relative;
              top: -3px
            }
          }

          input {
            position: absolute;
          }

          &:active,
          &:focus {
            background: $color-primary !important;
            border-color: $color-primary;
          }
        }

        .fileinput-remove {
          i {
            margin: 0;
            color: $color-danger;
          }

          span {
            display: none;
          }
        }
      }
    }

    &.no-loading {
      .file-input {
        .kv-upload-progress {
          display: none !important;
        }
      }
    }
  }

  .dropdown-menu > li > a {
    display: flex;
  }
}

.uploaded-file-link {
  position: absolute;
  top: 40px;
  z-index: 99;
  font-size: $text-xx-small;
  right: 10px;
}

//image uploader
.image-upload {
  @include flexable(center, center, column);
  background-color: $color-gray-50;
  min-height: 125px;
  @include b-radius($b-radius-sm);
  border: 2px dashed $color-gray-300;
  cursor: pointer;

  .align-center, i, span {
    pointer-events: none;
  }

  > i {
    color: $color-gray-300;
    font-size: $text-large;
    opacity: 0.3;
  }

  i {
    font-size: 30px;
    opacity: 0.3;
  }

  span {
    margin-top: 5px;
    font-size: $text-small;
    color: $color-dark-200;
    display: block;
    opacity: 0.3;
  }

  img {
    width: auto;
    max-width: 300px;
    height: auto;
    @include mediaMaxWidth($screen-phones) {
      max-width: 230px;
    }
  }
}

