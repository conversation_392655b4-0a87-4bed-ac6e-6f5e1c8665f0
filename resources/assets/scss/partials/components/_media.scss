.media {
  &-left {
    &.large-scale {
      flex: 0 0 200px;
      text-align: center;
      margin-left: 40px;
      padding-left: 0;

      img {
        width: 80px !important;
        height: 80px !important;
        border: 2px solid $color-gray-200;
      }
    }

    &.separator {
      border-left: 1px solid $color-gray-200;
    }
  }

  &-body {
    .location {
      i {
        position: relative;
        top: 3px;
      }
    }

    &.w-full {
      width: 100%;
    }
  }

  &__avatar {
    width: 40px;
    height: 40px;
    @include b-radius(50%);
  }

  @include mediaMaxWidth($screen-phones) {
    flex-direction: column;
    &.mobile-row{
      flex-direction: row;
      justify-content: flex-start;
      .media-body{
        width: 100% !important;
      }
      .media-right{
        margin-right: auto;
      }
    }
    &-left {
      &.large-scale {
        flex: 0 0 100%;
        width: 100%;
        margin: 0 0 20px;
        padding-bottom: 20px;
      }

      &.separator {
        border-left: 0;
        border-bottom: 1px solid $color-gray-200;
      }
    }
    &-body {
      &.align-center-vertical {
        @include flexable(center, center, column)
      }
    }
  }
  &.w-full {
    width: 100%;
  }
}
