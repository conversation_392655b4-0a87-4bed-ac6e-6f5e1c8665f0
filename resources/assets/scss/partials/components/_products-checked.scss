.products-checked {
  margin-bottom: 15px;
  margin-top: 20px;
  width: 100%;

  .rec-checkbox {
    position: absolute;
    top: 0;
    left: 15.5rem;
    label {
      padding: 0 !important;
      width: 22px;
      height: 22px;

    }
  }

  > a {
    font-size: $text-small;
    color: $color-dark-300;

    &:after {
      content: '\E96D';
      font-family: $font-sallaIcon;
      @include transi();
      margin-right: 8px;
      display: inline-block;
      width: 20px;
      background-color: $color-white;
      @include b-radius(50%);
      text-align: center;
      line-height: 21px;
      color: $color-dark-300; //colorAvilability
      height: 20px;
      position: relative;
      top: 3px;
      font-size: $text-x-medium;
    }

    &[aria-expanded="true"] {
      &:after {
        transform: rotate(180deg);
      }
    }
  }

  .dropdown-menu {
    min-width: 180px;
    top: 30px;

    &-right {
      left: 11px;
    }

    li {
      a i {
        color: $color-gray-400;
      }
      a path {
        fill: $color-gray-400;
      }
      &.delete-list {
        a, i {
          color: $color-danger
        }
      }
    }

    .dropdown-submenu a:after {
      top: 53%;
      left: 10px;
      color: $color-gray-400
    }
  }
}