.rec-progress-bar {
  $progress-bar-root: &;
  @include flexable(center, flex-start, row);
  gap: 10px;
  width: 100%;
  background-color: transparent;
  box-shadow: none;
  line-height: 1;

  // elements ---
  &__bg {
    position: relative;
    flex: 1 0 auto;
    @include b-radius(20px);
    height: 10px;
    background-color: $color-gray-25;
    overflow: hidden;
  }

  &__progress {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    @include b-radius(20px);
    background-color: $color-gray-200;
    @include transi();
  }

  &__indicator {
    flex: 0 0 auto;
    color: $color-dark-300;
  }

  // modifiers ----
  &--center {
    justify-content: center;
  }

  &--small {
    #{$progress-bar-root} {
      &__bg {
        height: 7px;
      }

      &__indicator {
        font-size: 13px;
      }
    }
  }

  &--primary {
    #{$progress-bar-root} {
      &__bg {
        background-color: rgba($color-secondary, 0.3);
      }

      &__progress {
        background-color: $color-secondary;
      }

      &__indicator {
        color: $color-secondary
      }
    }
  }
}

// orders progress bar ---
#orders_progress {
  max-width: 500px;
  margin: 0 auto;
  @include mediaMaxWidth($screen-phones) {
    max-width: unset;
  }
}

// merge progress widget
.progress-widget {
  $progress--widget-root: &;
  @include flexable(center, flex-start, row);
  gap: 28px;
  width: 420px;
  height: 60px;
  background: $color-white;
  box-shadow: 0 2px 10px 0 rgba($color-dark-200, 0.15);
  border: 1px solid $color-gray-200;
  padding: 10px 25px 10px 20px;
  position: fixed;
  bottom: 20px;
  left: min(100px, 20vw);
  transform: translateY(80px);
  opacity: 0;
  z-index: 99999;
  transition: all .75s cubic-bezier(0.55, 0, 0.1, 1) 0s;

  &__content {
    @include flexable(flex-start, flex-start, column);
    @include transi();
    gap: 6px;
    flex: auto;

    strong {
      display: block;
      max-width: 320px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: $text-xx-small;

      i {
        display: inline-block;
        margin-left: 8px;
        vertical-align: middle;
      }
    }
  }

  &__shrink {
    width: 25px;
    height: 25px;
    @include flexable();
    flex-basis: 25px;
    flex-shrink: 0;
    padding: 0;
    @include b-radius(50%);
    font-size: $text-x-small;
    transition: 0.3s;
    background-color: transparent;
    border: 1px solid $color-gray-300;

    &:before {
      content: '\ebb4';
      display: inline-block;
      font-family: $font-sallaIcon;
      line-height: 1;
      color: inherit;
    }

    &:hover,
    &:focus,
    &:active {
      color: $color-dark-300;
    }
  }

  &__save {
    @include b-radius(20px);
    min-width: 90px;
    height: 32px;
    padding: 4px 5px 6px;
    transition: 0.3s;
  }

  &--oval {
    @include b-radius(50px);
  }

  &.reveal {
    opacity: 1;
    transform: translateY(0);
  }

  &.shrinking {
    width: 70px;
    height: 70px;
    padding: 0;
    gap: 0;
    left: 10px;
    bottom: 20px;
    z-index: 9;
    #{$progress--widget-root} {
      &__content,
      &__shrink {
        opacity: 0;
      }
    }

    &.shrinked {
      #{$progress--widget-root} {
        &__content,
        &__shrink {
          opacity: 1;
        }
        &__content {
          width: 100%;
          height: 100%;
          gap: 0;

          // force rec-progress-bar for the widget ---
          .rec-progress-bar {
            width: 100%;
            height: 100%;

            &__indicator {
              position: absolute;
              top: 50%;
              right: 50%;
              transform: translate(50%, -50%);
              font-size: $text-xx-small;
            }

            &__bg {
              background: transparent;
              width: 100%;
              height: 100%;
              @include b-radius(50%);
            }

            &__progress {
              width: 100% !important;
              height: 100%;
              background: radial-gradient(closest-side, white 80%, transparent 100%),
              conic-gradient($color-secondary var(--current), rgba($color-secondary, 0.10) 0);
            }
          }

          strong {
            display: none;
          }
        }

        &__shrink {
          position: absolute;
          top: -6px;
          right: 70%;
          background: $color-white;
          transform: translateX(40%);
          &:before {
            content: "\ebb6";
          }
        }
      }
    }
  }

  @include mediaMaxWidth($screen-phones) {
    width: 260px;
    padding: 10px 20px 10px 15px;
    gap: 15px;

    &__content {
      @include flexable(flex-start, flex-start, column);
      @include transi();
      gap: 6px;
      flex: auto;

      strong {
        max-width: 180px;
      }
    }
  }

  // resize progress widget in mobile ---
  @include mediaMaxWidth($screen-phablet) {

    #{$progress--widget-root} {
      &__shrink {
        display: none;
      }
    }
  }

  &.shrinkd {
    animation-name: slide-progress;
    animation-duration: 300ms;
    animation-timing-function: ease-out;
    animation-fill-mode: forwards;
    left: 100px;
    bottom: 20px;
    width: 70px;
    height: 70px;
    padding: 0;
    gap: 0;
    z-index: 9;

    #{$progress--widget-root} {
      &__content {
        width: 100%;
        height: 100%;
        gap: 0;

        // force rec-progress-bar for the widget ---
        .rec-progress-bar {
          width: 100%;
          height: 100%;

          &__indicator {
            position: absolute;
            top: 50%;
            right: 50%;
            transform: translate(50%, -50%);
            font-size: $text-xx-small;
          }

          &__bg {
            background: transparent;
            width: 100%;
            height: 100%;
            @include b-radius(50%);
          }

          &__progress {
            width: 100% !important;
            height: 100%;
            background: radial-gradient(closest-side, white 80%, transparent 100%),
            conic-gradient($color-secondary var(--current), rgba($color-secondary, 0.10) 0);
          }
        }

        strong {
          display: none;
        }
      }

      &__shrink {
        position: absolute;
        top: -6px;
        right: 70%;
        height: 18px;
        width: 18px;
        background: $color-secondary-50;
        transform: translateX(40%);
        border: none;

        &:before {
          content: "\ebb6";
          color: $color-primary;
        }
      }
    }
  }

  // Save and Undo Btns ----------------------------
  &--save-messag,
  &--save-undo {
    max-width: 75%;
    gap: 15px;
    padding: 5px 15px 5px 10px !important;
  }
  &--save-message {
    .saved {
      width: 24px;
      height: 24px;
      min-width: 24px;
      line-height: 24px;
      text-align: center;
      color: $color-secondary;
      animation: 0.3s switch-to-saved;
      font-size: 22px;
    }
  }

  &--save-undo {
    small {
      white-space: nowrap;
    }

    .loader {
      position: absolute;
      border-width: 3px;
      display: none;
    }

    .progress-widget__save {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;

      &.is-loading {
        width: 32px;
        height: 32px;
        background-color: transparent !important;
        border-color: transparent !important;
        pointer-events: none;
        animation: 0.3s switch-to-loading;

        .text {
          opacity: 0;
          transition: 0.3s;
        }

        .icon {
          display: none;
        }

        .loader {
          display: inline-block
        }
      }

      &.saved {
        width: 32px;
        height: 32px;
        min-width: 32px;
        pointer-events: none;
        background-color: $color-secondary !important;
        animation: 0.3s switch-to-saved;

        .text {
          display: none;
        }
      }

      &.undo {
        width: 32px;
        height: 32px;
        min-width: 32px;
        transition: 0.3s;
        color: $color-danger !important;
        border-color: rgba($color-danger, 0.45);
        background-color: rgba($color-danger, 0.1);
        transition-duration: 0.5s;
        animation: 0.3s button-switch;

        .text {
          display: none;
        }

        &:hover {
          background-color: rgba($color-danger, 0.2);
        }
      }
    }
  }

  // states ---
  &.complete {
    #{$progress--widget-root} {
      &__shrink {
        color: $color-secondary;
        border-color: $color-secondary-50;
        pointer-events: none;

        &:before {
          content: '\ea9d';
        }

        &:hover,
        &:focus,
        &:active {
          color: $color-secondary;
        }
      }
    }
  }
}

@keyframes switch-to-loading {
  0% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes switch-to-saved {
  0% {
    transform: scale(0.7);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes button-switch {
  0% {
    transform: scale(0.7);
  }
  100% {
    transform: scale(1);
  }
}


@keyframes slide-progress {

  0% {
    transform: translateX(0) scale(0.5);
    opacity: 0.7;
  }

  100% {
    transform: translateX(-90px) scale(1);
    opacity: 1;
  }
}
