.page-header {
  min-height: 45px;
  padding: 0 40px;

  @include mediaMaxWidth($screen-tablet-l) {
    padding: 0 20px;
  }

  .page-header-content {
    padding: 20px 0 0;

    .page-title {
      &.breadcrumb-container {
        padding: 0;
      }
    }

    .heading-elements {
      &.heading-help {
        top: 20px;
        left: 0;
        margin: 0;

        &.extra-help {
          @include mediaMinWidth($screen-tablet-p) {
            > div {
              @include flexable(center, center, row);
              border: 1px solid $color-secondary-50;
              @include b-radius(999px);
              padding: 5px 12px 5px 5px;
              .help-title {
                display: block !important;
                font-weight: 500;
              }
            }
          }
        }

        @include mediaMaxWidth($screen-tablet-p) {
          padding-top: 0 !important;
        }

        @include mediaMaxWidth($screen-phones) {
          top: 20px !important;
          .btn.btn-icon {
            position: absolute;
            left: 0;
            margin-left: -20px;
            @include b-radius(0 50px 50px 0);

            span {
              display: none;
            }
          }
        }
      }
    }
  }

  @include mediaMaxWidth($screen-tablet-p) {
    padding: 0 15px;
  }

  @include mediaMaxWidth($screen-phablet) {
    padding: 0 10px;
  }

  &--languages {
    @include flexable(center, space-between);
    padding: 20px 0 0;
  }
}
