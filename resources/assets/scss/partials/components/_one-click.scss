.quick-order {
  width: 100%;
  height: auto;
  position: relative;
  padding: 15px;
  margin: 15px 0 0;
  @include b-radius($b-radius-sm);
  @include transi();

  .btn, .form-control {
    height: 35px;
    @include b-radius($b-radius-sm);
    user-select: auto;
  }

  .btn {
    &.qo-order, &.qo-submit, &.qo-submit {
      line-height: 1;
      padding-bottom: 10px;
    }

    &.qo-order {
      @include flexable();
      @include transi();
      min-width: 150px;
      font-size: $base-size;
      box-shadow: none;

      > * {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      span {
        transform: translateY(-9px);
      }

      i {
        color: $color-black;
        opacity: 0;
        visibility: hidden;
      }

      &.close {
        min-width: 40px;
        background: rgba($color-black, 0.1);

        span {
          opacity: 0;
          visibility: hidden;
        }

        i {
          opacity: 1;
          visibility: visible;
        }

        &:hover {
          background-color: rgba($color-black, 0.15);
        }
      }

      @include mediaMaxWidth($screen-tablet-l) {
        min-width: 80px;
      }
    }

    &.qo-submit {
      min-width: 100%;
      border: none;
      font-size: $base-size;
    }

    &.qo-confirm {
      line-height: 1;
      color: $color-white !important;
      background-color: $color-primary;
      border: none;

      &:hover {
        background-color: $color-secondary;
      }
    }
  }

  .rec{
    &-editable {
      position: relative;
      border: 1px solid transparent;
      background: transparent;
  
      &:after {
        content: '\ee29';
        font-family: $font-sallaIcon;
        font-size: $text-xxxx-small;
        color: $color-white;
        @include flexable();
        width: 23px;
        height: 23px;
        @include b-radius(50%);
        background-color: $color-primary; //colorAvilabilty
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -10px;
        @include transi();
        border: 2px solid $color-white;
        box-shadow: 0 1px 2px rgba($color-black, 0.3);
      }
  
      &.active {
        &:after {
          transform: scale(1.2) translateY(-50%);
          background-color: $color-secondary; //colorAvilabilty
        }
      }
    }

    //merge one-click-order-tag
    &-order-tag {
      display: inline-block;
      vertical-align: middle;
      margin: 0 5px 4px 0;
      font-size: 11px;
      padding: 1px 8px 4px;
      @include b-radius(50px);
  
      &--quick {
        color: $color-white;
        background-color: $color-danger;
      }
    }

  }

  .qo {
    &-title {
      @include flexable(center, space-between, row);

      h1 {
        line-height: 1;
        margin: 0;

        input {
          display: block;
          width: 100%;
          background: transparent;
          padding-right: 20px;

          &.title {
            font-size: $text-xx-larger;
          }

          &.slogan {
            font-size: $base-size;
            margin: 6px 0 0;
          }
        }
      }

      @include mediaMaxWidth(600px) {
        flex-direction: column;
        align-items: flex-start;
        h1 {
          margin: 0 0 20px 0;
        }
      }
    }

    &-form {
      padding-top: 25px;

      .form-group {
        margin-bottom: 15px;
      }

      .rec-checkbox {
        &.rec-checkbox--default {
          input[type=checkbox] {
            + label {
              padding-left: 15px;
            }
          }
        }
      }

      .form-control {
        border: none;
        box-shadow: none;
        padding-bottom: 10px;

        &::placeholder {
          color: $color-gray-400;
        }
      }
    }
  }


  // Store Main Color Version ---
  &--main {
    background-color: $color-primary;
    border: 1px solid $color-primary;

    .btn {
      &.qo-order {
        color: $color-primary;
        background-color: $color-white;
        border: none;
        box-shadow: 0 1px 2px rgba($color-black, 0.15);

        &:hover {
          background: $color-gray-25 !important;
        }
      }

      &.qo-submit {
        color: #110707;
        background: white !important;
      }
    }

    .qo {
      &-title {
        h1 {
          * {
            color: $color-white;

            &::placeholder {
              color: $color-white;
            }
          }
        }
      }

      &-form {
        .form-control {
          background-color: $color-white;
        }

        .rec-checkbox {
          &.rec-checkbox--default {
            input[type=checkbox] {
              + label {
                color: $color-white;

                &:before {
                  border-color: rgba($color-white, 0.5);
                }

                &:after {
                  color: $color-white;
                }
              }
            }
          }
        }
      }
    }
  }

  // Light version ---
  &--light {
    background-color: $color-white;
    border: 1px solid $color-gray-200;

    .btn {
      &.qo-order {
        color: $color-dark-200;
        background-color: $color-gray-300;
        border: none;
        box-shadow: none;

        &:hover {
          background: $color-gray-400 !important;
        }
      }

      &.qo-submit {
        color: $color-white;
        background: rgba($color-black, 0.50) !important;
      }
    }

    .qo {
      &-title {
        h1 {
          * {
            color: $color-dark-300;
          }

          input {
            &::placeholder {
              color: $color-dark-300;
            }
          }
        }
      }

      &-form {
        .form-control {
          background-color: $color-gray-25;
        }

        .rec-checkbox {
          &.rec-checkbox--default {
            input[type=checkbox] {
              + label {
                color: $color-dark-50;

                &:before {
                  border-color: rgba($color-gray-400, 0.5);
                }

                &:after {
                  color: $color-gray-400;
                }
              }
            }
          }
        }
      }
    }
  }

  // Grey Version ---
  &--grey {
    background-color: $color-gray-100;
    border: 1px solid $color-gray-200;

    .btn {
      &.qo-order {
        color: $color-dark-200;
        background-color: $color-gray-300;
        border: none;
        box-shadow: none;

        &:hover {
          background: $color-gray-400 !important;
        }
      }

      &.qo-submit {
        color: $color-white;
        background: rgba($color-black, 0.50) !important;
      }
    }

    .qo {
      &-title {
        h1 {
          * {
            color: $color-dark-100;
          }

          input {
            &::placeholder {
              color: $color-dark-100;
            }
          }
        }
      }

      &-form {
        .form-control {
          background-color: $color-white;
        }

        .rec-checkbox {
          &.rec-checkbox--default {
            input[type=checkbox] {
              + label {
                color: $color-dark-200;

                &:before {
                  border-color: rgba($color-gray-400, 0.5);
                }

                &:after {
                  color: $color-gray-400;
                }
              }
            }
          }
        }
      }
    }
  }

  &.confirmed {
    background: rgba($color-primary, 0.05);
    border: 1px solid rgba($color-primary, 0.5) !important;

    .qo-title {
      h1 {
        span {
          font-size: $text-xx-medium;

          i {
            @include right-icon-el(10px);
            transform: translateY(-3px);
          }
        }
      }

      * {
        color: $color-primary;
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      .qo-title {
        flex-direction: column;

        h1 {
          margin: 0 0 5px;
        }
      }
    }
  }
}

#one_click_theme_options {
  li {
    margin-left: 15px;

    .rec-radio-option {
      width: auto;
      margin: 0 !important;
      padding: 0;

      input {
        display:none;
        &[type="radio"] {
          &:checked,
          &:not(:checked) {
            + label {
              padding: 4px 35px 8px 10px;
              position:relative;
              @include b-radius($b-radius-sm);
              @include transi();
              font-size: $text-small;
              line-height: 20px;

              &:before {
                content: '';
                display: block;
                width: 20px;
                height: 20px;
                position: absolute;
                top: 6px;
                right: 6px;
                @include b-radius(100%);
                @include transi();
                border:1px solid $color-primary;
              }

              &:after {
                display: none;
              }
            }
          }

          &:not(:checked) {
            + label {
              &:after {
                opacity: 0;
                transform: scale(0);
              }
            }
          }

          &:checked {
            + label {
              &:before {
              }
              &:after{
                display: block;
                position:absolute;
                content:'';
                background:$color-primary;
                width: 10px;
                height: 10px;
                top:11px;
                right:11px;
                @include b-radius(50%);
              }
            }
          }
        }

      }
    }

    &:last-child {
      margin: 0;
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {
    li {
      margin-left: 5px;

      .rec-radio-option {
        input {
          &[type="radio"] {
            &:checked, &:not(:checked) {
              + label {
                font-size: $text-xx-small;
              }
            }
          }
        }
      }
    }
  }
}