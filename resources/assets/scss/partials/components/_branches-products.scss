.branches-products {
  $branches-products-root: &;
  @include flexable(flex-start, space-between, column);
  flex-wrap: wrap;
  padding: 15px;
  border: 1px solid $color-gray-200;
  @include b-radius($b-radius-sm);

  &--bordels {
    border: none
  }

  > div {
    flex: auto;
    width: 100%;

    &:first-of-type {
      .product-info {
        padding: 0;
        @include flexable(center, flex-start, row);

        a {
          padding: 8px;
          border: 1px solid $color-gray-200;
          display: inline-block;

          img {
            max-width: 32px;
          }
        }

        > div {
          width: 100%;
        }

        .product-name, .product-price {
          font-size: $text-xxx-small;
          font-weight: bold;
          margin-bottom: 2px;
          word-break: break-word;
        }

        .product-price {
          color: $color-secondary; //colorAvilabilty - its close to primary but the using here it might deffirent
        }
      }

      .product-options {
        display: none;
      }
    }
  }

  &__branch {

    > span {
      font-size: $text-xx-small;
      display: inline-block;
      margin-bottom: 7px;

      &:before {
        font-family: $font-sallaIcon;
        font-size: $text-xxx-small;
        margin-left: 3px;
        position: relative;
        top: 2px;
      }

      &.empty {
        color: $color-danger;

        &:before {
          content: '\ecdf';
          color: $color-danger
        }

        > span {
          display: none;
        }
      }

      &.not-empty {
        color: $color-primary;

        &:before {
          content: '\ea9b';
          color: $color-primary
        }

        span {
          margin-right: 5px;
          color: $color-dark-300;
          position: relative;
          top: 1px;

          &:before {
            content: none;
          }

          b {
            font-weight: bold;
          }
        }
      }
    }

    .bootstrap-select {
      position: initial
    }
  }

  &__product-fields {
    width: 100% !important;
    margin-top: 15px;

    > div {
      @include flexable(center, space-between, row);

      label {
        flex: 0 0 50%;
        font-size: $text-xxx-small;
        font-weight: bold;
      }

      .form-group {
        flex: auto;
        width: 100%;

        .bootstrap-select {
          width: 100% !important;
        }

        .form-check-label {
          .checker {
            margin-left: 5px;
          }
        }
      }

      &:last-of-type {
        .input-group {
          @include flexable(flex-start, flex-start, row);
          border: 1px solid $color-gray-200;

          span {
            padding: 9px 0 0 0;
            width: 36px;
            border: none;
          }
        }
      }

      &:not(:last-of-type) {
        .form-group {
          margin-bottom: 10px;
        }
      }

      &:last-of-type {
        .form-group {
          margin: 0;
        }
      }
    }
  }

  &.no-branches {
    #{$branches-products-root} {
      &__branch {
        display: none
      }
    }
  }

  @include mediaMaxWidth(576px) {
    flex-direction: column;
    > div {
      width: 100%;
    }

    #{$branches-products-root} {
      &__branch {
        margin: 10px 0;
      }

      &__product-fields {
        > div {
          flex-direction: column;
          align-items: flex-start;

          .form-group {
            flex: 0 0 100%;
            width: 100%;
          }

          &:first-of-type {
            .form-group {
              margin-bottom: 12px;
            }
          }
        }
      }
    }
  }
}