.rec {
  &-faq {
    &--light {
      .card-title {
        margin: 0 0 15px;

        i {
          display: none
        }

        a {
          position: relative;
          font-size: $text-small;
          font-weight: bold;
          display: block;
          @include flexable(center, space-between, row);

          &:after {
            font-family: $font-sallaIcon;
            font-size: $text-x-small;
            display: block;
          }

          &.collapsed {
            background: $color-gray-25;
            padding: 20px;

            &:after {
              content: '\e90b';
              color: $color-dark-100;
            }
          }

          &[aria-expanded=true] {
            border: 1px solid rgba(92, 213, 196, 0.5); //colorAvilabilty
            border-bottom: 0;
            color: $color-primary !important;
            padding: 20px 20px 0;
            margin-bottom: 15px;

            &:after {
              content: '\ed91';
              background-color: $color-primary;
              width: 25px;
              height: 25px;
              color: $color-dark-100;
              @include b-radius(50%);
              text-align: center;
              line-height: 25px;
            }
          }
        }
      }

      .collapse {
        .card-body {
          p {
            font-size: $text-small;
            margin: 0;
          }
        }

        &.in {
          border: 1px solid rgba(92, 213, 196, 0.5); //colorAvilabilty
          border-top: 0;
          padding: 10px 20px 20px;
          margin-bottom: 15px;
          position: relative;
          top: -15px;
        }
      }

      .card {
        &:last-of-type {
          .card-title {
            margin-bottom: 0;
          }
        }
      }
    }
    &--basic {
      .card-title {
        margin: 0 0 15px;

        i {
          display: none
        }

        a {
          position: relative;
          font-size: $text-small;
          font-weight: bold;
          display: block;
          @include flexable(center, space-between, row);
          border-bottom: 1px solid $color-gray-200;

          &:after {
            font-family: $font-sallaIcon;
            font-size: $text-xx-medium;
            display: block;
          }

          &.collapsed {
            padding: 20px;

            &:after {
              content: '\e90a';
              color: $color-gray-500;
            }
          }

          &[aria-expanded=true] {
            color: $color-primary !important;
            border-bottom: 0;
            padding: 20px 20px 0;
            margin-bottom: 15px;

            &:after {
              content: '\ed91';
              width: 25px;
              height: 25px;
              color: $color-primary;
              @include b-radius(50%);
              border: 1px solid $color-secondary;
              text-align: center;
              line-height: 25px;
            }
          }
        }
      }

      .collapse {
        .card-body {
          p {
            font-size: $text-small;
            margin: 0;
          }
        }

        &.in {
          border-bottom: 1px solid $color-gray-200;
          padding: 10px 20px 20px;
          margin-bottom: 15px;
          position: relative;
          top: -15px;
        }
      }

      .card {
        &:last-of-type {
          .card-title {
            margin-bottom: 0;
          }
        }
      }
    }
 
  }
}