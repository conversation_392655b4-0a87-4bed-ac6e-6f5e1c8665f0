.rec {
  &-client-contact {
    margin: 10px 0 0;
    @include flexable(flex-start, flex-start, column);
    gap: 5px;

    .direct-phone {
      @include flexable(center, flex-start, row);
      gap: 10px;
      unicode-bidi: plaintext;

      span {
        @include flexable();
        min-width: 70px;
        gap: 5px;
        padding: 5px;
        @include b-radius(50px);
        font-size: $text-xx-small;
        color: $color-primary-l;
        line-height: 1;
        text-align: center;
        border: 1px solid $color-secondary;

        i {
          display: inline-block;
          vertical-align: middle;
          font-size: 13px;
        }
      }

      &--large {
        span {
          font-size: $base-size;
        }
      }

      &--align-centered {
        @include flexable(center, flex-end, row);
      }

      &--sharp {
        span {
          @include b-radius(1px);
          font-weight: 400;

          i {
            background: transparent;
          }
        }
      }

      @include mediaMaxWidth($screen-phones) {
        &--align-centered {
          justify-content: center;
        }
      }
    }

    .rec-contact-list {
      @include strip-ul();
      @include flexable(center, flex-start, row);
      gap: 15px;
      margin-top: 10px;

      li {
        display: inline-block;
        vertical-align: middle;
        position: relative;

        a {
          @include flexable();
          gap: 5px;
          font-size: $text-x-small;

          i {
            display: inline-block;
            vertical-align: middle;
            font-size: inherit;
            color: inherit;
            @include transi();
          }

          &.large-font {
            font-size: $base-size;
          }
        }

        &:hover {
          a {
            color: $color-primary-d;
          }
        }
      }

      &--extra-space {
        li {
          &:not(:last-of-type) {
            padding-left: 30px;
          }
        }
      }

      @include mediaMaxWidth(375px) {
        &--extra-space {
          li {
            &:not(:last-of-type) {
              padding-left: 15px;
            }
          }
        }
      }
    }
  }

  &-order-light {
    position: relative;
    font-size: $text-small;
    width: 100%;
    margin-bottom: 10px;
    padding-bottom: 10px;

    b, strong {
      display: block;
      line-height: 1;
    }

    span {
      font-size: $text-small;
      color: $color-dark-200;
    }
  }
}
