.file {
  &-input {
    .file-preview-thumbnails {
      @include flexable(center, center, row);

      .file-preview-frame {
        flex: auto;
        width: 100%;
      }
    }

    .progress {
      margin-bottom: 10px;
    }

    .file-caption-main {
      @include flexable(center, flex-start, row);

      .form-control {
        flex: auto
      }

      .input-group-btn {
        width: auto;
      }
    }
  }
}

.file-input {
  &--light {
    .file-input.btn-flat {
      border: none;
      background: $color-gray-200;
    }

    .file-preview {
      margin-bottom: 10px;
    }

    &.bank-content {
      .btn-file {
        min-height: 50px;
        border: none;
        background: $color-gray-200;

        i {
          display: block;
          position: absolute;
          top: 50%;
          right: 50%;
          transform: translateX(50%) translateY(-50%);

          &:before {
            content: 'ارفاق الملف...';
            font-family: $font-main;
            font-size: $text-small;
            color: $color-dark-200;
          }
        }
      }
    }
  }

  &--rtl {
    .file-preview {
      .file-preview-thumbnails {
        .file-preview-frame.krajee-default {
          float: right;
          box-shadow: 0 1px 5px 0 $color-gray-300 !important;
        }
      }
    }
  }

  // when loaded ---
  &.loaded {
    .btn-file {
      display: none;
    }
  }
}
