.multi-select-full {
  .btn-group {
    .multiselect-container {
      &.dropdown-menu {
        list-style: none;
        margin: 0;
        padding: 0;
        background-color: $color-white;
        width: 100%;
        max-height: 250px;
        overflow-y: auto;
        @include b-radius(0);

        li {
          a {
            background-color: transparent !important;

            label {
              padding: 8px 15px 8px 12px;
              font-size: $text-x-small;
              color: $color-dark-300;
              display: block;
              @include transi();
            }
          }

          &:hover {
            a {
              label {
                background-color: rgba($color-secondary-50, 0.25) !important; //colorAvilabilty
              }
            }
          }

          &.active {
            margin-bottom: 1px;

            a {
              label {
                color: $color-primary-l;
                background-color: rgba($color-secondary-50, 0.25); //colorAvilability
              }
            }

            &:hover {
              a {
                label {
                  color: $color-primary-l !important;
                  background-color: rgba($color-secondary-50, 0.5) !important;
                }
              }
            }
          }
        }
      }
    }

    .caret {
      color: $color-dark-100;
    }

    // opened ---
    &.open {
      .multiselect.btn-default, .multiselect.btn-default:active {
        border-color: $color-gray-200;
      }
    }
  }
}


