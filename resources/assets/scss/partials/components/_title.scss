.title {
  margin: 0;

  &--small {
    font-size: $base-size;
  }

  &--primary {
    color: $color-primary;
  }

  &--icon {
    i {
      margin-left: 6px;
      color: $color-gray-300;
      font-size: $text-x-medium;
      position: relative;
      top: 2px;
    }
  }

  &--bold {
    font-weight: bold;
  }

  &--large {
    font-size: $text-xx-large;
  }

}

.rec-title {
  display: block;
  width: 100%;
  height: auto;
  position: relative;

  * {
    display: block;
    margin: 0;
  }

  &--section {
    @include flexable();

    * {
      font-size: $text-medium;
      padding: 0 15px 0;
      background: $color-white;
      z-index: 9;
    }

    &:after {
      content: '';
      display: block;
      width: 95%;
      height: 1px;
      background: $color-gray-100;
      position: absolute;
      top: 60%;
      transform: translateY(-50%);
    }
  }

  &--x-large {
    font-size: $text-larger;
  }

  &--large {
    font-size: $text-large;
    font-weight: bold;
  }

  &--medium {
    font-size: $text-x-medium;
    font-weight: bold;
  }

  &--with-sub-title {
    span {
      font-weight: 400;
      font-size: $text-small;
    }
  }
}

.rec {
  &-title-block {
    display: block;
    width: 100%;
    height: auto;
    position: relative;

    * {
      margin: 0;
    }

    &--center {
      * {
        text-align: center;
      }
    }

    &--primary {
      * {
        color: $color-primary;
      }
    }

    &--large {
      margin: 0 0 30px 0;

      * {
        &:not(.text-muted) {
          font-size: 30px;
        }
      }

      @include mediaMaxWidth($screen-phones) {
        * {
          &:not(.text-muted) {
            font-size: $text-larger;
          }
        }
      }
    }

    &--medium {
      margin: 0 0 20px 0;

      * {
        &:not(.text-muted) {
          font-size: $text-xx-medium;
        }
      }
    }

    &--small {
      margin: 0 0 15px 0;

      h1 {
        font-size: 15px;
      }
    }

    &--smaller {
      margin: 0 0 15px 0;

      h1 {
        font-size: $text-medium;
      }

      * {
        &:not(.text-muted) {
          font-size: $text-medium;
        }
      }
    }

    &--light {
      * {
        font-family: $font-main;
      }
    }

    &--smaller {
      h1 {
        font-size: $text-medium;
      }
    }

    &--slogan {
      .slogan {
        display: block;
        font-size: $text-medium;
        color: $color-dark-300;
      }
    }
  }

  &-sep-title {
    @include flexable();
    text-align: center;
    position: relative;
    margin: 0 0 20px 0;

    > * {
      text-align: center;
      display: inline-block;
      width: auto;
      margin: 0 auto;
      padding: 0 20px;
      font-size: 16px;
    }

    &.small {
      > * {
        font-size: $text-small;
        color: $color-dark-200;
      }
    }

    &.large {
      > * {
        font-size: $text-large;
      }
    }

    &.sided {
      justify-content: flex-start;

      * {
        padding-right: 0;
      }

      &:before {
        display: none;
      }
    }


    &.light {
      > * {
        font-family: $font-main;
      }
    }

    &:before, &:after {
      content: '';
      flex: auto;
      height: 1px;
      background: $color-gray-200;
    }
  }
}
