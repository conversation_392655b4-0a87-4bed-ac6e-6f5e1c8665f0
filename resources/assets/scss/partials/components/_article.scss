.rec {
  &-article {
    display: block;
    width: 100%;
    height: auto;
    position: relative;
    margin: 0;

    blockquote {
      display: block;
      width: 100%;
      height: auto;
      padding: 0;
      margin: 0;
      font-size: $text-larger;
      border: none;
      color: $color-primary;
    }

    .img-responsive {
      width: 100%;
    }

    * {
      margin-bottom: 10px;
      font-size: $text-small;
    }

    @each $num in 1, 2, 3, 4, 5, 6 {
      h#{$num} {
        margin: 0 0 15px 0;
        word-break: break-word;
      }
    }

    h1 {
      font-size: $text-xx-medium;
    }

    h2 {
      font-size: $text-medium;
    }

    h3 {
      font-size: $base-size;
    }

    a {
      display: inline-block;
      padding: 1px 6px 2px;
      @include transi();
      @include b-radius($b-radius-sm);
      word-break: break-word;
      margin: 0;

      &:hover {
        background: rgba($color-primary, 0.1);
      }
    }

    ul, ol {
      padding: 0;
      margin-right: 15px;
      @include strip-ul();

      li {
        padding-right: 10px;
        margin-bottom: 10px;

        ul, ol {
          margin-top: 10px;
          margin-right: 5px;
        }
      }
    }

    ul {
      li {
        list-style: none;

        &:before {
          content: '-';
          display: inline-block;
          vertical-align: middle;
          margin-left: 5px;
          padding-bottom: 6px;
        }
      }
    }

    ol {
      counter-reset: list-counter;

      li {
        padding-right: 0;
        counter-increment: list-counter;

        &:before {
          content: counter(list-counter) ". ";
        }
      }
    }

    img {
      display: block;
      margin: 0 0 20px 0;
    }

    @include mediaMaxWidth($screen-tablet-l) {
      blockquote {
        padding: 1rem;
        background-color: $color-gray-25;
        @include b-radius($b-radius);
      }
    }

    &--sm {
      font-size: $text-x-small;

      * {
        margin-bottom: 5px;
        font-size: $text-x-small;
      }

      ul, ol {
        margin-right: 10px;

        li {
          padding-right: 5px;
          margin-bottom: 5px;

          ul, ol {
            margin-top: 5px;
          }
        }
      }
    }
  }
}
