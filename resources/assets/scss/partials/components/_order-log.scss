@mixin statusOrder($color,$orderRoot) {
  #{$orderRoot} {
    &__icon {
      .icon-cont, .status {
        color: rgba($color, 0.8); //colorAvilabilty

      }
    }
  }
}

.order-log {
  $order-root: &;
  @include flexable(flex-start, flex-start, column);
  @include strip-ul();

  li, > div {
    @include flexable(flex-start, flex-start, row);
    width: 100%;
    height: auto;
    min-height: 60px;
    position: relative;
    padding: 20px 10px 20px 20px;
    margin: 0;
    border-bottom: 1px solid $color-gray-25;

    #{$order-root} {
      &__icon {
        @include flexable(center, center, column);
        flex: 0 0 80px;
        transform: translateY(2px);

        .icon-cont {
          color: $color-gray-400;
        }

        small {
          @include flexable(center, flex-start, row);
          display: inline-flex;
          font-size: $text-xxx-small;
          color: $color-gray-400;
        }

      }

      &__info {
        flex: 1 1 auto;

        > div {
          .name {
            display: inline-flex;
            font-size: $text-small;
            color: $color-dark-100;

            i {
              font-size: $text-small;
              margin: 0 0 0 5px;
              transform: translateY(5px);
            }

            b {
              font-family: $font-main;
              display: inline-block;
              margin: 0 5px 0 0;
              color: $color-primary;
            }
          }

          .std {
            display: inline-flex;
            font-size: $text-xx-small;
            color: $color-gray-400;

            small {
              @include flexable(center, flex-start, row);
              //margin: 0 0 0 10px;
              p {
                @include flexable(center, flex-start, row);
                position: absolute;
                left: 0;
                right: auto;
                top: 0;
                padding: 5px 10px;
                margin: 0;
                @include b-radius($b-radius-sm);
                background-color: $color-dark-100;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                visibility: hidden;
                opacity: 0;
                @include transi();

                > * {
                  color: $color-white;
                  flex: 1 0 auto;
                  margin: 0 0 0 5px;

                  &:last-child {
                    margin: 0;
                  }
                }
              }

              i {
                font-size: $text-x-small;
                margin: 0 0 0 3px;
              }

              &:last-child {
                margin: 0;
              }

              &.since {
                padding: 0 10px 4px 0;
                cursor: pointer;

                &:hover {
                  color: $color-dark-100;
                  //cursor: help;
                  > p {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(-8px);
                  }
                }
              }

              &.status {
                padding: 2px 10px 5px;
                @include b-radius(50px);
              }
            }
          }

          &:first-child {
            @include flexable(center, space-between, row);
          }
        }
      }

      &__note {
        display: block;
        margin: 2px 0 0;

        p {
          font-size: $text-small;
          color: $color-gray-400;
          margin: 0;
        }
      }
    }

    &.status-order--1 { // deleted
      #{$order-root} {
        &__icon {
          .status {
            color: rgba($color-danger, 0.8);
          }
        }
      }
    }

    &.status-order-1 { // await pay
      @include statusOrder($color-danger, $order-root);
    }

    &.status-order-2 { // await review
      @include statusOrder($color-dark-300, $order-root)
    }

    &.status-order-3 { // in progress
      @include statusOrder(#37baf6, $order-root)
    }

    &.status-order-4 { // complete
      @include statusOrder(#58c9b9, $order-root)
    }

    &.status-order-5 { // cancelled
      @include statusOrder(#cf444d, $order-root)
    }

    &.status-order-6 { // failed payment
      @include statusOrder(#f8d138, $order-root)
    }

    &.status-order-7 { // returned
      @include statusOrder(#ed696d, $order-root);
    }

    &.status-order-8 { // delivering
      @include statusOrder(#f3b051, $order-root)
    }

    &.status-order-9 { // delivered
      @include statusOrder(#4fb0c6, $order-root)
    }

    &.status-order-10 { // shipped
      @include statusOrder(#008c9e, $order-root)
    }

    &.status-order-comment { // shipped
      @include statusOrder($color-gray-400, $order-root)
    }

    &:last-child {
      border: none;
    }

    @include mediaMaxWidth($screen-tablet-l) {
      align-items: flex-start;
      padding: 15px 5px 15px 15px;
      #{$order-root} {
        &__icon {
          flex: 0 0 70px;
          margin-left: 5px;
        }

        &__info {
          > div {
            .name {
              flex: 1 0 auto;
            }

            .std {
              width: 100%;
              justify-content: flex-end;
              margin: 0;

              small {
                &.status {
                  padding: 1px 8px 4px;
                }

                &.since {
                  p {
                    top: 10px;
                  }

                  &:hover {
                    > p {
                      transform: translateY(-15px);
                    }
                  }
                }
              }

            }

            &:first-child {
              width: 100%;
            }
          }
        }
      }
    }
  }

  &--table {
    li, > div {
      align-items: center;

      #{$order-root} {
        &__info {
          > div {
            .std {
              p {
                left: auto !important;
                right: 0 !important;
                z-index: 10;
                min-width: 145px;
              }
            }

            @include mediaMaxWidth($screen-phones) {
              .std {
                p {
                  right: auto !important;
                  left: 0 !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
