.order-st-entry {
  flex: auto;
  min-width: 170px;
  min-height: 90px;
  @include b-radius($b-radius);
  @include flexable(flex-end, space-between, row);
  @include transi();
  padding: 40px 11px 6px;
  position: relative;
  background-color: $color-white;
  border: 1px solid $color-gray-200;
  cursor: pointer;
  margin: 0 5px;
  max-width: 250px;

  .refresh-data{
    left: 40%;
    padding: 0;
    position: absolute;
    top: 10px;
    transition: all .35s cubic-bezier(.2,1,.3,1);
    width: 20px;
    height: 20px;
    display: none;
  }
  //General Icon
  &:before {
    font-family: $font-sallaIcon;
    font-size: 20px;
    position: absolute;
    right: 18px;
    top: 8px;
    color: $color-dark-100;
  }

  .btn {
    width: 20px;
    height: 20px;
    @include b-radius(50%);
    @include transi();
    background-color: $color-danger;
    display: none;
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0;

    &:before {
      content: '\ea47';
      font-family: $font-sallaIcon;
      color: $color-white;
      position: relative;
      top: -1px;
    }

    &:hover {
      background-color: #f44336; //colorAvilabilty
    }
  }

  h5 {
    font-family: $font-main;
    font-size: $text-xx-medium;
    color: $color-dark-300;
    margin: 0;
    width: 100%;
    padding-right: 18px;
    position: relative;
    @include flexable(center, space-between, row);

    span {
      font-size: $text-xx-small;
      color: $color-dark-300;
      margin-left: 12px;


      //Type Active Color
      &.status-circle {
        width: 8px;
        height: 8px;
        @include b-radius(50%);
        position: absolute;
        right: 0;
        top: 10px;
      }
    }
  }

  //Different class Modifier to specify icons and status circle color
  &--in-progress {
    &:before {
      content: '\e920';
    }

    h5 span {
      &:before {
        background: #37BAF6 //colorAvilabilty
      }
    }
  }

  &--done {
    &:before {
      content: '\ee13';
    }

    h5 span {
      &:before {
        background: #37BAF6 //colorAvilabilty
      }
    }
  }

  &--shipping {
    &:before {
      content: '\ef31';
    }

    h5 span {
      &:before {
        background: #4FB0C6
      }
    }
  }

  &--checkup {
    &:before {
      content: '\eb24';
    }

    h5 span {
      &:before {
        background: $color-dark-300
      }
    }
  }

  &--payment {
    &:before {
      content: '\e935';
    }

    h5 span {
      &:before {
        background: #54CABB
      }
    }
  }

  &--returned {
    &:before {
      content: '\e95e';
    }

    h5 span {
      &:before {
        background: $color-green
      }
    }
  }

  &--delivered {
    &:before {
      content: '\e930';
    }

    h5 span {
      &:before {
        background: #4FB0C6 //colorAvilabilty
      }
    }
  }

  &--shipped {
    &:before {
      content: '\ef32';
    }

    h5 span {
      &:before {
        background: #4FB0C6 //colorAvilabilty
      }
    }
  }

  &--canceled {
    &:before {
      content: '\ea6f';
    }

    h5 span {
      &:before {
        background: $color-danger
      }
    }
  }

  &.active {
    background-color: $color-primary-l;

    &:before {
      color: $color-secondary-50
    }

    .btn , .refresh-data {
      display: block;
    }

    h5, span {
      color: $color-secondary-50 !important;

      span {
        &.status-circle {
          background-color: $color-secondary-50 !important;
        }
      }

    }
  }

  &:first-child {
    margin-right: 0;
  }

  &:last-child {
    margin-left: 0;
  }

  @include mediaMaxWidth($screen-phablet) {

    h5 {
      font-size: $text-x-medium;

      span {
        font-size: $text-x-small;

        &.status-circle {
          top: 12px
        }
      }
    }
  }
}
