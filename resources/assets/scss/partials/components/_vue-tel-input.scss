.vue-tel-input {
  direction: ltr;
  border-color: $color-gray-200;
  transition: 0.3s;
  border-radius: 0;
  border: 0 !important;
  &.fullWidth {
    // .vti__dropdown {
    //   position: unset;
    //   border: 1px solid #eee;
    //   background: #fcfcfc;
    // }
    .vti__dropdown-list {
      width: 100%;
    }
  }
  &:focus-within {
    box-shadow: none !important;
    border-color: #eee !important;
  }
  input {
    border: 1px solid #eee;
    text-align: right;
    border-right: 0;
    border-left: 0;
    padding: 5px;
    &::placeholder {
      color: #999;
      font-size: 13px;
    }
  }
  .vti__dropdown {
    padding: 7px 0;
    border: 1px solid #eeeeee;
    border-right: 0;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
    position: unset;
  }
  .vti__dropdown-list {
    direction: rtl;
    text-align: right;
    width: 352px;
    border-color: $color-gray-200;
    &.below {
      top: 42px !important;
    }
  }
  .vti__dropdown-item {
    transition: 0.3s;
    font-size: 0.9em;
    padding: 8px 10px 10px;
    text-align: right;
    strong {
      font-weight: normal;
    }
    &:hover {
      background: rgb(240, 240, 240);
    }
    .vti__flag {
      margin-right: 0;
      margin-left: 10px;
    }
  }
}