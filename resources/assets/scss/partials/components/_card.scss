.card {
  $card-root: &;
  @include flexable(flex-start, flex-start, column);
  position: relative;

  &__header {
    img {
      display: block;
      max-width: 65px;
    }

    .circle-icon {
      display: block;
      width: 65px;
      height: 65px;
      background-color: #E4FAF5;
      border-radius: 50%;
      text-align: center;
    }
  }

  &__content {
    flex: 1;
    h6 {
      color: $color-dark-300;
    }
  }

  // &__footer {}

  &--horizontal {
    align-items: center;
    justify-content: center;
    flex-direction: row;
    &.flex-stretch {
      justify-content: flex-start;
      align-items: stretch;
    }
  }

  &--responsive {
    @include mediaMaxWidth($screen-phones) {
      flex-direction: column;
      align-items: center !important;

      #{$card-root} {
        &__header {
          margin: 0 0 15px 0 !important;
        }

        &__content {
          text-align: center;

          * {
            text-align: center;
          }

          margin: 0 0 15px 0 !important;
        }
      }
    }
  }

  &--clickable {
    padding-left: 30px;
    &:after {
      content: '\e970';
      font-family: $font-sallaIcon;
      font-size: $text-larger;
      color: $color-gray-400;
      position: absolute;
      @include centerY();
      left: 0;
      opacity: 0.5;
    }
  }

  // dimmed here for the card component in general ---
  &--dimmed {
    #{$card-root} {
      &__header, &__content {
        opacity: 0.5;
      }
      &__header {
        img {
          filter: grayscale(100%);
        }
      }
    }
  }

  // dimmed here for shipping companies only --
  &--dimmed {
    &.conditional-display {
      #{$card-root} {
        &__content {
          opacity: 1;
          h6 {
            color: $color-gray-400 !important;
            .tooltip-content {
              color: $color-dark-300 !important;
            }
          }
        }
      }
    }
  }

  &--special-widget {
    padding: 15px 15px 15px 40px;
    background: $color-primary-l;

    .card__header {
      margin-top: -15px;
    }

    .badge {
      &--primary {
        background-color: $pro-color; //wired variables
        color: $color-dark-300;
        transform: translateY(15px);
      }
    }

    .rounded {
      width: 65px;
      height: 65px;
      @include b-radius(50%);
      border: 2px dashed $pro-color;
      object-fit: cover;
    }

    &:after {
      font-size: $text-xx-larger;
      color: $color-primary;
      left: 10px;
      opacity: 0.6;
    }

    &.unactive {
      &:after {
        content: none
      }
    }
  }

  &--flyer {
    background: $color-white;
    padding: 20px;
    border: 1px solid $color-gray-200;
    @include b-radius($b-radius);
    #{$card-root} {
      &__header {
        padding: 20px;
        margin: 0 !important;
        background: $color-gray-50;
        @include b-radius($b-radius-sm);
        border: 1px solid $color-gray-200;
        &.with-image {
          padding: 0;
          background: transparent;
          border: 0;
        }
      }
      &__content {
        padding: 0 20px;
      }
    }
    @include mediaMaxWidth($screen-phablet) {
      padding: 0;
      #{$card-root} {
        &__header, &__content, &__footer {
          width: 100%;
          text-align: center;
        }
        &__header {
          border: none;
          border-bottom: 1px solid $color-gray-200;
          @include b-radius(0);
        }
        &__content,
        &__footer {
          padding: 20px;
          margin: 0 !important;
        }
      }
    }
  }
}
