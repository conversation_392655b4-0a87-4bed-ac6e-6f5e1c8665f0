.alert-shipping-balance {
  background: #FEEFB3;
  @include b-radius($b-radius-sm);
  color: #807b5c;

  > a {
    color: #807b5c !important;

    i {
      float: left;
    }
  }
}

.alert {

  &-paid {
    padding-right: 0 !important;
  }

  &-salla {
    background: $color-danger;
    color: $color-white;
    text-align: center;
    padding: 10px 0;
    font-size: $text-small;
  }

  &-red {
    border: 0;
    display: block;

    .label {
      background: $color-danger;
      margin-left: 10px;
      @include b-radius(33%)
    }

    &:hover {
      color: #9c1f1f;
    }
  }

  &-img {
    @include flexable(center, space-between, row);

    img {
      width: 20px
    }
  }
}

.rec {
  &-alert-box {
    @include flexable();
    padding: 7px 10px 9px;
    @include b-radius($b-radius-sm);
    margin: 10px 0 0;

    p {
      font-size: $base-size;
      margin: 0;
    }

    i, img {
      display: inline-block;
      vertical-align: middle;
      margin: 0 0 0 10px;
      transform: translateY(2px);
    }

    &--start {
      justify-content: flex-start;
    }

    &--success {
      background-color: $color-gray-200;

      * {
        color: $color-primary;
      }
    }

    &--danger {
      background-color: $color-gray-50;

      * {
        color: $color-danger;
      }
    }


    &--info {
      background-color: $color-gray-50;

      * {
        color: #8a6d3b; //colorAvilabilty
      }
    }

    &--small {
      p {
        font-size: $text-xx-small;
      }
    }

    &--violation {
      &.replay {
        span {
          display: inline-block;
          font-weight: 400;
          margin-right: 4px;

          &.replay-count {
            background-color: #9C1F1F;
            @include b-radius(50%);
            font-size: $text-x-small;
            font-weight: bold;
            color: $color-white;
            width: 20px;
            height: 20px;
            text-align: center;
            position: absolute;
            right: 43px;
            top: 16px;
          }
        }

        @include mediaMaxWidth($screen-phablet) {
          h4, p {
            font-size: 14px;
          }
          p {
            width: 98%;
          }
        }
      }
    }
  }
}

.alert-belt {
  @include flexable(center, start, row);
  background-color: #fff7ec !important;
  font-style: normal;
  font-weight: 400;
  font-size: $text-x-small;
  line-height: 19px;
  margin-top: auto;
  margin-bottom: 0 !important;

  &.danger {
    .ring-border {
      @media screen and (max-width: 767px) {
        margin-left: 0px !important;
        width: 30px;
        height: 25px;
      }
      @include flexable(center, center, row);
      @include b-radius(50%);

      background: #F55157;
      width: 30px;
      height: 30px;
      margin: 0 10px;
      box-sizing: border-box;
      border: 4px solid white;
      color: white;
      box-shadow: 0 0 0 6px #8B080D;
      position: relative;
    }

    background: #fee2e2;
    border: 1px solid rgba(245, 81, 87, 0.2);
    color: #8B080D;

    a {
      color: $color-danger;
      text-decoration: underline;
    }
  }
}

#alert_row {
  position: sticky !important;
  top: 0;
  z-index: 99;
  @media screen and (max-width: 767px) {
    top: 60px;
  }
}