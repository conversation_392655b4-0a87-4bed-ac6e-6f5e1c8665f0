.rules {
  
  .vue-treeselect__menu-container {
    top: unset !important;  
  }
  
  .input-group-addon {
    border-color: $color-gray-200;
  }

  &__title {
    font-size: $text-small;
    color: $color-primary-l;
    font-weight: 500;
    margin: 0 0 10px 0;

    span {
      display: inline-block;
      background-color: $color-secondary-d;
      @include b-radius(50%);
      width: 25px;
      height: 25px;
      text-align: center;
      color: $color-primary-l;
      margin-left: 10px;
      line-height: 25px;
    }

    small {
      font-size: $text-xx-small;
      display: block;
      margin-top: 5px;
      font-weight: 400;
    }
  }

  &__groups {
    width: 100%;
    margin-bottom: 20px;
    @include flexable(flex-start, space-between, row);
    &.with-gap{
      gap: 10px;
    }
    > div {
      &.select-group {
        position: relative;
        flex: 1;
        @include flexable(center, flex-start, row);

        > span {
          &:last-of-type {
            width: 32px;
            height: 36px;
            border-left: none;

            i {
              position: relative;
              top: 2px;
            }
          }
        }

        .btn-group {
          width: 100% !important;
        }

        &.categories {
          flex: 0 0 50%;
          margin-right: 20px !important;
          flex-wrap: wrap;

          .input-group-addon {
            padding: 8px 10px 9px 21px;
          }

          .error-msg {
            width: 100%;
            height: auto;
          }

          .vue-treeselect {
            flex: 1
          }
        }
        &.product_quantity_ids {
          flex: 0 50% 0 0;
          margin-right: 20px !important;
          flex-wrap: wrap;

          .input-group-addon {
            padding: 8px 10px 9px 21px;
          }

          .error-msg {
            width: 100%;
            height: auto;
          }

          .vue-treeselect {
            flex: 1
          }

          .vue-treeselect__multi-value {
            height: 33px !important;
          }
        }
      }

      &.outer-field {
        flex: 0 0 50% !important;
        margin-right: 20px !important;
      }

      &.fields-group {
        >.input-group {
          > .form-control {
            border-right: 1px solid #eee;
          }
        }
      }
    }


    .dropdown-toggle {
      border-right: 1px solid $color-gray-200 !important;

      &.bs-placeholder {
        .filter-option {
          color: $color-dark-100
        }
      }
    }


    .condition {
      position: relative;


      .v-select-placeholder {
        right: 11px
      }


      .vs__selected-options {
        border-right: 1px solid $color-gray-200
      }

      .error-msg {
      }
    }

    .btn {
      &-delete-circle {
        width: auto;
        flex: 0 0 22px;
        transform: translateY(7px);
      }
    }

    &.mobile-enhanced {
      @media (max-width: 768px) {
        @include flexable(flex-start, space-between, column);
      }
    }
  }

  &__groups-cart-price {
    > div {
      &.condition {
        flex: 0 0 25%;
        margin: 0 20px 0 0;

        .equal {
          width: 100% !important;
        }
      }

      &.inclusion_field {
        .vue-treeselect__control {
          border-right: 1px solid #eee !important;
        }
      }

      &.quantity_field {
        flex: 0 0 10%;
      }

      &.fields-group {
        margin-right: 15px;
        flex: 0 0 25%;
      }
    }
  }

  .dropdown-toggle {
    &.bs-placeholder {
      .filter-option {
        color: $color-gray-200
      }
    }
  }


  .btn-add {
    padding: 6px 8px 8px 17px;


    i {
      margin-left: 10px;
    }
  }

  .v-select {
    position: initial;


    .vs__selected-options {
      background-color: transparent !important;
      padding-left: 34px;
      overflow: hidden;
    }
  }

  .map-view {
    position: relative;
  }

  .controls {
    position: absolute;
    width: 100%;
    top: 35px;
    right: 0;
    padding: 0 15px;
    z-index: 999999;
    @include flexable(center, flex-start, row);
    flex-direction: row-reverse;
    gap: 10px;


    #map_search {
      height: 36px;
      max-width: initial;
      border: 1px solid $color-gray-200;
      width: 100%;
      position: initial;
      box-shadow: none;
    }

    .btn {
      height: 36px;
      width: auto !important;

      &-add-poly,
      &-delete-poly {
        i {
          margin-left: 5px;
        }
      }


      &#current-location {
        position: initial;
        width: initial !important;
      }
    }
  }


  .alert {
    position: absolute;
    z-index: 9999999999;
    width: 100%;
  }

  @include mediaMaxWidth(500px) {
    &__groups-cart-price {
      flex-wrap: wrap;
      position: relative;

      > div {
        &.select-group,
        &.input-group {
          width: 100%;
          margin-bottom: 15px;

          &.categories,
          .outer-field {
            width: 100%;
            flex: 1;
            margin: 0 !important;
            flex-basis: 100% !important;
            margin-bottom: 1.5rem !important;
          }
        }

        &.condition,
        &.outer-field {
          margin: 0 0 15px 0 !important;
          flex: auto;
          width: 100%;
          flex-basis: 100% !important;
        }

        &.outer-field {
          margin: 0 !important
        }

        &.fields-group {
          width: 100%;
          flex: auto;
          margin-right: 0;
          margin-bottom: 1.5rem;
        }
      }

      .btn {
        &-delete-circle {
          position: absolute;
          top: 2px;
          left: 2px;
          width: 22px;
        }
      }

      &.space {
        padding-left: 35px;
      }
    }
  }
  @include mediaMaxWidth($screen-phablet) {
    .v-select-placeholder {
      width: 35%;
      white-space: nowrap;
      overflow: hidden;
    }
    .controls {
      .btn {
        span {
          display: none;
        }


        i {
          margin: 0
        }
      }
    }
  }
}