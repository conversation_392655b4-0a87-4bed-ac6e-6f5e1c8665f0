// merge this file with tooltip from visitation PR later ---
.tooltip {
  &-toggle {
    position: relative;
    overflow: visible;
    display: block;

    .tooltip-content {
      width: 200px;
      padding: 10px;
      @include b-radius($b-radius-sm);
      @include transi();
      position: absolute;
      background-color: $color-white;
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.18);
      cursor: default;
      opacity: 0;
      visibility: hidden;
      z-index: 99;

      &--xs{
        width: 110px;
        padding: 5px;
        bottom: 65% !important;
      }
      p {
        font-size: $text-xx-small;
        margin: 0;
        white-space: normal;
        text-align: right
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        width: 0;
        height: 0;
        border: 10px solid transparent;
        border-bottom-color: $color-white;
        border-top: 0;
        margin-left: -10px;
        margin-top: -10px;
      }

      &--small {
        font-size: 12px;

        a {
          font-size: $text-xx-small;
          cursor: default;
        }

        .btn {
          padding: 3px 10px 6px;
          margin-right: 5px;
          font-size: $text-xx-small;

          &:after {
            content: '\efd2';
            font-family: $font-sallaIcon;
            font-size: $text-xxx-small;
            position: relative;
            top: 1px;
            margin-left: 5px;
          }

          &.no-text {
            &:after {
              margin-left: 0 !important;
            }

            &.copied {
              &:after {
                content: '\ea9d';
              }
            }
          }
        }
      }

      &--max-width {
        width: 320px;
        max-width: 320px;
      }

      &--ltr {
        direction: ltr;
      }

      &.copy-tooltip {
        @include flexable(center, center, row-reverse);

        a {
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block;
          text-align: left;
        }
      }

      &.width-auto {
        width: auto;
      }

      .bold {
        font-weight: 700 !important;
      }
    }

    &.bottom {
      .tooltip-content {
        top: 100%;
        left: 50%;
        transform: translateX(-50%) translateY(20px);
      }

      &.left {
        .tooltip-content {
          left: 0;
          right: unset;
          transform: translateX(0) translateY(20px);

          &:after {
            left: 10px;
          }
        }
      }

      &.right {
        .tooltip-content {
          right: 0;
          left: unset;
          transform: translateX(0) translateY(20px);

          &:after {
            left: unset;
            right: 10px;
          }
        }
      }
    }

    &.top {
      .tooltip-content {
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%) translateY(-20px);

        &:after {
          top: unset !important;
          bottom: -8px;
          transform: rotate(180deg);
        }
      }

      &.right {
        .tooltip-content {
          right: 0;
          left: unset;
          transform: translateX(0) translateY(-20px);

          &:after {
            left: unset;
            right: 5px;
            bottom: -10px;
            z-index: -1
          }
        }
      }

      &.left {
        .tooltip-content {
          left: -15px;
          right: unset;
          transform: translateX(0) translateY(20px);

          &:after {
            left: 21px;
            z-index: -1
          }
        }
      }
    }

    &.trans {
      .tooltip-content {
        background: $color-white;
        color: $color-dark-200 !important;

        * {
          color: $color-dark-200 !important;
        }

        &:after {
          border-bottom-color: $color-white;
        }
      }
    }

    &.primary {

      border: 1px solid $color-secondary-50;
      background: transparent !important;

      i.tool-icon {
        color: $color-dark-100 !important;
      }

      * {
        color: $color-primary-l !important;
      }

      .tooltip-content {
        background: $color-secondary-50;
        border-color: $color-secondary-50;

        * {
          color: $color-primary-l !important;
        }

        &:after {
          content: '';
          position: absolute;
          top: 1px;
          left: 50%;
          width: 0;
          height: 0;
          border: 8px solid transparent;
          border-bottom-color: $color-secondary-50;
          border-top: 0;
          margin-left: -8px;
          margin-top: -8px;
        }
      }

      &.filled {
        background: $color-secondary-50 !important;

        * {
          color: $color-primary-l !important;
        }
      }

      &.no-border {
        border: none !important;
      }

      &.text-white {
        * {
          color: $color-white !important;
        }
      }

      &.no-border {
        border: 0 !important;
      }

      &.left {
        &.center {
          .tooltip-content {
            right: auto;
            transform: translateX(-100%) translateY(-42%);
            left: -20px;
            &:after {
              top: 50%;
              left: calc(100% + 8px);
              margin-top: -5px;
              border-width: 8px;
              border-style: solid;
              border-color: transparent transparent transparent $color-secondary-50;;
            }
          }
        }
      }
    }

    &.grey {
      background: $color-gray-200 !important;
    }

    &--rounded {
      @include b-radius(50%);
    }


    &--rich {
      .tooltip-content {
        padding: 0;

        &:after {
          border-bottom-color: $color-gray-25;
          margin-left: -10px;
          margin-top: -10px;
        }
      }

      &__head,
      &__content {
        padding: 8px 10px;
      }

      &__head {
        img {
          margin-left: 10px;
        }

        h6 {
          color: $color-dark-300;
          text-align: right !important;
          line-height: 1.3;
        }
      }

      &__content {
        * {
          color: $color-dark-300;
        }

        a {
          * {
            @include transi();
          }

          &:hover {
            * {
              color: $color-primary;
            }
          }
        }

        > * {
          &:not(:last-child) {
            margin-bottom: 5px;
          }
        }
      }
    }

    &:hover {
      .tooltip-content {
        opacity: 1;
        visibility: visible;
      }

      &.top {
        .tooltip-content {
          transform: translateX(-50%) translateY(-15px);
        }

        &.left,
        &.right {
          .tooltip-content {
            transform: translateX(0) translateY(-15px);
          }
        }
      }

      &.bottom {
        .tooltip-content {
          transform: translateX(-50%) translateY(15px);
        }

        &.left,
        &.right:last {
          .tooltip-content {
            transform: translateX(0) translateY(15px);
          }
        }
      }
    }

    &--visible {
      .tooltip-content {
        opacity: 1;
        visibility: visible;
      }
    }

    &--clickable {
      .tooltip-content {
        opacity: 1 !important;
        visibility: visible !important;
        display: none;
      }

      &.bottom {
        &.left,
        &.right {
          .tooltip-content {
            transform: translateY(20px) !important;
          }
        }
      }
    }

    &--small {
      .tooltip-content {
        width: auto;
        min-width: 130px;
        padding: 5px 10px 7px;
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      &.sm-narrow-width {
        .tooltip-content {
          width: 120px;
        }
      }
    }

    &--inline-block {
      display: inline-block;
      transform: translateY(-2px);
    }

    &.no-border {
      border: none !important;
    }

    @include mediaMaxWidth($screen-phablet) {
      &.sm-narrow-width {
        .tooltip-content {
          width: 120px;
        }
      }
    }

    &--responsive {
      .tooltip-content {
        width: auto;
        min-width: 200px;
        @include mediaMaxWidth($screen-phones) {
          width: calc(100vw - 40px);
          white-space: normal;
          * {
            white-space: normal;
          }
        }
      }
    }


    // specifice styling ---
    &.volume-weight {
      .tooltip-content {
        @include mediaMaxWidth($screen-phones) {
          .rec-list {
            flex-direction: column;

            > * {
              &:first-child {
                margin: 0 !important;
                order: 1;
              }

              &:last-child {
                order: 0;
              }
            }
          }
        }
      }
    }
  }
}
