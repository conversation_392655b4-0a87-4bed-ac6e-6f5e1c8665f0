//sharable style
@mixin alertVariant($color) {
  border-color: rgba($color, .2);
  background-color: rgba($color, 0.1);
  color: darken($color, 30%) !important;

  * {
    color: darken($color, 30%) !important;
  }

  a {
    color: $color !important;
  }

  .btn {
    // alert box action button ---
    &-action {
      color: $color-white !important;
      background: darken($color, 30%);
    }
  }
}

.alert-box {
  $alert-box-root: &;
  @include flexable(center, flex-start, row);
  position: relative;
  padding: 10px 15px;
  margin: 0 0 20px 0;
  border-width: 1px;
  border-style: solid;
  @include b-radius($b-radius);
  @include transi(opacity);
  font-size: $text-small;

  a:not(.dropdown-menu a),
  .btn {
    margin: 0 5px;
  }

  a:not(.dropdown-menu a):not(#alert-dismiss a) {
    text-decoration: underline;
  }

  .btn {

    // alert box action button ---
    // border: 1px solid;
    display: block;
    margin-top: 15px;
    width: fit-content;

    &.copied {
      i {
        &:before {
          content: '\ea9d';
        }
      }
    }

    &#{$alert-box-root} {
      &__close {
        width: 30px;
        height: 30px;
        padding: 0;
        flex-shrink: 0;
        margin: 0 15px 0 0;
        @include b-radius($b-radius-sm);
        background: rgba($color-black, 0.05);
        border: none;

        &:before {
          content: '\ea47';
          font-family: $font-sallaIcon;
          font-size: $text-x-small;
        }
      }
    }

    &-action {
      font-size: $text-x-small;
      margin: 0 10px 0 0 !important;
      @include b-radius($b-radius-sm);
      padding: 5px 20px;
      border: none !important;

      &.custom-margin {
        margin-right: auto !important;
        @media screen and (max-width: 767px) {
          margin-right: 0 !important;
          margin-top: 10px !important;
        }
      }
    }
  }

  &.onboarding-banner{
    @include mediaMinWidth($screen-phones){
      padding-right: 60px;
    }
    @include mediaMaxWidth($screen-phones){
      text-align: center;
      @include flexable(center, flex-start, column);
      gap: 10px;
    }
    svg{flex-shrink: 0;}
    #{$alert-box-root} {
      &__close {
        background-color: transparent !important;
        text-decoration: none;
        position: absolute;
        top: 10px;
        right: 10px;

        &:before {font-size: $text-medium !important;}

      }
    }
  }

  > * {
    display: inline-block;
  }

  img {
    position: absolute;
    left: 10px;
    @include centerY;

    &.initial {
      position: initial;
      transform: unset;
    }
  }

  .notification-label {
    height: 25px;
    min-width: 25px;
    background-color: $color-danger;
    color: $color-white !important;
    @include b-radius($b-radius-sm);
    text-align: center;
    line-height: 20px;
    font-size: $text-small;
    position: relative;
    padding: 2px 4px;
    margin-left: 15px;
  }

  i {
    display: inline-block;
    vertical-align: top;
    margin-left: 15px;
    font-size: $text-larger;
    flex-shrink: 0;
  }

  article {
    flex: auto;

    > * {
      margin: 0;
      color: $color-dark-300;

      .dark &{
        color: $color-secondary-50;
      }

      &.text-muted {
        color: $color-dark-100;
      }
    }

    @each $num in 1,
    2,
    3,
    4,
    5,
    6 {
      h#{$num} {
        font-size: $base-size;
        margin-bottom: 0;
      }
    }

    p {
      font-size: $text-x-small;
      margin: 0;
      white-space: normal;
    }

    small {
      font-size: $text-xx-small;
    }

    &.with-separator {
      border-right: 1px solid $color-gray-200;
    }
  }

  .btn {
    // border: 1px solid;
    // display: block;
    // margin-top: 15px;
    // width: fit-content;
    // color: $color-white !important;
    // text-decoration: none;
    // padding: 6px 12px;

    &#{$alert-box-root} {
      &__close {
        width: 30px;
        height: 30px;
        padding: 0;
        flex-shrink: 0;
        margin: 0 15px 0 0;
        @include b-radius($b-radius-sm);
        background: rgba($color-black, 0.05);
        border: none;

        &:before {
          content: '\ea47';
          font-family: $font-sallaIcon;
          font-size: $text-x-small;
        }
      }
    }

    .dark &{
      i:before{
        color: $color-primary;
      }
    }
  }

  &--linked {
    padding-left: 50px;

    &:after {
      content: '\e970';
      font-family: $font-sallaIcon;
      font-size: $text-larger;
      color: inherit;
      position: absolute;
      @include centerY;
      left: 10px;
      opacity: 0.5;
    }

    &.without-icon {
      &:after {
        content: none
      }

      button {
        color: $color-primary !important;
      }
    }
  }

  &--close {
    > i:last-child {
      font-size: $text-xx-medium;
      cursor: pointer;
      opacity: 0.8;
      margin: 2px 0 0 0 !important;
    }
  }

  &--danger {
    @include alertVariant($color-danger);
  }

  &--success {
    @include alertVariant($color-success);
  }

  &--warning {
    @include alertVariant($color-warning);
  }

  &--info {
    @include alertVariant($color-info);
  }

  &--mahally {
    @include alertVariant($color-mahally);
  }

  &--with-image {
    img {
      max-width: 45px;
      position: initial;
      transform: none;
      margin-left: 15px;
    }
  }


  &--white {
    background-color: $color-white;
    border-color: $color-gray-200;

    i:not(.dropdown-menu i):not(button i){
      display: inline-block;
      width: 62px;
      height: 62px;
      border-radius: 50%;
      font-size: $text-larger;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;

      &:before {
        font-family: $font-sallaIcon;
        font-style: normal;
        color: $color-white;
      }
    }

    p {
      color: $color-dark-100;
    }

    &.uncompleted {
      i {
        background-color: #FFEFDA;

        &:before {
          content: '\ef29';
          color: #A46F29 !important;
        }
      }

      h6{
        color:#A46F29;
      }
    }

    &.pending {
      i {
        background-color: $color-warning-light;

        &:before {
          content: '\ef2b'
        }
      }

      h6 span {
        color: $color-warning-light;
      }
    }

    &.completed {
      border-color: $color-gray-200 !important;

      i {
        background-color: $color-light-l;

        &:before {
          content: '\ef2b';
          color: $color-primary !important;
        }
      }

      h6{
        color: $color-primary;
      }
    }
  }

  // if alert-box-white is link ---
  &--center {
    align-items: center;
  }

  &--critical {
    border: none;
    background-color: $color-danger;
    color: $color-white !important;

    * {
      color: $color-white !important;
    }
  }


  &--bar{
      @include flexable(center, flex-start, row);
      background-color: #fff7ec!important;
      font-style: normal;
      font-weight: 400;
      font-size: $text-x-small;
      line-height: 19px;
      margin-top: auto;
      margin-bottom: 0;
      border-radius: 0;
      border-inline: 0;
  }

  &--sticky {
    width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 4999;

    &.alert-box--linked {
      padding-right: 50px;
    }
  }

  &--borderless {
    border: none;
  }

  &--redirect-icon {
    a {
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }

      > i {
        font-size: inherit;
        color: inherit !important;
        vertical-align: baseline;
      }
    }
  }

  &--transparent {
    background-color: transparent;
    border: none;
    padding: 0;

    i {
      color: $color-gray-400;
      font-size: $text-xxxx-larger;
      transform: translateY(4px);
    }

    article {
      p {
        color: $color-dark-100;
        font-size: $text-x-small;
      }
    }
  }

  // for plan upgrade ---
  &--upgrade {
    background: $color-primary;
    border-top: 4px solid rgba($color-primary-darker, 0.3) !important;

    *,
    article *,
    article p {
      font-size: $base-size;
      color: $color-white;
    }

    .content-wrap {
      @include flexable(center, center, row);
      padding: 8px 0;

      article {
        flex: unset;
      }

      .btn {
        margin: 0;
        text-decoration: none;
        background: var(--infinte-color);
        border: 0 !important;
        margin-right: 10px;
        color: $color-primary;
      }

      .counter {
        background-color: var(--infinte-color);
        padding: 5px;
        @include b-radius($b-radius-sm);
        b, span, small {
          color: $color-primary !important;
        }
      }

      p.light-primary {
        color: $color-secondary-50;
      }
    }

    // plus plan ----
    &.plus {
      border-top-color: $color-plan-plus !important;
      background: $color-white;

      *, article * {
        color: $color-plan-plus;
      }

      .btn {
        color: $color-white;
        background: $color-plan-plus;
        border-color: $color-plan-plus;
      }
    }

    // team ----
    &.team {
      border-top-color: $color-plan-team !important;
      background: $color-white;

      *, article * {
        color: $color-plan-team;
      }

      .btn {
        color: $color-white;
        background: $color-plan-team;
        border-color: $color-plan-team;
      }
    }

    &.danger {
      background: $color-danger;
      border-top: 4px solid rgba($color-danger-darker, 0.3) !important;

      .content-wrap {
        .btn {
          background: $color-danger-darker;
          border-color: $color-danger-darker;
        }
      }
    }

    &.dark {
      background: $color-primary-d;
      border-top-color: $color-primary-d !important;
      box-shadow: none !important;
      z-index: 999999991;;

      .content-wrap {
        .btn {
          background: $color-warning-light;
          border-color: $color-warning-light;
          color: $color-primary-d !important;
        }
      }
    }

    &.light-btn {
      .content-wrap {
        .btn {
          background: $color-secondary;
          border-color: $color-secondary;
          color: $color-primary-d !important;
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {

      .content-wrap {
        > i {
          display: none;
        }

        transform: unset;
        padding: 10px 15px;

        article {
          padding-left: 10px;
          margin: 0;
          border: none;

          &.rec-flex {
            flex-direction: column;

            ul {
              margin: 15px 0 !important
            }
          }
        }
      }
    }
  }

  &.closing {
    opacity: 0;
  }


  &.center-content {
    align-items: center;
  }

  &.sticky {
    width: 100%;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 9999;
    margin: 0;
    @include b-radius(0);
    border: none;
    box-shadow: 0px -2px 10px rgba($color-black, 0.15);

    &.alert-box--linked {
      padding-right: 50px;
    }

    &.top {
      top: 0;
      bottom: auto;
    }

    &.bottom {
      top: auto;
      bottom: 0;
    }
  }

  &.responsive {
    @include mediaMaxWidth($screen-phones) {
      flex-direction: column;
      align-items: flex-start;
      .btn-action {
        margin: 10px 0 0 0 !important;
      }
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {

    *:not(.notification-label):not(i):not(.rec-title),
    .btn-action {
      font-size: 14px;
    }
  }

  @include mediaMaxWidth($screen-phablet) {
    article span.sub-title {
      display: block;
      width: 85%;
    }
  }
}

.alert-flat-box {
  display: flex;
  align-items: center;
  color: $color-white;
  background-color: $color-primary;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 25px;
  @include b-radius($b-radius);
  position: relative;
  margin-bottom: 50px;

  @include mediaMaxWidth($screen-phablet) {
    margin-bottom: 20px;
  }

  &__icon-wrap {
    @include flexable(center, center);
    width: 60px;
    flex: 0 0 60px;
    font-size: $text-x-giant;
    margin-left: 30px;

    .icon {
      font-size: $text-x-giant;
      color: $color-white;
    }

    img {
      max-width: 100px;
    }
  }

  &__content {
    flex-grow: 1;
    padding-left: 30px;
  }

  &__title {
    font-size: $base-size;
    margin: 0 0 10px;
    line-height: 1;

    @media (min-width: $screen-tablet-p) {
      &.large {
        font-size: $text-xx-larger;
      }
    }
  }

  &__desc {
    font-size: $text-x-small;
    max-width: 550px;
    margin: 0;
  }

  &__actions {
    flex-grow: 0;
    @include flexable(center, flex-end)
  }

  &__btn {
    padding: 0;
    line-height: 36px;
    color: $color-dark-300;
    background-color: $color-white;
    @include b-radius($b-radius-sm);
    font-size: $text-small;
    display: inline-flex;
    align-items: center;
    transition: 0.3s;

    .text {
      padding: 0 10px;
      font-weight: bold;
    }

    .icon {
      border-right: 1px solid $color-gray-300;
      width: 36px;
      height: 36px;
      font-size: $base-size;
      display: inline-block;
      line-height: inherit;
    }

    &:hover {
      color: $color-primary;
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {
    @include flexable(center, center, column);
    text-align: center;

    &__icon-wrap {
      margin: 0 0 15px;
    }

    &__content {
      padding: 0;
      margin-bottom: 15px;
    }

    &__bg {
      height: 50%;
      top: auto;
      bottom: 0;
    }
  }

  &--primary {
    color: $color-white;
    background-color: $color-primary;
  }

  &--secondary {
    color: $color-white;
    background-color: $color-primary-l;
  }

  &.alert-box--info {
    .alert-flat-box__title {
      font-size: $text-xx-large;
    }
  }

  &.themes-store-banner {
    min-height: 180px;
    background: rgb(45, 74, 120);
    background: linear-gradient(90deg, rgba(45, 74, 120, 1) 0%, rgba(7, 14, 25, 1) 100%);
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      top: 30px;
      left: -370px;
      background: url(../../../../../public/cp/assets/images/apps/themes-banner-bg.png) no-repeat;
      background-size: 130%;
      opacity: 0.3;
      transform: rotate(12deg);
    }

    .alert-flat-box__icon-wrap {
      width: auto;

      img {
        max-width: 170px;
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {

      .alert-flat-box__icon-wrap {
        img {
          max-width: 100px;
        }
      }
    }
  }
}

a.alert-box--white {
  padding-left: 60px;

  &:after {
    content: '\e970';
    font-family: $font-sallaIcon;
    color: $color-gray-400;
    font-size: $text-xxx-large;
    position: absolute;
    left: 17px;
    @include center-v();
    @include transi();
  }
}