.qty-field {
  @include flexable(center, center, row);
  width: fit-content;
  border: 1px solid $color-gray-200;
  height: 36px;
  @include b-radius($b-radius-sm);

  > .btn {
    padding: 0;
    width: 36px;
    height: 34px;
    @include transi();
    position: relative;

    &:after {
      content: '';
      position: absolute;
      top: 5px;
      width: 1px;
      height: 70%;
      background-color: $color-gray-200;
    }

    &:first-of-type {
      &:after {
        left: 0;
      }
    }

    &:last-of-type {
      &:after {
        right: 0;
      }
    }

    i {
      color: $color-dark-100;
      font-size: $text-small;
    }

    &:hover,
    &:active {
      background-color: $color-gray-50 !important
    }
  }

  a.btn {
    i {
      transform: translateY(10px);
    }
  }

  .dropdown-toggle {
    border-left: none;

    span {
      color: $color-dark-300
    }
  }

  .form-group {
    margin: 0 !important;

    .form-control {
      border: none;
      padding: 0;
      max-width: 55px;
      text-align: center;
      font-size: $text-small;
      color: $color-dark-300;
      height: 33px;

      &[disabled] {
        background-color: $color-white !important;
        cursor: text;
      }
    }
  }

  &--single {
    > .btn {
      background-color: rgba($color-secondary, 0.1) !important;

      i {
        display: block;
        color: $color-primary-l;
      }

      &:after {
        height: calc(100% + 2px);
        top: -1px;
        right: -1px !important;
        left: auto;
      }

      &:hover {
        background-color: rgba($color-secondary, 0.4) !important;
      }
    }
  }

  &--infinite {
    > .btn {
      background-color: rgba(92, 213, 196, 0.1) !important;
      pointer-events: none;

      &:after {
        content: none;
      }

      svg {
        fill: $color-primary-l;
      }
    }
  }

  &--wide {
    .form-group {
      .form-control {
        max-width: unset
      }
    }
  }

  &--custom {
    @include flexable();
    height: auto;
    position: relative;
    border: none;

    .form-control {
      min-width: 50px;
      max-width: 100%;
      width: calc(100% - 100px);
      height: 36px;
      text-align: center;
      flex: auto;
      font-weight: bold;
      margin: 0 !important;
      padding-right: 10px;
      padding-left: 10px;
      user-select: initial;
      border-right: none;
      border-left: none;
      @include b-radius(0);

      &:focus, &:active {
        border-color: $color-gray-200;
      }
    }

    .btn {
      &--qty {
        &-add, &-sub {
          height: 36px;
          background: $color-white;
          border: 1px solid $color-gray-200;
          @include transi();

          i {
            display: inline-block;
            vertical-align: middle;
            font-size: $text-xx-small;
            font-weight: bold;
            transform: translateY(-1px);
          }

          &:hover, &:active, &:focus {
            border-color: $color-gray-200;
            background: transparent;
            box-shadow: none;
            color: $color-dark-300 !important;
          }
        }

        &-add {
          @include b-radius(0 $b-radius-sm $b-radius-sm 0);
          border-left: none;

          &:before {
            right: auto !important;
            left: 0
          }
        }

        &-sub {
          @include b-radius($b-radius-sm 0 0 $b-radius-sm);
          border-right: none;
        }
      }

      &:before {
        content: "";
        width: 1px;
        height: 80%;
        background: $color-gray-200;
        position: absolute;
        top: 10%;
        right: 0;
      }
    }

    &:hover {
      .form-control, .btn {
        border-color: darken($color-gray-200, 10%) !important;
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      .btn {
        &--qty {
          &-add, &-sub {
            flex: 0 0 40px;
          }
        }
      }
    }
  }
}
