.products-bulk-pricing {
  .page-content {
    overflow: inherit;
    height: auto;
  }

  @include mediaMaxWidth($screen-tablet-p) {
    .btns-row {
      margin-bottom: 0 !important;
    }
  }
}

.panel--bulk-pricing {
  p {
    font-size: $text-small;
  }
}

.bulk-pricing-filters {
  display: flex;

  .vue-treeselect__control-arrow-container {
    padding-left: 30px;
  }

  > .btn {
    justify-content: center;

    @include mediaMinWidth($screen-tablet-p) {
      align-self: flex-start;
      //flex: 0 0 90px;
    }
  }

  > div {
    flex: 1;
    margin-left: 15px;
  }

  .vue-treeselect {
    .vue-treeselect__list-item .vue-treeselect__option .vue-treeselect__label {
      font-size: $text-x-small;
    }
  }

  .amount-field {
    position: relative;
    margin-bottom: 0;
    transition: 0.3s;

    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Firefox */
    input[type=number] {
      padding: 7px 20px 7px 7px;
      -moz-appearance: textfield;
      width: 100%;

      &:disabled:hover {
        border-color: $color-gray-200;
      }
    }

    .unit {
      position: absolute;
      top: 5px;
      left: 10px;
    }

    &.has-error {
      padding-bottom: 15px;
    }

    .error {
      transition: 0.3s cubic-bezier(0.55, 0, 0.1, 1) 0s;
      opacity: 0;
      transform: translateY(-10px);
      position: absolute;
      color: $color-danger;
      font-size: $text-x-small;
      display: inline-block;
      pointer-events: none;
      padding-top: 3px;
      white-space: nowrap;

      &.is-show {
        transform: translateY(0);
        opacity: 1;
        pointer-events: auto;
      }
    }
  }

  input.form-control {
    border-right: 1px solid $color-gray-200;
    width: 150px;
    padding: 7px 20px;

    &:hover,
    &:focus {
      border-color: $color-primary;
    }
  }

  .short-field {
    max-width: 145px !important;
    @include mediaMaxWidth($screen-laptop) {
      max-width: 100% !important;
    }
  }

  @include mediaMaxWidth($screen-laptop) {
    flex-direction: column;

    > div {
      max-width: none;
      margin-left: 0;

      &:not(:last-child) {
        margin-bottom: 15px;
      }
    }

    input.form-control {
      width: 100%;
    }
  }
}

#bulk-pricing-wrap {
  .bulk-edit-table {

    &:not(.is-empty) {
      border-bottom: 1px solid $color-gray-200;
      @include b-radius(0);
    }

    .vuetable-slot {
      padding: 0 5px;

      &:first-child {
        width: 400px;
        padding: 0 15px;
      }

      &:nth-child(2),
      &:nth-child(3) {
        width: 100px;
      }
    }

    tr > th:last-child,
    tr > td:last-child {
      border-left: 0;
    }

    th:nth-child(n+2) {
      text-align: center;
    }

    tr > td:nth-child(n+3) {
      text-align: center;
    }
  }

  .btn--bulk-save {
    width: 90px;
    justify-content: center;
    text-align: center;
  }
}

.vtable-loading-area {
  @include flexable(center, center);
  height: 135px;
}