.daterangepicker {
  &.rec-daterangepicker {
    // ranges ---
    .ranges {
      min-width: 220px;
      @include b-radius($b-radius-sm);
      margin: 0 0 0;
      border: 1px solid $color-gray-25;
      box-shadow: 0 1px 6px 0 rgba($color-black, 0.04);
      background: $color-white;
      position: absolute;
      top: 0;
      right: 0;

      ul {
        padding: 0;

        li {
          font-family: $font-main;
          font-size: $text-x-small;
          color: $color-dark-200;
          padding: 10px 30px 10px 10px;
          position: relative;
          border-bottom: 1px solid $color-gray-200;
          @include transi();
          margin: 0;

          &:before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 5px;
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background-color: $color-gray-300;
            @include b-radius(50%);
          }

          &.active {
            background: rgba($color-secondary, 0.1);
            color: $color-primary-l;

            &:before {
              background: $color-secondary-50;
            }

            &:hover {
              background: rgba($color-secondary-50, 0.15);
            }
          }

          &:last-child {
            border: none;
          }

          &:hover {
            color: $color-dark-300;
            background-color: $color-gray-25;
          }
        }
      }
    }

    .calendar {
      border: none;
      margin: 0;
      box-shadow: none;
      @include b-radius(0);

      .daterangepicker_input {
        .form-control {
          border: 1px solid $color-gray-200;
        }

        i {
          bottom: 11px;
        }
      }

      .calendar-table {
        thead {
          tr {
            th {
              &.month {
                font-size: $text-small;
              }
            }
          }
        }

        tbody {
          tr {
            td {
              font-size: $text-xxx-small;
              padding: 7px;

              &.active,
              .start-date,
              &.end-date {
                @include b-radius(0);
                color: $color-primary-l !important;
                background-color: $color-secondary-50 !important;
              }

              &.active {
                @include b-radius($b-radius-sm);
              }

              &.start-date {
                @include b-radius(0 $b-radius-sm $b-radius-sm 0);
              }

              &.end-date {
                @include b-radius($b-radius-sm 0 0 $b-radius-sm);
              }

              &.in-range {
                color: $color-primary-l;
                background-color: rgba($color-secondary-50, 0.2);
              }
            }
          }
        }
      }

      &.right {
        float: left;
      }

      &.left {
        float: right;
      }
    }

    &.show-calendar {
      .ranges {
        @include b-radius(0 $b-radius-sm $b-radius-sm 0);
        border-left: none;
        box-shadow: none;
      }

      @include mediaMaxWidth(576px) {
        flex-direction: column-reverse;

        .ranges {
          @include b-radius($b-radius-sm 0 0 $b-radius-sm);
          margin-bottom: 0;
          border-left: 1px solid $color-gray-200;
          border-bottom: none;
        }

        .calendar {
          &.right {
            border: 1px solid $color-gray-200;
            margin: 0;
          }

          &.left {
            margin-top: 0;
            border: 1px solid $color-gray-200;
            border-top: none;
            @include b-radius(0 $b-radius-sm $b-radius-sm 0);
          }
        }
      }
    }

    .range_inputs {
      display: none;
    }

    &.show-calendar {
      padding-right: 220px;
      background: $color-white;
    }

    &:after {
      content: '';
      position: absolute;
      display: inline-block;
      right: 10px;
      top: -20px;
      border: 11px solid;
      border-color: transparent transparent $color-white transparent;
    }

    @include mediaMaxWidth($screen-laptop) {
      .ranges {
        min-width: 130px;
        width: 130px;
      }

      &.show-calendar {
        padding-right: 130px;
      }
    }

    @include mediaMaxWidth($screen-laptop-small) {
      .ranges {
        min-width: 100%;
        width: 100%;
      }

      .calendar {

        &.right,
        &.left {
          float: unset;
        }

        &.left {
          padding-top: 125px;
        }
      }

      &.show-calendar {
        padding: 280px 0 0;
      }
    }
  }

  &--with-dropdown {
    table {
      thead {
        .month {
          padding: 7px 0 !important;
          position: relative;

          &:before, &:after {
            content: '\e96d';
            font-family: $font-sallaIcon;
            color: $color-dark-100;
            font-size: $text-xx-small;
            position: absolute;
            @include centerY
          }

          &:before {
            right: 60px
          }

          &:after {
            left: 7px;
          }

          select {
            background: transparent;
            border-color: $color-gray-200;
            padding: 4px 7px;
            width: 48%;
            @include b-radius(2px);
            -webkit-appearance: none;
            -moz-appearance: none;
            outline: none !important;
            cursor: pointer;
          }

          select::-ms-expand {
            display: none;
          }
        }
      }
    }
  }

  &--top-position {
    @include mediaMaxWidth($screen-phablet) {
      top: auto !important;
      bottom: 40px;
    }
  }
}