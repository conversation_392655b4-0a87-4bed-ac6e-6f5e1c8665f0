.rec-placeholder {
  @include flexable(center, center, column);

  * {
    text-align: center;
  }

  &__icon {
    margin: 0 auto 15px;

    i,
    img,
    svg {
      display: block;
    }

    img, svg {
      max-width: 150px;
      height: auto;
    }

    i {
      font-size: 80px;
      color: $color-gray-300;
    }

    &.small i {
      font-size: 45px;
    }

    // if you have an svg icon ---
    svg {
      path {
        fill: $color-gray-300;
      }
    }

    &.large {
      img {
        max-width: 130px;
      }
    }

    &.largest {
      img {
        max-width: unset;
      }

    }

    &.with-bg {
      i {
        width: 80px;
        height: 80px;
        color: $color-dark-100;
        background-color: rgba(136, 139, 152, .1);
        line-height: 80px;
        font-size: $text-xx-large;
        @include b-radius(50%)
      }
    }
  }

  &__title {
    font-size: $text-xx-medium;
    color: $color-dark-300;
    margin: 0 auto 5px;

    &.medium {
      font-size: $text-x-medium;
    }
  }

  &__desc {
    color: $color-dark-100 !important;

    p {
      margin: 0;
    }

    .btn {
      padding-right: 30px;
      padding-left: 30px;
      margin: 15px auto 0;
      font-size: $base-size;

      i {
        font-size: $text-small;
        position: relative;
        top: -1px;
      }
    }
  }

  // #review ---
  img {
    display: inline-block;
    width: 100%;
    height: auto;
    margin-bottom: 15px;
    opacity: 0.05;

    &.op-1 {
      opacity: 1;
    }

    &.big {
      max-width: 150px;
    }

    &.mw-unset {
      max-width: unset;
    }
  }

  .icon-holder {
    position: relative;
    margin-bottom: 10px;
    line-height: 0;

    i {
      font-size: $text-xx-giant;

      &.sicon-forward {
        transform: rotate(95deg);
        display: inline-block;
      }
    }

    .extra {
      font-size: $text-xx-larger;
      position: absolute;
      @include centerXY;
      font-weight: bold;
    }
  }

  h2 {
    font-size: $text-xx-medium;
    color: $color-dark-300;
    text-align: center;

    &.dark {
      color: $color-dark-300;
    }
  }

  p {
    text-align: center;

    &.narrow-text {
      width: 40%;
    }
  }

  &--danger {
    .icon-holder {
      * {
        color: $color-danger;
      }
    }
  }

  @include mediaMaxWidth($screen-phones) {
    p {
      &.narrow-text {
        width: 100%;
      }
    }
  }

  @include mediaMaxWidth($screen-phablet) {
    &__icon {
      margin: 0 auto 10px;

      img, svg {
        max-width: 80px;
      }

      i {
        font-size: 70px;
      }
    }

    &__title {
      font-size: 18px;
      margin: 0 auto 5px;
    }

    &__desc {
      font-size: $text-x-small;

      .btn {
        padding-right: 20px;
        padding-left: 20px;
      }
    }

    h2 {
      font-size: $text-x-medium;
    }

    p {
      font-size: $text-small;

      &.narrow-width {
        width: 100%;
      }
    }
  }

  &--bg {
    padding: 20px;
    @include b-radius($b-radius-sm);
    background: $color-gray-25;
  }

  &.small {
    i {
      font-size: $text-xx-large;
    }
  }
}

.store-locked-placeholder {
  padding: 15px;
  @include b-radius($b-radius-sm);
  background-color: $color-white;
  align-items: center;
  position: relative;
  margin: 30px auto;

  p {
    font-size: $text-small;
    width: 65%;
  }

  .btn {
    &:after {
      content: '\e943';
      font-family: $font-sallaIcon;
      margin-right: 10px;
      position: relative;
      top: 2px
    }
  }

  img {
    position: absolute;
    top: -15px;
    left: 12px;
    height: 120px;
  }

  @include mediaMaxWidth(370px) {
    flex-direction: column;
    text-align: center;
    &__message {
      order: 2;

      p {
        width: 80%;
        margin: -35px auto 10px;
      }
    }
    img {
      order: 1;
      position: relative;
      top: -40px;
      margin: 0 auto;
    }
  }
}