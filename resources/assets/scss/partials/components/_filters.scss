.rec {
  &-filter-open {
    overflow: hidden;
  }

  // wrapper ---
  &-filter-wrapper {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba($color-primary-l, 0.5);
    z-index: 9999999999;
    overflow: hidden;
    @include transi();
    opacity: 0;
    visibility: hidden;

    &.reveal {
      opacity: 1;
      visibility: visible;

      .rec-filter-cont {
        opacity: 1;
        left: 0;

        &:before,
        &:after {
          display: block;
        }
      }
    }

    &.conceal {
      opacity: 0;
      visibility: hidden;

      .rec-filter-cont {
        opacity: 0;
        left: -280px;

        &:before, &:after {
          display: none;
        }
      }
    }

    .ui.dropdown {
      .ui {
        &.label {
          @include b-radius(50px);
          border: none !important;
          box-shadow: none !important;
          font-size: $text-xxxx-small !important;
          color: $color-dark-100;
          padding: 3px 7px 3px 4px !important;
          position: relative;
          background: $color-gray-100;
          z-index: 5;

          .delete.icon {
            display: inline-block;
            width: 15px;
            height: 15px;
            line-height: 15px;
            @include b-radius(50%);
            font-style: normal !important;
            border: 1px solid $color-danger;
            font-size: $text-xxxx-small;
            margin-right: 5px;

            &:before {
              display: block;
              content: '\ea47';
              font-family: $font-sallaIcon;
              font-size: $text-xxxx-small;
              color: $color-danger;
              transform: translateY(0);
            }
          }
        }
      }

      &.search {
        padding: 8px !important;
        min-height: 34px !important;
        border-color: $color-gray-200;

        > input.search {
          width: 100% !important;
          padding: 4px 25px 4px 0 !important;
          margin: 0 !important;
          font-size: $text-xx-small !important;
          @include b-radius($b-radius-sm !important);
          @include transi();
          background: transparent !important;
        }

        .text {
          &.default {
            padding: 10px 13px 5px 10px !important;
            margin: 0 !important;
            font-size: $text-xx-small !important;
          }
        }

        &.has-value {
          .text.default {
            opacity: 0;
          }
        }
      }
    }
  }


  &-filter-wrapper-products {
    .form-group {
      width: 100%;
    }

    .multiselect {
      height: auto;
      padding: 3px 10px 8px 0;

      input {
        border: none;
        font-size: $text-xx-small;
      }

      ul li {
        margin-bottom: 0 !important;
      }
    }
  }

  &-filter-cont {
    $filter-root: rec-filter;
    width: 300px;
    height: 100%;
    padding: 20px 15px;
    position: absolute;
    top: 0;
    left: -280px;
    background: $color-white;
    box-shadow: 0 0 5px 5px rgba($color-black, 0.1);
    overflow-x: hidden;
    overflow-y: scroll;
    @include transi();
    transition-duration: 0.75s;
    opacity: 0;

    .ui.popup{
      transform: translateX(18%) translateY(-121%) !important;
    }

    #filter_close {
      width: 24px;
      height: 24px;
      @include b-radius(50%);
      @include flexable();
      border: 1px solid $color-danger;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 999;
      @include transi();
      padding: 0;
      line-height: 1;
      background: transparent;

      &:before {
        content: '\ea47';
        display: inline-block;
        font-family: $font-sallaIcon;
        font-size: $text-small;
        color: $color-danger;
      }
    }

    .#{$filter-root} {
      &__section {
        margin: 0 0 15px 0;

        .filter {
          // head --
          &-head {
            @include flexable(center, space-between, row);
            width: 100%;
            min-height: 40px;
            padding: 0;
            margin-bottom: 0;
            text-align: right;
            background-color: $color-white;
            border: none;

            span {
              display: inline-block;
              font-size: $text-small;
              color: $color-dark-300;
              padding: 0 0 0 10px;
              background-color: $color-white;
              z-index: 2;

              i {
                display: inline-block;
                vertical-align: middle;
                margin: 0 0 0 5px;
                opacity: 0.6;
                font-size: 14px;
              }
            }

            &:hover, &:focus, &:active {
              box-shadow: none !important;

            }

            &:before {
              content: '';
              display: block;
              width: calc(100% - 20px);
              height: 1px;
              background: $color-gray-200;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              z-index: 0;
            }

            &:after {
              content: '\ed91';
              font-family: $font-sallaIcon;
              color: $color-dark-100;
              display: inline-block;
              vertical-align: middle;
              @include transi();
              margin-top: 4px;
            }

            &.collapsed {
              span {
                color: var(--color-text);
              }

              &:after {
                content: '\e90c';
              }
            }
          }

          //content --
          &-content {
            padding: 0;

            .form-group {
              margin-bottom: 0;

              label {
                font-size: $text-x-small;
              }
            }

            .rec {
              &-list {
                margin-top: 10px;

                li {
                  width: 100%;
                  margin: 0 0 15px 0;

                  .rec-checkbox {
                    &--default {
                      input[type=checkbox], input[type=radio] {
                        + label {
                          display: inline-flex;
                          justify-content: space-between;
                          width: 100%;
                          cursor: pointer;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }

        &.ex-margin {
          margin-bottom: 35px;
        }

        &:last-child {
          margin: 0;
        }
      }

      &__submit {
        @include flexable(center, space-between, row);

        &.btn {
          padding: 3px 10px 6px;

          &-filter-submit {
            &:hover {
              background-color: $color-primary;
            }
          }

          &-filter-reset {
            border: 1px solid $color-gray-200;

            &:hover {
              border-color: $color-gray-300;
            }
          }
        }

        .btn-filter-submit {
          flex: 1;
          margin-left: 10px;
        }
      }
    }
    &:before, &:after {
      content: '';
      display: none;
      width: 300px;
      height: 20px;
      position: fixed;
      left: 0;
      z-index: 99;
      background: $color-white;
      pointer-events: none;
      @include transi();
      transition-delay: 0.5s;
    }

    &:before {
      top: 0;
      background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
    }

    &:after {
      bottom: 0;
      background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    }
  }

  &-filters {
    .title {
      margin: 0 0 15px 0;
      text-align: right;

      i {
        display: inline-block;
        vertical-align: middle;
        margin: 0 0 0 5px;
        font-size: $base-size;
      }

      &.title--small {
        position: relative;
        font-size: $text-medium;
      }
    }
  }
}
