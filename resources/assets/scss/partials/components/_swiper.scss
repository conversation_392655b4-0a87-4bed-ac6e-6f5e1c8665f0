.swiper {
    --swiper-navigation-size: 13px;
    --swiper-navigation-top-offset: 35%;
    .swiper-button-next,
    .swiper-button-prev {
        display: inline-flex;
        height: 3.5rem;
        width: 3.5rem;
        align-items: center;
        justify-content: center;
        border-radius: 9999px;
        border-width: 1px;
        background-color: rgba(255, 255, 255, 1);
        border-color: rgba(209, 213, 219, 1);
        font-size: 1.25rem;
        line-height: 1.75rem;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
        color: #6b7280;
    }
    .swiper-thumbs {
        margin-top: 15px;
        .swiper-slide {
            height: 133px;
            width: 286px;
            border-radius: 4px;
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            -ms-border-radius: 4px;
            -o-border-radius: 4px;
            cursor: pointer;
            &.swiper-slide-thumb-active{
                border: 2px solid var(--color-secondary);
            }
            img {
                object-fit: cover;
                border-radius: 4px;
                height: 100%;
                width: 100%;
                -webkit-border-radius: 4px;
                -moz-border-radius: 4px;
                -ms-border-radius: 4px;
                -o-border-radius: 4px;
                object-position: center;
            }
        }
    }
}
