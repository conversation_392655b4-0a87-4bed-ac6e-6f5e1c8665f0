.upload-entry {
  @include flexable(flex-start, flex-start, row);
  width: 100%;
  padding: 15px 25px;
  font-size: $text-small;
  border-bottom: 1px solid $color-gray-25;
  opacity: 0.5;

  > * {
    display: inline-block;
    position: relative;
    flex: 0 0 8%;
    padding: 0 10px;
    color: $color-dark-200;
  }

  &__title {
    flex: auto;
    padding: 0 0 3px 0;
    color: $color-dark-300;
  }

  &:after {
    content: '';
    font-family: $font-sallaIcon;
    font-size: $text-xx-medium;
    text-align: center;
    line-height: 1;
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: middle;
    transform: translateY(6px);
    flex: 0 0 auto;
    opacity: 0.7;
  }

  &--success, &--failed, &--progress {
    opacity: 1;
  }

  &--success {
    &:after {
      content: '\ea9b';
      color: $color-primary;
    }
  }

  &--failed {
    &:after {
      content: '\ea45';
      color: $color-danger;
    }
  }

  &--progress {
    &:after {
      background: url("/cp/assets/uploader/img/loading-lg.gif") center no-repeat;
      -webkit-background-size: 100%;
    }
  }

  &:last-child {
    border: none;
  }

  @include mediaMaxWidth($screen-phones) {
    flex-wrap: wrap;
    padding: 15px 20px;
    &__title {
      width: 100%;
      margin-bottom: 15px;
    }
    &__size {
      flex: auto;
      padding-right: 0;
    }
    &:after {
      transform: translateY(3px);
    }
  }
}