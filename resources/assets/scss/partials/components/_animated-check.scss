@use "sass:math";

$spark-width: 25px;
$spark-height: 4px;
$item-count: 20;
$circle-size: 80px;

.animation-ctn {
  width: $circle-size;
  height: $circle-size;
  margin: 0 auto 30px;
  text-align: center;

  .icon {
    &--order-success {
      width: $circle-size;
      height: $circle-size;
      position: relative;

      svg {
        transform: scale(0.55) translateX(45%) translateY(-45%);
      }
    }
  }
}

.icon--order-success {
  svg {
    polyline {
      animation: checkmark 0.25s ease-in-out 0.7s backwards
    }

    circle {
      animation: checkmark-circle 0.6s ease-in-out backwards;

      &#colored {
        animation: colored-circle 0.6s ease-in-out 0.7s backwards;
      }
    }
  }
}

.icon-wrapper {
  .spark {
    @include on-circle($item-count, $circle-size, $spark-width, $spark-height);
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -($circle-size*0.5);
    transform: translateY(-50%);

    span {
      background: $color-primary;
      @include b-radius($spark-height*0.5);
    }
  }

  &.anim {
    .spark {
      $inc: 0;
      $angle: math.div(360, $item-count);
      @for $i from 1 through $item-count {
        span:nth-of-type(#{$i}) {
          animation: spark-animation-#{$i} cubic-bezier(0.075, 0.820, 0.165, 1.000) 3.5s;
        }
        $inc: $inc + $angle;
      }
    }
  }
}


// keyframes ---
$angle: math.div(360, $item-count);
$inc: 0;
@for $i from 1 through $item-count {
  @keyframes spark-animation-#{$i} {
    0% {
      opacity: 1;
      transform: rotate($inc*-1deg) translate($circle-size*0.5) scale(1);
    }
    80% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: rotate($inc*-1deg) translate($circle-size*1.2) scale(0);
    }
  }
  $inc: $inc + $angle;
}

@-webkit-keyframes checkmark {
  0% {
    stroke-dashoffset: 100px
  }

  100% {
    stroke-dashoffset: 200px
  }
}

@-ms-keyframes checkmark {
  0% {
    stroke-dashoffset: 100px
  }

  100% {
    stroke-dashoffset: 200px
  }
}

@keyframes checkmark {
  0% {
    stroke-dashoffset: 100px
  }

  100% {
    stroke-dashoffset: 0px
  }
}

@-webkit-keyframes checkmark-circle {
  0% {
    stroke-dashoffset: 480px

  }

  100% {
    stroke-dashoffset: 960px;

  }
}

@-ms-keyframes checkmark-circle {
  0% {
    stroke-dashoffset: 240px
  }

  100% {
    stroke-dashoffset: 480px
  }
}

@keyframes checkmark-circle {
  0% {
    stroke-dashoffset: 480px
  }

  100% {
    stroke-dashoffset: 960px
  }
}

@keyframes colored-circle {
  0% {
    opacity: 0
  }

  100% {
    opacity: 100
  }
}