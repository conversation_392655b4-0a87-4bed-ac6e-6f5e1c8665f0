.rec {
  &-accordion {
    .rec {
      &-acc__section {
        width: 100%;
        position: relative;

        &__head {
          @include flexable(center, flex-start, row);
          width: 100%;
          position: relative;
          padding: 0;
          margin: 0 0 20px 0;
          cursor: pointer;

          span {
            display: inline-block;
            font-size: $base-size;
            color: $color-dark-300;
            flex: initial;
            padding: 0 0 0 20px;
            z-index: 99;
          }

          &:after {
            content: "";
            display: block;
            width: auto;
            flex: auto;
            height: 1px;
            background: $color-gray-200;
          }
        }

        &__content {
          .rec-acc-content-wrapper {
            padding: 0 20px 20px;

            p {
              font-size: $text-small;
              margin: 0 0 15px 0;
              color: $color-dark-300;
              line-height: 1.6;

              &:last-child {
                margin: 0;
              }
            }

            @include mediaMaxWidth($screen-tablet-l) {
              padding: 0;
            }
          }
        }
      }
    }

    &.has-steps {
      counter-reset: rec-accordion;

      .rec {
        &-acc__section {
          counter-increment: rec-accordion;

          &__head {
            span {
              &:before {
                content: counter(rec-accordion);
                display: inline-block;
                width: 35px;
                height: 35px;
                vertical-align: middle;
                @include b-radius(50%);
                border: 1px solid $color-primary;
                color: $color-primary;
                text-align: center;
                line-height: 32px;
                margin: 0 0 0 15px;
                @include transi();
              }
            }

            &[aria-expanded="true"] {
              span {
                &:before {
                  color: white;
                  border: 1px solid $color-primary;
                  background-color: $color-primary;
                }
              }
            }
          }
        }
      }
    }

    .panel {
      border: none;

      &:not(:last-of-type) {
        margin-bottom: 15px;
      }

      &.has-error {

        .rec-accordion__heading,
        &.rec-accordion__companies {
          border: 1px solid red;
        }
      }
    }
    &.has-error {
      .rec-accordion__heading {
        border: 1px solid red;
      }
    }
    &.custom &__heading {
      padding: 13px 20px 13px 20px;

      &:before {
        content: "\e96d";
        font-family: $font-sallaIcon;
        color: $color-primary;
        font-size: $text-small;
        position: absolute;
        left: 16px !important;
        right: unset;
        top: 12px;
        background-color: transparent;
        border: 1px solid $color-primary;
        @include b-radius(50%);
        width: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
      }
      &.up-down-arrow{
        &:before {
          content: "\e96e" !important;
        }
        &[aria-expanded="true"]{
          &:before {
            content: "\e96d" !important;
          }
        }
      }
      &[aria-expanded="true"] {
        &:not(.dark-heading) {
          background-color: $color-gray-25 !important;
        }

        h4,
        span {
          color: $color-dark-300;
        }

        &:before {
          content: "\e96c";
          color: $color-white;
          background-color: $color-primary;
          border: 1px solid $color-primary;
          @include b-radius(50%);
          width: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 20px;
        }
      }
    }

    &__heading,
    &__companies {
      background-color: $color-gray-25 !important;
      cursor: pointer;
      @include flexable(center, space-between, row);
      @include b-radius($b-radius-sm);
      padding: 10px 55px 10px 20px;

      &:before {
        content: "\e90b";
        font-family: $font-sallaIcon, serif;
        color: $color-dark-300;
        font-size: 18px;
        position: absolute;
        right: 16px;
        top: 12px;
      }

      h4 {
        color: $color-dark-300;
        font-weight: 500;
        font-size: 14px;
        margin: 0;
        display: inline-block;

        &:not(:last-of-type) {
          margin-left: 5px;

          &:after {
            content: " / ";
          }
        }
      }

      span {
        color: $color-dark-100;
        font-size: 14px;
      }

      &.with-icon {
        padding: 10px;

        &:before {
          content: none;
        }

        h4 i {
          color: $color-dark-100;
          margin-left: 10px;
        }

        .arrow-rotate {
          @include transi();
          transform: rotate(0deg);
          font-size: 18px;
          top: 2px !important;
        }
      }

      &[aria-expanded="true"] {
        background-color: $color-secondary-50 !important;
        @include b-radius($b-radius-sm $b-radius-sm 0 0);

        h4,
        i,
        span {
          color: $color-primary-l !important;
        }

        .arrow-rotate {
          transform: rotate(-90deg);
        }

        &:before {
          content: none;
        }
      }
    }

    &__companies {
      background-color: transparent !important;
      border: 1px solid $color-gray-200 !important;

      &[aria-expanded="true"] {
        background-color: $color-gray-200 !important;
        border: 1px solid $color-gray-200 !important;

        h4,
        i,
        span {
          color: $color-dark-300 !important;
        }

        .white-icon {
          color: white !important;
        }

        .arrow-rotate {
          transform: rotate(180deg);
        }
      }
    }

    &.custom &__collapse {
      &:not(.dark-body) {
        &[aria-expanded="true"] {
          border: 1px solid $color-gray-200;
          @include b-radius(0 0 $b-radius-sm $b-radius-sm);
        }
      }
    }

    &__collapse {
      &[aria-expanded="true"] {
        border: 1px solid $color-secondary-50;
        @include b-radius(0 0 $b-radius-sm $b-radius-sm);
        border-top: none;
      }
    }

    &__collapse-companies {
      &[aria-expanded="true"] {
        border: 1px solid $color-gray-200;
        @include b-radius(0 0 $b-radius-sm $b-radius-sm);
        border-top: none;
      }
    }

    &__body {
      padding: 20px 12px;
      border-top: none !important;
    }

    &.custom &__body {
      padding: 20px;
      border-top: none !important;
    }

    .disabled {
      opacity: 0.6;
      pointer-events: none;
    }

    &--enhanced {
      .rec {
        &-acc__section {
          @include b-radius($b-radius-sm);
          @include transi();
          border: 1px solid $color-gray-25;
          background: $color-gray-25;

          &__head {
            @include flexable(center, flex-start, row);
            padding: 15px 20px 17px 40px;
            margin: 0;

            * {
              font-size: 15px;
              @include transi();
            }

            // override ---
            .v-align {
              font-size: 18px;
              color: $color-dark-100;
              transform: translateY(1px);
            }

            &:after {
              content: "\e96e";
              font-family: $font-sallaIcon;
              font-size: 18px;
              color: $color-gray-400;
              height: auto;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 18px;
              background: transparent;
              @include transi();
            }
          }

          &__content {
            display: none;

            .rec-acc-content-wrapper {
              padding: 0 15px 15px;
            }
          }

          &.active {
            background: white;
            border: 1px solid $color-primary;

            .rec-acc__section__head {
              background: $color-primary;

              * {
                color: white;
              }

              .img-placeholder {
                background: white;

                * {
                  color: white;
                }
              }

              &:after {
                transform: translateY(-50%) rotate(-90deg);
                color: white;
              }
            }
          }
        }
      }
    }
  }
}