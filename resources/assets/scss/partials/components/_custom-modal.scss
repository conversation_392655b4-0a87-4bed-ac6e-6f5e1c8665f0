//sharable style
%backgroundAndBorder {
  background-color: rgba($color-primary, 0.1);
  border-color: $color-primary;
  border-style: solid;
}

%productAndFileInput {
  width: 15px;
  height: 15px;
  border: none;
  background-color: $color-gray-300;
  @include b-radius(50%);
  position: absolute;
  @include centerY();
}

.custom-modal {
  position: fixed;
  transition: opacity .15s linear;
  overflow-x: hidden;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  z-index: 999999999999;
  width: 100%;
  background: rgba(26, 45, 76, 0);
  @include topLeftBottomRight(0, 0, 0, 0);

  .modal-dialog {
    transform: translate(0, -800px);
    transition: transform .3s ease-out;
    margin: 1em auto;

  }

  &.fade {
    opacity: 1;
    visibility: visible;
    background: rgba(26, 45, 76, .75);

    .show {
      &.modal-dialog {
        transform: translate(0, 0);
      }
    }
  }


  // custom modal quantity and options
  &#modal_quantity_management , &#modal_digital_card_management {

    .default-option-selector {
      @include flexable();
      width: 41px;
      height: 36px;
      border: 1px solid $color-gray-200;
      border-left: 0;
      background: $color-white;
      @include b-radius(0 $b-radius-sm $b-radius-sm 0);

      &:after {
        content: '';
        width: 1px;
        height: 20px;
        background: $color-gray-200;
        position: relative;
        left: -10px;
        z-index: 1;
      }

      .btn {
        padding: 0;
        width: 20px;
        height: 20px;
        text-align: center;
        background: transparent;
        border: 1px solid $color-gray-300;
        @include b-radius(50%);
        color: $color-white;

        i {
          display: none;
          transform: translateY(-2px);
          font-size: $text-small;
        }

        &.selected {
          color: $color-primary;
          border-color: $color-primary;

          i {
            display: inline-block;
          }
        }
      }
    }

    #options_toggle {
      font-size: $base-size;
      font-weight: bold;
      cursor: pointer;

      .switchery {
        margin-left: 10px;
      }

      &:hover {
        opacity: 0.8;
      }
    }


    .option {
      &-section {
        position: relative;
        padding: 25px 20px 20px;
        border: 1px solid $color-gray-100;
        margin-bottom: 20px;

        &-inner {
          display: flex;

          > .row {
            flex-grow: 1;
            margin-right: -6px;
            margin-left: -6px;

            .col {
              padding-right: 6px;
              padding-left: 6px;
            }
          }
        }

      }
    }

    .btn {
      &-delete {
        &-option {
          background-color: $color-danger;
          color: $color-white;
          box-shadow: none !important;
          @include b-radius($b-radius-sm);
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          margin-right: 12px;

          &:hover {
            background-color: #ed6163;
          }

          .icon {
            font-size: $text-medium;
            margin-right: -0.5px;
          }
        }

        &-value {
          width: 20px;
          height: 20px;
          @include b-radius(50%);
          border: none;
          padding: 0;
          @include flexable(center, center, row);
          color: $color-danger;
          background: $color-white;
          box-shadow: 0 0 0 1px #ffd6d6 !important; //colorAvilabilty
          transition: 0.3s;
          margin: 8px 22px 8px 6px;

          .icon {
            font-size: $text-xx-small;
            margin-right: -0.5px;
          }

          &:hover {
            background-color: #f451540f; // colorAvilabilty
          }
        }
      }

      &-add {
        &-value {
          width: 100%;
          margin: 0 auto;
          display: block;
          @include b-radius($b-radius-sm);
          background: transparent;
          color: $color-primary;
          border: 1px dashed $color-primary;
          transition: 0.3s;
          padding: 7px 10px;

          &:hover {
            @extend %backgroundAndBorder;
          }
        }
      }

      &-option-group {
        border: 1px dashed $color-gray-200;
        padding: 10px;
        background-color: $color-white;

        &:hover {
          color: $color-primary !important;
          @extend %backgroundAndBorder;
        }

        i {
          color: inherit;
        }
      }
    }

    .rec-checkbox {
      margin-bottom: 10px !important;

      label {
        font-size: $base-size;
        font-weight: bold;
      }
    }

    .value-container {
      display: flex;

      .form-group {
        flex-grow: 1;

        &.has-default-option {
          @include flexable(flex-start, flex-start, row);

          .input-group {
            flex: auto;

            .input-group-addon {
              border-right: 0 !important;
              @include b-radius(0);
            }
          }

          &.has-error {
            .default-option-selector {
              border-color: $color-danger;
            }
          }
        }
      }
    }

    p {
      font-size: $text-small;
      color: $color-dark-200
    }

    hr {
      border-top-color: $color-gray-200;
      border-top-width: 2px;
      margin: 30px 10px 20px
    }

    .form-group {
      margin-bottom: 12px;

      .option-type-wrapper {
        position: static;
        height: 33px;
        display: flex;
        align-items: center;
      }
    }

    .type-color,
    .type-image {
      input.form-control {
        border-left-color: transparent;
      }
    }

    .feature-group-addon {
      padding: 0;
    }

    .type-color .feature-group-addon {
      padding: 0 8px;
    }

    .verte__guide {
      width: 18px;
    }

    .product_feature_image_crop {
      @extend %productAndFileInput;
      @include transi();
      left: 34px;
      z-index: 99;

      &::before {
        content: '\eb1c';
        font-family: $font-sallaIcon;
        font-size: $text-xxxx-small;
        color: $color-dark-300;
        display: block;
        position: relative;
        left: 4px;
      }

      &:hover {
        opacity: .7;
      }
    }

    .fileinput-remove {
      @extend %productAndFileInput;
      padding: 1px 0 0 1px;
      left: 9px;
      z-index: 99;

      &:before {
        content: "\ea47";
        font-family: $font-sallaIcon;
        font-size: $text-xxxx-small;
        color: $color-white;
        display: block;
      }

      &:hover {
        background-color: $color-danger;

        i {
          color: $color-white
        }
      }
    }

    .btn-file {
      cursor: pointer;
    }

    .div_product_option_feature {
      &:before {
        display: none;
      }

      &:not(.uploading) .hidden-xs {
        text-align: center;
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      h5 {
        font-size: $text-small;
      }
      .btn-del-option {
        position: relative;
        left: 12px
      }
    }

    @include mediaMaxWidth($screen-phones) {
      .div_product_option_feature_image {
        width: 44px;
      }
    }


    .skus-conatainer {
      position: relative;

      .rec-accordion {
        max-height: 600px;
        overflow-y: auto;
        padding-bottom: 20px;
        padding-left: 3px;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
          background: $color-gray-200;
        }

        &::-webkit-scrollbar-thumb {
          background-color: darkgrey; //coloravilabilty 
          outline: 1px solid slategrey;
        }
      }

      &::after {
        pointer-events: none;
        position: absolute;
        width: calc(100% - 24px);
        height: 30px;
        bottom: 15px;
        right: 10px;
        content: '';
        background: linear-gradient(0deg, rgba(255, 255, 255, 1) 35%, rgba(255, 255, 255, 0) 100%);
      }
    }
  }
}
