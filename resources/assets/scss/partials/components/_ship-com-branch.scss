.companies-container {
  max-height: 270px;
  padding-left: 10px;
}

.ship-com-branch {
  $ship-com-branch-root: &;

  input[type=radio] {
    display: none;
  }

  @include flexable(center, center, row);
  width: 100% !important;
  border-bottom: 1px solid $color-gray-200;
  padding: 10px 0px;
  margin: 0;

  &__wrapper {
    @include flexable(center, flex-start, row);
    padding-right: 35px !important;
    position: relative;
    @include b-radius($b-radius-sm);
    cursor: pointer;

    &:before,
    &:after {
      content: "";
      top: 50%;
      transform: translateY(-50%);
      @include b-radius(50%);
      @include transi();
      display: inline-block;
      vertical-align: middle;
      position: absolute;
    }

    &:before {
      width: 20px;
      height: 20px;
      border: 1px solid $color-primary-l;
      background: $color-white;
      right: 0px;
    }

    &:after {
      opacity: 0;
      width: 12px;
      height: 12px;
      background-color: $color-primary-l;
      right: 4px;
    }
  }

  &__distance {
    min-width: 70px;
    min-height: 60px;
    @include flexable(center, center, column);
    background-color: $color-gray-75;
    @include b-radius($b-radius-lg);
    padding: 10px;

    span {
      font-size: $text-small;
      color: $color-dark-300;
      font-weight: 500;
      line-height: 26px;
    }
  }

  &__content {
    @include flexable(flex-start, center, column);
    margin-right: 15px;

    span {
      font-size: $text-small;
      color: $color-dark-300;
      font-weight: 500;
    }
  }

  &__footer {
    @include flexable(center, flex-end, row);
    margin-right: auto;

    .icon-button {
      width: 38px;
      height: 38px;
      text-align: center;
      line-height: 14px;
      padding: 0;
      margin-right: 10px;
    }
  }

  input:checked+ {
    #{$ship-com-branch-root} {
      &__wrapper {
        &:after {
          opacity: 1;
        }
      }
    }
  }

  &:last-child {
    border: none;
  }

  @include mediaMaxWidth($screen-phablet) {
    align-items: flex-start;
    
    #{$ship-com-branch-root} {
      &__wrapper {
        @include flexable(flex-start, flex-start, column);

        &:before,
        &:after {
          top: 23px;
        }
      }

      &__distance {
        min-width: 76px;
        min-height: 36px;
        @include flexable(center, space-between, row);  
      }

      &__content {
        margin-right: 0;
        margin-top: 5px;
      }
    }
  }
}