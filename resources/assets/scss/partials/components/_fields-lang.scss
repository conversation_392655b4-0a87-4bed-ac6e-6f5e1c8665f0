.rec-ls-field {
  position: relative;

  .rec {
    &-lsc {
      position: absolute;
      top: 2px;
      left: 1px;
      z-index: 9;

      &.visible {
        z-index: 10;

        .rec {
          &-ls-toggle {
            &:after {
              transform: rotate(180deg) translateY(50%);
            }
          }

          &-ls-list {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }

    &-ls-toggle {
      height: 32px;
      padding: 0 15px 0 25px;
      margin: 0;
      color: $color-dark-300;
      border: 0;
      background: transparent;

      &:after {
        content: '\ea62';
        font-family: $font-sallaIcon;
        font-size: $text-xxxx-small;
        line-height: 1;
        color: $color-gray-400;
        display: inline-block;
        vertical-align: middle;
        position: absolute;
        top: 50%;
        transform: translateY(-40%);
        left: 10px;
      }
    }

    &-ls-list {
      min-width: 130px;
      height: auto;
      @include b-radius($b-radius-sm);
      background-color: $color-white;
      box-shadow: 0 1px 4px 0 rgba($color-black, 0.09);
      position: absolute;
      top: 40px;
      left: 1px;
      overflow: hidden;
      opacity: 0;
      visibility: hidden;

      li {
        width: 100%;

        input {
          &:checked {
            + label {
              background: $color-secondary-25;
              span, &:after {
                color: $color-primary-l;
              }
            }
          }
        }

        label {
          display: flex;
          align-items: center;
          padding: 10px 15px !important;
          cursor: pointer;
          width: 100%;

          img {
            margin-left: 10px !important;
            width: 16px;
          }

          span {
            display: inline-block;
            vertical-align: middle;
            font-size: $text-xx-small;
            color: $color-dark-100;
            line-height: 1;

            &:first-child {
              margin: 0 0 0 10px;
            }
          }

          &:hover {
            span {
              color: $color-dark-300;
            }
          }

          &:before,
          &:after {
            display: none;
          }
        }

        &:not(:last-child) label {
          border-bottom: 1px solid $color-gray-25;
        }
      }
    }
  }

  textarea {
    resize: vertical;
  }

  .input-group {
    width: 100%;
    .form-control {
      @include b-radius($b-radius-sm 0 0 $b-radius-sm !important);
    }
  }

  &.rec-ls-ltr {
    > input,
    > textarea {
      direction: ltr;
      text-align: left;
      padding-left: 70px !important;
    }

    .ql-toolbar.ql-snow {
      padding-left: 60px !important;

      .ql-formats {
        float: left;
      }
    }
  }
}

.form-control {
  &.multi-language-input {
    padding-left: 55px;

    &.ltr {
      direction: ltr;
    }
  }
}

form.theme-customize-slogan-form {
  display: flex;
  justify-content: space-between;

  .rec-ls-field {
    flex-shrink: 0;
    flex-grow: 1;
    margin-left: 5px;

    .rec-ls-list {
      left: 0;
    }
  }

  .rec-lsc {
    left: 12px;
  }

  @media (max-width: $screen-laptop) {
    .btn-save {
      width: 80px;
      min-width: auto;
    }
  }

  @media (max-width: 600px) {
    flex-direction: column;

    .rec-ls-field {
      margin: 0 0 5px;
      padding: 0;
    }

    .rec-lsc {
      left: 2px;
    }

    .btn-save {
      width: 100%;
    }
  }
}