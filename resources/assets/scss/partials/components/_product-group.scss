.product-group {
  margin-top: 20px !important;

  &__items {
    padding: 0 15px 10px;
    align-items: center;

    &:not(:last-of-type) {
      border-bottom: 1px solid $color-gray-200;
      margin-bottom: 10px;
    }

    div {
      &:first-of-type {
        flex: 1 0 0;
        @include flexable(flex-start, flex-start, row);
        margin-left: 15px;

        img {
          width: 35px;
          height: 35px;
          margin-right: 15px !important;
          border: 1px solid $color-gray-200;
          @include b-radius($b-radius-sm);
        }

        h6 {
          display: inline-block;
          margin: 0 10px 0 0;
          color: $color-dark-200;
          font-size: $text-xx-small;
        }

        span, &::before {
          color: $color-gray-400;
          font-size: $text-xx-small;
        }

        span {
          position: relative;
          right: -7px;
        }
      }

      &:nth-of-type(2) {
        flex: 0 1 0;
        margin-left: 15px;

        .form-control {
          width: 40px;
          height: 30px;
          @include b-radius(2px);
          padding: 0 10px;
          text-align: center;

          &::-webkit-inner-spin-button,
          &::-webkit-outer-spin-button {
            -webkit-appearance: none;
          }
        }
      }
    }

    .rec-btn {
      padding: 0;
      width: 22px;
      height: 22px;
      position: relative;
      flex-shrink: 0;

      i {
        position: absolute;
        @include centerXY();
        font-size: 12px;
      }
    }

    @include mediaMaxWidth(576px) {
      div {
        &:first-of-type {
          flex: auto
        }

        &:nth-of-type(2) {
          flex: initial;
          margin-left: 20px;
        }
      }
    }
  }

  &--darker-text {
    .product-group__items {
      div {
        &:first-of-type {
          span, &::before {
            color: $color-dark-300;
          }
        }
      }
    }
  }

  &--order-list {
    margin: 0 !important;

    .product-group__items {
      border: none;
      padding: 0;

      div {
        &:first-of-type {
          flex: auto;
          padding-left: 10px;

          img {
            width: 22px !important;
            height: 22px !important;
            margin-right: 12px !important
          }

          h6 {
            color: $color-dark-200
          }
        }
      }
    }
  }

  &.small {
    div {
      font-size: 14px;
    }
  }

  &.flat {
    .product-group__items {
      padding-right: 0;
      padding-left: 0;
    }
  }
}

.order-product-group-wrapper {
  counter-reset: product-group-counter;
  position: relative;
  padding: 10px 70px 0 0;
  margin: 10px 0;

  &::before {
    content: '';
    position: absolute;
    height: calc(100% - 10px);
    width: 1px;
    background-color: $color-gray-200;
    top: 10px;
    right: 28px;
  }

  .order-product-group-entry {
    &__title {
      @include flexable(center, flex-start, row);
      gap: 10px;
      margin: 0 0 10px 0;
      &:before {
        counter-increment: product-group-counter;
        content: counter(product-group-counter);
        @include flexable();
        width: 24px;
        height: 24px;
        @include b-radius(50%);
        font-size: $text-xx-small;
        color: $color-dark-200;
        line-height: 1;
        background: $color-gray-25;
        border: 1px solid $color-gray-200;
      }
    }
  }

  &--edit {
    margin-top: 0;
    padding-right: 126px;

    &:before {
      right: 75px
    }
  }

  > ul {
    max-width: 600px;
  }

  @include mediaMaxWidth($screen-phones) {
    padding: 10px 45px 0 0;
  }
}
