%remain-steps {
  &:before {
    content: "\eb20";
    font-family: $font-sallaIcon;
    font-weight: 700;
  }

  &:after {
    background-color: $color-gray-200;
  }
}

.step-container {
  @include flexable(center, center, column);
  position: relative;
  color: $color-dark-300;
  font-size: $text-medium;

  &.step-badge {
    padding: 5px;
    margin: 5px;
    @include b-radius($b-radius-sm);
    cursor: pointer;
    font-size: $text-xx-small;
    color: $color-green;
    background-color: $color-light;
  }
  .dimmed {
    color: rgba(74, 158, 138, 0.50);
    background-color: rgba(186, 243, 230, 0.60);
  }
}

.multi-steps {
  display: table;
  table-layout: fixed;
  width: 100%;

  >li {
    text-align: center;
    display: table-cell;
    position: relative;
    color: $color-secondary-50;

    &:before {
      content: "\eb20";
      font-family: $font-sallaIcon;
      display: block;
      margin: 0 auto 4px;
      background-color: $color-white;
      width: 22px;
      height: 22px;
      line-height: 20px;
      text-align: center;
      font-weight: bold;
      border: 1px solid $color-secondary-50;
      border-radius: 50%;
    }

    &:after {
      content: "";
      height: 10px;
      width: calc(100% - 20px);
      background-color: $color-secondary-50;
      position: absolute;
      top: 6px;
      right: 53%;
      z-index: 1;
    }
    @include mediaMaxWidth($screen-tablet-p) {
      &:after {
         right: 54%;
      }
    }
    @include mediaMaxWidth($screen-phones) {
      &:after {
        right: 59%;
      }
    }
    @include mediaMaxWidth($screen-phablet) {
      &:after {
        right: 58%;
      }
    }
    @include mediaMaxWidth(400px) {
      &:after {
        right:60%;
      }
    }
    @include mediaMaxWidth($screen-mobile) {
      &:after {
        right: 63%;
      }
    }

        
    &:last-child {
      &:after {
        display: none;
      }
    }

    &.is-active {
      @extend %remain-steps;

      &:before {
        background-color: $color-white;
        border-color: $color-secondary-50;
      }

      ~li {
        color: $color-gray-200;
        @extend %remain-steps;

        &:before {
          background-color: $color-white;
          border-color: $color-gray-200;
        }

        .step-container {
          &.step-badge {
            color: $color-white;
            background-color: $color-gray-400;
          }
        }
      }
    }
  }
}

.commertial-img-container{
  display: flex;
  width: 112px;
  height: 112px;
  justify-content: center;
  align-items: center;
  margin-left: 20px;
  position: relative;
  cursor: pointer;

  &:before {
    content: "";
    font-family: $font-sallaIcon;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: $b-radius;
    background-color: rgba(0, 0, 0, 0.5);
  }
}
.play-button-icon {
  @include flexable(center, center);
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: $color-secondary-50;
  color: $color-primary-l;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid $color-secondary-50;
}
.commertial-img {
  display: flex;
  border-radius: $b-radius;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.ship-price-badge{
  background-color: $color-gray-100;
  border: 1px solid $color-gray-300;
  color: $color-primary-l;
}