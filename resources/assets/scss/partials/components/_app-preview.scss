.app-preview-builder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; 
}

.app-preview {
  $app-preview-root: &;
  // fiexed width and height for the app preview
  width: 260px;
  height: 526px;
  background: $color-white;
  border: 10px solid $color-dark-300;
  @include b-radius(40px);
  position: relative;
  margin: 0 auto 45px;
  box-shadow: 0 0 20px rgba($color-black, 0.08);


  &__wrapper {
    @include flexable(flex-start, flex-start, column);
    width: 100%;
    height: 100%;
    @include b-radius(30px);
    overflow: hidden;

    .notch {
      $corner-size: 7;
      width: 120px;
      height: 15px;
      position: absolute;
      top: 0;
      left: 50%;
      background-color: $color-dark-300;
      @include b-radius(0 0 $b-radius $b-radius);
      transform: translateX(-50%);
      z-index: 999;

      &:before,
      &:after {
        content: '';
        width: $corner-size * 2px;
        height: $corner-size * 1px;
        position: absolute;
        top: -1px;
        left: -$corner-size * 1px;
        background-size: 50% 100%;
        background-repeat: no-repeat;
        background-image: radial-gradient(circle at 0 100%,
            transparent $corner-size - 1px,
            $color-dark-300 $corner-size * 1px);
      }

      &::after {
        left: 100%;
        margin-right: $corner-size px;
        background-image: radial-gradient(circle at 100% 100%,
            transparent $corner-size - 1px,
            $color-dark-300 $corner-size * 1px);
      }
    }

    .controls {
      position: absolute;
      width: 100%;
      top: 0;

      &:before, &:after {
        content: '';
        display: block;
        width: 3px;
        position: absolute;
        top: 100px;
      }

      &:before {
        height: 40px;
        background: $color-dark-300;
        right: -14px;
        @include b-radius(0 $b-radius-sm $b-radius-sm 0);
      }

      &:after {
        height: 80px;
        background: $color-dark-300;
        left: -14px;
        @include b-radius($b-radius-sm 0 0 $b-radius-sm);
      }
    }

    .navbar-preview {
      @include b-radius(30px 30px 0 0);
    }

    .tabs-preview {
			@include b-radius(0 0 30px 30px);
      position: relative;
      align-items: flex-start;
      &-item {
        &.custom {
          padding: 9px 0 7px;
          border-radius: 3px;      
        }
       span {
        &.bottom_dash {
          width: 100%;
          height: 1px;
        }
        &.bottom_dot {
          width: 4px;
          height: 4px;
          @include b-radius(50%);
        }
        &.top_dash {
          width: 38px;
          height: 3px;
          position: absolute;
          top: 0;
          margin-top: 0 !important;
        }
       }
      }
		}
  }

  &__body {
    background: $color-white;
    flex: 1;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    @include b-radius(30px);
    @include scrollBar(0px, $color-gray-200, $color-gray-300);

    .rec-list.rec-list--vertical {
      li {
        width: 100%;
        overflow: hidden;
      }
    }

    .builder-empty {
      height: 100%;

      * {
        text-align: center;
      }

      svg {
        display: block;
        width: 100px;
        height: 100px;
        margin-bottom: 20px;
        opacity: 0.1;
      }

      h2 {
        font-family: 'DINNextLTArabic-Regular';
        font-size: $text-medium;
        color: $color-dark-200;
        margin: 0 0 5px 0;
      }
    }

    .preview-wrapper {
      display: grid;
      margin-bottom: 10px;

      >div {
        position: relative;

        img {
          display: block;
          width: 100%;
        }
      }

      // slider ---
      &.single-col {
        grid-template-columns: 1fr;

        >div {
          margin-bottom: 10px;
        }
      }

      &.double-col {
        grid-gap: 10px;
        grid-template-columns: 1fr 1fr;
        padding: 10px;

        &.no-gap {
          grid-gap: 0;
        }
      }

      &.products {
        >div {
          @include b-radius($b-radius-sm);
          border: 1px solid $color-gray-200;
        }

        &.slider {
          grid-auto-flow: column;
          grid-template-columns: 103px 103px 103px;
          margin-right: -45px;
        }
      }

      &.square-images {
        padding: 0;

        img {

          // temp -- will handle better later ---
          &.single {
            transform: scaleX(2.08) translateX(50%);
            transform-origin: right;
          }
        }
      }

      &.categories {
        grid-template-columns: 1fr;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &.added-section {
        padding: 10px
      }
    }

    .carousel-review {
      img {
        width: 100%;
      }
    }

    .products-review {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      padding: 20px 10px;
      grid-gap: 10px;

      img {
        width: 100%;
      }
    }

    .testimonial-review {
      >div {
        &:first-of-type {
          @include flexable(center, center, row);
        }

        &.dots {
          position: relative;
          top: -20px;
          @include flexable(center, center, row);

          span {
            height: 3px;
            width: 3px;
            @include b-radius(50%);
            margin-right: 2px;
            background-color: $color-gray-200;

            &:first-of-type {
              width: 6px;
              background-color: $color-gray-300;
            }
          }
        }
      }
    }

    .categories-review {
      @include flexable(center, space-between, row);
      padding: 10px;

      img {
        flex: 0 0 47%
      }
    }

    .app-menu {
      background: url('/vendor/store_app/image/app_builder/menu_preview.svg') center no-repeat;
      width: 100%;
      height: 120%;
      background-size: contain;
      background-position: top;
    }
  }

  &__categories-preview {
    height: 100%;
  }
  
  #app-splash {
    width: 90%;
  }

  .storeapp-fieldset {
    width: 100%;
    height: 100%;
    margin-bottom: 0;
    padding: 30px 8px 40px;
    @include flexable(center, center, column);

    .form-group {
      position: absolute;
      width: 85%;
      bottom: 30px;
      @include flexable(center, space-between, row);
      background: $color-gray-25;
      padding: 7px 8px;
      @include b-radius($b-radius-sm);

      label {
        font-size: $text-xxx-small;
        margin-bottom: 0;
      }
    }

    .app-motion {
      width: 100%;
      @include flexable(center, center, column);

      .review {
        position: absolute;
        background-color: $color-primary-l;
        border: none;
        cursor: pointer;
        height: 40px;
        width: 40px;
        @include b-radius(50%);
        line-height: 45px;

        i {
          font-size: $text-xx-large !important;
          color: $color-white;
        }
      }
    }

    .img-placeholder {
      background-color: $color-gray-25;
      border: none;
    }

    .img-overlay {
      position: absolute;
      top: 0;
      left: 0;
      max-width: 100%;
      pointer-events: none;
      max-height: 150px;
      width: 100%;
      object-fit: contain;
    }
  }

  // if needed later ---
  &--ios {}

  &--android {}

  &:before {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    @include b-radius(50px);
    background: $color-dark-300;
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 99;
  }


  &:after {
    content: '';
    display: block;
    width: 100%;
    height: 40px;
    position: absolute;
    bottom: -50px;
    left: 0;
    right: 0;
    background: url("/vendor/store_app/image/app_builder/oval-shadow.png") center no-repeat;
    background-size: contain;
  }

  @include mediaMaxWidth($screen-desktop-small) {
    width: 230px;
    height: 465px;
  }

  &__half {
    height: 219px;
    border-bottom: none;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    box-shadow: none;
    margin: 0px;

    #{$app-preview-root} {
      &__wrapper {
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
      }
    }

    &__body {
      background-image: linear-gradient(to bottom right, $color-bright-cyan, $color-dark-purple);
      flex: 1;
      width: 100%;
      padding-top: 30px;

      .preview-block {
        margin: 30px 15px 0;
        background: rgba(255, 255, 255, 0.8);
        @include b-radius($b-radius);
        padding: 6px 12px 8px;

        small {
          font-size: $text-xxxx-small;
          color: $color-dark-250;
          direction: ltr;
        }

        &__header {
          @include flexable(center, space-between, row);

          >div {
            @include flexable(center, center, row);

            .logo {
              width: 20px;
              padding: 3px;
              background-color: $color-white;
              @include b-radius($b-radius-sm);
              @include flexable(center, center, row);
              margin-left: 5px;

              img {
                width: 100%;
              }
            }
          }
        }

        &__body {
          margin-top: 8px;
          @include flexable(center, flex-start, row);

          span {
            font-size: $text-xxxx-small;
            color: $color-dark-400;
            margin-right: 5px;
            display: block;

            &.title {
              font-weight: bold;
              font-size: $text-small;
            }

            &.sub-title {
              font-size: $text-xxxx-small;
              color: $color-dark-250;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .logo {
            min-width: 40px;
            max-width: 40px;
            margin-left: 10px;
            @include b-radius($b-radius-sm);
            @include flexable(center, center, row);
            background-color: $color-white;

            >img {
              width: 100%;
              object-fit: cover;
              @include b-radius($b-radius-sm);
            }
          }
        }
      }
    }
  }

  &__half#{$app-preview-root} {

    &:before,
    &:after {
      content: none !important
    }
  }
}