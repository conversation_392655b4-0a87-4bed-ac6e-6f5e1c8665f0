.vue-treeselect--custom-wrapper {
  .vue-treeselect {
    &--custom {
      img {
        display: none;
      }

      .vue-treeselect__placeholder,
      .vue-treeselect__single-value {
        right: 33px;
      }

      .vue-treeselect__control {
        position: relative;
        z-index: 99;
      }

      .vue-treeselect__input-container {
        position: absolute;
        top: 37px;
        right: 30px;
      }

      .vue-treeselect__value-container {
        position: relative;
        padding-right: 38px;
        overflow: visible;
      }

      // $item vue-treeselect__list-item
      .vue-treeselect__list-item {
        .vue-treeselect__option {
          cursor: pointer;

          .vue-treeselect__label {
            padding-top: 10px !important;
            padding-bottom: 10px !important;

            &:before {
              display: none;
            }
          }

          &--selected {
            background-color: #fcfcfc;
            position: relative;

            &:before {
              content: '\ea9d';
              display: inline-block;
              height: 1px;
              position: absolute;
              left: 12px;
              top: 50%;
              font-family: sallaicons !important;
              color: #004956;
              font-size: 14px;
              line-height: 0;
            }
          }

          &--highlight {
            background-color: #fcfcfc;
          }
        }

        &:has(.vue-treeselect__list) {
          >.vue-treeselect__option {
            border-bottom: 1px solid #eeeeee;
          }
        }

        .vue-treeselect__option-arrow-container {
          display: table-cell;
          float: left;
          top: 25px;

          svg {
            display: none;
          }

          position: relative;

          &:before {
            content: '\e90c';
            display: inline-block;
            height: 1px;
            position: absolute;
            left: 12px;
            top: 50%;
            font-family: sallaicons !important;
            color: #999999;
            font-size: 14px;
          }

          &:has(.vue-treeselect__option-arrow--rotated) {
            &::before {
              content: '\ed91';
              color: #004956;
            }
          }
        }

        &.vue-treeselect__indent-level-0 {
          >.vue-treeselect__option {
            .vue-treeselect__label {
              font-weight: 500;
            }
          }
        }
      }

      .vue-treeselect__value-container {
        &:before {
          content: '\edf6';
          display: inline-block;
          height: 1px;
          position: absolute;
          right: 12px;
          top: 50%;
          font-family: sallaicons !important;
          color: #bbbbbb;
          font-size: 14px;
          line-height: 0;
        }
      }

      .vue-treeselect__menu-container {
        position: relative !important;
        z-index: 9 !important;

        // vue-treeselect __ Menu
        .vue-treeselect__menu {
          position: relative !important;
          max-height: auto;
          overflow: visible;

          .input-search-container {
            position: sticky;
            background-color: #fff;
            z-index: 1;
            top: 1px;

            >input {
              padding-right: 38px;
              background-color: #fff;
            }

            &:before {
              content: '\ef09';
              display: inline-block;
              height: 1px;
              position: absolute;
              right: 15px;
              top: 50%;
              font-family: sallaicons !important;
              color: #444444;
              font-size: 16px;
              line-height: 0;
            }
          }

          >.vue-treeselect__list {
            position: relative;
            overflow: auto;
            max-height: 240px;

            // scrollbar-width: thin;
            &::-webkit-scrollbar {
              width: 3px;
              background: #eeeeee;
            }

            &::-webkit-scrollbar-track {
              box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            }

            &::-webkit-scrollbar-thumb {
              background-color: #004d5a;
              border-radius: 30px;
            }
          }

        }
      }
    }

    &.showPlaceholder {
      .vue-treeselect-helper-hide {
        display: block !important;
      }
    }

    &.treeselect-invalid {
      .vue-treeselect__control {
        border: 1px solid #f55157 !important;
        border-radius: 4px;
        margin-bottom: 1px;
      }
    }
  }
}