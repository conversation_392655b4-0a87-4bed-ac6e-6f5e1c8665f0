.slider {
  &--has-controls {
    .owl-dots {
      text-align: center;

      .owl-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        @include b-radius(50%);
        @include transi();
        background: $color-gray-200;
        margin: 0 4px;

        &:hover, &.active {
          background-color: $color-primary;
        }

        &:first-child {
          margin-right: 0;
        }

        &:last-child {
          margin-left: 0;
        }
      }
    }

    .owl-nav {
      width: 100%;

      > div {
        width: 25px;
        height: 25px;
        line-height: 25px;
        text-align: center;
        @include b-radius(50%);
        @include transi();

        &:before {
          font-family: $font-sallaIcon;
          font-size: $text-large;
          color: $color-gray-100;
        }

        &.owl-prev {
          &:before {
            content: '\ea68';
          }
        }

        &.owl-next {
          &:before {
            content: '\ea65';
          }
        }

        &.disabled {
          opacity: 0.3;
        }
      }
    }
  }
}

.owl-carousel {
  &--fade-left {
    position: relative;

    &:after {
      pointer-events: none;
      position: absolute;
      content: "";
      left: 0;
      top: 0;
      bottom: 0;
      width: 100px;
      background-image: -webkit-gradient(linear, right top, left top, from($color-white), to(rgba(248, 248, 248, 0)));
      background-image: linear-gradient(to right, $color-gray-25, rgba(248, 248, 248, 0));
      z-index: 5;
    }

    @include mediaMaxWidth($screen-phablet) {
      &:after {
        width: 75px;
      }
    }
    @include mediaMaxWidth($screen-phones) {
      &:after {
        //display: none;
      }
    }
  }
}

.slick-slider {
  $slider-root: &;
  @extend .center-loader;

  > * {
    display: none;
  }

  &--fade-left {
    position: relative;

    .slick-list {

      &:before,
      &:after {
        pointer-events: none;
        position: absolute;
        content: "";
        top: 0;
        bottom: 0;
        width: 30px;
        z-index: 5;
      }

      &:before {
        left: 0;
        background-image: -webkit-gradient(linear, right top, left top, from($color-white), to(rgba(248, 248, 248, 0)));
        background-image: linear-gradient(to right, $color-gray-25, rgba(248, 248, 248, 0));
      }

      &:after {
        right: 0;
        background-image: -webkit-gradient(linear, left top, right top, from($color-white), to(rgba(248, 248, 248, 0)));
        background-image: linear-gradient(to left, $color-gray-25, rgba(248, 248, 248, 0));
      }
    }
  }

  &.slick-initialized {
    &:before, &:after {
      display: none;
    }

    > * {
      display: block;
    }
  }
}