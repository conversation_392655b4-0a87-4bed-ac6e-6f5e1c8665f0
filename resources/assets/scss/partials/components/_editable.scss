.vertical-products {
  .editable-container {
    .popover-title {
      color: $color-dark-300;
      font-size: $text-x-small;
      padding-top: 5px;
    }

    .popover-content {
      position: relative;
      padding: 5px 10px 10px;

      .editable-buttons {
        position: absolute;
        left: 0;
        top: 2px;

        .btn {
          background-color: transparent;
          border: none;
          padding: 6px 0 5px 7px;

          i {
            &:before {
              font-family: $font-sallaIcon;
              font-size: $text-medium;
            }
          }

          &:first-of-type {
            i {
              &:before {
                content: '\ea9d';
                color: $color-primary
              }
            }
          }

          &:last-of-type {
            i {
              &:before {
                content: '\ea47';
                color: $color-danger
              }
            }
          }
        }
      }

      .editable-clear-x {
        display: none !important
      }
    }
  }
}

.editable {
  font-family: $font-main;
  font-weight: 500;
  font-size: $base-size;
  text-align: center;

  + .popover {
    min-width: unset;
    @include b-radius($b-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.07);

    &.editable-container.editable-popup {
      padding: 0;

      .popover-title {
        font-family: $font-main;
        font-size: $text-x-small;
        color: $color-dark-300;
      }

      .popover-content {
        padding: 10px;

        .form-inline.editableform {
          .form-control {
            width: 100px;
            height: 34px;
            min-width: unset;
            min-height: unset;
            padding-right: 10px !important;
            padding-left: 30px;

            &.input-mini {
              width: 65px;
            }
          }

          .editable-clear-x {
            &:after {
              content: '\ea45';
              font-family: $font-sallaIcon;
              font-size: $text-small;
              color: $color-danger;
            }
          }

          .btn.btn-sm {
            .glyphicon {
              transform: translateY(-3px);

              &:before {
                font-family: $font-sallaIcon;
                font-size: $text-small;
              }

              &.glyphicon-ok {
                &:before {
                  content: '\ea9d';
                }
              }

              &.glyphicon-remove {
                &:before {
                  content: '\ea47';
                }
              }
            }
          }
        }

        .editableform-loading {
          margin: 0 auto;
          text-align: center;

          &:after {
            content: '';
            width: 16px;
            height: 16px;
            text-align: center;
            line-height: 1;
            display: inline-block;
            padding: 0;
            @include b-radius(100%);
            border: 2px solid;
            border-top-color: $color-primary;
            border-bottom-color: $color-gray-200;
            border-left-color: $color-primary;
            border-right-color: $color-gray-200;
            margin: auto 0;
          }
        }
      }
    }
  }
}
