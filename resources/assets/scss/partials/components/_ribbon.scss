.has-ribbon {
  position: relative;
  overflow: hidden;
  &:before {
    content: attr(data-ribbon-title);
    display: block;
    width: 110px;
    height: 30px;
    font-weight: bold;
    position: absolute;
    top: 10px;
    left: -30px;
    font-family: $font-main, sans-serif;
    font-size: $text-x-small;
    color: $color-brown-dark;
    background: $color-warning-light;
    z-index: 4;
    text-align: center;
    transform: rotate(-45deg);
    line-height: 30px;
  }
  &.padded {
    padding-right: 50px;
  }

  &.plus, &.team {
    &:before {
      color: $color-white;
    }
  }

  &.plus {
    &:before {
      background: $color-plan-plus;
    }
  }

  &.team {
    &:before {
      background: $color-plan-team;
    }
  }

  &.danger {
    &:before {
      color: $color-white;
      background: $color-danger;
    }
  }

  &--darker {
    &:before {
      background: $color-warning;
      color: $color-white;
    }
  }

  &--with-icon {
    &:after {
      content: '\e957';
      font-family: 'sallaicons';
      position: absolute;
      color: $color-white;
      font-size: 15px;
      top: 9px;
      left: 52px;
      z-index: 5;
      transform: rotate(-45deg);
    }
  }

  &--lg {
    font-family: $font-main, sans-serif;
    &:before {
      width: 170px;
      height: 35px;
      font-size: $text-small;
      font-weight: 500;
      top: 26px;
      left: -50px;
      padding-top: 8px;
      line-height: 20px;
    }
  }

  &--left {
    &:before {
      transform: none;
      left: 0;
      @include b-radius(0 $b-radius-sm $b-radius-sm 0);
    }
  }
}