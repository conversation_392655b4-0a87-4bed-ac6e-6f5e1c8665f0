.reports-pagination-wrapper {
  text-align: left;
  @include mediaMaxWidth($screen-phones) {
    text-align: center;
  }
}

.rec {
  &-pagination {
    padding: 10px 15px;
    font-size: $text-x-small;

    .page-item {
      a {
        &.page-link {
          padding-top: 5px;
          padding-bottom: 9px;

          &:hover {
            color: $color-white;
            background-color: $color-primary;
            border-color: $color-primary;
          }
        }
      }

      span {
        &.page-link {
          padding-top: 5px;
          padding-bottom: 9px;
        }
      }
    }

    .page-label {
      pointer-events: none;

      span {
        color: $color-dark-200;

        b {
          color: $color-primary;
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      padding: 8px;
      font-size: $text-xxxx-small;
    }
    // modified-pagination due-to DOM difference ---
    &--modified {
      a {
        min-height: 35px;
        color: $color-dark-300;
        padding: 8px 12px;
        @include flexable();
        margin-left: -1px;
        line-height: 1;
        border: 1px solid $color-gray-200;
        @include b-radius(0);

        i {
          font-size: inherit;
        }

        &:first-child {
          @include b-radius(0 4px 4px 0)
        }

        &:last-child {
          @include b-radius(4px 0 0 4px)
        }

        &:hover {
          background-color: $color-gray-25;
        }

        &.disabled {
          color: $color-dark-100;
          pointer-events: none;
          opacity: 0.8;
        }

        &.active {
          color: $color-primary;

          &:hover {
            background-color: rgba($color-primary, 0.1);
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      padding: 10px;
      font-size: 12px;
    }
  }
}
