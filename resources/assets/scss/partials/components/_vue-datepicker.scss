.mx {

  // input field ---
  &-datepicker {
    width: 100% !important;

    .mx-input-wrapper {
      .form-control {
        padding-right: 40px !important;
      }

      .mx-icon-calendar {
        right: 12px;

        svg {
          display: none;
        }

        &:before {
          content: '\ea37';
          font-family: sallaicons;
          font-size: 17px;
          font-style: normal;
          color: $color-gray-300;
        }
      }
    }

    .mx-time-content {
      .mx-scrollbar-track {
        display: none;
      }

      .mx-time-list {
        &:after {
          content: none;
        }
      }
    }
  }

  &--right-position {
    .mx-datepicker-popup {
      left: auto !important;
      right: 0;
      top: 36px !important;
    }
  }

  // calendar popup ---
  &-datepicker-popup {
    border: 1px solid $color-gray-300 !important;
    @include b-radius($b-radius-sm);
    box-shadow: 0 1px 3px rgba(0, 0, 0, .1) !important;

    .mx-calendar {
      padding: 8px;

      .mx-calendar-header {
        .mx-calendar-header-label {
          .mx-btn {
            font-size: 14px !important;
            color: $color-dark-300 !important;
            padding: 0 3px;

            &[class*="month"] {
              transform: translateY(-2px);
            }
          }
        }

        .mx-btn {
          min-width: 30px;
          height: 100%;
          text-align: center;

          &-icon-left {
            float: right;
            transform: scaleX(-1);
          }

          &-icon-right {
            float: left;
            transform: scaleX(-1);
          }

          &[class*="double"] {
            i {
              &:after {
                display: none;
              }
            }
          }
        }
      }

      &:not([class*="month"]):not([class*="year"]) {
        .mx-btn {
          &[class*="double"] {
            display: none;
          }
        }
      }

      .mx-table {
        th {
          text-align: center;
        }

        .cell {
          padding: 5px 0 !important;

          div {
            font-size: 13px;
          }

          &:hover {
            color: $color-primary;
            background: rgba($color-primary, 0.1);
          }

          &.active {
            color: white;
            background: $color-primary;

            &:hover {
              color: white;
              background: $color-primary;
            }
          }

          &.today {
            color: $color-primary;
            background: rgba($color-primary, 0.1);
          }
        }
      }
    }
  }
}


// Vuepic Date Picker

:root {
  --dp-font-family:'PingARLT'!important ;
  --dp-font-size: 12px !important;
  --dp-preview-font-size: 12px !important;
  --dp-input-padding: 12px 7px;
  --dp-action-buttons-padding:3px 12px;
  --dp-action-button-height:max-content;
  //coman color
  --color-primary-l:#004D5A;
  --color-secondary-50: #BAF3E6;
  --color-secondary-25:#CFF7EE;
  --color-white:#fff;
  --color-gray-300: #dddddd;
  --color-gray-200: #eeeeee;
  --color-gray-400:#bbbb;
  --color-gray-500:#cccc;
  --color-danger: #f55157;
  --color-dark-100:#999;
  --color-dark-200:#666;
  --color-dark-300:#444;
}
.dp{
  &__theme_light{
    --dp-background-color: var(--color-white) !important;
    --dp-primary-color:var(--color-primary-l) !important;
  }
  &__main{
    .dp__input_wrap{
      input{
        padding:8px 30px 7px 12px;
      }
      .dp{
        &__input{
          border:1px solid var(--color-gray-200) ;
          &:hover{
            border-color: var(--color-gray-200) ;
          }
        }
        &__input_icon{
          left: unset;
          right:9px;
          color:#d5d5d5;
        }
        &__clear_icon{
          right:unset;
          left:9px;
          color:var(--color-danger);
        }
      }
    }
  }
  &__outer_menu_wrap{
    direction:ltr;
    .dp {
      &__action_cancel {
        background-color: var(--color-gray-300);
        color:var(--color-dark-200);
        border: 1px solid var(--color-gray-300);
      }
      &__action_select {
        background-color: var(--color-secondary-50);
        color: var(--color-primary-l);
      }
    }
  }
}
