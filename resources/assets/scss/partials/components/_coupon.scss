#coupon_url_count {
  margin-bottom: 25px;

  .te-divider-wrapper {
    &::before, &::after {
      background-color: $color-gray-200;
    }

    &-title {
      font-size: $text-small;
      color: $color-dark-400;
    }
  }

  ul {
    padding: 0 10px;
    border: 1px solid $color-gray-200;
    position: relative;
    @include b-radius($b-radius-sm);

    li {
      @include flexable(center, space-between, row);
      padding: 10px 0;
      width: 100%;

      &:not(:last-child) {
        border-bottom: 1px solid $color-gray-200;
      }

      > span {
        font-size: $text-x-small;

        &.share-link {
          flex: 0 0 auto;
          color: $color-dark-400;
          margin-left: 10px;

          &::before {
            font-family: $font-sallaIcon !important;
            padding-left: 7px;
          }

          &-marketing {
            &::before {
              content: '\f078'
            }
          }

          &-coupon {
            &::before {
              content: '\f010'
            }
          }

          &-clients {
            &::before {
              content: '\f079'
            }
          }
        }

        &.link {
          flex: auto;
          unicode-bidi: plaintext;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          position: relative;
          color: $color-gray-400;

          &:after {
            content: '';
            display: block;
            width: 80px;
            height: 100%;
            position: absolute;
            top: 0;
            right: 0;
            pointer-events: none;
            background: $color-white;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
          }
        }
      }

      .controls-wrapper {
        @include flexable(center, center, row);
        margin: 0 10px 0 0;

        .btn {
          background-color: $color-white;
          color: $color-dark-300;
          border-color: $color-gray-200;
          font-size: $text-xx-small;
          padding: 3px 10px;
          @include b-radius($b-radius-sm);

          &:not(:last-of-type) {
            margin: 0 0 0 10px;
          }

          &-show-link {
            position: relative;
          }

          &-copy {
            &::before {
              content: '\efd3';
              font-family: $font-sallaIcon !important;
              padding-left: 7px;
            }
          }

          &--tooltip-toggle {
            .tooltip {
              width: 150px;
              text-align: left;
              cursor: text;
            }
          }
        }
      }
    }
  }

  @include mediaMaxWidth(576px) {
    ul {
      li {
        > span {
          font-size: $text-xx-small;

          &.share-link {
            margin: 0;
            flex: auto;
          }

          &.link {
            display: none !important;
          }
        }

        .btn {
          font-size: $text-xxx-small !important;
        }
      }
    }
  }
}

.coupon-share {
  &__message {
    background-color: $color-gray-25;
    border: 1px solid $color-gray-100;
    @include b-radius($b-radius-sm);
    padding: 20px;
  }

  .btn {
    margin-top: 15px;
    line-height: 1;

    &::before {
      content: '\efd3';
      font-family: $font-sallaIcon;
      margin-left: 12px;
    }

    &.copied {
      background: darken($color-primary, 10%) !important;
    }
  }
}

// coupon list entry ---
// coupon status color palettes ---
.coupon-cell {
  .badge {
    @include mediaMaxWidth($screen-phones) {
      margin: 10px 0 0 4px !important;
    }
  }

}

.load-data-coupon.entry, .coupon-guide {
  white-space: initial;
  display: inline-block;

  &:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    width: 15px;
    height: 15px;
    @include b-radius($b-radius-sm);
    background-color: $color-gray-300;
    margin-left: 10px;
  }

  &[data-coupon-status="active"] {
    &:before {
      background-color: $color-coupon-active !important;
    }
  }

  &[data-coupon-status="cancelled"] {
    &:before {
      background-color: $color-coupon-cancelled !important;
    }
  }

  &[data-coupon-status="overdue"] {
    &:before {
      background-color: $color-coupon-overdue !important;
    }
  }

}

.load-data-coupon {
  @include mediaMaxWidth($screen-phones) {
    display: block !important;
    &:before {
      margin-left: 10px;
    }
  }
}

.coupon-guide {
  display: inline-block;
  margin: 0 0 0 10px;

  &:before {
    width: 16px;
    height: 8px;
    margin-left: 5px !important;
  }
}
.coupon_search_input{
  border: 1px solid #76e8cd!important;
border-radius: 50px!important;
color: #004d5a;
font-size: 12px;
margin-left:5px;
height: 24px;
padding: 0 10px 0 30px!important;
transition: all .35s cubic-bezier(.2,1,.3,1);
width: 120px;
}
