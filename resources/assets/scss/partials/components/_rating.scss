.rec {
  &-star-rate {
    i {
      display: inline-block;
      font-size: $text-medium;
      color: $color-gray-200 !important;

      &.active {
        color: $color-gold !important;
      }
    }

    span {
      display: block;
      margin-top: 5px;
      color: $color-dark-100;
      font-size: 12px;
    }

    &--detail {
      line-height: 1;
    }
  }
}

.rating-area {
  .icon {
    color: $color-gray-300;
    transition: 0.3s;
    font-size: $text-larger;
  }

  .star {
    background: none;
    border: none;
    font-size: $text-medium;
    line-height: 1;
    padding: 0 5px;
    cursor: pointer;

    &.selected {
      .icon {
        color: $color-gold;
      }
    }
  }

  .screen-reader {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    white-space: nowrap;
    width: 1px;
  }
}

.rating-trigger {
  .icon {
    color: $color-gold
  }

  a {
    font-size: $text-small;
  }

  .rating-value {
    font-weight: bold;
    font-size: $base-size;
    color: $color-dark-200;
    line-height: 1;
    margin: 0 5px;
  }

  .rating-count {
    font-size: $text-x-small;
    color: $color-dark-100;
  }
}

.rating-stars {
  display: flex;
  align-items: center;
  flex-direction: row;

  i {
    font-size: $base-size;
    margin-left: 2px;
  }
}

.rating-block {
  gap: 100px;
  padding: 20px;

  @media screen and (max-width: 1024px) {
    gap: 30px;
    padding: 20px 10px;
    flex-direction: column;
  }

  &__details {
    .details-item {
      gap: 15px;

      >span {
        width: 30px;
      }
    }
  }

  .progress-bg {
    background-color: $color-gray-25;
    @include b-radius($b-radius);
    height: 12px;
    width: 100%;
    position: relative;
    margin-bottom: -3px;

    >span {
      position: absolute;
      height: 100%;
      max-width: 100%;
      @include b-radius(50px);
      background-color: $color-warning-light;
    }
  }
}

.review-avatar {
  width: 50px;
  height: 50px;
  position: relative;

  &:before {
    content: '';
    display: block;
    height: 100%;
    background-image: url(https://cdn.salla.network/images/avatar.png);
    background-size: cover;
    border-radius: 50%;
    background-repeat: no-repeat;
    background-position-x: -2px;
  }
}

.rating-comment {
  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    margin-right: 10px;
    width: 100%;

    @media(max-width: 992px) {
      flex-direction: column;
      align-items: flex-start;
      margin-right: 0;
    }
  }

  &__actions {
    display: flex;
    gap: 10px;
    margin-right: 20px;
    margin-top: 10px;

    @media(max-width: 992px) {
      margin: 0 0 20px;
    }
  }

  &__wrapper {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    border-radius: 0.5rem;

    @media screen and (max-width: 480px) {
      flex-direction: column;
      gap: 5px;
    }
  }
}