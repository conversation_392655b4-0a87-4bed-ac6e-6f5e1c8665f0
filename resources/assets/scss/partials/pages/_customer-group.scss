.client-group {
  margin: 5px 0 0 0;

  small {
    @include flexable(center, flex-start, row);
    max-width: 300px;
    position: relative;
    padding: 4px 8px;
    @include b-radius($b-radius-sm);
    font-size: 75%;
    white-space: normal;
    border: 1px solid $color-gray-200;
    word-break: break-word;

    i {
      display: inline-block;
      position: relative;
      font-size: $base-size;
    }
  }

  .store-setup-row {
    &.clients-gp {
      padding: 0;

      @include mediaMinWidth($screen-laptop-small) {
        grid-template-columns: repeat(5, 1fr);
      }

      @include mediaMinWidth($screen-desktop-small) {
        grid-template-columns: repeat(7, 1fr);
      }
    }
  }

  // apply to filter toggler ---
  #button_remove_search {
    padding-top: 8px;
  }

  .customer-conditions-wrapper {
    margin-bottom: 20px;
    flex-wrap: unset;

    .customer-condition {
      flex: auto;
      flex-wrap: unset;

      > li {

        flex: auto;

        &:not(:last-of-type) {
          margin: 0 0 0 15px;
        }

        &.statement {
          flex: 0 0 15%;
        }

        &.condition-value {
          flex: 0 0 30%;
        }

        &.seconde-col {
          flex: 0 0 40%;
        }

      }
    }

    .btn {
      flex-shrink: 0;
      transform: translateY(6px);
    }


    @include mediaMaxWidth($screen-phablet) {
      background: $color-gray-25;
      padding: 10px 10px 0;

      @include b-radius($b-radius-sm);
      align-items: flex-start;

      .customer-condition {
        flex-wrap: wrap;
        align-items: flex-start;

        > li {
          flex: 0 0 calc(50% - 5px) !important;
          margin-bottom: 10px;

          &:not(:last-of-type) {
            margin: 0 0 10px 10px;
          }

          &:first-of-type,
          &:last-of-type {
            margin-left: 0;
          }

          &:first-of-type {
            flex: 0 0 100% !important;
          }

          &.seconde-col {
            flex: 0 0 100% !important
          }
        }

        &.in-between {
          justify-content: space-between;

          li {
            flex: 0 0 100% !important;

            &.highest-value,
            &.lowest-value {
              flex: 0 0 48% !important;
            }
          }
        }
      }

      .btn {
        transform: translateY(6px);
      }
    }
  }
}