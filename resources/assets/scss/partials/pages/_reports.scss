.rec {
  &-reports {
    margin-bottom: 20px;

    .auto-scroll-x {
      overflow-x: auto;
    }

    .nav {
      &-inline {
        white-space: nowrap;

        li {
          display: inline-block;
        }

        @include mediaMaxWidth($screen-phones) {
          display: none;
        }
      }
    }

    #report_print,
    #report_export {
      @include b-radius($b-radius-sm);
      padding: 10px 15px;

      &.report-print-mobile,
      &.report-export-mobile {
        display: none;
        min-height: unset;
        padding: 4px 15px;
        border: none;
      }

      @include mediaMaxWidth($screen-phones) {
        &:not(.report-print-mobile):not(.report-export-mobile) {
          display: none;
        }
        &.report-print-mobile, &.report-export-mobile {
          display: block;
          position: absolute;
          top: 5px;
          left: 0;
          font-size: $text-x-small;

          i {
            font-size: $text-small;
          }
        }
        &.report-export-mobile {
          left: 75px;
        }
      }
    }

    .report-range-wrapper {
      position: relative;
      align-items: center;
      margin-bottom: 20px;
      @include mediaMaxWidth($screen-phones) {
        margin: 0 0 0 -15px;
      }
    }

    &__date {
      @include flexable(center, space-between, row);
      width: fit-content;
      position: relative;
      @include b-radius($b-radius-sm);
      padding: 10px 15px 10px 45px;
      background-color: $color-white;
      border: 1px solid $color-gray-200;

      h2 {
        font-size: $base-size;
        color: $color-dark-300;
        line-height: 1;
        margin: 0 0 0 15px;
        padding: 0 0 0 15px;
        border-left: 1px solid $color-gray-200;

        &:before {
          content: '\ea2d';
          font-family: $font-sallaIcon;
          font-size: $text-large;
          display: inline-block;
          vertical-align: middle;
          margin: 0 0 0 10px;
        }
      }

      span {
        font-size: $text-x-small;
        color: $color-dark-100;
        white-space: normal;
        text-align: right;
      }

      &:after {
        content: '\e96e';
        font-family: $font-sallaIcon;
        color: $color-gray-400;
        font-size: $text-xx-medium;
        position: absolute;
        top: 55%;
        transform: translateY(-50%);
        left: 10px;
      }
    }

    &__options {
      @include b-radius($b-radius);
      background-color: $color-white;
      border: 1px solid $color-gray-200;
      padding: 20px 30px;
      color: $color-dark-300;


      .report-types {
        position: relative;
        margin-bottom: 30px;

        // add loader before showing reports---
        @extend .center-loader;

        .report-type {
          display: none;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: auto;
          background: transparent;
          border: 1px solid $color-gray-200;
          padding: 10px;
          cursor: pointer;
          @include b-radius($b-radius-sm);
          @include transi();
          font-size: $text-small;
          color: $color-dark-300;
          font-weight: 500;
          white-space: nowrap;

          &:not(:last-of-type) {
            margin-left: 10px;
          }

          i {
            display: inline-block;
            margin-left: 8px;
          }

          &.active,
          &:hover {
            color: $color-primary-l;
            border-color: $color-secondary;
          }

          @include mediaMaxWidth($screen-phones) {
            font-size: $text-small;
            padding: 8px 5px 10px;
            i {
              margin-left: 7px;
            }
          }
        }

        .slick-arrow {
          position: absolute;
          top: -60px;
          left: 27px;
          background-color: transparent;
          color: transparent;
          border: none;
          width: 10px;
          height: 0;

          &:before {
            font-family: $font-sallaIcon;
            color: $color-gray-400;
            font-size: $text-xx-larger;
            position: absolute;
            @include transi();
          }

          &.slick {
            &-prev {
              left: 57px;

              &::before {
                content: '\e96c'
              }
            }

            &-next {
              &::before {
                content: '\e970'
              }
            }
          }
        }

        .slick-dots {
          @include strip-ul();
          margin-top: 20px;
          @include flexable(center, center, row);

          li {
            &:not(:last-of-type) {
              margin-left: 10px;
            }

            button {
              width: 10px;
              height: 10px;
              background-color: hsla(0, 0%, 84.7%, .45);
              @include b-radius(50%);
              padding: 0;
              border: none;
              color: transparent;
              @include transi();

              &:hover {
                background-color: $color-gray-300;
              }
            }

            &.slick-active {
              button {
                background-color: $color-secondary-d;
              }
            }
          }
        }

        &.slick-initialized {
          &:before, &:after {
            display: none;
          }

          .report-type {
            display: flex;
          }
        }

        @include mediaMaxWidth($screen-phones) {
          padding: 0 25px;
          margin-bottom: 0;
          .slick-arrow {
            top: -3px;

            &.slick {
              &-prev {
                left: auto;
                right: -17px;
              }

              &-next {
                left: 20px;
              }
            }
          }
        }
      }

      .type-options {
        .form-group {
          margin-bottom: 10px;
        }

        .bootstrap-select {
          select.bootstrap-select {
            right: 0;
            pointer-events: none;
          }
        }

        .bootstrap-select > .dropdown-toggle {
          padding: 3px 15px 3px 10px;
        }

        .row {
          margin-left: -5px;
          margin-right: -5px;

          [class*='col-'] {
            padding-right: 5px;
            padding-left: 5px;
          }
        }

        #orders {
          display: none;
        }

        .bootstrap-select {
          width: 100% !important;

          button {
            padding: 7px 15px 7px 10px !important
          }
        }
      }

      .sub-report-label {
        margin: 0 0 8px 0 !important;
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {
      &__options {
        padding: 20px 20px;

        .rec-title-block {
          margin-bottom: 30px;
        }
      }
    }
    @include mediaMaxWidth($screen-phones) {
      &__date, &__options {
        width: calc(100% + 30px);
        margin-right: -15px;
      }
      &__options {
        padding: 15px;
      }
      &__date {
        margin-bottom: 0;
        border-bottom: none;
      }
    }
  }
}

#reports_content {
  .panel {
    margin-bottom: 20px;
  }
}

.logo-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background-image: var(--logoUrl);
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
}