.salla-policies-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.salla-policies-companies.grid-block {
  grid-template-columns: repeat(auto-fit, 70px) !important;
  justify-content: flex-end;
  align-items: center;
}

.supported-companies {
  flex: 1;
  padding-right: 20px;
}

.company-label {
  width: 68.869px;
  height: 68.869px;
  background-color: white;
  padding: 7px;
  margin-bottom: 10px;
  display: flex;
  border: 1px solid;
  border-color: $color-gray-200;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
}

.company-label img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 5px;
  object-fit: cover;
}

.rounded-label {
  display: inline-block;
  padding-right: 20px !important;
  padding-left: 20px !important;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  border-radius: 50px !important;
  background-color: $color-gray-300 !important;
}

.shipping-companies-list .card .card__header img {
  max-width: 55px;
}

.shipping-companies-list .card {
  height: 50px;
}

@media (max-width: 1200px) {
  .salla-policies-container {
    flex-direction: column-reverse;
  }

  .shipping-companies-list .card {
    height: 65px;
  }

  .supported-companies {
    width: 100%;
    padding-right: 0;
  }

  .salla-policies-companies.grid-block {
    justify-content: center;
  }
}

.company-special-price-badge {
  position: absolute;
  top: -29px;
  right: -58px;
  color: white;
  padding: 5px 32px;
  transform: rotate(45deg);
  transform-origin: 0 0;
  z-index: 10;
  font-size: 10px;
}

.company-details-container{
  padding: 10px;
  @include b-radius($b-radius);
  background-color: $color-white;
  border: 1px solid $color-gray-50;
  padding: 5px;
  @include flexable(center, center);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.company-details-logo {
  width: 52px;
  height: 52px;
  @include b-radius($b-radius);
  object-fit: contain;
  background-repeat: no-repeat;
}

.rec-responsive-list {
  @include flexable(center, flex-start, row);
}

.rec-list-margined-small {
  margin-right: 10px;
  margin-left: 10px;
}

#feedback_popup {
  position: fixed;
  bottom: 90px;
  left: 20px;
  z-index: *********;
  width: 400px;
  max-width: 100%;
  max-height: 845px;
  @include b-radius($b-radius);
  background-color: $color-white;
  border: 1px solid $color-gray-50;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  #feedback_popup_title {
    width: 100%;
    font-weight: 500;
    font-size: 16px;
    i {
      font-weight: 400;
      font-size: 22px;
      &.sicon-add {
        color: #76E8CD !important;
      }
    }
  }
  #feedback_popup_content {
    width: 100%;
  }
  #feedback_popup_subtitle {
    font-size: 12px;
    font-weight: 500;
  }
  #feedback_popup_companies {
    width: 100%;
    max-height: 610px;
    overflow-y: auto;
    padding-left: 20px;
    &::-webkit-scrollbar {
      width: 3px;
    }
    &::-webkit-scrollbar-track {
      border-radius: 0;
      background: #eee;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 0px;
      background: #000;
    }
    .company-row-wrapper {
      width: 100%;
      padding: 20px 0;
      border-bottom: 1px solid #f8f8f8;
      &:last-of-type {
        border-bottom: none;
      }
      .company-row {
        width: 100%;
        .logo {
          padding: 10px;
          border-radius: 8px;
          background-color: #fff;
          border: 1px solid $color-gray-200;
          padding: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: row;
          img {
            width: 40px;
            height: 40px;
            max-width: none;
            object-fit: contain;
          }
        }
        .company-name {
          font-weight: 500;
          font-size: 14px;
        }
        .shipments-count-wrapper {
          font-size: 12px;
          font-weight: 400;
          color: $color-dark-200;
          .shipments-count-value {
            font-weight: 500;
            color: $color-dark-300;
          }
        }
        .feedback-btn {
          height: 34px;
          &.active {
            background-color: var(--02--Secondary-secondary-400, #BAF3E6)
          }
          &.inactive {
            border: 1px solid var(--02--Secondary-secondary-500, #96edd9)
          }
          .feedback-value {
            font-weight: 500;
            font-size: 14px;
          }
        }
      }
      .feedback-reasons {
        padding-top: 20px;
        .title {
          font-weight: 500;
        }
        .reason-row {
          padding: 12px 0;
          .checker {
            margin-left: 8px;
          }
        }
        .other-reasons {
          .title {
            padding: 8px 0;
          }
          .textarea-wrapper {
            position: relative;
            i {
              position: absolute;
              right: 10px;
              top: 10px;
              color: $color-dark-100;
            }
            textarea {
              width: 100%;
              height: 100px;
              padding-right: 30px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}


@include mediaMaxWidth ($screen-phones) {
  .rec-responsive-list {
    @include flexable(flex-start, center, column);
  }

  .rec-list-small {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .rec-list-margined-small {
    margin-right: 0;
    margin-left: 0;
  }

  #feedback_popup_wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: *********9;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    #feedback_popup {
      width: 100%;
      bottom: 0;
      left: 0;
      right: 0;
      border-radius: 24px;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      #feedback_popup_companies {
        max-height: 450px;
      }
    }
    &.minimized {
      background: none;
      #feedback_popup {
        bottom: 90px;
        left: auto;
        right: auto;
        @include b-radius($b-radius);
        max-width: 95%;
      }
    }
  }
}