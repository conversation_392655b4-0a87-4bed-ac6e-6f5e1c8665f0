.store-brands-listing {
  .store-setup-item-cont {
    > * {
      &.store-setup-item {
        padding: 2rem;

        .component-image {
          max-height: unset;
          margin-bottom: 15px !important;

          img {
            display: block;
            width: 100px;
            height: 60px;
            margin: 0 auto 5px;
            object-fit: contain;
          }
        }
      }

      &.store-setup-details {
        justify-content: space-between !important;
      }
    }
  }
}

