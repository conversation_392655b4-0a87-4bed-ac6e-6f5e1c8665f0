//sharable styles
%defaultAndFormEdit {
  @include flexable(center, space-between, row);
  width: 100%;
  direction: ltr;
  @include b-radius($b-radius-sm);
  border: 1px solid $color-gray-200;
}

%textH1AndH2 {
  font-family: $font-main;
  font-size: $text-large;
  margin: 0;
}


.rec { // @osama - needs revisit ---
  &-domain {
    // show and edit default domain ---
    &--default {
      @extend %defaultAndFormEdit;
      max-width: 600px;
      position: relative;
      margin: 0 auto;
      direction: ltr;
      padding: 10px 5px 10px 40px;
      @include b-radius($b-radius);
      border: 1px solid $color-gray-200;

      h1 {
        @extend %textH1AndH2;
        color: $color-primary-l;
      }

      li {
        .btn {
          direction: rtl;
          border: none;
          font-size: $text-xx-small;
          color: $color-dark-100;
          background: transparent;
          padding: 4px 10px 5px;
          opacity: 0.85;
          @include b-radius(0);

          i {
            font-size: $text-small;
            margin-left: 5px;
          }

          &:hover {
            opacity: 1;
          }

          &.copied {
            color: $color-primary;

            i {
              color: $color-primary;

              &:before {
                content: '\ea9d';
              }
            }
          }
        }

        &:first-child {
          .btn {
            border-right: 1px solid $color-gray-200;
          }
        }
      }

      @include mediaMaxWidth($screen-tablet-l) {
        justify-content: space-between;
        align-items: center;
        padding: 0;
        h1 {
          font-size: $text-medium;
          margin-left: 30px;
        }
        li {
          .btn {
            @include b-radius($b-radius-sm);
            border: 1px solid $color-gray-200;
            margin: 0 0 0 6px;
            padding: 1px 8px 4px;

            i {
              font-size: $text-xx-small;
              margin: 0;
            }

            span {
              display: none;
            }
          }
        }
      }

      &.active {
        max-width: unset;
        margin: 0 0 20px 0;
        background-color: $color-white;

        h1 {
          color: $color-dark-300;
          font-size: $base-size;
          text-decoration: underline;
        }

        @include mediaMaxWidth($screen-tablet-l) {
          padding: 6px 8px 8px 8px;
          margin: 0 0 10px 0;
        }
      }

      &:before {
        content: '\f0c3';
        font-family: $font-sallaIcon;
        font-size: $text-medium;
        line-height: 1;
        display: inline-block;
        margin: 0 10px 0 0;
        position: absolute;
        @include center-v();
        left: 12px;
      }
    }
  }
}

// edit domain form ---
#form_edit_domain {
  @include flexable(center, flex-start, row);
  width: 100%;
  direction: ltr;
  padding: 5px 5px 5px 15px;
  @include b-radius($b-radius-sm);
  border: 1px solid $color-gray-200;
  margin: 0;

  .form-group {
    @include flexable(center, flex-start, row);
    flex: auto;
    margin: 0;

    h2 {
      font-family: $font-main;
      font-size: 20px;
      color: $color-primary;
      margin: 0;
    }

    .form-control {
      flex: auto;
      padding: 0 10px 8px;
      border: none;
      font-size: 20px;
      color: $color-dark-200;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    flex-direction: column;
    padding: 0;
    border: none;
    .form-group {
      width: 100%;
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm);
      padding: 2px 10px;
      margin: 0 0 10px 0;

      h2 {
        font-size: 16px;
      }

      .form-control {
        font-size: 16px;
        padding-bottom: 6px;
      }
    }
    .btn {
      width: 100%;
    }
  }
}

#domain_link_options {
  justify-content: center;

  input[type="radio"] {
    + label {
      min-width: 170px;
      @include b-radius(2px);
    }
  }

  @include mediaMaxWidth($screen-phones) {
    flex-direction: column;
    li {
      width: 100%;
      margin: 0 0 5px 0;

      &:last-child {
        margin: 0;
      }
    }
  }
}

.domain_link_wrapper {
  width: 100%;
  @include mediaMaxWidth(374px) {
    .form-control, .input-group-btn {
      display: block;
      width: 100%;
      height: 36px;
      float: unset;
      margin: 0 0 10px 0;

      button {
        width: 100%;
        height: 36px;
        border-right: 1px solid $color-gray-200 !important;
      }
    }
  }
}

.domain_search_field {
  width: 75%;
  min-height: 40px;
  padding: 0 20px 2px;
  margin: 0 auto 20px;
  @include b-radius($b-radius-sm);
  border-right: 1px solid $color-gray-200;

  &:focus, &:active {
    border-color: $color-gray-300;
  }
}

.rec {
  &-domain-placeholder {
    padding: 30px;

    > * {
      text-align: center !important;
    }

    img {
      width: 80px;
      max-width: unset;
    }

    @include mediaMaxWidth($screen-phones) {
      h2 {
        font-size: 20px;
      }
      padding: 10px;
    }

    &.small {
      padding: 0;

      img {
        width: 50px;
        margin-bottom: 5px;
      }

      i {
        margin: 0 0 10px 0;
      }

      h2 {
        font-size: 15px;
        margin: 0 0 15px 0;
      }
    }
  }
}

#form_add_dns, #dns_values_list {
  direction: ltr;
  text-align: left;

  * {
    direction: ltr;
    text-align: left;
  }
}


#form_add_dns {
  [class*='col-'] {
    float: left;
  }

  .form-group {

    label {
      display: block;
      min-height: 23px;

      &.hidden-md {
        @include mediaMaxWidth(1024px) {
          display: none;
        }
      }
    }

    .form-control,
    #form_add_dns_submit {
      min-height: 40px;
    }


    .form-control {
      border: 2px solid $color-gray-200;
      padding: 5px 12px 5px;
    }

    .bootstrap-select {
      width: 100%;

      .dropdown-toggle {
        min-height: 40px;
        border: 2px solid $color-gray-200;
        padding: 5px 30px 5px 12px;

        .bs-caret {
          .caret {
            left: unset;
            right: 12px;
          }
        }
      }
    }

    #form_add_dns_submit {
      width: 100%;
      text-align: center;
    }

    &.rec-input {
      &-type {
        flex: 0 0 150px;
      }

      &-name, &-content {
        flex: auto;
      }

      &-submit {
        flex: 0 0 100px;
      }
    }
  }
}

#dns_values_list {
  td {
    font-size: 13px;
    color: $color-dark-50;
  }

  .btn {
    &.btn-delete {
      width: 20px;
      height: 20px;
      @include b-radius(50%);
      line-height: 12px;
      color: white;
      text-align: center;
      float: right;
      background: rgba($color-danger, 0.9);
      padding: 0;

      i {
        font-size: 13px;
      }

      &:hover {
        background: rgba($color-danger, 1);
      }
    }
  }
}

.rec {
  &-list {
    &--domains {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 20px;
      @include strip-ul();

      > li {
        position: relative;
        height: 100%;

        .rec-checkbox {
          width: 100%;
          height: 100%;
          position: relative;
          margin: 0;
          padding: 15px;
          background: $color-white;
          @include b-radius($b-radius);
          border: 1px solid $color-gray-200;
          @include transi();

          &.active {
            border-color: $color-primary-l;
          }

          input {
            display: none;

            + label {
              width: 100%;
              min-height: 30px;
              padding: 0;
              margin: 0 0 20px 0;
              cursor: pointer;
              color: $color-dark-400;
              font-size: $text-small;

              > span {
                @include flexable(center, space-between, row);
                position: relative;
                padding: 2px 45px 0 0;

                small {
                  font-size: $text-xxx-small;
                  padding: 3px 10px;
                  @include b-radius(50px);
                  border: 1px solid $color-gray-200;

                  b {
                    color: $color-black;
                  }

                  a {
                    display: inline-block;
                    vertical-align: middle;
                    margin: 0 10px 0 0;
                    text-decoration: underline;
                  }
                }

                &:before {
                  content: '\ea9d';
                  font-family: $font-sallaIcon;
                  font-size: 15px;
                  color: transparent;
                  text-align: center;
                  line-height: 30px;
                  display: inline-block;
                  width: 30px;
                  height: 30px;
                  @include b-radius(50%);
                  @include transi();
                  position: absolute;
                  top: 0;
                  right: 0;
                  border: 1px solid $color-primary-l;
                }

                &:hover {
                  &:before {
                    box-shadow: 0 0 10px inset rgba(black, 0.15);
                  }
                }
              }

              &:before,
              &:after {
                display: none;
              }
            }

            &:checked {
              + label {

                > span {
                  &:before {
                    color: $color-primary-l;
                    border-color: $color-primary-l;
                  }
                }
              }
            }
          }

          .domain-meta {
            li {
              display: inline-flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              padding: 0 0 10px 0;
              margin: 0 0 10px 0;
              border-bottom: 1px solid $color-gray-25;

              span {
                a {
                  color: $color-danger;
                  text-decoration: underline;
                }

                &:first-child {
                  font-size: $text-small;
                  color: $color-dark-200;
                }

                &:last-child {
                  font-size: $text-x-small;
                  color: $color-dark-300;
                }
              }

              &.domain-meta-note {
                margin: 0;
                padding: 0;
                justify-content: flex-end;
                border: none;

                span {
                  font-size: $text-xx-small;
                  color: $color-gray-400;
                }
              }

              &:last-child {
                padding: 0;
                margin: 0;
                border: none;
              }
            }
          }

          .btn {
            i {
              display: inline-block;
              vertical-align: middle;
              margin: 0 0 0 5px;
              font-size: $text-x-small;
            }
          }
        }
      }

      @include mediaMaxWidth($screen-tablet-l) {
        grid-template-columns: 1fr;
        > li {
          .rec-checkbox {
            input {
              + label {
                margin-bottom: 10px;

                > span {
                  padding: 0 30px 0 0;

                  &:before {
                    top: 2px;
                    font-size: $text-xxx-small;
                    line-height: 20px;
                    width: 20px;
                    height: 20px;
                  }

                }

                .domain-meta {
                  li {
                    padding: 0 0 5px 0;
                    margin: 0 0 5px 0;
                  }
                }

                .btn {
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
}

// edit domain form ---
#form_edit_domain {
  @extend %defaultAndFormEdit;
  padding: 5px 5px 5px 15px;
  margin: 0;

  .form-group {
    @include flexable(center, flex-start, row);
    flex: auto;
    margin: 0;

    h2 {
      @extend %textH1AndH2;
      color: $color-primary;
    }

    .form-control {
      flex: auto;
      padding: 0 10px 8px;
      border: none;
      font-size: $text-large;
      color: $color-dark-200;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    flex-direction: column;
    padding: 0;
    border: none;
    .form-group {
      width: 100%;
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm);
      padding: 2px 10px;
      margin: 0 0 10px 0;

      h2 {
        font-size: $text-medium;
      }

      .form-control {
        font-size: $text-medium;
        padding-bottom: 6px;
      }
    }
    .btn {
      width: 100%;
    }
  }
}

#domain_link_options {
  justify-content: center;

  input[type="radio"] {
    + label {
      min-width: 170px;
      @include b-radius(2px);
    }
  }

  @include mediaMaxWidth($screen-phones) {
    flex-direction: column;
    li {
      width: 100%;
      margin: 0 0 5px 0;

      &:last-child {
        margin: 0;
      }
    }
  }
}

.domain {
  &_link_wrapper {
    width: 100%;
    @include mediaMaxWidth($screen-mobile) {
      .form-control,
      .input-group-btn {
        display: block;
        width: 100%;
        height: 36px;
        float: unset;
        margin: 0 0 10px 0;

        button {
          width: 100%;
          height: 36px;
          border-right: 1px solid $color-gray-200 !important;
        }
      }
    }
  }

  &_search_field {
    width: 75%;
    min-height: 40px;
    padding: 0 20px 2px;
    margin: 0 auto 20px;
    @include b-radius($b-radius-sm);
    border-right: 1px solid $color-gray-200;

    &:focus, &:active {
      border-color: $color-gray-300;
    }
  }

  &_input {
    font-size: $text-small !important;
    border-right: 1px solid $color-gray-200 !important;
  }
}

#form_add_dns, #dns_values_list {
  direction: ltr;
  text-align: left;

  * {
    direction: ltr;
    text-align: left;
  }
}


#form_add_dns {
  [class*='col-'] {
    float: left;
  }

  .form-group {

    label {
      display: block;
      min-height: 23px;

      &.hidden-md {
        @include mediaMaxWidth($screen-laptop-small) {
          display: none;
        }
      }
    }

    .form-control,
    #form_add_dns_submit {
      min-height: 40px;
    }


    .form-control {
      border: 2px solid $color-gray-200;
      padding: 5px 12px 7px;
    }

    .bootstrap-select {
      width: 100%;

      .dropdown-toggle {
        min-height: 40px;
        border: 2px solid $color-gray-200;
        padding: 5px 30px 7px 12px;

        .bs-caret {
          .caret {
            left: unset;
            right: 12px;
          }
        }
      }
    }

    #form_add_dns_submit {
      width: 100%;
      text-align: center;
    }

    &.rec-input {
      &-type {
        flex: 0 0 150px;
      }

      &-name, &-content {
        flex: auto;
      }

      &-submit {
        flex: 0 0 100px;
      }
    }
  }
}

#dns_values_list {
  td {
    font-size: $text-x-small;
    color: $color-dark-200;
  }

  .btn {
    &.btn-delete {
      width: 20px;
      height: 20px;
      @include b-radius(50%);
      line-height: 12px;
      color: $color-white;
      text-align: center;
      float: right;
      background: rgba($color-danger, 0.9);
      padding: 0;

      i {
        font-size: 13px;
      }

      &:hover {
        background: rgba($color-danger, 1);
      }
    }
  }
}

#store_domains {
  margin: 0 auto 30px;
}

.check_domain {
  border-right: none !important;
}
