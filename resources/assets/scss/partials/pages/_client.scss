.client-profile {
  width: fit-content;
  display: grid;
  grid-gap: 20px;
  grid-template-columns: 1fr 1fr;
  margin-inline: auto;

  > * {
    height: 100%;
  }

  &__avatar {
    @include flexable(center, center, column);
    gap: 10px;

    h5 {
      font-size: $text-small;
      margin: 0;
    }

    p {
      color: $color-gray-400;
      margin: 0;
      line-height: 1;

      span {
        display: inline-block;
        vertical-align: middle;
        margin: 0 5px;
      }
    }

    &.custom-width {
      @media (min-width: 767px) {
        width: 22%;
      }
    }
  }

  &__contact {
    ul {
      li {
        width: auto;
        margin: 0 0 0 15px;
        padding: 0 0 0;

        a {
          font-size: $text-small;
          white-space: nowrap;

          i {
            margin-left: 3px;
          }
        }

        &:last-child {
          margin: 0;
        }
      }
    }
  }

  @include mediaMaxWidth($screen-phones) {
    flex-direction: column;
    > * {
      width: 100%;
      text-align: center;
    }

    &__avatar {
      padding: 10px 0 20px;
      border-left: none;
      margin: 0 0 20px;
      border-bottom: 2px solid $color-gray-200;
    }
    &__contact {
      ul {
        justify-content: center;
      }
    }
  }
  @include mediaMaxWidth($screen-phones) {
    grid-template-columns: 1fr;
  }
}

.client-avatar {
  display: block;
  width: 45px;
  height: 45px;
  position: relative;
  border: 2px solid $color-gray-200;
  @include b-radius(50%);
  overflow: hidden;

  img {
    display: block;
    width: 100%;
    height: auto;
  }
  &--large {
    width: 80px;
    height: 80px;
    flex: 0 0 80px !important;
  }
  &.initials {
    background: rgba(92, 213, 196, 0.05);
    border: 1px solid rgba(92, 213, 196, 0.2);
    color: #5cd5c4;
    font-size: 17px;
    flex: 0 0 50px;
    @include flexable(center, center, row);
    &.large {
      width: 50px;
      height: 50px;
    }
  }
  &--large {
    width: 80px;
    height: 80px;
  }
}
