.two-factor-auth-info {
  position: relative;
  min-height: 100vh;
  @include flexable(center, center, row);
  padding: 30px 0;

  &__inner {
    width: 780px;
    max-width: 90%;
    padding-bottom: 60px;
  }

  &__header {
    p {
      width: 80%;
      margin: 0 auto 40px;
    }
  }

  .loader-wrap {
    display: none;
    position: absolute;
    @include centerXY();
  }

  .loader {
    width: 32px;
    height: 32px;
    border-width: 2px;
  }

  &.is-loading {
    .loader-wrap {
      display: block;
      z-index: 2;
    }

    &:after {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba($color-white, 0.85);
      content: '';
      z-index: 1;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    .panel {
      .rec-list {
        > div {
          flex: 0 0 70%;
        }
      }
    }
  }

  @include mediaMaxWidth($screen-phablet) {
    &__header {
      p {
        width: 100%;
      }
    }
  }

  .btn:hover {
    opacity: 0.8;
  }
}


.two-factor-auth-otp {
  height: 100vh;
  padding: 0 15rem;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    width: 5px;
    height: 50%;
    @include centerXY;
    background-color: $color-gray-200;
  }

  .rec-list {
    > div, img {
      flex: 0 0 50%;
    }
  }
}