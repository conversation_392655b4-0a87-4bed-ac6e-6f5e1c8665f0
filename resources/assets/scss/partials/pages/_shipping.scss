.rec-wide-handler {
  margin-bottom: 0 !important;

  .irs {
    &--round {
      .irs-handle {
        &.single {
          width: 34px;
          border-width: 3px;
        }
      }

      .irs-single {
        &:after {
          border-top-color: $color-primary;
        }
      }
    }
  }
}

.weight-price-indicator {
  @include flexable(center, space-evenly, row);
  gap: 10px;
  min-width: 300px;
  max-width: 400px;
  padding: 10px;
  margin: 0 auto;
  background-color: $color-gray-25;
  @include b-radius($b-radius-sm);

  > * {
    flex: 0 0 50%;
    @include flexable(center, center, row);
    gap: 10px;

    > span {
      &:first-child {
        color: $color-dark-100;
      }
    }

    i {
      display: inline-block;
      transform: translateY(1px);
      margin-left: 5px;
    }

    &.price-content {
      border-right: 1px solid $color-gray-300;
      padding-right: 10px;
    }
  }
}

.slider-range-container {
  width: 100%;
  max-width: 400px;
  margin-inline: auto;
  @include mediaMaxWidth($screen-phones) {
    max-width: 280px;
  }
}

.irs--round {
  .irs-min,
  .irs-max {
    padding: 10px !important;
    top: -5px !important;
  }
}

#dhl_shipping_company_weight_span {
  line-height: 25px;
  margin-left: 3px;
}

#dhl_shipping_company_price {
  margin: 0 0 0 4px;
}

#dhl_shipping_company_price_search {
  width: 100%;
  @include b-radius(50px);
  padding: 8px 35px;

  i {
    position: absolute;
    left: 15px;
    @include centerY();
  }
}

#shipping-companies,
#store_brands {
  margin: 2rem 0;

  .store-setup-heading {
    margin-bottom: 20px;
    padding: 0 10px;

    h6 {
      margin: 0 !important;
    }
  }
}

.shipping-company-listing,
.store-brands-listing {
  border: 1px solid $color-gray-200;
  @include b-radius($b-radius);

  .store-setup-item-cont {
    @include flexable(center, flex-start, column);
    width: 100%;
    height: 100%;
    overflow: hidden;

    > * {
      width: 100%;

      &.store-setup-item {
        height: unset;
        flex: 1 0 auto;
        padding: 2rem 1rem;
        border: none;
        @include b-radius($b-radius);

        .success-flag {
          @include flexable(center, flex-start, row);
          font-size: 2rem;
          color: $color-primary;
          position: absolute;
          top: 12px;
          left: 13px;
          // override default styling ---
          i {
            color: $color-primary;
            position: unset;
            top: unset;
            left: unset;
          }
        }

        .offer-flag {
          transform: rotate(-45deg);
          position: absolute;
          top: 10px;
          left: -70px;
          text-align: center;
          width: 200px;
          font-size: $text-xx-small;
          color: $color-white;
          padding: 5px 0 7px;
          background-color: $color-danger;

        }

        .component-image {
          display: block;
          width: 100px;
          height: 60px;
          margin: 0 auto 5px;
          object-fit: contain;

          > i {
            display: block;
            margin-top: 20px;
            font-size: 35px;

            @include mediaMaxWidth($screen-phones) {
              margin-top: 10px;
            }
          }

          &.es-logo {
            width: 100%;
            height: auto;
            max-height: 50px;
          }
        }

        p {
          margin: 0;
          text-align: center;
          font-size: $text-xx-medium;

          &.component-desc {
            color: $color-dark-100;
            margin: 0;
          }
        }

        @include mediaMaxWidth($screen-phones) {
          padding: 1.5rem 1rem 1rem;;
        }
      }

      .component-credit {
        @include flexable(center, center, row);
        width: 100%;
        font-size: $text-xx-small;
        margin: 1rem 0 0;
        @include b-radius($b-radius-sm);

        > p {
          font-size: 12px;
          color: $color-gray-100;
        }

        b {
          display: inline-block;
          margin: 0 3px;
          color: $color-primary;
          margin-right: 6px;
          transform: translateY(1px);
          cursor: pointer;

          i {
            font-size: $text-small;
            vertical-align: middle;
            margin-left: 4px;
            transform: translateY(-1px);
          }
        }
      }

      &.store-setup-details {
        @include flexable(center, center, row);
        flex: 0 0 30px;
        padding: 10px 12px;
        background-color: transparent;

        .detail-btn {
          -moz-user-select: none;
          -webkit-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -o-user-select: none;
          width: auto;
          height: unset;
          padding: 2px 12px 5px;
          @include b-radius(50px);
          background-color: $color-primary;
          font-size: $text-xx-small;
          color: $color-white;
          border: none;
          @include transi(background-color);

          i {
            display: inline-block;
            vertical-align: middle;
            margin: 0 0 0 3px;
            transform: translateY(-1px);
          }

          &:hover {
            background-color: $color-primary-d;
          }

          // settings btn {
          &.settings {
            background: transparent;
            padding: 0;
            color: $color-dark-100;

            &:hover {
              color: $color-dark-100;
            }
          }
        }

        .switchery {
          &.switchery-small {
            width: 30px;
          }
        }

        &.connected {
          justify-content: space-between;
        }
      }
    }
  }
}

// pickup branches ---
#pickup-branches {
  .branch-name {
    &.open {
      &:after {
        content: 'مفتوح';
        font-size: $text-xxxx-small;
        color: $color-primary;
        background-color: rgba($color-secondary, 0.05);
        border: 1px solid rgba($color-secondary-d, 0.4);
      }
    }

    &.closed {
      &:after {
        content: 'مغلق';
        font-size: $text-xxxx-small;
        color: $color-danger;
        background-color: rgba($color-danger, 0.05);
        border: 1px solid rgba($color-danger, 0.4);
      }
    }

    &:after {
      display: inline-block;
      padding: 3px 10px;
      @include b-radius(25px);
      @include transi();
      margin: 0 10px 0 0;
    }
  }
}

#modal_branch_form {
  .modal-body {
    padding: 0;

    .modal-body-cont {
      padding: 20px;
    }
  }

  #new_branch_name {
    border: 1px solid $color-gray-200;
    min-height: 45px;
    padding: 5px 20px;
  }


  @include mediaMaxWidth($screen-tablet-p) {
    .modal-body {
      padding: 0 !important;
    }
  }
}

.working-days-hours {
  display: block;
  width: 100%;
  @include strip-ul();
  position: relative;  
  @include b-radius($b-radius-sm);

  li {
    > div {
      &.first-period {
        @include flexable(center, space-between, row);
        margin: 5px 0;
        font-size: $text-small;

        > div {
          &.week-day {
            flex: 1 0 65%;

            .switchery {
              margin-left: 5px;
            }
          }

          &.time-period {
            @include flexable(center, center, row);
            width: 100%;

            .input_time_picker {
              margin-left: 10px;
              flex: auto;

              &:last-child {
                margin-left: 0;
              }
            }

            input {
              display: inline-block;
              width: 100%;
              position: relative;
              margin-left: 10px;
              border: 1px solid $color-gray-200;
              direction: ltr;
              text-align: right;

              &[disabled] {
                background-color: $color-gray-50;
                opacity: 1;
              }

              &:first-child {
                &:after {
                  content: 'من';
                }
              }

              &:last-child {
                margin-left: 0;

                &:after {
                  content: 'إلى';
                }
              }

              &:before {
                vertical-align: middle;
                margin-right: 10px;
                color: $color-dark-100;
              }
            }
          }
        }
      }

      &.other-period {
        margin: 5px 0;
        @include flexable(center, space-between, row-reverse);

        .time-period {
          @include flexable(center, space-between, row);
          flex: 0 0 35%;

          div {
            flex: auto;
            margin-left: 10px;

            input {
              border: 1px solid $color-gray-200;
            }
          }
        }
      }
    }

    .btn {
      &::before {
        content: '\e90c';
        font-family: $font-sallaIcon !important;
        font-size: $text-x-small;
        position: absolute;
        @include centerXY;
        left: 53%;
      }

      &-add-period,
      &-delete-period {
        width: 20px;
        height: 20px;
        padding: 0;
        flex: 0 0 20px;
        line-height: 1;
      }

      &-add-period {
        opacity: 1;
        color: $color-primary-l;
        background: transparent;
        border: 1px solid $color-secondary-50;
      }

      &-delete-period {
        &::before {
          content: '\ed91'
        }
      }
    }

    &:not(:last-child) {
      padding-bottom: 5px;
      margin-bottom: 10px;
      border-bottom: 1px solid $color-gray-100;
    }
  }

  @include mediaMaxWidth($screen-tablet-p) {
    padding: 0;
    background-color: transparent;

    li {
      > div {
        &.first-period {
          flex-direction: column;
          align-items: flex-start;
          padding: 10px 0;

          > div {
            &.week-day {
              margin: 0 0 10px;
            }
          }
        }

        &.other-period {
          flex-direction: row;

          .time-period {
            flex: initial
          }
        }
      }
    }
  }
}

.search-select {
  &.bordered {
    .btn {
      &.dropdown-toggle {
        &.btn-default {
          border: 1px solid
        }
      }
    }
  }

  &.wide {
    width: 100% !important;
  }
}

.btn {
  &-wide {
    width: 100%;
  }
}

#company_form_div {
  max-width: 100% !important;
  max-height: 50px !important;
  width: unset !important;
  height: unset !important;
}

#load_shipping_div {
  min-height: 90vh;

  #contact-tab {
    .form-group {
      min-height: unset;
      margin-bottom: 10px;
    }
  }

  .card {
    &__header {

    }
  }

  #note-tab {
    p {
      font-size: 14px;
    }
  }
}
// bugfix to sort out working time responsive issues in mobile
@include mediaMaxWidth($screen-tablet-p) {
  .ui.popup.calendar table.ui.table tbody tr td {
    padding: 5.5px !important;
  }

  .working-days-hours li > div {
    &.first-period > div.time-period , &.other-period > div.time-period {
      margin-right: auto;
      width: 55%;
    }

    &.first-period > div.time-period input, &.other-period > div.time-period input {
      direction: ltr;
    }
  }
}

.pricing-summary {
  position: relative;
  @include flexable(center, space-around, row);
  @include b-radius($b-radius-lg);
  padding: 40px 5px 15px 5px;
  margin-top: 30px;
  border: 1px solid $color-gray-200;

  .icon-container {
    @include flexable(center, center);
    border-radius: 50%;
    background-color: $color-gray-200;
    height: 25px;
    width: 25px;
    text-align: center;

    i {
        color: $color-dark-200;
      }
  }

  .summary-header {
    position: absolute;
    top: -17px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 48px;
    background-color: $color-secondary-50;
    padding: 6px 16px;
    color: $color-primary-l;
  }
}
.shipping_zones{
  background: $color-gray-55;
  padding: 15px;
  border-radius: var(--b-radius-sm);
  margin-bottom: 30px;
  border: 1px solid $color-gray-200;
}

.delete-zone{
  float: left;
  margin: 3px 10px;
}

.progress-check{
  width: 48px;
  height: 48px;
  position: relative;
  @include b-radius(50%);
  margin-left: 20px;
  background: $color-secondary-50;
}

.check {
  position: absolute;
  width: 70%;
  height: 70%;
  z-index: 1;
  @include centerXY;
  @include b-radius(50%);
  @include flexable(center, center);
  span {
    font-size: $text-xx-small;
    color: $color-primary-l
  }
  i {
    color: $color-primary;
  }
}