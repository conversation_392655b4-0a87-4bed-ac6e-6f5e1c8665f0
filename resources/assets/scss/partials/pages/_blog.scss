.blog-thumbnail {
  min-height: $blog_image_height;
  .filepond--root {
    height: $blog_image_height;
  }

  .filepond--root .filepond--drop-label {
    height: $blog_image_height;
    background-color: $color-white;
    color: $color-gray-300;
    font-size: $text-small !important;
    @include b-radius($b-radius-sm);
    border: 1px solid $color-gray-200;
    cursor: pointer;

    .label-icon {
      font-size: $text-xxx-larger;
      color: $color-gray-200;
      cursor: pointer;
    }

    .label-text {
      cursor: pointer;
    }
  }

  .filepond--panel-root {
    height: $blog_image_height;
  }
}