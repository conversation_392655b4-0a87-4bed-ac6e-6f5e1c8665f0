.summary-welcome-block {
  padding: 30px;
  @include flexable(center, space-between);
  @include b-radius($b-radius);
  margin-bottom: 20px;
  border: 1px solid $color-gray-200;
  @media (max-width: $screen-laptop) {
    flex-direction: column;
  }

  .rightside {
    display: flex;
    flex-basis: 60%;
    max-width: 750px;

    img {
      margin-left: 20px;
    }

    h2 {
      font-size: $text-xx-larger;
      color: $color-primary;
      line-height: 1.4;
      margin: 0 0 10px;
    }

    h4 {
      font-size: $text-medium;
      margin: 0 0 5px;
    }

    p {
      font-size: $text-small;
    }

    @media (max-width: $screen-desktop-small) {
      max-width: 100%;
      flex-grow: 1;
    }

    @media (max-width: $screen-tablet-l) {
      flex-direction: column;
      text-align: center;

      img {
        margin: 0 auto;
        display: inline-block;
      }
    }
  }

  .sidearea {
    @include flexable(center, flex-end);
    flex-basis: 35%;
  }

  .leftside {
    border-color: $color-gray-200;
  }

  .progress-bar {
    flex-grow: 1;
    background-color: $color-gray-25;
    height: 10px;
    @include b-radius($b-radius-sm);
    margin-left: 15px;
    box-shadow: none;
    position: relative;

    &-percentage {
      background-color: $color-secondary-50;
      @include b-radius($b-radius-sm);
      position: absolute;
      height: 100%;
    }

    &-wrap {
      display: flex;
      width: 100%;
      align-items: center;

      span {
        font-size: $text-large;
      }
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {
    flex-direction: column;
    > div {
      width: 100%;

      &.leftside {
        margin-top: 20px;
        padding: 0 !important;
        border: 0;

        .btn {
          width: 100%;
        }
      }
    }
  }
}

.summary-steps {
  margin: 0;
  padding: 0;

  .summary-step {
    @include b-radius($b-radius);
    @include flexable(flex-start, flex-start, column);
    margin-bottom: 20px;
    border: 1px solid $color-gray-200;
    position: relative;


    &__showcase, &__actions {
      width: 100%;
      @include flexable(center, space-between);
    }

    &__actions {
      border-top: 1px solid $color-gray-200;
      padding: 20px;
      display: none;

      .rec-list {
        li {
          &:before {
            border: 1px solid $color-gray-200;
            @include b-radius(50px);
            padding: 3px 11px;
            margin-left: 20px;
          }

          &:not(:last-of-type) {
            margin-bottom: 30px;
          }

          > span {
            font-size: 16px;
            margin-left: 20px;
          }

          &.complete {
            &:before {
              content: '\ea9d';
              font-family: $font-sallaIcon;
              height: 30px;
              width: 30px;
              color: $color-primary-l;
              background-color: $color-secondary-50;
              @include b-radius(50%);
              @include flexable(center, center);
              box-shadow: 0 4px 11px 0 $color-secondary-d;
              border: 0;
              font-size: 17px;
            }
          }

          .btn {
            padding-right: 0;
            padding-left: 0;
            width: 130px;
          }

          button.btn, > div {
            margin-right: auto;
          }

          > div {
            @include flexable(center, center)
          }
        }
      }
    }

    &__title {
      display: flex;
      flex: auto;
      padding: 20px;

      > div {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .icon {
        color: rgba($color-primary-l, 0.75);
        font-size: $text-larger;
        border: 1px solid $color-secondary-50;
        width: 50px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        display: inline-block;
        margin-left: 20px;
        @include b-radius($b-radius-sm);
        flex-shrink: 0;
      }

      h5 {
        font-size: $text-small;
        line-height: 1;
        font-family: $font-main;
        margin: 0 0 7px;
        color: $color-dark-200;

        span {
          font-size: 80%;
          opacity: 0.5;
        }
      }

      h2 {
        font-size: $text-xx-medium;
        color: $color-primary-l;
        margin: 0;
        line-height: 1;
        font-family: $font-main;
      }
    }

    &__check {
      width: 20px;
      height: 20px;
      color: $color-primary-l;
      background-color: $color-secondary-50;
      @include b-radius(50%);
      @include flexable(center, center);
      position: absolute;
      top: 10px;
      right: 15px;
      box-shadow: 0 4px 11px 0 $color-secondary-d;

      .icon {
        font-size: $text-small;
      }
    }

    &__btn {
      .btn {
        min-width: 115px;
        padding: 10px;
      }
    }

    &.uncomplete {
      .summary-step__title {
        .icon {
          background-color: rgba($color-warning, 0.15);
          color: $color-warning;
        }

        h2 {
          color: $color-dark-300;
        }
      }
    }

    @media (max-width: $screen-tablet-p) {
      padding: 15px;
      &__title {
        h5 {
          font-size: $text-x-small;
        }

        h2 {
          font-size: $text-medium;
          line-height: 1.4;
        }
      }

      &__btn {
        .btn {
          min-width: 90px;
        }
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      &__actions {
        .rec-list {
          li {
            > span {
              font-size: 14px;
            }

            .btn {
              width: 115px;
            }
          }
        }
      }
    }
  }
}

.progress-circle {
  width: 3.5em;
  height: 3.5em;
  min-width: 3.5em;
  position: relative;
  background-color: $color-gray-200;
  @include b-radius(50%);
  .left-half-clipper {
    @include b-radius(50%);
    width: 3.5em;
    height: 3.5em;
    position: absolute;
    clip: rect(0 ,5em ,5em ,1.8em);
    .value-bar {
      position: absolute;
      clip: rect(0, 1.8em, 3.7em, 0);
      width: 3.5em;
      height: 3.5em;
      @include b-radius(50%);
      border: 0.45em solid $color-secondary-50;
      box-sizing: border-box;
    }
  }

  .steps {
    background: $color-secondary-50;
    position: absolute;
    width: 70%;
    height: 70%;
    z-index: 1;
    @include centerXY;
    @include b-radius(50%);
    @include flexable(center, center);
    span {
      font-size: $text-xx-small;
      color: $color-primary-l
    }
    i {
      color: $color-primary;
    }
  }

  &:after {
    border: none;
    position: absolute;
    top: 3px;
    left: 3px;
    text-align: center;
    display: block;
    border-radius: 50%;
    width: 3.1em;
    height: 3.1em;
    background-color: white;
    content: " ";
  }

  &.over50 {
    .left-half-clipper {
      clip: rect(auto,auto,auto,auto);
      .first50-bar {
        position: absolute;
        clip: rect(0, 5em, 5em, 1.8em);
        background-color: $color-secondary-50;
        border-radius: 50%;
        width: 3.5em;
        height: 3.5em;
      }
    }
  }

  &--gray {
    .steps {
      background-color: $color-gray-300;
      span {
        color: $color-primary;
      }
    }
  }

  &--column {
    .steps {
      flex-direction: column;
      span {
          font-size: $text-xxxx-small;
          &:last-child {
            transform: translateY(-4px)
          }
        }
      > span {
        font-size: 8px;
      }
    }
  }
}

.launch-store-btn {
  &.show {
    @include mediaMaxWidth($screen-phablet) {
      z-index: 999999991;
    }
  }
}

.launch-animation {
  display: none;
  position: absolute;
  top: -20%;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  z-index: 99999;
}

#launchModal {
  &.in {
    z-index: 9999 !important;
    @include flexable(center, center);
  }
  
  .modal-dialog {
    width: 600px;
    max-width: 90%;
    margin: 1em auto !important;
  }

  img {
    margin-bottom: 30px;
    display: inline-block;
  }

  h4 {
    font-size: $text-x-medium;
    line-height: 1;
    margin-bottom: 10px;
  }

  p {
    font-size: $text-small;
  }

  .avatar {
    min-width: 65px;
    height: 65px;
    @include b-radius(9999px);
    border: 1px solid $color-gray-200;
    @include flexable(center, center, row);

    img {
      @include b-radius(999px);
      height: 65px;
      width: 65px;
      object-fit: cover;
    }
  }
  .user-info {
    @include b-radius(12px);
    box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.1);
    .badge {
      background-color: #F1F8F9 !important;
      max-height: unset;
      white-space: unset;
      word-break: break-word;
    }

    .btn.copied {
      color: $color-success !important;
      &::before {
        content: '\ea9d';
        display: inline-block;
        width: 16px;
        height: 16px;
        background-color: $color-success;
        color: $color-white;
        text-align: center;
        font-size: 10px;
        line-height: 17px;
        @include b-radius(999px)
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      flex-direction: column;
      > div {
        &.avatar {
          width: 65px;
          margin: 0 auto 20px !important;
        }
        text-align: center !important;
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: space-between;

    &:before,
    &:after {
      display: none;
    }
  }

  .btn-dark {
    background-color: $color-dark-100;
    border-color: $color-dark-100;

    &:hover {
      background-color: $color-dark-200;
    }
  }
}

