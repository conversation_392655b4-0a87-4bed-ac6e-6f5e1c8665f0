#table_list_orders {
  &.empty {
    &:after {
      display: block;
      width: 100%;
      height: auto;
      padding: 15px 20px;
      content: 'لم يتم العثور على اي نتيجة...';
      font-family: $font-main;
      font-size: $text-small;
      color: $color-gray-400;
    }
  }
}
#coupons_table{
  .badge {
    &--coupon {
      &-created {
        color: 	#aa6100 !important;
        user-select: none;
        cursor: default;
        i{
          color:inherit !important;
        }
      }
    }
  }
}

// for modal button ---
#export_coupon_report {
  &:before {
    content: "\ead7";
  }
}


#file_uploader_row{
  .upload-file-wrapper {
    position: relative;

    .uploaded-successfully {
      position: absolute;
      left: 110px;
      top: 9px;
      color: $color-primary;
      font-size: $text-xx-small;
      z-index: 9999;
      display: none;

      i {
        font-size: $text-xx-small;
        margin-left: 3px;
        position: relative;
        top: 0px;
      }
    }

    .file-name {
      font-size: $text-xx-small;
      color: inherit;
      position: absolute;
      right: 15px;
      top: 9px;
      z-index: 55;
      display: none;
      width: 30%;
      white-space: nowrap;
      overflow: hidden;
    }
    .form-control{
      border-right:1px solid $color-gray-200;
    }
  }
  .file-caption-name {
    font-size: $text-xx-small;
    color: $color-dark-100;

    i {
      display: none;
    }
  }
  .btn-primary {
    background: $color-secondary-50;
    border-color: $color-secondary-50;
    color:$color-primary;
    cursor: pointer;

    i {
      &:before {
        content: '\eae0';
        font-family: $font-sallaIcon !important;
        position: relative;
        top: -3px
      }
    }

    &:active,
    &:focus,&:hover {
      background: $color-secondary !important;
      border-color: $color-secondary;
      color:$color-primary;
    }
  }
  .file-input {
    position: relative;

    .kv-upload-progress {
      position: absolute;
      z-index: 5555;
      width: 40%;
      height: 3px !important;
      right: 454px;
      top: 52px;
      margin: 0;

      .progress {
        height: 100%;

        &-bar {
          background-image: none !important;
        }
      }

      &:before {
        content: 'جاري رفع الملف';
        font-size: $text-xx-small;
        color: $color-dark-200;
        position: absolute;
        right: -96px;
        top: -10px;
      }
    }

    .kv-fileinput-caption {
      background: $color-white !important;
    }
  }
  .edit-state{
    position: absolute;
    top: 7px;
    right: 10px;
    z-index: 20;
    color:$color-dark-100;
  }
  .file-loading {
    position: relative;
    background-image: none;

    .upload-file-wrapper {
      position: relative;

      .uploaded-successfully {
        position: absolute;
        left: 110px;
        top: 9px;
        color: $color-secondary;
        font-size: 12px;
        z-index: 9999;
        display: none;

        i {
          font-size: 12px;
          margin-left: 3px;
          position: relative;
          top: 2px;
        }
      }

      .file-name {
        font-size: $text-xx-small;
        color: $color-gray-400;
        position: absolute;
        right: 15px;
        top: 9px;
        z-index: 55;
        display: none;
        width: 30%;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .file-caption-name {
      font-size: $text-xx-small;
      color: $color-gray-400;

      i {
        display: none;
      }
    }

    .file-input {
      position: relative;

      .kv-upload-progress {
        position: absolute;
        z-index: 5555;
        width: 96%;
        right: 20px;
        top: 66px;
        height: 3px !important;
        margin: 0;

        .progress {
          height: 100%;

          &-bar {
            background-image: none !important;
          }
        }

        &:before {
          content: 'جاري رفع الملف';
          font-size: $text-xx-small;
          color: $color-dark-50;
          position: absolute;
          right: -96px;
          top: -10px;
        }
      }


      .kv-fileinput-caption {
        background: $color-white !important;
      }
    }
  }

}
