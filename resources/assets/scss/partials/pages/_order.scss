// sharable styles
%inlineBlock {
  display: inline-block;
  vertical-align: middle;
}

%bRadiusAndTextSmall-xxx {
  font-size: $text-xxx-small;
  @include b-radius(50%);
}

%borderBackground {
  border: 1px solid $color-gray-100;
  background: transparent;
}


#branches_list {

  // @osama - needs revisit ---
  .shipping-cost {
    .product-price-bg {
      line-height: 1;
      font-size: $text-xx-small;
      padding: 3px 10px 6px;
      margin: 0;

      b {
        font-weight: normal;
      }
    }

    @include mediaMaxWidth($screen-phones) {
      .cart-total-title {
        width: 100%;
      }

      .product-price-bg {
        float: right;
      }
    }
  }
}

.cancel-loyalty-point{
  color:$color-danger-100;
  background:$color-danger-light-100;
  padding:2px 5px;
}
.cancel-loyalty-point:hover,.cancel-loyalty-point:focus{
  color:$color-danger-100;
}

.order-customer .customer-td {
  .order-source {
    @include mediaMaxWidth($screen-phones) {
      display: none !important;
    }
  }
}

.rec {
  &-order-info-panel {
    .panel-body {
      padding: 20px 5px;
    }

    .rec {
      &-order-info-block {
        .order-top-line {
          @include flexable(center, flex-start, row);
          font-size: $base-size;
          white-space: nowrap;
          color: $color-dark-200;
          margin: 0 0 5px 0;

          i {
            @extend %inlineBlock;
            transform: translateY(4px);
            font-size: $text-giant;
            color: $color-gray-200;
            margin: 0 0 0 15px;
          }
        }

        // order state ---
        &--state {
          .order-top-line {
            justify-content: flex-end;
          }
        }
      }

      &-order-no {
        float: right;
        padding: 4px 20px;
        @include b-radius(50px);
        background-color: $color-gray-25;
      }

      &-order-date {
        font-size: $text-xx-large;
        color: $color-dark-200;
      }

      &-order-status {
        padding-right: 25px;

        i,
        span {
          display: inline-block;
          vertical-align: middle;
          line-height: 1;
        }

        span {
          margin-bottom: 5px;
        }

        i {
          padding: 0 10px 0 0;
          margin: 0 10px 0 0;
          border-right: 1px solid rgba($color-white, 0.2);
          font-size: $text-xx-large;
        }

        &--name {
          float: left;
          padding: 0 25px 2px;
          @include b-radius(50px);
          min-height: 30px;
          background-color: $color-gray-25;
          font-size: $text-small;
          line-height: 25px;
        }
      }

      &-line-wrapper {
        @include flexable(flex-start, flex-start, column);
        margin: 0 60px 0 0;
      }
    }

    @include mediaMaxWidth($screen-desktop) {
      .rec {
        &-order-info-block {
          .order-top-line {
            i {
              font-size: $text-larger;
              margin: 0 0 0 15px;
              transform: translateY(0);
            }
          }
        }

        &-line-wrapper {
          margin: 0;
        }

        &-order-no,
        &-order-date {
          font-size: $text-small;
        }

        &-order-no {
          padding-top: 4px;
        }

        &-order-status {
          padding: 0 15px 1px 5px !important;

          i {
            padding: 0 5px 0 0;
            margin: 0 5px 0 0;
          }
        }
      }
    }

    @include mediaMaxWidth($screen-laptop-small) {
      .rec {
        &-order-info-block {
          @include flexable(flex-start, space-between, row);
          margin: 0 0 20px 0;

          .order-top-line {
            margin: 0 0 0 15px;

            i {
              font-size: $text-xx-medium;
            }
          }

          &--state {
            margin: 0;
          }
        }

        &-order-date {
          text-align: left;
        }
      }
    }
  }

  &-order-panel {
    display: flex;
    flex-flow: column;

    .panel-body {
      margin-bottom: auto;
    }

    .media {
      .media-body {
        .media-heading {
          margin-bottom: 5px;
        }

        .text-muted {
          line-height: 1.5;
          font-size: $text-x-small;
        }

        .rec {
          &-shipping-policy {
            @include flexable(center, space-between, row);
            margin: 10px 0;

            b {
              display: inline-block;
              margin: 0 3px;
              color: $color-black;
            }

            .btn {
              &.btn-danger {
                @extend %bRadiusAndTextSmall-xxx;
                padding: 3px 15px 5px;
              }
            }
          }
        }
      }
    }
  }

  &-order-tags-list {
    margin: 5px 0 0 0;
    flex-wrap: wrap;

    li {
      margin: 0 0 5px 5px;

      span {
        @extend %bRadiusAndTextSmall-xxx;
        color: $color-dark-100;
        padding: 0 10px 4px;
        background: $color-gray-25;
        white-space: nowrap;

        a {
          @extend %inlineBlock;
          transform: translateY(-1px);
          color: $color-danger;

          i {
            font-size: $text-xxx-small;
          }
        }
      }

      &:last-child {
        margin-left: 0;
      }
    }

    &--light {
      li {
        margin: 0 0 4px 0;

        span {
          padding: 0;
          background: transparent;
        }

        &.title {
          margin: 0 0 0 10px;

          i {
            @extend %inlineBlock;
            font-size: $text-x-small;
            color: $color-dark-100;
          }

          &:after {
            display: none;
          }
        }

        &:not(:last-child):after {
          content: '،';
          display: inline-block;
          vertical-align: baseline;
          color: $color-dark-100;
          margin: 0 2px 0 10px;
        }
      }
    }
  }

  &-order-tags {
    height: 20px;
    padding: 0;
    margin: 10px 0 0 0;
    transform: translateY(1px);
    font-size: $text-xx-small;
    color: $color-primary;
    text-decoration: underline;
    border: none;

    i {
      @extend %inlineBlock;
      font-size: $text-small;
      margin-left: 5px;
      transform: translateY(-1px);
    }

    &:hover {
      color: lighten($color-primary, 10%);
      text-decoration: underline;
    }
  }

  &-more-tags {
    @include flexable(center, flex-start, row);
    flex: auto;

    .rec-show-all-tags {
      display: inline-block;
      width: 20px;
      height: 20px;
      flex: 0 0 auto;
      position: relative;
      margin: 3px 0 0;
      padding: 0;
      text-align: center;
      font-size: $text-xxxx-small;
      color: $color-dark-100;
      line-height: 20px;
      @include b-radius(50%);
      background: $color-gray-200;

      &.fa-angle-right {
        font-size: $text-x-small;
        padding-left: 1px;
      }
    }

    >div {
      display: none;

      >div {
        @include flexable(flex-start, flex-start, row);
        flex-wrap: wrap;
      }

    }

    @include mediaMaxWidth($screen-phones) {
      display: inline-block;

      >div {
        >div {
          display: inline-block;
        }
      }
    }
  }
}


#edit_return {
  min-width: 140px;

  i {
    color: $color-dark-100;
    @extend %inlineBlock;
    margin: 0 0 0 10px;
    padding: 5px;
    @include b-radius(50%);
    background: $color-gray-200;
    position: absolute;
    @include centerY();
    right: 10px;
  }
}

// edit mode styling ---
body {
  &.order-edit- {
    .order-info-panel {
      border: 1px solid $color-secondary;
      background-color: $color-white;

      .rec-edit-mode {
        .order-top-line {
          float: right;
          margin-right: -15px;
          padding: 0 10px 3px 15px;
          background-color: $color-gray-25;
          @include b-radius($b-radius);

          i {
            display: inline-block;
            margin-top: 4px;
            color: $color-gray-200;
          }
        }

        .order-second-line {
          clear: both;
        }
      }


      .rec-order-status {

        &--name {
          @extend %borderBackground;
        }
      }
    }

    .panel-heading {
      .heading-elements {}
    }
  }
}

.dropdown-menu {
  &.order-print-options {
    right: 50%;
    transform: translateX(50%);
  }
}

.order-actions {
  gap: 20px;
}

.orders-nav {
  margin-top: 50px;

  @include mediaMaxWidth($screen-phones) {
    margin-top: 0;
  }
}

.payment-confirm {
  width: 25px;
  height: 25px;
  background: $color-primary;
  color: $color-white;
  padding: 0;

  * {
    transform: translateY(6px);
  }
}

// Agreement chars length
#agreement_length {
  position: absolute;
  bottom: 27px;
  left: 20px;
  font-size: $text-xxx-small;
}

// first order steps
.order-steps {
  padding: 30px;
  margin: 0;
  position: relative;

  &__head {
    color: var(--color-primary-l);
    display: block;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    margin: 0;

    .dark & {
      color: var(--color-secondary-50)
    }
  }

  &__body {
    display: flex;
    gap: 50px;
    flex-wrap: wrap;
    justify-content: space-around;
    padding-top: 30px;
    padding-bottom: 30px;
  }

  &__foot {
    text-align: center;
  }

  &__close {
    color: $color-danger !important;
    position: absolute;
    top: 18px;
    left: 18px;
    font-size: 24px !important;
  }

  .step {
    text-align: center;
    width: 220px;

    &__num {
      width: 50px;
      height: 50px;
      border-radius: 100px;
      background: var(--color-secondary-50);
      color: var(--color-primary-l);
      font-size: $text-large;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    &__head {
      color: var(--color-black);
      font-size: 15px;
      font-weight: 500;
      margin: 10px 0;

      .dark & {
        color: var(--color-secondary-50)
      }
    }

    &__text {
      color: var(--color-dark-300, );
      font-size: 14px;
    }
  }
}

.customer-tag {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 250px;
}