.option-container { // @osama - needs revisit ---
  margin: 0 -10px !important;

  .value-columns {
    padding-left: 5px;
    padding-right: 0;
  }
}

// targeting price fields only ---
.form-group {
  .option-type-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;


    .media {
      @include flexable(center, flex-start, row);

      img {
        width: 40px;
        height: 25px;
        cursor: pointer;
      }

      .btn {
        &-delete-circle {
          width: 15px;
          height: 15px;

          i {
            font-size: $text-xxxx-small;
          }
        }
      }
    }

    .div_product_option_feature {
      // color field ---
      &_color {
        .sp-replacer {
          &.sp-light {
            height: 36px;
            padding: 7px 10px;
            background-color: transparent;
            border: none;

            .sp-preview {
              width: 22px;
              height: 22px;
              @include b-radius(50%);
              overflow: hidden;
              box-shadow: 0 0 2px rgba($color-black, 0.4);
              margin: 0;
            }

            .sp-dd {
              display: none;
            }
          }
        }
      }

      // image field ---
      &_image {
        @include flexable(center, flex-start, row);
        width: 90px;
        height: 36px;
        padding: 0 7px;
        position: relative;

        img {
          width: 40px !important;
          max-height: 30px
        }

        > * {
          width: 100%;
          cursor: pointer;
        }

        > .file-input {
          display: inline-block;
          width: 200px;
          height: 100%;
          float: right;
          padding: 0;
          z-index: 1;

          .kv-upload-progress {
            position: absolute;
            @include centerY() ;
            left: 10px;

            .progress-bar, .progress {
              display: none;
            }

            &:before {
              content: "\f1ce";
              font-family: $font-awesome;
              color: $color-gray-300;
              font-size: $text-xx-small;
              display: block;
              animation: loop 1s infinite;
              transform-origin: center;
            }
          }

          .btn {
            &.fileinput-cancel {
              display: none;
            }

            &.btn-file {
              width: 100%;
              height: 100%;
              padding: 0;
              cursor: pointer;

              > * {
                display: inline-block;
                vertical-align: middle;
                width: 100%;
                min-width: unset;
                max-width: unset;

                &.hidden-xs {
                  display: block !important;
                  font-size: $text-xx-small;
                  color: $color-dark-100;
                  padding: 0;
                  text-align: right;
                  position: absolute;
                  right: 0;
                  top: 45%;
                  transform: translateY(-50%);
                  pointer-events: none;

                  &:before {
                    display: none;
                    vertical-align: middle;
                    content: '\eae0';
                    font-family: $font-sallaIcon;
                    font-size: $text-medium;
                    color: $color-gray-300;
                    margin-right: 7px;
                  }
                }

                &.option_details_image_file {
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  top: 0;
                  right: 0;
                  bottom: 0;
                  left: 0;
                  cursor: pointer;
                }
              }

              i {
                display: none;
              }

              input {
                padding: 0;
              }
            }

            &.fileinput-remove {
              width: 15px;
              height: 15px;
              border: none;
              padding: 1px 0 0 1px;
              background-color: $color-gray-300;
              @include b-radius(50%);
              position: absolute;
              @include centerY();
              left: 12px;
              z-index: 99;

              i, span {
                display: none;
              }

              &:before {
                content: '\ea47';
                font-family: $font-sallaIcon;
                font-size: $text-xxxx-small;
                color: $color-white;
                display: block;
              }

              &:hover {
                background-color: $color-danger;
              }
            }
          }
        }

        .img-prev-wrapper {
          width: 45px;
          height: 25px;
          overflow: hidden;
          @include b-radius($b-radius-sm);
          position: absolute;
          z-index: 9;
          pointer-events: none;
          @include transi();
          opacity: 0;
          visibility: hidden;
          box-shadow: 0 0 2px 0 rgba($color-black, 0.2);
          background: $color-gray-200;
          z-index: 0;

          img {
            display: block;
            width: auto;
            height: 100%;
            object-fit: cover;
          }
        }

        &.uploaded {
          > .file-input {
            .btn {
              &.btn-file {
                > * {
                  &.hidden-xs {
                    display: none !important;
                  }
                }
              }
            }
          }

          .img-prev-wrapper {
            opacity: 1;
            visibility: visible;
          }
        }

        @include mediaMaxWidth($screen-phones) {
          width: 50px;

          > .file-input {

            .kv-upload-progress {
              display: none;
            }

            .btn {

              &.btn-file {
                > * {
                  &.hidden-xs {
                    font-size: 0;
                    color: transparent;

                    &:before {
                      display: inline-block;
                    }
                  }
                }
              }

              &.fileinput-remove {
                left: 17px;
              }
            }
          }
          .img-prev-wrapper {
            width: 35px;
          }
        }
      }

      &:before {
        content: '';
        display: block;
        width: 1px;
        height: 70%;
        position: absolute;
        top: 15%;
        right: 0;
        background: $color-gray-100;
      }
    }
  }

  &--color {
    .form-control {
      &.product_price {
        padding-left: 50px;
      }
    }
  }

  &--image {
    .form-control {
      &.product_price {
        padding-left: 95px;
      }
    }

    @include mediaMaxWidth($screen-phones) {
      .form-control {
        &.product_price {
          padding-left: 60px;
        }
      }
    }
  }
}

#options_list {
  &.rec-options-list {
    @include mediaMaxWidth($screen-phones) {
      .form-group {
        margin-bottom: 3px;
      }
      .option-options {
        padding: 4px 12px 3px;
        @include b-radius($b-radius-sm);
        background: $color-gray-100;
        margin: 0 0 5px 0;
        border: 1px solid $color-gray-200;
      }
      .row {
        margin-#{$right}: -3px;
        margin-#{$left}: -3px;

        [class*='col-'] {
          padding-#{$right}: 3px;
          padding-#{$left}: 3px;
        }

        &.option-container {
          margin-bottom: 8px !important;
          background: $color-white;
          padding: 3px 0 0;
          @include b-radius($b-radius-sm);

          &:last-child {
            margin-bottom: 0 !important;
          }
        }
      }
    }
  }

  .value-columns {
    padding-left: 0;

    &:not(:last-of-type) {
      padding-left: 5px
    }
  }
}
