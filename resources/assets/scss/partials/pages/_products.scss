#products_div {
  grid-gap: 20px;

	.product-box {
		position: relative;
		margin: 0;

		.btn-delete-product {
			width: 30px;
			height: 30px;
			top: 15px;
			right: 15px;
			padding: 0;
			color: $color-white;
			line-height: 15px;
			background-color: $color-danger;
		}

		form {
			&.product-form {
				margin: 0;

        .thumbnail {
          &.active {
            border-color: rgba($color-primary, 0.35) !important;
            box-shadow: 0 1px 2px rgba($color-primary, 0.05) !important;
          }
        }

      }
    }

    &--template {
      .thumb {
        &:before {
          content: 'منتج غير حقيقي';
          position: absolute;
          line-height: 1.7;
          z-index: 10;
          color: white;
          background: $color-danger;
          cursor: pointer;
          opacity: 0.7;
          font-size: $text-xxx-small;
        }
      }
    }
  }

  &.conceal {
    animation: conceal 0.35s forwards;
  }

  &.reveal {
    animation: reveal 0.35s forwards;
  }

  &.default-view {
    opacity: 1;
    transform: translateY(0);
  }

  .product-hidden-badge-wrapper {
    position: absolute;
    left: 50px;
    top: 12px;
    .product-hidden-badge {
      width: 40px;
      height: 40px;
      background: rgba($color-danger, 0.10);
      color: $color-danger;
      .tooltip-content {
        width: 110px;
        * {
          font-size: 13px;
          color: $color-dark-300;
        }
        li {
          margin-bottom: 10px;
          i {
            display: inline-block;
            margin-left: 5px;
            position: relative;
          }
          &.hidden-platform {
            color: $color-danger;
            i {
              color: $color-danger;
              &:after {
                content: '';
                position: absolute;
                height: 25px;
                width: 5px;
                background-color: $color-danger;
                border-radius: 2px;
                border: 2px solid white;
                left: 4px;
                bottom: -5px;
                rotate: 145deg;
              }
            }
          }
          &:last-child {
            margin: 0;
          }
        }
      }
      &.tooltip-toggle {
        display: flex;
      }
    }
  }

  // grid view ---
  &.grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));

    &.single-product {
      @include flexable(center, flex-start, column);
      grid-template-columns: initial;

      .product-box {
        width: 430px;

        .rec-checkbox {
          display: none;
        }
      }

      @include mediaMaxWidth($screen-tablet-l) {
        .product-box {
          width: 100%;
        }
      }
    }

    .product-box {
      form {
        &.product-form {
          // override legacy code ---
          .thumbnail {
            padding: 0;

            .product-thumb-wrapper {
              position: relative;

              .thumb {
                overflow: hidden;
              }
            }

            .product-check {
              top: 15px;
              right: 15px;

              label {
                position: absolute;
                white-space: nowrap;
              }
            }

            .btn-delete-product {
              top: 15px;
              right: 15px;
            }

            .product_image_btn {
              padding: 5px 10px;
              border: none;
              @include transi(background);

              i {
                font-size: 15px;
                position: relative;
                margin-left: 5px;
              }
            }

            .prfw {
              padding: 12px 12px 0;
            }

            .form-group {
              margin-bottom: 12px;
            }

            .controls-wrapper {
              margin-top: 12px;

              .options-wrapper {
                .options {
                  &:first-of-type {
                    border-left: 1px solid $color-gray-200;
                  }

                  .btn {
                    padding: 0;
                    height: 40px;

                    &:first-of-type {
                      border: none;
                    }

                    i {
                      margin-left: 5px;
                    }
                  }
                }
              }
            }

            .save-product,
            .add-product {
              height: 40px;
              @include b-radius(0 0 $b-radius-sm $b-radius-sm);
            }

            .price_quantity_row {
              .rec-list {
                div {
                  width: 100%
                }
              }
            }

          }
        }
      }

      &--template {
        .thumb {
          &:before {
            z-index: 10;
            top: 15px;
            @include b-radius(50px);
            padding: 2px 10px 4px;
            left: 15px;
          }
        }
      }

      @include mediaMaxWidth(1350px) {
        &--template {
          .thumb {
            &:before {
              top: -44px;
            }
          }
        }
      }
    }
  }

  // list view ---
  &.list {
    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));

    .product-box {
      form {
        &.product-form {
          // override legacy code ---
          .thumbnail {
            @include flexable(flex-start, flex-start, row);
            padding: 15px 60px 15px 15px;

            .caption {
              flex: auto;
              @include flexable(flex-start, flex-start, column);
            }

            .product-thumb-wrapper {
              width: 100%;
              @include flexable(flex-start, flex-start, row);
              position: relative;

              .thumb {
                width: auto;
                height: auto;
                padding: 0;
                overflow: visible;

                .thumb-mask {
                  width: 84px;
                  height: 84px;

                  img {
                    width: 100%;
                    object-fit: cover;
                    @include b-radius($b-radius-sm);
                  }
                }
              }

              .product-name-type {
                >div:first-child{
                  flex: 3;
                }
                .form-group {
                  &.tooltip-toggle {
                    .help-block {
                      margin-bottom: 50px;
                    }
                  }
                }
              }
            }

            .pin_btn {
              right: -44px;
              bottom: 0;
            }

            .product-check {
              right: -40px;
              top: 0;

              label {
                position: absolute;
                white-space: nowrap;
              }
            }

            .btn-delete-product {
              right: 20px;
              @include center-v
            }

            .template-badge-danger {
              position: absolute;
              opacity: 0.8;
              background: $color-danger;
              color: white;
              left: unset;
              top: 70%;
              padding: 1px 15px 4px;
              @include b-radius(0);
              font-size: 12px;
            }

            .product_image_btn {
              z-index: 555;
              bottom: auto;
              top: 5px;
              left: auto;
              right: 5px;
              @include b-radius(50%);
              padding: 0;
              height: 20px;
              width: 20px;
              line-height: 30px;

              i {
                font-size: 13px;
                position: absolute;
                @include centerXY;
                margin: 0;

                &:before {
                  content: '\e90c'
                }
              }

              span {
                display: none;
              }
            }


            .prfw {
              flex: auto;
              @include flexable(flex-start, flex-start, row);
              padding: 0 12px 0;

              &.product-fields-wrapper {
                width: calc(100% - 84px);
                margin-right: 84px;
                margin-top: -35px;

                .form-group {
                  margin-bottom: 0;

                  &:nth-of-type(2) {
                    .rec-list {
                      span {
                        flex: 0 0 67% !important
                      }
                    }
                  }
                }
              }
            }

            .product-price-small-screen.has-error{
              // to compentate for the margin-top -35px written above for showing the error msg
              padding-bottom: 40px;
            }

            .form-group {
              flex: 1;
              margin: 0 0 12px 12px;
            }

            .controls-wrapper {
              flex: 0 0 160px;
              margin: 0;

              .options-wrapper {
                margin-bottom: 12px;
                @include b-radius($b-radius-sm);
                border: 1px solid $color-gray-200;

                .options {
                  .btn {
                    &.btn-product-delete {
                      border-right: 1px solid $color-gray-200 !important;
                    }
                  }

                  .product-options-btn {
                    border: none
                  }
                }
              }
            }

            .save-product,
            .add-product {
              height: 36px;
              @include b-radius($b-radius-sm);
            }

            .product-hidden-badge-wrapper {
              top: 20px;
              right: 118px;
              .product-hidden-badge {
                padding: 5px;
                width: 20px;
                height: 20px;
                i {
                  font-size: 12px;
                }
              }
            }

          }
        }
      }

      &--template {
        .thumb {
          &:before {
            top: 61px;
            @include b-radius($b-radius-sm);
            padding: 0 6px 4px;
            left: 1px;
          }
        }
      }

      @include mediaMaxWidth(1350px) {
        &--template {
          .thumb {
            &:before {
              top: -44px;
              padding: 2px 10px 4px;
              @include b-radius(50px);
              left: 15px;
            }
          }
        }
      }
      @include mediaMinMaxWidth(1025px, 1360px) {
        width: 100%;
      }
    }
  }

  // &.list,
  // &.grid {
  .product-box {
    form {
      &.product-form {
        .thumbnail {
          position: relative;
          @include b-radius($b-radius);
          border-color: $color-gray-200;
          margin: 0;

          .caption-small {
            padding: 0 !important;
          }

          .product-thumb-wrapper {
            .thumb {
              @include b-radius($b-radius $b-radius 0 0);

              .thumb-mask {
                cursor: pointer;
              }

              .pinned {
                background: $color-danger !important;

                i {
                  color: white !important;
                }
              }
            }
          }

          .pin_btn {
            background-color: $color-gray-100;
            width: 30px;
            height: 30px;

            i {
              font-size: $text-small;
              line-height: 29px;
              color: $color-dark-100;
            }
          }

          .product-check {
            width: auto;
            position: absolute;
          }

          .btn-delete-product, .product-hidden-badge {
            position: absolute;
            z-index: 555;
          }

          .product-type {
            display: none;
          }

          .product-price-small-screen {
            flex: 1;
          }

          .prfw {
            > div {

              &:nth-of-type(1) {
                .rec-btn {
                  border: 1px solid $color-gray-200;
                  border-right: none;
                  padding: 0 7px;
                  height: 36px;

                  i {
                    font-size: $base-size;
                    color: $color-dark-100;
                  }

                  &:hover {
                    opacity: 1;
                  }

                  &.value-filled {
                    i {
                      color: $color-primary
                    }
                  }
                }

                .rec-list {
                  position: absolute;
                  width: 100%;
                  z-index: 99999;
                  left: 0;
                  top: 100%;
                  background: $color-white;
                  border: 1px solid $color-gray-200;
                  @include b-radius($b-radius-sm);
                  box-shadow: 0 0 10px rgba(0, 0, 0, .10);
                  align-items: center;
                  padding: 7px 10px;

                  > span {
                    flex: 0 0 76%;
                    width: auto;
                    height: auto;
                    font-size: $text-xx-small;
                    color: $color-dark-300;
                  }

                  .form-control {
                    border: none;
                    border-right: 1px solid $color-gray-200;
                    padding: 0 15px 0 0;
                    height: 25px;
                  }

                  &:after {
                    content: '';
                    position: absolute;
                    width: 0;
                    height: 0;
                    border: 8px solid;
                    border-color: $color-white transparent transparent;
                    bottom: -16px;
                    left: 132px;
                  }

                  &.left-arrow {
                    &:after {
                      left: 38px
                    }
                  }
                }

                &.quantity-low {
                  span {
                    &.input-group-addon-small {
                      i {
                        color: $color-danger
                      }
                    }
                  }

                  input.product_quantity {
                    color: $color-danger;
                  }
                }
              }

            }
          }

          .form-group {

            .form-control {
              font-size: $text-xx-small;
            }

            .input-group-addon {
              color: $color-dark-200;
            }

            .btn {
              @include b-radius(0);

              &-tiffany {
                width: 94px;
                color: $color-dark-300 !important;
                border-color: $color-gray-200;
                border-right: 1px solid $color-gray-200;
                background-color: $color-gray-50;
                font-size: $text-xx-small;
                position: relative;
                box-shadow: inset 0 0 1px 1px $color-white !important;
                z-index: 5;
                border-right: none;

                &:active,
                &:focus {
                  color: $color-dark-300;
                  border-color: $color-gray-200 !important;
                  background-color: $color-gray-50 !important;
                }
              }

              &:last-child {
                @include b-radius($b-radius-sm 0 0 $b-radius-sm);
              }
            }

            &.categories {
              .input-group {
                .vue-treeselect {
                  padding-left: 94px;

                  .vue-treeselect__control {
                    @include b-radius(0 $b-radius-sm $b-radius-sm 0);
                  }
                }

                .input-group-btn {
                  width: fit-content;
                  position: absolute;
                  top: 0;
                  left: 0;
                }
              }
            }

            &.has-error {
              .form-control,
              .dropdown-toggle,
              .input-group-addon, .btn, .rec-btn {
                border-color: $color-danger !important;
              }
            }
          }

          .product-quantities {
            display: flex;

            > div {
              @include flexable(center, center, row);
              flex: auto;

              span {
                width: 31px;
                height: 36px;
              }
            }

            .form-control {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
              min-width: 55px;

              &.disabled {
                pointer-events: none;
              }
            }

            .btn {
              height: 36px;
              border-right: none;
              margin-right: 1px;

              &-infinite, &-notify {
                width: 30px;
                flex-shrink: 0;
                padding: 0;
              }

              &-infinite {
                svg {
                  height: 15px;
                  width: 15px;
                  fill: $color-dark-100;
                  margin: 0 auto;
                }

                &.active {
                  background: $color-gray-25;

                  svg {
                    fill: $color-primary
                  }
                }

                &.wide {
                  width: 47px;
                }
              }

              &[data-inline-loader] {
                width: auto;
              }
            }

            &--products-group {
              .btn {
                position: relative;
                right: 0;
              }
            }
          }

          .controls-wrapper {

            .options-wrapper {
              @include flexable(center, center, row);
              border-top: 1px solid $color-gray-200;

              .form-group {
                margin: 0;
              }

              .options {
                text-align: center;
                width: 100%;
                position: relative;

                .btn {
                  padding: 3px 15px 5px;
                  @include transi();
                  color: $color-dark-300 !important;
                  font-weight: 500;
                  font-size: $text-xx-small;
                  height: 36px;

                  i {
                    margin-left: 5px;
                  }
                }

                .dropdown-menu {
                  margin-top: 0;
                  padding: 0;
                  z-index: 99991;

                  li {
                    a {
                      display: block;
                      min-height: 36px;
                      clear: unset;
                    }
                  }

                  &.dropdown-menu-left {
                    left: 0;
                    right: auto;
                  }
                }

                .btn-group {
                  display: block;
                  position: initial;

                  > .btn {
                    @include b-radius(0 !important);
                  }
                }
              }
            }
          }

          .save-product,
          .add-product {
            font-size: $text-small;
            line-height: 1;
          }

          .product-name-type {
            .form-group {
              &.tooltip-toggle {
                .tooltip-content {
                  display: table;
                  width: auto;
                  font-size: 12px;
                  padding: 3px 8px 5px 14px;
                  @include b-radius($b-radius-sm);
                  right: 0;
                  left: auto;
                  z-index: 555;
                  transform: translate(0, -20px);

                  i {
                    font-size: inherit;
                    position: relative;
                    top: 1px;
                    margin-left: 5px;
                  }

                  &:after {
                    left: auto;
                    right: 8px;
                    margin-left: 0;
                    bottom: -5px;
                    border-width: 5px;
                  }
                }

                &:hover {
                  .tooltip-content {
                    transform: translate(0, -2px);
                  }
                }

                &.visible {
                  .tooltip-content {
                    opacity: 1;
                    visibility: visible;
                    transform: translate(0, -2px);
                  }
                }

                .input-group {
                  .input-group-addon {
                    @include b-radius(0 $b-radius-sm $b-radius-sm 0 !important);
                  }

                  .form-control {
                    @include b-radius($b-radius-sm 0 0 $b-radius-sm !important);
                  }
                }
              }
            }
          }
        }
      }

      .submit-wrapper {
        &-save {
          .btn {
            animation: blinker 1.8s linear infinite;
            @keyframes blinker {
              50% {
                opacity: .5;
              }
            }
          }
        }
      }
    }

    @include mediaMinMaxWidth(1025px, 1360px) {
      width: 100%;
    }

    // new product ---
    &-new {
      .thumbnail {
        background-color: $color-gray-50;
        border: 1px dashed $color-gray-200;
      }
    }
  }

  // }

  // &.list,
  // &.grid {
  &.grid-block {
    @include mediaMaxWidth(1350px) {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)) !important;
      grid-gap: 15px;

      .product-box {
        form {
          &.product-form {
            .thumbnail {
              display: block;
              padding: 60px 0 0 !important;

              .btn-delete-product {
                top: 15px;
                right: 10px;
                transform: unset;
              }

              .product-type {
                display: block;
              }

              .product-price-small-screen {
                display: block;
                margin: 12px 0 0 0 !important;

                &.has-error{
                  padding-bottom: 0;
                }
              }
              

              .product-thumb-wrapper {
                padding: 0 12px;
                @include flexable(initial, initial, row);

                .thumb {
                  overflow: visible;
                  margin: 0 0 0 10px;
                  width: 84px;
                  min-width: 84px;
                  height: 84px;
                  padding: 0;
                  @include b-radius($b-radius-sm);
                  position: initial;

                  .product-check {
                    top: 30px;
                    right: -38px;
                  }

                  .template-badge-danger {
                    opacity: 0.8;
                    background: $color-danger;
                    color: white;
                    left: unset;
                    top: 70%;
                    padding: 1px 15px 4px;
                    @include b-radius(0);
                    font-size: 12px;
                  }

                  .product_image_btn {
                    z-index: 555;
                    top: 7px;
                    bottom: auto;
                    left: auto;
                    right: 17px;
                    @include b-radius(50%);
                    height: 20px;
                    width: 20px;
                    pointer-events: none;
                    padding: 0;

                    i {
                      font-size: 13px;
                      position: absolute;
                      @include centerXY;
                      margin: 0;

                      &:before {
                        content: '\e90c'
                      }
                    }

                    span {
                      display: none;
                    }
                  }

                  .thumb-mask {
                    width: 84px;
                    height: 84px;
                    border: 1px solid $color-gray-200;
                    position: relative;
                    @include b-radius($b-radius-sm);
                    overflow: hidden;

                    img {
                      width: 100%;
                      height: auto;
                    }
                  }

                  .product-check {
                    top: -48px !important;
                    right: 12px !important;
                    transform: unset;
                  }

                  .pin_btn {
                    top: -48px !important;
                    left: 12px;
                    right: auto !important;
                    background-color: rgba($color-gray-200, 0.8);

                    i {
                      color: $color-dark-100;
                    }
                  }
                }

                .prfw {
                  padding: 0 !important;
                }

                .product-name-type {
                  display: block;
                  flex: auto;
                  padding: 0;

                  .form-group {
                    &.tooltip-toggle {
                      .help-block {
                        margin: 7px;
                      }

                      .tooltip-content {
                        display: none !important;
                      }
                    }

                    .input-group-addon {
                      .product-icon {
                        color: $color-gray-300;

                        &:before {
                          content: '\f060';
                        }
                      }
                    }
                  }
                }
              }


              .form-group {
                margin: 0 0 12px 0;

                .product-name-type {
                  .bootstrap-select {
                    > .dropdown-menu {
                      min-width: unset;
                      width: calc(100% - 32px);
                      margin-right: -32px;
                    }
                  }
                }
              }

              .product-fields-wrapper {
                display: block;
                width: 100% !important;
                margin-right: 0 !important;
                margin-top: 12px !important;
                padding-top: 0 !important;

                .form-group {
                  &:not(:last-child) {
                    margin-bottom: 13px;
                  }
                }
              }

              .controls-wrapper {

                .options-wrapper {
                  border: none;
                  border-top: 1px solid $color-gray-200;
                  @include b-radius(0);
                  margin: 12px 0 0;

                  .options {
                    .btn {
                      height: 40px;
                    }
                  }
                }
              }

              .save-product,
              .add-product {
                height: 40px;
                @include b-radius(0 0 $b-radius-sm $b-radius-sm);
              }

              .product-hidden-badge-wrapper {
                left: 42px;
                top: 12px !important;
                right: unset;
                .product-hidden-badge {
                  width: 30px;
                  height: 30px;
                  left: 10px;
                  i {
                    font-size: 14px;
                  }
                  .tooltip-content{
                    width: max-content;
                  }
                }
              }

            }
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(auto-fill, minmax(100%, 1fr)) !important;
    }
  }

  .hidden-product {
    &:after {
      position: absolute;
      content: '';
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: rgba($color-white, 0.5);
      pointer-events: none;
      z-index: 9;
    }
  }
}

#excluded_dates {
  .rec-list {
    li {
      &:last-child {
        [data-excluded-date] {
          padding-left: 0 !important;
          margin-left: 0 !important;
          border: none !important;
        }
      }
    }
  }
}
