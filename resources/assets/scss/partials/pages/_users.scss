.link_resend_activation_email {
  font-size: $text-xx-small;
  color: $color-primary-l;
  display: inline-block;
  padding: 3px 8px 3px;
  margin: 8px 0 0 0;
  @include b-radius(50px);
  background: rgba($color-secondary-d, 0.1);

  i {
    display: inline-block;
    vertical-align: middle;
    margin: 0 0 0 5px;
    transform: translateY(-2px);
  }
}

.user-role-info {
  display: block;
  font-size: $text-xxx-small;
  color: $color-gray-400;
}

.rec-users-list {
  @include mediaMaxWidth($screen-tablet-p) {
    tr.table-row {
      td {
        height: auto;
        padding-right: 20px;
      }
    }
  }
}

.order-info-panel {
  position: relative;
}

.order-permission {
  position: relative;
  padding: 10px 65px 0 20px;
  margin-bottom: 15px;
  @include b-radius($b-radius-sm);
  border: 1px solid $color-gray-200;
  background: $color-gray-50;

  .checker {
    position: absolute;
    @include centerY();
    right: 15px;
  }

  .btn {
    &.btn-danger {
      background-color: transparent;
      color: $color-danger;
      position: absolute;
      right: 10px;
      top: 12px;
      padding: 0px 3px 3px;
      @include b-radius(50%);
    }
  }

  label {
    font-size: $text-x-small;
  }

  @include mediaMaxWidth($screen-phones) {
    padding: 20px 10px 0 10px;

    .btn {
      &.btn-danger {
        border: none;
        right: unset;
        left: 10px;
        top: 10px;
      }
    }
  }
}

.icon-position-fix {
  position: relative;
  top: 2px;
  vertical-align: unset !important;
  font-size: 17px !important;
  font-weight: unset !important;
}

.users, .users-{
  .page-header:not(.px-15){
    display: none;
  }
}