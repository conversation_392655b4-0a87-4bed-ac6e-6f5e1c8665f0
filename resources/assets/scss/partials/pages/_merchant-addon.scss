.store-addon {
  // section two
  &__item {
    &__title-container {
      @include mediaMaxWidth($screen-phablet) {
        display: block;
      }
    }

    &__avatar {
      width: 65px;
      height: 65px;
      @include b-radius($b-radius-sm);
      background-color: $color-gray-25;
      overflow: hidden;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;

      img {
        max-width: 100%;
      }
    }

    .rating-area {
      margin-bottom: 10px;
    }

    &__meta {
      @include mediaMaxWidth($screen-phablet) {
        display: block;
      }
    }

    &__actions {
      &.media-left {
        padding-left: 0;
      }

      @include mediaMaxWidth($screen-laptop-small) {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        .btn {
          &:first-child {
            margin-right: 0;
          }
        }
      }
    }

    &__buttons {
      .btn {
        min-width: 80px;
        margin-right: 15px;
        @include mediaMaxWidth($screen-laptop-small) {
          flex-grow: 1;
        }
      }

      @include mediaMaxWidth($screen-laptop-small) {
        display: flex;
        width: 100%;
      }
    }

    &--app {
      background-color: $color-white;
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius);
      display: flex;
      flex-direction: column;
    }

    &.private {
      // .addons__item__title {
      // }
      .outside-tag {
        color: $color-white;
        @include b-radius(50%);
        display: inline-block;
        width: 22px;
        height: 22px;
        line-height: 17px;
        text-align: center;
      }
    }

    &.new-update {
      .btn-info {
        padding-bottom: 6px;
        line-height: 1;
      }
    }

    &__body, &__footer {
      padding: 20px;
    }

    &__footer {
      border-top: 1px solid $color-gray-200;
      margin-top: auto;
      @include mediaMaxWidth($screen-phones) {
        > .btn {
          flex-grow: 1;
        }
        .flex-grow-1 {
          flex-grow: unset;
        }
      }
    }

    &__indicator {
      font-size: $text-small;
      color: $color-dark-200;
      margin-right: 20px;
      margin-left: 20px;

      a {
        color: inherit;
      }

      .icon {
        margin-left: 5px;
        font-size: $text-small;
      }

      @include mediaMaxWidth($screen-phablet) {
        margin-right: 12px !important;
        margin-left: 12px !important;
      }
    }

    &__menu-trigger {
      @include mediaMaxWidth($screen-tablet-p) {
        margin-right: 30px;
      }
    }
  }
}