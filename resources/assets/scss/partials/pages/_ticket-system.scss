.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 8px;
  border: 1px solid $color-gray-200;
  border-radius: $b-radius-sm;
  padding: 10px;
  position: relative;
}

.grid-item {
  padding: 5px 10px;
  @include flexable(center, flex-start, row);
}

.grid-container::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: $color-gray-200;
}


.grid-container::after {
  bottom: calc(50% - 1px);
}

@include mediaMaxWidth($screen-phones) {

  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }

  .grid-container::before,
  .grid-container::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background-color: $color-gray-200;
  }

  .grid-container::before {
     top: calc(33.33% - 1px);
  }
    
  .grid-container::after {
    bottom: calc(33.33% - 1px);
  }

  .grid-item {
     padding-right: 0px;
  }
}