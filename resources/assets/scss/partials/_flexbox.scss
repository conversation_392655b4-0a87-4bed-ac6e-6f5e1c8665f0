// the reason to use deffrent flexbox case there are all used through the project with diffrent way and name
.rec {
  &-flex {
    display: flex !important;

    &-row {
      flex-direction: row;
    }

    &-col {
      flex-direction: column;
    }

    &.center {
      align-items: center;
      justify-content: center;
    }

    &.start {
      align-items: flex-start;
      justify-content: flex-start;
    }

    &.end {
      align-items: flex-end;
      justify-content: flex-end;
    }

    &.sp {
      justify-content: space-between;
    }

    &.a-center {
      align-items: center;
    }
  }

  &-flex-col {
    display: flex;
    flex-direction: column;
  }

}

.flexable {
  @include flexable(flex-start, flex-start);

  &--column {
    flex-direction: column;
  }

  &--row {
    flex-direction: row;
  }

  &--end {
    align-items: flex-end;
  }

  &--row-sp {
    justify-content: space-between;
  }

  &--center {
    @include flexable(center, center)
  }

  &--force {
    display: flex !important;
  }
}

// flex classes
.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row {
  flex-direction: row;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-center {
  justify-content: center !important;
}

.align-items-end {
  align-items: flex-end;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-basis-auto {
  flex-basis: auto;
}

.flex-wrap {
  flex-wrap: wrap;
}

@each $num in 5, 10, 15, 20, 25, 30, 35, 40, 50, 60, 70 {
  .gap-#{$num} {
    gap: #{$num}px !important;
  }
}

.flex-1 {
  flex: 1 0 0;
}

  
  