.border-grey-light {
  border-color: $color-gray-200;
}

.text-primary {
  color: $color-primary-l;
}

.color-box-wrapper {
  @include flexable(center, flex-start, row);
  padding: 5px 10px;
  @include b-radius($b-radius-sm);
	background-color: white;
	border: 1px solid $color-gray-200;
  span.box {
    height: 13px;
    width: 13px;
    margin-right: 5px;
    @include b-radius($b-radius-sm);
  }

  &--has-copy {
    padding: 0 5px 0 0;
    .btn {
      margin-right: 10px;
      padding: 0;
      height: 25px;
      width: 25px;
      line-height: 0;
      background-color: transparent;
      border-right: 1px solid $color-gray-200;
      i {
        font-size: $text-xx-small;
      }
    }
  }
}