//colors --
$color-primary: #004956;
$color-primary-d: #00414d;
$color-primary-l: #004D5A;
$color-primary-darker: #1e2f32;

$color-secondary: #76E8CD;
$color-secondary-d: #73E7CC;
$color-secondary-l: #96EDD9;
$color-secondary-50: #BAF3E6;
$color-secondary-25: #CFF7EE;

$color-light: #BBF3E5;
$color-light-d: #A8F0DE;
$color-light-l: #CBF6EB;

$color-brown: #af820a;
$color-brown-dark: #907017;

$color-green: #0d8a67;
$color-green-light: #159c6d;

$color-gold: #ffdb29;

$color-success: #00af6c;
$color-alert: #f8e71c;
$color-info: #5196F3;

$color-warning: #ffaf44;
$color-warning-dark: darken($color-warning, 2%);
$color-warning-2: #ffc62a;
$color-warning-light: #ffc62a;

$color-danger: #f55157;
$color-danger-100:#EA5455;
$color-danger-light:rgba($color-danger, 0.2);
$color-danger-light-100:#FEECEC;
$color-danger-darker: darken($color-danger, 30%);

$color-social-facebook: #3b5998;
$color-social-twitter: #00b6f1;
$color-social-gram: #c32aa3;
$color-social-youtube: #ff0000;
$color-social-in: #007bb6;
$color-social-g-plus: #df4a32;
$color-social-snap: #fffc00;

$color-coupon-active: $color-secondary-d;
$color-coupon-cancelled: $color-danger;
$color-coupon-overdue: #F7CA68;

$color-mahally: #FF5028;

$color-white: #fff;
$color-black: #000;
$color-gray-25: #f8f8f8;
$color-gray-50: #fcfcfc;
$color-gray-55: #fbfbfb;
$color-gray-75: #F7F7F7;
$color-gray-100: #f2f5f7;
$color-gray-200: #eeeeee;
$color-gray-300: #dddddd;
$color-gray-400: #bbbbbb;
$color-gray-500: #cccccc;

$color-dark-50: #777777;
$color-dark-100: #999999;
$color-dark-200: #666666;
$color-dark-250: #555555;
$color-dark-300: #444444;
$color-dark-400: #333333;


// --- plans colors
$color-plan-plus: #764aaf;
$color-plan-team: #55ccfc; // team is actually pro

$color-bright-cyan: #37d5d6;
$color-dark-purple: #36096d;


// breakpoints ---
$max-breakpoint: 2000;
$screen-desktop: 1600px;
$screen-desktop-large: 1920px;
$screen-desktop-small: 1439px;
$screen-laptop: 1200px;
$screen-laptop-small: 1024px;
$screen-tablet-l: 992px;
$screen-tablet-p: 768px;
$screen-phones: 767px;
$screen-phablet: 480px;
$screen-mobile: 320px;

$container-width: 1440px;
$input-max-height: 60px;
$input-min-height: 50px;

$trans-speed: 0.35s;
$box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.07);
$input-height: 36px;

// --- text
$line-height: 1.4;
$base-size: 15px;

$text-small: 14px;
$text-medium: 16px;
$text-large: 20px;
$text-larger: 25px;
$text-giant: 50px;

$text-x-medium: 17px;
$text-xx-medium: 18px;
$text-xxx-medium: 19px;

$text-x-small: 13px;
$text-xx-small: 12px;
$text-xxx-small: 11px;
$text-xxxx-small: 10px;

$text-x-large: 21px;
$text-xx-large: 22px;
$text-xxx-large: 23px;

$text-x-larger: 27px;
$text-xx-larger: 30px;
$text-xxx-larger: 35px;
$text-xxxx-larger: 40px;

$text-x-giant: 60px;
$text-xx-giant: 70px;
$text-xxx-giant: 80px;
$text-xxxx-giant: 90px;

$line-height: 1.4;

// layout ---
$b-radius: 8px;
$b-radius-sm: 4px;
$b-radius-lg: 8px;
$blog_image_height: 165px;

// fonts ---
$font-main: "PingARLT";
$font-sallaIcon: "sallaicons";
$font-awesome: "FontAwesome";

// default direction --- rtl;
$direction: 'rtl';
$left: null;
$right: null;

@if ($direction == 'rtl') {
  $left: left;
  $right: right;
} @else {
  $left: right;
  $right: left;
  $base-size: 15px;
  $text-small: 14px;
  $text-x-small: 13px;
  $text-medium: 16px;
  $text-x-large: 21px;
}