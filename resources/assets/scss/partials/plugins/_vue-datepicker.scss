.mx {
  // input field ---
  &-datepicker {
    width: 100% !important;

    .mx-input-wrapper {
      .form-control {
        padding-right: 40px !important;
      }

      .mx-icon-calendar {
        right: 12px;

        svg {
          display: none;
        }

        &:before {
          content: '\ea37';
          font-family: $font-sallaIcon;
          font-size: $text-x-medium;
          font-style: normal;
          color: $color-gray-300;
        }
      }
    }

    .mx-time-content {
      .mx-scrollbar-track {
        display: none;
      }

      .mx-time-list {
        &:after {
          content: none;
        }
      }
    }
  }

  &--right-position {
    .mx-datepicker-popup {
      left: auto !important;
      right: 0;
      top: 36px !important;
    }
  }

  // calendar popup ---
  &-datepicker-popup {
    border: 1px solid $color-gray-300 !important;
    @include b-radius($b-radius-sm);
    box-shadow: 0 1px 3px rgba(0, 0, 0, .1) !important;

    .mx-calendar {
      padding: 8px;

      .mx-calendar-header {
        .mx-calendar-header-label {
          .mx-btn {
            font-size: $text-small !important;
            color: $color-dark-300 !important;
            padding: 0 3px;

            &[class*="month"] {
              transform: translateY(-2px);
            }
          }
        }

        .mx-btn {
          min-width: 30px;
          height: 100%;
          text-align: center;

          &-icon-left {
            float: right;
            transform: scaleX(-1);
          }

          &-icon-right {
            float: left;
            transform: scaleX(-1);
          }

          &[class*="double"] {
            i {
              &:after {
                display: none;
              }
            }
          }
        }
      }

      &:not([class*="month"]):not([class*="year"]) {
        .mx-btn {
          &[class*="double"] {
            display: none;
          }
        }
      }

      .mx-table {
        th {
          text-align: center;
        }

        .cell {
          padding: 5px 0 !important;

          div {
            font-size: $text-x-small;
          }

          &:hover {
            color: $color-primary;
            background: rgba($color-primary, 0.1);
          }

          &.active {
            color: $color-white;
            background: $color-primary;

            &:hover {
              color: $color-white;;
              background: $color-primary;
            }
          }

          &.today {
            color: $color-primary;
            background: rgba($color-primary, 0.1);
          }
        }
      }
    }
  }
}

.dp {
  &__theme_light {
    --dp-background-color: var(--color-white) !important;
    --dp-primary-color: var(--color-primary-l) !important;
  }

  &__main {
    .dp__input_wrap {
      input {
        padding: 8px 30px 8px 12px;
      }

      .dp {
        &__input {
          border: 1px solid var(--color-gray-200);

          &:hover {
            border-color: var(--color-gray-200);
          }
        }

        &__input_icon {
          left: unset;
          right: 9px;
          color: $color-gray-300;
        }

        &__clear_icon {
          left: 9px;
          right: unset;
          color: var(--color-danger);
        }
      }
    }
  }

  &__outer_menu_wrap {
    direction: ltr;

    .dp {
      &__action_cancel {
        background-color: var(--color-gray-300);
        color: var(--color-dark-200);
        border: 1px solid var(--color-gray-300);
      }

      &__action_select {
        background-color: var(--color-secondary-50);
        color: var(--color-primary-l);
      }
    }
  }
}