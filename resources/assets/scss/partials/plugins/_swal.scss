.swal2-container {
  background: rgba(0,78,92,.5) !important;
  .swal2-modal {
    @include b-radius($b-radius);
    box-shadow: 0 4px 10px 0 rgba($color-black, 0.1);

    .swal2 {
      &-title {
        font-size: 22px;
        font-weight: normal;
        font-family: $font-main, serif;
      }

      &-icon {
        margin: 20px auto 14px;
        font-size: $text-xx-large;
      }

      &-content {
        margin-bottom: 16px;
        font-size: 16px;
        padding: 0 14px;
      }

      &-buttonswrapper {
        margin-top: 0;

        .swal2-styled {
          font-size: $base-size;
          padding: 8px 25px;
          margin-top: 10px;
          @include b-radius($b-radius-sm);

          &:hover, &:focus, &:active {
            outline: none;
            box-shadow: none;
          }
        }
        .btn-danger {
          background: #f5515733 !important;
          color: $color-danger !important;
        }
      }
    }
  }
}

// To fix an issue in the store apps page
.apps .swal2-container {
  display: flex !important;
}

body.dark {
  .swal2-container .swal2-modal {
    background: $color-dark-400 !important;
    & .swal2-title{
      color: $color-white !important;
    }
  & .swal2-content {
    color: $color-white !important;
  }
  }
}