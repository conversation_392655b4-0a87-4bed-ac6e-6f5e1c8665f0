.switchery {
  &.switchery-small {
    width: 30px;
  }

  + label {
    color: $color-gray-400;
    cursor: pointer;
  }

  &:checked {
    + label {
      color: $color-dark-300;
    }
  }

  &.active {
    box-shadow: rgb(87, 212, 196) 0 0 0 10px inset !important;
    border-color: rgb(87, 212, 196) !important;
    background-color: rgb(87, 212, 196) !important;
    small {
      left: 12px !important;
    }
  }
}


.checkbox-switchery {
  &#is_short_link_url-switch {
    label {
      span {
        &.switchery {
          transform: translateY(2px);
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      margin-bottom: 20px;
    }
  }
}