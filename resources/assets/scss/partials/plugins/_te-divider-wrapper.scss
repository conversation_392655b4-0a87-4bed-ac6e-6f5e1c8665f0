.te-divider-wrapper {
  $wrapper-root: &;
  text-align: center;
  @include flexable(center, center, row);
  margin: 0 0 20px;

  &-title {
    font-size: $text-small;
    margin: 0 15px;

    &--bold {
      font-weight: bold;
    }
  }

  &:before,
  &:after {
    content: "";
    display: inline-block;
    flex: 1;
    height: 1px;
    background: $color-gray-200;
  }

  &--small {
    #{$wrapper-root} {
      &-title {
        font-size: $text-x-small;
      }
    }

  }

  &--xl {
    #{$wrapper-root} {
      &-title {
        font-size: $base-size;
      }
    }
  }

  &--xxl {
    #{$wrapper-root} {
      &-title {
        font-size: $text-x-medium;
      }
    }
  }

  &--single {
    .te-divider-wrapper__title {
      justify-content: flex-start;
      font-size: $text-large;
      font-weight: bold;
      color: $color-dark-200;
      margin: 0 0 0 30px;
    }

    &:before {
      content: none;
    }
  }

  &:before {
    content: none;
  }
}
