.vpd {
  &-wrapper {
    background: rgba($color-primary, 0.75) !important;
    z-index: 999999 !important;
  }

  &-content {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
    background: transparent !important;
  }

  &-header {
    background-color: $color-primary !important;
    text-align: right;
    padding: 7px 15px 5px !important;
    @include b-radius($b-radius-sm $b-radius-sm 0 0 !important);

    .vpd-year-label {
      font-size: $base-size;

      > span {
        padding: 3px 5px 8px;
        @include b-radius($b-radius-sm);
        background: rgba(0, 0, 0, 0.35);
        font-size: 14px;
        line-height: 1;

        span {
          line-height: 1;
        }
      }
    }

    .vpd-date {
      font-size: $text-larger;
      line-height: 1 !important;
    }
  }

  &-body {
    @include b-radius(0 0 $b-radius-sm $b-radius-sm !important);
    background: $color-white !important;

    .vpd-controls {
      > * {
        height: 45px;
        line-height: 40px;
        text-align: center;

      }

      .vpd-month-label {
        > span {
          height: 32px;
          font-family: $font-main !important;
          font-weight: bold;
          font-size: $base-size !important;
          color: $color-primary !important;

          &:hover, &:focus, &:active {
            border: none !important;
          }
        }
      }

      button {
        width: 50px;

        svg {
          margin: 0 auto;

          path {
            fill: $color-dark-200;
          }
        }
      }
    }

    .vpd-month {
      .vpd-week {
        font-size: $base-size;
        padding: 0 15px;
        margin-bottom: 15px;
      }

      .vpd-days {
        padding: 0 15px;

        .vpd-day {
          font-size: $text-small;

          .vpd-day-effect {
            @include b-radius(2px);
            transform: scale(1) !important;
            background: $color-primary !important;
          }
        }
      }
    }

    .vpd-addon-list {
      @include scrollBar(2px, $color-primary, $color-gray-200);

      .vpd-addon-list-content {
        padding: 5px 10px;

        .vpd-addon-list-item {
          height: 45px;
          line-height: 32px;
          @include b-radius($b-radius-sm);
          background: $color-gray-25;

          &.selected {
            font-size: $text-small !important;
            color: $color-white !important;
            background: $color-primary;

            &:hover {
              color: $color-white !important;
              background: $color-primary;
            }
          }

          &:hover {
            color: $color-primary-d !important;
            background: rgba($color-primary, 0.2);
          }
        }
      }

      &.can-close {
        margin-top: 50px;
        padding-top: 0;
      }
    }

    .vpd-close-addon {
      width: 25px;
      height: 25px;
      line-height: 24px;
      @include b-radius(50%);
      background: $color-danger;
      color: transparent;
      top: 10px;
      right: 10px;
      left: unset;

      &:after {
        content: '\e945';
        font-family: $font-sallaIcon;
        font-size: $text-xx-small;
        color: $color-white;
        position: absolute;
        top: 1px;
        right: 6px;
      }
    }
  }
}

// Input Style
.vpd-input-group {
  .vpd-icon-btn {
    background-color: transparent !important;
    border: 1px solid $color-gray-200;
    border-left: none;
    @include b-radius(0 2px 2px 0);
    padding: 4px 12px;

    svg {
      display: none;
    }

    &:before {
      content: '\ea37';
      font-family: $font-sallaIcon;
      color: $color-gray-300;
      font-size: $text-x-medium;
    }
  }

  input {
    border-color: $color-gray-200 !important;
    padding-right: 0 !important;
  }
}
