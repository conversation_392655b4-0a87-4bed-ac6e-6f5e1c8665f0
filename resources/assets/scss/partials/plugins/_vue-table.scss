.vuetable-body-wrapper {
  overflow-x: auto;

  .vuetable {
    &.ui.table {
      overflow: visible;
      text-align: right;
      border: none;
      white-space: nowrap;

      thead, tbody {
        tr {
          th {
            border-top: 1px solid $color-gray-200 !important;
            border-bottom: 1px solid $color-gray-200 !important;
          }

          > * {
            font-family: $font-main;
            font-size: $text-x-small;
            font-weight: normal;

            &[class$="-center"] {
              text-align: center;
            }

            &[class$="-sticky"], &:first-of-type {
              @include mediaMinWidth($screen-phones) {
                min-width: 300px;
                position: sticky;
                right: 0;
                z-index: 999;
                white-space: normal;
                box-shadow: -2px 3px 8px 0 rgba($color-dark-300, 0.1);
              }
              @include mediaMaxWidth($screen-phones) {
                min-width: 200px;
                white-space: normal;
              }
            }

            .vuetable-td-name {
              padding: 0 15px;

              &.sku-product {
                padding-right: 30px;
              }
            }
          }

          .vuetable-td-weight {
            text-align: center;
          }

        }
      }

      thead {
        tr {
          // header custom styling ---
          th {
            background: $color-gray-50 !important;
            white-space: nowrap;
            padding: 10px 15px !important;
          }
        }
      }

      tbody {
        tr {
          td {
            position: relative;
            padding: 0;

            &.vuetable-td-name-sticky,
            &.vuetable-empty-result,
            &.vuetable-td-sku {
              padding: 10px 15px;
            }

            &.vuetable-empty-result {
              text-align: center;
              color: $color-dark-100;

              &:hover {
                background-color: unset;
              }
            }

            .img-wrapper {
              width: 85px;
              max-width: 85px;

              img {
                width: 40px;
                height: 40px;
                object-fit: cover;
                display: block;
                margin: 5px auto;
                border: 1px solid $color-gray-200;
                @include b-radius(2px);
                overflow: hidden;

              }
            }

            .form-group {
              .form-control {
                width: 100%;
                border: 0;
                font-size: $text-xx-small;
                padding: 5px 10px 7px;
                height: $input-height;
                @include b-radius(0);
                @include transi();
                background: transparent;

                &:hover, &:focus {
                  border-color: $color-primary;
                }

                &[type="number"] {
                  max-width: 100%;
                  margin: 0 auto;
                }

                &[type="text"] {
                  min-width: 180px;
                }

                &.sku-input {
                  width: 120px;
                  max-width: 120px;
                  min-width: auto;
                }

                &._parseArabicNumbers {
                  min-width: unset;
                }
              }

              .input-group {
                width: 100%;

                input {
                  width: 50px;
                }

                > * {
                  width: 100%;
                  max-width: 50px;
                }

                .bootstrap-select {
                  min-width: 70px;
                }
              }
            }

            &.tcs-selected,
            &:hover:not([class$="-sticky"]) {
              background-color: $color-gray-200; //colorAvilabilty

              .form-control, .input-group-addon {
                border-color: $color-primary;
              }
            }

            &.tcs-selected {
              box-shadow: inset 0 0 0 1px $color-primary;
              position: relative;

              &.is-first-selected:after {
                content: '';
                width: 5px;
                height: 5px;
                position: absolute;
                left: 0;
                bottom: 0;
                background-color: $color-primary;
                box-shadow: 0 0 0 1px $color-white;
                cursor: ns-resize;
              }
            }

            .v-select, .multiselect {
              min-width: 180px;
              max-width: 180px;
            }

            .vue-treeselect__control {
              border: 0;
              background-color: transparent;
              height: 41px;
            }

            .vue-treeselect {
              min-width: 250px;
              max-width: 250px;

              &__menu {
                border: 0;
                box-shadow: 0 5px 5px 3px rgba($color-black, 0.05);
              }

              &__placeholder {
                line-height: 38px;
              }
            }

            .multiselect {
              padding: 5px 10px 5px 0;

              &__tags-wrap {
                width: 80%;
              }
            }

            button.btn {
              border: 0;
              box-shadow: none;
              @include b-radius(0);
              background-color: transparent;
            }

            .bootstrap-select--full {
              border-right: 0 !important;
              border-left: 0 !important;
            }

            .v-select {
              border-right: 1px solid $color-gray-200;

              ul {
                width: 100%;
                position: absolute;
                z-index: 555555555;
                left: 0 !important;
              }
            }

            @include mediaMaxWidth($screen-phones) {
              padding: 5px 8px !important;
            }
          }
        }
      }

      &.stripped {
        > tbody {
          > tr {
            &:nth-child(2n) {
              background-color: $color-gray-50;
            }
          }
        }
      }
    }
  }

  // fix safari issue ---
  &.safari {
    .vuetable {
      &.ui.table {
        max-width: initial;

        thead, tbody {
          tr > *[class$=-sticky], tr > .vuetable-slot:first-of-type {
            width: 300px;
            left: 0;
            right: auto;

            &[class$="-sticky"], &:first-of-type {
              @include mediaMinWidth($screen-phones) {
                right: unset;
              }
              @include mediaMaxWidth($screen-phones) {
                min-width: 200px;
                white-space: normal;
              }
            }

            @include mediaMaxWidth($screen-phones) {
              width: 200px;
              white-space: normal;
            }
          }
        }
      }
    }
  }
}

.bulk-edit-table {
  width: 100%;
  scroll-behavior: smooth;
  @include scrollBar(1px, $color-primary, $color-gray-200);

  .vuetable-body-wrapper {
    .vuetable {
      &.ui.table {

        thead, tbody {
          tr {
            > * {
              &[class$="-sticky"], &:first-of-type {
                @include mediaMinWidth($screen-phones) {
                  min-width: 300px;
                  position: sticky;
                  right: 0;
                  background: $color-white;
                  z-index: 98;
                  white-space: normal;
                  box-shadow: -2px 3px 8px 0 rgba($color-dark-300, 0.1);
                }
                @include mediaMaxWidth($screen-phones) {
                  min-width: 200px;
                  white-space: normal;
                }
              }
            }
          }
        }
      }
    }
  }

  &.quantities {
    .vuetable-body-wrapper {
      .vuetable {
        thead tr th:not(:nth-child(-n+2)) {
          text-align: center;
        }
      }
    }
  }

  &.quantity-log {
    .vuetable-body-wrapper {
      .vuetable.ui.table tbody tr td {
        padding: 10px 15px
      }
    }
  }
}

.v-pagination {
  border-top: 1px solid $color-gray-200;
}
