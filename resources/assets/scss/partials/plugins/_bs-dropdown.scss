.dropdown-menu {
  &.custom-top {
    top: 63% !important;
  }

  &.bordered-items {
    li {
      &:not(:last-child) {
        a {
          border-bottom: 1px solid #F8F8F8;
        }
      }
    }
  }

  &.dropdown-menu-right {
    &.mobile-enhanced {
      @media (max-width: 7607px) {
        left: -152%;
      }
    }
  }

  .dropdown-menu.inner {
    max-height: 240px !important;

    li {

      &.selected,
      &.active {
        a {
          &:hover {
            color: $color-primary-l !important;
            background-color: rgba($color-secondary-50, 0.5) !important;

            * {
              color: $color-primary-l !important;
            }
          }
        }
      }
    }
  }

  .bs-searchbox {
    .form-control {
      border-right: 1px solid $color-gray-200 !important;
    }
    &:after {
      content: "\ef09";
      font-family: $font-sallaIcon;
    }
  }

  &--products-type {
    width: 282px;
    padding: 0;
    top: 4.7rem;

    li {

      a {
        @include flexable(center, initial, row);

        i {
          color: $color-gray-400;
          margin-left: 16px;
          font-size: $text-large;
        }

        h6 {
          font-size: $text-x-small;
          color: $color-dark-300; // colorAvilabilty
          display: inline-block;
          margin: 0;

          span {
            display: block;
            font-size: $text-xx-small;
            font-weight: normal;
            color: $color-dark-100;
            white-space: normal;
          }
        }

        &.dropdown-link--template {
          border-top: 1px solid $color-gray-200;
          background-color: rgba($color-gray-200, 0.25);
        }
      }
    }
  }

  // customize button to fix safari issue ---
  > li button {
    // bootstrap code ---
    display: block;
    line-height: 1.5384616;
    white-space: nowrap;
    border: none;
    background: transparent !important;
    outline: none !important;
    padding: 11px 15px;
    width: 100%;
    text-align: right;
  }
}