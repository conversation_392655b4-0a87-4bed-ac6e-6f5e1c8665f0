.filepond {
  $filepond-root: &;

  &--root {
    height: auto;
  }

  &--panel-root,
  &--item-panel {
    @include b-radius($b-radius-sm !important);
  }

  &--item-panel {
    background-color: $color-dark-300;
  }

  &--file {
    color: $color-white;
  }

  &--credits {
    display: none !important;
  }

  &--drip-blob {
    background-color: $color-dark-100;
  }

  &--drop-label {
    color: $color-dark-100;
    font-family: $font-main;
    font-size: $text-small !important;

    label {
      font-size: $text-small;
      position: relative;

      span, small {
        display: block;
      }

      span {
        font-size: $text-xx-small;
        color: $color-dark-100;
        text-decoration: none;
      }

      small {
        font-size: $text-x-small;
      }
    }
  }

  &--file-action-button {
    cursor: pointer;
    color: $color-white;
    background-color: rgba(0, 0, 0, 0.5);

    &:hover, &:focus {
      box-shadow: 0 0 0 0.125em rgba(255, 255, 255, 0.9);
    }
  }

  &--label-action {
    text-decoration-color: $color-gray-400;
    outline: none !important;
  }

  &#brandBannerUpload {
    height: 150px;
    margin: 0;
  }

  &#brandLogoUpload {
    margin: 0;
  }
}

.filepond--file-poster {
  pointer-events: none;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 100%;
  margin: 0;
  @include b-radius(0.45em);
  overflow: hidden;
  background: rgba(0, 0, 0, 0.01);
}

[data-filepond-item-state*='error'] .filepond--item-panel,
[data-filepond-item-state*='invalid'] .filepond--item-panel {
  background-color: red; //colorAvilabilty
}

[data-filepond-item-state='processing-complete'] .filepond--item-panel {
  background-color: green; //colorAvilabilty
}
