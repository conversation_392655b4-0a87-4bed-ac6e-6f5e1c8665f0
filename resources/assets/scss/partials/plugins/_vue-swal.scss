.swal2{
  &-popup {
    &.swal2-modal {
      width: 400px;
      max-width: 94%;
      padding: 44px 30px;
  
      .swal2-title {
        font-size: 18px;
        color: $color-primary;
      }
  
      .swal2-content {
        font-size: $text-small;
        color: $color-dark-300;
      }
  
      .swal2-styled {
        outline: none;
        transition: 0.3s;
        font-size: $text-small;
        @include b-radius(1px);
        box-shadow: none !important;
  
        &.swal2-confirm {
          background-color: $color-primary;
          color: $color-white;
  
          &:hover {
            background-color: $color-primary-d;
          }
        }
  
        &.swal2-cancel {
          background-color: $color-dark-100;
          color: $color-white;
  
          &:hover {
            background-color: $color-dark-200;
          }
        }
      }
    }
  
    &.swal2-icon-success {
      .swal2-title {
        color: $color-primary;
      }
    }
  
    &.swal2-icon-error {
      .swal2-title {
        color: $color-danger;
      }
    }
  
    // Caclulate letters in translate modal -------
    &.calculate-popup {
      width: 500px;
      max-width: 90%;
      padding: 40px 20px 30px;
  
      .swal2-header {
        .swal2-icon {
          &.swal2-success:after {
            content: "\ea26";
          }
  
          &:before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            @include b-radius(100%);
            border: 2px solid;
            border-color: $color-gray-200 $color-gray-200 $color-gray-200 $color-secondary;
            animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
          }
        }
      }
  
      .message-text {
        color: $color-dark-100;
      }
  
      .letters-calc {
        font-size: $text-x-small;
        color: $color-dark-100;
        margin-top: 5px;
  
        b {
          font-size: $text-large;
          color: $color-primary;
        }
  
        .unit-text {
          opacity: 0;
          transition: 0.3s;
          transform: translateX(20px);
          display: inline-block;
  
          &.showed {
            transform: translateX(0);
            opacity: 1;
          }
        }
      }
  
      .swal2-actions {
        &.swal2-loading {
          order: -1;
          margin: 0;
        }
      }
    }
  
    // Balence Info Popup ------------------------
    &.balance-info-popup {
      width: 500px;
      max-width: 90%;
      padding: 40px 20px 20px;
  
      .swal2-header {
        .swal2-icon.swal2-question:after {
          content: "\e96f";
        }
      }
  
      strong {
        font-weight: bold;
      }
  
      .alert-banlance-info {
        margin-top: 30px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
  
        .label {
          font-size: $text-x-small;
          color: $color-dark-300;
        }
  
        .value {
          font-weight: bold;
          font-size: $text-xx-large;
          line-height: 1;
        }
  
        .balance-data {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
        }
      }
  
      .swal2-actions {
        display: flex;
        margin: 20px -5px 0;
        padding: 0;
  
        .swal2-confirm {
          flex-grow: 1;
        }
  
        @media (min-width: 400px) {
          .swal2-cancel {
            flex: 0 0 140px;
          }
        }
      }
    }
  
    &.swal2-toast.toast-without-icon {
      padding: 1.2em 1.25em 1.4em;
      min-width: 155px;
  
      .swal2-icon {
        display: none !important;
      }
  
      .swal2-title {
        font-size: $text-x-small;
        font-weight: normal;
      }
    }
  }
  &-actions {
    .swal2-loader {
      border-color: $color-gray-200 $color-gray-200 $color-primary $color-primary;
      width: 25px;
      height: 25px;
    }
  }
  // Custom Success icon
  &-header {
  .swal2-icon {
    background-color: $color-dark-100;
    box-shadow: 0 0 0 7px $color-white, 0 0 0 8px $color-dark-100;
    @include b-radius(50%);
    width: 95px;
    height: 95px;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 30px auto;

    > div,
    > span {
      display: none !important;
    }

    &:after {
      font-family: $font-sallaIcon !important;
      content: "\ea9d";
      font-size: $text-xxxx-larger;
      color: $color-white;
    }

    &.swal2-success {
      background-color: $color-primary;
      box-shadow: 0 0 0 7px $color-white, 0 0 0 8px $color-primary;

      &:after {
        content: "\ea9d";
      }
    }

    &.swal2-error {
      background-color: $color-danger;
      box-shadow: 0 0 0 7px $color-white, 0 0 0 8px $color-danger;

      &:after {
        content: "\f097";
      }
    }

    &.swal2-question {
      background-color: #87adbd; //colorAvilability 
      box-shadow: 0 0 0 7px $color-white, 0 0 0 8px #87adbd;

      &:after {
        content: "\ece0";
      }
    }

    &.swal2-info {
      background-color: #3fc3ee; //colorAvilabilty
      box-shadow: 0 0 0 7px $color-white, 0 0 0 8px #3fc3ee;

      &:after {
        content: "\ecdf";
      }
    }
  }
  }

}

body.swal2-toast-shown.swal2-shown {
  overflow-y: auto;
}



