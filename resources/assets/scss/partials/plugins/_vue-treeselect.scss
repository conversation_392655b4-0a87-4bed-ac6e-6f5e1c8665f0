.vue-treeselect { // @osama - needs revisit ---
  $vue-treeselect-root: &;

  &:focus, &:active {
    outline: none;
    box-shadow: none;
  }

  &__menu {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      // box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      background-color: darkgrey;
      outline: 1px solid slategrey;
    }
  }

  &__input {
    cursor: pointer;
  }

  &__control-arrow-container {
    width: 30px !important;

    svg {
      display: none;
    }

    &:before {
      content: '\e96e';
      font-family: $font-sallaIcon;
      font-size: $base-size;
      color: $color-dark-100;
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) {
    #{$vue-treeselect-root} {
      &__control {
        &:hover {
          border-color: $color-primary !important;
        }
      }
    }
  }

  body &__control {
    @include b-radius(2px);
    border-color: $color-gray-200 !important;
    font-size: $text-x-small;
    cursor: pointer;

    &:focus, &:active {
      outline: none;
      box-shadow: none;
    }
  }

  .vue-treeselect--multi &__input {
    cursor: pointer;
    border-width: 0 !important;
  }

  &__input {
    cursor: pointer;
  }

  &--open {
    .vue-treeselect__input {
      cursor: text;
    }
  }

  body &__list-item {
    #{$vue-treeselect-root} {
      &__label {
        position: relative;

        &:after {
          display: none;
          content: '\ea9d';
          font-family: $font-sallaIcon;
          font-size: $text-x-small;
          color: $color-primary-d;
          opacity: 0.5;
          vertical-align: middle;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 10px;
        }
      }

      &__option {
        &--highlight {
          background-color: rgba($color-secondary-50, 0.15);
        }

        &--selected {
          background-color: rgba($color-secondary-50, 0.15);

          &:hover {
            background-color: rgba($color-secondary-50, 0.50);
          }

          #{$vue-treeselect-root} {
            &__label {
              color: $color-primary-l;

              &:after {
                display: inline-block;
              }
            }
          }
        }
      }
    }
  }

  body &__multi-value {
    margin-bottom: 1px !important;

    &-item {
      background: rgba($color-primary, 0.1);
      color: $color-primary-d;
    }

    .vue-treeselect__value-remove {
      color: $color-primary-d;
    }
  }

  body &__checkbox-container {
    padding-right: 15px;
  }


  body &__checkbox--indeterminate {
    background-color: $color-primary;
    border-color: $color-primary;
  }

  body &__control {
    @include b-radius(2px);
    border-color: $color-gray-200 !important;
    font-size: $text-x-small;
    cursor: pointer;

    &:focus, &:active {
      outline: none;
      box-shadow: none;
    }
  }

  body &__checkbox-container,
  body &__label-container {
    &:hover .vue-treeselect__checkbox--unchecked {
      border-color: $color-primary;
    }
  }

  body &__label-container:hover .vue-treeselect__checkbox--checked,
  body &__checkbox--checked {
    background-color: $color-primary;
    border-color: $color-primary;
  }

  &--open {
    .vue-treeselect__input {
      cursor: text;
    }
  }

  &--custom,
  &--append-to-body {
    #{$vue-treeselect-root} {
      &__control {
        @include b-radius(2px);
        border-color: $color-gray-200 !important;
        font-size: $text-small;
        line-height: 30px;
        cursor: pointer;
        border-right: none;
        padding-right: 6px;
        padding-left: 0;
        box-shadow: none !important;

        #{$vue-treeselect-root} {
          &__x-container {
            display: none;
          }
        }
      }

      &__placeholder {
        color: $color-dark-100 !important;
        font-size: 12px;
      }

      &__menu-container {
        z-index: 99999999999 !important;
        cursor: pointer;
        box-shadow: none !important;
        outline: none !important;

        #{$vue-treeselect-root} {
          &__x-container {
            display: none;
          }
        }
      }

      &__menu {
        border-color: $color-gray-200 !important;
        padding: 0;
      }

      &__value-container {
        overflow: hidden;
        padding-right: 0;
      }

      &__single-value {
        font-size: $text-x-small;
        padding-right: 0;
      }

      &__input {
        border: none !important;
        padding: 0 !important;
        height: 27px !important;
      }

      &__multi-value {
        white-space: nowrap;
        cursor: pointer;
        margin-bottom: 1px !important;

        &-item {
          background: rgba($color-primary, 0.1);
          color: $color-primary-d;
          margin-left: 5px;
          padding: 4px 0;

        }

        .vue-treeselect__value-remove {
          color: $color-primary-d;
        }
      }

      &__multi-value-label {
        padding: 0 4px;
        white-space: nowrap;
      }

      &__multi-value-item-container {
        padding-left: 0 !important;
        padding-top: 2px !important;
        vertical-align: middle;

        &:after {
          content: '،';
          display: inline-block;
          vertical-align: baseline;
        }

        &:nth-last-child(n-1) {
          &:after {
            display: none;
          }
        }
      }

      &__multi-value-item {
        padding: 0;
        background: transparent !important;
        border: none !important;
        color: $color-dark-300;
        @include b-radius(0);
      }

      &__value-remove {
        display: none;
      }

      &__control-arrow-container {
        position: relative;

        svg {
          display: none;
        }

        &:before {
          content: '\e96e';
          font-family: $font-sallaIcon;
          font-size: $base-size;
          color: $color-dark-100;
          position: absolute;
          left: 50%;
          top: 52%;
          transform: translateY(-50%) translateX(-50%);
        }
      }

      &__list-item {
        #{$vue-treeselect-root} {
          &__option {
            padding: 0 !important;
            @include transi(background);
            cursor: pointer;

            #{$vue-treeselect-root} {
              &__option-arrow-placeholder {
                display: none;
              }

              &__checkbox-container {
                display: none;
              }

              &__label {
                display: block;
                position: relative;
                padding: 4px 10px !important;
                min-height: 36px;
                font-size: $text-xx-small;
                line-height: 28px;
                margin: 0;

                &-disabled{
                  cursor: not-allowed;
                }

                &:after {
                  display: none;
                  content: '\ea9d';
                  font-family: $font-sallaIcon;
                  font-size: $text-xx-small;
                  color: #2e6f66; //colorAvilabilty
                  opacity: 0.5;
                  vertical-align: middle;
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                  left: 10px;
                }
              }
            }

            &-arrow-container {
              display: none;
            }
          }

          &__list {
            #{$vue-treeselect-root} {
              &__label {
                padding-right: 30px !important;
                font-size: $text-x-small;

                &:before {
                  content: '';
                  display: inline-block;
                  width: 12px;
                  height: 1px;
                  background: $color-dark-100;
                  @include b-radius(1px);
                  position: absolute;
                  right: 12px;
                  top: 50%;
                  transform: translateY(-50%);
                }
              }
            }
          }

          &__indent-level-2 {
            #{$vue-treeselect-root} {
              &__label {
                padding-right: 35px !important;

                &:before {
                  content: '';
                  display: inline-block;
                  width: 18px;
                  height: 1px;
                  background: $color-dark-100;
                  @include b-radius(1px);
                  position: absolute;
                  right: 12px;
                  top: 50%;
                  transform: translateY(-50%);
                }
              }
            }
          }

          &__no-children-tip {
            display: none;
          }
        }
      }

      &--open {
        &__control {
          border-color: $color-gray-200 !important;
        }
      }
    }
  }

  &--overflow-x {
    #{$vue-treeselect-root} {
      &__control {
        #{$vue-treeselect-root} {
          &__value-container{
            overflow-x: unset;
          }
        }
      }
    }
  }

  &--enhanced-tags,
  &--append-to-body {
    #{$vue-treeselect-root} {
      &__multi-value {
        white-space: nowrap;
        cursor: pointer;
        margin-bottom: 1px !important;

        &-item-container {
          line-height: 1.5;
          padding: 0 0 0 5px !important;
        }

        &-item {
          background: rgba($color-secondary-50, 0.25) !important;
          color: $color-primary-l;
          margin-left: 0;
          padding: 2px 0;
          @include b-radius($b-radius-sm)
        }

        .vue-treeselect__value-remove {
          color: $color-primary-l;
        }
      }

      &__value-remove {
        display: table-cell;
      }

      &__multi-value-label {
        padding: 0 5px;
        white-space: nowrap;
      }
    }
  }

  &--with-icon {
    #{$vue-treeselect-root} {
      &__control {
        @include b-radius(2px 0 0 2px);
        border-right: none;
      }
    }
  }

  &--disabled {
    #{$vue-treeselect-root} {
      &__control {
        cursor: not-allowed !important;

      }
    }
  }

  &--with-icon {
    #{$vue-treeselect-root} {
      &__control {
        @include b-radius(2px 0 0 2px)
      }
    }
  }

  &--full {
    #{$vue-treeselect-root} {
      &__control {
        border-right: 1px solid $color-gray-200;
      }
    }
  }

  &--without-effect {
    &:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) {
      #{$vue-treeselect-root} {
        &__control {
          &:hover {
            border-color: $color-gray-200 !important;
          }
        }
      }
    }
  }

  &--flat-style,
  &--append-to-body {
    #{$vue-treeselect-root} {
      &__menu {
        border-color: $color-gray-200;
      }

      &__control {
        @include b-radius(0);
      }

      body &__checkbox-container {
        padding-right: 10px;
      }

      &__input {
        font-size: $text-small;
      }

      &__option {
        padding: 0 !important;
        font-size: $text-x-small;
      }

      &__label {
        text-align: right;
        padding: 5px 15px !important;
      }

      &__single-value {
        padding: 0 10px;
        font-size: $text-small;
      }

      &__indent-level-2 {
        .vue-treeselect__label-container {
          padding-right: 10px;
        }
      }
    }
  }

  &--lang-list {
    #{$vue-treeselect-root} {
      cursor: pointer;

      &__input {
        padding-right: 31px;
      }

      &__label {
        padding: 5px 5px 5px 15px !important;

        &--with-icon {
          #{$vue-treeselect-root} {
            &__control {
              border-right: none;
              @include b-radius(2px 0 0 2px)
            }
          }
        }

        &::after {
          content: none !important
        }
      }

      &__label-container {
        padding-right: 10px;
      }

      &__checkbox-container {
        width: 25px;
      }

      &__multi-value {
        .vue-treeselect__input {
          padding-right: 0
        }
      }
    }

    &.has-error {
      .vue-treeselect__control {
        border-color: $color-danger !important;
      }
    }

    .language-list_item {
      margin: 0;
      cursor: pointer;
      display: flex;
      align-items: center;

      img {
        margin-left: 5px;
      }
    }

    &.vue-treeselect--multi {
      .language-list_item {
        padding-right: 5px;
      }
    }

    .flag {
      margin-left: 8px;
    }

    .language-value {
      display: flex;
      align-items: center;

      img {
        margin-left: 10px;
      }
    }
  }

  &--mini-select {
    width: 290px;

    #{$vue-treeselect-root} {
      &__control {
        border: 0;
        padding: 0;
      }

      body &__checkbox-container {
        padding-right: 15px;
      }

      &__menu {
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.09) !important;
        border: none;
      }

      &__list-item {
        border-bottom: 1px solid $color-gray-25;
      }

      body &__list-item {
        #{$vue-treeselect-root} {
          &__option {
            &-arrow-container,
            &-arrow-placeholder {
              display: none;
            }

            &--selected {
              background-color: transparent;

              &:hover {
                background-color: transparent;
                opacity: 0.8;
              }

              .vue-treeselect__label:after {
                content: '';
              }
            }
          }
        }
      }

      &__multi-value {
        &-item-container:nth-of-type(3) > div,
        &-item-container:nth-child(n + 4) {
          display: none;
        }

        &-item-container:nth-child(3) {
          &:before {
            content: '...';
          }
        }
      }
    }
  }

  &--mini-select-bg {
    #{$vue-treeselect-root} {
      &__control {
        background-color: $color-gray-25;
        border: 1px solid $color-gray-300;
        padding: 0 6px;
      }

      &__menu {
        width: 290px;
        // max-width: 90%;
      }

      body &__multi-value-item {
        @include b-radius($b-radius);
        padding: 2px 4px;
      }
    }

    &.primary {
      #{$vue-treeselect-root} {
        &__control {
          background-color: $color-primary;
          border: none;
        }

        &__placeholder {
          color: $color-white;
        }

        &__control-arrow-container {
          &:before {
            color: $color-white;
          }
        }
      }
    }
  }


  &--with-icon {
    position: initial !important;

    #{$vue-treeselect-root} {
      &__control {
        border-right: none;
        padding-right: 0;
      }
    }
  }

  &--has-error {
    &:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) {
      #{$vue-treeselect-root} {
        &__control {
          border-color: $color-danger !important;

          &:hover {
            border-color: $color-danger !important;
          }
        }
      }
    }
  }

  &--initial {
    position: initial !important;
  }

  &--disabled {
    #{$vue-treeselect-root} {
      &__multi-value-item-container {
        padding-top: 2px;
        @include b-radius($b-radius-sm);
      }
    }
  }

  &:focus, &:active {
    outline: none;
    box-shadow: none;
  }

  .vue-treeselect--multi &__input {
    cursor: pointer;
    border-width: 0 !important;
  }

  &--single-select-tag {
    #{$vue-treeselect-root} {
      &__control {
        #{$vue-treeselect-root} {
          &__value-container{
            display: inline-block !important;
            // width: fit-content;
            #{$vue-treeselect-root}{
              &__single-value{
                color: #004D5A;
                font-size: 12px;
                background: rgba(186, 243, 230, 0.25);
                padding: 2px 5px;
                border-radius: 4px;
                max-height: 22px;
                height: 100%;
                line-height: 18px;
                top: 50%;
                transform: translateY(-50%);
                width: fit-content;
                display: flex;

                .value{
                  padding-left: 5px;
                }

                .clear {
                  border-right: 1px solid #FFF;
                  padding-right: 5px;
                  pointer-events: auto;
                  cursor: pointer;
                  img{
                    transform: translateY(-1px);
                    width: 6px;
                    height: 6px;
                  }
                  &:hover img{
                    filter: invert(45%) sepia(92%) saturate(6035%) hue-rotate(345deg) brightness(95%) contrast(88%);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}