.multiselect {
  width: 100%;
  height: 36px;
  border: 1px solid $color-gray-200;
  background-color: $color-white;
  cursor: pointer;
  @include b-radius($b-radius-sm);

  &:focus {
    outline: none;
  }

  &__placeholder {
    font-size: $text-xx-small;
    color: $color-dark-100;
  }

  &__tags-wrap {
    padding-left: 20px;
    overflow: hidden;
  }

  &__tag {
    display: inline-block;
    @include b-radius($b-radius-sm);
    padding: 0 8px;
    background-color: rgba($color-secondary-50, 0.25);
    margin: 0 0 0 6px;

    span {
      color: $color-primary-l;
      font-size: $text-xx-small;
    }
  }

  &__content-wrapper {
    position: absolute;
    width: 100%;
    top: 100%;
    z-index: 99999;
    right: 0;
    margin-top: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, .07);
    min-width: 100%;
    max-height: 300px;
    overflow-y: auto;
    @include b-radius(0 0 $b-radius-sm $b-radius-sm);

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      width: 100%;
      background-color: $color-white;

      li {
        > span {
          padding: 8px 15px;
          display: block;
          position: relative;
          @include transi();

          span {
            font-size: $text-small;
            color: $color-dark-300
          }

          &.multiselect__option--selected {
            background-color: rgba($color-secondary-50, 0.25);

            span {
              color: $color-primary-l;
            }

            &:hover {
              background-color: rgba($color-gray-100, 0.35);

              span {
                color: $color-primary-l
              }
            }

            &:after {
              content: '\ea9d';
              font-family: $font-sallaIcon;
              position: absolute;
              left: 7px;
              @include centerY;
              font-size: $text-xxx-small;
              color: $color-primary-l
            }
          }
        }

        &:hover {
          > span {
            color: $color-primary-l;
            background-color: rgba($color-secondary-50, 0.25);
          }
        }
      }
    }
  }

  &__select {
    position: relative;

    &:before {
      content: '\e96e';
      font-family: $font-sallaIcon;
      color: $color-gray-400;
      font-size: $base-size;
      position: absolute;
      top: 6px;
      left: 10px;
    }
  }

  &-category {
    ul li span {
      &.multiselect__option {
        span {
          padding-right: 6px;

          &:before {
            content: '';
            height: 1px;
            width: 10px;
            display: inline-block;
            background-color: $color-dark-300;
            margin-left: 4px;
            position: relative;
            top: -3px;
          }
        }

        &--group {
          span {
            padding: 0;

            &::before {
              content: none;
            }
          }
        }
      }
    }
  }

  &-vue {
    padding-right: 0;
    padding-left: 0;
    overflow: visible;
    cursor: pointer;
    position: relative;

    &:focus {
      outline: none !important;
    }

    .multiselect__placeholder {
      font-size: $text-x-small;
      color: $color-dark-300
    }

    .multiselect__tags {
      height: 36px;
      padding: 6px 12px 6px 30px;
      overflow: hidden;
      white-space: nowrap;

      &:focus {
        outline: none !important;
      }
    }

    .multiselect__content-wrapper {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.07);

      ul {
        list-style: none;
        margin: 0;
        padding: 0;
        background-color: $color-white;
        width: 100%;
        overflow-y: auto;

        li {
          .multiselect__option {
            display: block;
            min-height: 34px;
            padding: 8px 15px 8px 12px;
            font-size: $text-small;
            color: $color-dark-300;

            &:hover {
              background-color: rgba($color-gray-100, 0.25) !important;
            }

            &--selected {
              color: $color-primary-l;
              background-color: rgba($color-secondary-50, 0.35);

              &:hover {
                color: $color-primary-l !important;
                background-color: rgba($color-secondary-50, 0.45) !important;
              }
            }
          }
        }
      }
    }

    &--has-error {
      .multiselect__tags {
        border-color: $color-danger !important;
      }
    }

    &--with-prefix {
      .multiselect__tags {
        padding-right: 40px;
      }

      + i {
        position: absolute;
        top: 10px;
        right: 14px;
        font-size: $text-x-medium;
        color: $color-gray-300;
      }
    }
  }
}
