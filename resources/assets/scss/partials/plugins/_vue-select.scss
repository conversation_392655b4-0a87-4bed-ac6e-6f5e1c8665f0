.v-select {
  position: relative;
  width: 100%;

  .vs__selected {
    position: relative;
    top: 7px;
    right: 7px;
    color: $color-dark-300;
    font-size: $text-x-small;
    direction: rtl;
    white-space: nowrap;
    overflow: hidden;
    padding-left: 30px;
    text-overflow: ellipsis;
    display: block;

    .selected {
      direction: rtl;
      white-space: nowrap;
      overflow: hidden;
      padding-left: 30px;
      text-overflow: ellipsis;
      color: $color-dark-300;
    }
  }

  .vs__selected-options {
    position: relative;
    width: 100%;
    height: 36px;
    border: 1px solid $color-gray-200;
    border-right: none;
    text-align: right;
    background-color: $color-white;
    cursor: pointer;

    input {
      width: 0;
      height: 0;
      border: none;
      padding: 0;
    }

    &:after {
      content: "\e96e";
      font-family: $font-sallaIcon;
      color: $color-dark-100;
      font-size: $base-size;
      position: absolute;
      left: 10px;
      @include centerY;
    }
  }

  &--full {
    .vs__selected-options {
      border-right: 1px solid $color-gray-200 !important;
    }
  }

  &--initial {
    position: initial !important;
  }

  &--with-icon {
    .vs__selected-options {
      border-right: none !important;
    }
  }

  .vs__deselect {
    visibility: hidden;
  }

  .vs__actions {
    display: none;
  }

  @include mediaMaxWidth(375px) {
    position: initial;
  }

  &--full {
    .vs__selected-options {
      border-right: 1px solid $color-gray-200;
    }
  }

  &--with-icon {
    .vs__selected-options {
      border-right: none !important;
    }
  }

  &--deselect {
    .vs__selected {
      padding-left: 13px;

      .vs__deselect {
        visibility: visible;
        background-color: transparent;
        border: none;
        padding: 0;

        svg {
          display: none;
        }

        &:after {
          content: "\ea47";
          font-family: $font-sallaIcon;
          color: $color-dark-200;
          font-size: $text-xxx-small;
          opacity: 0.7;
          position: absolute;
          left: 0;
          top: 4px;
          @include transi();
        }

        &:hover {
          &::after {
            opacity: 1;
          }
        }
      }
    }
  }

  &--light {
    .vs__dropdown-menu {
      li {
        font-size: $text-xxx-small;
        color: $color-gray-400;
        position: relative;

        &.vs__dropdown-option--selected {
          background-color: rgba(72, 212, 198, 0.1);

          &:after {
            content: "\ea9d";
            font-family: $font-sallaIcon;
            position: absolute;
            @include centerY;
            left: 10px;
            color: $color-secondary; //colorAvilabilty
            font-size: $text-x-small;
          }
        }
      }
    }
  }

  &--with-search {
    &.vs--open {
      &:before {
        content: "";
        position: absolute;
        transform: translateY(15px);
        bottom: -100%;
        left: 0;
        z-index: 9999999;
        width: 100%;
        height: 50px;
        background: $color-white;
        @include b-radius(3px 3px 0 0);
      }

      .vs__selected-options {
        input {
          display: initial;
          position: absolute;
          @include centerX;
          top: 45px;
          z-index: 9999999;
          padding: 15px 35px 15px;
          border: none;
          background: $color-gray-25;
          width: 97%;
          direction: rtl;
          font-size: $text-x-small;
          @include b-radius(999px);
          visibility: visible;
        }

        &:before {
          content: "\ef09";
          font-family: $font-sallaIcon;
          color: $color-gray-400;
          font-size: $text-xx-small;
          position: absolute;
          right: 27px;
          top: 52px;
          z-index: 99999999;
        }
      }
    }

    .vs__dropdown-menu {
      margin-top: 50px !important;
      box-shadow: 0 -15px 19px rgba(0, 0, 0, 0.07) !important;
    }
  }

  .vs__dropdown-menu {
    left: 0 !important;
  }

  &.top-placement {
    &:before {
      content: none;
    }

    &.vs--open {
      .vs__selected-options {
        input {
          top: 0;
          height: 100%;
          right: 0;
          width: 100%;
          @include b-radius($b-radius-sm);
          padding: 5px 28px 7px 15px;
          transform: translateX(0);
        }

        &:before {
          right: 9px;
          top: 6px;
        }
      }
    }

    .vs__dropdown-menu {
      padding: 0;
      direction: rtl;

      .d-center {
        overflow: hidden;
      }
    }
  }
}

.vs__dropdown-menu {
  list-style: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.07);
  margin-top: 3px !important;
  background-color: $color-white;
  padding: 0;
  max-height: 300px;
  left: 15px !important;
  overflow-y: auto;
  position: absolute;
  width: 100%;
  color: $color-dark-300;
  font-size: $text-x-small;
  z-index: 9999999;
  right: 0;
  @include b-radius($b-radius-sm);

  .vs__dropdown-option {
    text-align: right;
    font-size: $text-small;
    padding: 8px 15px;
    color: $color-dark-300 !important;
    cursor: pointer;
    @include transi();

    &:hover {
      background-color: hsla(0, 0%, 96.1%, 0.47); //colorAvilabilty
    }

    h6 {
      font-size: $text-x-small;
      font-weight: normal;
      color: $color-gray-200;
      margin: 0;
    }

    .vs__dropdown-menu {
      padding-top: 50px;
    }

    &--disabled {
      background: $color-gray-25;
      color: $color-dark-100;
      cursor: not-allowed;
    }
  }

  .vs__no-options {
    padding: 5px 23px 12px;
    text-align: right;
  }
}