// semantic ui mods --
.ui {
  &.popup {
    &.calendar {
      border: none;

      &:before {
        box-shadow: -1px -1px 0 0 $color-white !important;
      }

      table {
        &.ui.table {
          min-width: 18em;
          font-size: $text-xxx-small;

          thead {
            tr {
              th {
                font-size: $text-x-small;
                text-align: center;
                font-weight: normal;

                .link {
                  display: block;
                  font-size: $text-x-small;
                  color: $color-dark-300;
                  text-align: center;
                  margin: 0 auto;

                  &.prev, &.next {
                    top: 8px;
                  }
                }
              }
            }
          }

          tbody {
            tr {
              td {
                font-family: 'Arial', serif;
                text-align: center;

                &.link {
                  &.today {
                    color: $color-white;
                    background-color: $color-primary;
                  }

                  &.range {
                    color: darken($color-primary, 5%);
                    background-color: rgba($color-primary, 0.2);
                  }
                }
              }
            }
          }

          &.month, &.year {
            tbody {
              tr {
                td {
                  padding: 0.75em;
                }
              }
            }
          }
        }
      }
    }
  }

  // dropdown ---
  &.dropdown {
    @include flexable(center, flex-start, row);
    width: 100%;
    min-width: unset !important;
    min-height: unset !important;
    @include b-radius($b-radius-sm);
    transition: all $trans-speed easeOut !important;
    border: $color-gray-200;

    .text {
      width: 100%;
      font-size: $text-x-small;
      color: $color-dark-200 !important;
      margin: 5px 5px 5px 0 !important;

      &.default {
        color: $color-dark-100 !important;
      }
    }

    i {
      &.dropdown.icon {
        color: $color-dark-100;
        line-height: 1;
        padding: 0 !important;
        margin: 0 !important;
        position: absolute;
        top: 54% !important;
        transform: translateY(-50%);
        #{$left}: 10px !important;
        opacity: 1;

        &:before {
          content: '\ea62';
          font-family: $font-sallaIcon;
          font-size: get-rem($text-medium);
        }
      }
    }

    .menu {
      width: 100%;
      overflow: auto;
      border-color: $color-gray-25;

      .item {
        @include transi();
        font-weight: normal !important;
        color: $color-dark-200;
        cursor: pointer;

        &:hover,
        &.active,
        &.selected {
          background: $color-gray-50;
        }
      }
    }

    &.active,
    &.visible {
      border-color: $color-gray-300 !important;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;

      &:hover {
        border-color: $color-gray-300 !important;
      }
    }

    &.upward {
      &.active {
        border-top: none;
        @include b-radius(0 0 5px 5px);

        .menu {
          border-bottom: none;
          border-top: 1px solid $color-gray-300 !important;
          @include b-radius(5px 5px 0 0);
        }
      }
    }

    &.find, &.search {
      padding: 5px !important;

      > input {
        &.search {
          width: 100%;
          min-height: 34px;
          padding: 5px 10px !important;
          margin: 0;
          font-size: $text-x-small;
          @include b-radius(0);
        }
      }

      .text {
        &.default {
          padding: 12px 10px 10px 40px;
          margin: 0 !important;
          min-height: 36px !important;
          font-size: $text-x-small !important;
        }
      }

      > .menu {
        overflow: hidden;

        .search {
          position: relative;
          padding: 0 10px 10px;
          margin: 0;

          i {
            position: absolute;
            top: 50%;
            transform: translateY(-85%);
            right: 20px;
            opacity: 0.8;
            font-size: 13px;
          }

          .form-control {
            height: 30px;
            padding: 2px 35px 7px 5px;
            border: none !important;
            background: $color-gray-50;
          }
        }

        .scrolling {
          max-height: 120px;
          overflow-y: auto;

          .item {
            width: 100%;
            height: auto;
            padding: 3px 10px 5px;
            font-size: $text-xx-small;
            border-top: 1px solid $color-gray-50;
          }
        }
      }
    }

    &:hover {
      border-color: $color-gray-25;
    }

    // overrides selection rules ---
    &.selection {
      @include b-radius($b-radius-sm !important);

      &:hover {
        border-color: $color-gray-300 !important;
      }

      .menu {
        border-color: $color-gray-300 !important;

        &.visible {
          &:hover {
            border-color: $color-gray-300 !important;
          }
        }
      }
    }

    // ui dropdown - style like bootstrap dropdown ---
    &.bs-dropdown {
      min-height: 34px !important;
      padding: 0 !important;
      box-shadow: none !important;
      border-color: $color-gray-200 !important;

      .text {
        min-height: 34px;
        font-size: $text-xx-small;
        color: $color-black;
        padding: 12px 10px 10px 40px;
        margin: 0 !important;

        &.default {
          font-size: $text-x-small;
        }
      }

      .dropdown.icon {
        left: 12px;

        &:before {
          content: '\e96e';
          font-family: $font-sallaIcon;
          font-size: $base-size;
          color: $color-dark-100;
        }
      }

      .menu {
        border: none !important;
        box-shadow: 0 4px 8px rgba($color-black, 0.07) !important;
        @include b-radius($b-radius-sm);
        margin-top: 5px !important;
        padding: 10px 0 !important;

        .item {
          min-height: 36px;
          padding: 11px 15px 6px !important;
          font-size: $text-x-small;
          border: none !important;

          &.selected, &.active {
            background-color: $color-secondary !important; //colorAvilability
            color:  #2e6f66 !important; //colorAvilability
          }
        }
      }

      &.multiple {
        .ui.label {
          box-shadow: none !important;
          border: 1px solid $color-gray-200 !important;
          @include b-radius(50px !important);
          font-size: 12px !important;
          color: $color-dark-200 !important;
          margin: 5px 0 0 5px !important;
          padding: 3px 5px 3px 10px !important;
          position: relative;
          z-index: 999;

          .delete.icon {
            display: inline-block;
            width: 15px;
            height: 15px;
            line-height: 14px;
            vertical-align: middle;
            background: $color-danger;
            float: right;
            margin: 0 0 0 5px;
            @include b-radius(50%);

            &:after {
              content: '\ea47';
              font-family: $font-sallaIcon;
              font-size: $text-xxxx-small;
              font-style: normal;
              color: $color-white;
            }
          }
        }
      }

      &.selection {
        .menu {
          > .item {
            padding: 11px 15px 6px !important;
          }
        }
      }

      &.search {

        @include mediaMaxWidth($screen-phones) {
          .menu {
            max-height: 175px !important;
          }
        }
      }

      &:hover {
        border-color: $color-gray-200 !important;
      }
    }

    // very custom styling for #city dropdown ---
    &#city {
      .text {
        pointer-events: none;
      }

      .dropdown.icon {
        width: 100%;
        height: 100%;
        cursor: text;
        right: 0;

        &:before, &:after {
          position: absolute !important;
          left: 11px !important;
          right: unset !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
        }
      }

      &.loading {
        .dropdown.icon {
          width: 100%;
          height: 100%;
          cursor: text;

          &:before {
            display: none !important;
          }
        }
      }
    }
  }

  // for filter bar
  &.filter {
    &.calendar {
      .ui {
        &.popup {
          &.calendar {
            width: 100%;
          }
        }
      }
    }
  }
}
