.multi-language-input {
  &.vue-html5-editor {
    border-color: $color-gray-200;
    @include b-radius(0);

    > .toolbar {
      background-color: $color-gray-50;

      div {
        text-align: right;
      }

      .content {
        direction: rtl;
        text-align: right;
      }

      > ul {
        text-align: right;
        border-bottom-color: $color-gray-200;

        > li {
          &[title="معلومات"] {
            display: none !important;
          }
        }
      }

      > .dashboard {
        padding: 5px 10px;

        * {
          font-size: $text-xx-small;
          color: $color-dark-300;
        }

        label {
          line-height: 1.4;
          margin-bottom: 0;
          vertical-align: middle;
        }

        //buttons {
        button {
          padding: 4px 8px 6px;
          line-height: 1.4;
          height: 28px !important;
          @include b-radius(2px);
          background: $color-white;
          margin: 2px;

          &[type="submit"] {
            margin-bottom: 0;
            vertical-align: middle;
          }
        }

        input {
          padding: 4px 6px !important;
          height: 28px !important;
          @include b-radius(2px !important);
          background: $color-white !important;
          vertical-align: middle;

        }
      }
    }

    .rec-ls-ltr & {
      .toolbar {
        ul {
          direction: ltr;
          padding-left: 50px;
        }
      }
    }
  }

  &.en-language-input {
    direction: ltr;

    > .toolbar {
      direction: ltr;
      text-align: left;

      div {
        direction: ltr;
        text-align: left;
      }

      .content {
        direction: ltr;
        text-align: left;
      }

      > ul {
        text-align: left;
      }
    }
  }
}
