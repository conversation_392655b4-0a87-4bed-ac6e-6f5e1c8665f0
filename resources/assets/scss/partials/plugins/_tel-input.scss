.iti-tel-input {
  // pro version ---
  $b-rad: 5px;

  &-pro {
    .input-group {
      line-height: 0.8;
    }

    .iti {
      $root_el: &;
      width: 100%;
      position: relative;
      border: none;
      box-shadow: none;

      &__flag-container {
        height: 100%;
        border: none;

        &:hover, &.open {
          .selected-flag {
            background-color: transparent !important;
          }
        }

        &:before {
          content: '';
          display: block;
          width: 1px;
          height: 60%;
          position: absolute;
          right: 5px;
          top: 20%;
          background-color: $color-gray-200;
        }
      }

      &__country-list {
        min-width: 300px;
        margin: 8px 0 0 0;
        @include b-radius($b-radius-sm);
        border-color: $color-gray-200;
        left: 0;
        box-shadow: 0 1px 1px rgba($color-black, 0.05);

        li {
          @include flexable(center, flex-start, row);
          direction: rtl;
          padding: 8px 10px;

          span {
            font-size: $text-xx-small;
            text-align: right;
          }

          &:hover, &.highlighted {
            background-color: $color-gray-25;
          }
        }
      }

      &__flag-box {
        margin: 0 0 0 5px;
      }

      &__country-name {
        flex: auto;
        margin: 0;
        padding: 0 10px;
        white-space: normal;
        line-height: 1.3;
      }

      &__dial-code {
        unicode-bidi: plaintext;
        font-family: Arial, serif;
        letter-spacing: 1px;
      }

      &__divider {
        border-bottom-color: $color-gray-200;
      }

      &__selected-flag {
        padding: 2px 10px 0 30px;
        outline: none;
        box-shadow: none;
        border-right: 1px solid $color-gray-200;
        background: transparent !important;

        &:after {
          content: '\e96d';
          font-family: $font-sallaIcon;
          display: inline-block;
          position: absolute;
          top: 55%;
          left: 10px;
          transform: translateY(-50%);
        }
      }

      &__flag {
        display: block !important;
        margin: 0 0 0 5px;
      }

      &__selected-dial-code {
        font-family: 'Arial', serif;
        font-size: $text-x-small;
        unicode-bidi: plaintext;
        margin: 2px 0 0 0 !important;
        letter-spacing: 1px;
      }

      &__arrow {
        display: none;
      }

      .form-control {
        letter-spacing: 1px;
        direction: ltr;
        unicode-bidi: plaintext;
        padding-left: 78px !important;
      }

      @include mediaMaxWidth($screen-phablet) {
        .vti__dropdown {
          .vti__dropdown-list {
            width: 220px !important;
          }
        }
      }
    }

    .error-msg {
      font-size: $text-xx-small;
    }
  }
}
