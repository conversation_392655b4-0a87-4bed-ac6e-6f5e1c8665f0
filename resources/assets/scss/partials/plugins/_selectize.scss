.selectize-control {
  .selectize-input {
    padding: 0 12px;

    input[type="text"] {
      height: 36px;
      @include b-radius($b-radius-sm !important);
      font-size: $text-x-small;
      margin-top: 0
    }

    .item {
      padding: 5px 10px
    }
  }

  &.selectized__icon {
    position: relative;

    .selectize-input {
      input {
        margin-top: 0;
        padding: 6px 40px 10px 10px !important;
      }
    }

    &:before {
      content: '\ef09';
      font-family: $font-sallaIcon;
      color: $color-gray-300;
      position: absolute;
      height: 20px;
      bottom: 12px;
      right: 12px;
      font-size: $text-medium;
      line-height: 25px;
      pointer-events: none;
      z-index: 9;
    }

    // hack to remove unwanted search icon ---
    .selectized__icon {
      &:before {
        display: none;
      }
    }
  }


  &.has-error {
    border: 1px solid $color-danger !important;
  }

  &.no-padding {
    .selectize-input {
      padding: 0 !important;
    }
  }

  &--no-padding {
    .selectize-input {
      padding: 0 !important
    }
  }

  &--absolute-dropdown {
    .selectize-dropdown {
      position: absolute;
      z-index: 999;
      background-color: $color-white;
    }
  }

  // dropdown ---
  .selectize-dropdown {
    max-height: 300px;
    overflow-y: auto;

    .selectize-dropdown-content {
      .media {
        margin-top: 10px;

        .media-left {
          padding-left: 10px;

          img {
            &.thumb.img-lg {
              width: 35px !important;
              height: 35px !important;
            }
          }
        }

        .media-body {
          .media-heading {
            font-size: $text-small;
            line-height: 1;
            margin: 0;
          }

          .text-success {
            font-size: $text-x-small;
            line-height: 1;
          }

          .highlight {
            color: $color-danger;
          }
        }
      }

      .selectize-items {
        padding: 10px 10px 10px 0;
        cursor: pointer;

        &:hover {
          background-color: $color-gray-25;
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      max-height: 150px;
    }
  }

  .dropdown-empty-message {
    font-size: $text-x-small;

    .selectize-dropdown-content {
      padding: 20px !important;
      text-align: center;
    }
  }

  &.selectize-nom {
    .selectize-input {
      padding: 0 !important;

      input[type="text"] {
        border: none !important;
        padding-right: 5px !important;
      }
    }
  }

  &.multi {
    .selectize-input {
      > div {
        padding: 3px 10px 6px !important;
        border: 1px solid $color-gray-200 !important;
        @include b-radius($b-radius-sm !important);
        background-color: $color-white !important;
      }
    }
  }

  // minimal version ---
  &.minimal {
    .selectize-input {
      padding: 0;

      input {
        width: calc(100% - 8px) !important;
        height: 36px;
        @include b-radius($b-radius-sm !important);
        font-size: $text-x-small;
        // border: 1px solid $color-gray-200 !important;

      }
    }

    &.has-error {
      border: 1px solid $color-danger !important;
    }


    &.selectized__icon {
      position: relative;

      .selectize-input {
        input {
          margin-top: 0;
          padding: 6px 40px 10px 10px !important;
        }
      }

      &:before {
        content: '\ef09';
        font-family: $font-sallaIcon;
        color: $color-gray-300;
        position: absolute;
        top: 6px;
        right: 10px;
        font-size: $text-medium;
        z-index: 5555
      }

      // hack to remove unwanted search icon ---
      .selectized__icon {
        &:before {
          display: none;
        }
      }
    }

    // dropdown ---
    .selectize-dropdown {
      .selectize-dropdown-content {
        .media {
          margin-top: 10px;

          .media-left {
            padding-left: 10px;

            img {
              &.thumb.img-lg {
                width: 35px !important;
                height: 35px !important;
              }
            }
          }

          .media-body {
            .media-heading {
              font-size: $text-small;
              line-height: 1;
              margin: 0;
            }

            .text-success {
              font-size: $text-x-small;
              line-height: 1;
            }

            .highlight {
              color: $color-danger;
            }
          }
        }
      }
    }

    .dropdown-empty-message {
      font-size: $text-x-small;

      .selectize-dropdown-content {
        padding: 0 !important;
      }
    }

    // multi select version ---
    &.multi {
      .selectize-input {
        &.has-items {
          padding-right: 2px;
          padding-left: 4px;

          .item {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden !important;
            text-overflow: ellipsis;
            padding: 0 30px 3px 10px !important;
            margin: 0 4px 4px 0 !important;
            font-size: $text-xx-small;
            z-index: 9;

            .remove {
              width: 25px;
              font-size: $text-large;
              line-height: 15px;
              color: $color-danger;
              background: rgba($color-danger, 0.1);
            }

            &.active {
              color: $color-dark-300;
            }
          }

          input {
            position: relative !important;
            padding-top: 7px !important;
            padding-bottom: 4px !important;
          }
        }
      }
    }

    &.attached {
      .selectize-dropdown {
        top: 0 !important;
        max-height: 200px !important;
        overflow: auto;
      }
    }
  }
}



// merge selectized 
