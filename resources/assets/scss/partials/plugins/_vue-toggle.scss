.vue-toggle {
  $vue-toggle-root: &;

  width: 100%;
  position: relative;

  .vue-js-switch {
    .v-switch {
      &-core {
        width: 32px !important;
        height: 20px !important;
        background-color: transparent !important;
        border: 1px solid $color-gray-300;
        box-shadow: rgb(223, 223, 223) 0 0 0 0 inset;
        transition: border 0.4s ease 0s, box-shadow 0.4s ease 0s !important;
      }

      &-button {
        height: 18px !important;
        width: 18px !important;
        top: -3px !important;
        left: -1px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
        transform: translate3d(1px, 3px, 0px) !important;
      }
    }

    &.toggled {
      .v-switch {
        &-core {
          background-color: $color-secondary !important;
          box-shadow: $color-secondary 0 0 0 10px inset;
          border-color: $color-secondary;
          transition: border 0.4s ease 0s, box-shadow 0.4s ease 0s, background-color 1.2s ease 0s !important;
        }

        &-button {
          width: 18px !important;
          height: 18px !important;
          left: 1px !important;
          box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
          transform: translate3d(11px, 3px, 0px) !important;
        }
      }
    }
  }


  &--with-label {
    .vue-js-switch {
      position: initial !important;
    }

    .v-switch {
      &-label {
        font-size: $text-small;
        color: $color-dark-300 !important;
        @include center-v;
        top: 45% !important;
        right: 40px !important;
        font-weight: 400 !important;
      }
    }
  }
}