.theme-entry {
  $theme-entry-root: &;
  height: 100%;
  overflow: hidden;
  @include b-radius($b-radius);
  @include transi(border-color);
  border: 1px solid $color-gray-200;
  background: white;
  box-shadow: none;
  display: flex;
  flex-direction: column;

  &__thumb-wrapper {
    display: block;
    width: 100%;
    height: 300px;
    position: relative;
    overflow: hidden;
    background-color: $color-gray-100;

    img {
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
      transition: unset;
      object-fit: cover;
      object-position: top;
    }
  }

  &__title {
    display: flex;
    align-items: center;

    i {
      color: $color-gray-400;
      font-size: 20px;
    }
  }

  &__info {
    padding: 10px 15px 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    > div {
      @include flexable(center, space-between, row);
      width: 100%;

      h2 {
        font-size: 20px;
        color: $color-dark-300;
        margin: 0;
        @include transi(color);

        &:hover {
          color: $color-primary;
        }
      }

      a {
        font-size: 14px;
        line-height: 1;
        padding: 6px 5px 8px !important;

        &:hover,
        &:focus {
          box-shadow: unset !important;
          color: $color-secondary !important;
        }
      }

      .product-price {
        font-size: 16px;
        color: $color-dark-200;

        * {
          font-size: 16px;
          color: $color-dark-200;
        }
      }

      .btn {
        &.active {
          border-color: $color-secondary-d;
          color: $color-white;
          cursor: text;
          padding: 3px 6px 8px 10px !important;
          background: $color-secondary-d;
          pointer-events: none;

          &:before {
            content: '\ea9b';
            font-family: $font-sallaIcon;
            font-size: 15px;
            line-height: 1;
            display: inline-block;
            width: 15px;
            height: 15px;
            margin-left: 5px;
            transform: translateY(3px);
          }
        }
      }

      &:last-child {
        margin: 10px 0 0 0;
      }
    }

    #theme-custom-css-widget {
      position: absolute;
      left: 0;
    }

    #{$theme-entry-root} {
      &__tags {
        li {
          a {
            font-size: 14px;
            color: $color-gray-400;
            text-decoration: underline;
            @include transi(color);

            &:hover {
              color: $color-dark-100;
            }
          }

          &:after {
            content: '/';
            color: $color-gray-400;
            line-height: 1;
            display: inline-block;
            vertical-align: middle;
            margin: 0 3px;
          }

          &:last-child {
            &:after {
              display: none;
            }
          }
        }
      }
    }

    .theme-title {
      color: $color-primary-l;
      transition: 0.3s;

      &:hover {
        color: $color-secondary;
      }
    }

    .tags {
      display: flex;
      gap: 10px;

      &__item {
        font-size: 12px;
        color: $color-dark-100;
        text-decoration: underline;
        line-height: 1;
        margin-bottom: 5px;

        &:hover {
          text-decoration: none;
          color: $color-secondary;
        }
      }
    }

    .price {
      gap: 5px;

      b {
        font-size: 16px;
        color: $color-dark-300;
      }

      span {
        color: $color-dark-100;
        font-weight: normal;
        font-size: 13px;
      }
    }
  }

  // purchased theme ---
  &--purchased {
    border: none;
    box-shadow: none;

    .theme-entry__info {
      gap: 10px;

      > .btn {
        flex-shrink: 0;
      }
    }

    input[type=radio] {
      display: none;

      + label {
        @include flexable(flex-start, flex-start, column);
        @include b-radius($b-radius);
        @include transi();
        height: 350px;
        overflow: hidden;
        border: 1px solid $color-gray-200;
        cursor: pointer;
        margin: 0;

        > * {
          width: 100%;
        }

        #{$theme-entry-root} {
          &__thumb-wrapper {
            flex: 1;
            height: 100%;

            img {
              display: block;
              width: 100%;
              height: 100%;
              overflow: hidden;
              position: relative;
              top: unset;
              right: unset;
              transition: unset;
              object-fit: cover;
              object-position: top;
            }
          }

          &__info {
            @include flexable(center, flex-start, row);
            min-height: 50px;
            padding: 10px;
            border-top: 1px solid $color-gray-200;

            > span {
              flex: 1;
              position: relative;
              padding-right: 30px;

              &:before {
                content: '';
                display: inline-block;
                vertical-align: middle;
                width: 20px;
                height: 20px;
                @include b-radius(50%);
                border: 1px solid $color-primary-l;
                margin: 0 -27px 0 8px;
              }

              &:after {
                content: "\ea9d";
                font-family: $font-sallaIcon;
                font-size: 12px;
                color: $color-primary-l;
                line-height: 1;
                display: block;
                position: absolute;
                top: 7px;
                right: 7px;
                opacity: 0;
                visibility: hidden;
              }
            }

            .btn {
              &.btn-custom-css {
                line-height: 1;
                padding: 5px 10px 6px;
                display: none;
              }
            }
          }

          &__rate {
            padding: 0 10px 10px;
            .theme-rating-toggler {
              &.btn-link {
                color: $color-dark-200 !important;
                text-decoration: underline;
                &.rated {
                  text-decoration: none !important;
                }
              }
            }
          }
        }

        &:hover {
          border-color: rgba($color-secondary-d, 0.5);
        }
      }

      &:checked {
        + label {
          border-color: $color-secondary-d;

          #{$theme-entry-root} {
            &__info {
              i {
                color: $color-gray-400;
              }

              span {
                color: $color-primary;

                &:after {
                  opacity: 1;
                  visibility: visible;
                  transform: scale(1);
                }
              }

              .btn {
                &.btn-custom-css {
                  display: block;
                }
              }
            }
          }

          &:hover {
            border-color: $color-secondary-d;
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      input[type=radio] {
        display: none;

        + label {
          height: 300px;

          #{$theme-entry-root} {
            &__info {
              @include flexable(center, flex-start, row);
              min-height: 35px;
              padding: 5px;

              span {

                &:before {
                  margin: 0 -27px 0 8px;
                }
              }

              .btn {
                &.btn-custom-css {
                  font-size: 12px;
                  padding: 3px 7px 4px;
                }
              }
            }
          }
        }
      }
    }
  }

  &.full-height {
    min-height: 420px;
    @include mediaMaxWidth($screen-phones) {
      min-height: 320px;
    }
  }

  &:hover {
    border-color: rgba($color-secondary-d, 0.5);
  }

  &--new {
    .theme-entry__info {
      > div {
        justify-content: center;
      }
    }
  }
}
