.themes-archive {
  .btn {
    &.btn-link {
      &.btn-return {
        padding-right: 0;
        margin: 0 0 10px 0;
        font-size: 16px;
        color: $color-dark-300;

        i {
          font-size: 18px;
          margin-left: 10px;

        }

        &:hover {
          color: $color-primary;
        }
      }
    }
  }

  .archive-search {
    @include flexable(center, flex-start, row);
    margin-bottom: 20px;

    .form {
      flex: 1;

      .form-group {
        &::before {
          content: '\ef09';
          font-family: 'sallaicons';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 15px;
          color: $color-gray-400;
          font-size: 20px;
        }

        .form-control {
          min-height: 50px;
          @include b-radius($b-radius-sm);
          border: 1px solid $color-gray-200;
          padding: 7px 15px 7px 50px;
          font-size: 16px;

          &::placeholder {
            color: $color-gray-400;
          }

          &:active,
          &:focus,
          &:hover {
            border-color: $color-gray-300;
          }
        }
      }
    }

    .btn {
      background-color: $color-white;
      height: 50px;
      padding: 5px 15px;
      line-height: 40px;
      color: $color-gray-400;
      border: 1px solid $color-gray-200;
      @include b-radius($b-radius-sm);
      flex: 0 0 50px;
      margin-right: 15px;

      i {
        font-size: 20px;
      }

      &:hover,
      &.active {
        border-color: $color-gray-300 !important;
      }
    }

    @include mediaMaxWidth($screen-tablet-l) {
      .form {
        .form-group {
          .form-control {
            min-height: 40px;
            padding: 4px 15px 7px 45px;
            font-size: 14px;
          }
        }
      }

      .btn {
        height: 40px;
        padding: 5px 15px;
        line-height: 30px;
        flex: 0 0 40px;

        i {
          font-size: 18px;
        }
      }
    }
  }
}
