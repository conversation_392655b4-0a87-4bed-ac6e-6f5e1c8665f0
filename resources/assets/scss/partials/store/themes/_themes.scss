.modal {
  &#theme_preview_modal {
    .modal-header {
      @include flexable(flex-start, space-between, row);
      padding: 23px 20px 0 70px;

      .modal-title-wrap {
        display: flex;
      }

      .modal-title {
        display: inline-block;
        margin: 0 0 0 10px;
        font-size: 25px;

        &:after {
          content: ' - ';
          font-family: inherit;
          font-size: inherit;
        }
      }

      .rec-price-wrapper {
        font-size: 25px;


        small {
          font-size: 16px;
          display: inline-block;
          transform: translateY(-2px);
        }
      }

      .theme-tags {
        clear: both;

        li {
          margin: 0 0 0 5px;

          a {
            color: $color-dark-100;
            text-decoration: underline;

            &:hover {
              color: $color-dark-300;
            }
          }

          &:last-child {
            margin: 0;
          }
        }

      }

      #purchase_theme {
        min-width: 100px;
      }

      &:before, &:after {
        display: none;
      }
    }

    #theme_slider {
      .owl-stage-outer {
        @include b-radius($b-radius-sm);
      }

      .owl-dots {
        position: absolute;
        top: -39px;
        left: 80px;
      }

      .owl-nav {
        position: absolute;
        top: -39px;
        left: 0;

        > div {
          position: absolute;

          &.owl-prev {
            left: 35px;
          }

          &.owl-next {
            left: 0;
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      .modal-header {
        flex-direction: column;
        align-items: flex-start;
        padding: 23px 10px 0 40px;

        > div {
          width: 100%;
          text-align: center;
        }

        .close {
          top: 20px;
        }

        .btn {
          margin: 10px 0 0;
        }
      }
    }
  }
}

.explore-themes {
  @include flexable();
  @include transi();
  width: 100%;
  height: 100%;
  background: $color-gray-25;
  @include b-radius($b-radius-sm);
  border: 2px dashed $color-primary;
  padding: 30px 10px;

  > div {
    text-align: center;

    > * {
      display: block;
      line-height: 1;
      color: $color-primary;
    }

    i {
      margin: 0 0 20px 0;
      font-size: 55px;
    }

    h4 {
      font-size: 18px;
      margin: 0 0 10px 0;
    }

    span {
      font-size: 16px;
    }
  }

  &:hover {
    background: $color-gray-200;
  }

  @include mediaMaxWidth($screen-phones) {
    > div {
      i {
        margin: 0 0 10px 0;
        font-size: 35px;
      }

      h4 {
        font-size: 16px;
      }

      span {
        font-size: 14px;
      }
    }
  }
}
