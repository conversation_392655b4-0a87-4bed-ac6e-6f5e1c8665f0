.credit-card-entry {
  input[type=checkbox],
  input[type=radio] {
    display: none;

    + label {
      @include flexable(center, center, row);
      @include b-radius($b-radius-sm);
      @include transi();
      padding: 10px 15px !important;
      font-size: $text-small;
      cursor: pointer;
      border: 1px solid $color-gray-200;
      margin: 0;

      img {
        display: inline-block;
        width: auto;
        max-height: 20px;
        margin-left: 10px !important;
        transform: translateY(1px);
      }

      span {
        display: inline-block;
        vertical-align: middle;
        transform: translateY(-2px);

        small {
          display: inline-block;
          vertical-align: middle;
          margin: 0 2px;
          font-size: $text-medium;
          line-height: 1;
          color: $color-dark-200;
          @include transi();

          &:first-child {
            margin-right: 0;
          }

          &:last-child {
            margin-left: 0;
            transform: translateY(3px);
          }
        }
      }

      &:hover {
        border-color: $color-gray-300;
      }
    }

    &:checked {
      + label {
        border-color: $color-secondary;

        span {
          small {
            color: $color-primary;
          }
        }
      }
    }
  }

  &--new {

    input[type=checkbox],
    input[type=radio] {
      + label {
        border: 1px dashed $color-gray-400;

        &:before {
          content: '\e90a';
          font-family: $font-sallaIcon;
          font-size: $text-large;
          color: $color-gray-300;
          line-height: 1;
          display: inline-block;
          vertical-align: middle;
          margin-left: 10px;
        }

        &:hover {
          border-color: $color-gray-300;
        }
      }

      &:checked {
        + label {
          border: 1px solid $color-secondary;
          color: $color-primary;

          &:before {
            color: $color-primary;
          }
        }
      }
    }
  }

  &--new-tiffany {

    input[type=checkbox],
    input[type=radio] {
      + label {
        border: 1px solid $color-secondary;
        background: $color-gray-50;
        color: $color-primary;

        &:before {
          content: '\e909';
          font-family: $font-sallaIcon;
          font-size: $text-large;
          color: $color-primary;
          line-height: 1;
          display: inline-block;
          vertical-align: middle;
          margin-left: 10px;
        }
      }

      &:checked {
        + label {
          border: 1px solid $color-secondary;
          color: $color-primary;
          background: white;

          &:before {
            color: $color-primary;
          }
        }
      }
    }
  }


  @include mediaMaxWidth($screen-tablet-l) {

    input[type=checkbox],
    input[type=radio] {

      + label {
        padding: 10px 5px !important;

        span {

          small {
            font-size: $text-small;
          }
        }
      }
    }
  }
}