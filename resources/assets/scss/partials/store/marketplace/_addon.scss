$plan-plus: #764AAF;
$plan-pro: $color-primary;

#salla_plans {
  padding-top: 50px;
}

#packages_benefits_wrapper {
  max-width: 1000px;
  margin: 0 auto;
}

.salla {
  &-addon {
    $salla-addon-root: &;
    @include flexable(center, flex-start, column);
    padding: 30px 20px;
    @include b-radius($b-radius);
    background: white;
    @include transi(border);
    border: 1px solid $color-gray-200;
    overflow: hidden;

    &-wrap {
      margin-bottom: 20px;

      @include mediaMaxWidth(480px) {
        width: 100%;
        flex-basis: 100%;
      }
    }

    &__meta {
      @include flexable(center, flex-start, column);
      width: 100%;
      flex: auto;
      margin-bottom: 20px;

      #{$salla-addon-root} {
        &__thumb {
          position: relative;
          display: inline-block;
          margin: 0 0 10px 0;

          img {
            display: block;
            max-height: 70px;
          }
        }

        &__title {
          display: block;
          margin: 0 0 10px 0;

          * {
            font-family: $font-main;
            font-size: 18px;
            color: $color-primary;

            margin: 0;
            @include transi(color);
          }

          &:hover {
            * {
              color: $color-primary;
            }
          }
        }
      }

      .rec-price-wrapper {
        .product-price {
          color: $color-black;
          font-size: 25px;
          font-weight: bold;

          .currency {
            font-weight: 400;
            color: $color-primary;
          }

          small {
            font-size: 15px;
            color: $color-black;
          }

          b {
            color: $color-primary;
          }
        }
      }
    }

    &__desc {
      p {
        margin: 0;
      }

      * {
        font-size: 13px;
        color: $color-dark-100 !important;
      }
    }

    &__install {
      @include b-radius(100px);
      @include transi(background-color);
      padding-right: 15px;
      padding-left: 15px;

      i {
        font-size: 15px;
        margin: 0 0 0 5px;
        transform: translateY(-3px);
      }

      &.fetching {
        color: transparent !important;

        &:before {
          content: '';
        }
      }
    }

    .rec-price-wrapper {
      margin: 0 0 10px 0;
    }

    // installed addon ---
    &--installed {
      align-items: flex-start;
      padding: 20px 20px;

      #{$salla-addon-root} {
        &__meta {
          @include flexable(flex-start, flex-start, row);
          margin-bottom: 20px;

          >div {
            @include flexable(center, flex-start, row);

            &:first-child {
              flex: 1 0 auto;
            }
          }
        }

        &__title {
          margin: 0 0 4px 0;
        }

        &__thumb {
          margin: 0 0 0 15px;

          &:after {
            content: '\ea9d';
            font-family: sallaicons;
            font-size: 13px;
            color: white;
            text-align: center;
            width: 25px;
            height: 25px;
            @include b-radius(50%);
            background: rgba($color-primary, 0.8);
            position: absolute;
            line-height: 26px;
            right: 0;
            bottom: 0;
          }
        }

        &__settings {
          width: 45px;
          height: 45px;
          position: relative;
          @include b-radius(50%);
          @include transi();
          background: $color-primary;

          &:before {
            content: '\ef1f';
            font-family: sallaicons;
            font-size: 20px;
            color: white;
            position: absolute;
            top: 50%;
            right: 50%;
            transform: translateY(-50%) translateX(50%);
          }
        }

        &__info {
          p {
            text-align: right;
          }

          @include mediaMinWidth($screen-desktop-large) {
            max-width: 100%;
          }
        }
      }
    }

    // detail addon ---
    &--detail {
      background: transparent;
      box-shadow: none !important;
      padding: 0 !important;
      border: none !important;

      >* {
        width: 100%;
      }

      .addon-title {
        @include flexable(flex-start, flex-start, row);
        margin-bottom: 30px;

        &__thumb {
          @include flexable(flex-start, flex-start, row);
          flex: auto;
          /*padding-left: 40px;*/

          img {
            max-height: 100px;
            margin: 0 0 0 30px;
          }

          >div {
            >* {
              color: $color-dark-200;
            }

            h1 {
              font-size: 30px;
              color: $color-dark-300;
              margin: 0 0 10px 0;
            }

            p {
              color: $color-dark-200;
              margin: 0 0 15px 0;

              @include mediaMinWidth($screen-desktop-large) {
                max-width: 80%;
              }
            }

            ul {
              flex-wrap: wrap;

              li {
                margin: 0 0 5px 5px;
                font-size: 14px;

                &:first-child {
                  margin-left: 10px;
                }
              }
            }
          }
        }

        &__price {
          text-align: left;

          .rec-price-wrapper {
            b {
              font-size: 28px;
              color: $color-primary;
            }

            small {
              // transform: translateY(3px);
            }

            .product-price {
              width: 100%;
              font-family: $font-main;
              font-size: 20px;
              color: $color-dark-300;
              text-align: left;
            }
          }
        }

        @include mediaMaxWidth($screen-laptop-small) {
          padding: 15px;
          flex-direction: column;
          align-items: center;

          &__thumb {
            flex-direction: column;
            align-items: center;
            padding: 0;

            img {
              max-height: 75px;
              margin: 0 0 5px 0;
            }
          }

          >div {

            h1,
            p {
              text-align: center;
            }

            ul {
              justify-content: center;
            }
          }
        }
      }

      #{$salla-addon-root} {
        &__content {
          margin-bottom: 30px;

          * {
            color: $color-dark-50;
            line-height: 1.9;
          }
        }
      }

      // override options ---
      .order-plan {
        padding: 0;
        background: transparent;
        margin: 0 0 20px 0;

        >h6 {
          font-family: $font-main;
          font-size: 14px !important;
          margin: 0 0 6px 0 !important;
        }
      }
    }

    &--summary {
      .addon-title {
        &__thumb {
          align-items: center;

          img {
            width: auto;
            max-height: 75px;
            margin: 0 0 0 20px !important;
          }

          >div {
            h1 {
              font-size: 20px;
              margin-bottom: 5px;
            }
          }
        }
      }
    }

    @include mediaMaxWidth($screen-desktop-large) {
      &--installed {
        #{$salla-addon-root} {
          &__thumb {
            img {
              max-height: 40px;
            }
          }

          &__settings {
            width: 30px;
            height: 30px;

            &:before {
              font-size: 14px;
            }
          }
        }
      }
    }

    @include mediaMaxWidth($screen-desktop-small) {
      padding: 30px 15px;
      @include b-radius($b-radius-sm);

      &__meta {
        margin-bottom: 20px;

        #{$salla-addon-root} {
          &__thumb {
            img {
              max-height: 60px;
            }
          }

          &__title {
            * {
              font-size: 18px;
            }
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      flex-direction: column;

      &__meta,
      &__info {
        padding: 0 15px;

        #{$salla-addon-root} {
          &__thumb {
            margin: 0 0 0 10px;

            img {
              max-height: 55px;
            }
          }

          &__title {
            * {
              font-size: 16px;
            }
          }
        }
      }

      &--installed {
        #{$salla-addon-root} {
          &__meta {
            margin-bottom: 15px;
          }

          &__thumb {
            margin: 0 0 0 15px;

            img {
              max-height: 40px;
            }

            &:after {
              font-size: 11px;
              width: 20px;
              height: 20px;
              line-height: 21px;
            }
          }
        }
      }
    }

    &--plan {
      @include flexable(flex-start, flex-start, column);
      position: relative;
      padding: 0;

      #{$salla-addon-root} {
        &__thumb {
          padding: 20px 20px 0;
          z-index: 2;

          img {
            display: block;
            height: auto;
            max-height: 120px;
            max-width: 80px;
            margin-inline: auto;
          }

          .badge {
            position: relative;
            top: -15px;
            cursor: unset;
          }
        }

        &__title-wrapper {
          @include flexable(center, space-between, column);
          width: 100%;
          padding: 0 20px 20px;
          border-bottom: 1px solid $color-gray-200;

          h2 {
            font-size: $text-xxx-large;
            color: $color-dark-300;
            font-weight: 500;
            margin: 5px auto 10px;
          }

          p {
            color: $color-dark-200;
          }

          .product-price {
            color: $color-primary;
            font-size: $text-xxx-large;
            font-weight: 700;
          }
        }

        &__info {
          width: 100%;
          padding: 20px;

          &-title {
            margin-bottom: 0;
            display: block !important;
            font-size: $text-small;
          }

          .rec-list--numeric {
            &.checked-list {
              outline: none !important;

              li {
                &:before {
                  color: $color-secondary;
                }
              }
            }
          }
        }

        &__desc {
          p {
            span {
              font-family: $font-main !important;
              font-size: $text-xxx-small !important;
            }
          }
        }

        &__meta {
          @include flexable(center, center, column);
          margin: 0;
          z-index: 2;
          text-align: center;
          margin-bottom: 60px;

          >* {
            width: 100%;
          }

          img {
            max-width: 50px;
          }
        }

        &__testimony {
          margin-top: auto;

          .testimony-wrapper {
            @include b-radius($b-radius);
            min-height: 220px;

            >div {
              margin-top: auto
            }
          }

          .swiper-pagination {
            position: static;
            margin-top: 15px;

            &-bullet {
              width: 10px;
              height: 10px;
              background: $color-secondary-50;
              opacity: 1;

              &-active {
                background: $color-primary;
                width: 40px;
                @include b-radius(5px)
              }
            }
          }
        }

        &__actions {
          @include flexable(center, space-between, row);

          .btn {
            min-width: 80px;
            max-width: 115px;
          }
        }

        &__options {
          width: 100%;
          padding: 20px;
          border-top: 1px solid $color-gray-200;

          .rec-list {
            li {
              margin-bottom: 20px;

              label {
                color: $color-dark-300;
                font-size: 16px !important;
                font-weight: 400;
              }

              .previous {
                text-decoration: line-through;
                margin: 0 5px;
              }

              b {
                font-weight: bold;
                margin-bottom: 10px;
                display: inline-block;

                span {
                  font-weight: 400;
                }

                .badge {
                  &.light {
                    background-color: $color-gray-50;
                  }
                }
              }
            }
          }
        }

        &__duedate {
          @include b-radius($b-radius);
          border: 1px solid rgba($color-secondary-50, 0.6);
          background: rgba($color-secondary-50, 0.10);

          &__head {
            text-align: center;
            padding: 10px 15px;
            background: rgba($color-secondary-50, 0.15);
            border-bottom: 1px solid rgba($color-secondary-50, 0.6);
            font-size: 14px;
          }

          &__body {
            padding: 20px;
            text-align: center;
          }

          b {
            color: $color-primary-l;
          }

          &.warning {
            border-color: rgba($color-warning, 0.6);
            background: rgba($color-warning, 0.03);

            .salla-addon__duedate__head {
              background: rgba($color-warning, 0.05);
              border-color: rgba($color-warning, 0.6);
            }

            b {
              color: $color-warning;
            }
          }

          &.danger {
            border-color: rgba($color-danger, 0.6);
            background: rgba($color-danger, 0.03);

            .salla-addon__duedate__head {
              background: rgba($color-danger, 0.05);
              border-color: rgba($color-danger, 0.6);
            }

            b {
              color: $color-danger;
            }
          }
        }

        &__renewal {
          width: 100%;

          .header {
            background: $color-gray-25;
            font-size: 13px;
            color: $color-black;
            @include b-radius(5px 5px 0 0);
            padding: 10px 20px;
            text-align: center;
          }

          .body {
            background-color: $color-gray-50;
            border: 1px solid $color-gray-200;
            @include b-radius(0 0 5px 5px);
            @include flexable(center, center, column);
            padding: 15px 0;

            b {
              font-size: 30px;
              margin-bottom: 10px;

              &.text-warning-2 {
                color: $color-warning-light;
              }
            }

            small {
              font-size: 11px;
            }
          }
        }
      }

      &.plus {
        #{$salla-addon-root} {
          &__title-wrapper {
            h2 {
              color: $plan-plus;
            }
          }

          &__info {
            .rec-list--numeric {
              &.checked-list {
                li {
                  &:before {
                    color: $plan-plus;
                  }
                }
              }
            }
          }
        }
      }

      // for all single plans, pro, special or any other ---
      &.single {
        flex-direction: row;
        align-items: center;
        margin: 50px auto;
        max-width: 670px;

        .salla-addon__duedate {
          margin: 85px 0;
        }

        section {
          height: 100%;
        }

        .single__info {
          flex: 1;
          padding: 50px;
        }

        .single__meta {
          flex: 1;
          padding: 50px;
          position: relative;

          @include mediaMinWidth($screen-laptop) {
            &::after {
              content: '';
              width: 1px;
              background-color: $color-gray-200;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              right: 0;
              height: 80%;
            }
          }
        }

        @include mediaMaxWidth(576px) {
          padding: 30px;
          max-width: unset;
          flex-direction: column;

          section:last-of-type {
            flex: auto;
            width: 100%;
            margin: 40px 0 0;
          }
        }

        &:after {
          width: 50%;
        }

        @include mediaMaxWidth($screen-tablet-l) {
          flex-direction: column;
          padding: 30px;

          .single__info,
          .single__meta {
            padding: 0;
          }

          &:after {
            width: 100%;
          }
        }
      }

      &.upgrade {

        .sub-title p {
          margin: 0 !important;
        }

        .upgrade__title {
          display: flex;
          align-items: center;
          margin-bottom: 30px;

          img {
            height: 40px;
            transform: translateY(1px);
          }
        }

        .featured-stores {
          border-right: 1px solid $color-gray-200;
        }

        &:after {
          background-size: 60%;
        }

        @include mediaMaxWidth($screen-phones) {
          .upgrade__title {
            flex-direction: column;
            margin-bottom: 50px;

            h2 {
              max-width: 90%;
              text-align: center;
            }

            img {
              margin: 0 0 10px 0 !important;
            }
          }

          .upgrade__features {
            height: auto;

            ul.rec-list {
              flex-grow: unset !important;
              padding: 0 !important;

              li {
                padding: 15px 0;
                border: none;
              }

              .sub-title {
                display: none;
              }
            }
          }

          .featured-stores {
            padding: 50px 0 0 0 !important;
            border-right: none;

            .grid-block.grid-block--col-2 {
              grid-template-columns: repeat(2, 1fr);
            }
          }
        }
      }

      &.full {
        display: block;

        &:after {
          background-size: 35%;
          opacity: 0.7;
        }
      }

      @include mediaMaxWidth($screen-phones) {
        flex-direction: column;
        align-items: center;
        padding: 0;

        #{$salla-addon-root} {

          &__title-wrapper,
          &__info,
          &__options {
            padding-right: 30px;
            padding-left: 30px;
          }

          &__title-wrapper {
            flex-direction: column;
            align-items: center;

            h2 {
              font-size: 30px;
            }
          }


          &__actions {
            justify-content: center;

            .btn {
              margin: 0 5px;

              &:first-child {
                margin-right: 0;
              }

              &:last-child {
                flex: 1;
                max-width: unset;
                margin-left: 0;
              }
            }
          }
        }
      }

      @include mediaMaxWidth($screen-phablet) {
        &:after {
          background-size: 150%;
        }
      }
    }

    &--recommended {
      grid-row: 1;
      border: none;
      box-shadow: 0 10px 16px 0 rgba(0, 0, 0, .08);
      &.notSorted {
        grid-row: unset;
      }
    }
  }
}

.addon-thumb {
  position: absolute;
  z-index: 99999;
}


// custom banner
.custom-banner {
  background-color: $color-secondary-25;
  padding: 6px 30px;
  border-radius: 12px;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -ms-border-radius: 12px;
  -o-border-radius: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 991px) {
    flex-direction: column;
    padding: 30px;
  }

  .btn {
    font-size: 20px;
    padding: 13px 24px;
    border-radius: 12px;
    font-weight: bold;

    @media (max-width: 991px) {
      font-size: 16px;
    }

    &-confirm {
      color: $color-secondary-25;
    }

    &--outlined {
      border-color: $color-primary !important;
      color: $color-primary !important;
    }
  }

  &__content {
    display: flex;
    align-items: flex-start;
    color: $color-primary;

    &__icon {
      margin-top: 10px;

      i {
        font-size: 48px;
      }
    }

    @media (max-width: 991px) {
      margin-bottom: 30px;
    }
  }

  &__info {
    margin-right: 20px;
  }

  &__title {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 35px;
    font-weight: 700;
  }

  &__subtitle {
    font-size: 20px !important;
    margin-bottom: 24px;
    font-weight: 400;
    color: #444;

    @media (max-width: 991px) {
      font-size: 15px !important;
    }
  }

  &__image {
    margin-left: 25px
  }
}