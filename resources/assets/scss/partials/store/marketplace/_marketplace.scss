.salla-marketplace {
  p {
    font-size: 14px;

    @include mediaMaxWidth($screen-desktop) {
      font-size: 13px;
    }
  }

  // cart button ---
  .btn.btn-tiffany {
    &.button_show_cart {
      width: 50px;
      height: 50px;
      @include b-radius(50%);
      box-shadow: 0 2px 2px rgba($color-black, 0.1) !important;
      position: fixed;
      top: 260px;
      left: 20px;
      background: $color-secondary-50;
      color: $color-primary-l;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
      border: none !important;

      i {
        font-size: 20px;
        color: $color-primary-l;
      }

      .button_show_cart__badge {
        min-width: 20px;
        min-height: 20px;
        position: absolute;
        right: -3px;
        padding: 0 5px;
        line-height: 18px;
      }

      @include mediaMaxWidth($screen-phones) {
        top: 300px;
      }

      &:focus,
      &:active {
        background: $color-secondary-50 !important;
      }
    }
  }
}

.marketplace-section {
  position: relative;
  margin: 0 0 50px 0;

  @include mediaMaxWidth($screen-phones) {
    padding: 15px 0;

    .rec-title-block {
      padding: 0 15px;
      margin-bottom: 15px !important;
    }

    &--clear {
      padding: 0;
      background: transparent;
    }
  }
}

.addons-list {
  display: flex;
  flex-wrap: wrap;

  .salla-addon {
    height: 100%;
  }
}

.addon-feature {
  @include flexable(center, center, column);
  padding: 30px 20px;
  margin: 0 0 20px 0;
  @include b-radius($b-radius);

  * {
    text-align: center;
  }

  i {
    @include b-radius(50%);
    width: 70px;
    height: 70px;
    background: $color-gray-200;
    text-align: center;
    line-height: 70px;
    margin: 0 0 15px 0;
  }

  h2 {
    font-size: 16px;
    color: $color-dark-300;
    margin: 0 0 10px 0;
  }

  p {
    color: $color-dark-200;
    margin: 0;
  }

  &--bg {
    background: white;
  }

  @include mediaMaxWidth($screen-laptop) {
    padding: 20px 15px;
  }
}


.bank-content {
  .btn-file {
    border: 2px solid $color-dark-200;
  }
}