.marketplace {
  &-banners { 
    display: flex;
    flex-wrap: wrap;
  }

  &-banner {
    display: flex;
    align-items: flex-start;
    position: relative;
    @include b-radius($b-radius);
    overflow: hidden;
    @include transi();
    margin-bottom: 20px;

    @include mediaMaxWidth(480px) {
      width: 100%;
      flex-basis: 100%;
    }

    img {
      display: block;
      width: 100%;
      height: auto;
    }

    &:hover {
      opacity: 0.9;
    }

    &--empty {
      border: 1px solid $color-gray-200;
      background-color: $color-gray-50;

      >div {
        @include flexable();
        width: calc(100% - 50px);
        height: calc(100% - 50px);
        background:
          linear-gradient(to top left,
            rgba(218, 218, 218, 0) 5%,
            rgba(218, 218, 218, 0) calc(50% - 0.8px),
            rgba(218, 218, 218, 1) 50%,
            rgba(218, 218, 218, 0) calc(50% + 0.8px),
            rgba(218, 218, 218, 0) 95%),
          linear-gradient(to top right,
            rgba(218, 218, 218, 0) 5%,
            rgba(218, 218, 218, 0) calc(50% - 0.8px),
            rgba(218, 218, 218, 1) 50%,
            rgba(218, 218, 218, 0) calc(50% + 0.8px),
            rgba(218, 218, 218, 0) 95%);
        text-align: center;

        * {
          color: $color-dark-50;
        }

        span {
          padding: 15px;
          background: $color-gray-50;

          h4 {
            font-size: 25px;
            line-height: 1;
            margin: 0 0 10px 0;
          }

          small {
            font-size: 20px;
            unicode-bidi: plaintext;
            opacity: 0.8;
          }
        }
      }
    }

    @include mediaMaxWidth($screen-desktop-small) {
      @include b-radius($b-radius-sm);

      &--empty {
        >div {
          >span {
            padding: 10px;

            h4 {
              font-size: 18px;
              margin: 0 0 4px 0;
            }

            small {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}