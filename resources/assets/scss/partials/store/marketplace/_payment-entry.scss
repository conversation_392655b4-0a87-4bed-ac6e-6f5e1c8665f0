.payment-entry {
	$payment-entry-root: &;
	@include flexable(center, flex-start, row);
	width: 100%;
	position: relative;
	padding: 20px;
	border-bottom: 1px solid $color-gray-200;

	&__thumb {
		flex: 0 0 65%;

		> a {
			display: inline-block;
			float: right;
			margin: 0 0 0 10px;

			img {
				width: auto;
				max-height: 50px;
			}
		}

		> div {
			a {
				display: block;
				font-size: 16px;
				color: $color-dark-300;
			}

			> div {
				span {
					margin-left: 5px;
				}

				small {
					font-size: 11px;
					color: $color-dark-100;

					b {
						font-weight: bold;
						color: $color-dark-200;
						text-decoration: underline;
					}
				}
			}
		}
	}

	&__qty, &__price {
		text-align: center;
		flex: auto;
	}

	&__delete {
		flex: 0 0 30px;
	}

  &--plans-prices {
	margin-bottom: 20px;
    .previous {
      margin: 0 5px;
      text-decoration: line-through;
    }
  }

	&:last-child {
		border: none;
	}

	@include mediaMaxWidth($screen-phones) {
		padding: 15px;
		&__thumb {
			> a {
				img {
					max-height: 40px;
				}
			}

			> div {
				> div {
					small {
						display: table;
						margin: 10px 0 0;
					}
				}
			}
		}
		&__price {
			margin-left: 15px;
		}
	}

	&[data-subscribtion="yearly"] {
		#{$payment-entry-root} {
			&__thumb {
				> div {
					> div {
						.badge {
							&.badge--grey {
								color: $color-primary;
								background: rgba($color-primary, 0.1);

								&:hover {
									color: $color-primary !important;
									background: rgba($color-primary, 0.15);
								}
							}
						}
					}
				}
			}
		}
	}
}

// generic section
.salla-addon__gift {
	display: inline-block;
	@include b-radius(50px);
	padding: 2px 10px;
	background-color: $color-gray-25;

	i {
		display: inline-block;
		margin-left: 8px;
	}
}