.payment-detail {
  .rec-price-wrapper {
    .product-price {
      width: 100%;
      font-family: $font-main;
      font-size: 18px;
      color: $color-dark-300;
      text-align: left;
    }
  }
}

.rec-list {
  &.payment-items {
    margin-bottom: 20px;

    @include mediaMaxWidth($screen-phones) {
      margin-bottom: 15px;
    }
  }

  &.payment-totals,
  &.payment-methods {
    > li {
      width: 100%;
      padding: 8px 0;
      &.payment-methods__alt {
        padding-bottom: 0;
      }
    }
  }

  &.payment-totals {
    background: $color-gray-50;
    border-top: 1px solid $color-gray-200;
    border-bottom: 1px solid $color-gray-200;
    padding: 0 20px;
    margin: 0 0 10px 0;

    > li {
      @include flexable(center, space-between, row);

      * {
        color: $color-dark-300;
      }

      input[type=checkbox],
      input[type=radio] {
        &:disabled {
          + label {
            cursor: default;
            color: $color-dark-100;

            &:before {
              border: 1px solid $color-dark-100;
            }
          }
        }
      }

      // fix from legacy styling ---
      label {
        line-height: unset;
      }

      &.payment-subtotal {
        padding: 12px 0 15px;
        font-size: 18px;
        border-bottom: 1px solid $color-gray-50;
        margin-bottom: 10px;

        .rec-price-wrapper {
          .product-price {
            font-size: 18px;
          }
        }
      }

      &.payment-coupon {
        flex-direction: column;
        align-items: flex-start;
        padding-bottom: 20px;

        > div {
          width: 100%;

          &#coupon_code {
            position: relative;

            .loader {
              position: absolute;
              top: 10px;
              left: 10px;
            }

            .form-group {
              margin: 20px 0 0;

              &.coupon-field {
                .form-control {

                  &:focus,
                  &:active {
                    border-color: $color-primary;
                    box-shadow: 0 0 4px 5px rgba($color-primary, 0.05);
                  }
                }

                .coupon-submit {
                  min-width: 100px;
                  @include b-radius($b-radius-sm);
                  @include transi(background);

                  i {
                    display: inline-block;
                    vertical-align: middle;
                    margin-left: 5px;
                    font-size: 14px;
                    color: white;
                  }

                  @include mediaMaxWidth($screen-phones) {
                    top: 11%;
                    padding-top: 5px;
                  }
                }
              }
            }
          }
        }
      }

      &.payment-total-price {
        padding: 12px 0 13px;
        border-top: 2px solid $color-gray-200;

        h4 {
          font-family:$font-main;
          font-size: 16px;
          color: $color-dark-300;
          margin: 0;
        }

        .rec-price-wrapper {
          .product-price {
            font-size: 18px;
          }

          small {
            font-size: 16px;
          }
        }
      }
    }

    &.summary {
      border: none;
      margin: 0;
      padding: 0;

      li {
        padding-bottom: 0;
        border: none;
      }
    }

    @include mediaMaxWidth($screen-phones) {
      padding: 0 15px;
      margin: 0 0 15px 0;
    }
  }

  // cards list --
  &.cards-list,
  &.alt-payments {
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 10px -5px 0;

    > li {
      flex: 1 0 calc(25% - 10px);
      padding: 0;
      margin: 5px;
    }

    @include mediaMaxWidth($screen-phones) {
      > li {
        flex: 1 0 calc(50% - 10px);
      }
    }
  }

  &.alt-payments {
    li {
      padding: 0;

      a {
        @include flexable();
        width: 100%;
        height: auto;
        min-height: 43px;
        border: 1px solid $color-gray-200;
        @include transi();
        @include b-radius($b-radius-sm);
        font-size: 14px;
        color: $color-dark-200;

        i {
          font-size: 15px;
          display: inline-block;
          vertical-align: middle;
          margin: 0 0 0 10px;
        }

        img {
          display: inline-block;
          width: auto;
          max-height: 16px;
        }
      }

      &.active {
        a {
          color: $color-dark-300;
          border-color: $color-primary;

          &:hover {
            border-color: $color-primary;
          }
        }
      }

      &:hover,
      &:active,
      &:focus {
        a {
          color: $color-dark-300;
          border-color: $color-gray-300;
        }
      }
    }
  }
}

.new-card,
.payment-alt {
  display: none;
}

.payment-logos {
  height: 24px;
  background-size: 100% !important;

  &.visa-master {
    width: 70px;
    background: url("https://cdn.assets.salla.network/dash/cp/assets/images/visa_mastercard.png") center no-repeat;
  }

  &.visa {
    width: 40px;
    background: url("https://cdn.assets.salla.network/dash/cp/assets/images/payment_methods/visa.svg") center no-repeat;
  }

  &.master_card {
    width: 40px;
    background: url("https://cdn.assets.salla.network/dash/cp/assets/images/payment_methods/master_card.svg") center no-repeat;
  }

  &.mada {
    width: 40px;
    background: url("https://cdn.assets.salla.network/dash/cp/assets/images/mada.png") center no-repeat;
  }

  &.apple-pay {
    width: 45px;
    background: url("https://cdn.assets.salla.network/dash/cp/assets/images/applepay.png") center no-repeat;
  }
}

.payment-methods {
  * {
    &.tooltip-toggle {
      &.primary {
        border: 0;

        .tooltip-content {
          right: -10%;
        }
      }
    }

    &:disabled {
      + label {
        color: $color-dark-100 !important;
      }
    }
  }
}