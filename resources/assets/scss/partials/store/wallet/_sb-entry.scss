.sb-entry {
  @include flexable(space-between, center, column);
  width: 100%;
  height: 100%;
  min-height: 280px;
  position: relative;
  padding: 20px 10px;
  @include b-radius($b-radius-sm);
  @include transi();
  background: white;
  border: 1px solid $color-gray-200;
  > div {
    text-align: center;
    img {
      width: auto !important;
      max-height: 30px;
    }
    p {
      display: none;
      margin: 0;
      font-size: 12px;
      color: $color-gray-400;
		text-align: center;
    }
    input {
      &.form-control {
        max-width: 85%;
        min-height: 40px;
        background: $color-white;
        border: 1px solid $color-primary;
        box-shadow: inset 0 1px 9px 0 rgba(93,213,196,0.16);
        padding: 6px 5px 7px;
        font-size: 25px;
        text-align: center;
        color: $color-dark-200;
        text-align: center;
        margin: 18px auto;
        -moz-appearance: textfield;
        &[disabled] {
          background: transparent;
          border: 1px solid $color-gray-25;
          box-shadow: none;
        }
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
      }
    }
    .btn {
      &.balance-edit, &.balance-cancel {
        padding: 5px 10px 7px;
        font-size: 11px;
      }
      &.balance-edit {
        @include b-radius(50px);
        background: $color-gray-25;
        color: $color-dark-200;
        &:before {
          content: '\ee29';
          font-family: sallaicons;
          font-size: 11px;
          display: inline-block;
          vertical-align: middle;
          line-height: 1;
          margin-left: 5px;
        }
        &:hover {
          background: $color-gray-200;
        }
        &.active {
          color: $color-white;
          background: $color-primary;
          &:before {
            content: '\ea9d';
          }
        }
      }
      &.balance-cancel {
        position: absolute;
        bottom: 10px;
        right: 50%;
        transform: translateX(50%);
        color: $color-danger;
        background: transparent;
        visibility: hidden;
        opacity: 0;
      }
    }
    &:first-child {
      @include flexable();
      flex: 1;
    }
  }
  &:hover {
    box-shadow: 0 0 10px rgba(black, 0.05);
  }
  &.active {
    padding-bottom: 50px;
    border-color: $color-primary;
    box-shadow: 0 0 10px rgba($color-primary, 0.2);
    p {
      display: block;
    }
    .btn {
      &.balance-cancel {
        opacity: 1;
        visibility: visible;
      }
    }
  }
  @include mediaMaxWidth($screen-desktop) {
    > div {
      img {
        max-height: 20px;
      }
    }
  }
  @include mediaMaxWidth($screen-phones) {
    > div {
      img {
        max-height: 40px;
      }
    }
  }
}
