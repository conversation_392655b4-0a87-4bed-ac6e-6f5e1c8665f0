.wallet-accounts {
	grid-gap: 50px !important;
	> div {
		&.wallet__balance {
			@at-root .current-balance {
				&__item {
					font-size: $text-xx-medium;
					color: $color-dark-300;
					unicode-bidi: plaintext;
					text-align: right;
					line-height: 1;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 50px 0 0;

					> span {
						display: inline-block;
					}
				}

				&__value {
					font-size: $text-giant;
					transform: translateY(-10px);
					margin-left: 10px;
					&.suspend {
						opacity: .5;
					}
				}

				&__label {
					color: $color-dark-100;
				}

				&__currency {
					color: $color-gray-400;
				}

				&.empty {
					h2 {
						font-size: 30px;
						color: $color-gray-200;
					}
				}
			}
		}

		&.wallet__sub-balances {
			@include b-radius($b-radius-sm 0 0 $b-radius-sm !important);
			background: $color-gray-25 !important;
			@include mediaMaxWidth($screen-phones) {
				padding-bottom: 50px;
			}
		}

		.tooltip-toggle.info {
			width: 18px;
			height: 18px;
			@include b-radius(50%);
			padding: 0;
			background: $color-primary;
			margin: 0 10px 0 0;
			display: inline-flex;
			justify-content: center;
			align-items: center;

			i {
				font-size: 12px;
				color: white;
				line-height: 1;
				margin: 0;
			}
		}
	}

	@include mediaMaxWidth($screen-tablet-l) {
		grid-template-columns: repeat(1, 1fr) !important;
		grid-gap: 20px !important;
		.current-balance__value {
			font-size: $text-xxx-larger;
		}
	}
}

#balance_management {

	&:before {
		content: '\ef1f';
		font-family: $font-sallaIcon;
		font-size: $text-medium;
		line-height: 1;
		color: $color-white;
		display: inline-block;
		vertical-align: middle;
		margin: 0 0 0 12px;
	}

	@include mediaMaxWidth($screen-phones) {
		min-width: unset;
		span {
			display: none;
		}
		&:before {
			margin: 0;
		}
	}
}

.recent-transactions {
	.rec-title-block {
		@include flexable(center, flex-start, row);
		width: 100%;
		margin: 0;

		h1 {
			font-size: $text-medium;
			order: 0;
			margin: 0 0 0 10px;
		}

		a {
			&.btn.btn-link {
				order: 2;
				padding: 10px 0;
				margin-right: 10px;

				&:after {
					content: '\ea65';
					font-family: $font-sallaIcon;
					display: inline-block;
					vertical-align: middle;
					font-size: $text-xxxx-small;
					margin: 0 5px 0 0;
				}

				&:before {
					content: '';
					display: block;
					width: 100%;
					height: 1px;
					background: $color-primary;
					@include transi();
					position: absolute;
					bottom: 8px;
					right: 0;
					transform-origin: right;
					transform: scaleX(0);
				}

				&:hover {
					&:before {
						transform: scaleX(1);
					}
				}
			}
		}

		&:after {
			content: '';
			display: inline-block;
			height: 1px;
			background: $color-gray-200;
			flex: 1;
			order: 1;
			transform: translateY(5px);
		}
	}

	li {
		@include flexable(center, flex-start, row);
		position: relative;
		width: 100%;
		padding: 8px 0;
		font-size: $text-small;

		span {
			flex: 0;
			margin-left: 16px;

			&.trx-date {
				color: $color-gray-400;
			}

			&.trx-title {
				flex: 1;
			}

			&.trx-amount {
				.rec-price-wrapper {
					b {
						font-size: 14px;
					}
				}
			}

			&:last-child {
				margin-left: 0;
			}
		}
	}

	@include mediaMaxWidth($screen-phones) {
		li {
			flex-direction: column;
			align-items: flex-start;
			@include b-radius($b-radius-sm);
			padding: 40px 0 0;
			margin: 0;
			&:not(:last-of-type) {
				border-bottom: 1px solid $color-gray-200;
				padding-bottom: 20px;
				margin-bottom: 5px;
			}

			span {
				margin: 0;

				&.trx-date {
					font-size: $text-xx-small;
					position: absolute;
					top: 10px;
					left: 0;
				}

				&.trx-title {
					width: 100%;
					padding-top: 10px;
					font-size: $text-x-small;
				}

				&.trx-amount {
					position: absolute;
					top: 11px;
					right: 0;

					b {
						font-size: $text-small;
					}
				}
			}

			&:before {
				position: absolute;
				top: 10px;
				right: 5px;
				margin: 0;
			}
		}
	}
}

#sub_balances_list {
	.owl-stage-outer {
		padding: 5px 0;
	}

	.owl-dots {
		position: absolute;
		top: -47px;
		left: 80px;
		@include mediaMaxWidth($screen-laptop) {
			top: -45px;
		}
		@include mediaMaxWidth($screen-phones) {
			width: 100%;
			top: unset;
			right: 50%;
			transform: translateX(50%);
			bottom: -30px;
		}
	}

	.owl-nav {
		position: absolute;
		top: -45px;
		left: 0;

		> div {
			position: absolute;

			&.owl-prev {
				left: 35px;
			}

			&.owl-next {
				left: 0;
			}
		}
	}
}

// TODO: chagne this to a modifier
.rec-list {
	&--table {
		font-size: $text-small;
	}
}

#list_monthly_subscriptions_invoice {
	@include flexable(flex-start, flex-start, column);
	width: 100%;

	.invoice-list {
		flex: 1;
	}
}


// balance control Modal

.withdraw-input-group {
	overflow: hidden;
}

#withdraw_balance_tab {
	.alert-box a {
		text-decoration: underline;

		&:hover {
			text-decoration: none;
		}
	}
}
