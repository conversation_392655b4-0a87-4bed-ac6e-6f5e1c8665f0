.panel {
	&.wallet-panel {
		$wallet-panel-root: wallet-panel;

		> * {
			width: 100%;
		}

		.#{$wallet-panel-root} {
			&__title {
				@include flexable(center, space-between, row);
				margin: 0 0 20px 0;

				h1 {
					font-family:$font-main;
					font-size: 15px;
					margin: 0;

					> i {
						display: inline-block;
						vertical-align: middle;
						line-height: 1;
						margin: 0 0 0 5px;
						font-size: 22px;
					}
				}

				.title-controls {
					.btn.btn-link {
						line-height: 1;
						padding: 5px;
						color: $color-dark-300;
						@include transi(color);

						&:hover {
							color: $color-primary;
						}
					}
				}
			}

			&__body {
				flex: 1;

				&.empty {
					@include flexable();

					.rec-placeholder {
						> i {
							font-size: 50px;
						}

						.btn {
							font-size: 13px;
							margin-top: 20px;

							i {
								font-size: 18px;
								color: white;
								margin-left: 10px;
							}
						}
					}
				}
			}

			@include mediaMaxWidth($screen-desktop) {
				padding: 20px;
			}
			@include mediaMaxWidth($screen-laptop-small) {
				padding: 15px;
				&__title {
					h1 {
						font-size: 15px;

						> i {
							font-size: 20px;
							margin: 0 0 0 8px;
						}
					}
				}
			}
		}

		// invoice panel ---
		&.invoice {
			.wallet-panel__title {
				@include mediaMaxWidth($screen-phones) {
					flex-direction: column;
					align-items: flex-start;
					> * {
						width: 100%;
						/*.bootstrap-select {
						  float: left;
						}
						&:last-child {
						  margin: 10px 0 0 0;
						  text-align: left;
						}*/
					}
				}
			}
		}
		@include mediaMaxWidth($screen-phones) {
			min-height: unset;
			margin-bottom: 0;
		}
	}
}

.credit-card-entry {
	width: 100%;
	@include flexable(center, space-between, row);
	padding-right: 0 !important;
	padding-left: 0 !important;

	label {
		@include flexable(center, flex-start, row);
		flex: auto;
		padding-left: 10px;
	}

	.more-opt {
		order: 10;
	}

	&__logo {
		display: inline-block;

		&.img-placeholder {
			width: 40px;
			height: 30px;
			margin: 0;
			position: relative;
			@include b-radius($b-radius-sm);
			text-align: center;
			background: $color-gray-100;

			img {
				display: inline-block;
				vertical-align: middle;
				width: 80%;
				height: auto;
			}
		}
	}

	&__detail {
		display: inline-block;
		line-height: 1;

		b {
			display: block;
			font-family: Arial, serif;
			font-weight: bold;
			font-size: 12px;
			color: $color-dark-200;
			unicode-bidi: plaintext;
			letter-spacing: 1px;
		}

		small {
			display: block;
			line-height: 1;
			font-size: 10px;
		}
	}

	&.is-default {
		&:after {
			content: "\ea9d";
			font-family: $font-sallaIcon;
			font-size: 16px;
			color: $color-secondary-d;
			background: $color-gray-100;
			margin-left: 10px;
			display: block;
			width: 30px;
			height: 30px;
			line-height: 30px;
			text-align: center;
			@include b-radius(50%);
		}
	}
}

.inv-credits {
	grid-template-columns: 2fr 1fr;
	margin-bottom: 50px;

	.panel {
		height: 100%;
	}

	@include mediaMaxWidth($screen-laptop) {
		grid-template-columns: 1fr;
		grid-gap: 0;
		.panel {
			height: auto;
		}
	}
}
