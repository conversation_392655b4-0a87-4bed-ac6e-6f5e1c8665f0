.promotion-coupon {
  position: relative;
  @include flexable(center, flex-start, column);

  >* {
    width: 100%;
    text-align: center;

    &.badge {
      width: auto;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .rec-title-block {
    @include flexable(center, space-between, row);
    border: 1px dashed $color-gray-200;
    padding: 10px;
    @include b-radius($b-radius);
    background: $color-gray-50;
    position: relative;
    z-index: 1;

    &--large{
      margin-bottom: 15px;
      margin-top: 5px;
    }

    h1 {
      line-height: 1;
      margin: 0 20px;
      white-space: nowrap;

      .icon{
        margin-left: 10px;
      }

      > .promo-text{
        display: inline-block;
        transform: translateY(-3px);
      }
    }

    // &:before {
    //   content: '';
    //   display: inline-block;
    //   width: 30px;
    //   height: 20px;
    //   background: url("/cp/assets/images/coupon.svg") center no-repeat;
    // }

    .btn {
      width: 28px;
      height: 28px;
      @include b-radius(50%);
      padding: 0;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      color: $color-primary;
      border-color: rgba($color-primary, 0.3);
      background-color: rgba($color-primary, 0.1);
      transition: 0.3s;
      flex-shrink: 0;

      &:focus{
        background-color: rgba($color-primary, 0.1) !important;
        border-color: rgba($color-primary, 0.3) !important;
      }

      &:hover,
      &:active{
        background-color: rgba($color-primary, 0.2);
      }

      .icon{
        font-size: 14px;
      }

      // &:before {
        // content: '';
        // display: inline-block;
        // width: 18px;
        // height: 18px;
        // position: absolute;
        // top: 50%;
        // right: 50%;
        // transform: translateY(-50%) translateX(50%);
        // background: url("/cp/assets/images/icons/copy.png") center no-repeat;
        // background-size: contain;
      // }

      &.copied {
        &:before {
          display: none;
        }
      }
    }
  }

  .current-banlance{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    color: $color-gray-400;
    border: 1px dashed $color-gray-200;
    @include b-radius(30px);

    span{
      display: inline-block;
    }

    &__value{
      font-size: 24px;
      color: $color-dark-300;
      margin: 0 10px;
      transform: translateY(-3px);
      font-family: $font-main, serif;
    }
  }

  .badge {
    font-family: $font-main, serif;
    font-size: 12px;
    cursor: default;
  }

  .btn.btn-link {
    font-size: 15px;
    padding: 0;
  }

  #copy_code_done{
    position: absolute;
    z-index: 0;
    top: -20px;
    font-size: 11px;
    transform: translateY(20px);
    opacity: 0;
    transition: 0.3s cubic-bezier(0.55, 0, 0.1, 1) 0s;

    &.is-copied-in{
      transform: translateY(0);
      opacity: 1;
    }

    &.is-copied-out{
      transform: translateY(-20px);
      opacity: 0;
    }
  }

  p {
    text-align: center;
    margin-bottom: 10px;
    color: $color-gray-400; 
  }

  .rec-list {
    &.social-links {
      justify-content: center;
      margin: 0 -8px 15px;

      li {
        margin: 0 8px;
        color: $color-gray-400;

        a {
          font-size: 15px;
          color: $color-dark-100;
          display: inline-block;
          line-height: 1.4;
          transition: 0.3s;

          &:hover{
            color: $color-primary;
          }
        }
      }
    }
  }

  .more-links{
    display: flex;
    justify-content: center;
    align-items: center;

    a{
      color: $color-dark-100;
      transition: 0.3s;

      &:hover{
        color: $color-primary;
      }
    }

    .links-sep{
      height: 11px;
      width: 1px;
      background-color: $color-gray-200;
      display: inline-block;
      transform: translateY(4px);
    }
  }
}