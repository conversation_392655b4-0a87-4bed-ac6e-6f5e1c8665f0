/*.rec-list {
	&.features-list {
		width: 100%;
		margin: 0 0 30px 0;

		li {
			flex: auto;
			padding: 0 10px;

			&:before {
				display: none;
			}
		}

		@include mediaMaxWidth($screen-laptop) {
			margin: 0 0 15px 0;
		}
		@include mediaMaxWidth($screen-phones) {
			flex-direction: column;
			li {
				width: 100%;
			}
		}
	}
}*/

.features-list {
	li {
		padding: 0 !important;
		margin: 0 !important;
		&:before {
			display: none !important;
		}
	}
}

.feature-entry {
	@include flexable(center, flex-start, column);
	padding: 0;
	margin: 30px 0 !important;
	.img-placeholder {
		* {
			font-size: 25px;
		}
	}
	h4 {
		font-size: 15px;
		margin: 0 0 10px 0 !important;
		text-align: center;
	}

	&__meta {
		color: $color-dark-200;
		text-align: center;
	}

	@include mediaMaxWidth($screen-laptop) {
		.img-placeholder {
			width: 60px;
			height: 60px;
			* {
				font-size: 18px;
			}
		}
	}
}
