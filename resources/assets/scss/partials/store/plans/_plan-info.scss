.plan-meta {
	grid-template-columns: 3fr 1fr;

	.panel {
		height: 100%;
		margin: 0;
	}

	@include mediaMaxWidth($screen-laptop) {
		grid-template-columns: 1fr;
	}
}

.plan { 
	.rec-title-block {
		margin-bottom: 15px;

		h1 {
			display: inline-block;
			line-height: 1;
		}

		.badge {
			font-size: 11px;
			margin: 0 15px 0 0;
		}
	}

	p {
		max-width: 90%;
	}

	.btn {
		min-height: 40px;
		padding-right: 40px;
		padding-left: 40px;
		font-size: 14px;
		margin-left: 5px;
    transition: 0.3s;
    margin-bottom: 10px;

		&-outline-primary {
			color: $color-primary;
      border-color: $color-primary;
      
      &:hover{
        color: $color-white;
        background-color: $color-primary;
      }
		}
	}

	&:before, &:after {
		display: none;
	}

	@include mediaMaxWidth($screen-desktop) {
		.rec-title-block {
			h1 {
				display: block;
			}

			.badge {
				display: table;
				margin: 20px 0 0 0;
			}
		}
	}
	@include mediaMaxWidth($screen-desktop-small) {
		.btn {
			padding-right: 12px;
			padding-left: 12px;
		}
	}
	@include mediaMaxWidth($screen-phones) {
		text-align: center;
		.flexable {
			flex-direction: column;
			align-items: center;

			> * {
				&:first-child {
					order: 1;
				}

				&:last-child {
					order: 0;
					margin: 0 0 15px 0;
				}
			}

			.badge {
				margin: 20px auto 0;
			}

			p {
				max-width: 95%;
				margin: 0 auto 30px;
				text-align: center;
			}
		}
	}


  &-meta{
    .panel-body{
      height: 100%;
    }
  }

	&--current {
    display: flex;
    height: 100%;
    justify-content: space-between;
    align-items: center;

    &__inner{
      display: flex;
      flex-direction: column;
      height: 100%;
    }

		img {
			display: block;
			max-height: 100px;
    }
    
    .plan-avatar{
      width: 320px;
      height: 190px;
      max-width: 50%;
      flex-shrink: 0;
      background-image: url("/cp/assets/images/online-shopping.svg");
      background-position: center;
      background-repeat: no-repeat;
    }

    &__btns{
      margin-top: 20px;
    } 

    @include mediaMaxWidth($screen-tablet-l){
      flex-direction: column;

      .plan-avatar{
        order: -1;
        margin-bottom: 20px;
        width: 100%;
        max-width: 60%;
      }

      &__btns{
        margin-top: 0;
      }
    }
	}
}
