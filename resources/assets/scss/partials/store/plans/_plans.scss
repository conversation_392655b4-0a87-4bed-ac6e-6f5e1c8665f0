.plan-details {
	.rec-title-block {
		@include flexable(center, space-between, row);
		h1 {
			font-size: 25px;
		}
		@include mediaMaxWidth($screen-phones) {
			h1 {
				font-size: 20px;
			}
		}
	}

	.btn {
		&.btn-plan-upgrade {
			width: auto !important;
			min-height: 50px;
			font-size: 16px;
			line-height: 25px;
			padding: 10px 15px 7px;
			@include b-radius(2px);
			&:before {
				content: '\eed6';
				font-family: sallaicons;
				font-size: 18px;
				line-height: 1;
				display: inline-block;
				margin-left: 15px;
				padding-left: 15px;
				border-left: 1px solid rgba(white, 0.6)
			}

			@include mediaMaxWidth($screen-phones) {
				min-height: 45px;
				font-size: 16px;
				line-height: 21px;
			}
		}
	}
}
