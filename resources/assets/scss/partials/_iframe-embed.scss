// style customization for iFrame embed
body {
  &.iframe-embed {
    :root {
      --page-container-width: 1536px;
    }

    height: auto !important;
    min-height: auto !important;

    // custom.css
    overflow: hidden;
    background: #F7F7F7;

    .page-container {
      max-width: var(--page-container-width);
      padding: 0 15px 50px;
      margin: 0 auto;
      min-height: auto !important;
    }

    #page_container #content_box {
      padding-right: 0;
    }

    .content {
      min-height: unset;
      padding: 0 !important;
    }

    @media (max-width: 992px) {
      .content {
        padding: 0;
      }

      .navbar-container {
        padding: 0;
      }
    }

    .fade {
      transition-property: opacity;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 700ms;
    }

    .modal-backdrop {
      opacity: 1 !important;
      background: rgba(0, 78, 92, 0.8);
    }

    .jconfirm .jconfirm-box {
      margin-top: 1rem !important;
    }

    .jconfirm-bg {
      opacity: 1 !important;
    }


    .swal2-container {
      align-items: flex-start;
    }

    .swal2-container {
      align-items: flex-start;
      background: rgba(0,78,92,.5) !important;
      .swal2-modal {
        margin: 1em auto;
      }
    }


    // _layout.scss
    .store-setup-row {
      padding: 0;
    }

    .content-wrapper {
      .content,
      .page-header {
        @include mediaMinWidth($screen-desktop-small) {
          max-width: unset;
        }
      }
    }

    .content-wrapper {
      @include mediaMaxWidth($screen-tablet-p) {
        padding-top: 0 !important;
      }
      .iframe-content{
        border-top: none !important;
        .dark &{
          border-color: unset !important;
        }
        @include mediaMaxWidth($screen-tablet-p) {
          padding-top: 0;
        }
      }
    }

    #res_sf {
      display: none !important;
    }

    // _filters.scss
    .rec {
      &-filter-cont {
        height: auto;
        top: 20px;
        border-radius: 1rem;
        max-height: calc(100vh - 40px);
        &:before,
        &:after {
          display: none !important;
        }

      }
      &-filter-wrapper {
        background-color: rgba($color-primary-l, 0.8);
        &.reveal {
          .rec-filter-cont {
            left: 20px;
          }
        }
      }
    }

    .ife {
      &-hidden {
        display: none !important;
      }
      &-p-0 {
        padding: 0 !important;
      }
      &-m-0 {
        margin: 0 !important;
      }
      &-mr-5 {
        margin-right: 5px !important;
      }
      &-mb-25 {
        margin-bottom: 25px !important;
      }
      &-modal-fix {
        display: block;
        position: fixed;
        top: 30%;
      }
      &-flex {
        display: flex !important;
      }
      &-justify-between {
        justify-content: space-between !important;
      }
    }

    #load_shipping_div {
      min-height: auto !important;
    }
    #salla_plans {
      padding-top: 0;
    }
    .salla-addon--plan {
      &.single {
        max-width: unset;
        margin: 0 auto;
      }
    }
    .modal {
      .modal-content {
        @include mediaMaxWidth($screen-tablet-p) {
          margin-bottom: 120px;
          padding-bottom: 120px;
        }
      }
    }
  }
  .new-dashboard-switch {
    background: url("https://cdn.salla.network/images/salla4-btn.png") center no-repeat !important;
    width: 105px;
    height: 38px;
    background-size: contain !important;
    padding: 0;
    max-width: unset;
    max-height: unset;
    border: none;
    box-shadow: none !important;
    &--mobile {
      @include mediaMinWidth($screen-tablet-p) {
        display: none !important;
      }
    }
    &:hover {
      background: url("https://cdn.salla.network/images/salla4-btn.png") center no-repeat !important;
      background-size: contain !important;
    }
  }
}