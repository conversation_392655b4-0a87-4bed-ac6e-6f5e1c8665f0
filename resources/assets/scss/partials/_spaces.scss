.no-p {
  padding: 0 !important;
}

// margin & padding ---
@each $space in 0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50 {
  .m-#{$space} {
    margin: #{$space} !important;
  }
  .mt-#{$space} {
    margin-top: #{$space}px !important;
  }
  .mb-#{$space} {
    margin-bottom: #{$space}px !important;
  }
  .mx-#{$space} {
    margin-left: #{$space}px !important;
    margin-right: #{$space}px !important;
  }
  .my-#{$space} {
    margin-top: #{$space}px !important;
    margin-bottom: #{$space}px !important;
  }
  .p-#{$space} {
    padding: #{$space}px !important;
  }
  .pt-#{$space} {
    padding-top: #{$space}px !important;
  }
  /*.pl-#{$space} {
    padding-left: #{$space}px !important;
  }*/
  .pb-#{$space} {
    padding-bottom: #{$space}px !important;
  }
  /*.pr-#{$space} {
    padding-right: #{$space}px !important;
  }*/
  .px-#{$space} {
    padding-right: #{$space}px !important;
    padding-left: #{$space}px !important;
  }

  .py-#{$space} {
    padding-top: #{$space}px !important;
    padding-bottom: #{$space}px !important;
  }
}
