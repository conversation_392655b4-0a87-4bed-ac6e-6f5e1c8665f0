//responsive & spaces utilities
.col-xs-5ths,
.col-sm-5ths,
.col-md-5ths,
.col-lg-5ths {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col-xs-5ths {
  width: 20%;
  float: right;
}

@media (min-width: 768px) {
  .col-sm-5ths {
    width: 20%;
    float: right;
  }
}

@media (min-width: 992px) {
  .col-md-5ths {
    width: 20%;
    float: right;
  }
}

@media (min-width: 1200px) {
  .col-lg-5ths {
    width: 20%;
    float: right;
  }
}

.d-block {
  display: block !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mr-4 {
  margin-right: 1.5rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.d-none {
  display: none !important;
}

.p-md-normal {
  padding: 10px !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.py-15 {
  padding-block: 15px !important;
}

@media (min-width: 768px) {
  .justify-content-md-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .p-md-normal {
    padding: 15px 20px !important;
  }

  .d-md-flex {
    display: flex !important;
  }

  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-none {
    display: none !important;
  }

  .ml-md-10 {
    margin-right: 10px !important;
  }

  .mr-md-0 {
    margin-right: 0 !important;
  }

  .d-md-block {
    display: block !important;
  }

  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important;
  }

  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }
}