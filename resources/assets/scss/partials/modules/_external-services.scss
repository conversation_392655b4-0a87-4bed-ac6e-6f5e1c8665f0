.rec {
&--es {
  .store-setup-heading {
    margin-bottom: 15px;
    h6 {
      margin-bottom: 0;
    }
    @include mediaMaxWidth($screen-phones) {
      padding: 0 8px;
    }
  }

    .store-setup-row {
      margin-bottom: 30px;
      padding-bottom: 30px;
      border-bottom: 1px solid $color-gray-200;
    }

  .store-setup-item-wrapper {
    //@include flexable(flex-start, flex-start, column);
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
    @include transi(box-shadow);

      .store-setup-item {
        height: auto;
        min-height: 130px;

        img {
          display: block;
          max-width: 90%;
          width: auto;
          height: auto;
          max-height: 50px;
        }
      }

      .store-setup-details {
        a {
          @include transi();
          padding: 12px 8px;

          &:hover {
            color: darken($color-gray-400, 20%);
            background-color: rgba($color-gray-200, 0.5);
          }

          &.link-btn {
            i {
              display: inline-block;
              transform: translateY(2px);
            }
          }
        }
      }

      &:hover {
        box-shadow: 0px 2px 4px rgba($color-black, 0.09);
      }
    }
}
}