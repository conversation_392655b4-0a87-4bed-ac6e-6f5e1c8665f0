.design-menu {
  // --- custom styling -- Aug 19 2019
  .mnu {

    .tab-content {
      overflow: initial;

      label, .control-label {
        font-size: $base-size;
      }
    }

    &__title {
      @include flexable(center, space-between, row);
      margin: 0 0 20px 0;

      .title {
        flex: 1;

        h2 {
          font-size: $text-xx-larger;
          margin: 0;

        }

        small {
          display: block;
          color: $color-dark-100;
        }
      }

      .controls {
        #new-item {
          width: 40px;
          height: 40px;
          @include b-radius($b-radius-sm);
          padding: 5px 10px 8px;
          margin-left: 5px;
          color: $color-dark-100;
          background-color: transparent;
          border: 1px solid rgba($color-dark-100, 0.5);

          &:hover {
            background-color: rgba($color-dark-100, 0.2);
            border: 1px solid rgba($color-dark-100, 0.8);
          }
        }

        #save-menu {
          height: 40px;
          padding: 8px 30px 10px;
          @include b-radius($b-radius-sm);
          color: $color-white;
          background-color: $color-primary;

          &:hover {
            background-color: darken($color-primary, 10%);
          }
        }

      }
    }

    &__error {
      @include strip-ul();
      display: block;
      width: 100%;
      height: auto;
      padding: 10px 20px 15px;
      margin: 10px 0;
      @include b-radius($b-radius-sm);

      li {
        @include flexable(center, flex-start, row);
        flex-wrap: wrap;
        margin: 0 0 10px 0;

        i {
          display: inline-block;
          float: right;
          margin: 4px 0 0 10px;
          vertical-align: middle;
        }

        &:last-child {
          margin: 0;
        }
      }

      &.danger {
        color: $color-danger;
        background-color: rgba($color-danger, 0.1);
      }
    }

    &__options {
      @include flexable(flex-start, flex-start, row);
      width: 100%;
      position: relative;
      padding: 40px !important;
      margin: 0 !important;
      @include b-radius($b-radius-sm !important);
      border: 1px solid $color-gray-100;

      .fields-cont {
        @include flexable(flex-start, space-between, row);
        width: 100%;
        flex-wrap: wrap;
        padding: 20px;
        @include b-radius($b-radius-sm);
        border: 1px solid rgba($color-gray-200, 0.8);
        background-color: rgba($color-gray-200, 0.2);
      }

      .option-field {
        @include flexable(center, flex-start, row);
        min-height: 40px;
        margin: 0 0 20px 0;
        width: calc(50% - 15px);

        .of {
          &-label {
            flex-shrink: 0;
            margin: 0 0 0 10px;
          }

          &-val {
            flex: 1;

            .box-select {
              width: 100%;
              height: 35px;
              border-color: $color-gray-200;
              background-color: $color-white;

            }

            .form-control {
              border: 1px solid $color-gray-200;
              padding: 0 10px;
              background-color: transparent;
            }

            .vue-dynamic-select {
              min-height: 35px;
              border-color: $color-gray-200;

              .search {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                padding-right: 5px;
                background-color: transparent;
              }

              .dropdown {
                float: left;
              }

              .placeholder {
                padding-right: 0;
              }

              .selected-option {
                padding-right: 0;
              }

              .result-list {
                max-height: 160px;
                margin-top: 30px;
                border-color: $color-gray-200;
                overflow-y: auto;

                .result {
                  min-height: 20px;
                }
              }
            }
          }
        }

        &.wide {
          flex: 1;
          width: 100%;
        }

        &.submit {
          justify-content: flex-start;

          button {
            &.btn {
              padding-right: 30px;
              padding-left: 30px;
            }
          }
        }

        &:last-child {
          margin: 0;
        }
      }

      &--row {
        flex-direction: column;
      }

      &.open {
        padding-top: 10px !important;
        @include b-radius(0 0 $b-radius-sm $b-radius-sm);
        border: 1px solid $color-gray-100;
        border-top: none;
        margin: 0 0 5px 0 !important;
      }

      @include mediaMaxWidth($screen-phones) {
        padding: 30px !important;
        .option-field {
          width: 100%;

          &:not(.row) {
            flex-direction: column;
            align-items: flex-start;

            .of {
              &-label, &-val {
                width: 100%;
              }

              &-label {
                margin: 0 0 10px 0;
              }
            }
          }
        }
        .fields-cont {
          padding: 15px;
        }
      }
    }

    select {
      &:focus, &:active {
        outline: none !important;
        box-shadow: none !important;
      }
    }

    // list items --
    #items-container {
      .item-container {
        width: 100%;
        max-width: unset;

        .item-group {
          @include transi();

          .item {
            @include flexable(center, flex-start, row);
            height: 40px;
            position: relative;
            background-color: transparent;
            @include b-radius($b-radius-sm);
            @include transi(border-color);
            border: 1px solid $color-gray-200;
            margin-bottom: 5px;
            overflow: hidden;

            .item-content {
              height: 100%;
              flex: 1;
              padding: 9px 40px 10px 25px;
              font-size: $base-size;
              color: $color-dark-200;
              line-height: 1;
            }

            .sicon {
              &-trash {
                @include flexable();
                width: 25px;
                height: 25px;
                @include b-radius(50%);
                @include transi(background-color);
                padding: 0;
                font-size: $base-size;
                color: $color-white;
                background-color: rgba($color-danger, 0.6);

                &:hover {
                  background-color: rgba($color-danger, 0.8);
                }
              }

              &-keyboard_arrow_left {
                color: $color-gray-200;
              }

              &-keyboard_arrow_down {
                color: $color-primary;
              }
            }

            &:before {
              content: "\f0b2";
              font-family: $font-awesome;
              color: $color-gray-200;
              display: block;
              width: 40px;
              height: 100%;
              position: absolute;
              right: 0;
              top: 0;
              cursor: pointer;
              text-align: center;
              line-height: 38px;
            }

            &.open {
              margin-bottom: 0;
              @include b-radius($b-radius-sm $b-radius-sm 0 0);
              border-color: $color-gray-100;
              border-bottom: none;
              background-color: $color-white;

              .item-content {
                color: $color-primary;
              }

              &:hover {
                background-color: $color-white;
              }
            }

            &:hover {
              border-color: $color-gray-100;
              background-color: rgba($color-white, 0.2);
              cursor: pointer;
            }
          }

          &.sortable-chosen.ghost {
            box-shadow: $box-shadow;
          }

          &.ghost {
            opacity: 0.1;
          }
        }

        &.item-sub {
          margin: 0;
          padding: 0 30px 0 0;
        }
      }
    }
  }
}