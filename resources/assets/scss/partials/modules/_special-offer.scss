.special-offer {
  &__choose-offer,
  &__titles {
    font-size: $base-size;
    margin: 0;
  }

  &__offer-type {
    font-size: $text-small;
    color: $color-gray-400;
    margin-bottom: 3rem
  }

  &__type-list {
    li {
      width: 100%;

      &:not(:last-child) {
        padding-bottom: 12px;
      }
    }
  }

  .selectize-input {
    padding: 0 !important
  }

  &__titles {
    margin: 0 0 20px 0;

    &--small {
      font-size: $base-size;
      font-weight: normal;
      margin: 0 0 18px 0;
    }
  }

  .form-group {

    .bootstrap-select {
      width: 100% !important;
      right: 0 !important;
    }
  }

  #get_product_display {
    li {
      margin-top: 0;
    }
  }

  &__product-list {
    @include transi();
    @include b-radius($b-radius-sm);

    li {
      @include flexable(center, center, row);

      &:not(:last-child) {
        padding-bottom: 10px !important;
      }

      .btn {
        width: 18px;
        height: 18px;
        @include b-radius(50%);
        padding: 0 3px;
        margin-left: 10px;
        position: relative;
        top: 2px;

        &:before {
          content: '\ea47';
          font-family: $font-sallaIcon !important;
          font-size: $text-xxx-small;
          line-height: 1;
          position: absolute;
          top: 50%;
          right: 50%;
          transform: translateY(-50%) translateX(50%);
        }
      }

      .placeholder {
        font-size: $text-x-small;
        font-weight: 600;
        color: $color-dark-100;
      }
    }

    &.product-summary {
      margin: 10px 0;

      li {
        & > span {
          font-size: $text-x-small;
          color: $color-dark-200;
        }
      }
    }

    &.no-p {
      li {
        &:first-child {
          margin-top: 15px;
        }
      }
    }
  }

  .selectize-dropdown {
    top: 0 !important
  }

  &__values {
    hr {
      border-top-width: 1px;
      margin: 24px 0 16px
    }
  }

  .summary-wrapper {
    margin-top: 30px;
    border: 1px solid $color-gray-200;
    @include b-radius($b-radius);
    padding: 30px 0 0;
    position: relative;
  }

  .summary-title {
    width: auto;
    position: absolute;
    top: -20px;
    right: 50%;
    transform: translateX(50%);
    padding: 5px 15px;
    @include b-radius(50px);
    white-space: nowrap;
    margin: 0;
    font-size: $text-small;
    color: $color-primary-l;
    text-align: center;
    background-color: $color-secondary-50;

    &:before {
      content: '\e94d';
      font-family: $font-sallaIcon;
      font-size: $text-xx-medium;
      vertical-align: middle;
      margin-left: 5px;
    }
  }

  &__offer-summary {
    position: relative;
    @include flexable(flex-start, space-between, row);
    padding: 10px 0 10px;
    @include b-radius($b-radius-sm);

    .summary {
      width: 100%;
      position: relative;

      &-inner-wrapper {
        @include flexable();
        width: 100%;
        position: relative;
        @include mediaMaxWidth($screen-tablet-l) {
          flex-direction: column;
        }
      }

      &-placeholder {
        @include flexable();
        width: 100%;
        padding: 15px;

        p {
          font-size: $base-size;
          color: $color-gray-400;
          text-align: center;
          margin: 0;
        }
      }

      &-condition,
      &-result {
        h5 {
          * {
            font-family: $font-main;
          }
          font-size: $text-small;
          margin: 0 0 10px 0;

          b {
            display: inline-block;
            margin: 0 2px;
            color: $color-secondary;
          }
        }
      }

      &-condition {
        padding: 2px 20px 0 50px;
        border-left: 1px solid $color-gray-100;

        &:after {
          content: '\e944';
          font-family: $font-sallaIcon;
          display: block;
          width: 40px;
          height: 40px;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: -20px;
          @include b-radius(50%);
          background-color: $color-white;
          border: 1px solid $color-gray-100;
          text-align: center;
          font-size: $text-medium;
          line-height: 40px;
        }
      }

      &-result {
        padding: 2px 80px 0 0;
      }

      ul {
        padding: 5px 0;
      }
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {
    &__offer-summary {
      flex-direction: column;
      align-items: center;
      position: relative;
      padding: 30px 15px 7px;

      .summary {
        &-title {
          top: -22px;
          padding: 7px 15px 10px;
        }

        &-condition, &-result {
          padding: 30px 0;
        }

        &-result {
          padding-bottom: 15px;
        }

        &-condition {
          padding-top: 15px;
          border-left: none !important;
          border-bottom: 1px solid $color-gray-100;

          &::after {
            content: '\e942';
            top: auto;
            bottom: -20px;
            @include centerX;
          }
        }
      }
    }
  }

  &-listing-entry {
    .offer-title {
      display: table;
      margin: 0;
    }

    .offer-excerpt {
      margin: 0 !important;
      font-size: $text-x-small;
    }

    .offer-duration {
      display: table;
      font-size: $text-xx-small;
      color: $color-gray-400;
      margin: 5px 0 0;

      i {
        @include right-icon-el(5);
        font-size: $text-xx-small;
        position: relative;
      }
    }
  }

  .offer-note {
    display: block;
    margin: 10px 0 0;
    font-size: $text-x-small;
    color: $color-dark-100;

    b {
      color: $color-dark-300;
    }

    @include mediaMaxWidth($screen-phones) {
      padding: 0 15px 10px 0;
    }
  }
}



