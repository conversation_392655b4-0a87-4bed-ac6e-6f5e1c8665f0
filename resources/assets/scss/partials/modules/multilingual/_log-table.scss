.panel-multilang {
  .panel-title {
    font-size: $base-size !important;

    i {
      font-size: $base-size;
      margin-top: -4px;
      display: inline-block;
      font-weight: normal;
    }

    i.sicon-lang {
      font-size: $text-large;
    }
  }
}

.panel-log {
  margin-bottom: 20px;

  .panel-body {
    min-height: 200px;
  }

  .ui.table {
    border-width: 1px 0 0;
    border-color: $color-gray-200;
    text-align: right;

    thead > tr > th {
      text-align: right;
    }

    > thead {
      > tr > th {
        font-size: $text-small;
        padding: 8px 0.78571429em 10px;
        padding-right: 26px;
        position: relative;

        .icon {
          font-size: $text-small;
          color: $color-gray-300;
          margin-right: 5px;
          position: absolute !important;
          @include centerY();
          right: 7px;
          display: inline-block;
        }

        &.sortable {
          color: $color-primary;

          &:hover {
            color: $color-primary-d;
          }
        }
      }
    }
  }

  #log-table {
    tr {
      &:nth-child(even) {
        background-color: $color-gray-50;
      }

      &:hover {
        background-color: $color-gray-50;
      }

      td {
        padding: 10px 15px !important;
      }
    }
  }

  .loader-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    // background-color: rgba(93, 213, 196, 0.1);
    background-color: rgba($color-white, 0.55);
    z-index: 1;
    @include flexable(center, center)
  }
}

.pagination-log-wrap {
  .v-pagination {
    border: 0;
  }

  .rec-pagination {
    padding: 0;
    background-color: $color-white;
    display: inline-flex;
  }

  .pagination-info {
    text-align: left;
  }

  @media (max-width: $screen-laptop-small) {
    text-align: center;

    .pagination-info {
      margin-top: 10px;
      text-align: center;
    }
  }
}