.editor-main-field {
  display: flex;
  margin: 0 -12px;

  label {
    margin-bottom: 10px;
  }

  .editor {
    &-lang {
      flex: 0 0 310px;
      margin: 0 12px;

      label {
        font-size: $text-x-small;
      }
    }

    &-cats {
      flex-grow: 1;
      margin: 0 12px;
    }
  }

  @media (max-width: $screen-laptop) {
    .edtior-cats,
    .editor-lang {
      flex: 1 0 0;
    }
  }

  @media (max-width: $screen-phablet) {
    flex-direction: column;

    .edtior-cats {
      margin-bottom: 15px;
    }
  }
}

.panel-editor {
  @media (max-width: $screen-laptop) {
    margin-bottom: 30px;
  }

  &.translations-section {
    background: transparent;
    border: none;
  }

  .panel-heading {
    @include flexable(center, space-between);
    border: 1px solid $color-gray-200;
    background: $color-white;

    .side-part {
      display: flex;
      align-items: center;
      flex: 1 0 0;
    }

    h6.panel-title {
      font-size: $text-xx-medium !important;
    }

    &__dropdown {
      flex: 0 0 210px;
      margin-right: 10px;
    }

    &__search {
      flex-grow: 1;

      &-field {
        position: relative;
      }

      .icon {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: $text-medium;
        color: $color-dark-100;
      }

      input {
        outline: none;
        line-height: 39px;
        padding-right: 45px;
        font-size: $text-small;
        width: 100%;
        height: 45px;
        border: 2px solid $color-gray-200;
        transition: 0.3s;
        @include b-radius($b-radius-sm);

        &:focus {
          border-color: $color-gray-200;
        }
      }
    }

    &__options {
      padding-right: 20px;
      margin-right: 20px;
      height: 30px;
      display: flex;
      align-items: center;

      @media (min-width: $screen-laptop) {
        &:before {
          height: 100%;
          content: '';
          position: absolute;
          width: 1px;
          background-color: $color-gray-200;
          transform: translateX(20px);
        }
      }

      label {
        font-size: $text-xx-small;
        cursor: pointer;
      }

      .checker span {
        border: 1px solid $color-gray-200;

        &:after {
          right: 1px;
          color: $color-primary;
          top: 1px !important;
        }
      }
    }

    @media (max-width: $screen-laptop) {
      flex-direction: column;
      align-items: flex-start;

      .side-part {
        width: 100%;
      }

      .side-right {
        justify-content: space-between;
      }

      &__options {
        position: absolute;
        left: 35px;
      }
    }

    @media (max-width: $screen-phablet) {
      .side-part {
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
      }

      &__options {
        position: static;
        margin: 10px 0 0;
        padding: 0;
      }

      &__search {
        width: 100%;
      }

      &__dropdown {
        flex: 0 0 0;
        margin: 10px 0 0;
      }
    }
  }
}


.editor-block {
  padding: 20px;
  margin: 20px 0;
  @include b-radius($b-radius-sm);
  border: 1px solid $color-gray-200;
  background: $color-white;

  &.even-has-bg {
    padding: 0;

    .editor-item {
      padding: 20px;

      &:nth-child(n) {
        margin-bottom: 0;
      }
    }
  }

  .odd &, &.even-has-bg .editor-item:nth-child(even) {
    background-color: $color-gray-25;
  }

  input.form-control,
  textarea.form-control {
    border-right: 1px solid $color-gray-200;

    &:focus {
      border-color: $color-gray-400;
    }
  }

  &__crumbes {
    font-size: $text-xxx-small;
    display: flex;
    align-items: center;
    line-height: 24px;
    margin-bottom: 10px;
    color: $color-dark-100;

    .sep {
      color: $color-dark-100;
    }

    span {
      display: inline-block;
      max-width: 150px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  &__product-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;

    .img-wrap {
      width: 40px;
      height: 40px;
      background-color: $color-gray-200;
      margin-left: 15px;
      @include b-radius($b-radius-sm);
      overflow: hidden;
    }

    img {
      display: block;
      width: 100%;
    }

    h5 {
      font-size: $text-small;
      margin: 0;
      line-height: 1;
    }
  }

  .editor-item {
    display: flex;
    width: 100%;

    &:not(:last-child):not(:empty) {
      margin-bottom: 25px;
    }

    &__field {
      flex: 1 0 0;

      .form-control {
        padding: 10px;
      }
    }

    &__status {
      flex: 0 0 60px;
      @include flexable(center, center);
      color: $color-gray-400;
      transition: 0.3s 0.1s;
      // padding-top: 25px;

      .icon:before {
        font-size: $text-xx-large;
      }

      &.complete {
        color: $color-primary;

        .icon:before {
          content: '\ea9b';
          font-size: $text-x-medium;
        }
      }
    }


    &__first {
      display: flex;
      flex-direction: column;
      max-width: 50%;

      .editor {
        width: 100%;
        flex-grow: 1;

        .editor__content {
          height: 100%;
        }
      }

      img {
        max-width: 100%;
      }
    }


    &.has-label {
      .editor-item__status {
        padding-top: 25px;
      }
    }

    &.dir-ltr {
      .editor-item__second {
        direction: ltr;
      }

      .ql-direction-rtl {
        direction: ltr;
      }
    }

    .ql-container {
      font-size: $base-size;
    }

    .ql-editor {
      text-align: inherit !important;
      direction: inherit !important;
      min-height: 250px;
      height: 250px;
      padding: 10px 15px !important;

      ol,
      ul {
        padding: 0;
      }

      li.ql-indent-1:not(.ql-direction-rtl) {
        padding-left: 3em;
      }

      li.ql-indent-2:not(.ql-direction-rtl) {
        padding-left: 4em;
      }

      li.ql-indent-3:not(.ql-direction-rtl) {
        padding-left: 5em;
      }

      li.ql-indent-4:not(.ql-direction-rtl) {
        padding-left: 6em;
      }
    }

    textarea.form-control {
      height: 100%;
    }

    .editor-preview {
      height: 314px;
      overflow: hidden;
      overflow-y: auto;
    }


    @media (max-width: $screen-tablet-l) {
      flex-direction: column;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        width: 1px;
        height: 10px;
        background: $color-gray-200;
        @include centerY();
        right: 18px;
      }

      &__first {
        flex-direction: column;
        margin-bottom: 5px;
        max-width: none;
      }

      &__second .form-control {
        padding-left: 40px;
      }

      &__status {
        position: absolute;
        left: 10px;
        bottom: 9px;
        z-index: 1;

        &.empty {
          display: none;
        }
      }

      &.dir-ltr {
        .editor-item__status {
          right: 10px;
          left: auto;
        }

        .editor-item__second .form-control {
          padding-left: 10px;
          padding-right: 40px;
        }
      }
    }
  }

  &.theme-block {
    h5 {
      margin-bottom: 20px;
      font-weight: 300;
      font-size: $text-medium;
      font-family: $font-main;
      color: $color-dark-200;
    }
  }

  &__child {
    margin-top: 25px;

    &-title {
      font-family: $font-main;
      font-weight: bold;
      margin-bottom: -10px;
    }

    &-item {
      margin-top: 20px;
      padding: 10px 15px 15px;
      border: 1px solid $color-gray-200;
      background-color: $color-white;
      @include b-radius($b-radius-sm);

      .grand-child-item {
        margin-top: 15px;

        label {
          font-size: $text-small;
        }
      }
    }
  }
}

.theme-block-main-title {
  margin-top: 30px;
}


.editor-placeholder {
  @include flexable(center, center, column);
  margin-top: 100px;

  .icon {
    font-size: $text-xxxx-giant;
    color: $color-gray-200;
    display: block;
  }

  h3 {
    margin: 0 0 15px;
    line-height: 1;
  }

  p {
    color: $color-gray-400;
    line-height: 1;
  }
}

.theme-editor-placeholder {
  margin: 70px auto;

  .icon {
    font-size: $text-x-giant;
    margin-bottom: 15px;
  }

  &__desc {
    margin-bottom: 20px;
  }

  .panel-heading__dropdown {
    flex: auto;
  }
}


.fade-enter-active,
.fade-leave-active {
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.55, 0, 0.1, 1);
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
  transform: translateY(20px);
}