
.general-report-panel-body {
  @include mediaMaxWidth($screen-phones) {
    padding: 20px 15px;
  }
}

.general-report {
  padding: 8px 10px 8px 0;

  > div {
    @include flexable(center, null, row);
    flex: auto;
    width: 100%;

    i {
      display: block;
      width: 65px;
      height: 60px;
      border: 1px solid;
      text-align: center;
      line-height: 60px;
      font-size: $text-xx-larger;
      @include b-radius($b-radius-sm);
    }

    > div {
      margin-right: 20px;

      h6 {
        margin: 0;
        font-size: $text-xxxx-larger;
        line-height: 1.1;
        font-weight: bold;
      }

      span {
        font-size: $base-size;
      }
    }
  }

  .info-report {
    i {
      border-color: rgba(77, 171, 247, .2);
      background-color: rgba(77, 171, 247, .05);
      color: $color-secondary;
    }

    h6 {
      color: #4DABF7;
    }
  }

  .success-report {
    i {
      border-color: rgba(93, 213, 196, .2);
      background-color: rgba(93, 213, 196, .05);
      color: $color-primary
    }

    h6 {
      color: $color-primary;
    }
  }

  .purple-report {
    i {
      border-color: rgba(158, 133, 255, .2);
      background-color: rgba(158, 133, 255, .05);
      color: #9E85FF //colorAvilabity
    }

    h6 {
      color: #9E85FF;
    }
  }

  @include mediaMaxWidth($screen-tablet-l) {
    padding: 0;

    > div {
      flex-direction: column;

      i {
        width: 41px;
        height: 38px;
        line-height: 38px;
        font-size: $text-large;
      }

      > div {
        margin: 12px 0 0;
        text-align: center;

        h6 {
          font-size: $text-large;
          line-height: .8;
        }

        span {
          font-size: $text-xx-small;
        }
      }
    }
  }
}


.campaign-statistics {
  display: grid;
  grid-template-columns: 33% 65%;
  grid-gap: 2%;

  > div {
    b {
      color: $color-dark-300;
      font-size: $text-medium;
      font-weight: bold;
      border-bottom: 1px solid $color-gray-200;
      display: block;
      padding-bottom: 15px;
    }

    .campaign-content {
      padding-top: 14px;

      span {
        font-size: $text-small;
        display: block;
      }

      small {
        font-size: $text-small;
        color: $color-gray-400;
      }

      > div {
        @include flexable(flex-start, space-between, row);
        margin-top: 10px;

        p {
          margin: 0;
          font-size: $text-small;
          flex: 0 0 80%;
          text-align: right;
        }

        img {
          object-fit: cover;
          max-width: 100%;
        }
      }
    }

    .purchase-stats {
      li {
        @include flexable(center, space-between, row);
        padding: 14px 0;
        width: 100%;

        &:not(:last-of-type) {
          border-bottom: 1px solid $color-gray-200;
        }

        span {
          font-size: $base-size;

          &:first-of-type {
            &:before {
              content: '';
              width: 18px;
              height: 18px;
              @include b-radius($b-radius-sm);
              margin-left: 10px;
              display: inline-block;
              position: relative;
              top: 5px;
            }
          }
        }

        &.views {
          span {
            &:before {
              background-color: $color-primary;
            }
          }
        }

        &.orders-amount {
          span {
            &:before {
              background-color: $color-info;
            }
          }
        }

        &.total-sales {
          span {
            &:before {
              background-color: #9E85FF; // colorAvilabilty
            }
          }
        }
      }
    }
  }

  @include mediaMaxWidth($screen-laptop) {
    grid-template-columns: 49% 49%;
  }

  @include mediaMaxWidth($screen-laptop-small) {
    grid-template-columns: 100%;
    grid-gap: 0;

    > div {
      b {
        border: none;
        padding-bottom: 0;
      }

      .campaign-content, .purchase-stats {
        padding-top: 7px;
      }

      .campaign-content {
        > div {
          margin-top: 5px;
        }
      }

      .purchase-stats {
        span {
          &:last-of-type {
            font-size: $text-medium !important;
          }
        }
      }
    }
  }
}