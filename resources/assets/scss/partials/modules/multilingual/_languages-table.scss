.default-languages {
  display: flex;
  justify-content: space-between;
  padding: 25px 22px;

  &_title {
    // flex: 0 0 450px;
    padding-left: 20px;
    flex-shrink: 0;
    max-width: 50%;

    h3 {
      font-size: $base-size;
      line-height: 1;
      margin: 0 0 5px;
    }

    p {
      font-size: $text-xx-small;
      color: $color-dark-100;
      margin: 0;
    }
  }

  &_dropdown {
    flex-grow: 1;
    flex-shrink: 0;
    max-width: 50%;
  }

  @media (max-width: $screen-phablet) {
    flex-direction: column;

    > div {
      max-width: 100%;
    }

    &_title {
      margin-bottom: 10px;
    }
  }
}

.languages-table-wrap {
  position: relative;

  @media (max-width: $screen-phablet) {
    .languages-table {
      overflow-x: auto;
    }

    .three-dots-dropdown {
      position: static;

      .dropdown-menu {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .dropdown-backdrop {
      background: rgba($color-white, 0.6);
    }
  }
}

.languages-table {
  font-size: $base-size;
  min-height: 130px;

  .sortable-ghost {
    background-color: rgba(255, 255, 0, 0.05) !important;
  }

  thead {
    white-space: nowrap;
  }

  tr {
    span {
      transition: 0.3s;
    }

    &:nth-child(even) {
      background-color: $color-gray-50;
    }

    &.is-disabled {
      .status-item {
        opacity: 0.3;
      }
    }

    .badge {
      margin-right: 10px;
      background-color: $color-gray-300;
      padding: 0 10px 3px;
    }
  }

  .lang-wrapper {
    display: flex;
    align-items: center;

    .handle {
      padding: 0 0 0 15px;
      cursor: move;
    }
  }

  .table-row__lang {
    display: flex;
    align-items: center;
  }

  @media (min-width: $screen-tablet-p) {
    thead th:first-child {
      width: 450px;
    }
  }

  .flag-img {
    margin-left: 10px;
  }

  .lang-actions {
    text-align: left;
  }

  .language-name__default {
    color: $color-dark-100;
    font-size: 90%;
    white-space: nowrap;
  }
}

.opacity-0 {
  opacity: 0;
}

.panel-languages__content {
  transition: 0.3s;
}

.add-new-lang {
  font-size: 14px;
  padding: 22px 21px 26px;
  background-color: $color-gray-50;
  border-top: 1px solid $color-gray-100;

  .loader {
    width: 12px !important;
    height: 12px !important;
    margin-left: 8px;
  }

  .add-new-lang__field {
    display: flex;

    @media (max-width: $screen-phablet) {
      flex-direction: column;

      .languages-list-wrap {
        margin: 0 0 10px;
      }

      .btn {
        flex-basis: auto;
      }
    }
  }

  .languages-list-wrap {
    flex-grow: 1;
    margin-left: 20px;
  }

  .btn {
    flex: 0 0 85px;
    // font-weight: bold;
    // font-size: 14px;

    .icon {
      margin-left: 5px;
      transform: translateY(2px);
    }
  }
}