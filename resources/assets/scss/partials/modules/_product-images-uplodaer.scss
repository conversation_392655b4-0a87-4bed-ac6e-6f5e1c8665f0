// blog image  upload
.rec-uploaded-images { // @osama - needs revisit ---
  ._slim {
    width: 49%;
    margin: 0px 0 16px 2%;
    border: 1px solid $color-gray-200;
    @include b-radius($b-radius-sm);

    .slim-area {
      //max-height: 180px;
      //overflow: hidden;
    }

    &:nth-child(even) {
      margin-left: 0 !important;
    }

    &.sortable-chosen {
      box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.3);
      overflow: hidden;

      &.sortable-ghost {
        transform: scale(1);
        opacity: 0.95;
        filter: blur(1px);
        -webkit-filter: blur(1px);
      }
    }
  }

  .product-photo-meta {
    padding: 10px 11px 13px 10px;
    background-color: $color-gray-100 !important;

    .radio-inline {
      padding-right: 28px;
      font-size: $text-xx-small;

      .choice {
        top: -2px;

        span {
          border-color: $color-gray-300 !important;
          border-width: 1px;
          width: 20px;
          height: 20px;
          background-color: $color-white;

          &.checked {
            background-color: $color-primary;
            border-color: $color-primary !important;

            &:after {
              content: "\ea9d";
              font-family: $font-sallaIcon;
              font-size: $text-x-small;
              color: $color-white;
              top: -4px;
              right: -1.6px;
              border-color: transparent;
            }
          }
        }
      }

      @include mediaMaxWidth($screen-phones) {
        font-size: $text-xx-small;
        padding-top: 2px;
      }
    }

    i {
      background-color: $color-white;
      @include b-radius($b-radius-sm);
      width: 24px;
      height: 24px;
      top: 12px;
      border: 1px solid $color-gray-300;
      line-height: 23px;
      text-align: center;
      font-size: $text-small;

      &:before {
        position: initial;
      }

      &.remove-product-photo {
        border-color: rgba(245, 81, 87, 0.2);
        color: $color-danger;
        left: 10px;
      }

      &.crop-product-photo {
        left: 42px;
      }
    }
  }
  // light version ---
  &.light {

    .slim-result {
      @include b-radius($b-radius-sm $b-radius-sm 0 0);
      border: 1px solid $color-gray-200;
      border-bottom: none;
      overflow: hidden;
    }

    .product-photo-meta {
      min-height: 46px;
      border: 1px solid $color-gray-200;
      @include b-radius(0 0 $b-radius-sm);
      overflow: hidden;
      background: $color-white !important;
    }

    input[type="file"] {
      opacity: 0;
      display: none;
      visibility: hidden;
    }
  }

  @include mediaMaxWidth(420px) {
    .product-photo-meta {
      @include flexable(flex-start, flex-start, column);

      .radio-inline {
        margin: 0 0 10px 0;
      }

      .action-icon {
        position: initial;
        display: inline-block;
      }
    }
  }

}

.add-product-link {
  margin-top: 20px;

  > div.rec-list {
    div {
      flex: auto;

      .form-control {
        border-color: $color-gray-300;
        border-right: none;
        height: 40px;
        border-left: none;
        @include b-radius(0);
      }

      .input-group-addon {
        border-color: $color-gray-300;
        width: 36px;
        height: 40px;
        color: $color-dark-300;

        i {
          position: relative;
          top: 5px;
          color: $color-gray-300;
        }
      }
    }

    .btn {
      height: 40px;
      @include b-radius(2px 0 0 2px);
      font-size: $base-size;

      &:before {
        content: "\e909";
        font-family: $font-sallaIcon;
        position: relative;
        top: 1px;
        margin-left: 5px;
      }
    }
  }
}




