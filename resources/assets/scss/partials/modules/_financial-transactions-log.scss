.financial-transactions-log {
  .panel-heading-wrapper {
    background: $color-gray-200;
  }
  .status-cell div:first-of-type {
    color: $color-primary-d
  }

  .status-cell div {
    white-space: nowrap;

  }

  .status-cell div:last-of-type {
    display: flex;
    align-items: center;
    gap: 4px;

    & .status span:last-of-type {
      white-space: nowrap;

    }
  }

  .status-indicator {
    $indicator-color: transparent;
    display: inline-block;
    width: 10px;
    aspect-ratio: 1;
    flex-shrink: 0;
    flex-basis: 10px;
    border-radius: 100vw;
    background-color: $indicator-color
  }

  .search-placeholder {
    @include flexable(center, center, column);
    width: 100%;
    padding: 20px;

    > span {
      @include flexable();
      width: 90px;
      height: 90px;
      @include b-radius(50%);
      background-color: $color-gray-100;
      margin: 0 0 10px 0;

      i {
        font-size: $text-large;
        color: $color-dark-100;
      }
    }

    > p {
      font-size: $text-small;
      color: $color-dark-100;
      margin: 0;
    }
  }

  .rec-list {
    .btn {
      min-width: 80px;

      i {
        font-size: $base-size;
        display: inline-block;
        margin-left: 5px;
      }
    }
  }

  #search {
    width: 100%;

    input {
      position: absolute !important;
      top: -2px;
      background-color: transparent;
      right: 15px;
      margin: 0;
      padding: 0;
    }

    .selectize-control {
      .item {
        border: none;
        padding: 6px 9px;
      }

      .selectize-dropdown.form-control {
        width: 100% !important;
        height: auto;
        position: absolute;
        top: 38px !important;
        left: auto !important;
        right: 0;
        z-index: 999;
        max-height: 160px;
        overflow: auto;
        overflow-x: hidden;

        .media {
          cursor: pointer;
          padding: 0 10px;

          &:hover {
            background-color: $color-gray-100;
          }
        }
      }

      &.hide-selected {
        .item {
          display: none;
        }
      }
    }

    .plugin-no_results {
      padding: 0
    }
  }

  &__results {
    .empty {
      font-size: $text-xx-medium;
      color: $color-gray-400;
      display: block;
    }

    .user {
      @include b-radius(2px);
      @include flexable(center, flex-start, row);
      padding: 12px 15px;
      background: $color-gray-100;

      .icon {
        margin-left: 15px;
        display: inline-block;
        width: 38px;
        height: 38px;
        background-color: $color-gray-100;
        color: $color-white;
        font-size: $text-medium;
        text-align: center;
        line-height: 38px;
        @include b-radius(50%)
      }

      img {
        width: 45px;
        height: 45px;
        @include b-radius(50%);
        margin-left: 15px;
        border: 1px solid $color-gray-200;
      }

      div {
        flex: auto;

        h6 {
          font-size: $base-size;
          line-height: 1;
          margin: 0;
        }

        span {
          font-size: $text-x-small;
        }
      }
    }

    .short-note {
      display: inline-block;

      p {
        font-size: $text-xx-small;
        text-align: right;
        white-space: normal;
        margin: 0;

        a {
          vertical-align: baseline;
        }
      }

      .tooltip-content {
        width: 100%;
        box-shadow: 0 5px 10px 0 rgba($color-black, 0.1);
        transition-delay: 0.20s;
        padding-top: 15px;

        p {
          font-size: $text-xx-small;
          text-align: right;
        }

        hr {
          margin: 8px 0;
        }

        .btn {
          display: inline-block;
          margin: 0;
          padding: 5px 10px;
          float: right;

          i {
            display: inline-block;
            margin-left: 5px;
            font-size: $text-x-small;
          }
        }
      }
    }

    .table-financial-log {
      tbody {
        td {
          img {
            max-height: 10px;
          }

          @include mediaMaxWidth($screen-tablet-p) {
            padding-right: 0 !important;
            padding-left: 0 !important;
            > * {
              max-width: 50%;
              float: left;
            }
          }

          &:last-of-type {
            position: relative;

            .short-note {
              position: initial;

              .tooltip-content {
                bottom: 80%;
              }
            }
          }
        }
      }
    }
  }
}
