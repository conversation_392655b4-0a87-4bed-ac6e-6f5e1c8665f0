.marketing-abandoned-carts {
  .label {
    text-transform: inherit;
  }

  .create-cart-rule-hr {
    margin: 7px 0;
    border: 0;
  }

  p.text-muted {
    font-size: $text-x-small;
    margin-bottom: 15px;
  }

  h5 {
    margin: 0;
  }

  .modal .form-group label {
    padding-top: 0;
  }

  .helper-message-perview {
    display: block;
    margin-top: 5px;
  }

  .content-divider > span {
    background-color: $color-white;
  }

  .span_discount_type {
    border: 1px solid $color-gray-200;
    border-right: 0
  }
  .table-row{
  .cart-status {
    &-completed {
      color: #58c9b9;
    }

    &-reminder_sent {
      color: #4D74E4;
    }

    &-moved_to_store {
      color: #f3b051;
    }

    &-sending_error {
      color: #CF444D ;
    }

    &-reminder_scheduled {
    color:#5196f3;
    }

    &-not_updated {
      color: #666666;
    }

    &-new {
  color:#ffb150;
    }
  }
  }

}
.carts-reminder{
  .reminder{
    &-item{
      border:$color-gray-200 solid 1px;
      min-width: 250px;
    }
  }
  @media only screen and (max-width:1270px) {
    .grid-block--col-3{
      grid-template-columns: repeat(2,1fr);
    }
  }
  @media only screen and (max-width:950px) {
    .grid-block--col-3{
      grid-template-columns: repeat(1,1fr);
    }
  }
  @media only screen and (max-width:768px) {
    .grid-block--col-3{
      grid-template-columns: repeat(2,1fr);
    }
  }
  @media only screen and (max-width:672px) {
    .grid-block--col-3{
      grid-template-columns: repeat(1,1fr);
    }
  }
}