body {
  .mobile-webview-show {
    display: none;
  }

  &.mobile-webview {
    margin-top: 5px !important;
    #page_header_box {
      margin-bottom: 0;
      min-height: unset;

      .page-header-content {
        padding: 0;
      }
    }

    #shipping-companies {
      .store-setup-row {
        grid-template-columns: repeat(1, 1fr);
        grid-gap: 15px;

        .store-setup-item {
          padding: 3rem 1rem 1rem;
        }
      }
    }

    .section-header {
      font-size: $text-small;
      padding: 0 0 10px 0;
    }

    .section-container {
      display: flex;
      justify-content: space-between;

      .setting-box {
        @include flexable(center, flex-start, column);
        width: calc(33.33% - 8px);
        height: auto;
        margin: 0 0 12px !important;
        padding: 15px 10px;
        flex-direction: column;

        p {
          margin: 5px 0 0;
        }

        &.mid {
          width: calc(50% - 5px);
        }

        &.wide {
          width: 100%;
        }
      }
    }

    .store-setup-row {
      display: grid;
      grid-gap: 16px;
      grid-template-columns: repeat(3, 1fr);
      padding-right: 8px;
      padding-left: 8px;

      .store-setup-item {
        padding: 10px;
        justify-content: flex-start;

        .store-setup-title {
          font-size: $text-x-small;
        }
      }
    }

    .mobile-webview {
      &-hide {
        display: none !important;
      }

      &-show {
        display: block !important;
      }

    }

    .report_filters_year {
      .bootstrap-select {
        overflow: hidden;
      }

      &.force-visible {
        .bootstrap-select {
          overflow: visible;
        }
      }
    }

    // external-services ---
    &.services-integration {
      section {
        .store-setup-row {
          grid-template-columns: repeat(2, 1fr);

          .store-setup-item {
            align-items: center;
            justify-content: center;
            min-height: 100px;
          }
        }
      }
    }

    &[data-mobile-agent-version^="3.1"] {
      .apple-pay-exists {
        display: none !important;
      }
    }

    // remove load more button in notifications stream ---
    .media-list {
      &.feed-list {
        .activities-load-more {
          display: none;
        }
      }
    }

    // hide return button in help center ---
    &.help-center- {
      .help {
        button {
          &.btn {
            &.bg-slate-400 {
              display: none;
            }

            &.btn-info {
              width: 100% !important;
            }
          }
        }
      }

      .panel-footer {
        &.help {
          .prev {
            display: none;
          }
        }
      }
    }

    // fix payment-policy-button position ---
    &.payment-list-epayments {
      .panel-heading {
        &.has-abs-button {
          .panel-title {
            padding-left: 90px;
          }

          .heading-elements {
            .payment-policy-btn {
              margin-top: 12px;
            }
          }
        }
      }
    }

    #res_sf {
      display: none;
    }

    // store-lock ---
    .store-locked-placeholder {
      display: flex;
    }

    // Reports Page Data range Picker
    .daterangepicker {
      .rec {
        &-daterangepicker {
          .ranges {
            ul li {
              font-family: $font-main;
            }
          }
        }
      }
    }

    // hide for mobile --
    .alert-box--sticky {
      display: none;
    }

    // Hide return button in edit order
    #edit_return_wrapper {
      display: none;
    }

    #remove_order_wrapper {
      width: 100% !important;
    }
  }
}
