.text-primary {
  color: $color-primary !important;

  &:hover, &:focus, &:active {
    color: $color-primary !important;
  }
}

.text-muted-small {
  font-size: $text-xx-small;
}

.text-muted-smaller {
  font-size: $text-xxx-small;
}

@each $align-text in 'left', 'right' {
  .text-#{$align-text}-align {
    text-align: #{$align-text};
  }
}

.text-size-medium {
  font-size: $text-small;
}

.font-10 {
  font-size: $text-xxxx-small;
}

.text-md-right {
  @include mediaMinWidth($screen-laptop-small) {
    text-align: left;
  }
}

.text {
  &-underline {
    text-decoration: underline;
  }


  &-center {
    text-align: center !important;
  }

  &-wrap {
    white-space: normal;
  }

  &-break {
    word-break: break-all;
  }

  &-sm-wrap {
    @include mediaMaxWidth($screen-phones) {
      white-space: normal;
    }
  }

  &-style-none {
    text-decoration: none !important;
  }

  &-plain {
    unicode-bidi: plaintext;
    text-align: right;
    direction: rtl;
  }

  &-gray-100 {
    color: $color-gray-100 !important;
  }

  &-gray-200 {
    color: $color-gray-200 !important;
  }

  &-gray-300 {
    color: $color-gray-300 !important;
  }

  &-dark-100 {
    color: $color-dark-100 !important;
  }

  &-dark-200 {
    color: $color-dark-200 !important;
  }

  &-dark-300 {
    color: $color-dark-300 !important;
  }

  &-dark-400 {
    color: $color-dark-400 !important;
  }

  &-success {
    color: $color-success;
  }

  &-danger {
    color: $color-danger;
  }

  &-gold {
    color: $color-gold;
  }

}

.font {
  &-regular {
    font-family: var(--font-regular), sans-serif;
  }

  &-medium {
    font-family: var(--font-medium), sans-serif;
  }
  &-normal {
    font-weight: normal !important;
  }
}

.font-weight-700 {
  font-weight: 700 !important;
}

.lh {
  &-1 {
    line-height: 1;
  }

  &-1_2 {
    line-height: 1.2;
  }
  &-1_5 {
    line-height: 1.5;
  }
}

@each $num in 10, 20, 25, 30, 35, 40, 50, 60, 70 {
  .font-#{$num} {
    font-size: #{$num}px !important;
  }
}

.text-decoration-none {
  text-decoration: none !important;
}

.theme-elm-sub-label {
  display: inline-block;
  max-width: 200px;
  height: 17px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transform: translateY(1px);
  @include mediaMaxWidth($screen-phones) {
    display: none;
  }
}

