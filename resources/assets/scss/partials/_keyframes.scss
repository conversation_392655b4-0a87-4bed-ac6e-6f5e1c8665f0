@keyframes delete {
  to {
    width: 0;
    opacity: 0;
    padding-right: 0;
    padding-left: 0;
    margin-left: 0;
    margin-right: 0;
  }
}

@keyframes loop {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }

  75% {
    transform: rotate(270deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes conceal {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0;
    transform: translateY(20px);
  }

}

@keyframes reveal {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@-webkit-keyframes loader {
  100% {
    transform: rotate(-360deg);
  }
}

@keyframes loader {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.sicon-spin {
  animation: loader 2s infinite;
  display: inline-block;
  -webkit-animation: loader 2s infinite;
}

@keyframes circleLoop {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0);
  }
}

@keyframes fadeScale {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(3);
  }
}

  
