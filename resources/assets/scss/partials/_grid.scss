// grid block ---
.grid-block {
  display: grid;

  @each $colSize in 2, 3, 4 {
    &--col-#{$colSize} {
      grid-template-columns: repeat(#{$colSize}, 1fr);
      @if (#{$colSize}==4) {
        @include mediaMaxWidth($screen-desktop-large) {
          grid-template-columns: repeat(3, 1fr);
        }
        @include mediaMaxWidth($screen-laptop-small) {
          grid-template-columns: repeat(2, 1fr);
        }
        @include mediaMaxWidth($screen-phones) {
          grid-template-columns: repeat(1, 1fr);
        }
      } @else if (#{$colSize}==3) {
        @include mediaMaxWidth($screen-laptop-small) {
          grid-template-columns: repeat(2, 1fr);
        }
        @include mediaMaxWidth($screen-phones) {
          grid-template-columns: repeat(1, 1fr);
        }
      } @else if (#{$colSize}==2) {
        @include mediaMaxWidth($screen-phones) {
          grid-template-columns: repeat(1, 1fr);
        }
      }
    }
  }

  &--plans{
    max-width: 1000px;
    margin: 0 auto;
    @include mediaMaxWidth($screen-phones) {
      display: flex;
      flex-direction: column-reverse;
      &.reverse {
        flex-direction: column-reverse;
      }
    }
  }

  &--col-2-lg {
    grid-template-columns: repeat(2, 1fr);
    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &--col-sm-3 {
    @include mediaMaxWidth($screen-laptop-small) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @each $gap in 0, 5, 10, 15, 20, 25, 30, 35, 40 {
    &--gap-#{$gap} {
      grid-gap: #{$gap}px;
      @include mediaMaxWidth($screen-desktop-small) {
        @if ($gap>= 30) {
          grid-gap: 15px;
        } @else {
          grid-gap: 10px
        }
      }
    }
  }

  @each $fitOrFill in "fit", "fill" {
    &--auto-#{$fitOrFill} {
      grid-template-columns: repeat(#{$fitOrFill}, minmax(250px, 4fr));
      @include mediaMaxWidth($screen-phones) {
        grid-template-columns: repeat(#{$fitOrFill}, minmax(200px, 4fr));
      }
    }
  }


  &.addons-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 4fr));

    &--wide {
      grid-template-columns: repeat(auto-fill, minmax(500px, 4fr));

      @include mediaMaxWidth($screen-phones) {
        grid-template-columns: 1fr;
      }
    }

    &--upgrade {
      max-width: unset;
      grid-template-columns: 1fr 3fr;
      grid-gap: 30px;

      @include mediaMaxWidth($screen-laptop) {
        grid-template-columns: 1fr;
      }
    }
  }

  &.plan-meta {
    grid-template-columns: 3fr 1fr;
    grid-gap: 20px;

    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: 1fr;
      grid-gap: 0;
    }
  }

  &.store-themes-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));

    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }

    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include mediaMaxWidth($screen-phablet) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &.archive-themes-list {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));

    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include mediaMaxWidth($screen-phablet) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &.merchant-addons-list {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));

    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &.salla-policies-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));

    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &.empty {
    grid-template-columns: 1fr !important;
  }

  &.auto-flow {
    grid-auto-flow: column;
  }

  @each $span in 2, 3, 4, 5 {
    .grid-block-col-span#{$span}{
        grid-column: span #{$span};
    }
    .grid-block-row-span-#{$span}{
        grid-row: span #{$span};
    }
  }
}
