.rec {
  &-flex {
    display: flex !important;

    &-row {
      flex-direction: row;
    }

    &-col {
      flex-direction: column;
    }

    &.align-items-center {
      align-items: center;
    }
    &.align-items-start {
      align-items: flex-start;
    }
    &.flex-wrap {
      flex-wrap: wrap;
    }

    &.center {
      align-items: center;
      justify-content: center;
    }

    &.start {
      align-items: flex-start;
      justify-content: flex-start;
    }

    &.end {
      align-items: flex-end;
      justify-content: flex-end;
    }

    .shrink-0 {
      flex-shrink: 0;
    }

    .flex-auto {
      flex: auto;
    }

    &.sp {
      justify-content: space-between;
    }
  }

  &-flex-col {
    display: flex;
    flex-direction: column;
  }

  &-position-initial {
    position: initial !important;
  }

  &-position-relative {
    position: relative !important;
  }

  &-btns-row {
    li {
      a {
        &:hover {
          background: transparent !important;
        }
      }
    }

    @include mediaMaxWidth($screen-phablet) {
      ul {
        &.nav.nav-lg {
          > li {
            > a {
              // padding: 0 10px 0; -- disable for later review
            }
          }
        }
      }
    }
  }

  &-mt-sm-0 {
    @include mediaMaxWidth(768px) {
      margin-top: 0 !important;
    }
  }

  &-mr-sm-auto {
    @include mediaMaxWidth(768px) {
      margin-right: auto !important;
    }
  }

  &-modal-sm {
    @include mediaMinWidth(576px) {
      max-width: 500px;
    }
  }

  &-price-wrapper {
    @include flexable();

    * {
      display: inline-block;
      margin: 0 2px;
      white-space: nowrap;
      line-height: 1;

      &:first-child {
        margin-right: 0;
      }

      &:last-child {
        margin-left: 0;
      }
    }

    .product-price {
      color: $color-primary;
      font-size: $text-xxx-medium;
    }

    b {
      font-size: $base-size;
      unicode-bidi: plaintext;
      color: $color-dark-300;
    }

    small {
      font-size: $text-small;
      color: $color-dark-100;
    }

    @include mediaMaxWidth($screen-desktop-small) {
      b {
        font-size: $base-size;
      }

      small {
        font-size: $text-small;
      }
    }

    // debit negative number
    &.debit {
      b {
        color: $color-danger !important;
      }
    }

    &.light {
      b {
        font-weight: normal;
      }
    }

    &.end {
      justify-content: flex-end;
    }

    &.start {
      justify-content: flex-start;
    }

    &--large {

      b,
      small {
        font-size: $text-large;
      }
    }

    &--giant {
      align-items: flex-end;

      b {
        font-family: $font-main !important;
        font-weight: 500;
        font-size: $text-giant;
        color: $color-black;
      }

      small {
        font-size: $text-larger;
      }

      @include mediaMaxWidth($screen-phones) {
        b {
          font-size: $text-xxx-larger;
        }
      }
    }
  }

  &-note {
    margin: 0 0 5px 0;

    > span {
      display: inline-block;
      margin-left: 5px;
      color: $color-danger;

      &:before {
        content: '\ecdf';
        font-family: $font-sallaIcon;
        position: relative;
        top: 3px;
        margin-left: 6px;
      }
    }

    &--grey {
      color: $color-dark-100;
    }

    &--small {
      font-size: $text-small;
    }

    &--smaller {
      font-size: $text-xx-small;
    }
  }
}

.pcr-app {
  z-index: 10000000000 !important;
}

.btns-row {
  .nav.nav-lg {
    > li {
      a:not(.btn-filter-toggle) {
        &:hover {
          background: transparent !important;
        }
      }
    }
  }

  &--sm {
    @include mediaMaxWidth(513px) {
      .sub-btns {
        float: none;
        margin-bottom: 15px;

        .nav.nav-lg {
          float: none;

          li {
            a {
              font-size: $text-x-small;
            }
          }
        }
      }
    }
  }
}

.body-waves-bg {
  img {
    position: absolute;
    z-index: -1;
    width: 100%;
    top: 0;
    right: 0;
  }
}

.right-back-arrow {
  display: inline-block;
  vertical-align: middle;
}

.inline-icon {
  display: inline-block;
  vertical-align: middle;

  &.start {
    margin-left: 10px;
  }

  &.center {
    transform: translateY(-1px);
  }

  &.end {
    margin-right: 10px;
  }
}

.text-size-medium {
  font-size: $text-small;
}

.order-panels {
  &.rec-responsive {
    @include mediaMaxWidth(1250px) {
      flex-direction: column;
      margin: 0;

      .order-panel {
        width: 100%;
        margin: 0 0 40px;
      }
    }
  }
}

.notification {
  &:before {
    content: "";
    width: 8px;
    height: 8px;
    @include b-radius(50%);
    background: $color-danger;
    position: absolute;
    top: 4px;
    right: -5px;
    border: 1px solid darken($color-danger, 10%);
  }
}

.mb {
  &-0 {
    margin-bottom: 0 !important;
  }

  &-md-20 {
    @include mediaMaxWidth($screen-laptop) {
      margin-bottom: 20px !important;
    }
  }

  &-sm-10 {
    @include mediaMaxWidth($screen-phones) {
      margin-bottom: 10px !important;
    }
  }

  &-sm-20 {
    @include mediaMaxWidth($screen-phones) {
      margin-bottom: 20px !important;
    }
  }
}

.mx {
  &-10 {
    margin-inline: 10px;
  }

  &-20 {
    margin-inline: 20px;
  }
}

.seperate-line {
  width: 100%;
  height: 1px;
  margin: 20px 0px;
  background-color: $color-gray-200;
}

.total-points {
  i.sicon-star2 {
    font-size: $text-xxx-larger;
  }

  h4 {
    font-size: $text-xxx-larger;
  }
}

.opacity-0 {
  opacity: 0;
  pointer-events: none;
  position: absolute;
}

.h-auto {
  height: auto !important;
}

.v-scroll {
  max-height: 380px;
  overflow-y: auto;
  @include scrollBar(2px, $color-primary, $color-gray-200);
}

.v-hidden {
  overflow-y: hidden;
}

.h-scroll {
  overflow-x: auto;
}

.h-hidden {
  overflow-x: hidden;
}

.mt-0 {
  margin-top: 0 !important;
}

.mh-auto {
  margin-right: auto;
  margin-left: auto;
}

.mh-5 {
  margin-inline: 5px;
}

.mh-10 {
  margin-inline: 10px;
}

.no-p {
  padding: 0 !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.no-action {
  pointer-events: none;
}

.w-auto {
  width: auto !important;
}

.float-none {
  float: none !important;
}

.wide-row {
  > td {
    width: 100%;
    padding-right: 0 !important;
    padding-left: 40px !important;
  }
}

.inset-0 {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

#currency_placeholder {
  td {
    @include mediaMaxWidth(424px) {
      > * {
        width: auto !important;
      }
    }
  }
}

.cubic {
  width: 10px;
  height: 10px;
  @include b-radius($b-radius-sm);
  display: inline-block !important;
  position: relative;
  top: 1px;
}

.daterangepicker {
  .calendar {
    padding: 8px;

    .table-condensed {

      tr > td,
      tr > th {
        padding: 8px;
      }
    }
  }
}

.fancybox__container {
  z-index: 9999999999 !important;
}

.table-responsive {
  direction: $direction;

  &.rec-wrap-txt {
    @include mediaMaxWidth($screen-phones) {
      td {
        white-space: initial !important;
      }
    }
  }

  &--ov-visible {
    overflow: visible;
  }

  &--ov-x-visible {
    overflow-x: visible;
  }


}

.rec-wrap-txt {
  @include mediaMaxWidth($screen-phones) {
    td {
      white-space: initial !important;
    }
  }
}

.hc-learning-videos-row {
  display: grid;
  grid-gap: 40px;
  grid-template-columns: repeat(3, 1fr);
  @include mediaMaxWidth(576px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @include mediaMaxWidth(375px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.store-setup-row {
  padding: 0 10px;
  grid-gap: 20px;

  &.row-4col {
    padding: 0 10px;
    grid-gap: 20px;
    grid-template-columns: repeat(4, 1fr);
    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: repeat(3, 1fr);
    }
    @include mediaMaxWidth($screen-tablet-l) {
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 16px;
    }
    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  &.row-5col {
    grid-template-columns: repeat(5, 1fr);
    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: repeat(4, 1fr);
    }
    @include mediaMaxWidth($screen-tablet-l) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(1, 1fr);
      grid-gap: 16px;
    }
  }

  .store-setup-title {
    word-break: break-word;
  }

  &.row-4col {
    padding: 0 10px;
    grid-gap: 20px;
    grid-template-columns: repeat(4, 1fr);
    @include mediaMaxWidth($screen-laptop) {
      grid-template-columns: repeat(3, 1fr);
    }
    @include mediaMaxWidth($screen-tablet-l) {
      grid-template-columns: repeat(2, 1fr);
    }
    @include mediaMaxWidth($screen-phones) {
      grid-template-columns: repeat(1, 1fr);
      grid-gap: 16px;
    }
  }
}

.bg {
  &--white {
    background: $color-white !important;
  }
}

.wide {
  width: 100% !important;
}

.section-container {
  .setting-box {
    &.mid {
      width: 48.25%;
    }

    &.wide {
      width: 100%;
      height: auto;
      margin: 0;
      flex-direction: row;
      padding: 7px 17px 12px;

      i {
        display: inline-block;
        vertical-align: middle;
        margin: 0 0 4px 10px;
        font-size: $text-large;
      }

      p {
        display: inline-block;
        font-size: $text-small;
      }
    }
  }

  &--sb {
    justify-content: space-between;

    > .setting-box {
      margin: 0 !important;
    }
  }
}

.footer {
  position: fixed;
  bottom: 3px;
}

.row {
  &.row-narrow {
    margin-right: -5px;
    margin-left: -5px;

    [class*="col-"] {
      padding-right: 5px;
      padding-left: 5px;
    }
  }
}

.ltr {
  direction: ltr;
}

.rtl {
  direction: rtl;
}

.listing-title-with-excerpt {
  display: block;
  margin: 0 0 4px 0;
  white-space: normal;
}

.full-width {
  width: 100% !important;
}

.full-height {
  height: 100% !important;
}

.w-auto {
  width: auto !important;
}

.listing-excerpt {
  display: block;
  color: $color-dark-100;
  white-space: normal;
  margin: 0;
}

.break-word {
  word-break: break-word;
}

.nowrap {
  white-space: nowrap !important;
}

.d {
  &-inline {
    display: inline;
  }

  &-inline-block {
    display: inline-block !important;
  }

  &-block {
    display: block;
  }
}

// search form - for responsive layout ---
#res_sf {
  display: none;
  max-width: 100%;

  @include mediaMaxWidth($screen-tablet-p) {
    display: block;
    margin: 6px 0 0 -1px;

    .navbar-form {
      width: 100% !important;
      padding: 0;

      .search-group {
        @include flexable();
        margin: 0;

        #searchbox {
          flex: 1;
          max-width: unset !important;
          @include b-radius(0);
        }

        .search-select {
          flex: 0 0 90px;
          width: unset;
        }

        .input-group-btn {
          flex: 0 0 60px;

          .btn {
            width: 60px;
            @include b-radius(0);
          }
        }
      }
    }
  }

  @include mediaMaxWidth(480px) {
    .navbar-form {
      width: 100% !important;
      padding: 0;

      .search-group {
        .input-group-btn {
          flex: 0 0 50px;

          .btn {
            width: 50px;
          }
        }
      }
    }
  }
}

.img-placeholder {
  display: inline-block;
  width: 80px;
  height: 80px;
  @include b-radius(99999px);
  background: $color-gray-200;
  margin: 0 0 10px;
  @include transi(background);

  &.center {
    @include flexable();
  }

  &.small {
    width: 40px;
    height: 40px;
  }
}

.center-loader {
  min-height: 50px;

  &:before {
    position: absolute;
    content: "";
    top: 30%;
    right: 50%;
    width: 25px;
    height: 25px;
    @include b-radius(50%);
    border: 2px solid $color-gray-200;
  }

  &:after {
    position: absolute;
    content: "";
    top: 30%;
    right: 50%;
    width: 25px;
    height: 25px;
    -webkit-animation: loader 0.6s infinite linear;
    animation: loader 0.6s infinite linear;
    border: 2px solid $color-primary;
    @include b-radius(500rem);
    border-color: $color-primary transparent transparent;
    -webkit-box-shadow: 0 0 0 1px transparent;
    box-shadow: 0 0 0 1px transparent;
  }
}

.rtl-safari-hack {
  @media not all and (min-resolution: 0.001dpcm) {
    direction: ltr;
  }
}

.sp-container {
  z-index: 9999999999 !important;
}

.page-load-status {
  text-align: center;
}

hr.thick-sep {
  border-top-width: 2px;
}

.w-lg-200 {
  width: 100%;

  @include mediaMinWidth($screen-tablet-l) {
    width: 200px;
  }
}

.w-50 {
  width: 50%;
}

.h-300 {
  max-height: 300px;
}

.h-600 {
  max-height: 600px;
}

.h-fit-content {
  height: fit-content;
}

.h-full {
  height: 100%;
}

.content-padding {
  @include mediaMinWidth(1400px) {
    padding: 0 20px;
  }
}

.help-tags {
  @include mediaMaxWidth($screen-tablet-l) {
    display: block;
    margin: 8px 0 0 0 !important;

    .mr-10 {
      margin-left: 5px !important;
    }
  }
}

.no-b-radius {
  @include b-radius(0 !important);
}

.border-right-0 {
  border-right: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

// b for before --
.b-v-align {
  &:before {
    vertical-align: middle;
    transform: translateY(-2px);
  }
}

.v-align {
  display: inline-block;
  vertical-align: middle;
  transform: translateY(-2px);
}

.v-align-middle {
  vertical-align: middle;
}

.v-align-top {
  vertical-align: text-top !important;
}

.bg {
  &-tiffany-light {
    background-color: rgba($color-secondary, 0.02) !important;
  }
  &-tiffany-lighter {
    background-color: rgba($color-secondary, 0.05);
  }
  &-danger-light {
    background-color: rgba($color-danger, 0.2);
  }

  &-transparent {
    background: transparent !important;
  }
}

.border {
  &-tiffany {
    border: 1px solid $color-secondary;
  }
  &-tiffany-light{
    border: 1px solid rgba($color-secondary, 0.2) !important;
  }
}

.border-danger {
  border: 1px solid $color-danger;
}

.numeric {
  unicode-bidi: plaintext;
  text-align: right;
}

.img-center {
  margin: 0 auto !important;
}

.img-auto {
  width: auto !important;
}

.padded-block {
  padding: 20px;

  &.bg {
    background: $color-gray-25;
  }

  @include mediaMaxWidth($screen-phones) {
    padding: 15px;
  }
}

.rounded {
  @include b-radius(50%);

  &-sm {
    @include b-radius($b-radius-sm);
  }

  &-md {
    @include b-radius($b-radius)
  }

  &-lg {
    @include b-radius($b-radius);
  }
}

.f-left {
  float: left;

  &-xs {
    @include mediaMaxWidth($screen-phones) {
      float: left;
    }
  }
}

.f-right {
  float: right;

  &-xs {
    @include mediaMaxWidth($screen-phones) {
      float: right;
    }
  }
}

.plain-text {
  unicode-bidi: plaintext;
  text-align: right;
  direction: rtl;
}

.content-flex {
  &-centered {
    @include flexable(center, center, column)
  }
}

#div_row_search_info {
  margin-bottom: 20px;
}

.noticeable-widget-view {
  z-index: 999
}


.row {
  &--flex {
    @include flexable();
    width: 100%;
    margin: 0;

    @include mediaMaxWidth($screen-phones) {
      flex-direction: column;
    }
  }
}

.no-m {
  margin: 0 !important;
}

.max-w-none {
  max-width: unset !important;
}


// general classes
.removed {
  -webkit-animation: disapear 1.4s;
  -webkit-animation-fill-mode: forwards;
  animation: disapear 1.4s;
  animation-fill-mode: forwards;
}

.rotate {
  -webkit-animation: loop 1s normal linear infinite;
  animation: loop 1s normal linear infinite;
}

.content-wrapper {
  .content,
  .page-header {
    @include mediaMinWidth($screen-desktop-small) {
      max-width: 1400px;
      margin-right: auto;
      margin-left: auto;
    }
  }
}

.content-wrapper {
  .iframe-content{
    border-top: 1px solid $color-gray-200;
    .dark &{
      border-color: #333;
    }
  }
}

.d-none {
  display: none;
}

.sticky-el {
  position: sticky;
  z-index: 111;

  &.top {
    &-20 {
      top: 20px;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    position: relative;
    top: unset !important;
  }
}

.bg {
  &-gray {
    background: $color-gray-25 !important;
  }

  &-gray-50 {
    background: $color-gray-50 !important;
  }

  &-gray-100 {
    background: $color-gray-100 !important;
  }
}

.border-bottom {
  border-bottom: 1px solid $color-gray-200 !important;
}

.last-border-0:last-child {
  border-width: 0;
}

.border-top {
  border-top: 1px solid $color-gray-200 !important;
}

.border-left-none {
  border-left: none;
}

.limited-height {
  overflow-y: auto;
  @include scrollBar(3px, $color-primary, $color-gray-200);
  padding-left: 20px;
}

.img-gray {
  filter: grayscale(100%);
}

.bt-none {
  border-top: none !important;
}

.bb-none {
  border-bottom: none !important;
}

// sticky page ---
.products-bulk-editor,
.products-quantity-editor {
  overflow: hidden;

  .sidebar.sidebar-main {
    height: calc(100vh - 46px);

    .sidebar-content {
      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background: rgba($color-dark-100, 0.2);
        -webkit-border-radius: 0;
        @include b-radius(0);
      }

      &::-webkit-scrollbar-thumb {
        -webkit-border-radius: 0;
        @include b-radius(0);
        background: $color-dark-100;
      }

      &::-webkit-scrollbar-thumb:window-inactive {
        background: rgba($color-dark-100, 0.5);
      }
    }
  }

  .content {
    position: relative;
    // subrtact header height ---
    height: calc(100vh - 120px);
    min-height: unset;
    padding-bottom: 10px;
  }


  @media screen and (max-width: 768px) {
    .content-wrapper {
      overflow-x: hidden;
    }

    .content {
      padding: 0 !important;
      height: calc(100vh - 95px);
    }

    #page_header_box {
      height: 0;
      padding: 0;
      margin: 0;
      min-height: auto;
      position: relative;
      z-index: 1;

      .page-header-content {
        padding: 0;
      }

      .heading-elements.heading-help .btn.btn-icon {
        margin: 0;
      }
    }
  }
}


.bordered {
  border: 1px solid $color-gray-200 !important;

  &-gray-200 {
    border: 1px solid $color-gray-300 !important;
  }
}

.b-radius {
  @include b-radius($b-radius !important);

  &--min {
    @include b-radius($b-radius-sm !important)
  }

  &--lg {
    @include b-radius($b-radius-lg !important)
  }
}

.border {
  &-gray {
    border: 1px solid $color-gray-25;
  }

  &-gray-100 {
    border: 1px solid $color-gray-100;
  }

  &-gray-200 {
    border: 1px solid $color-gray-200;
  }

  &-none {
    border: none !important;
  }
}

._hj-4a_14__MinimizedWidgetBottom__left {
  left: 100px !important;
}

.overflow-hidden {
  overflow: hidden !important;
}


.otp-wrapper {
  max-width: 300px;
  margin: 0 auto !important;
}

.h-full-screen {
  height: 80vh !important; // minus the wrapper top padding of 20px
}

.bg-white {
  background: $color-white;
}

.payment-url {
  background: $color-gray-100;
  padding-right: 15px;
  @include b-radius($b-radius-sm);

  p {
    font-size: $text-x-small;
    color: $color-dark-100;
    padding-left: 1rem;
    margin: 0;
  }

  button {
    padding-right: 10px;
    font-size: $text-xx-small;
    padding-left: 10px;

    i {
      display: inline-block;
      margin-left: 5px;
      transform: translateY(-1px);
    }
  }


  &--light {
    border: 1px solid $color-gray-300;
    @include b-radius($b-radius-sm);
    height: 38px;

    .link-wrapper {
      padding-left: 13px;
      overflow: hidden;
      flex: auto;

      p {
        font-size: $text-x-small;
        color: $color-dark-100;
        padding-left: 1rem;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    button {
      padding: 0 14px;
      color: $color-dark-300;
      font-weight: bold;
      height: 36px;
      border-right: 1px solid $color-gray-300;
      @include b-radius(2px 0 0 2px);
      background-color: $color-white;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    p {
      font-size: get-rem($text-medium);
    }
  }
}

#force-rebuild {
  color: $color-white;
}

.rec {
  &-align-center {
    text-align: center;
  }
}

.creditor {
  color: $color-danger !important;
  font-weight: bolder;
}

.option-hint {
  display: block;
  font-size: $text-xxx-small;
  padding-right: 33px;
  color: $color-dark-100;
}

.jconfirm .jconfirm-box .jconfirm-buttons button.btn-default:last-child {
  margin: 0 10px;
}

.z-index-1 {
  z-index: -1;
}

.t-2px {
  display: inline-block;
  transform: translateY(-3px);
}

.default-languages {
  border-width: 0 0 1px;
}

.t-2px {
  display: inline-block;
  transform: translateY(-3px);
}

.cursor-default {
  cursor: default !important;
}

.z-10 {
  z-index: 10;
}

.d-block-force {
  display: block !important;
}

.content-wrap {
  width: 100%;
  max-width: 1400px;
  height: auto;
  position: relative;
  margin: 0 auto;
  padding: 0 40px;

  &.shift-center {
    transform: translateX(-130px);
  }
}

pr {
  @include b-radius($b-radius);
}

.intercom-namespace .intercom-1qaopps {
  width: 0 !important;
  height: 0 !important;
}

.has-renew-plan {
  .intercom-launcher-badge-frame {
    bottom: 93px !important
  }

  @include mediaMaxWidth($screen-phones) {
    #floating_menu_wrapper {
      bottom: 70px !important
    }
  }
}

.intercom-with-namespace-16gpml1 {
  width: 0 !important;
  height: 0 !important;
}

.intercom-namespace .intercom-with-namespace-1pf7jua,
.intercom-namespace .intercom-with-namespace-vlu1up {
  height: 0 !important;
}

.multi-order-search-tooltip {
  position: absolute;
  left: 10px;
  background-color: white !important;
  z-index: 999;
}


.chatgpt-iframe {
  &.open {
    position: fixed;
    bottom: 0px;
    left: 0px;
    height: 100vh;
    width: 100vw;
    z-index: 999999;
    border: none;
  }

  &.close {
    width: 90px;
    height: 90px;
    position: fixed;
    bottom: 0px;
    left: 0px;
    opacity: 1 !important;
    z-index: 999999;
    border: none;
  }
}

.has-renew-plan {
  .chatgpt-iframe {
    @include mediaMaxWidth($screen-phones) {
      bottom: 70px;
      left: -10px;
    }
  }
}

@include mediaMaxWidth($screen-phones) {
  .search-input-container {
    flex: 1;
  }
}
.col-md-5ths{
  position: relative;
  min-height: 1px;
  padding-right: 10px;
  padding-left: 10px;
  @media (min-width: 992px) {
    width: 20%;
    float: right;
  }
  @media (min-width: 1200px) {
    width: 20%;
    float: right;
  }
}

.new-dash-upgrade-icon {
  width: 48px;
  height: 48px;
  border-radius: 999px;
  background: var(--color-secondary-25);
  line-height: 1;
  flex-shrink: 0;
}

.new-dash-features-list {
  li {
    flex: 0 0 calc(33.33% - 20px);
  }
  @include mediaMaxWidth($screen-phones) {
    li {
      flex: 0 0 100%;
    }
  }
}