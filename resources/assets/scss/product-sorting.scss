@import "partials/vars";
@import "partials/mixins";
@import "partials/components/pages-indicator";

#sorting {
  font-family: $font-main, serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: $color-dark-300;
  margin-top: 60px;
}

#sort-mode {
  position: relative;

  #sort_controls {
    width: 100%;

    &.sticky-controls {
      padding-top: 20px;
      padding-bottom: 20px;
      z-index: 99;
      background-color: $color-gray-25;
    }
  }
}

.product-listing {
  @include strip-ul();
  @include flexable(center, flex-start, row);
  flex-wrap: wrap;
  margin: 30px 0 0 0;
}

.page-indicator {
  @include flexable(center, flex-start, column);
  width: 10px;
  height: 100%;
  position: absolute;
  top: 0;
  left: -30px;

  .page {
    @include flexable();
    flex: 1;
    position: relative;

    small {
      @include flexable();
      width: 40px;
      height: 40px;
      @include b-radius(50%);
      background-color: $color-gray-200;
      color: $color-dark-100;
      border: 1px solid $color-gray-25;
    }

    &:after {
      content: '';
      display: block;
      width: 1px;
      height: calc(100% - 30px);
      position: absolute;
      top: 10px;
      background-color: $color-gray-200;
      z-index: -1;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    left: -15px;
  }
}

// select customizer ---
.bootstrap-select {
  &.sort-select.btn-group {
    width: 100% !important;
    position: relative;

    button {
      &.btn.dropdown-toggle {
        width: 100%;
        min-height: 42px;
        font-size: $text-x-small;
      }
    }

    > .dropdown-menu {
      overflow-y: auto !important;
      .dropdown-menu {
        overflow-y: unset !important;
      }

      .bs-searchbox {
        input {
          &.form-control {
            background-color: $color-gray-25;
            padding: 0.375rem 30px 0.37rem 1.2rem !important;
            font-size: $text-x-small;
            @include b-radius($b-radius-sm);
          }
        }

        &:after {
          content: '\ef09';
          font-family: $font-sallaIcon, serif !important;
        }
      }

      ul {
        li {
          text-align: right;

          a {
            &.child {
              padding-right: 35px;
              color: $color-gray-400;

              &:before {
                content: '—';
                display: inline-block;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 15px;
              }
            }
          }
        }
      }

      @include mediaMaxWidth($screen-phones) {
        overflow-y: unset !important;
      }
    }

    @include mediaMaxWidth($screen-laptop-small) {
      margin: 0 0 10px 0;
    }
  }
}

.btn {
  &.btn-tiffany {
    &.sort-save {
      width: 100%;
      height: 40px;
    }
  }
}

#products_container {
  position: relative;
}

#intro {
  @include flexable(center, flex-start, column);
  margin: 30px 0 0 0;

  img {
    display: none;
    width: 80%;
    height: auto;

    &.desktop {
      max-width: 750px;
      @include mediaMinWidth($screen-phones) {
        display: block;
      }
    }

    &.mobile {
      max-width: 450px;
      @include mediaMaxWidth($screen-phones) {
        display: block;
      }
    }
  }

  p {
    margin: 0 0 30px 0;
  }

  @include mediaMaxWidth($screen-tablet-l) {
    img {
      width: 100%;
    }
  }
}

#scroll_top {
  width: 50px;
  height: 50px;
  background: $color-secondary;
  @include b-radius(50%);
  @include transi();
  opacity: 0;
  visibility: hidden;
  position: fixed;
  bottom: 30px;
  left: 20px;
  z-index: 999;
  cursor: pointer;
  transform: translateY(60px);
  transition-duration: 1s;

  i {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translateY(-50%) translateX(50%);
    font-size: $text-xx-medium;
    color: $color-white;
  }

  &:hover {
    background: $color-secondary;
  }

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.sort-product-entry {
  display: flex !important;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  width: 100%;
  height: 250px;
  position: relative;
  direction: rtl;
  text-align: right;
  padding: 15px;
  margin: 0 8px 16px;
  background-color: $color-white;
  border: 1px solid $color-gray-200;
  cursor: move;
  @include b-radius($b-radius-sm);

  .sort-thumb,
  .sort-content {
    width: 100%;
  }

  .sticky {
    @include flexable();
    width: 30px;
    height: 30px;
    @include b-radius(50%);
    position: absolute;
    top: -15px;
    right: 15px;
    box-shadow: $box-shadow;
    background-color: $color-danger;

    &:before {
      display: block;
      font-size: $text-xx-small;
      color: $color-white;
    }
  }

  .sort-thumb {
    //height: 60%;
    flex: 1;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
    }
  }

  .move-handler {
    display: none;
    width: 30px;
    height: 30px;
    position: absolute;
    top: 15px;
    right: 15px;
    @include b-radius($b-radius-sm);
    background-color: rgba($color-primary, 0.8);

    svg {
      width: 20px;
      height: 20px;

      * {
        fill: $color-white;
      }
    }
  }

  .sort-content {
    padding: 0.25rem 0 0;

    .sort-title {
      @include flexable(center, flex-start, row);
      width: 100%;
      margin: 0 0 0.25rem 0;
      padding: 0.25rem;
      font-size: $text-x-small;
      color: $color-dark-300;
      line-height: 17px;

      i {
        display: inline-block;
        flex: 0 0 1rem;
        margin-left: 5px;
      }
    }

    .sort-meta {
      @include flexable(center, flex-start, row);
      flex-wrap: wrap;
      @include strip-ul();
      width: 100%;
      height: auto;

      li {
        display: inline-block;
        flex: 1 0 auto;
        padding: 4px 10px 8px;
        font-size: $text-x-small;
        color: $color-dark-100;

        &.available, &.unavailable {
          flex: 0 0 auto;
          padding: 5px 10px 8px;
          @include b-radius(50px);
          font-size: $text-xxx-small;
          text-align: center;
          line-height: 1;
        }

        &.available {
          color: $color-primary;
          background-color: rgba($color-primary, 0.1);
        }

        &.unavailable {
          color: $color-danger;
          background-color: rgba($color-danger, 0.1);

        }
      }
    }
  }

  &.sortable-chosen {
    box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.3);
    overflow: hidden;

    &.ghost {
      transform: scale(1);
      opacity: 0.5;
      filter: blur(1px);
      -webkit-filter: blur(1px);
    }
  }

  &.sticky {
    pointer-events: none;
    cursor: help;

    .move-handler {
      display: none;
    }
  }

  &.dimmed {
    opacity: 0.4;
  }

  &:hover {
    box-shadow: $box-shadow;
  }

  @include mediaMinWidth($screen-tablet-l) {
    width: calc(25% - 16px);
  }
  @include mediaMaxWidth($screen-tablet-l) {
    width: calc(33.33% - 16px);
  }
  @include mediaMaxWidth($screen-phones) {
    height: 180px;
    width: calc(50% - 16px);
    .move-handler {
      @include flexable();
    }
  }
}