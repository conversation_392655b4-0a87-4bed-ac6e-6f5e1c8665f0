#products_div {
  .product-form {
    .thumbnail {
      background-color: $color-dark-700;
      border-color: $color-dark-400 !important;
      .thumb-mask {
        img[src*="placeholder.png"] {
          filter: invert(1);
        }
      }
      .form-group {
        .btn-tiffany {
          color: $color-gray-300 !important;
          background-color: $color-dark-400 !important;
          box-shadow: none !important;
          border-color: $color-dark-300 !important;
          &:focus {
            border-color: $color-dark-300 !important;
            background-color: $color-dark-400 !important;
          }
        }
      }
      .product-quantities {
        .btn-infinite {
          svg {
            fill: $color-gray-300;
          }
          &.active {
            background-color: rgba($color-secondary-50, 0.05) !important;
            svg {
              fill: $color-secondary-50;
            }
          }
        }
      }

      .controls-wrapper {
        .options-wrapper {
          border-color: $color-dark-300 !important;
          .options {
            .btn {
              color: $color-gray-300 !important;
              background-color: transparent !important;
            }
            &:first-of-type {
              border-left-color: $color-dark-300 !important;
            }
          }
        }
        .submit-wrapper {
          .btn-tiffany {
            color: $color-gray-25 !important;
            background-color: $color-dark-200;
            border-color: $color-dark-200;
            &:hover {
              background-color: $color-dark-300;
              border-color: $color-dark-300;
            }
          }
        }
      }
      .pin_btn {
        color: $color-gray-25;
        background-color: transparent;
        border: 1px solid rgba($color-dark-400, 0.5);
        &.pinned {
          border-color: $color-danger;
        }
      }
      &.hidden-product {
        &:after {
          background: rgba($color-dark-300, 0.65);
        }
      }
    }
    div.product-check{
        label{
          color:$color-white ;
        }

    }
  }
  &.list {
    .product-box form.product-form .thumbnail .controls-wrapper .options-wrapper .options .btn.btn-product-delete {
      border: none !important;
    }
  }
}

.product_image_btn {
  background: $color-dark-300;
  border: 1px solid $color-dark-300;
  color: $color-gray-400;
}


.thumbnail {
  background-color: $color-dark-700;
  border-color: $color-dark-400 !important;
}