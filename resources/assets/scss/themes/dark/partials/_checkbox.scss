.rec-checkbox {
  $checkbox-root: &;

  input[type=checkbox],
  input[type=radio] {
    + label {
      &:before {
        border-color: rgba($color-secondary-50, 0.45);
      }
    }
  }

  input[type=checkbox] {
    + label {
      &:after {
        color: $color-secondary-50;
      }
    }
  }

  input[type=radio] {
    + label {
      &:after {
        background-color: $color-secondary-50;
      }
    }
  }


  // as buttons ---
  &--buttons {
    margin: 0 !important;

    input[type=checkbox],
    input[type=radio] {
      + label {
        border: 1px solid $color-dark-300;

        &:hover {
          border-color: $color-dark-300;
        }
      }

      &:checked {
        + label {
          color: $color-dark-300;
        }
      }
    }
  }

  &--boxed {
    input[type=checkbox],
    input[type=radio] {

      + label {
        border: 1px solid $color-dark-300;
      }

      &:checked {
        + label {
          border-color: rgba($color-dark-300, 0.5);
        }
      }
    }
  }

  // boxed border radio
  &--bordered-option {

    input[type=checkbox],
    input[type=radio] {
      display: none;

      + label {
        border-color: $color-dark-300;
      }

      &:checked {
        + label {
          background-color: rgba($color-dark-300, 0.05);
          border-color: $color-dark-300;

          &:before {
            border-color: $color-secondary-50;
          }
        }
      }
    }
  }

  &--light-primary {
    input[type=checkbox],
    input[type=radio] {
      &:checked {
        + label {
          &:before {
            border-color: $color-secondary-l;
          }

          &:after {
            background-color: $color-secondary-l;
          }
        }
      }
    }
  }
}

.panel-heading.dark-heading {
  &[aria-expanded="true"] {
    .rec-checkbox {
      input[type=checkbox],
      input[type=radio] {
        + label {
          &:before {
            border-color: rgba($color-primary-l, 0.45);
          }
        }
      }

      input[type=checkbox] {
        + label {
          &:after {
            color: $color-primary-l;
          }
        }
      }
    }
  }
}

.choice {
  span {
    border-color: $color-secondary-50 !important;

    &:after {
      border-color: $color-secondary-50 !important;
    }
  }
}