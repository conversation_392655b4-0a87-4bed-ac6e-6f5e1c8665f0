.vuetable {
  &.ui.table {
    color: $color-gray-300;
    background-color: $color-dark-700;
  }
}

.vuetable-body-wrapper .vuetable.ui.table thead tr > *[class$=-sticky],
.vuetable-body-wrapper .vuetable.ui.table thead tr > *:first-of-type {
  color: $color-gray-300;
  background-color: $color-dark-600;
}

.vuetable-body-wrapper .vuetable.ui.table thead tr th {
  color: $color-gray-300 !important;
  background-color: $color-dark-700 !important;
  border-color: $color-dark-300 !important;
}

.bulk-edit-table .vuetable-body-wrapper .vuetable.ui.table tbody tr > *:first-of-type {
  background-color: $color-dark-700;
}

.vuetable-body-wrapper .vuetable.ui.table tbody tr td:hover:not([class$=-sticky]) {
  color: $color-secondary-50;
  background-color: rgba($color-secondary-50, 0.05);
}