.v-select {
  .vs__selected {
    color: $color-gray-300;
  }

  .vs__selected-options {
    border: 1px solid $color-dark-300;
    background-color: $color-dark-700;


    &:after {
      color: $color-dark-100;
    }
  }

  &--full {
    .vs__selected-options {
      border-right-color: $color-dark-300 !important;
    }
  }

  &--full {
    .vs__selected-options {
      border-right-color: $color-dark-300;
    }
  }

  &--deselect {
    .vs__selected {

      .vs__deselect {

        &:after {
          color: $color-gray-300;
        }
      }
    }
  }

  &--light {
    .vs__dropdown-menu {
      li {

        &.vs__dropdown-option--selected {
          background-color: rgba($color-secondary-50, 0.1);

          &:after {
            color: $color-secondary-50; //colorAvilabilty
          }
        }
      }
    }
  }

  &--with-search {
    &.vs--open {
      &:before {
        background: $color-white;
      }

      .vs__selected-options {
        input {
          background: $color-gray-25;
        }

        &:before {
          color: $color-gray-400;
        }
      }
    }
  }
}

.vs__dropdown-menu {
  background-color: $color-dark-600;
  color: $color-gray-300;

  .vs__dropdown-option {
    color: $color-gray-300 !important;

    &:hover {
      background-color: rgba($color-dark-500, 0.8);
    }

    h6 {
      color: $color-gray-300;
    }

    &--disabled {
      background: $color-gray-25;
      color: $color-dark-100;
    }
  }
}