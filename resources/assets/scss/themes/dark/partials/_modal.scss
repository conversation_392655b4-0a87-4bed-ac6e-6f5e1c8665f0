.modal {
  &-header {
    color: $color-gray-300;
    background-color: $color-dark-600;
    border-color: $color-dark-600;

    .close {
      color: $color-dark-100 !important;
    }
  }

  &-content {
    background-color: $color-dark-700;
  }

  &-footer {
    background-color: $color-dark-600;
  }

  &-light {
    .modal-header,
    .modal-footer {
      background-color: $color-dark-700 !important;
    }
    .modal-title {
      color: $color-gray-300 !important;
    }
  }

  &-backdrop {
    background-color: $color-black;
  }
}

.jconfirm {
  &-box {
    background-color: $color-dark-700;
    .jconfirm-title-c {
      color: $color-gray-300;
      background-color: $color-dark-600;
      border-color: $color-dark-600;
    }
    .jconfirm-buttons {
      background-color: $color-dark-600;
      .btn.btn-default {

      }
    }
  }
  &-bg {
    background-color: $color-black !important;
  }
}

.custom-modal {
  &#modal_quantity_management {
    .default-option-selector {
      border-color: $color-dark-300;
      background: $color-dark-700;


      &:after {
        background: $color-dark-400;
      }

      .btn {
        border-color: $color-dark-300;
        color: $color-gray-300;


        &.selected {
          color: $color-secondary-50;
          border-color: $color-secondary-50;
        }
      }
    }

    .option {
      &-section {
        border: 1px solid $color-gray-100;
      }
    }

    .btn {
      &-delete {
        &-option {
          background-color: $color-danger;
          color: $color-white;

          &:hover {
            background-color: #ed6163;
          }
        }

        &-value {
          color: $color-danger;
          background: $color-white;
          box-shadow: 0 0 0 1px #ffd6d6 !important; //colorAvilabilty

          &:hover {
            background-color: #f451540f; // colorAvilabilty
          }
        }
      }

      &-add {
        &-value {
          color: $color-secondary-50;
          border-color:$color-secondary-50;
        }
      }
    }

    p {
      color: $color-dark-200
    }

    hr {
      border-top-color: $color-gray-200;
    }

    .product_feature_image_crop {
      &::before {
        color: $color-dark-300;
      }
    }

    .fileinput-remove {
      &:before {
        color: $color-white;
      }

      &:hover {
        background-color: $color-danger;
        i {
          color: $color-white
        }
      }
    }

    .btn-file {
      cursor: pointer;
    }


    .skus-conatainer {
      &::after {
        background: linear-gradient(0deg, rgba(255, 255, 255, 1) 35%, rgba(255, 255, 255, 0) 100%);
      }
    }
  }

  &.fade {
    background: rgba($color-black, .75);
  }
}
