.main-title > div:nth-child(2) > b,
.store-card div:last-child > h3 {
  color: $color-gray-200;
}

.app-showcase-card,
.store-card{
  background: none;
  border: 1px solid $color-dark-400;
}

.metrics-card, .suggested-header, .vr{
  background: $color-dark-600;
  border: 1px solid $color-dark-400;
}

.install-mahly-container>div:nth-child(2){
  span{
    color: $color-gray-300;
  }
  small{
    color: $color-gray-400;
  }
}

.metrics-wrapper, .sec-header, .metrics-wrapper .sec-header i{
  color: $color-gray-300;
}

.metrics-chart-controls, .suggested-steps-desc p, .metrics-chart-header h5{
  color: $color-gray-400;
}

.completed-suggested-steps {
  >span{
    color: $color-gray-200;
  }

  >p{
    color: $color-gray-400;
  }
}

.suggested-progress-count{
  border-color: $color-dark-100;
}

.suggested-progress-bar{
  background: $color-gray-400;
}