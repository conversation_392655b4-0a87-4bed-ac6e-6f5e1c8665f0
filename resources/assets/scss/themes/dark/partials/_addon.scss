.salla-addon {
  $salla-addon-root: &;
  background-color: $color-dark-700;
  border-color: $color-dark-400;

  &__meta {
    #{$salla-addon-root} {

      &__title {
        * {
          color: $color-secondary-50;
        }

        &:hover {
          * {
            color: $color-secondary-50;
          }
        }
      }
    }

    .rec-price-wrapper {

      .product-price {
        color: $color-gray-300;

        .currency {
          color: $color-secondary-50;
        }

        small {
          color: $color-gray-300;
        }

        b {
          color: $color-secondary-50;
        }
      }
    }
  }

  &__desc {
    * {
      color: $color-gray-400 !important;
    }
  }

  &--installed {

    .salla-addon {
      &__thumb {
        &:after {
          background: rgba($color-secondary-50, 0.8);
        }
      }

      &__settings {
        background: $color-secondary-50;

        &:before {
          color: $color-gray-300;
        }
      }
    }
  }

  &--detail {
    .addon-title {
      &__thumb {
        > div {
          > * {
            color: $color-dark-200;
          }

          h1 {
            color: $color-dark-300;
          }

          p {
            color: $color-dark-200;
          }
        }
      }

      &__price {
        .rec-price-wrapper {
          b {
            color: $color-primary;
          }

          .product-price {
            color: $color-dark-300;
          }
        }
      }
    }

    .salla-addon {
      &__content {
        * {
          color: $color-dark-50;
        }
      }
    }
  }

  &--plan {
    .salla-addon {
      &__title-wrapper {
        h2 {
          color: $color-secondary-50;
        }
      }

      &__info {

        .rec-list--numeric {
          &.checked-list {
            li {
              &:before {
                color: $color-secondary;
              }
            }
          }
        }
      }

      &__desc {
        p {
          span {
            background-color: transparent !important;
          }
        }
      }

      &__options {
        background: rgba($color-secondary-50, 0.02);
        border-color: $color-dark-400;

        .rec-list {
          li {

            label {
              color: $color-gray-300;

              .badge {
                &.light {
                  background-color: $color-gray-200;
                }
              }
            }
          }
        }
      }

      &__duedate {
        b {
          color: $color-secondary-50;
        }
      }

      &__renewal {
        .header {
          background: $color-gray-25;
          color: $color-black;
        }

        .body {
          background-color: $color-dark-400;
          border-color: $color-dark-300;
        }
      }
    }

    &.single {
      .single__meta {
        @include mediaMinWidth($screen-laptop) {
          &::after {
            background-color: $color-dark-400;
          }
        }
      }
    }

    &.upgrade {
      .featured-stores {
        border-right-color: $color-dark-300;
      }
    }

    &:after {
      opacity: 0.1;
    }
  }
}

.salla-addon__gift {
  background-color: transparent;
  padding: 0;
}