.default-languages {
  border-color: $color-dark-300;

  &_title {
    p {
      color: $color-dark-100;
    }
  }
}

.languages-table-wrap {

  @media (max-width: $screen-phablet) {
    .dropdown-backdrop {
      background: rgba($color-dark-300, 0.6);
    }
  }
}

.languages-table {
  tr {
    background-color: transparent;
    &:nth-child(even) {
      background-color: $color-dark-500;
    }

    .badge {
      background-color: $color-gray-300;
    }
  }

  .language-name__default {
    color: $color-gray-300;
  }
}

.add-new-lang {
  background-color: $color-dark-800;
  border-top-color: $color-dark-300;
}