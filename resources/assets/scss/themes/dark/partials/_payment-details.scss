.payment-detail {
  .rec-price-wrapper {
    .product-price {
      color: $color-gray-300;
    }
  }
}

.rec-list {
  &.payment-totals {
    background: $color-dark-600;
    border-top-color: $color-dark-300;
    border-bottom-color: $color-dark-300;

    > li {
      * {
        color: $color-gray-300;
      }

      &.payment-subtotal {
        border-bottom-color: $color-dark-300;
      }

      &.payment-coupon {

        > div {

          &#coupon_code {


            .form-group {

              &.coupon-field {
                .form-control {

                  &:focus,
                  &:active {
                    border-color: $color-secondary-50;
                    box-shadow: 0 0 4px 5px rgba($color-secondary-50, 0.05);
                  }
                }
              }
            }
          }
        }
      }

      &.payment-total-price {
        border-top-color: $color-dark-300;

        h4 {
          color: $color-dark-100;
        }
      }
    }
  }

  &.alt-payments {
    li {
      a {
        border-color: $color-dark-300;
        color: $color-gray-300;
      }

      &.active {
        a {
          color: $color-gray-300;
          border-color: $color-secondary-50;

          &:hover {
            border-color: $color-secondary-50;
          }
        }
      }
    }
  }
}

.payment-entry {
  border-color: $color-dark-300;
}