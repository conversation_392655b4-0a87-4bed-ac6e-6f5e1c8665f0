:root {
  //coman color
  --color-primary-l:#004D5A;
  --color-secondary-50: #BAF3E6;
  --color-secondary-25:#CFF7EE;
  --color-white:#fff;
  --color-gray-300: #dddddd;
  --color-gray-200: #eeeeee;
  --color-gray-400:#bbbb;
  --color-gray-500:#cccc;
  --color-danger: #f55157;
  --color-dark-100:#999;
  --color-dark-200:#666;
  --color-dark-300:#444;
}

.dp{
  &__theme_light{
    --dp-background-color: #212121 !important;
    --dp-primary-color: var(--color-secondary-25) !important;
    --dp-primary-text-color:var(--color-dark-300);
    --dp-text-color:var(--color-gray-500);
    --dp-secondary-color: var(--color-dark-100);
    --dp-hover-color: rgba(186, 243, 230, .2);
    --dp-menu-border-color:var(--color-dark-300);
  }
  &__main{
    .dp__input_wrap{
      input{
        padding:8px 30px 7px 12px;
      }
      .dp{
        &__input{
          border:1px solid var(--color-dark-300);
          &:hover{
            border-color: var(--color-dark-300);
          }
        }
        &__input_icon{
          left: unset;
          right:9px;
          color:#666;
        }
        &__clear_icon{
          right:unset;
          left:9px;
          color:var(--color-danger);
        }
      }
    }
  }
  &__outer_menu_wrap{
    .dp {
      &__action_cancel {
        background-color: #444;
        color:var(--color-gray-400);
        border: 1px solid #444;
      }
      &__cell_disabled{
        color:#444;
      }
      &__action_select {
        background-color: var(--color-secondary-25);
        color: var(--color-primary-l);
      }
    }
  }
}
