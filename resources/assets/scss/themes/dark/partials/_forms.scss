.form {
  &--light {
    .form-group {
      .form-control,
      .bootstrap-select .btn.dropdown-toggle.btn-default {
        border-color: $color-dark-300 !important;

        &:hover, &:active, &:focus {
          border-color: $color-dark-300 !important;
        }
      }

      .bootstrap-select {
        .btn.dropdown-toggle.btn-default {
          border-color: $color-dark-300 !important;
        }
      }

      .input-group {
        .input-group-addon {
          border-color: $color-dark-300;
        }

        &.input-group-append {

          .form-control {
            border-color: $color-dark-300;

            &:hover, &:active, &:focus {
              border-color: $color-dark-300;
            }
          }
        }
      }
    }
  }
}

.form-group {
  .input-group-addon {
    color: $color-dark-200;
    border-color: $color-dark-300;
  }

  label {
    color: $color-gray-400 !important;
  }

  .form-control {
    &:focus {
      border-color: $color-dark-300 !important;
    }
  }

  .message-block {
    border-color: $color-dark-300;
    background: $color-dark-300;
  }

  .input-group input[disabled] ~ .input-group-addon {
    background-color: $color-dark-800;
  }

  &.search-field {
    border: none;

    .form-control {
      border: none;
    }
  }

  &.excel-upload-section {
    &--inline {
      .rec-upload-label {
        .form-control {
          &:focus {
            + label {
              background-color: $color-dark-700;
            }
          }
        }
      }
      label {
        background-color: $color-dark-700;
        border-color: $color-dark-300;
      }

      &.unloaded {
        label {
          background-color: $color-dark-700;
        }
      }
    }
  }

  &--with-border,
  &--bordered {
    border-color: $color-dark-400;
  }
}

.form-control,
.selectize-input {
  color: $color-gray-400;
  background: $color-dark-700;
  border-color: $color-dark-300;
}

.selectize-input {
  border-color: $color-dark-300 !important;

  input {
    color: $color-gray-400 !important;
    background: $color-dark-700;
    border-color: $color-dark-300 !important;
  }

  &.focus,
  &.input-active {
    background: $color-dark-600 !important;
    border-color: $color-dark-200 !important;
  }
}

.selectize-dropdown {
  border-color: $color-dark-300;

  .active {
    color: $color-gray-300;
    background-color: $color-dark-800;
  }
}

.input-group-addon {
  background: $color-dark-700;
  border-color: $color-dark-300;

  i {
    color: $color-dark-200;
  }
}

.search-input {
  border-right-color: $color-dark-300 !important;
}

#searchbox {
  border-color: $color-dark-300;
}

.search-group {
  .bootstrap-select {
    border-right-color: $color-dark-300;

    .btn {
      color: $color-gray-300;
      background-color: $color-dark-400;
      border-color: $color-dark-300 !important;
    }
  }
}

.dropdown-menu {
  color: $color-gray-400;
  background-color: $color-dark-400;

  > li {
    > a {
      color: $color-gray-300;

      &:hover {
        background-color: $color-dark-600 !important;
      }
    }

    &.selected {
      > a {
        color: $color-secondary-50 !important;
        background-color: rgba($color-secondary-50, 0.15) !important;

        &:hover {
          color: $color-secondary-50 !important;
          background-color: rgba($color-secondary-50, 0.20) !important;

          * {
            color: $color-secondary-50 !important;
          }
        }
      }
    }

    &.divider {
      background-color: $color-dark-300 !important;
    }
  }

  &--products-type {
    li {
      a {
        h6 {
          color: $color-gray-200;
        }

        &.dropdown-link--template {
          border-top-color: $color-dark-300;
          background-color: rgba($color-dark-600, 0.50);
        }
      }
    }
  }
}

.dropdown-menu > .dropdown-submenu:hover > a,
.dropdown-menu > .dropdown-submenu:focus > a {
  background-color: $color-dark-600;
}

.bootstrap-select {
  &.btn-group {
    .dropdown-menu {
      background-color: $color-dark-400;

      > li {
        > a {
          color: $color-gray-300;

          &:hover {
            background-color: $color-dark-600 !important;
          }
        }

        &.selected {
          > a {
            color: $color-secondary-50 !important;
            background-color: rgba($color-secondary-50, 0.15) !important;

            &:hover {
              color: $color-secondary-50 !important;
              background-color: rgba($color-secondary-50, 0.20) !important;

              * {
                color: $color-secondary-50 !important;
              }
            }
          }
        }
      }
    }

    .btn {
      &.btn-default {
        color: $color-gray-400;
        border-color: $color-dark-300 !important;
        background-color: $color-dark-700;

        &:focus {
          background-color: $color-dark-700;
        }

        &:hover {
          border-color: $color-dark-300 !important;
          background-color: $color-dark-700 !important;
        }
      }
    }
  }
}

.field-section {
  background: $color-dark-400 !important;
  border: 1px solid $color-dark-300;

  > div.field-row-header,
  .field-row-header-splitter {
    background-color: $color-dark-400;
    border-bottom: 1px solid $color-dark-300;

    .field-label-col {
      .label-info {
        color: $color-gray-300;
      }
    }

    .field-required-col {
      .btn-group {
        .btn {
          &.remove-button {
            i {
              color: $color-white
            }
          }
        }
      }

      &:after,
      &:before {
        background: $color-white;
      }
    }
  }

  .field-row-header-splitter {
    .btn-group {
      .btn {
        background-color: $color-white;
      }
    }

    &:after,
    &:before {
      background: $color-gray-200;
    }
  }

  .add_option {
    background-color: $color-primary !important;
  }

  .remove-button-option {
    i {
      color: $color-white;
    }
  }

  &--disabled {
    .fields-wrapper {
      background: $color-gray-50;
    }
  }
}

.ui {
  &.popup {
    &.calendar {
      &:before {
        box-shadow: -1px -1px 0 0 $color-dark-700 !important;
        background-color: $color-dark-700;
      }

      table {
        &.ui.table {
          thead {
            tr {
              th {
                .link {
                  color: $color-gray-200;
                }
              }
            }
          }

          tbody {
            tr {
              td {
                font-family: 'Arial', serif;
                text-align: center;

                &.link {
                  &.today {
                    color: $color-primary-l;
                    background-color: $color-secondary-50;
                  }

                  &.range {
                    color: darken($color-primary-l, 5%);
                    background-color: rgba($color-secondary-50, 0.2);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // dropdown ---
  &.dropdown {
    border-color: $color-dark-400 !important;
    background-color: $color-dark-700;

    .text {
      color: $color-gray-400 !important;

      &.default {
        color: $color-gray-300 !important;
      }
    }

    i {
      &.dropdown.icon {
        color: $color-gray-400;
      }
    }

    .menu {
      background-color: $color-dark-400;
      border-color: $color-dark-400;

      .item {
        color: $color-dark-100 !important;

        &:hover {
          background-color: $color-dark-600 !important;
        }

        &.active,
        &.selected {
          color: $color-secondary-50 !important;
          background-color: rgba($color-secondary-50, 0.15) !important;

          &:hover {
            color: $color-secondary-50 !important;
            background-color: rgba($color-secondary-50, 0.20) !important;

            * {
              color: $color-secondary-50 !important;
            }
          }
        }
      }
      >.message:not(.ui) {
        color: $color-gray-400 !important;
      }
    }

    &.active,
    &.visible {
      border-color: $color-dark-400 !important;

      &:hover {
        border-color: $color-dark-400 !important;
      }
    }

    &.upward {
      &.active {
        .menu {
          border-top-color: $color-dark-400 !important;
        }
      }
    }

    &.find,
    &.search {
      > .menu {
        .search {
          .form-control {
            color: $color-gray-400;
            background: $color-dark-700;
          }
        }

        .scrolling {
          .item {
            border-top: 1px solid $color-gray-50;
          }
        }
      }
    }

    &:hover {
      border-color: $color-gray-25;
    }

    &.selection {
      .menu {
        border-color: $color-dark-400 !important;

        .item {
          border-top-color: $color-dark-400 !important;
        }

        &.visible {
          &:hover {
            border-color: $color-dark-400 !important;
          }
        }
      }
      &:hover {
        border-color: $color-dark-400 !important;
      }
    }

    // ui dropdown - style like bootstrap dropdown ---
    &.bs-dropdown {
      border-color: $color-dark-300 !important;

      .text {
        color: $color-black;
      }

      .dropdown.icon {
        left: 12px;

        &:before {
          color: $color-dark-100;
        }
      }

      &.multiple {
        .ui.label {
          border-color: $color-dark-300 !important;
          color: $color-dark-200 !important;

          .delete.icon {
            background: $color-danger;

            &:after {
              color: $color-white;
            }
          }
        }
      }

      &:hover {
        border-color: $color-dark-300 !important;
      }
    }
  }

  // for filter bar
  &.filter {
    &.calendar {
      .ui {
        &.popup {
          &.calendar {
            width: 100%;
            right: 0 !important;
          }
        }
      }
    }
  }

  &.table {
    color: $color-gray-400;
    background-color: $color-dark-800;
  }
}

.input-group-addon:last-child {
  color: $color-gray-300;
  border-color: $color-dark-300;
}

.iti-tel-input-pro .iti__selected-flag {
  border-right-color: $color-dark-400;
}

.iti__country-list {
  background-color: $color-dark-400;
}

.iti-tel-input-pro .iti__country-list {
  border-color: $color-dark-300;
}

.iti-tel-input-pro .iti__country-list li:hover,
.iti-tel-input-pro .iti__country-list li.highlighted {
  background-color: $color-dark-300;
}

.input-group .form-control:focus {
  border-color: $color-dark-400;
}

.option-section,
.category-section,
.field-section {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
  background: $color-dark-800 !important;
}

.lang_field_size_desc_null {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
  background: $color-dark-800 !important;
}