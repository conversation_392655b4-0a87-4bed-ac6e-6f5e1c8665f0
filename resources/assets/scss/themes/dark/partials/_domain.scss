.rec { // @osama - needs revisit ---
  &-domain {
    // show and edit default domain ---
    &--default {
      border-color: $color-dark-300;

      h1 {
        color: $color-secondary-50;
      }

      li {
        .btn {
          color: $color-dark-100;

          &.copied {
            color: $color-secondary-50;

            i {
              color: $color-secondary-50;
            }
          }
        }

        &:first-child {
          .btn {
            border-right: 1px solid $color-gray-200;
          }
        }
      }

      @include mediaMaxWidth($screen-tablet-l) {
        li {
          .btn {
            border: 1px solid $color-gray-200;
          }
        }
      }

      &.active {
        background-color: $color-dark-800;

        h1 {
          color: $color-gray-300;
        }
      }
    }
  }
}

// edit domain form ---
#form_edit_domain {
  border: 1px solid $color-gray-200;

  .form-group {

    h2 {
      color: $color-secondary-50;
    }

    .form-control {
      color: $color-dark-200;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    .form-group {
      border: 1px solid $color-gray-200;
    }
  }
}

.domain_link_wrapper {
  @include mediaMaxWidth(374px) {
    .form-control, .input-group-btn {
      button {
        border-right: 1px solid $color-gray-200 !important;
      }
    }
  }
}

.domain_search_field {
  border-right: 1px solid $color-gray-200;

  &:focus,
  &:active {
    border-color: $color-gray-300;
  }
}

#form_add_dns {
  .form-group {
    .form-control {
      border: 2px solid $color-gray-200;
    }

    .bootstrap-select {

      .dropdown-toggle {
        border: 2px solid $color-gray-200;
      }
    }
  }
}

#dns_values_list {
  td {
    color: $color-dark-50;
  }
}

.rec {
  &-list {
    &--domains {
      > li {
        .rec-checkbox {
          background: $color-dark-800;
          border-color: $color-dark-300;

          &.active {
            border-color: $color-secondary-d;
          }

          input {

            + label {
              color: $color-gray-300;

              > span {
                small {
                  border-color: $color-dark-300;

                  b {
                    color: $color-gray-200;
                  }
                }
              }
            }

            &:checked {
              + label {

                > span {
                  &:before {
                    border-color: $color-secondary-50;
                    color: $color-secondary-50;
                  }
                }
              }
            }
          }

          .domain-meta {
            li {
              border-bottom-color: $color-dark-300;

              span {
                &:first-child {
                  color: $color-gray-400;
                }

                &:last-child {
                  color: $color-gray-300;
                }
              }

              &.domain-meta-note {
                span {
                  color: $color-gray-300;
                }
              }
            }
          }
        }
      }
    }
  }
}

// edit domain form ---
#form_edit_domain {
  .form-group {
    h2 {
      color: $color-secondary-50;
    }

    .form-control {
      color: $color-dark-100;
    }
  }

  @include mediaMaxWidth($screen-phones) {
    .form-group {
      border-color: $color-dark-300;
    }
  }
}

.domain {
  &_link_wrapper {
    @include mediaMaxWidth($screen-mobile) {
      .form-control,
      .input-group-btn {
        button {
          border-right-color: $color-dark-300 !important;
        }
      }
    }
  }

  &_search_field {
    border-right-color: $color-dark-300;
    &:focus,
    &:active {
      border-color: $color-dark-300;
    }
  }

  &_input {
    border-right-color: $color-dark-300 !important;
  }
}

#form_add_dns {
  .form-group {
    .form-control {
      border-color: $color-dark-300;
    }

    .bootstrap-select {
      .dropdown-toggle {
        border-color: $color-dark-300;
      }
    }
  }
}

#dns_values_list {
  td {
    color: $color-gray-300;
  }
}

