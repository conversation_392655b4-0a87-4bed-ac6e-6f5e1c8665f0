#rec_order_status {
  .rec {
    &-order-status {
      .btn {
        &.icon_picker {
          border-color: $color-dark-400;
        }
        &--add {
          border-color: $color-dark-300 !important;
        }
      }

      // color pickr ---
      .pickr {
        .pcr-button {
          border-color: $color-dark-400 !important;
        }
      }

      &__parent {
        &:before {
          background: $color-dark-300;
        }
      }

      &__fields {
        border-color: $color-dark-400;

        .status-label {
          color: $color-gray-300;

          &:before {
            color: $color-dark-100;
            background: $color-dark-500;
          }
        }

        .fields-cont {
          .form-group {

            .form-control {
              border-color: $color-dark-400;
            }
          }
        }

        &:hover {
          border-color: $color-dark-400;

          .status-label {
            &:before {
              background-color: $color-dark-400;
            }
          }
        }
      }


      &.mode {
        &--edit {
          > .rec-order-status__parent {
            .rec-order-status__fields {
              border-color: $color-dark-400;
            }
          }
        }
      }

      &--default {


        > .rec-order-status__parent {
          > .btn {
            &.icon_picker {
              border-color: $color-dark-400;

              &:hover {
                border-color: $color-gray-300;
              }
            }
          }
        }

        &:before {
          background: $color-dark-300;
        }
      }

      @include mediaMaxWidth($screen-phones) {
        &--default {
          border-color: $color-dark-400;

          &.mode--edit {
            border-color: $color-dark-400;
          }

        }
      }

      .m-short-cuts {
        h6 {
          color: $color-dark-100;
        }
      }
    }
  }

  .icon_picker {
    background-color: $color-dark-400;
  }
}