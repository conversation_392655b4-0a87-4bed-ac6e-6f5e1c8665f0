.credit-card-entry {
  input[type=checkbox],
  input[type=radio] {
    + label {
      border-color: $color-dark-400;


      &:hover {
        border-color: $color-dark-300;
      }
    }

    &:checked {
      + label {
        border-color: $color-secondary-50;

        span {
          small {
            color: $color-secondary-50;
          }
        }
      }
    }
  }

  &--new {

    input[type=checkbox],
    input[type=radio] {
      + label {
        border-color: $color-dark-200;

        &:before {
          color: inherit;
        }

        &:hover {
          border-color: $color-dark-200;
        }
      }

      &:checked {
        + label {
          border-color: $color-secondary-50;
          color: $color-secondary-50;
        }
      }
    }
  }

  &--new-tiffany {

    input[type=checkbox],
    input[type=radio] {
      + label {
        border-color: $color-secondary-50;
        background: transparent;
        color: $color-secondary-50;

        &:before {
          color: $color-secondary-50;
        }
      }

      &:checked {
        + label {
          border-color: $color-secondary-50;
          color: $color-secondary-50;
          background: transparent;

          &:before {
            color: $color-secondary-50;
          }
        }
      }
    }
  }
}