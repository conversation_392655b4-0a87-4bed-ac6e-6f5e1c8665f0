#salla-table-wrapper {
  .header-wrapper {
    background-color: $color-dark-600;
    .header-content {
      color: $color-gray-300;
    }
  }
}

.rgRow:nth-child(2n) .rgCell,
revo-grid[theme=compact] revogr-header .rgHeaderCell.sortable:hover
{
  background-color: $color-dark-600;
}

.rgHeaderCell,
revogr-data .rgCell, revogr-header .rgHeaderCell {
  border-color: $color-dark-400;
}

revo-grid[theme=compact] revogr-data .rgCell {
  color: $color-gray-300;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar-thumb,
revogr-scroll-virtual.horizontal::-webkit-scrollbar-thumb {
  border-radius: 0;
  background: $color-dark-100;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar-track,
revogr-scroll-virtual.horizontal::-webkit-scrollbar-track {
  background: $color-dark-700;
  border-radius: 0;
}

revogr-scroll-virtual.vertical::-webkit-scrollbar-thumb:window-inactive,
revogr-scroll-virtual.horizontal::-webkit-scrollbar-thumb:window-inactive {
  background: $color-dark-300;
}