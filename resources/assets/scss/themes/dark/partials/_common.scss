img[src*="https://cdn.salla.network/images/logo/logo"] {
  filter: invert(90%) sepia(70%) saturate(190%) hue-rotate(125deg) brightness(100%) contrast(70%);
}

.dark-toggle {
  &.btn--outlined.primary {
    color: $color-primary-l !important;
    background-color: $color-secondary-50 !important;

    * {
      color: inherit !important;
    }

    &:hover {
      color: $color-primary-l !important;

      * {
        color: inherit !important;
      }
    }
  }
}

.navbar-item {
  &.dark-toggle-wrapper {
    .dark-toggle {
      color: $color-secondary-50 !important;

      * {
        color: inherit !important;
      }
    }
  }
}

a {
  color: $color-secondary-50;
}

.products-control {
  > * {
    a {
      color: $color-gray-200;

      * {
        color: $color-gray-200;
      }
    }

    &.open,
    &:hover {
      > a {
        color: $color-gray-200;
      }
    }
  }
}

.bg-white {
  color: unset;
  background-color: $color-dark-700;
}


#navbar_box.navbar {
  background: $color-dark-800;

  .navbar-collapse {
    background: $color-dark-800 !important;
  }
}

// sidebar ---
.sidebar,
.navbar-header {
  background: $color-dark-800;
}

.navbar-nav > li > a {
  color: $color-gray-300 !important;
}

.navigation {
  .navigation-header {
    color: $color-secondary-50;
  }

  li {
    > a {
      color: $color-gray-300;

      &:hover {
        color: $color-gray-300;
        background-color: $color-dark-600;
      }
    }

    &.active {
      > a {
        background-color: $color-dark-600;
      }
    }
  }
}

.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-nav .open .dropdown-menu {
  background-color: $color-dark-400 !important;
}

.sidebar-user .store-link .dropdown-link:not(:last-child),
.media-list-bordered > li {
  border-color: $color-dark-300 !important;
}

.order-customer-name {
  color: $color-gray-300;
}

.text-price {
  color: $color-secondary-50 !important;
}

.notifications-list .media-body {
  color: $color-gray-300;
}

#page_header_box {
  .nav-breadcrumb {
    ol {
      li {
        a {
          color: $color-secondary-50;
        }

        &:before {
          color: $color-gray-300;
        }
      }
    }
  }
}

.rec-placeholder {
  h2 {
    color: $color-gray-300;
  }

  &__icon {
    i {
      color: $color-dark-300;
    }

    svg path {
      fill: $color-dark-300;
    }
  }
}

.btns-row .nav > li > a {
  color: $color-gray-300;
}

.order-st-entry {
  background-color: $color-dark-600;
  border-color: $color-dark-400;

  h5 {
    color: $color-white;

    span {
      color: $color-gray-300;
    }
  }

  &.active {
    background-color: $color-dark-800;
  }
}

.checker {
  span {
    border-color: rgba($color-secondary-50, 0.5);

    &:after {
      color: $color-secondary-50;
    }
  }
}

.payment-url {
  background-color: $color-dark-400;

  &--light {
    border-color: $color-dark-200;

    button {
      color: $color-gray-300;
      background-color: $color-dark-600;
      border-right-color: $color-dark-200;
    }
  }
}

.border-bottom {
  border-color: $color-dark-400 !important;
}

.empty-box {
  .empty-icon {
    color: $color-dark-300 !important;
  }
}

.store-setup-item,
.store-brands-listing,
.setting-box {
  border-color: $color-dark-400;
  background-color: $color-dark-700;
}

.store-setup-item {

  i {
    color: $color-secondary-50;
  }

  .store-setup-title {
    color: rgba($color-secondary-50, 0.65);
  }

  .store-setup-desc {
    color: $color-dark-200;
  }
}


.ui.calendar {
  .form-control {
    background: $color-dark-700;
  }
}

.client-avatar {
  border-color: $color-dark-200;
}


.nav-tabs-light {
  li {
    &:not(.active) {
      a {
        background: $color-dark-400;

        &:hover {
          color: $color-secondary-50;
        }
      }
    }
  }
}

.nav-tabs {
  &.nav-tabs-solid {
    background: transparent;

    > li {
      &:not(.active) {
        > a {
          color: $color-secondary-50;

          &:hover {
            color: $color-secondary-50;
            background: transparent;
          }
        }
      }
    }
  }
}

.rec-btn-group {
  .dropdown-menu {
    border-color: $color-dark-300;
  }
}

.order-product-group-wrapper {
  &:before {
    background: $color-dark-400;
  }
}

.border-gray-200 {
  border-color: $color-dark-400 !important;
}

.bg-gray-50, .bg-gray {
  background: $color-dark-600 !important;
}

.seo-preview {
  .title,
  .link {
    color: #8ab4f8; // coming from google dark mode
  }

  .description {
    color: $color-gray-300;
  }
}

.ql-toolbar {
  &.ql-snow {
    background: $color-dark-700;
    border-color: $color-dark-300 !important;

    .ql-fill {
      fill: $color-gray-400;
      stroke: none;
    }

    .ql-stroke {
      fill: none;
      stroke: $color-gray-400;
    }
  }
}

.bg-tiffany {
  color: $color-primary-l;
  background-color: rgba($color-secondary-50, 0.80);
  border-color: rgba($color-secondary-50, 0.80);
}

.text-primary {
  color: $color-secondary-50 !important;
}

.ql-container {
  &.ql-snow {
    border-color: $color-dark-300 !important;
  }
}

.switchery {
  background-color: $color-gray-400 !important;
}

hr {
  border-color: $color-dark-300;
}

.marketing-abandoned-carts {

  .content-divider {
    > span {
      background-color: $color-dark-700;

      &:before {
        background-color: $color-dark-400;
      }
    }
  }

  .span_discount_type {
    border: 1px solid $color-dark-400;
  }
}

.branches-products {
  border-color: $color-dark-400;

  .product-info a {
    border-color: $color-dark-400 !important;
  }
}

.popover {
  background-color: $color-dark-600;
  border-color: $color-dark-300;

  .popover-title {
    color: $color-dark-100 !important;
  }

  .arrow {
    border-top-color: $color-dark-600;
  }

  &.bottom {
    > .arrow {
      &:after {
        border-bottom-color: $color-dark-300
      }
    }
  }
}


.filepond--panel-root {
  background-color: $color-dark-400;
}

.filepond--drop-label * {
  color:$color-dark-100;
}

.text-dark-300,
.current-balance__item,
.text-default,
.rec-price-wrapper--giant b {
  color: $color-gray-300 !important;
}

.text-dark-200 {
  color: $color-gray-400 !important;
}

.tab-content-bg {
  border-color: $color-dark-400;
  background-color: transparent;
}

.text-muted,
.text-muted2,
.help-block {
  color: $color-dark-100 !important;
}

.add-theme-area {
  .header-info {
    h2 {
      color: $color-gray-200;
    }
  }
}

.shipping_details_option, .cod_option {
  background-color: $color-dark-600;
}

.rec {
  &-price-wrapper {

    .product-price {
      color: $color-secondary-50;
    }

    b {
      color: $color-gray-400;
    }

    small {
      color: $color-gray-400;
    }

    &--giant {
      b {
        color: $color-gray-300;
      }
    }
  }
}

#modal_quantity_management {
  .option-section {
    background-color: $color-dark-500 !important;
    border-color: $color-dark-400 !important;
  }
}

.heading-label {
  background-color: $color-dark-300 !important;
}

.collapse-title {
  .line {
    border-color: $color-dark-300 !important;
  }
}

.color-picker-wrapper {
  border-color: $color-dark-300;

  input[type=color] {
    background: $color-dark-600;
  }
}

#pickup-branches {
  .branch-name.open:after,
  .sort.default a {
    color: $color-secondary-50;
  }

  .sort.default a:before {
    color: $color-primary-l;
    background-color: $color-secondary-50;
  }
}

.search-placeholder > span {
  background-color: $color-dark-800;
}

.right-border {
  border-right-color: $color-dark-300 !important;
}

#page_header_box .nav-breadcrumb ol li a i {
  color: $color-secondary-50;
}

.product-header {
  background-color: $color-dark-600 !important;
  border: none;
}

.ql-editor.ql-blank::before {
  color: $color-dark-100;
}

.card {
  &--flyer {
    background: $color-dark-500;
    border-color: $color-dark-400;

    .card__header {
      background: $color-dark-400;
      border-color: $color-dark-300;
    }

    @include mediaMaxWidth($screen-phablet) {
      .card__header {
        border-bottom: 1px solid $color-dark-300;
      }
    }
  }
}

.swal2-popup {
  background-color: $color-dark-700 !important;

  .swal2-content {
    color: $color-gray-400 !important;
  }

  .swal2-title {
    color: $color-secondary-50 !important;
  }
}

.swal2-success-fix,
.swal2-success-circular-line-right,
.swal2-success-circular-line-left {
  background-color: transparent !important;
}

.padded-block {
  &.bg {
    background: $color-dark-800;
  }
}

.section-title {
  color: $color-gray-300;
  background: $color-dark-500;
}

.bordered {
  border-color: $color-dark-400 !important;

  &-gray-200 {
    border-color: $color-dark-400 !important;
  }
}

#search-box {
  .search-results {
    background-color: $color-dark-600 !important;

    ul {
      &::-webkit-scrollbar-track {
        background: $color-dark-600;
      }

      &::-webkit-scrollbar-thumb {
        background-color: $color-dark-200;
      }

      &::-webkit-scrollbar {
        width: 4px;
      }
    }

    &__item {
      a {
        transition: 0.3s;

        &:hover {
          background-color: $color-dark-500 !important;
        }
      }
    }
  }
}

.rec-ls-field {
  .rec-ls-list {
    background-color: $color-dark-400;

    li {
      input {
        + label {
          border-bottom-color: $color-dark-300;

          &:hover {
            background-color: $color-dark-600;

            span {
              color: $color-dark-100;
            }
          }
        }

        &:checked {
          + label {
            &:hover {
              background-color: $color-secondary-50;

              span {
                color: $color-primary-l;
              }
            }
          }
        }
      }
    }
  }
}

.order-permission {
  border-color: $color-dark-400;
  background-color: $color-dark-600;
}

#create_template_product {
  .card {
    border-color: $color-dark-400;

    &.active {
      border-color: rgba($color-secondary-50, 0.25);

      .card__header i,
      .card__content h6, {
        color: $color-secondary-50 !important;
      }

      .card__content h6 {
        &:before {
          border-color: $color-secondary-50;
        }

        &:after {
          color: $color-secondary-50;
        }
      }
    }
  }
}

.three-dots-dropdown .dropdown-menu li:not(:last-child) {
  border-bottom-color: $color-dark-400;
}

.chart_block text {
  color: $color-gray-300 !important;
  fill: $color-gray-300 !important;
}

pre {
  border-color: $color-dark-400;
  background-color: $color-dark-600;
}

.navbar-brand .dark-logo {
  background: url("https://cdn.salla.network/images/logo/logo-wide.svg") 0 0 no-repeat;
}

* {
  ::-webkit-scrollbar-track {
    background: $color-dark-600;
  }

  ::-webkit-scrollbar-thumb {
    background: $color-dark-200;
  }

  ::-webkit-scrollbar-thumb:window-inactive {
    background: $color-dark-100;
  }
}

.working-days-hours {
  li {
    & > div.first-period > div.time-period {
      input {
        border: 1px solid $color-dark-400;

        &[disabled] {
          background-color: $color-dark-600;
        }
      }
    }
    .btn-add-period {
      color: $color-secondary-50;
    }
    &:not(:last-child) {
      border-bottom: 1px solid $color-dark-400;
    }
  }
}

.option-section {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
}

.weight-price-indicator {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
}

.vue-slider-rail {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
  background: $color-dark-800 !important;
}

.vue-slider-dot-handle {
  border-color: $color-dark-400 !important;
}

.customer-conditions-wrapper {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
  background: $color-dark-800 !important;
}

.media-list {
  background-color: $color-dark-500 !important;
  border-color: $color-dark-400 !important;
  background: $color-dark-800 !important;
}
