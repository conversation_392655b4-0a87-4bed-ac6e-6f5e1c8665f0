.rec {
  &-list {
    &--tags {
      li {
        background-color: $color-dark-400;

        &.placeholder {
          color: $color-dark-100;
        }

        &.input-wrapper {
          .form-control {
            border: 1px solid $color-dark-300 !important;
            color: $color-dark-100;

            + .btn.btn-add-tag {
              &.btn-tiffany {
                background-color: $color-dark-300;
                border-color: $color-dark-300;

                i {
                  color: $color-gray-300 !important;
                }
              }
            }

            &::placeholder {
              color: $color-dark-100;
            }

            &:focus,
            &.touched {
              background: $color-dark-400;
              color: $color-dark-100;

              &::placeholder {
                color: $color-dark-100;
              }
            }
          }
        }
      }
    }
    &--bordered {
      > li {
        border-color: $color-dark-300;
      }

      &.light-border {
        > li {
          border-color: $color-dark-400;
        }
      }

      &.light-border {
        border-color: $color-dark-300;
      }
    }
    &--table {

      > div > li {
        border-bottom-color: $color-dark-300;

        &:not(:last-child) {
          border-bottom-color: $color-dark-300;
        }
      }

      li {
        &.table-head {
          color: $color-dark-100;
          background-color: $color-dark-800;
        }
      }
      &.invoice-list {

        li {


          &.table-footer {
            > * {
              border-bottom-color: $color-dark-300;
            }
          }
        }
      }

      &.subscriptions-list {
        > li {


          @include mediaMaxWidth($screen-phones) {
            border-bottom-color: $color-dark-300 !important;

            span {
              &:first-child {
                border-bottom-color: $color-dark-300;
              }
            }
          }
        }
      }

      &-style {
        li {
          border-bottom-color: $color-dark-300;

          > div {
            @include mediaMaxWidth($screen-phones) {
              border-bottom-color: $color-dark-300 !important;
            }
          }
        }
      }
    }
  }
  &-order-assignee {
    background: $color-dark-400;
    border: 1px solid $color-dark-400;

    &__title {
      color: $color-dark-100;
    }

    &__assigned-users {
      > li {
        > span {
          background: $color-white;
          color: $color-dark-100;
        }

        > button {
          &:before {
            color: $color-white;
          }
        }

        &#assign_user {
          border: 1px solid $color-dark-300;
          background: $color-dark-400;

          &:after {
            color: $color-dark-100;
          }

          ul {
            border: 1px solid $color-dark-300;
          }
        }
      }
    }
  }
  &-dropdown {
    $dropdown-root: rec-dropdown;
    background: $color-dark-400;

    .#{$dropdown-root} {
      &__list {
        @include scrollBar(2px, $color-dark-200, $color-dark-100);

        .rec-dropdown__item {
          border-top: 1px solid $color-dark-300;

          &:hover {
            background-color: $color-dark-600;
          }

          &.selected {
            color: $color-secondary-50;
            background-color: rgba($color-secondary-50, 0.15);

            &:hover {
              color: $color-secondary-50;
              background-color: rgba($color-secondary-50, 0.20);

              * {
                color: $color-secondary-50;
              }
            }
          }
        }
      }
    }
  }
  &-client-contact {
    .direct-phone {
      span {
        color: $color-secondary-50;
        border-color: rgba($color-secondary, 0.5);
      }
    }

    .rec-contact-list {
      li {
        &:hover {
          a {
            color: $color-secondary;
          }
        }
      }
    }
  }
}