.rec {
  &-pagination {
    .page-item {
      a {
        &.page-link {
          &:hover {
            color: $color-secondary-50;
            background-color: rgba($color-secondary-50, 0.15);
            border-color: rgba($color-secondary-50, 0.15);
          }
        }
      }
    }

    .page-label {
      span {
        color: $color-dark-200;

        b {
          color: $color-secondary-50;
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
    }

    &--modified {
      a {
        color: $color-dark-100;
        border-color: $color-dark-300;

        &:hover {
          background-color: $color-dark-500;
        }

        &.disabled {
          color: $color-dark-400;
        }

        &.active {
          color: $color-secondary-50;

          &:hover {
            background-color: rgba($color-secondary-50, 0.1);
          }
        }
      }
    }
  }
}

.pagination {
  > li {
    > a {
      color: $color-gray-400;
      background-color: $color-dark-600;
      border-color: $color-dark-300;
    }
  }
  .disabled {
    > span {
      border-color: $color-dark-300;
    }
  }
}