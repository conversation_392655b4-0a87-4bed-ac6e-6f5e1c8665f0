.rec {
  &-filter-cont {
    background-color: $color-dark-700;

    .filter-head {
      background-color: $color-dark-700;
      span {
        color: $color-gray-300 !important;
        background-color: $color-dark-700;
      }
      &:before {
        background-color: $color-dark-300;
      }
    }

    &:before {
      background: linear-gradient(0deg, rgba(39, 38, 38, 0) 0%, rgba(39, 38, 38, 1) 100%);
    }

    &:after {
      background: linear-gradient(0deg, rgba(39, 38, 38, 1) 0%, rgba(39, 38, 38, 0) 100%);
    }
  }
  &-filter-wrapper {
    background-color: rgba($color-black, 0.65);
    .ui.dropdown .ui.label {
      background-color: $color-dark-600;
    }
  }
}
