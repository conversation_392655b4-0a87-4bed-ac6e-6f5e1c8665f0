.multiselect {
  border: 1px solid $color-dark-300;
  background-color: $color-dark-700;

  &__tag {
    background-color: rgba($color-secondary-50, 0.15);

    span {
      color: $color-secondary-50;
    }
  }

  &__content-wrapper {
    ul {
      background-color: $color-dark-400;

      li {
        > span {
          span {
            color: $color-gray-400
          }

          &.multiselect__option--selected {
            background-color: rgba($color-secondary-50, 0.10);

            span {
              color: $color-secondary-50;
            }

            &:hover {
              background-color: rgba($color-gray-100, 0.10);

              span {
                color: $color-secondary-50;
              }
            }

            &:after {
              color: $color-secondary-50;
            }
          }
        }

        &:hover {
          > span {
            color: $color-secondary-50;
            background-color: $color-dark-600;
          }
        }
      }
    }
  }

  &__select {
    &:before {
      color: $color-dark-100;
    }
  }

  &-category {
    ul li span {
      &.multiselect__option {
        span {

          &:before {
            background-color: $color-dark-300;
          }
        }
      }
    }
  }

  &-vue {

    .multiselect__content-wrapper {
      ul {

        li {
          .multiselect__option {
            color: $color-gray-400;

            &--selected {
              color: $color-primary-l;
              background-color: rgba($color-secondary-50, 0.10);

              &:hover {
                color: $color-primary-l !important;
                background-color: rgba($color-secondary-50, 0.10) !important;
              }
            }

            &:hover {
              background-color: $color-dark-600 !important;
            }
          }
        }
      }
    }
  }
}
