.tabs-main-app-builder .tabs-component-tabs {
  .tabs-component-tab {
    &:not(.is-active) {
      a {
        color: $color-gray-400;
        background-color: $color-dark-400;
      }
    }
  }
}

.home-page-placeholder {
  svg {
    g {
      fill: $color-white
    }
  }
}

.tabs-review {
  &__tabs {
    background-color: $color-dark-400;
    border-color: $color-dark-300;
  }

  &__tab {
    &:hover {
      a {
        color: $color-secondary-50;
        background-color: transparent;
      }
    }

    &.active {
      &:hover {
        a {
          color: $color-primary-l;
          background-color: $color-secondary-50;
        }
      }
    }
  }
}

.app-tabs-wrapper {
  border-color: $color-dark-300;
  background-color: $color-dark-800;

  .tab {
    border-color: $color-dark-300;
    background-color: $color-dark-600;

    .options .move-handler {
      border-right-color: $color-dark-300;
    }
  }

  .tab-options-inner {
    .tab-cont {
      &:before {
        background-color: $color-dark-300 !important;
      }
    }

    &:before {
      background-color: $color-dark-300 !important;
    }
  }
}

.tabs-main-interface > div:last-of-type.active-tab {
  border-color: $color-dark-300;
}

.tabs-main-interface .tabs-component-tabs li {
  a {
    color: $color-dark-100;
    background-color: transparent;
    border-color: $color-dark-700;
  }

  &.active {
    a {
      color: $color-gray-300;
      border-color: $color-dark-300;
      background-color: $color-dark-700;
    }
  }
}

.storeapp-panel #app-logo .img-placeholder {
  border-color: $color-dark-400;
  background-color: $color-dark-500;
}

.add-new-tab {
  > button {
    color: $color-secondary-50;
    border-color: $color-secondary-50;
  }

  > a {
    &:hover {
      color: $color-secondary-50;
    }
  }

  .dropdown-menu {
    box-shadow: none;

    li {
      border-bottom-color: $color-dark-400;
      a {
        &:hover {
          background-color: transparent !important;
        }
      }

      &:hover {
        a {
          color: $color-primary-l !important;
          background-color: $color-secondary-50 !important;
        }
      }
    }
    &:after {
      border-color: transparent transparent transparent $color-dark-400;
    }
  }

  &.open {
    > a {
      color: $color-secondary-50;
    }
  }
}

.storeapp-panel .section-wrapper > * {
  border-color: $color-dark-200;
}

.color-picker-wrapper {
  border-color: $color-dark-300;
  background-color: $color-dark-400;
  span {
    color: $color-gray-300;
  }
}

.tabs-design-preview li:not(.active) {
  border-color: $color-dark-400;
}


