.daterangepicker {
  &.rec-daterangepicker {
    .ranges {
      border: 1px solid $color-dark-300;
      background: $color-dark-400;

      ul {
        li {
          color: $color-gray-400;
          border-bottom: 1px solid $color-dark-300;

          &:before {
            background-color: $color-dark-300;
          }

          &.active {
            color: $color-secondary-50 !important;
            background-color: rgba($color-secondary-50, 0.15) !important;

            &:before {
              background: $color-secondary-50;
            }

            &:hover {
              color: $color-secondary-50 !important;
              background-color: rgba($color-secondary-50, 0.15) !important;
            }
          }


          &:hover {
            color: $color-gray-400;
            background-color: $color-dark-600;
          }
        }
      }
    }

    .calendar {
      .daterangepicker_input {
        .form-control {
          border: 1px solid $color-dark-300;
        }
      }

      .calendar-table {
        tbody {
          tr {
            td {
              &.active,
              .start-date,
              &.end-date {
                color: $color-primary-l !important;
              }

              &.in-range {
                color: $color-secondary-50;
              }
            }
          }
        }
      }
    }

    &.show-calendar {

      @include mediaMaxWidth(576px) {
        .ranges {
          border-left: 1px solid $color-gray-200;
        }

        .calendar {
          &.right {
            border: 1px solid $color-gray-200;
          }

          &.left {
            border: 1px solid $color-gray-200;
          }
        }
      }
    }

    &.show-calendar {
      background: $color-dark-400;
      .calendar {
        background: $color-dark-400;
      }
    }

    &:after {
      border-color: transparent transparent $color-dark-400 transparent;
    }
  }

  &--with-dropdown {
    table {
      thead {
        .month {

          &:before,
          &:after {
            color: $color-dark-100;
          }

          select {
            border-color: $color-gray-200;
          }
        }
      }
    }
  }
}

.mx-datepicker {
  .mx-input-wrapper {
    .mx-icon-calendar {
      &:before {
        color: $color-dark-200;
      }
    }
  }
  .mx-datepicker-main {
    background: $color-dark-400;
    border-color: $color-dark-300 !important;
    .mx-calendar-header {
      .mx-btn {
        color: $color-dark-100 !important;
      }
    }
    .mx-table {
      color: $color-dark-100;
      .cell {
        &.today {
          color: $color-primary-l;
          background-color: $color-secondary-50;
        }
        &.disabled {
          color: $color-dark-200;
          background-color: $color-dark-400;
        }
        &:hover {
          color: $color-secondary-50 !important;
          background-color: rgba($color-secondary-50, 0.15) !important;
        }
      }
    }
    .mx-time {
      background: $color-dark-400;
      border-color: $color-dark-300 !important;
    }
    .mx-time-header {
      border-bottom-color: $color-dark-300;
      .mx-btn {
        color: $color-dark-100 !important;
      }
    }
    .mx-time-column {
      .mx-time-item {
        color: $color-dark-100 !important;
        &:hover {
          color: $color-secondary-50 !important;
          background-color: rgba($color-secondary-50, 0.15) !important;
        }
        &.active {
          color: $color-secondary-50 !important;
        }
      }
    }
  }
}
