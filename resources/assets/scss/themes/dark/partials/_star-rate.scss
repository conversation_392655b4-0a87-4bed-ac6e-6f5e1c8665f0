.rating-stars {
  .star-icon {
    &:not(.is-selected) {
      color: $color-dark-200;
    }
  }
  &__wrap {
    background-color: $color-dark-400;
  }
}

.rating-block {
  .progress-bg {
    background-color: $color-dark-400;
  }
}

.rec {
  &-star-rate {
    i {
      &:not(.active) {
        color: $color-dark-200 !important;
      }
    }

    span {
      color: $color-dark-200;
    }
  }
}

.rating-area {
  .icon {
    color: $color-dark-200;
  }
}

.comments-list {
  .border-b:not(:last-child) {
    border-bottom-color: $color-dark-400;
  }
}

.theme-replay-item {
  border-color: $color-dark-400;
  background-color: $color-dark-600;
}