.panel-editor {

  .panel-heading {
    border-color: $color-dark-400;
    background: $color-dark-600;

    &__search {
      .icon {
        color: $color-dark-100;
      }

      input {
        border-color: $color-dark-300;
        background-color: $color-dark-700;

        &:focus {
          border-color: $color-dark-300;

        }
      }
    }

    &__options {
      @media (min-width: $screen-laptop) {
        &:before {
          background-color: $color-dark-300;
        }
      }
    }
  }
}


.editor-block {
  border-color: $color-dark-400;
  background: $color-dark-700;

  div:not(:last-child) > &,
  &.even-has-bg .editor-item:not(:last-child) {
    border-bottom-color: $color-dark-400 !important;
  }

  .odd &, &.even-has-bg .editor-item:nth-child(even) {
    background-color: $color-dark-500;
  }

  input.form-control,
  textarea.form-control {
    border-right-color: $color-dark-300;

    &:focus {
      border-color: $color-dark-300;
    }
  }

  &__crumbes {
    color: $color-dark-100;

    .sep {
      color: $color-dark-100;
    }
  }

  &__product-header {

    .img-wrap {
      background-color: $color-dark-600;
    }
  }

  .editor-item {
    &__status {
      color: $color-gray-400;

      &.complete {
        color: $color-secondary-50;
      }
    }

    @media (max-width: $screen-tablet-l) {
      &:before {
        background: $color-gray-200;
      }
    }
  }

  &.theme-block {
    h5 {
      color: $color-dark-200;
    }
  }

  &__child {
    &-item {
      border-color: $color-dark-300;
      background-color: $color-dark-700;
    }
  }
}

.editor-placeholder {
  .icon {
    color: $color-gray-200;
  }

  p {
    color: $color-gray-400;
  }
}

.panel-log {
  .ui.table {
    border-color: $color-gray-200;


    > thead {
      > tr > th {
        .icon {
          color: $color-gray-300;
        }

        &.sortable {
          color: $color-secondary-50;

          &:hover {
            color: $color-secondary-50;
          }
        }
      }
    }
  }

  #log-table {
    tr {
      &:nth-child(even) {
        background-color: $color-dark-500;
      }

      &:hover {
        background-color: $color-dark-500;
      }
    }
  }

  .loader-wrapper {
    background-color: rgba($color-dark-500, 0.55);
  }
}

.pagination-log-wrap {
  .rec-pagination {
    background-color: $color-dark-500;
  }
}

.general-report {

  .success-report {
    i {
      border-color: rgba(93, 213, 196, .2);
      background-color: rgba(93, 213, 196, .05);
      color: $color-primary
    }

    h6 {
      color: $color-primary;
    }
  }
}


.campaign-statistics {
  > div {
    .purchase-stats {
      li {
        &.views {
          span {
            &:before {
              background-color: $color-secondary-50;
            }
          }
        }
      }
    }
  }
}

.default-languages {

  &_title {
    p {
      color: $color-dark-100;
    }
  }
}

.languages-table {

  tr {
    &:nth-child(even) {
      background-color: $color-dark-500;
    }

    .badge {
      background-color: $color-dark-100;
    }
  }

  .language-name__default {
    color: $color-dark-100;
  }
}


.add-new-lang {
  background-color: $color-dark-600;
  border-top-color: $color-dark-400;
}