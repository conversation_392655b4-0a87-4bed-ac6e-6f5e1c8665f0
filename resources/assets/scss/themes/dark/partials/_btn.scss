.btn {
  &-default {
    color: $color-gray-400;
    border-color: $color-dark-300;
    background-color: $color-dark-400;

    &.btn-tiffany {
      color: $color-secondary-50 !important;
    }

    &:focus {
      background-color: $color-dark-400;
    }
    &:hover {
      border-color: $color-dark-300 !important;
      background-color: $color-dark-400 !important;
    }
  }

  &-filter-reset {
    color: $color-gray-400;
    border-color: $color-dark-400;
    background-color: $color-dark-300;
  }

  &-print {
    &.btn-link {
      color: $color-gray-300;
      background-color: $color-dark-600;
    }
  }

  &-link {
    color: $color-secondary-50;
  }

  &-success {
    color: $color-secondary-50 !important;
  }

  &-add-option-group {
    color: $color-gray-300 !important;
    border-color: $color-dark-300;
    background-color: $color-dark-400;

    i {
      color: $color-gray-300;
    }
  }

  &--more-nav {
    background: transparent;

    .dot,
    .dot:before,
    .dot:after {
      background-color: $color-gray-300;
    }

    .more-options {
      background-color: $color-dark-400;

      li {
        a {
          &.text-default {
            color: $color-dark-100 !important;
          }

          &:hover {
            background-color: $color-dark-600;
          }
        }
      }
    }

    &.active {
      background-color: $color-dark-500;

      &:hover,
      &:focus,
      &:active {
        background-color: $color-dark-500;
      }
    }
  }

  &--outlined {
    color: $color-gray-300;
    border-color: $color-dark-200;
    &.primary {
      color: $color-secondary-50 !important;
      border-color: rgba($color-secondary-50, 0.25) !important;
      * {
        color: var(--color-secondary-50) !important;
      }
      &:hover {
        color: var(--color-secondary-50) !important;
        border-color: rgba($color-secondary-50, 0.50) !important;
        * {
          color: var(--color-secondary-50) !important;
        }
      }
    }
  }
}

.rec-btn {
  &--trans {
    &:hover {
      background-color: transparent !important;
    }

    &:focus {
      color: $color-gray-400 !important;
    }
  }
}