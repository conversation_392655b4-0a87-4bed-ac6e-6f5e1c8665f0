.vue-treeselect {
  $vue-treeselect-root: &;

  &__control {
    background-color: $color-dark-700 !important;
    border-color: $color-dark-300 !important;

    &:hover {
      border-color: $color-dark-300 !important;
    }
  }

  &__menu {
    background-color: $color-dark-400;
    border-color: $color-dark-300 !important;
  }

  &__label {
    color: $color-gray-300;
  }

  &__single-value {
    color: $color-dark-100;
  }

  &__label-container {
    border-bottom-color: $color-dark-400;
  }

  &__option {
    &--highlight {
      background-color: $color-dark-600;
    }

    &--selected {
      background-color: rgba($color-secondary-50, 0.15);

      .vue-treeselect__label {
        color: $color-secondary-50 !important;
      }

      &:hover {
        background-color: rgba($color-secondary-50, 0.20);
      }
    }
  }

  &__multi-value-item {
    background-color: rgba($color-secondary-50, 0.15);
  }

  &__multi-value-label {
    color: rgba($color-secondary-50, 0.8) !important;
  }

  &__value-remove {
    border-right-color: #485552;

    svg {
      path {
        fill: rgba($color-secondary-50, 0.85);
      }
    }
  }

  &--without-effect {
  }

  &--custom {
  }

  &--single-select-tag{
    .vue-treeselect__single-value{
      background: rgba($color-secondary-50, 0.15);
      color: rgba($color-secondary-50, 0.8) !important;
      
      .clear{
        border-right-color: #485552 !important;
        img{
          filter: invert(93%) sepia(42%) saturate(70%) hue-rotate(135deg) brightness(115%) contrast(90%);
        }
      }
    }
  }
}

.vue-treeselect--without-effect:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) .vue-treeselect__control:hover {
  border-color: $color-dark-300 !important;
}