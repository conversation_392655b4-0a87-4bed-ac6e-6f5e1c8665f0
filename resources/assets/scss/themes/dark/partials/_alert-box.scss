$color-success: #86b3a2;
$color-info: #86aadb;
$color-warning: #dcc4a5;
$color-danger: #e6aaac;

.alert-box {
  article {
    &.with-separator {
      border-right-color: $color-dark-300;
    }
  }

  &--linked {
    &.without-icon {
      button {
        color: $color-secondary-50 !important;
      }
    }
  }

  &--danger {
    * {
      color: $color-danger !important;
    }
  }

  &--success {
    * {
      color: $color-success !important;
    }
  }

  &--warning {
    * {
      color: $color-warning !important;
    }
  }

  &--mahally {
    * {
      color: $color-mahally !important;
    }
  }

  &--info {
    * {
      color: $color-info !important;
    }
  }

  &--white {
    border-color: $color-dark-400;
    background-color: $color-dark-700;

    h6 {
      color: $color-gray-300;
    }

    i {
      &:before {
        color: $color-gray-300;
      }
    }

    p {
      color: $color-gray-300;
    }

    &.uncompleted {
      i {
        background-color: $color-danger;
      }

      h6, h6 span {
        color: $color-danger;
      }
    }

    &.pending {
      i {
        background-color: $color-warning;
      }

      h6 span {
        color: $color-warning;
      }
    }

    &.completed {

      border-color: $color-dark-300 !important;

      i {
        background-color: $color-secondary-50;

        &:before {
          color: $color-primary-l;
        }
      }

      h6, h6 span {
        color: $color-secondary-50;
      }
    }
  }

  &--critical {
    background-color: $color-danger;
    color: $color-white !important;

    * {
      color: $color-danger !important;
    }
  }

  &--bar {
    background: #3e3529 !important;
  }

  &--transparent {

    i {
      color: $color-gray-400;
    }

    article {
      p {
        color: $color-gray-300;
      }
    }
  }
}

.alert-flat-box {
  color: $color-primary;
  background-color: $color-secondary-50;

  &__icon-wrap {
    .icon {
      color: $color-primary;
    }
  }

  &__btn {
    color: $color-dark-300;
    background-color: $color-white;

    .icon {
      border-right: 1px solid $color-gray-300;
    }

    &:hover {
      color: $color-primary;
    }
  }

  &--primary {
    color: $color-white;
    background-color: $color-primary;
  }

  &--secondary {
    color: $color-white;
    background-color: $color-primary-l;
  }
}

a.alert-box--white {
  &:after {
    color: $color-gray-400;
  }
}

.alert-paid {
  background-color: $color-dark-400;

  &:after {
    color: $color-dark-100 !important;
  }
}
