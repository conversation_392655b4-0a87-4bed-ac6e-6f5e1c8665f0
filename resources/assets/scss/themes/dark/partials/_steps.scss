.rec-step {
  &--wizard {
    &:not(.active) {
      a {
        color: $color-secondary-50;
        background-color: $color-dark-400;

        &:before {
          color: $color-secondary-50;
        }
      }
    }
  }

  &--labels {
    .inner-steps {
      li {
        a {
          background-color: transparent;
          color: $color-dark-300;
          &::before, &::after {
            background: $color-dark-300;
          }
        }
        &.complete {
          a {
            color: $color-gray-400;
          }
        }
      }
    }
  }

  &--linear {
    > a {
      &:before {
        color: $color-gray-400;
      }
      &:after {
        background-color: $color-dark-650;
      }
      span {
        color: $color-secondary-50;
      }
    }
    &.active {
      > a {
        &:before {
          color: $color-primary;
        }
      }
    }
  }
}
