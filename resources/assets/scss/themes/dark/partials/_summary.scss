.summary-welcome-block,
.summary-welcome-block .leftside,
.summary-steps .summary-step,
.summary-steps .summary-step__actions {
  border-color: $color-dark-400;
}

.summary-welcome-block {
  .rightside {
    h2 {
      color: $color-secondary-50;
    }
  }

  .progress-bar {
    background-color: $color-dark-400;
  }
}

.summary-steps {
  .summary-step {

    &__title {
      .icon {
        background-color: rgba($color-secondary-d, 0.1);
        color: rgba($color-secondary-50, 0.75);
      }

      h5 {
        color: $color-dark-100;
      }

      h2 {
        color: $color-secondary-50;
      }
    }

    &__actions {
      .tooltip-toggle.btn * {
        color: $color-primary-l !important;
      }
    }

    &.uncomplete {
      .summary-step__title {
        h2 {
          color: $color-gray-400;
        }
      }
    }
  }
}

.progress-circle {
  &:after {
    background-color: $color-dark-650;
  }
}

