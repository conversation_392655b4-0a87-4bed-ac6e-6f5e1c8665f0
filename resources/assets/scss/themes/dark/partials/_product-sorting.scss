.sort-select.btn-group > .dropdown-menu .bs-searchbox input.form-control {
  background-color: $color-dark-600;
}

.sort-product-entry {
  border-color: $color-dark-400;
  background-color: $color-dark-600;

  .sort-title {
    color: $color-gray-300;
  }

  .sort-content {
    .sort-meta {
      li {
        &.available {
          color: $color-secondary-50;
          background-color: rgba($color-secondary-50, 0.10);
        }
        &.unavailable {
          color: lighten($color-danger, 20%);
          background-color: rgba($color-danger, 0.10);
        }
      }
    }
  }
}

.page-indicator {
  .page {
    small {
      border-color: $color-dark-400;
      background-color: $color-dark-600;
    }

    &:after {
      background-color: $color-dark-400;
    }
  }
}