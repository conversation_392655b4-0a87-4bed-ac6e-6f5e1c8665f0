.loader {
  border-top-color: $color-secondary-50 !important;
  border-bottom-color: $color-dark-300 !important;
  border-left-color: $color-secondary-50 !important;
  border-right-color: $color-dark-300 !important;
}

.loader-wrapper {

  &--wide {
    background-color: rgba($color-black, 0.85);
  }

  &:before {
    color: $color-white;
  }

  &.success {
    &::before {
      background-color: $color-secondary-50;
    }
  }
}

// Infinit scroll spinner -----------
.infinite-message {
  color: $color-dark-300;

  .icon {
    color: $color-dark-300;
  }
}

.loading-spiral {
  border-color: $color-secondary-50 $color-secondary-50 rgb(245, 245, 245) rgb(245, 245, 245) !important;
}
