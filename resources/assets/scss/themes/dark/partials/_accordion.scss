.rec {
  &-accordion {
    .rec {
      &-acc__section {
        &__head {
          span {
            color: $color-gray-300;
          }
          &:after {
            background: $color-gray-400;
          }
        }

        &__content {
          .rec-acc-content-wrapper {
            p {
              color: $color-dark-100;
            }
          }
        }
      }
    }

    &.has-steps {

      .rec {
        &-acc__section {
          counter-increment: rec-accordion;

          &__head {
            span {
              &:before {
                border-color: $color-secondary-50;
                color: $color-secondary-50;
              }
            }

            &[aria-expanded="true"] {
              span {
                &:before {
                  color: $color-primary-l;
                  border-color: $color-secondary-50;
                  background-color: $color-secondary-50;
                }
              }
            }
          }
        }
      }
    }

    &__heading {
      background-color: $color-gray-25 !important;

      &:before {
        color: $color-dark-100;
      }

      h4 {
        color: $color-dark-100;
      }
      span {
        color: $color-dark-100;
      }

      &.with-icon {
        padding: 10px;

        h4 i {
          color: $color-dark-100;
        }
      }

      &[aria-expanded="true"] {
        background-color: $color-secondary-50 !important;

        h4, i, span {
          color: $color-primary-l !important;
        }
      }
    }

    &__collapse {
      &[aria-expanded="true"] {
        border-color: $color-secondary-50;
      }
    }

    &--enhanced {
      .rec {
        &-acc__section {
          border-color: $color-dark-400;
          background: $color-dark-100;

          &__head {

            .v-align {
              color: $color-dark-100;
              transform: translateY(1px);
            }

            &:after {
              color: $color-gray-400;
            }
          }

          &.active {
            background: $color-secondary-50;
            border-color: $color-secondary-50;

            .rec-acc__section__head {
              background: $color-secondary-50;

              * {
                color: $color-primary;
              }
            }
          }
        }
      }
    }
  }
}