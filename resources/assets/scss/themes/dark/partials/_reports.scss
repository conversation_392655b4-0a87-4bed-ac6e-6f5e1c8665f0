.rec {
  &-reports {

    &__date {
      background-color: $color-dark-400;
      border: 1px solid $color-dark-300;

      h2 {
        color: $color-gray-300;
        border-left: 1px solid $color-dark-300;
      }

      span {
        color: $color-gray-400;
      }

      &:after {
        color: $color-gray-400;
      }

    }

    &__options {
      background-color: $color-dark-700;
      border-color: $color-dark-400;
      color: $color-gray-400;


      .report-types {
        .report-type {
          border: 1px solid $color-dark-300;
          color: $color-gray-300;

          &.active,
          &:hover {
            color: $color-secondary-50;
            border-color: rgba($color-secondary-50, 0.5);
          }
        }

        .slick-arrow {
          &:before {
            color: $color-gray-300;
          }
        }

        .slick-dots {

          li {

            button {
              background-color: hsla(0, 0%, 84.7%, .45);

              &:hover {
                background-color: $color-gray-300;
              }
            }

            &.slick-active {
              button {
                background-color: $color-secondary-d;
              }
            }
          }
        }
      }
    }
  }
}