.panel {
  color: $color-gray-500;
  background-color: $color-dark-700;
  border-color: $color-dark-400;

  &-heading {
    .panel-title,
    .panel-title i {
      color: $color-gray-400;
    }
  }

  &-footer {
    background-color: $color-dark-700;
  }

  &-white,
  &-default,
  &-flat {
    .panel-heading, .panel-footer {
      color: $color-gray-400 !important;
      background-color: $color-dark-600 !important;
    }
  }

  .content-divider {
    > span {
      background-color: $color-dark-600;
      &:before {
        background-color: $color-dark-300;
      }
    }
  }

  &--bordered {
    border-color: $color-dark-400 !important;
  }
}

.stats-row {
  background-color: $color-dark-700;
}

.main-stats, .stats-title {
  border-color: $color-dark-400;
}

.stats-number {
  color: $color-secondary-50;
}

.stats-subtext {
  color: $color-gray-400;
}


#helpCenterPanel {
  background-color: $color-dark-750;

  .help__heading {
    .btn *{
      color: $color-primary-l !important;
    }
  }
}
