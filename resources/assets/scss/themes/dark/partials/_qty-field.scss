.qty-field {
  border-color: $color-dark-300;

  > .btn {
    &:after {
      background-color: $color-dark-300;
    }

    i {
      color: $color-dark-100;
    }

    &:hover,
    &:active {
      background-color: $color-gray-50 !important
    }
  }

  .dropdown-toggle {

    span {
      color: $color-dark-300
    }
  }

  .form-group {

    .form-control {
      color: $color-gray-400;

      &[disabled] {
        background-color: $color-white !important;
      }
    }
  }

  &--single {
    > .btn {
      background-color: rgba($color-secondary-50, 0.2) !important;

      i {
        color: $color-secondary-50
      }

      &:hover {
        background-color: rgba($color-secondary, 0.25) !important;
      }
    }
  }

  &--infinite {
    > .btn {
      background-color: rgba($color-dark-600, 0.1) !important;

      svg {
        fill: $color-secondary-50;
      }
    }
  }

  &--custom {
    .form-control {
      &:focus, &:active {
        border-color: $color-gray-200;
      }
    }

    .btn {
      &--qty {
        &-add,
        &-sub {
          background: $color-white;
          border: 1px solid $color-gray-200;

          &:hover,
          &:active,
          &:focus {
            border-color: $color-gray-200;
            color: $color-dark-300 !important;
          }
        }
      }

      &:before {
        background: $color-gray-200;
      }
    }

    &:hover {
      .form-control,
      .btn {
        border-color: darken($color-gray-200, 10%) !important;
      }
    }
  }
}