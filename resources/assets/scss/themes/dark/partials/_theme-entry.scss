.theme-entry {
  $theme-entry-root: &;
  border-color: $color-dark-300;
  background: $color-dark-600;

  &__thumb-wrapper {
    background-color: $color-dark-800;
  }

  &__title {
    i {
      color: $color-dark-300;
    }
  }

  &__info {
    > div {
      h2 {
        color: $color-gray-200;

        &:hover {
          color: $color-secondary-50;
        }
      }

      a {
        &:hover,
        &:focus {
          color: $color-secondary-50 !important;
        }
      }

      .product-price {
        color: $color-gray-300;

        * {
          color: $color-gray-300;
        }
      }

      .btn {
        &.active {
          border-color: $color-secondary-d;
          color: $color-white;
          background: $color-secondary-d;
        }
      }
    }

    #{$theme-entry-root} {
      &__tags {
        li {
          a {
            color: $color-gray-400;

            &:hover {
              color: $color-dark-100;
            }
          }

          &:after {
            color: $color-gray-400;
          }
        }
      }
    }

    .theme-title {
      color: $color-secondary-50;

      &:hover {
        color: $color-secondary-50;
      }
    }

    .tags {

      &__item {
        color: $color-dark-100;

        &:hover {
          color: $color-secondary;
        }
      }
    }

    .price {

      b {
        color: $color-gray-200;
      }

      span {
        color: $color-dark-100;
      }
    }
  }

  .actions-buttons {
    border-color: $color-dark-300;

    .btn--preview {
      color: $color-secondary-50;

      + .btn--preview {
        @include b-radius(0);
        border-right-color: $color-dark-300;
      }
    }

  }

  // purchased theme ---
  &--purchased {

    input[type=radio] {

      + label {
        border-color: $color-dark-300;

        .theme-entry__info {
          border-color: $color-dark-800 !important;

          > span {

            &:before {
              border-color: $color-secondary-50;
            }

            &:after {
              color: $color-secondary-50;
            }
          }
        }

        .theme-entry__rate {
          .theme-rating-toggler {
            &.btn-link {
              color: $color-dark-100 !important;
            }
          }
        }

        &:hover {
          border-color: rgba($color-secondary-d, 0.5);
        }
      }

      &:checked {
        + label {
          border-color: $color-secondary-50;

          .theme-entry__info {
            i {
              color: $color-dark-300;
            }

            span {
              color: $color-secondary-50;
            }
          }

          &:hover {
            border-color: $color-secondary-50;
          }
        }
      }
    }
  }

  &:hover {
    border-color: rgba($color-secondary-50, 0.5);
  }
}

.theme-details {
  .theme-details-header {
    background-color: $color-dark-700;
    border-color: $color-dark-300;
  }

  .rec-article {
    * {
      color: $color-gray-300 !important;
    }
  }
}

.theme-images-slider {
  .swiper-slide {
    border-color: $color-dark-400;
    background-color: $color-dark-600;
  }

  .swiper-thumbs {
    .swiper-slide-thumb-active {
      box-shadow: inset 0 0 0 3px $color-secondary-50
    }
  }
}

.theme-grid-list {
  > div {
    box-shadow: -1px 0 0 0 $color-dark-400;
  }
}





