.fc {
  &.fc-theme-standard {
    td,
    th,
    .fc-list,
    .fc-view > table
    {
      border-color: $color-dark-300 !important;
    }
    .fc-view {
      border-top-color: $color-dark-300;
      border-left-color: $color-dark-300;
    }
    .fc-col-header {
      .fc-col-header-cell-cushion {
        color: $color-gray-300;
      }
    }
    .fc-daygrid-week-number {
      color: $color-dark-100;
      background-color: $color-dark-900;
    }
    .fc-button-group {
      .fc-button {
        background-color: $color-dark-600 !important;
        border-color: $color-dark-300 !important;
        color: $color-gray-300;

        &:hover,
        &.fc-button-active {
          color: $color-primary-l !important;
          background-color: $color-secondary-50 !important;
          border-color: $color-secondary-50 !important;

          span {
            color: $color-primary-l !important;
          }
        }
      }
    }

    .fc-scrollgrid {
      > thead {
        background-color: $color-dark-600;
      }
    }
  }
  .fc-list-empty {
    background-color: rgba($color-dark-800, 0.25);
  }
}