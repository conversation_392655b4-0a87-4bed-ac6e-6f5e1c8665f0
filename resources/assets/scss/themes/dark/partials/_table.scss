.table {
  > thead {
    > tr {
      > th {
        color: $color-dark-100;
        background-color: $color-dark-800 !important;
      }
    }
  }

  > tbody {
    > tr {
      > td {
        border-top-color: $color-dark-600;
      }

      &.selected {
        background-color: $color-dark-800 !important;
        border-color: unset;
      }

      &.active {
        td {
          background-color: $color-dark-800 !important;
        }

        &.light {
          td {
            background-color: $color-dark-600 !important;
          }
        }
      }
    }
  }

  &-hover {
    tbody tr:hover {
      background-color: $color-dark-800;
    }
  }
}

.no-more-tables {
  &--medium-screen {
    @include mediaMaxWidth(1300px) {

      > table tbody {
        border-top-color: $color-dark-400;

        tr {
          &.table-row {
            border-bottom-color: $color-dark-400;

            td {
              &::before {
                color: $color-dark-100;
              }
            }
          }
        }
      }
    }
  }

  @include mediaMaxWidth($screen-phones) {
    .text-muted {
      color: $color-dark-100;
    }
    tbody {
      border-top-color: $color-dark-400;
    }
    tr.table-row {
      border-bottom-color: $color-dark-400;
    }
  }
}

.table-with-links tr.table-row:hover,
.media-list-linked .media-link:hover {
  background-color: $color-dark-750;
}

.new-order-row {
  background-color: $color-dark-800;
}

.table > tbody > tr:first-child {
  border-top-color: $color-dark-400;
}

.report-table > tbody > tr:last-child > td {
  border-color: $color-dark-400 !important;
  background-color: $color-dark-600 !important;
}

.promotion-coupon {
  .rec-title-block {
    border-color: $color-dark-300;
    background-color: $color-dark-600;
  }
}
