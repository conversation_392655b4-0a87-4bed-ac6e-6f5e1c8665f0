.alert-shipping-balance {
  background: #FEEFB3; //colorAvilabilty
  @include b-radius($b-radius-sm);
  color: #807b5c; //colorAvilabilty

  > a {
    color: #807b5c !important;

    i {
      float: left;
    }
  }
}

.alert {
  &-salla {
    background: $color-danger;
    color: $color-white;
  }

  &-red {
    .label {
      background: $color-danger;
    }

    &:hover {
      color: #9c1f1f;
    }
  }
}

.rec {
  &-alert-box {
    &--success {
      background-color: $color-gray-200;

      * {
        color: $color-primary;
      }
    }

    &--danger {
      background-color: $color-gray-50;

      * {
        color: $color-danger;
      }
    }


    &--info {
      background-color: $color-gray-50;

      * {
        color: #8a6d3b; //colorAvilabilty
      }
    }

    &--violation {
      &.replay {
        span {
          &.replay-count {
            background-color: #9C1F1F; //colorAvilabilty
            color: $color-white;
          }
        }
      }
    }
  }
}

.alert-belt {
  background: #3e3529 !important;

  &.danger {
    .ring-border {
      background: #F55157;
      border-color: white;
      color: white;
      box-shadow: 0 0 0 6px #8B080D;
    }

    background: rgba(245, 81, 87, 0.1);
    border-color: rgba(245, 81, 87, 0.2);
    color: #8B080D;

    a {
      color: $color-danger;
    }
  }
}
