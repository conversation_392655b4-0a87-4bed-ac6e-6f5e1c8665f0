$direction: 'rtl';
$fonts: ();
@import "./../../partials/vars";
@import "./../../partials/mixins";


@import "vars";
// dark mode theme override everything ---
body {
  &.dark {
    color: $color-gray-500;
    background: $color-dark-650;

    @import "partials/common";
    @import "partials/forms";
    @import "partials/btn";
    @import "partials/panel";
    @import "partials/table";
    @import "partials/accordion";
    @import "partials/products";
    @import "partials/v-treeselect";
    @import "partials/vue-select";
    @import "partials/filter";
    @import "partials/checkbox";
    @import "partials/list";
    @import "partials/order-log";
    @import "partials/reports";
    @import "partials/daterange";
    @import "partials/modal";
    @import "partials/badge";
    @import "partials/speical-offer";
    @import "partials/multiselect";
    @import "partials/qty-field";
    @import "partials/addon";
    @import "partials/card";
    @import "partials/tooltip";
    @import "partials/theme-entry";
    @import "partials/merchant-addon";
    @import "partials/categories";
    @import "partials/product-sorting";
    @import "partials/bulk-editor";
    @import "partials/vuetable";
    @import "partials/pagination";
    @import "partials/feature-promo";
    @import "partials/domain";
    @import "partials/languages-table";
    @import "partials/title";
    @import "partials/timeline";
    @import "partials/full-calendar";
    @import "partials/oneclick";
    @import "partials/alert-box";
    @import "partials/alert";
    @import "partials/payment-details";
    @import "partials/app-builder";
    @import "partials/file-input";
    @import "partials/steps";
    @import "partials/summary";
    @import "partials/mahallay";
    @import "partials/custom-stateses";
    @import "partials/loader";
    @import "partials/star-rate";
    @import "partials/cc-entry";
    @import "partials/credit-card-entry";
    @import "partials/multilingual";
    @import 'partials/vue-datepicker';
    @import 'partials/bootstrap-tagsinput';
  }
}

