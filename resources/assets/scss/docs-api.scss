// override colors ---
:root { // @osama - needs revisit ---
  --color-primary-tint: hsla(202, 100%, 65%, 0.03);
  --color-text-paragraph: #444;
  --color-canvas-100: #040d21;
  --color-sidebar-bg: #040d21;
  --color-primary: #76E8CD;
}

$color-primary: #76E8CD;
$color-secondary: #16263f;
$color-secondary-lighter: #2a4979;
$color-secondary-darker: #040d21;
$color-alt: #ff9e01;
$color-border: rgba($color-secondary, 0.1);
$color-bg: #f8f8f8;

// breakpoints ---
$screen-desktop-small: 1439px;
$screen-laptop: 1200px;
$screen-phones: 767px;
$trans-speed: 0.85s;

@import "partials/mixins";
@import 'partials/vars';

// logo and sidebar ---
.LeftSidebar {
  background-color: $color-secondary-darker;
}

body {
  &:after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background-color: rgba($color-secondary-darker, 0.7);
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99;
    @include transi();
    opacity: 0;
    visibility: hidden;
  }

  &.nav-open {
    overflow: hidden;

    .LeftSidebar {
      left: 0 !important;
      opacity: 1 !important;
      visibility: visible !important;
    }

    &:after {
      opacity: 1;
      visibility: visible;
    }
  }
}

.Editor {

  .h-TopNav {
    border-color: $color-border;

    svg.fa-search {
      width: 1.4rem;
      height: 1.4rem;
    }

    @include mediaMaxWidth($screen-phones) {
      padding-left: 80px;
      position: fixed;
      top: 0;
      z-index: 9;
      background: $color-white;
      left: 0;
      right: 0;
    }
  }
}

.ProjectPage {
  &.sl-px-20 {
    @include mediaMaxWidth($screen-phones) {
      padding-left: 40px !important;
      padding-right: 40px !important;
    }
  }

  &.sl-py-16 {
    @include mediaMaxWidth($screen-phones) {
      padding-top: 3px !important;
      padding-bottom: 30px !important;
    }
  }
}

.SiteSidebarTopNav {
  > a {
    padding-bottom: 20px;

    > div {
      width: 100% !important;
      max-width: unset !important;
      margin: 0 !important;

      img {
        height: 45px;
      }
    }

    &:hover {
      background-color: transparent;
    }
  }

  &:hover {
    background-color: transparent;
  }
}

.slider-element {
  padding-top: 0 !important;
}

// -- info blocks --
.bp3 {
  &-collout {
    &.bp3-intent-warning {
      p {
        a {
          color: #ed8935;
          background-color: rgba(#ed8935, 0.1);
          border-color: rgba(#ed8935, 0.25);
        }
      }
    }

    &.bp3-intent-success {
      border-color: $color-primary !important;
      background-color: rgba($color-primary, 0.1) !important;
    }
  }

  //tabs --
  &-simple-tab-list {

    .bp3-simple-tab {
      background-color: $color-bg;

      pre.CodeViewer {
        @include mediaMaxWidth($screen-phones) {
          margin: 10px;
        }
      }

      &.selected-tab {
        background-color: $color-white;
      }
    }
  }

  &-button {
    min-height: 30px !important;

    &.bp3-large {
      min-height: 30px;
    }

    &.bp3-intent-primary {
      background-color: lighten($color-secondary-darker, 10%);
      background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
      box-shadow: unset !important;

      &:hover {
        background-color: lighten($color-secondary-darker, 15%);
      }
    }
  }

  &-tab {
    &-list {
      min-height: 40px;
    }

    &-indicator-wrapper {
      height: 100% !important;

      .bp3-tab-indicator {
        height: 1px;
        @include b-radius($b-radius-sm);
        background-color: lighten($color-secondary, 30%);
      }
    }
  }

  &-heading {
    color: $color-secondary-lighter;
  }

}

.bp3-tab[aria-selected="true"], .bp3-tab:not([aria-disabled="true"]):hover {
  color: lighten($color-secondary, 30%);
}


// anchor tag ---

.sl-elements-article {
  a {
    color: $color-primary;
    background-color: rgba($color-primary, 0.01);
    border-color: rgba($color-primary, 0.5);

    &[rel~="noreferrer"] {
      padding: 0;
      margin: 0 8px;
    }

    &:hover {
      background-color: rgba($color-primary, 0.1);
    }

    &.hover\:text-primary:hover {
      color: var(--color-primary);
    }
  }
}


.MarkdownViewer {
  a {
    background-color: rgba($color-primary, 0.01);

    &:hover {
      color: var(--color-primary);
      background-color: rgba($color-primary, 0.1);
      border-color: rgba($color-primary, 0.5);
    }
  }

  @include mediaMaxWidth($screen-desktop-small) {
    padding: 30px;
  }
  @include mediaMaxWidth($screen-phones) {
    padding: 15px;
    overflow: auto;
  }
}

*, :before, :after {
  border-color: $color-border;
}

.text-success {
  color: $color-primary;
}

.border-success {
  border-color: $color-primary;
}


h2.bp3-heading, .bp3-running-text h2 {
  font-size: $text-xxx-large;
  word-break: break-all;

  .uppercase {
    padding-top: 0;
    padding-bottom: 0;
    @include mediaMaxWidth($screen-phones) {
      font-size: $base-size;
      line-height: 26px;
      &.mr-5 {
        margin-right: 10px;
      }
    }
  }
}

.bg-gray-1 {
  background-color: rgba($color-secondary, 0.03);
}

// select ---
.bp3-html-select select,
.bp3-select select {
  background-color: $color-white;

  &:hover {
    background-color: $color-white;
    box-shadow: none;
  }
}

// req-marker ---
.RequestMaker {
  &__RequestEditor {
    background-color: rgba($color-secondary, 0.02);

    &-tabs {

    }

    &.mt-10 {
      margin-top: 20px;
    }
  }

  &__RequestSend {
    margin-left: 10px;
  }

  &____RequestParameters {
    border-bottom: 1px solid $color-border;

    .flex.items-center {
      .bp3-button {
        height: 23px;
        min-height: unset !important;
        margin: 3px 0;
      }
    }
  }

  &____RequestPath {
    border-right: none;
  }
}


// buttons --- done


// nav_toggle ---
#nav_toggle {
  display: none;
  width: 55px;
  height: 40px;
  position: fixed;
  top: 10px;
  left: 10px;
  border-right: 1px solid $color-border;
  font-size: $text-xx-large;
  color: $color-secondary-lighter;
  z-index: 9999999;

  i {
    pointer-events: none;
  }

  &.nav-open {
    color: $color-white;
  }

  @include mediaMaxWidth($screen-phones) {
    display: block;
  }
}

// responsive ---
.Editor.sl-min-h-screen {
  .h-TopNav {
    background-color: $color-white;
    @include mediaMaxWidth($screen-desktop-small) {
      padding-left: 30px;
    }
    @include mediaMaxWidth($screen-phones) {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      padding: 15px 15px 15px 80px;
      z-index: 99;
      background-color: $color-white;
    }
  }

  .ProjectPage {
    @include mediaMaxWidth($screen-desktop-small) {
      padding: 30px;
    }
    @include mediaMaxWidth($screen-phones) {
      padding: 15px;
      .MarkdownViewer {
        padding: 0;
      }
    }
  }

  main.sl-min-h-screen {
    > div {
      > .flex.flex-grow {
        @include mediaMaxWidth($screen-laptop) {
          flex-direction: column;
          .ProjectPage, > .bg-gray-1 {
            width: 100%;
          }

          .bg-gray-1 {
            border: none;
            border-top: 1px solid $color-border;

            .absolute {
              width: 100%;
              position: relative;

              &.p-10 {
                padding: 20px;
              }

              > h2.mt-10.mb-8 {
                margin: 0 0 20px 0;
              }
            }
          }
        }
        @include mediaMaxWidth($screen-phones) {
          .bg-gray-1 {

            .absolute {

              &.p-10 {
                padding: 15px;
              }

              > h2.mt-10.mb-8 {
                margin: 0 0 15px 0;
              }
            }
          }
        }
      }
    }

    @include mediaMaxWidth($screen-phones) {
      margin-top: 60px;
    }
  }
}

.Workspace.Page {
  @include mediaMaxWidth($screen-phones) {
    .LeftSidebar {
      position: fixed;
      top: 0;
      left: -100%;
      opacity: 0;
      visibility: hidden;
      z-index: 99999;
      overflow: auto;
      @include transi();

      .SiteSidebarTopNav {
        .h-TopNav {
          margin-left: 60px;
        }
      }
    }
    .Editor {
      margin-left: 0;
      overflow: hidden;

      .HttpOperation__Responses {
        @include mediaMaxWidth($screen-laptop) {
          .SectionTitle {
            border: none;
          }
          > .flex {
            flex-direction: column;
          }
        }
        @include mediaMaxWidth($screen-phones) {
          .HttpOperation__Response {
            > .MarkdownViewer {
              &.ml-1 {
                margin-left: 0;
                padding-left: 15px;
              }

            }

            &.pl-8 {
              padding-left: 0;
            }

            &:before, &:after {
              border: none;
            }
          }
        }
      }
    }
  }
}


.sl-badge {
  &.sl-text-paragraph {
    color: $color-white !important;
  }
}

