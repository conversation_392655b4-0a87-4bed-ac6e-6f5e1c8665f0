@import "partials/vars";
@import "partials/mixins";

body {
  --bg-main: #ffffff;
  --bg-light: #E8EEF2;
  --border-light: #eee;
  --bg-dark: #2d4d63;
  --text-dark: #303633;
  --color-danger: #f55157;
  --color-primary: #5CD5C4;
  --color-primary-d: #1cb19c;
  margin: 0;

  * {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    @include selection($color-primary, $color-primary-d);
  }
}

#redoc_container > div > div.menu-content > div.scrollbar-container.undefined.ps > div {
  display: none !important;
}

#redoc_container {
  @each $num in 1, 2, 3, 4, 5, 6 {
    h#{$num} {
      color: $color-primary;
      font-weight: 600;
      -webkit-margin-before: 0;
      -webkit-margin-after: 0;
      -webkit-margin-start: 0;
      -webkit-margin-end: 0;

      &.sc-gZMcBi.kBWwoV {
        margin: 20px 0;
      }
    }
  }

  .redoc-wrap {
    // menu content ---
    .menu-content {
      background: var(--bg-dark);

      .security-details {
        tr, td, th {
          border-color: var(--border-light);
        }
      }

      > div {
        // brand ---
        &:first-child {
          width: calc(100% - 20px);
          height: 40px;
          margin: 40px 0 30px 20px;
          background: url('/assets/images/api-db.png') left no-repeat;
          background-size: contain;

          img {
            &.hZCbNs {
              // hide for now -  temp solution
              display: none;
              width: 60px;
              height: auto;
              margin: 40px auto;
              padding: 0;
            }
          }
        }

        // search ---
        &[role=search] {
          padding: 0 20px 20px;
          position: relative;

          svg {
            display: block;
            position: absolute;
            top: 7px;
            left: 35px;

            path {
              fill: var(--bg-dark);
            }
          }

          i {
            top: 5px;
            right: 30px;
          }

          input[type=text] {
            width: 100%;
            height: 40px;
            @include b-radius($b-radius-sm);
            border: none;
            background-color: $color-white;
            padding-left: 40px;
            margin: 0;
          }

          .scrollbar-container {
            @include b-radius($b-radius-sm);
            margin-top: 10px;
            background: $color-white;

            div[data-role='search:results'] {
              background: $color-white;

              li {
                label {
                  &:hover {
                    background: rgba($color-primary, 0.1);
                  }
                }
              }
            }
          }
        }

        // navigation ---
        &.scrollbar-container {
          ul[role=navigation] {
            li {
              label {
                @include transi();

                * {
                  font-weight: 600;
                  color: $color-white;
                  fill: $color-white;
                  @include transi();
                }

                &:hover {
                  background-color: rgba($color-black, 0.1);
                }

                // active state ---
                &.active {
                  background-color: rgba($color-black, 0.5);
                }
              }

              // sub nav elements ---
              ul {
                li {
                  label {
                    &.active {
                      background-color: rgba($color-black, 0.3);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // api content ---
    .api-content {
      > * {
        width: 100%;

        .diqfaT {
          color: var(--text-dark);
        }
      }

      > div[data-section-id^="section/Overview"] {
        padding: 0;
        padding-bottom: 20px;
      }
    }


    // layout classes ---

    // buttons
    .jIdpVJ, .eoxkJU, .http-verb {
      color: $color-white;
      border: none;
      background-color: $color-primary;
      @include b-radius($b-radius-sm);
      @include transi();

      &:hover {
        background-color: $color-primary-d;
      }
    }

    .shv3r-4 {
      border: none;
    }

    .bnFPhO {
      a {
        color: $color-white;
        border: none;
        background-color: $color-primary;
        @include b-radius($b-radius-sm);
        @include transi();
        padding: 5px 10px;

        &:hover {
          background-color: $color-primary-d;
        }
      }
    }

    .http-verb {
      line-height: 1;
      padding: 5px 10px;
    }

    // spacing ---
    .fLUKgj, .dtUibw, .VVcFr {
      background: var(--bg-light);
      @each $num in 1, 2, 3, 4, 5, 6 {
        h#{$num} {
          color: #5d5d5d;
        }
      }
    }

    .dtUibw {
      > div {
        margin: 0 0 30px 0;

        h3 {
          margin-bottom: 10px;
        }

        .ecxnvs {
          span {
            position: relative;
            top: unset;
            left: unset;
          }
        }
      }

      @include mediaMaxWidth(800px) {
        padding: 20px;
      }
    }

    .dluJDj {
      @include mediaMinWidth(800px) {
        //padding: 20px 0;
      }
    }

    .kdxmLJ {
      padding: 10px 10px 10px 0;
      border-bottom: 1px solid var(--border-light);

      svg {
        margin: 0 !important;

        * {
          fill: var(--text-dark);
        }
      }
    }

    .LiUBH {
      padding: 10px;
      margin: 10px 0;
      border: none;
      background: rgba($color-primary, 0.1);
      @include b-radius($b-radius-sm);
    }

    .geWpKA {
      h5 {
        border: none;
      }
    }

    .kGwPhO {
      border-bottom: 1px solid $color-gray-200;
    }

    .bYENt {
      color: $color-danger;
      margin-top: 2px;
    }

    .cjtbAK {
      padding: 10px 20px;
      @include mediaMaxWidth(800px) {
        padding: 10px 20px;
      }
    }

    .byLrBg {
      color: $color-danger;
      background: rgba($color-danger, 0.1);

      svg {
        margin-right: 10px;

        * {
          fill: $color-danger;
        }
      }

      @include mediaMaxWidth(1360px) {
        margin-bottom: 20px;
      }
    }

    .espozG {
      padding: 10px 0 5px;
      margin-bottom: 10px;
    }

    .kmipUx {
      padding: 30px 0 0;
    }

    .hiuczA {
      padding: 20px 0;

      &:after {
        display: none;
      }
    }

    .hqYVjx {
      color: $color-primary-d;
    }

    .kkcUap {
      background: $color-primary;
      @include b-radius($b-radius-sm $b-radius-sm 0 0);
      font-weight: 700;
      line-height: 1;
      padding: 8px 15px;
    }

    .react-tabs__tab-list {
      @include flexable(flex-start, flex-start, row);
      margin: 0;

      > li {
        border: none;
        color: var(--text-dark);
        background: $color-white;
        padding: 8px 10px;
        @include transi();
        flex: auto;
        margin: 0;
        @include b-radius(0);

        &.react-tabs__tab--selected {
          color: $color-white;
          background: var(--bg-dark);

          &:hover {
            color: $color-white !important;
            background: var(--bg-dark) !important;
          }
        }

        &:first-child {
          @include b-radius($b-radius-sm 0 0 0)
        }

        &:last-child {
          @include b-radius(0 $b-radius-sm 0 0)
        }

        &:hover {
          color: var(--bg-dark);
        }
      }

      .tab-error {
        @include b-radius($b-radius-sm $b-radius-sm 0 0 !important);
      }
    }

    .react-tabs__tab-panel {
      @include b-radius(0 0 $b-radius-sm $b-radius-sm);
      background: var(--bg-dark);

      .CodeMirror {
        padding: 20px;
        @include b-radius($b-radius-sm);
        background: rgba(black, 0.3) !important;
      }

      .punctuation {
        color: $color-white
      }
    }

    .VVcFr {
      background: var(--bg-dark);

      .eLrhLh, .gbHJAS {
        background: var(--bg-dark);
        border-bottom: 1px solid #5c778a;

        .fPiNGd {
          background: var(--color-primary);
        }

        .dHKGl {
          color: rgba($color-white, 0.5);
          background: #1c2f3c;
        }
      }
    }

    .bjXsCV {
      background: #1c2f3c;
    }

    .kJDpdl {
      color: $color-primary;
    }

    .liEIwZ {
      background: $color-primary;
    }

    .giaaPy {
      color: $color-primary;
    }

    .fXybtJ {
      background: $color-primary;

      svg {
        * {
          fill: $color-white;
        }
      }
    }

    .gackFU {
      border-color: $color-gray-200;
      background: $color-white;
      padding: 3px 5px;
      @include b-radius($b-radius-sm);
      margin: 0 3px;
    }

    td {
      code {
        color: $color-danger;
        border-color: $color-gray-200;
        background: $color-white;
        padding: 1px 5px 3px;
        @include b-radius($b-radius-sm);
        margin: 0 3px;
      }
    }

    .nGwee {
      display: none !important;
    }

    .bcLONg {
      &:before, &:after {
        background: #ddd;
      }
    }

    .bIrgla {
      background: linear-gradient(transparent 0%, transparent 22px, rgb(238, 238, 238) 22px, rgb(238, 238, 238) 100%);
      border-left-width: 0px;
      background-size: 1px 100%;
      background-position: left top;
      background-repeat: no-repeat;
    }
  }
}
