@import 'partials/vars';
.bootstrap-select {
    display: block !important;
    width: 100% !important;

    .btn {
        height: 45px;
        .filter-option {
            i {
                color: $color-gray-300;
                margin-left: 12px !important;
            }
        }
    }

    &--full {
        .btn {
            border-right: 1px solid $color-gray-200 !important;
        }
    }

    &.btn-group .dropdown-menu  {
        top: 44px;
        ul li {
            > a {
                .glyphicon {
                    color: $color-gray-300;
                    font-size: $text-x-medium;
                    margin-left: 12px !important;
                }
                &:hover {
                    background-color: rgba(245, 245, 245, .47);;
                }
            }

            &.selected {
                > a {
                    background: rgba(92, 213, 196, .32) !important;
                    .glyphicon {
                        color: $color-dark-200;
                    }
                    &:hover {
                        color: $color-white !important;
                        background-color: $color-primary !important;
                    }
                }
            }
        }
    }
}