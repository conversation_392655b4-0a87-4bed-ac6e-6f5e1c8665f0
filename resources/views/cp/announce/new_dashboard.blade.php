<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>انتقل إلى لوحة التحكم الجديدة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .icon {
            font-size: 120px;
            margin-bottom: 30px;
            color: #96edd9;
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #96edd9;
            font-weight: 700;
        }

        .subtitle {
            font-size: 1.5rem;
            margin-bottom: 40px;
            color: #cccccc;
            line-height: 1.6;
        }

        .message {
            font-size: 1.2rem;
            margin-bottom: 40px;
            color: #e0e0e0;
            line-height: 1.8;
            background: rgba(255, 255, 255, 0.05);
            padding: 30px;
            border-radius: 15px;
            border-right: 4px solid #96edd9;
        }

        .steps {
            text-align: right;
            margin: 40px 0;
            background: rgba(0, 0, 0, 0.2);
            padding: 30px;
            border-radius: 15px;
        }

        .steps h3 {
            color: #96edd9;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }

        .step-number {
            background: #96edd9;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-weight: bold;
        }

        .step-text {
            flex: 1;
            font-size: 1.1rem;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #96edd9, #004e5c);
            color: white;
            padding: 18px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 212, 170, 0.3);
            margin: 20px 10px;
            border: none;
            cursor: pointer;
            font-family: inherit;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 212, 170, 0.4);
        }

        .warning {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            font-size: 1.1rem;
        }

        .footer {
            margin-top: 40px;
            color: #888;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1.2rem;
            }
            
            .icon {
                font-size: 80px;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(5px);
            overflow-y: auto;
            padding: 20px 0;
        }

        .modal-content {
            position: relative;
            margin: 0 auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            background: rgba(26, 26, 26, 0.95);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            max-height: none;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .modal-header {
            color: #96edd9;
            font-size: 1.5rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .modal img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            margin: 20px 0;
        }

        .close {
            position: absolute;
            top: 15px;
            left: 25px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #96edd9;
        }

        .modal-instructions {
            text-align: right;
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            line-height: 1.8;
        }

        .modal-instructions h4 {
            color: #96edd9;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🚀</div>
        
        <h1>لوحة التحكم الجديدة متاحة الآن!</h1>
        
        <p class="subtitle">هذه الصفحة لم تعد تعمل في لوحة التحكم القديمة</p>
        
        <div class="message">
            <strong>عذراً!</strong> لوحة التحكم القديمة لم تعد متاحة. لقد قمنا بتطوير لوحة تحكم جديدة ومحسنة لتوفير تجربة أفضل وأكثر سهولة في الاستخدام.
        </div>

        <div class="warning">
            ⚠️ <strong>تنبيه مهم:</strong> جميع الميزات والخدمات متاحة الآن فقط في لوحة التحكم الجديدة
        </div>

        <div class="steps">
            <h3>كيفية الانتقال إلى لوحة التحكم الجديدة:</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">اضغط على أيقونة القائمة (<img width="20" height="20" src="{{ store()->avatar?:'https://cdn.salla.network/images/logo/logo-light-square.png' }}" alt="Menu">) في أعلى يمين الشاشة</div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">ابحث عن خيار "الانتقال للوحة التحكم الجديدة" في أسفل القائمة</div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">اضغط على الخيار وستنتقل تلقائياً إلى لوحة التحكم الجديدة</div>
            </div>
        </div>

        <div style="margin: 40px 0;">
            <button class="cta-button pulse" onclick="showNewDashboardInstructions()">
                📱 عرض التعليمات التفصيلية
            </button>
        </div>

        <div class="footer">
            <p>شكراً لك على صبرك وتفهمك. نحن متحمسون لتقديم تجربة أفضل لك!</p>
            <p style="margin-top: 10px;">فريق الدعم الفني • 2025</p>
        </div>
    </div>

    <!-- Modal for detailed instructions -->
    <div id="instructionsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div class="modal-header">التعليمات التفصيلية للانتقال إلى لوحة التحكم الجديدة</div>
            
            <div class="modal-instructions">
                <h4>اتبع الخطوات الموضحة في الصورة:</h4>
                <p>1️⃣ افتح التطبيق على هاتفك</p>
                <p>2️⃣ اضغط على أيقونة القائمة (☰) في أعلى اليسار</p>
                <p>3️⃣ مرر لأسفل حتى تجد "الانتقال للوحة التحكم الجديدة"</p>
                <p>4️⃣ اضغط على الخيار كما هو موضح بالسهم الأحمر</p>
                <p>5️⃣ ستنتقل تلقائياً للوحة الجديدة</p>
            </div>
            
            <img src="https://cdn.assets.salla.network/dash/images/new-dashboard.png" alt="تعليمات الانتقال للوحة التحكم الجديدة" />
            
            <div style="margin-top: 20px; color: #ffc107;">
                <strong>💡 نصيحة:</strong> إذا لم تجد الخيار، تأكد من تحديث التطبيق إلى أحدث إصدار
            </div>
        </div>
    </div>

    <script>
        function showNewDashboardInstructions() {
            document.getElementById('instructionsModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('instructionsModal').style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('instructionsModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.style.opacity = '0';
                    step.style.transform = 'translateX(50px)';
                    step.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        step.style.opacity = '1';
                        step.style.transform = 'translateX(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
