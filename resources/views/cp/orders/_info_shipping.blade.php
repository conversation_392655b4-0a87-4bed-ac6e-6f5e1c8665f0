@php
    /** @var \App\Models\Order $order */
    /** @var \Modules\Orders\Services\Shipping\MultiShipments\ShipmentResolver $shipment */
 @endphp
<div class="panel panel-default order-panel">
    <div class="panel-heading">
        <h6 class="panel-title">
            {{ trans('order.shipping_or_picking_from_branch') }}
        </h6>
        {{--Show printing button or issing policy button --}}

        @if(
            $shipment->isRequireShipping() &&
            ! $order->hasMultiShipments() &&
            $shipment->shippingCompany &&
            $order->isBuyAsGiftReadyForShipping()
        )
            @if($shipment->shippingCompany->isOnDemandDelivery())
                @include('orders::dashboard.shipping.shipping_police_fast_delivery')
            @else
                @include('orders::dashboard.shipping.shipping_police')
            @endif
        @endif
    </div>

    {{--
        Case1: OneShipment, show company info, shipping address duration, policies (details & controls)
        Case2: MultiShipments, show summary of shipments, shipping address duration
    --}}
    <div class="panel-body">
        {{--Case1: OneShipment--}}
        @if(! $order->hasMultiShipments())
            <div class="media">
                <div class="media-body">
                    @if ($shipment->getReference()->shippingCompany && $shipment->getReference()->shippingCompany->isOnDemandDelivery())
                        @include('orders::dashboard.shipping.shipping_details_fast_delivery_companies')
                    @else
                        @include('orders::dashboard.shipping.shipping_details')
                    @endif
                </div>
            </div>

        {{--Has multi shipment--}}
        @else
            {{--Case2: MultiShipments--}}
            <div class="">
                @include('orders::dashboard.shipping.multi_shipments.multi_shipments_details')
            </div>
        @endif

        @if (!$order->isBuyAsGiftReadyForShipping() && $order->settings->get('ready_for_shipping', false) == false)
            <hr>
            <span class="text-muted text-danger">
              {{ __('orders::order.gift_order_is_not_ready_for_shipping') }}
            <span>
        @endif
    </div>
</div>
