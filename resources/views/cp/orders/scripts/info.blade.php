<style>
    [data-show-card] {
        cursor: pointer;
    }

    .unactive-small {
        color: #5dd5c4;
        border-bottom: 1px solid #5dd5c4;
        line-height: 1.5;
    }
</style>
<script type="application/javascript">
function changeOrderStatusModel(event) {
$('#modal_change_order_status_div').html(''); //reset model change order status div
$('#modal_policy_div').html(''); //reset model issue policy div

showLoading(event);

$.ajax({
    type: "GET",
    url: "{{route('cp.orders.change_order_status_model', ['order' => $order->getRouteKey()])}}",
    success: function (data) {

        $('#modal_change_order_status_div').html(data);
        $('#modal_order_status_method').modal('show');
        $('#order_status').change();

        hideLoading();

    },
    error: function (data) {
        swal('تنبيه', 'حصل خطأ غير متوقع، برجي المحاولة لاحقآ', 'error');
    },
});
}

function renderCompaniesOptions(options_container, order_shipment_branch_id = null, shipmentType = 'shipment', branch_id = null) {
let _shipping_details_id = 0 ,
    url="{{route('cp.order.company_options',['order' => $order->getRouteKey()])}}",
     order_id = "{{ $order->getRouteKey() }}";
if (order_shipment_branch_id) {
    url += '/' + order_shipment_branch_id;
}

if (1 || _shipping_details_id != 0) {
  showLoading();
    axios.post(url, {
        order_id,
        order_shipment_branch_id,
        shipmentType,
        branch_id
    }).then(function (response) {
        options_container.html(response.data)
            .parent()
            .removeClass('hidden').fadeIn();
        $("[data-select-picker]").selectpicker();
        $(".styled").uniform({
            radioClass: 'choice'
        });
        enableSubmitButton();
        scrollAttachEvent();

        hideLoading();
    }).catch(function ({response}) {
        const {error} = response.data;
        swal('خطا', error.fields.company_id[0], 'error');
    });
} else {
    options_container.parent().addClass('hidden');
    options_container.html('');
}
}



// let show the current cart
$('[data-card-customer-{{ $order->getReceiver() ? 'receiver' : 'buyer' }}]').show();
@if(!$order->getReceiver())
$('.customer-role').hide();
@endif

$('[data-show-card]').on('click', function () {
$('[data-' + $(this).data('show-card') + ']').fadeIn().show();
$('[data-' + $(this).data('hide-card') + ']').fadeOut().hide();
$('[data-show-card]').removeClass('unactive-small');
$(this).addClass('unactive-small');
})

$('#ocAddComment').on('click', function () {
$('#ocComment').removeClass('hidden').slideToggle('fast');
})

</script>

<script type="text/javascript" src="{{ asset('cp/assets/js/plugins/forms/inputs/bootstrap-maxlength.js?v=1.7.0') }}">
</script>

<script type="text/javascript">

    $(function() {
        var select_countiner = $('.bootstrap-select');
        select_countiner.selectpicker();
        select_countiner = $('#order_status');

        //select_countiner.on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
        $(document).on('change', '#order_status', function(e, clickedIndex, isSelected, previousValue) {
            // if isShippable
            if ($('option:selected', this).data("is-shippable")) {

                renderCompaniesOptions($("#companies_options"));
                $('.branches-dropdown').show();
                $('#companies_options').show();
            } else {
                $('#companies_options').hide();
                $('.branches-dropdown').hide();
                $("#companies_options").html('');
            }
        });

        $('#order_status').change();

        $('textarea#status_note').maxlength({
            alwaysShow: true,
        });

      @if (Auth::user()->checkPermission('order_delete'))
      let lethtmlCheckboxLoyaltyTemplate, has_loyalty_point
        has_loyalty_point=$(".confirmation").attr('data-has_loyalty-point') ==='true'
        lethtmlCheckboxLoyaltyTemplate = has_loyalty_point ? '<div class="rec-checkbox rec-checkbox--default light"> <input type="checkbox" id="loyalty-checkbox" name="loyalty-checkbox"/> <label for="loyalty-checkbox"> <span>' +
          '{{trans('order.delete_loyalty_message')}}' +
          '</span> </label> </div>':''

          $('.confirmation').confirm({
              content: `<p>${has_loyalty_point ? "{{ trans('order.ask_delete_with_loyalty') }}" :  "{{ trans('order.ask_delete') }}" }</p>
                 ${lethtmlCheckboxLoyaltyTemplate}
              `,
              title: "{{ trans('global.alert') }}",
              rtl: true,
              closeIcon: false,
              buttons: {
                  confirm: {
                      btnClass: ' btn-danger ',
                      text: "تأكيد الحذف",
                      action: function() {
                          let isReturnLoyaltyPoint = $('#loyalty-checkbox').is(':checked')
                          if(has_loyalty_point){
                              processUpdatePost(-1,'',true,isReturnLoyaltyPoint);
                            } else {
                                processUpdatePost(-1);
                            }
                        }
                    },
                    cancel: {
                        text: '{{ trans('global.cancel') }}',
                        action: function() {}
                    }
                }
            });
        @endif

        $(document).on('click', '#btn-confirm-suspicious-order', function() {
          confirmSuspiciousOrder(handleSuspiciousOrderAjax)
        })

        $(document).on('click', '#btn-cancel-suspicious-order', function () {
          swal({
            title: "تنبيه",
            html: `<p>سيتم إرجاع المبلغ لبطاقة العميل وإشعاره بإلغاء الطلب حسب إشعارات حالة الطلب المفعَّلة في متجرك.</p>`,
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "موافق، ألغي الطلب",
            cancelButtonText: "تراجع",
          }).then((result) => {
            if (result) {
              handleSuspiciousOrderAjax('cancel')
            }
          })
        })


        const confirmSuspiciousOrder = function (callback) {
        swal({
          title: "هل أنت متأكد من الاستمرار بتنفيذ الطلب؟",
          html: `<p>استمرارك بتنفيذ هذا الطلب يحمِّلك المسؤولية في حال وردتنا شكوى من مالك البطاقة، وقد يُخصم مبلغ العملية من حسابك إذا ثبتت صحة الشكوى</p>`,
          type: "warning",
          showCancelButton: true,
          confirmButtonText: "نعم، وأتحمل المسؤولية",
          cancelButtonText: "تراجع",
        }).then((result) => {
          if (result) {
            callback()
          }
        })
      }

        const handleSuspiciousOrderAjax = function (operation = 'confirm') {
        $.ajax({
          url: `{{route('cp.order.handle_suspicious_alert', optimus()->encode($order->id))}}`,
          type: "POST",
          data: {
            operation: operation
          },
          cache: false,
          success: function (response) {
            swal('تم', response.message, 'success')
            setTimeout(function() {
              location.reload()
            }, 100);
          },
          error: function () {
            swal('خطأ', 'حدث خطأ يرجى التواصل مع فريق الدعم', 'error')
          }
        })
      }

        $(document).on('click', '#update_status, #update_status_suspicious', function(e) {
          e.preventDefault();
          let _status = $('#order_status').val();
          if($(this).attr('id') === 'update_status_suspicious') {
            $.ajax({
              type: 'GET',
              url: `{{ route('cp.order.check_suspicious_popup') }}?status=${_status}`,
              cache: false,
              success: function(response) {
                if(response.message === false) {
                  updateStatusCallback()
                } else {
                  confirmSuspiciousOrder(updateStatusCallback)
                }
              }
            })
          } else {
            updateStatusCallback()
          }
        });

        function updateStatusCallback() {
            $('#update_status').html('الرجاء الانتظار ..');
            $('#update_status').attr('disabled', true);

            let _status = $('#order_status').val();
            let orderStatus = $('#order_status').find('[value="' + _status + '"]');
            let is_restored_status = orderStatus.data('is-restored');
            let is_canceled_status = orderStatus.data('is-canceled');
            let is_price_quote = orderStatus.data('is-price-quote');
            let refundAmount = orderStatus.data('refund-amount');
            let hasRefundableMethod = orderStatus.data('has-refundable-method');
            let isRefundableStatus = orderStatus.data('is-refundable-status');

            let isRefundable = hasRefundableMethod && isRefundableStatus;
            let currency = "{{ getHumanCurrency($order->currency) }}";
            let customerName = "{{ str_replace(["\r", "\n"], '', addslashes($order->full_name)) }}".replace(/[\r\n]+/gm, '');
            let isRefundStatus = is_restored_status || is_canceled_status || is_price_quote;
            let canRefund = refundAmount && (hasRefundableMethod && isRefundableStatus);

            if (isRefundable && is_restored_status) validateOrderAmount(refundAmount);
            isRefunded = false
            if (isRefundable && refundAmount && $('#refund_all').val() == 1) {
              // start refund
              refundAction(refundAmount, currency, customerName, _status)
              //if the merchant can refund the payment in terms of order status selected, time (14 days), and order is refundable
            } else if (canRefund && isRefundStatus) {
              processUpdatePost(_status, "refund_later")
            } else {
              processUpdatePost(_status)
            }
        }

        function validateOrderAmount(refundAmount) {
            if ($('#refund_all').val() != 1 && ($('#refund_amount_input').val() > refundAmount)) {
                swal({
                    title: "{{ trans('global.alert') }}",
                    text: "يجب أن لاتتجاوز قيمة المبلغ  قيمة الطلب",
                    type: 'warning',
                });
                $('#update_status').html(' تعديل حالة الطلب');
                $('#update_status').attr('disabled', false);
                return;
            }
        }

        function refundAction(refundAmount, currency, customerName, _status) {
            console.log('refundAction')
            swal({
                title: "{{ trans('global.alert') }}",
                text: "هل أنت متأكد من تحويل مبلغ " + refundAmount + ' ' + currency +
                    ' للعميل ' + customerName + '؟',
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: "#5dd5c4",
                confirmButtonText: "{{ trans('global.confirm') }}",
                cancelButtonText: '{{ trans('global.cancellation') }}',
            }).then(
                function() {
                    processUpdatePost(_status, "with_refund");
                },
                function(dismiss) {
                    $('#update_status').html(' تعديل حالة الطلب');
                    $('#update_status').attr('disabled', false);
                }
            );
        }

        $('#private_order_status_btn').click(function(e) {
            e.preventDefault();
            showLoading();
            $.ajax({
                type: "POST",
                url: "{{ route('cp.order.private_status', ['order' => $order->getRouteKey()]) }}",
                data: {
                    _token: _token
                },
                success: function(data) {
                    var message = "هذه الخاصية غير متاحة";
                    var isOnDemand = false;

                    if (data) {
                        if (data.tracking_link) {
                            window.open(data.tracking_link, '_blank');

                            return;
                        }

                        if (data.message) {
                            message = data.message;
                        }

                        if (data.isOnDemand) {
                            isOnDemand = true;
                        }
                    }

                    if (isOnDemand) {
                        swal({
                            title: message,
                            type: 'warning',
                            confirmButtonText: "موافق"
                        });
                    } else {
                        $('#private_order_status_div').text(message);
                    }
                },
                complete: function() {
                    hideLoading();
                }
            });
        });


    });


    function processUpdatePost(_status, refund_flag = "", loyalty_point = false,isReturnLoyaltyPoint=false) {
        function statusChangeFunc() {
            // adding ajax call
            var url = "{{ route('cp.order.status') }}";
            var _note = $('#status_note').val();
            var formData = $('#order_status_form').serializeArray();
            if (_status == -1) {
                formData.push({
                    name: "order_status",
                    value: -1,
                });
            }
            if (!formData.some(function(el) {
                    return el.name === "order_id"
                })) {
                formData.push({
                    name: "order_id",
                    value: {{ $order->getRouteKey() }}
                });
            }
            var _data = jQuery.param(formData);
            $.ajax({
                type: 'POST',
                url: url,
                data: _data,
                success: function(data) {

                    if (data.case == 'error') {

                        if (data.type == 'smsa') {

                            swal({
                                title: '',
                                text: data.message,
                                type: 'error',
                                showCancelButton: true,
                                confirmButtonColor: '#f55157',
                                confirmButtonText: 'شحن الرصيد',
                                cancelButtonText: 'إغلاق',
                            }).then(function() {
                                window.location = baseUrl + '/wallet';
                            }, function(dismiss) {

                            });

                        } else {
                            $.alert({
                                title: 'رقم الطلب #' + data.order_id,
                                content: data.message,
                            });

                            refreshUpdateStatusForm();
                        }

                    }

                    if (_status == -1 && data.case == 'success') {
                        window.location = '{{ route('cp.orders') }}';
                        return;
                    }

                    setTimeout(function() {
                        showLoading();
                    }, 100);


                    let message = "{{ trans('orders::order.messages.success.status_change') }}";
                    if (refund_flag === 'with_refund') {
                        message = "{{ trans('orders::order.refund.alert_title') }}"
                    } else if (refund_flag === 'refund_later') {
                        message = "{{ trans('orders::order.refund.alert_text') }}"
                    }
                    if (data.case != 'error') {
                        // hide notification
                        $('#SIFT-alert').fadeOut();
                        //order updated success message
                        swal({
                            title: "{{ trans('orders::order.messages.success.status_change') }}",
                            ...refund_flag && {
                                text: message
                            },
                            type: 'success',
                            confirmButtonColor: '#f55157',
                            confirmButtonText: 'موافق',
                            showConfirmButton: refund_flag == 'with_refund',
                            ...refund_flag && {
                                timer: 3000
                            }
                        }).then(function() {

                        }, function(dismiss) {

                        });
                    }
                    location.reload();
                    return;
                },
                error: function(data) {
                    laravel.ajax.errorHandler(data);
                    refreshUpdateStatusForm();
                },
            });
        }

        if (window.hasSuspiciousAlert && window.hasSuspiciousAlert === true) {
            swal({
                title: '<h2>' + 'هل أنت متأكد من الإستمرار في الطلب؟' + '</h2>',
                html: '<p class="mx-10">' +
                    'تم إيقاف معالجة الطلب من قبل نظام الحماية من الاحتيال في سلة، يرجى مراجعة تفاصيل الطلب قبل الاستمرار.' +
                    '</p>',
                type: "warning",
                showCancelButton: true,
                allowOutsideClick: false,
                confirmButtonText: 'نعم، الإستمرار في الطلب !',
                cancelButtonText: 'تراجع',
            }).then(function() {
                // if you have to do action after confirm
                statusChangeFunc()
            }, function(dismiss) {
                // if you have to do action after Cancel
                $('#modal_order_status_method').modal('hide');
            });
        } else {
            statusChangeFunc();
        }

    }

    function refreshUpdateStatusForm() {
        $('#update_status').html(' تعديل حالة الطلب');
        $('#update_status').attr('disabled', false);
    }

    function mustConverOrdertoComplete() {
        $.alert({
            title: 'تنبيه',
            content: " الرجاء تغيير حالة الطلب إلى (تم التنفيذ) لكي يتم إرسال الطلب إلى شركة الشحن ثم طباعة البوليصة",
        });
    }

    @inject('feature', 'Salla\FeatureRules\Facades\FeaturesManager')
    $(".order-wherehouse").click(function(e) {
        e.preventDefault();
        @if (!$feature::accessible('order-warehouse-invoice'))
            swal({
                title: 'تحذير',
                text: "{{ $feature::getMessage('order-warehouse-invoice') }}",
                type: 'error'
            });
        @else
            window.open($(this).data('url'), '_blank',
                'toolbar=yes, scrollbars=yes, resizable=yes, width=900, height=700');
        @endif
    });

    $('#issue-policy-modal').on('shown.bs.modal', () => {
        $('#issue-policy-modal .branches-dropdown').show();
        renderCompaniesOptions($("#policy_companies_options"));
    });

    $(document).on('click', '.policy_issue', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const isDemoOrder = $(this).data('demo-order');
        const matchInternationalRulesString = $(this).data('match-international-rules');
        const matchInternationalRules = parseInt((matchInternationalRulesString))

        if (isDemoOrder) {
            swal({
                title: 'لا يمكن إصدار بوليصة لطلب تجريبي',
                width: 400,
                confirmButtonText: 'موافق'
            });
        } else if (matchInternationalRules === 1) {
            showInternationalShippingWarning(url, 'policy_issue');
        } else {
            performPolicyIssue(url);
        }
    });

    $(document).on('click', '.policy_issue_without_options', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const matchInternationalRulesString = $(this).data('match-international-rules');
        const matchInternationalRules = parseInt((matchInternationalRulesString))

        if (matchInternationalRules === 1) {
            showInternationalShippingWarning(url, 'policy_issue_without_options');
        } else {
            performPolicyIssueWithoutOptions(url);
        }
    });

    function showInternationalShippingWarning(url, scenario, order_shipment_branch_id) {
        const swalConfig = {
            title: "تأكد من استيفاء المنتجات لشروط الشحن الدولي",
            html: "يلزم إضافة رمز التنسيق الجمركي وسعر أعلى من 0 ر.س ووصف واضح لكل منتج في الشحنة لتفادي إرجاعها.<br>اعرف المزيد في <a class='text-underline' href='https://help.salla.sa' target='_blank'>مركز المساعدة</a>.",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-success",
            confirmButtonText: "استمرار بإصدار البوليصة",
            cancelButtonText: "العودة"
        };

        swal(swalConfig).then(function() {
            if (scenario === 'policy_issue') {
                performPolicyIssue(url); // Handle policy_issue case
            } else if (scenario === 'return_policy') {
                performReturnPolicyIssue(url, order_shipment_branch_id); // Handle return_policy case
            } else if (scenario === 'return_policy_single_shipment') {
                performReturnPolicyIssueSingle(url); // Handle return_policy case for single shipment
            } else if (scenario === 'policy_issue_without_options') {
                performPolicyIssueWithoutOptions(url); // Handle policy_issue has no options case
            }
        }).catch(function() {});
    }

    function performPolicyIssue(url) {
        $('#modal_change_order_status_div').html(''); //reset model change order status div
        $('#modal_policy_div').html(''); //reset model issue policy div

        showLoading();
        $.ajax({
            url: url,
            type: "GET",
            success: function(data) {
                $('#modal_policy_div').html(data);
                $("#issue-policy-modal").modal();
                //$("#companies_options").html(''); //on change order status model
                $('#issue-policy-modal .branches-dropdown').show();
                hideLoading();
            },
            error: function(data) {
                swal('تنبيه', 'حصل خطأ غير متوقع، برجي المحاولة لاحقآ', 'error');
            },
        });
    }

    function showReturn(event) {
        event.preventDefault();
        const button = event.currentTarget;

        const matchInternationalRulesString = button.dataset.matchInternationalRules;
        const matchInternationalRules = parseInt((matchInternationalRulesString))
        const url = "{{ route('cp.orders.police.return.form', ['order' => $order->getRouteKey()]) }}";

        if (matchInternationalRules === 1) {
            showInternationalShippingWarning(url, 'return_policy_single_shipment');
        } else {
            performReturnPolicyIssueSingle(url);
        }
    }

    function performPolicyIssueWithoutOptions(url) {
        $.ajax({
            url: url,
            type: "POST",
            success: function(data) {
                laravel.ajax.successHandler(data);
            },
            error: function(error) {
                laravel.ajax.errorHandler(error);
            },
        });
    }

    function performReturnPolicyIssueSingle(url) {
        $('#modal_policy_div').html('');
        showLoading();

        $.ajax({
            type: "GET",
            url: url,
            success: function(data) {

                $('#modal_policy_div').html(data);
                $("#return_policy").modal();

                $('#return_policy .branches-dropdown').show();
                renderCompaniesOptions($("#policy_companies_options"), null, 'return');

                hideLoading();

            },
            error: function(data) {
                swal('تنبيه', 'حصل خطأ غير متوقع، برجي المحاولة لاحقآ', 'error');
            },
        });
    }

    /** MultiShipments 📦 🚚 🚚*/

    $(document).on('click', '.showBranchShipping', function(e) {
        e.preventDefault();

        $('#modal_policy_div').html('');

        let url = $(this).attr('href');

        showLoading(e);

        $.ajax({
            type: "GET",
            url: url,
            success: function(data) {

                if (data.case) {
                    swal('تنبيه', data.message, 'error');
                } else {

                    $('#modal_policy_div').html(data);
                    $("#modal_block").modal();

                }
                hideLoading();
            }
        });
    });

    $(document).on('click', '.track_shipment_status', function(e) {
        e.preventDefault();

        let url = $(this).attr('href');

        showLoading();
        $.ajax({
            type: "POST",
            url: url,
            data: {
                _token: _token
            },
            success: function(data) {
                var message = "هذه الخاصية غير متاحة";
                if (data) {
                    if (data.message) {
                        message = data.message;
                    }
                }

                swal({
                    title: "{{ trans('global.alert') }}",
                    text: message,
                    type: 'success',
                    confirmButtonText: "تأكيد",
                });
            },
            complete: function() {
                hideLoading();
            }
        });

    });

    $(document).on('click', '.return_policy_issue', function(e) {
        e.preventDefault();

        const url = $(this).attr('href');
        const order_shipment_branch_id = $(this).attr('data-order-shipment-branch-id');
        const matchInternationalRulesString = $(this).data('match-international-rules');
        const matchInternationalRules = parseInt((matchInternationalRulesString))

        if (matchInternationalRules === 1) {
            showInternationalShippingWarning(url, 'return_policy', order_shipment_branch_id);
        } else {
            performReturnPolicyIssue(url, order_shipment_branch_id);
        }
    });

    function performReturnPolicyIssue(url, order_shipment_branch_id) {
        $('#modal_policy_div').html('');
        showLoading();

        $.ajax({
            url: url,
            type: "GET",
            success: function(data) {
                if (data.case) {
                    swal('تنبيه', data.message, 'error');
                } else {
                    $('#modal_policy_div').html(data);
                    $("#return_policy").modal();

                    $('#return_policy .branches-dropdown').show();
                    renderCompaniesOptions($("#policy_companies_options"), order_shipment_branch_id);
                }
                hideLoading();
            }
        });
    }

    $(document).on('click', '.update_shipment_status', function(e) {
        e.preventDefault();

        $('#modal_policy_div').html('');

        let url = $(this).attr('href');

        showLoading(e);

        $.ajax({
            url: url,
            type: "GET",
            success: function(data) {

                if (data.case) {
                    swal('تنبيه', data.message, 'error');
                } else {

                    $('#modal_policy_div').html(data);
                    $("#modal_branch_shipment_status_method").modal();

                }
                hideLoading();
            }
        });
    });
    $(document).on('change', '.branches-dropdown select', function() {
        let branch_id = $(this).val(),
            selected = $(this).find('option:selected'),
            orderShipmentBranchId = selected.data('order-shipment-branch-id');

        if ($(this).parents().hasClass('changePickupBranches')) {
            renderCompaniesOptions($("#companies_options"), orderShipmentBranchId, 'shipment', branch_id);
        } else {
            renderCompaniesOptions($("#policy_companies_options"), orderShipmentBranchId, 'shipment',
                branch_id);
        }
    });

    $(document).on('change', '.drop_off_option', function(e) {
        e.preventDefault();

        let enable_dropoff = $(this).is(':checked'),
            orderShipmentBranchId = $(this).attr('data-order-shipment-branch-id'),
            isMultiShipments = $(this).attr('data-is-multi-shipments'),
            selectedBranchId = $('.branches-dropdown select').find('option:selected'),
            branchId = selectedBranchId.val();

        if (!branchId) {
            branchId = $(this).data('branch-id');
        }

        if (enable_dropoff && $(`#drop_off_offices_list_${orderShipmentBranchId}`).children().length > 0) {
            const parentOfficess = $(`#drop_off_offices_list_${orderShipmentBranchId}`);
            let radioInput = parentOfficess.find('input[type="radio"]');
            radioInput.prop("checked", false);
            $(`#drop_off_offices_${orderShipmentBranchId}`).slideDown(250);
        } else if (enable_dropoff) {
            $(`#drop_off_offices_list_${orderShipmentBranchId}`).empty();
            fetchData(branchId, orderShipmentBranchId, isMultiShipments, 1);
        } else {
            $(`#drop_off_offices_${orderShipmentBranchId}`).slideUp(250);
        }
    });

    function fetchData(branchId = 0, orderShipmentBranchId = 0, isMultiShipments = 0, page = 1) {
        let url =
            '{{ route('cp.orders.dropOff_offices', ['order' => $order->getRouteKey(), 'orderShipmentBranch' => ':orderShipmentBranchId']) }}'
            .replace(':orderShipmentBranchId', orderShipmentBranchId);

        const officesScroll = $(`#drop_off_offices_scroll_${orderShipmentBranchId ?? 0}`);
        const currentScrollTop = officesScroll.scrollTop();

        $.ajax({
            url: `${url}?page=${page}`,
            type: 'POST',
            cache: false,
            dataType: 'json',
            data: {
                branch_id: branchId,
                is_multi_shipments: isMultiShipments
            },
            success: function(data) {
                $('#drop_off_offices_scroll_' + orderShipmentBranchId ?? 0).data("next-page", (!data
                    .onLastPage ? data.nextPage : null));
                $('#drop_off_offices_list_' + orderShipmentBranchId ?? 0).append(data.offices);
                officesScroll.scrollTop(currentScrollTop); // restore scroll position
                $(`#drop_off_offices_${orderShipmentBranchId ?? 0}`).slideDown(250);

                scrollAttachEvent();

                laravel.ajax.successHandler(data);
            }
        });
    }

    function enableSubmitButton() {
        const enableDropoff = $('.drop_off_option').is(':checked');
        const selectedBranch = $("input[name='drop_off[office_id]']:checked");
        const isCompanyPickupCityNotSupported = $("input[name='drop_off[is_required]']").val();

        if ((enableDropoff && selectedBranch.length > 0) || (!enableDropoff && !isCompanyPickupCityNotSupported)) {
            $('#issue-policy-submit').prop('disabled', false);
        } else {
            $('#issue-policy-submit').prop('disabled', true);
        }
    }

    function scrollAttachEvent() {
        setTimeout(() => {
            $("[id^='drop_off_offices_scroll_']").off('scroll').on('scroll', function() {
                let officesScroll = $(this),
                    orderShipmentBranchId = officesScroll.data(
                        "order-shipment-branch-id"),
                    isMultiShipments = $(this).attr('data-is-multi-shipments'),
                    page = officesScroll.data("next-page"),
                    selectedBranchId = $('.branches-dropdown select').find(
                        'option:selected'),
                    branchId = selectedBranchId.val();
                if (!branchId) {
                    branchId = officesScroll.data("branch-id");
                }
                if (page && officesScroll.scrollTop() + officesScroll.innerHeight() >= officesScroll[0]
                    .scrollHeight) {
                    fetchData(branchId, orderShipmentBranchId, isMultiShipments, page);
                }
            });
        }, 100);
    }

    $(document).on('click', '.send-invoice', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        let token = localStorage.getItem("token"),
            storeId = null;
        // console.log(typeof token === 'object' && token !== null, token.key)
        if (token !== null) {
            let Ptoken = JSON.parse(token);
            token = Ptoken.key;
            storeId = Ptoken.id;
        }
        console.log(token)
        showLoading();
        $.ajax({
            type: "POST",
            url: url,
            headers: {
                'Authorization': "Bearer " + token,
                'X-Api-Key': token,
                'S-Store-Id': storeId,
                'Content-Type': 'application/json'
            },
            dataType: "json",
            processData: false,
            contentType: false,
            context: {
                url: url
            },
            success: laravel.ajax.successHandler,
            error: laravel.ajax.errorHandler,
        });
    });
</script>
<script>
    function cancelDelayPolicy(event) {
        event.preventDefault();
        showLoading();
        const token = window.token;
        axios.post(this.dataset.url, {}, {
            headers: {
                's-store-id': `${token.id}`,
                'X-API-KEY': `${token.key}`
            }
        }).then(function({
            response
        }) {
            hideLoading();
            location.reload();
        }).catch(function({
            response
        }) {
            swal('خطا', "حصل خطأ يرجي المحاولة مرة أخري", 'error');
        });
    }
    
    function downloadQrCode(target) {
        fetch(target.href)
            .then(response => response.blob())
            .then(blob => {
                var link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'Redbox-self-dropoff-code.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
            .catch(error => console.error('Error downloading the image:', error));
    }

    $(document).on('click', '.cancel-delay-policy', cancelDelayPolicy);
</script>