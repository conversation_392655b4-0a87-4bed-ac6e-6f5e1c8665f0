@extends('cp.layouts.master')
@section('content')
@include('cp.layouts.error')

<!-- @if($withStore)
<a href="{{route('cp.marketplace.index')}}" class="salla-store-box"><i class="sicon-store"></i> متجر سلة</a>
@endif -->
<a href="@if(app('store')->getSetting('help_center_enabled')) {{store()->helpCenterBaseUrl()}} @endif" @if(! $withStore) style="width: 100%; margin-bottom: 27px" @endif class="salla-support-box"><i class="sicon-heart"></i> مركز المساعدة</a>
<div class="clr"></div>

{{ Widget::group('dashboard::menu')->display() }}

<div class="section-container">
    <a href="{{route('cp.customers')}}" class="setting-box">
        <i class="sicon-users"></i>
        <p>العملاء</p>
    </a>

    @if(
             Auth::user()->checkPermission('report_sales') ||
             Auth::user()->checkPermission('report_orders') ||
             Auth::user()->checkPermission('report_customers') ||
             Auth::user()->checkPermission('report_customer_cities') ||
             Auth::user()->checkPermission('report_customer_satisfaction') ||
             Auth::user()->checkPermission('report_visits') ||
             Auth::user()->checkPermission('report_repeat_sell') ||
             Auth::user()->checkPermission('report_average_cart') ||
             Auth::user()->checkPermission('report_most_product_sell') ||
             Auth::user()->checkPermission('report_most_product_search') ||
             Auth::user()->checkPermission('report_most_customer_paid') ||
             Auth::user()->checkPermission('report_most_customer_request') ||
             Auth::user()->checkPermission('report_restore') ||
             Auth::user()->checkPermission('report_most_day_request') ||
             Auth::user()->checkPermission('report_most_hour_request') ||
             Auth::user()->checkPermission('report_source_visit') ||
             Auth::user()->checkPermission('report_device') ||
             Auth::user()->checkPermission('report_sold_out_codes') ||
             Auth::user()->checkPermission('special_offers_statistics') ||
             Auth::user()->checkPermission('report_loyalty_system')
           )
                <a href="{{route('cp.reports')}}" class="setting-box">
                    <i class="sicon-chart-pie"></i>
                    <p>التقارير</p>
                </a>
    @endif

    @if(auth()->user()->checkPermission('store_setting_purchase_fromـsalla_store') 
         && !mobileAgent()->isAndroid()
            && (
                !mobileAgent()->isIos()
                || mobileAgent()->campareVersion('5.0.0')
            )
            ) 
            <a href="{{route('cp.marketplace.index')}}" class="setting-box">
                        <i class="sicon-store"></i>
                <p>باقة المتجر</p>
            </a>
                
    @endif

    @if( app('store')->getCurrent()->showLinkEPayment() )
        <a href="{{route('cp.payment.history.index')}}" class="setting-box">
            <i class="sicon-wallet"></i>
            <p>المدفوعات</p>
        </a>
    @endif

    <a href="{{route('cp.marketing.marketing_tools')}}" class="setting-box">
            <i class="sicon-megaphone"></i>
            <p>التسويق</p>
    </a>

    <a href="{{route('cp.shipping')}}" class="setting-box">
            <i class="sicon-shipping"></i>
            <p>الشحن</p>
    </a>

</div>

<div class="section-header">قنوات الدفع</div>
<div class="section-container">
    @if(feature(\Modules\MahlyDash\Features\MahlyFeature::getName())->isReleased())
    <a href="{{route('mahly.index')}}" class="setting-box">
            <i class="sicon-iphone"></i>
            <p>محلي</p>
    </a>
    @endif
</div>

<div class="section-header">أدوات مساندة</div>
<div class="section-container">
    @if(feature()->accessible('expert-marketplace'))
    <a href="{{route('cp.expert.index')}}" class="setting-box">
            <i class="sicon-briefcase"></i>
            <p>خدمات التاجر</p>
    </a>
    @endif
    @if(feature()->accessible('influencer-marketplace'))
        <a href="{{ route('cp.marketing.influencers') }}" class="setting-box">
            <i class="sicon-user-list"></i>
            <p>المؤثرين</p>
        </a>
    @endif
    @if(mobileAgent()->isAndroid() && auth()->user()->checkPermission('marketplace_apps_management') && feature()->accessible('salla-portal-marketplace'))
    <a href="{{route('cp.marketplace.apps.index')}}" class="setting-box">
            <i class="sicon-puzzle"></i>
            <p>التطبيقات المثبتة</p>
    </a>
    @endif
</div>

<div class="section-header">الاعدادات</div>
<div class="section-container">
    @if(auth()->user()->checkPermission('store_setting_browse'))
    <a href="{{route('cp.settings')}}" class="setting-box">
            <i class="sicon-settings"></i>
            <p>إعدادات المتجر</p>
    </a>
    @endif
    <a href="{{route('cp.profile')}}" class="setting-box">
        <i class="sicon-user"></i>
        <p>الملف الشخصي</p>
    </a>

    <a href="{{route('cp.app.alerts')}}" class="setting-box">
        <i class="sicon-notification"></i>
        <p>التنبيهات</p>
    </a>
</div>
<!-- <div class="section-header">المزيد</div>
<div class="section-container">
</div> -->

<div class="section-header">مظهر المتجر</div>
<div class="section-container">
    <a href="{{route('cp.new-dashboard')}}" class="setting-box">
        <i class="sicon-window-layout"></i>
        <p>تصميم المتجر</p>
    </a>
</div>

<div class="section-header">عام</div>
<div class="section-container">
    <a href="{{route('cp.app.contact-us')}}" class="setting-box">
      <i class="sicon-phone"></i>
      <p>تواصل معنا</p>
    </a>
    <a target="_self"
       class="setting-box"
       data-salla-click-event="mobile::share"
       data-message=" صمم متجرك على منصة #سلة واحصل على أسعار شحن مخفضة لعملائك وخدمات دفع إلكتروني وأدوات تسويق وتقارير واحصائيات مالية وأكثر! بخصم خاص عبر الرابط:"
       data-shared-link="{{app('store')->getReferralUrl()}}"
       href="#">
        <i class="sicon-dollar-coin-stack text-bottom" style="display: inline-block;font-size: 20px;"></i>
        <p style="display: block; font-size: 14px;">انشر واحصل على رصيد مجاني</p>
    </a>
    <a href="#native"
       click-event="mobile::rating"
       class="setting-box">
        <i class="sicon-star text-bottom" style="display: inline-block;font-size: 20px;"></i>
        <p style="display: block; font-size: 14px;">قيّم سلة في متجر التطبيقات</p>
    </a>
</div>


{{--<div class="section-header">انشر واحصل على رصيد مجاني 💰</div>
<div class="section-container">
    <a href="mailto:<EMAIL>" class="setting-box">
        <i class="sicon-whatsapp"></i>
        <p>واتس آب</p>
    </a>
    <a href="mailto:<EMAIL>" class="setting-box">
        <i class="sicon-paper-plane-o"></i>
        <p>تيليجرام</p>
    </a>
    <a href="mailto:<EMAIL>" class="setting-box">
        <i class="sicon-twitter"></i>
        <p>تويتر</p>
    </a>
</div>--}}
<div class="section-container">
<a href="{{route('cp.logout')}}" class="setting-box" style="width: 100%;"> 
    <i class="sicon-send-out"></i>
    <p>تسجيل الخروج</p>
</a>
</div>
{{--<div class="section-container">
    <a href="#native"
       click-event="mobile::rating"
       class="wide setting-box">
        <i class="sicon-star2 text-bottom"></i>
        <p>قيّم سلة في متجر التطبيقات</p>
    </a>
</div>--}}

<div class="desktop-version">
    <p>لإدارة المتجر من جهاز الكمبيوتر عبر الرابط التالي:</p>
    <p class="desktop-link">https://s.salla.sa</p>
</div>
@endsection
