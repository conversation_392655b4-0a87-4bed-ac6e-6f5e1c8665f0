<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=1"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta name="theme-color" content="#003d47"/>
    <meta name="msapplication-navbutton-color" content="#003d47"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="#003d47"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
    <title>لوحة تحكم المتجر | سلة</title>
    <meta name="description"
          content="سلة توفر لك حلول رقمية متكاملة وذكية لتبدأ تجارتك الإلكترونية بسهولة وأمان، من السوق المحلي وحتى العالمية. استمتع بربط منتجاتك بحلول احترافية للمدفوعات،"/>
    <meta property="og:image" content="https://cdn.salla.network/salla.com/salla-logos.png"/>
    <meta name="robots" content="index, follow, "/>
    <meta name="googlebot" content="index=yes"/>
    <meta name="googlebot" content="follow=yes"/>
    <meta name="googlebot" content="noimageindex=no"/>
    <meta name="googlebot" content="max-video-preview=-1"/>
    <meta name="googlebot" content="max-image-preview=large"/>
    <meta name="googlebot" content="max-snippet=-1"/>
    <link rel="icon" href="https://cdn.salla.network/images/logo/logo-square.png" media="(prefers-color-scheme: light)"/>
    <link rel="icon" href="https://cdn.salla.network/images/logo/logo-light-square.png"
          media="(prefers-color-scheme: dark)"/>
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:title" content="حلق نحو مستقبل التجارة عبر إنشاء متجرك الإلكتروني | سلة"/>
    <meta name="twitter:description" content="امتلك متجرًا إلكترونيًا مع سلّة – المنصة الأولى في التجارة الإلكترونية"/>
    <meta name="twitter:creator" content="@SallaApp"/>
    <meta name="twitter:image" content="https://cdn.salla.network/images/logo/logo-wide.png"/>
    <link rel="stylesheet" href="https://cdn.salla.network/fonts/pingarlt.css"/>
    <link rel="stylesheet" href="https://cdn.salla.network/fonts/hugeicons-font.min.css"/>
    <link rel="stylesheet" href="https://cdn.salla.network/fonts/sallaicons-light.min.css?v=0.1"/>
    @php $isDevIp = in_array(request()->ip(), ['**************', '***************'], true); @endphp
    @if($isDevIp)
       <meta http-equiv="Content-Security-Policy" content="
  default-src 'self' https: http://localhost:*;
  script-src 'unsafe-inline' 'unsafe-eval' https: http://localhost:* blob:;
  connect-src https: http://localhost:* ws://localhost:* wss://ws.salla.cloud wss://notifications.ws.salla.dev wss://nexus-websocket-a.intercom.io blob: data:;
  style-src 'unsafe-inline' https: http://localhost:*;
  object-src 'none';
  img-src 'self' data: blob: https: http://localhost:*;
  media-src 'self' blob: https: http://localhost:*;                
  worker-src blob: https: http://localhost:*;
  font-src 'self' data: *;">
        @else
        <meta http-equiv="Content-Security-Policy" content="
  default-src 'self' https:;
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https:;
  connect-src 'self' http://localhost:* ws://localhost:* https: blob: data: wss://ws.salla.cloud wss://notifications.ws.salla.dev wss://nexus-websocket-a.intercom.io wss://ws.hotjar.com;
  style-src 'self' 'unsafe-inline' https:;
  object-src 'none';
  img-src 'self' data: blob: https:;
  worker-src 'self' blob: https:;
  font-src 'self' data: *;
  media-src 'self' blob: https: http://localhost:*;                 
  form-action 'self' https:;">
    @endif
    <meta name="importmap-type" content="systemjs-importmap"/>
    @unless($isDevIp)
    <!-- Google Tag Manager -->
    <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-T3L5JL9F');
    </script>
    <!-- End Google Tag Manager -->
     @endunless
</head>
<body dir="rtl">
    @unless($isDevIp)
    <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T3L5JL9F"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
     @endunless

<div id="header" style="display: contents;"></div>
<div id="root" style="display: contents;"></div>
<div id="app" style="display: contents;"></div>
<noscript> You need to enable JavaScript to run this app.</noscript>
<!-- sentry -->
<script src="https://browser.sentry-cdn.com/9.20.0/bundle.min.js" integrity="sha384-H7O6Dw7Q1CI5HrPxNsyBuntQgyosNqQbAPPsaA2uUfE6GaIja89XV5L+H1iCwf6E" crossorigin="anonymous"></script>   
<!-- sentry -->
<script src="https://cdn.jsdelivr.net/npm/regenerator-runtime@0.13.7/runtime.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.15.1/system.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.15.1/extras/amd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.15.1/extras/named-register.min.js"></script>
<script type="systemjs-importmap" src="https://api.salla.dev/ui/v1/apps/prod/dashboard/importmap"></script> 
<!-- Cloudflare recaptcha -->    
<script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
<!-- Cloudflare recaptcha -->  
@if($isDevIp)
    <script src="https://cdn.jsdelivr.net/npm/import-map-overrides@2.2.0/dist/import-map-overrides.js"></script>
    <script src="https://cdn.assets.salla.network/latest/@salla.sa/ui-dev-tool-widget/ui-dev-tool-widget.umd.js"></script>
    <ui-dev-tool-widget trigger-position="bottom-right" dev-libs="true"></ui-dev-tool-widget>
@endif
<script>System.import('@salla.sa/dashboard');</script>
</body>
</html>
