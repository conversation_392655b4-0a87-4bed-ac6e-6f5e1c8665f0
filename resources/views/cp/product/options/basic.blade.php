@php
    /** @var \Modules\Product\Entities\Product|\App\Models\Product $product  */
    use Modules\Product\Enum\ProductType;
@endphp
<style>
    .text-right {
        text-align: right !important;
    }

    .disabled_mode {
        color: #999999;
        user-select: none;
        pointer-events: none
    }

    .modal#modal_product_options:has(.disabled_mode) .form-group label {
        color: #999999;
    }

    .disabled_mode .checker span {
        border-color: #999999
    }

    .has-error .btn-group.bootstrap-select.input-group-btn {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }
</style>
@if ($product->canHasWeight)
    <div class="row product-data-row {{ $product->type }}">
        <div class="col-xs-12">
            <div class="form-group">
                <label>
                    يتطلب شحن / توصيل ؟
                    @widget('features::showPlanBadge')
                </label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"><i class="sicon-shipping"></i></span>
                    <select class="bootstrap-select bootstrap-select--initial" id="require_shipping"
                        name="require_shipping" style="width: 100%" @if (\Auth::user()->store->plan == 'basic') disabled @endif>
                        <option value="1" @if ($product->require_shipping == 1) selected="selected" @endif>نعم، يتطلب
                            شحن
                        </option>
                        <option value="0" @if ($product->require_shipping == '0') selected="selected" @endif>لا يتطلب
                            شحن
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>


    <div class="row product-data-row" id="product_weight_div">
        <div class="col-xs-12">
            <div class="form-group" id="product_weight_field">
                <label>
                    وزن المنتج
                    <small class="d-block text-muted text-muted-smaller">
                        أدخل الوزن الفعلي للمنتج ليتم معالجته بشكل صحيح من قبل شركة الشحن، وفي حال كانت منتجاتك ذات حجم
                        كبير الرجاء الانتباه للوزن الحجمي
                        <a href="https://help.salla.sa/article/1478470046" class="text-underline" target="_blank">تفاصيل
                            أكثر</a>
                    </small>
                </label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"> <i class="sicon-luggage-cart"></i> </span>
                    <input type="text" id="product_weight" name="weight" class="form-control"
                        placeholder="وزن المنتج" value="{{ $product->getRealWeight(false) }}"
                        onkeyup="parseArabicNumbers('product_weight')">
                    <div class="input-group-addon no-padding">
                        <select class="bootstrap-select bootstrap-select--no-border pl-10" title="الوزن"
                            data-width="100px" name="weight_type" id="product_weight_type">
                            @foreach (\Salla\Core\Enum\WeightTypeEnum::choices() as $weightKey => $weightType)
                                <option value="{{ $weightKey }}" @if ($weightKey == $product->getWeightType()) selected @endif>
                                    {{ $weightType }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <small id="weight_error_message" class="help-block message-helpers help-tags"
                    style="{{ !$product->getRealWeight(false) ? '' : 'display:none' }}">
                    قيمة الوزن غير صحيحة
                </small>
            </div>
        </div>
    </div>
@else
    <input type="hidden" id="require_shipping" name="require_shipping" value="0" />

@endif

@if ($product->type === ProductType::FOOD)
    <div class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>السعرات الحرارية</label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"> <i class="sicon-fire"></i> </span>
                    <input type="text" id="product_calories" name="calories" class="form-control _parseArabicNumbers"
                        placeholder="السعرات الحرارية" value="{{ $product->calories }}">

                </div>
            </div>
        </div>
    </div>
@endif

@if ($product->type === ProductType::BOOKING && !$product->booking()->exists())
    <section class="alert-box alert-box--warning">
        <i class="sicon-warning"></i>
        <article>
            لابد من إضافه جدول الحجوزات ﻹظهار المنتج
        </article>
    </section>
@endif
@if (!(ProductType::isDonatingProduct($product->type)) && auth()->user()->checkPermission('products_management'))
    <div class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>سعر التكلفة @widget('features::showPlanBadge')</label>
                <div class="input-group" title="سعر التكلفة">
                    <span class="input-group-addon input-group-addon-small">
                        @if (\Auth::user()->store->plan == 'basic')
                            <i class="sicon-lock"></i>
                        @else
                            <i class="sicon-coin-dollar"></i>
                        @endif
                    </span>
                    <input type="text" id="cost_price" name="cost_price"
                        class="form-control cost_price _parseArabicNumbers" placeholder="سعر التكلفة"
                        value="{{ $product->cost_price }}" @if (\Auth::user()->store->plan == 'basic') readonly="readonly" @endif>
                </div>
            </div>
        </div>
    </div>

    <div class="hidden row">
        <div class="col-xs-12">
            <div class="form-group">
                <input type="number" class="form-control full-bordered" id="current_product_price" readonly
                    value="{{ $product->price }}" />
            </div>
        </div>
    </div>

    <div class="row product-data-row">
        <div class="col-xs-6">
            <div class="form-group">
                <label>السعر المخفض</label>
                <div class="input-group" title="السعر المخفض">
                    <span class="input-group-addon input-group-addon-small"><i
                            class="sicon-special-discount"></i></span>
                    <input type="text" id="product_sale_price" name="sale_price"
                        class="form-control product_sale_price _parseArabicNumbers" placeholder="السعر المخفض"
                        value="{{ $product->sale_price }}">
                </div>
            </div>
        </div>

        <div class="col-xs-6">
            <div class="form-group">
                <label>نهاية التخفيض</label>
                <div class="input-group" title="نهاية التخفيض">
                    <span class="input-group-addon input-group-addon-small"><i class="sicon-calendar"></i></span>
                    <input type="text" id="product_end_sale" name="sale_end" class="form-control"
                        value="{{ isset($product->sale_end) && $product->sale_end !== '1970-01-01' ? $product->sale_end : '' }}"
                        placeholder="نهاية التخفيض (اختياري)">
                </div>
            </div>
        </div>
    </div>
@endif

@if (ProductType::IsFINANCIAL_SUPPORT($product->type))
    @php $financialSupportPeriod = $product->donation?->target; @endphp
    <div class="row product-data-row">
        <div class="row product-data-row hidden" id="financial-period-info">
            <div class="col-xs-12">
                <div class="alert-box alert-box--info">
                    <i class="sicon-info font-20"></i>
                    <article>
                        <p>عند الضغط على "حفظ" لن تستطيع اختيار مدة أقل من المدة المحددة مسبقا</p>
                    </article>
                </div>
            </div>
        </div>
        <div class="col-md-5 col-xs-12">
            <div class="form-group">
                <label>مدة الكفالة</label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"><i class="sicon-box"></i></span>
                    <select class="bootstrap-select" id="financial_support_period" name="financial_support_period"
                        style="width: 100%">
                        <option value="3" @if (!$financialSupportPeriod || $financialSupportPeriod == 3) selected="selected" @endif>
                            3 شهور
                        </option>
                        <option value="6" @if ($financialSupportPeriod == 6) selected="selected" @endif>6 شهور
                        </option>
                        <option value="12" @if ($financialSupportPeriod == 12) selected="selected" @endif>12 شهر
                        </option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-xs-12">
            <div class="form-group">
                <label>مبلغ الكفالة الشهرى</label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"> <i class="sicon-coin-dollar"></i> </span>
                    <input type="number" id="financial_support_price" min="1" name="financial_support_price"
                        class="form-control" placeholder="أدخل مبلغ الكفالة في الشهر الواحد"
                        value="{{ $product->cost_price }}">
                </div>
            </div>
        </div>
        <div class="col-md-3 col-xs-12">
            <div class="form-group">
                <label class="font-13 mb-20"></label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"></span>
                    <input type="text" id="financial_support_total" name="financial_support_total"
                        class="form-control" placeholder="الإجمالى: 0 ر.س" disabled="true" readonly>
                </div>
            </div>
        </div>
    </div>
@endif

@if ($product->type != 'booking')
    <div class="row product-data-row">
        <div @if ($product->canHaveMpnAndGtin && !ProductType::IsFINANCIAL_SUPPORT($product->type)) class="col-md-4 col-xs-12" @else class="col-xs-12" @endif>
            <div class="form-group">
                <label>رمز التخزين
                    @widget('features::showPlanBadge')
                </label>
                <div class="input-group" title="رمز التخزين SKU">
                    <span class="input-group-addon input-group-addon-small">
                        @if (\Auth::user()->store->plan == 'basic')
                            <i class="sicon-lock"></i>
                        @else
                            <i class="sicon-barcode"></i>
                        @endif
                    </span>
                    <input type="text" dir="ltr" id="sku" name="sku"
                        class="text-right form-control" placeholder="رمز التخزين SKU" value="{{ $product->sku }}"
                        @if (\Auth::user()->store->plan == 'basic') readonly="readonly" @endif>
                </div>
            </div>
        </div>
        @if ($product->canHaveMpnAndGtin && !ProductType::IsFINANCIAL_SUPPORT($product->type))
            <div class="col-md-4 col-xs-12">
                <div class="form-group">
                    <label>MPN</label>
                    <div class="input-group">
                        <span class="input-group-addon input-group-addon-small"> <i class="sicon-barcode"></i> </span>
                        <input type="text" dir="ltr" id="mpn" name="mpn"
                            class="text-right form-control" placeholder="MPN" value="{{ $product->mpn }}">
                    </div>
                </div>
            </div>
            <div class="col-md-4 col-xs-12">
                <div class="form-group">
                    <label>GTIN</label>
                    <div class="input-group">
                        <span class="input-group-addon input-group-addon-small"> <i class="sicon-barcode"></i> </span>
                        <input type="text" id="gtin" name="gtin" class="form-control" placeholder="GTIN"
                            value="{{ $product->gtin }}">
                    </div>
                </div>
            </div>
        @endif
    </div>
@endif

@if ($product->canHasWeight)
    <div class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>
                    أقصى كمية لكل عميل
                    @widget('features::showPlanBadge')
                </label>
                <div class="input-group" title="أقصى كمية لكل عميل ">
                    <span class="input-group-addon input-group-addon-small">
                        @if (\Auth::user()->store->plan == 'basic')
                            <i class="sicon-lock"></i>
                        @else
                            <i class="sicon-user"></i>
                        @endif
                    </span>
                    <input type="text" id="maximum_quantity_per_order" name="maximum_quantity_per_order"
                        class="form-control maximum_quantity_per_order _parseArabicNumbers"
                        placeholder="أقصى كمية لكل عميل " value="{{ $product->maximum_quantity_per_order }}"
                        @if (\Auth::user()->store->plan == 'basic') readonly="readonly" @endif>
                </div>
            </div>
        </div>
    </div>
@endif

@if (store()->getSetting('products::hs-code-enabled', false))
    <div id="product_hs_code_div" class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>
                    رمز التنسيق الجمركي (HS Code)
                    <small class="d-block text-muted text-muted-smaller">
                        يتكون الرمز من أرقام فقط ويكون مطلوباً عند شحن المنتج دولياً
                        <a href="https://help.salla.sa/article/1502230829" class="text-underline"
                            target="_blank">اعرف المزيد</a> </small>
                </label>
                <div class="input-group" title="رمز التنسيق الجمركي (HS Code)">
                    <span class="input-group-addon input-group-addon-small">
                        <i class="sicon-user"></i>
                    </span>
                    <input type="text" id="product_hs_code" name="extra_attributes[hs_code]"
                        class="form-control" placeholder="رمز التنسيق الجمركي (HS Code)"
                        value="{{ $product->hs_code ?? '' }}">
                </div>
            </div>
        </div>
    </div>
@endif

@if (feature()->isActive('manage-products-by-branches') &&
        isset($branches) &&
        !ProductType::IsFINANCIAL_SUPPORT($product->type))
    <div class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">

                <label>إظهار المنتج في الفروع</label>
                <div class="input-group input-group--flex">
                    <span class="input-group-addon input-group-addon-small"><i class="sicon-home"></i></span>
                    <select class="bootstrap-select bootstrap-select--initial" id="branches"
                        title="إظهار المنتج في الفروع" name="branches[]" style="width: 100%" multiple="true">
                        @foreach ($branches as $branch)
                            <option class="branches" value="{{ $branch->getRouteKey() }}"
                                {{ in_array($branch->id, $productBranches) ? 'selected="true"' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <span class="help-block">إختيار الفروع التي سيظهر فيها المنتج</span>
            </div>
        </div>
    </div>
@endif

@if (
    \Salla\FeatureRules\Facades\FeaturesManager::accessibleForCustomers('brands') &&
        !ProductType::IsFINANCIAL_SUPPORT($product->type))
    @widget('product::dashboard.product_option_brands_list', ['product' => $product])
@endif

@featureReleased('promotion-title-and-subtitle')
    <div class="row product-data-row">
        <div class="col-xs-12">
            {!! $product->subTitleInput !!}
        </div>
    </div>

    <div class="row product-data-row">
        <div class="col-xs-12">
            {!! $product->promotionTitleInput !!}
        </div>
    </div>
@endfeatureReleased

@if (ProductType::IsDonating($product->type))
    @include('cp.product.options._basic_donating')
@elseif(!ProductType::IsBooking($product->type) and !ProductType::IsFINANCIAL_SUPPORT($product->type))
    <div class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>تحديد كمية المنتج
                    @widget('features::showPlanBadge')
                </label>
                <div class="input-group">
                    <span class="input-group-addon input-group-addon-small"><i class="sicon-box"></i></span>
                    <select class="bootstrap-select" id="hide_quantity" name="hide_quantity" style="width: 100%"
                        @if (\Auth::user()->store->plan == 'basic') disabled @endif>
                        <option value="0">تحديد كمية المنتج</option>
                        <option value="0" @if ($product->hide_quantity == 0) selected="selected" @endif>تفعيل
                            خيار
                            تحديد الكمية
                        </option>
                        <option value="1" @if ($product->hide_quantity == 1) selected="selected" @endif>تعطيل
                            خيار
                            تحديد الكمية
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>
@endif

@if(auth()->user()->checkPermission('products_management'))
    <div class="row product-data-row">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="mb-0">قنوات عرض المنتج</label>
                <span class="help-block mt-0">قم بتحديد قنوات عرض المنتج</span>
                @if ($product->type === ProductType::BOOKING && !$product->booking)
                    <section class="alert-box alert-box--warning">
                        <i class="sicon-warning"></i>
                        <article>
                            لابد من إضافه جدول الحجوزات ﻹظهار المنتج
                        </article>
                    </section>
                @else
                    <div class="input-group input-group--flex">
                        <span class="input-group-addon input-group-addon-small"><i class="sicon-devices"></i></span>
                        <select class="bootstrap-select bootstrap-select--initial wide" id="channel_show_in"
                            title="قم بتحديد قنوات عرض المنتج" multiple="multiple">
                            <option value="web" @if ($product->show_in_web) selected @endif>
                                اظهار في موقع المتجر
                            </option>
                            @if (store()->hasActiveMobileApps() || store()->hasPlan('special'))
                                <option value="app" @if ($product->show_in_app) selected @endif>
                                    اظهار في تطبيق المتجر
                                </option>
                            @endif
                            {{-- @if (feature(\Modules\MahlyDash\Features\MahlyFeature::getName())->isHaveFeature())
                                <option value="mahly"  @if ($product->showInMahly()) selected @endif >
                                    اظهار في تطبيق محلي
                                </option>
                            @endif --}}
                        </select>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endif

<div class="row product-data-row">
    <div class="col-xs-12">
        <div class="form-group m-0">
            <div class="checkbox m-0">
                <label>
                    <input type="checkbox" class="styled" name="enable_upload_image" id="enable_upload_image"
                        @if ($product->enable_upload_image) checked="checked" @endif value="1">
                    ارفاق ملف عند الطلب
                </label>
            </div>
        </div>
    </div>
</div>
<div class="row product-data-row">
    <div class="col-xs-12">
        <div class="form-group m-0">
            <div class="checkbox m-0">
                <label>
                    <input type="checkbox" class="styled" name="enable_note" id="enable_note"
                        @if ($product->enable_note) checked="checked" @endif value="1">
                    امكانية كتابة ملاحظة
                </label>
            </div>
        </div>
    </div>
</div>

@if (!ProductType::isDonatingProduct($product->type))
    @feature('tax')
        <div @class([
            'row',
            'product-data-row',
            'disabled_mode' => !$is_tax_included,
        ])>
            <div class="col-xs-12">
                <div class="checkbox m-0">
                    <div class="form-group m-0">
                        <label @class([
                            'mb-5' => !$is_tax_included,
                        ])>
                            <input type="checkbox" class="styled" name="productـwith_tax" id="productـwith_tax"
                                @if ($product->with_tax) checked="checked" @endif>
                            المنتج خاضع لضريبة
                        </label>
                    </div>
                </div>
                @if (!$is_tax_included)
                    <span class="help-block message-helpers mt-0">
                        <small>
                            يمكنك تفعيل خيار عرض أسعار المنتجات شاملة الضريبة من الاعدادات
                        </small>
                    </span>
                @endif
            </div>
        </div>
        @if ($is_tax_included)
            <div class="row product-data-row" id="tax-reason">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label>
                            سبب عدم خضوع المنتج لضريبة القيمة المضافة <span class="text-danger">*</span>
                            @widget('features::showPlanBadge')
                        </label>
                        <div class="input-group">
                            <span class="input-group-addon input-group-addon-small"><i class="sicon-content"></i></span>
                            <select class="bootstrap-select" id="tax_reason_code" name="tax_reason_code"
                                style="width: 100%" required title="إختر السبب">
                                {{-- <option value="">إختر السبب</option> --}}
                                @foreach ($tax_reasons as $key => $reason)
                                    <option @if ($selected_tax_reason == $key) selected="" @endif
                                        value="{{ $key }}">{{ $reason }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @endfeature
@endif




{!! $product->descriptionEditor !!}

@feature('product-tags')
    {{-- Tags Option --}}
    @widget('product::dashboard.product_option_tags', ['product' => $product])
    {{-- Tags Option --}}
@endfeature

@featureReleased('seo-meta-tag')
    @php
        $type = 'المنتج';
        $translations = $product->getTranslationsArray();
    @endphp

    <div class="form-group mb-0 p-20 bg-gray-50 border-gray-200 rounded-sm">
        <h6 class="mt-0 mb-10">
            تحسينات SEO
            @widget('features::showPlanBadge', ['feature' => 'seo-meta-tag'])
        </h6>
        <div class="seo-fields">
            @foreach (['title', 'custom_url', 'description'] as $name)
                <div class="form-group mb-20">
                    <x-seo-fields :translations="$translations" :id="$name" :type="$type" entity="products" />
                </div>
            @endforeach
            <bulk-seo-preview entity="products" item_id="{{ $product->getRouteKey() }}" slug="{{ $product->name }}"
                store_domain="{{ store()->getDomain() }}" title="{{ $product->name }}">

            </bulk-seo-preview>
        </div>
    </div>
@endfeatureReleased


<style>
    .sp-container {
        z-index: 999999999999;
    }
</style>

<script type="text/javascript">
    if ($('#productـwith_tax').length && $('#tax-reason').length) {
        const checkbox = document.getElementById('productـwith_tax');
        const taxReasonDiv = document.getElementById('tax-reason');
        // Function to toggle visibility
        function toggleTaxReason() {
            if (checkbox.checked) {
                taxReasonDiv.style.display = 'none';
            } else {
                taxReasonDiv.style.display = 'block';
            }
        }
        // Initial call to set the correct state on page load
        toggleTaxReason();
        // Add event listener to the checkbox
        checkbox.addEventListener('change', toggleTaxReason);

        $('#tax-reason .bootstrap-select').on('changed.bs.select', function(e, clickedIndex, isSelected,
        previousValue) {
            // do something...
            if ($(this).val() == '0') {
                $(this).closest('.form-group').addClass('has-error');
            } else {
                $(this).closest('.form-group').removeClass('has-error');
            }
        });

    }

    $(function() {
        toggleHsCodeInput();
    });

    const demoData = {
      'Name': {!! json_encode($product->name) !!},
      'Brand': {!! json_encode($product->brand->name ?? '') !!},
      'Category': {!! json_encode($product->category->category->name ?? '') !!},
    };

    $('#modal_product_options').on('show.bs.modal', function() {
        const seoPreviewField = document.querySelector('bulk-seo-preview');
        if (seoPreviewField) {
            seoPreviewField.demoData = demoData
        }
    });


    function toggleHsCodeInput() {
        var selected_shipping_option = $('#require_shipping').val();
        if (selected_shipping_option === '0') {
            $('#product_hs_code_div').hide();
            $('#product_hs_code').val('');
        } else {
            $('#product_hs_code_div').show();
            $('#product_hs_code').val('{{ $product->hs_code }}');
        }
    }

    $('#product_end_sale').daterangepicker({
        autoUpdateInput: false,
        singleDatePicker: true,
        opens: 'center',
        locale: {
            direction: 'rtl',
            format: 'YYYY-MM-DD'
        }
    });

    $('#product_calories').blur(function() {
        if ($(this).val() !== '') {
            let num = parseFloat($(this).val()),
                cleanNum = num.toFixed(2);
            $(this).val(cleanNum);
        }
    });

    // add discount percentage, discount val & brand buttons  ---
    const productPrice = parseFloat('{{ $product->price }}');
    const currency = '{{ getHumanCurrency($product->currency) }}';
    let productSalePrice = '';
    let productDiscountPercentage = '';
    let productDiscountAmount = '';

    function calculateDiscountValues() {
        productSalePrice = parseFloat($('#product_sale_price').val());
        if (
            (productPrice > 0) &&
            (productSalePrice > 0) &&
            ((productPrice - productSalePrice) > 0)
        ) {
            productDiscountPercentage = (productSalePrice <= productPrice) ? `${
                ((((productPrice - productSalePrice) / productPrice) + Number.EPSILON) * 100).toFixed(2)
            }%` : '0%';
            productDiscountAmount = (productSalePrice <= productPrice) ? (Math.round(((productPrice -
                productSalePrice) + Number.EPSILON) * 100) / 100) + ' ' + currency : '0 ' + currency;
        }
    }


    function toggleShortcutButtons(val) {
        $('button[data-parameter="discount"]').attr('disabled', val);
        $('button[data-parameter="percent"]').attr('disabled', val);
    }

    /**
     * Set promotional title and subtitle for all products
     */
    function setPromotionAndSubtitle() {
        calculateDiscountValues();
        let targetedInputName = $(this).data('targeted-input');
        let targetedInput = getTransInputByName(targetedInputName.slice(1));
        const parameter = $(this).data('parameter'),
            cursorPos = targetedInput.prop('selectionStart'),
            targetedInputValue = targetedInput.val(),
            textBefore = targetedInputValue.substring(0, cursorPos),
            textAfter = targetedInputValue.substring(cursorPos, targetedInputValue.length);
        targetedInput.val(`${textBefore} {${parameter}} ${textAfter}`);
        targetedInput.focus();
        updatePromotionAndSubtitle();

        if (getTransInputByName('subtitle').val().length >= 35) {
            $(`button[data-targeted-input="#subtitle"]`).attr('disabled', true);
        }
        if (getTransInputByName('promotion_title').val().length >= 25) {
            $(`button[data-targeted-input="#promotion_title"]`).attr('disabled', true);
        }
    }

    function updatePromotionAndSubtitle() {
        calculateDiscountValues();
        let subtitle = getTransInputByName('subtitle').val();
        let promotion_title = getTransInputByName('promotion_title').val();

        $('.apply-shortcut-msg').each(function(index, template) {
            let parameter = $(template).data('parameter');

            switch (parameter) {
                case 'percent':
                    if (productSalePrice !== '') {
                        subtitle = subtitle.replace(`{${parameter}}`, productDiscountPercentage);
                        promotion_title = promotion_title.replace(`{${parameter}}`, productDiscountPercentage);
                    }
                    break;
                case 'discount':
                    if (productPrice !== '') {
                        subtitle = subtitle.replace(`{${parameter}}`, productDiscountAmount);
                        promotion_title = promotion_title.replace(`{${parameter}}`, productDiscountAmount);
                    }
                    break;
                case 'brand':
                    if ($('#brand_id').val() !== '') {
                        subtitle = subtitle.replace(`{${parameter}}`, $('#brand_id option:selected').data(
                            'content'));
                        promotion_title = promotion_title.replace(`{${parameter}}`, $(
                            '#brand_id option:selected').data('content'));
                    } else {
                        subtitle = subtitle.replace(`{${parameter}}`, '');
                        promotion_title = promotion_title.replace(`{${parameter}}`, '');
                    }
                    break;

                    // for donation product
                case 'end_date':
                    if ($('#input_target_donating_date').val() !== '') {
                        subtitle = subtitle.replace(`{${parameter}}`, $('#input_target_donating_date').val());
                        promotion_title = promotion_title.replace(`{${parameter}}`, $(
                            '#input_target_donating_date').val());
                    } else {
                        subtitle = subtitle.replace(`{${parameter}}`, '');
                        promotion_title = promotion_title.replace(`{${parameter}}`, '');
                    }
                    break;
                case 'min_amount':
                    if ($('#min_amount_donating').val() !== '') {
                        subtitle = subtitle.replace(`{${parameter}}`, $('#min_amount_donating').val());
                        promotion_title = promotion_title.replace(`{${parameter}}`, $('#min_amount_donating')
                            .val());
                    } else {
                        subtitle = subtitle.replace(`{${parameter}}`, '');
                        promotion_title = promotion_title.replace(`{${parameter}}`, '');
                    }
                    break;
            }
        });

        $('#subtitle_content').val('');
        $('#promotion_title_content').val('');
        $('#subtitle_content').val(subtitle);
        $('#promotion_title_content').val(promotion_title);
    }

    /**
     * Enable/Disable buttons if empty fields min_amount_donating|input_target_donating_date
     */
    $('#min_amount_donating, #input_target_donating_date').keyup(function() {
        let button_selector = 'button[data-parameter-en="' + $(this).attr('id') + '"]';
        setTimeout(() => {
            if ($(this).val() == '') {
                $(button_selector).attr('disabled', 'disabled')
            } else {
                $(button_selector).attr('disabled', false);
            }
            updatePromotionAndSubtitle();
        }, 500);
    });

    $('#brand_id').change(function() {
        if ($(this).val() === 'تحديد الماركة التجارية') {
            $('button[data-parameter="brand"]').attr('disabled', 'disabled')
        } else {
            $('button[data-parameter="brand"]').attr('disabled', false);
        }
        updatePromotionAndSubtitle();
    });

    function checkToDisableHandel(id, titleLength) {
        let fieldLength = $(`${id}`).val().length;

        if (fieldLength >= titleLength) {
            $(`button[data-targeted-input="${id}"]`).attr('disabled', 'disabled');
        } else if (fieldLength <= titleLength && $('#product_sale_price').val() !== '' && $('#brand_id').val() !==
            'تحديد الماركة التجارية') {
            $(`button[data-targeted-input="${id}"]`).attr('disabled', false);
        } else if (fieldLength <= titleLength && $('#brand_id').val() !== 'تحديد الماركة التجارية') {
            $(`button[data-parameter="brand"]`).attr('disabled', false);
        } else if (fieldLength <= titleLength && $('#product_sale_price').val() !== '') {
            $(`button[data-parameter="percent"], button[data-parameter="discount"]`).attr('disabled', false);
        } else if (fieldLength <= titleLength && $('#min_amount_donating').val() !== '') {
            $(`button[data-parameter="end_date"]`).attr('disabled', false);
        } else if (fieldLength <= titleLength && $('#input_target_donating_date').val() !== '') {
            $(`button[data-parameter="min_amount"]`).attr('disabled', false);
        }
    }

    // Update the message block on updating the titles field
    function updateMessageBlock(id) {
        updatePromotionAndSubtitle();
        checkToDisableHandel('#' + id, 35);
    }

    $('#subtitle .multi-language-input,#promotion-title .multi-language-input')
        .keyup(function() {
            updateMessageBlock($(this).closest('.form-group').attr('id'));
        });

    /**
     * On keyup
     * disable/enable subtitle & Promotion Title based on value
     * When user update the cost/sale price > update the discount values in subtitle &
     * PromotionTitle fields.
     */
    $('#product_sale_price').keyup(function() {
        setTimeout(() => {
            if (productPrice !== '' && $('#product_sale_price').val() !== '') {
                toggleShortcutButtons(false);
                updatePromotionAndSubtitle();
            } else {
                toggleShortcutButtons(true);
            }
        }, 500)
    });


    function handleDonationTab() {
        if ($('#input_target_donating_enable').is(':checked')) {
            $('#li_donation_tab').removeClass('disabled');
            $('#li_donation_tab a').attr('data-toggle', 'tab');
            $('#li_donation_tab a').attr('aria-expanded', 'false');
            $('#donation_tab_tooltip').hide();
        } else {
            $('#li_donation_tab').addClass('disabled');
            $('#li_donation_tab a').removeAttr('data-toggle');
            $('#li_donation_tab a').removeAttr('aria-expanded');
            $('#donation_tab_tooltip').show();

        }
    }

    $('.apply-shortcut-msg').on('click', setPromotionAndSubtitle);


    $('#modal_product_options').on('show.bs.modal', function() {

        // disable shortcut buttons if values are empty ---
        $('#product_sale_price').val() == '' ? $(
            'button[data-parameter="percent"], button[data-parameter="discount"]').attr('disabled',
            'disabled') : null;
        $('#brand_id').val() == 'تحديد الماركة التجارية' ? $('button[data-parameter="brand"]').attr('disabled',
            'disabled') : $('button[data-parameter="brand"]').attr('disabled', false);

        if (getTransInputByName('subtitle').val().length >= 35) {
            $(`button[data-targeted-input="subtitle"]`).attr('disabled', 'disabled');
        }

        if (getTransInputByName('promotion_title').val().length >= 25) {
            $(`button[data-targeted-input="promotion_title"]`).attr('disabled', 'disabled');
        }

        if (productPrice !== '' && $('#product_sale_price').val() !== '') {
            calculateDiscountValues();
        }
        updatePromotionAndSubtitle();
    });

    $('#product_end_sale').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD'));
    });

    $('#require_shipping').change(function() {
        toggleHsCodeInput();
    });

    // Show client side validation for weight value
    $('#product_weight').keyup(function() {
        let weight = parseFloat($(this).val());
        if ($(this).val() == '' || weight <= 0 || weight > 99999) {
            $('#product_weight_field').addClass('has-error');
            $('#weight_error_message').show();
        } else {
            $('#product_weight_field').removeClass('has-error');
            $('#weight_error_message').hide();
        }
    });

    function getTransInputByName(inputName) {
        //try to get visible input, otherwise get default input
        var inputElement = $(`[data-name=${inputName}]:visible`);
        return inputElement.length ? inputElement : $('[name="' + inputName + '"]')
    }

    $(".styled").uniform({
        radioClass: 'choice'
    });

    let suiMsg = {
        addResult: 'اضافة <b>{term}</b>',
        count: '{count} تم اختيارها',
        maxSelections: 'اقصى عدد للخيارات  {maxCount}',
        noResults: 'لا توجد نتائج!'
    };
    $('#brand_id').dropdown({
        message: suiMsg
    });

    $('#input_target_donating_enable').change(function() {
        if ($(this).is(':checked')) {
            $('#div_target_donating').slideDown();
        } else {
            $('#div_target_donating').slideUp();
        }
    });

    $('#input_target_donating_date').daterangepicker({
        autoUpdateInput: false,
        singleDatePicker: true,
        opens: 'center',
        locale: {
            direction: 'rtl',
            format: 'YYYY-MM-DD'
        }
    }).on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD'));

        let button_selector = 'button[data-parameter-en="input_target_donating_date"]';
        if ($(this).val() == '') {
            $(button_selector).attr('disabled', 'disabled')
        } else {
            $(button_selector).attr('disabled', false);
        }
        updatePromotionAndSubtitle();
    });
</script>
