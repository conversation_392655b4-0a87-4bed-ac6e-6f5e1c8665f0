<script>     var _products_search_data = []; </script>
<div id="modal_page" class="modal modal-backup fade" style="direction: ltr;">
    <form data-filter-before-submit="beforeSubmit"
          action="{{route('cp.pages.store')}}"
          id="page_form"
          method="post"
          class="ajax">
        @csrf
        <input type="hidden" name="products_id" >

        <input type="hidden" name="page_id" value="{{isset($page->id)?hasEncode($page->id):''}}" id="page_id"/>
        <div class="modal-dialog modal-lg" style="direction: rtl;">
            <div class="modal-content">

                <div class="modal-header bg-info">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h6 class="modal-title">{{isset($page->id) ? trans('global.edit') : trans('global.create')}} {{trans('page.single')}}</h6>
                </div>

                <div  class="modal-body" id="page-form-contents">
                    {!! \Salla\Core\Entities\Page::LingualTitle($page??null) !!}
                    <div class="form-group">
                        <div class="input-group">
                            <span class="input-group-addon"><i class="sicon-menu"></i></span>
                            <select class="bootstrap-select bootstrap-select--initial" id="page_type" name="type" style="width: 100%" onchange="showProducts()" data-title="نوع الصفحة ">
                                <option value="general"
                                        @if( isset($page->type) && $page->type == 'general') selected="selected" @endif >عامة</option>
                                <option value="privacy_policy"
                                        @if( isset($page->type) && $page->type == 'privacy_policy') selected="selected" @endif >سياسة الاستخدام والخصوصية</option>
                                <option value="return_policy"
                                        @if( isset($page->type) && $page->type == 'return_policy') selected="selected" @endif >سياسة الاستبدال والإسترجاع</option>
                                <option value="html_code"
                                        @if( isset($page->type) && $page->type == 'html_code') selected="selected" @endif >{{ __('HTML Code') }}</option>
                            </select>
                        </div>
                    </div>
                    
                    {{-- Custom HTML Editor Block --}}
                    <div class="form-group" id="html-editor-block" style="display: none;">
                        <div class="html-editor-container" style="
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            background: #f8f9fa;
                            position: relative;
                        ">
                            <div class="editor-header" style="
                                background: #e9ecef;
                                padding: 8px 12px;
                                border-bottom: 1px solid #ddd;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            ">
                                <span style="font-size: 12px; color: #666;">{{ __('HTML Code Editor') }}</span>
                                <div>
                                    <button type="button" class="btn btn-xs btn-outline-primary" id="format-code-btn" title="{{ __('Format Code') }}">
                                        <i class="fa fa-magic"></i> {{ __('Format') }}
                                    </button>
                                </div>
                            </div>
                            <div id="codemirror-container" style="min-height: 300px;"></div>
                            <textarea id="html-content-editor" style="display: none;">{{ $page['content'] ?? '' }}</textarea>
                        </div>
                    </div>
                    
                    {{-- Normal Content Editor --}}
                    {!! \Salla\Core\Entities\Page::LingualContent($page??null) !!}
                    <div class="form-group">
                      <div class="product-check rec-checkbox rec-checkbox--default rec-checkbox--large rec-checkbox--primary-bg">
                        <input type="checkbox" name="show_in_footer" id="show_informative_page" @if(!isset($page->show_in_footer)) {{'checked'}} @elseif (isset($page->show_in_footer) && $page->show_in_footer) {{'checked'}} @endif>
                        <label for="show_informative_page">
                            <span class="font-15">عرض رابط الصفحة في أسفل الموقع </span>
                            <small class="d-block text-muted text-muted-small">سيتم اخفاء رابط الصفحة من أسفل الموقع عند تعطيل هذا الخيار</small>
                        </label>
                      </div>
                    </div>
                    @widget('store::dashboard.product.mate_data_option', [
                    'id'            => $page?'/page-'.$page->getRouteKey():'',
                    'type'          => 'تعريفية',
                    'name'          => $page->title??'',
                    'translations'  => optional($page)->getTranslationsArray(),
                    ])
                </div>

                <div class="modal-footer">
                    @if(Auth::user()->checkPermission('pages_management'))
                    <button type="button" id="save_page"
                            class="btn btn-info btn-save" data-inline-loader>{{trans('global.save')}}</button>
                    @endif
                    @if(Auth::user()->checkPermission('pages_management'))
                        @if(isset($page->id))
                            <button type="button" id="delete_page" data-url="{{route('cp.pages.delete',['id'=>hasEncode($page->id)])}}" class="btn btn-danger pull-right" data-inline-loader>
                                <i class="sicon-cancel"></i>
                                {{trans('page.delete')}}
                            </button>
                        @endif
                    @endif
                </div>

            </div>
        </div>

    </form>

</div>

@include('cp.pages.legal_text_template')
@include('cp.pages.custom_html_code')

<script type="text/javascript">
    $(function () {
        $('.bootstrap-select').selectpicker();

        initLiveSearch('products_search', function (_elem, id, title, _elem_image) {
            _products_search_data.push(id);
            var _elem_index = _products_search_data.length-1;
            $('#products_ul').
            append('<li id="product_li_' + _elem_index + '" class="rec-list rec-list--horizontal rec-list--space-between product-group__items" data-id="' + id + '" > ' + '<div> <img class=""radius-3" src="' + _elem_image + '" /> <h6> ' + title +  '</h6> </div>' +  '  <a   class="rec-btn rec-btn--remove-el-light rec-btn--with-border rec-btn--rounded" data-index="' + _elem_index + '" onclick=removeProduct(' +_elem_index+ ') ><i class="sicon-cancel"></i></a> </li>');

        }, '&landing_page_products=true');
    });

    //------------------------------------------------------------------------------------------------------------------

    $('#save_page').on('click', function (e) {
        if (checkPlan("{{\Auth::user()->store->plan}}","{{trans('global.plan_permission')}}") == false) {
            return false;
        }

        var page_title = $('#page_form input[name="title"]');

        if (validateInp([{inpID: page_title}]) === false) {
            return false;
        }

        $('[name="products_id"]').val(_products_search_data);

        showLoading(e)

        $('#page_form').submit();

    });

    $('#delete_page').on('click', function (e) {

        var _url = $('#delete_page').attr('data-url');
        showLoading(e)
        loadModule({
            url: _url, data: {_token : _token}, callBack: function (data) {
                    $('#modal_page').modal('toggle');
                    swal({
                        text: 'تم حذف الصفحة',
                        type: 'success',
                        showConfirmButton: true,
                        confirmButtonText: "موافق"
                    });
                    loadPages();
                    hideLoading()
            }
        })

    });

    $('#modal_page').on('hide.bs.modal', function () {
      $('body').css('padding', 0)
    })

    function removeProduct(_elem_index){
        _products_search_data.splice(_elem_index, 1);
        $('#product_li_'+_elem_index).remove();
    };

    function showProducts() {
        var _type = $('#page_type').val();
        if(_type == 'html_code'){
            showHtmlEditor();
            return;
        }
        showNormalContent();

        if (_type == 'landing_products' ) {
            $('#products_div').show();
            $('#category_div').hide();
        } else if (_type == 'landing_category' ) {
            $('#category_div').show();
            _products_search_data = [];
            $('#products_ul').html('');
            $('#products_div').hide();
        } else {
            _products_search_data = [];
            $('#products_ul').html('');
            $('#products_div').hide();
            $('#category_div').hide();
        }
    }

    /**
     * Lets Append Editro Data To The FormData
     * @param {FormData} formData
     * @param form
     * @param event
     * @return {*}
     */
    function beforeSubmit(formData, form, event) {
        if(formData.get('type') === 'html_code'){
            formData.append('content', document.querySelector('#html-content-editor').value);
            return formData
        }
        $('#page-form-contents').find('[data-name="content"]')
            .each(function () {
                let keyPath = this.getAttribute('name');
                //if It's Editor, it will return data, otherwise undefined, so try to get value
                let value = $(this).find('.ql-editor').html() || this.value;

                //if value is just html tags ex. "<p><br> </p>", ignore it
                if(value && (value.replace(/(<([^>]+)>)/gi, "").replace(/(\s+)/gi, "") || value.indexOf('<img') > -1 )) {
                    formData.append(keyPath, value);
                }

            });
        return formData;
    }
</script>
