@extends('cp.layouts.master')

@section('content')
@include('cp.layouts.error')

@widget(
    \Modules\Settings\Widgets\UpgradeAlertForBasicPlanWidget::class,
    ['for_feature' => \Modules\Store\Features\StorePagesFeature::class])

    @if(Auth::user()->checkPermission('pages_management'))
    <div class="row btns-row nav nav-lg ife-hidden">
        <div class="col-xs-6 main-btn">
            <button class="load-data-page btn btn-tiffany btn-rounded btn-xlg" data-page-id="0" data-inline-loader>
                <i class="sicon-add"></i>
                {{trans('page.new')}}
            </button>
        </div>
        <div class="col-xs-6 align-left">
          <ul class="nav nav-lg">
            <li class="dropdown">
              <a class="dropdown-toggle text-nowrap" data-toggle="dropdown" aria-expanded="false"><i class="sicon-toolbox"></i> خدمات</a>
              <ul class="dropdown-menu dropdown-menu-right">
                  <li class="dropdown-link">
                      <button type="button" data-toggle="modal" data-target="#sort_informative_pages_modal" >
                          <i class="sicon-list-reorder mr-5"></i>
                          ترتيب عرض الصفحات التعريفية
                      </button>
                </li>
              </ul>
            </li>
          </ul>
        </div>
    </div>
    @endif

    <div id="load_pages_div">
        @include('cp.pages.load_pages')
    </div>

    <div id="page_form_div"></div>
    <div id="sort_informative_pages"></div>

    <script src="{{ mix('cp/assets/js/sort_informative_pages.js') }}"></script>
    <script>

if(!window.sallaLegacy){
    window.parent?.postMessage({event:"breadcrumb",data:[
              { label: "قنوات البيع" },
              { label: "الصفحات التعريفية", route: "/pages", },
             ]},"*")
}    

@if(Auth::user()->checkPermission('pages_management'))
        window.parent?.postMessage({
                event: "nav.primary-action",
                    title: "{{trans('page.new')}}",
                    url: "#new_page_action",
                    icon: "hgi-stroke hgi-add-01"
                
            }, "*");
            
            window.addEventListener('message', function (event) {
                if (event.data.event === 'nav.primary-action.clicked') {
                $('.load-data-page[data-page-id="0"]').click()
                }
            });

            @endif
        $(document).on('click', '.load-data-page', function (event) {
            event.preventDefault();
            var page_id = $(this).attr('data-page-id');
            $('#page_form_div').html('');
            var url = '{{ route("cp.pages.create", ":page_id") }}';
            url = url.replace(':page_id', page_id);
            if (page_id == 0) {
                url = '{{ route("cp.pages.create") }}';
            }
            showLoading(event)
            $.ajax({
                url    : url,
                type   : "GET",
                data   : {},
                cache  : false,
                success: function (resp) {
                  $('#page_form_div').html(resp);
                    $("#modal_page").modal();
                    hideLoading();
                    loadPages();
                },
                error  : function (err) {
                    console.log(err)
                    hideLoading();
                }
            })
        });
        $(document).on('change', 'input.page-switch', function (e) {
            var _id = $(this).attr('data-page-id');
            var _status = 'inactive';
            if (this.checked)
                _status = 'active';
            showLoading();
            $.ajax({
                url    : "{{route('cp.pages.disable')}}",
                type   : "POST",
                data   : {
                    status: _status,
                    _token: _token,
                    id    : _id
                },
                cache  : false,
                success: function (resp) {
                    hideLoading();
                },
                error  : function (err) {
                    hideLoading();
                }
            })
        });
    </script>
@endsection



@section('before_js')
    @widget('quill_editor_js')

    <script type="text/javascript" src="{{ asset('cp/assets/js/plugins/forms/selects/bootstrap_multiselect.js') }}">
    </script>
    {{--<script type="text/javascript" src="{{ asset('cp/assets/js/plugins/table-selector/tablecellsselection.js') }}"></script>--}}

    <script type="text/javascript"
            src="{{ asset('cp/assets/js/plugins/selectize/js/standalone/selectize.min.js')}}?v=0.1"></script>
    <script type="text/javascript" src="{{ asset('cp/assets/js/plugins/selectize/js/selectize_no_results.js')}}?v=0.1">
    </script>

    <script>
        function loadPages() {
            loadModule({
                url     : "{{route('cp.load_pages')}}",
                data    : {
                    _token: _token
                },
                callBack: function (data) {
                    $('#load_pages_div').html(data);
                    var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
                    elems.forEach(function (html) {
                        var switchery = new Switchery(html, {
                            color: '#57d4c4',
                            size : 'small'
                        });
                    });
                }
            })
        }
    </script>
    @include('cp.pages.short_url_scripts')
@endsection
