<?php

namespace Modules\Payment\Exports;

use App\Models\PaymentLog;
use Illuminate\Database\Query\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Entities\SallaOrders;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

/**
 * Logic class defines exporting store e-payment details excel sheet logic actions.
 *
 * <AUTHOR> El-Kabbany at 8 October 2019
 * @contact <EMAIL>
 */
class ExportPaymentSallaOrdersSheet implements
    FromQuery,
    WithMapping,
    WithHeadings,
    ShouldAutoSize,
    WithColumnFormatting,
    WithEvents,
    WithTitle
{
    use Exportable;

    /**
     * selected e-payment id
     *
     * @var int
     */
    private $epayment_id;

    /**
     * EPaymentsExport controller construct.
     *
     * @param int $epayment_id
     */
    public function __construct($epayment_id)
    {
        $this->epayment_id = $epayment_id;
    }

    /**
     * Set this sheet title.
     *
     * <AUTHOR> El-Kabbany at 8 October 2019
     * @contact <EMAIL>
     */
    public function title(): string
    {
        return trans('payment::sheets.epayments_details_title', ['id' => $this->epayment_id]);
    }

    /**
     * Define exported sheet table heading.
     *
     * <AUTHOR> El-Kabbany at 8 October 2019
     * @contact <EMAIL>
     */
    public function headings(): array
    {
        return [trans('payment::order_details_headings')];
    }

    /**
     * Customize exported sheet with theme format.
     *
     * <AUTHOR> El-Kabbany at 8 October 2019
     * @contact <EMAIL>
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                /** RTL Sheet */
                $event->sheet->getDelegate()->setRightToLeft(true);

                foreach ($this->columnFormats() as $column => $columnFormat) {
                    $event->sheet->getDelegate()
                        ->getStyle($column)
                        ->getNumberFormat()
                        ->setFormatCode($columnFormat);
                }

                /** Style table header font, borders, text alignment, filling color, and height */
                new StyleHeaderRow($event);
            },
        ];
    }

    /**
     * Define exported sheet table columns formats.
     *
     * <AUTHOR> El-Kabbany at 8 October 2019
     * @contact <EMAIL>
     */
    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return Builder
     */
    public function query()
    {
        return SallaOrderItems::query()->where('payment_id', $this->epayment_id)
            ->with('partnerCompany');
    }

    /**
     * @param mixed $row
     *
     * @return array
     */
    public function map($row): array
    {
        /** @var SallaOrderItems $row */
        $fee = $row->getFess();

        return [
            $row->order_id,
            $row->total,
            $row->tax_value,
            $fee,
            $row->total - $row->tax_value - $fee,
        ];
    }
}
