<?php


namespace Modules\Payment\Jobs;


use App\Models\Payment;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\MarketPlace\Entities\PartnerOrderDetail;
use Modules\MarketPlace\Enum\PartnerOrderTypeEnum;
use Modules\MarketPlace\Enum\SallaOrderItemStatus;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Repositories\SallaOrderItemRepository;
use Modules\Payment\Actions\PaymentLogAction;
use Modules\Payment\Enums\PaymentLogCreatedByType;
use Modules\Payment\Enums\PaymentLogType;
use Modules\Payment\Enums\SallaPartnersPaymentType;
use Modules\Payment\Notifications\IssuePartnerPaymentNotification;
use Modules\Payment\Notifications\Partners\NotifyPartnerNeedVerifiedNotification;
use Modules\Store\Enum\StorePattern;
use Modules\Payment\Enums\PaymentPartnerRelatedType;
use Salla\Core\Entities\PartnerModel;
use Salla\Core\Traits\Jobs\RunOnce;
use Salla\Logger\Facades\Logger;
use Salla\Settings\Facades\Settings;
use Salla\Core\Traits\WithStageLogger;
use Modules\MarketPlace\Entities\SallaOrders;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\Payment\Entities\SallaPartnersPayments;
use Modules\MarketPlace\Entities\Partner\PartnerApp;
use Modules\MarketPlace\Entities\Partner\PartnerBank;
use Modules\MarketPlace\Entities\Partner\PartnerUser;
use Modules\MarketPlace\Entities\Partner\PartnerSetting;

class GeneratePartnerPaymentsItems
{
    use WithStageLogger, RunOnce;

    private $developer_id = null;
    private $types        = [];
    private $without_limit = false;

    public function __construct($developer_id = null, $types = null, $without_limit = false)
    {
        $this->developer_id = $developer_id;
        $this->types = !empty($types) ? Arr::wrap($types) : [];
        $this->without_limit = $without_limit;
    }

    public function handleRun()
    {
        foreach (SallaProductType::getPartnerTypes() as $type) {
            if (!empty($this->types) && !in_array($type, $this->types)) {
                continue;
            }

            if($type == SallaProductType::ADDON) {
                foreach (SallaProductAddonType::getPartnerTypes() as $type_value) {
                    $list_partner_id = $this->getParntersHasAcceptedTotalPayment($type, $type_value);
                    if(empty($list_partner_id)) {
                        continue;
                    }

                    $this->createPartnerPaymentItem($type, $type_value, $this->getPartnerItems($type, $type_value, $list_partner_id));
                }

                continue;
            }

            $list_partner_id = $this->getParntersHasAcceptedTotalPayment($type);
            if(empty($list_partner_id)) {
                continue;
            }

            $this->createPartnerPaymentItem($type, $type_value, $this->getPartnerItems($type, null, $list_partner_id));
        }

        dispatch_sync(new GeneratePartnerPayments($this->developer_id, $this->without_limit));
    }

    /**
     * to prevent from generate records in table "salla_partners_payments" and the total less than 500 SAR
     * so we check if developer has due amount more than 500 SAR
     * bring them and pass them to query that generate payment
     * @param $type
     * @return mixed
     */
    private function getParntersHasAcceptedTotalPayment($type, $type_value = null)
    {
        $list_partner_id = [];

        $tax = Settings::get('payment::salla-partner-tax', 15);

        SallaOrderItems::selectRaw("
            salla_order_items.developer_user_id,           
            SUM(salla_order_items.total) as total,           
            SUM(salla_order_items.tax_value) as tax_amount,
            SUM(salla_order_items.refund_amount) as refund_amount                             
            ")
            ->PartnerItemsHasNotPayments($type, $type_value)
            ->groupBy('salla_order_items.developer_user_id') //group by app developer_user_id
            ->when(!empty($this->developer_id), function ($query) {
                return $query->where('salla_order_items.developer_user_id', $this->developer_id);
            })
            ->with([
                'partnerCompany',
            ])
            ->chunk(100, function ($items) use ($type_value, $type, $tax, &$list_partner_id) {
                // Get first item fees since items are grouped by developer_user_id
                /** @var SallaOrderItems $firstItem */
                $firstItem = $items->first();
                $salla_fees = $firstItem->partnerCompany->getCommissionPercentage($type, $type_value);

                foreach ($items as $item) {
                    $total = $item->total;
                    $tax_amount = $item->tax_amount;
                    $refund_amount = !empty($item->refund_amount) ? $item->refund_amount : 0;

                    if ($refund_amount > 0) {
                        $total = $total - $refund_amount;
                        $tax_amount = round(($total * $tax) / ($tax + 100), 3);
                    }

                    $fees = ($total - $tax_amount) * ($salla_fees / 100);
                    $due_amount = $total - $tax_amount - $fees;

                    $commission_amount = $this->getCommissionAmount($item->developer_user_id);
                    if($commission_amount > 0) {
                        $due_amount -= $commission_amount;
                    }

                    if (
                        ($due_amount >= store()->getSetting('payment::min-amount-for-generate-partner-payment', 500)) ||
                        $this->without_limit
                    ) {
                        $list_partner_id[] = $item->developer_user_id;
                    }
                }
            });

        return $list_partner_id;
    }

    /**
     * Get Items Apps
     */
    private function getPartnerItems($type, $type_value = null, $list_partner_id = [])
    {
        //SallaProductType::Addon
        $partner_related_id = 'salla_product_marketplace_app.app_id';
        if ($type == SallaProductType::THEME) {
            $partner_related_id = 'themes.marketplace_theme_id';
        } else if(in_array($type_value, SallaProductAddonType::getPartnerServiceTypes())) {
            $partner_related_id = 'salla_product_expert_services.service_id';
        }

        return SallaOrderItems::selectRaw("
            salla_order_items.*,
            COUNT(salla_order_items.id) as subscription_quantity,
            SUM(salla_order_items.total) as total,
            SUM(salla_order_items.product_price) as amount,
            SUM(salla_order_items.tax_value) as tax_amount,
            SUM(salla_order_items.refund_amount) as refund_amount,           
            salla_products_pricing.uuid as portal_plan_id,
            $partner_related_id as partner_related_id,
            salla_products_pricing.subtitle as plan_name,
            salla_products.name as product_name,
            salla_order_items.tax as related_item_tax,           
            salla_order_items.developer_user_id
            ")
            ->PartnerItemsHasNotPayments($type, $type_value)
            ->groupBy('salla_order_items.product_price_id') //group by app plan id
            ->when(!empty($this->developer_id), function ($query) {
                return $query->where('salla_order_items.developer_user_id', $this->developer_id);
            })
            ->when(!empty($list_partner_id), function ($query) use ($list_partner_id) {
                return $query->whereIn('salla_order_items.developer_user_id', $list_partner_id);
            })
            ->with([
                'partnerCompany.defaultBank',
            ])->cursor();
    }

    /**
     * @param $items
     */
    private function createPartnerPaymentItem($type, $type_value = null, $items)
    {
        /** @var SallaOrderItems $item */
        foreach ($items as $item) {

            if ($item->total <= 0) {
                continue;
            }

            /**
             * @var PartnerCompany $company
             * @var PartnerBank $bank
             */
            $company = $this->getCompany($item);
            if (empty($company) || !$company->canGeneratePayout()) {
                if (empty($company->id_verified) && !empty($company->adminUser)) {
                    $company->adminUser->notify(new NotifyPartnerNeedVerifiedNotification());
                }

                continue;
            }

            $bank = $company->getBank();
            if (empty($bank)) {
                continue;
            }

            $partner_related_type = PaymentPartnerRelatedType::APP;
            if ($type == SallaProductType::THEME) {
                $partner_related_type = PaymentPartnerRelatedType::THEME;
            } else if($type_value == SallaProductAddonType::SERVICE) {
                $partner_related_type = PaymentPartnerRelatedType::SERVICE;
            } else if($type_value == SallaProductAddonType::INFLUENCER_SERVICE) {
                $partner_related_type = PaymentPartnerRelatedType::INFLUENCER;
            }

            $tax = Settings::get('payment::salla-partner-tax', 15);
            $salla_fees = $company->getCommissionPercentage($type, $type_value);

            $refund_amount = !empty($item->refund_amount) ? $item->refund_amount : 0;

            $total = $item->total;

            $tax_amount = $item->tax_amount;
            if ($refund_amount > 0) {
                $total = $total - $refund_amount;
                $tax_amount = round(($total * $tax) / ($tax + 100), 3);
            }

            $fees = ($total - $tax_amount) * ($salla_fees / 100);
            $due_amount = $total - $tax_amount - $fees;

            $commission_amount = $this->getCommissionAmount($item->developer_user_id, $item->product_price_id, $partner_related_type);
            if($commission_amount > 0) {
                $due_amount -= $commission_amount;
            }

            $partener_payment = SallaPartnersPayments::query()->create([
                'product_id'            => $item->product_id,
                'product_price_id'      => $item->product_price_id,
                'product_name'          => $item->product_name,
                'portal_plan_id'        => $item->portal_plan_id,
                'partner_related_type'  => $partner_related_type,
                'partner_related_id'    => $item->partner_related_id,
                'plan_name'             => $item->plan_name,
                'due_amount'            => $due_amount,
                'subscription_quantity' => $item->subscription_quantity,
                'total'                 => $total,
                'tax_amount'            => $tax_amount,
                'tax'                   => $tax,
                'fees'                  => $fees,
                'refund_amount'         => $refund_amount,
                'developer_user_id'     => $item->developer_user_id,
                'fee_percent'           => $salla_fees,
                'commission_amount'     => $commission_amount,
                'type'                  => SallaPartnersPaymentType::PARTNER,
            ]);

            Logger::setAdditions('partner_payment_id', $partener_payment->id);

            SallaOrderItems::PartnerPaymentItems(
                $item->developer_user_id,
                $item->product_id,
                $item->product_price_id,
                $partner_related_type
            )
                ->update([
                    'salla_order_items.salla_partner_payment_id' => $partener_payment->id,
                ]);
        }
    }


    /**
     * @param SallaOrderItems $item
     * @return PartnerCompany|null
     */
    private function getCompany(SallaOrderItems $item)
    {
        return !empty($item->partnerCompany) ? $item->partnerCompany : null;
    }

    /**
     * @return string
     */
    protected function getRunOnceKey(): string
    {
        return 'generate-partner-payment-items';
    }

    /**
     * @param $companyId
     * @param $productPriceId
     * @return int
     *
     * this give us total of commission amount with vat
     */
    protected function getCommissionAmount($companyId, $productPriceId = null, $related_type = null)
    {
        return app(SallaOrderItemRepository::class)->getCommissionAmount(
            $companyId,
            $productPriceId,
            $related_type
        );
    }
}
