<?php

namespace Modules\MarketPlace\Jobs\App;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Salla\Logger\Traits\WithStageLogger;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\MarketPlace\Entities\SallaProductPrice;
use Modules\MarketPlace\Enum\SallaProductPriceType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppStatus;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppPlanType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppDomainType;
use Modules\MarketPlace\Actions\MarketplaceApp\Subscription\ExpireFreeAppSubscriptionsAction;

class ExpireFreeAppSubscriptionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, WithStageLogger;

    public function __construct(protected SallaProductMarketplaceApp $app)
    {

    }

    /**
     * we only handle apps have plan type recurring and on_demand
     * check if app not have currently free plan, and already have free plan
     * @return void
     */
    public function handle()
    {
        if (
            ($this->app->status != SallaProductMarketplaceAppStatus::LIVE) ||
            ($this->app->type != SallaProductMarketplaceAppType::PUBLIC) ||
            ($this->app->domain_type != SallaProductMarketplaceAppDomainType::APP) ||
            !in_array($this->app->plan_type, [
                SallaProductMarketplaceAppPlanType::RECURRING,
                SallaProductMarketplaceAppPlanType::ON_DEMAND,
            ])) {
            return;
        }

        if ($this->checkAppHaveFreePlan() || !$this->checkAppHaveOldFreePlan()) {
            return;
        }

        ExpireFreeAppSubscriptionsAction::make([
            'app' => $this->app,
        ])->run();
    }

    /**
     * check if current plans of app have free plan
     * @return bool
     */
    private function checkAppHaveFreePlan()
    {
        return $this->app->product->productPricesWithoutConstraints()
            ->whereNull('store_id')
            ->where('price', 0)
            ->where('type', $this->getPlanType())
            ->where('version', $this->app->update_version)
            ->exists();
    }

    /**
     * check if app have already free plan and this plan is deleted
     * @return bool
     */
    private function checkAppHaveOldFreePlan()
    {
        return $this->app->product->productPricesWithTrashed()
            ->onlyTrashed()
            ->whereNull('store_id')
            ->where('price', 0)
            ->where('type', $this->getPlanType())
            ->exists();
    }

    /**
     * @return string
     */
    private function getPlanType()
    {
        if ($this->app->plan_type == SallaProductMarketplaceAppPlanType::ON_DEMAND) {
            return SallaProductPriceType::OnDemand;
        }

        return SallaProductPriceType::Recurring;
    }
}