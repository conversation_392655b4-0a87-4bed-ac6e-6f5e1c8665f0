<?php

namespace Modules\Settings\Http\Controllers\Dashboard\Entity;

use App\Models\Bank;
use Illuminate\Http\Request;
use Modules\Payment\Actions\Gateways\CreateOrUpdatePendingMainBankAccount;
use Modules\Payment\Enums\EPaymentRequestStatus;
use Modules\Payment\Enums\StoreBankAccountType;
use Modules\Settings\Http\Requests\DocumentCancelRequest;
use Modules\Settings\Http\Requests\StoreDocumentRequest;
use Modules\Settings\Transformers\StoreDocumentTransformer;
use Modules\Violation\Repositories\ViolationRepository;
use Salla\Core\Enum\NormalStatus;
use Salla\Core\Enum\StoreDocumentsStatus;
use Salla\Core\Entities\StoreDocument;
use Modules\Settings\Transformers\StoreDocumentRequestTransformer;
use Modules\Payment\Entities\EPaymentRequest;
use Modules\Settings\Actions\RetrieveDocumentInfoAction;
use Salla\Core\Enum\StoreDocumentStatus;
use Salla\Core\Enum\StoreEntityEnum;
use Salla\Logger\Facades\Logger;
use Modules\Settings\Traits\LockedHelper;

class StoreDocumentController
{
    use LockedHelper;

    private $request;
    private $violated_store_url;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function index()
    {
        $hasUnderReview = EPaymentRequest::where('status', EPaymentRequestStatus::UNDER_REVIEW)->count() > 0;
        $hasApproved = store()->hasPriorApprovedRequest();

        $documentationBtn = (object)[
            'type' => $hasApproved ? 'update' : 'create',
            'disabled' => $hasUnderReview || store()->isDemoStore(),
            'icon' => $hasApproved ? null :'<i class="sicon-add"></i>',
            'text' => $hasApproved ? "تحديث بيانات التوثيق" : "رفع طلب توثيق",
        ];

        return view(config('salla.views_path.cp').'.store_verification.index', [
            'page_title'      => trans('Breadcrumbs::labels.store_document'),
            'is_index'  => true,
            'documentation_btn' => $documentationBtn
        ]);
    }

    public function editDocument()
    {
        return view(config('salla.views_path.cp').'.store_verification.index', [
            'page_title'      => trans('Breadcrumbs::labels.store_document'),
            'is_index'  => false,
        ]);
    }

    public function indexInfo()
    {
        $allVerifications = EPaymentRequest::query()->where('store_id' , store()->getId())->get()->sortByDesc('created_at');
        $hasUnderReview = $allVerifications->where('status', EPaymentRequestStatus::UNDER_REVIEW)->count() > 0;
        $formedVerifications = $this->canViewRequests($allVerifications);

        return ajax()->setData([
            'store_verifications' => transformation($formedVerifications,
                StoreDocumentTransformer::class)->transform(),
            'has_options' => ($hasUnderReview && !store()->hasPlan('basic')) ||
                             $formedVerifications->where('can_view', true)->isNotEmpty(),
        ]);
    }

    public function cancelRequest(DocumentCancelRequest $request)
    {
        $documentRequest = EPaymentRequest::find($request->id);
        if ($documentRequest->store_id !== store()->id ||
            $documentRequest->status != EPaymentRequestStatus::UNDER_REVIEW ||
            $documentRequest->store->hasPlan('basic')) {
            return ajax()->error(__('settings::messages.epayment_request_cancel_error'));
        }

        $documentRequest->update(['status' => EPaymentRequestStatus::CANCELED]);
        store()->pendingStoreDocument->forceDelete();

        $pendingBankAccount = store()->getMainPendingBankAccount();
        if ($pendingBankAccount) {
            $pendingBankAccount->update(['status' => NormalStatus::DELETED, 'type' => StoreBankAccountType::NORMAL]);
        }

        return ajax()->success('Document request canceled');
    }

    public function documentInfo()
    {
        $storeDocument = RetrieveDocumentInfoAction::make()->run();

        return ajax()->setData(transformation($storeDocument, StoreDocumentRequestTransformer::class)->transform());
    }

    public function sendRequest(StoreDocumentRequest $request)
    {
        $storeDocument = StoreDocument::withoutGlobalScope('approved')->where('status', StoreDocumentsStatus::PENDING)->first();

        // If no pending store document exists, create a new one with empty data
        if (!$storeDocument) {
            $storeDocument = $this->getOrCreateStoreDocument([]);
        }

        $ownerIdentityNumber = $storeDocument->owner_identity_number;

        if($this->hasPriorViolations($ownerIdentityNumber)) {
            $message = sprintf('لديك متجر مقيّد مرتبط بنفس رقم الهوية (%s) يرجى معالجة المخالفات المرصودة على متجرك المقيّد لتتمكن من استكمال توثيق المتجر  
                    <br> رابط المتجر :<a href="%s"> %s </a>',$ownerIdentityNumber,$this->violated_store_url,$this->violated_store_url);
            return ajax()->error($message);
        }

        $this->updateFreelanceCertificate($storeDocument);
        $pendingBankAccount = $this->updateMainBankAccountDetails();

        if (store()->ePaymentRequests->where('status', EPaymentRequestStatus::APPROVED)->isEmpty()) {
            $storeData = [
                'main_account_verified' => 0,
                'document_status'       => StoreDocumentStatus::UNDER_REVIEW
            ];

            store()->update($storeData);
        }

        EPaymentRequest::query()->updateOrCreate([
            'store_id' => store()->getId(),
            'status'   => 'under_review'
        ], [
            'entity'          => $request->entity,
            'bank_account_id' => $pendingBankAccount ? $pendingBankAccount->id : null
        ]);

        if(! store()->getStoreDocumentStatus() == StoreDocumentStatus::COMPLETED) {
            store()->setSetting('store::status', [
                'status' => 'inactive',
                'title' => 'المتجر قيد الصيانة',
                'message' => 'عذراً عزيزي العميل، المتجر حالياً قيد الصيانة و سنعاود العمل خلال فترة وجيزة',
            ]);
        }

        $storeDocumentData = [];
        if ($request->filled('owner_identity_image') && $storeDocument->owner_identity_image != $request->owner_identity_image) {
            $storeDocumentData['owner_identity_image'] = $request->owner_identity_image;
        }
        if ($request->filled('manager_identity_image') && $storeDocument->manager_identity_image != $request->manager_identity_image) {
            $storeDocumentData['manager_identity_image'] = $request->manager_identity_image;
        }

        if ($request->filled('commercial_register_image') && $storeDocument->commercial_register_image != $request->commercial_register_image) {
            $storeDocumentData['commercial_register_image'] = $request->commercial_register_image;
        }

        if (isset($storeDocumentData)) {
            $storeDocument->update($storeDocumentData);
        }

        return ajax()->success('جاري مراجعة بيانات الحساب من قبل فريق سلة و سيتم الرد في مدة أقصاها 48 ساعة')->runJavascript('location.reload();');
    }

    private function updateFreelanceCertificate(StoreDocument $storeDocument): void
    {
        if ($this->request->entity == StoreEntityEnum::PERSON && $this->request->freelance_certificate) {  // if entity is person
            $data = [
                'freelance_certificate'      => $this->request->freelance_certificate,
                'freelance_number'      => $this->request->freelance_number,
            ];

            $storeDocument->update($data);
        } elseif ($this->request->entity == StoreEntityEnum::CHARITY && $this->request->charity_certificate_image) { // if entity is charity
            $data = [
                'charity_certificate_image' => $this->request->charity_certificate_image,
            ];

            $storeDocument->update($data);
        }
    }

    private function updateMainBankAccountDetails()
    {

        $bank = Bank::find($this->request->bank_id);

        if (
            store()->getMainPendingBankAccount() &&
            store()->getMainPendingBankAccount()->auto_verified &&
            optional($bank)->lean_supported &&
            !(optional($bank)->code === 'RJHI' && optional($this->request)->account_type === 'commercial')
        ) {
            return store()->getMainPendingBankAccount();
        }

        if($this->request->bank_id && $this->request->account_name &&
            $this->request->iban_number && $this->request->account_number && ($this->request->iban_certificate || $this->request->sbc_certificate)) {

            $pendingBankAccount = CreateOrUpdatePendingMainBankAccount::make($this->request->all())
                                    ->run();

            Logger::info('🎉 created pending main bank account details');
            return $pendingBankAccount;
        }

        Logger::info('❌❌ error creating pending main bank account details with the following data', [
            'bank_id'          => $this->request->bank_id ?? 0,
            'bank_name'        => $this->request->bank_name ?? '',
            'account_name'     => $this->request->account_name ?? '',
            'iban_number'      => $this->request->iban_number ?? 0,
            'iban_certificate' => $this->request->iban_certificate ?? 0,
            'sbc_certificate' => $this->request->sbc_certificate ?? 0,
            'account_number'   => $this->request->account_number ?? 0,
        ]);

        return null;
    }

    private function hasPriorViolations($ownerIdentityNumber): bool
    {
        // I used join because of the many stores that are all registered with the same identity number already
        // join will get any violations registered with any of the same-identity-number stores and return the url of the first occurrence of those

        $prior_violations = app(ViolationRepository::class)
            ->withoutGlobalScopes()
            ->leftJoin('store_document', 'violations.store_id', '=', 'store_document.store_id')
            ->selectRaw('count(violations.store_id) as store_id_count,violations.store_id,owner_identity_number')
            ->where('store_document.owner_identity_number', $ownerIdentityNumber)
            ->where('violations.store_id','<>',store()->id)
            ->where('violations.status','opened')
            ->groupBy('violations.store_id')
            ->get();

        $this->violated_store_url = $prior_violations->isNotEmpty() ? store($prior_violations->first()->store_id)->url() : '';

        return $prior_violations->isNotEmpty();
    }

    public function getSignedImagesUrls(Request $request)
    {
        $imageUrls = [
            'owner_identity_image' => temp_file_url($request->owner_identity_image),
            'manager_identity_image' => temp_file_url($request->manager_identity_image),
            'freelance_certificate' => temp_file_url($request->freelance_certificate),
            'charity_certificate_image' => temp_file_url($request->charity_certificate_image),
            'commercial_register_image' => temp_file_url($request->commercial_register_image),
            'iban_certificate' => temp_file_url($request->iban_certificate),
            'sbc_certificate' => temp_file_url($request->sbc_certificate),
        ];

        return response()->json($imageUrls);
    }

    private function canViewRequests($storeVerifications)
    {
        $lastRejection = $storeVerifications->first(fn($value) => $value->status == EPaymentRequestStatus::REJECTED);

        $canView = $lastRejection && !$storeVerifications->where('updated_at', '>', $lastRejection->updated_at)->count();

        $storeVerifications->map(fn($item) => $item['can_view'] = ($item === $lastRejection && $canView));

        return $storeVerifications;
    }

    private function getOrCreateStoreDocument(array $approvedData): StoreDocument
    {
        return StoreDocument::withoutGlobalScope('approved')->firstOrCreate(
            [
                'store_id' => store()->getId(),
                'status' => StoreDocumentsStatus::PENDING
            ],
            $approvedData
        );
    }
}
