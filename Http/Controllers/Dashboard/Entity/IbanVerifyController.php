<?php

namespace Modules\Settings\Http\Controllers\Dashboard\Entity;

use Modules\Payment\Actions\Gateways\CreateOrUpdatePendingMainBankAccount;
use Modules\Settings\Actions\API\IbanVerificationAction;
use Modules\Settings\Http\Requests\IbanVerificationRequest;
use Salla\Core\Jobs\ClearCache;

class IbanVerifyController
{
    public function __invoke(IbanVerificationRequest $request)
    {
        // Check if user has exceeded lean verification limit before making API call
        if ($this->hasExceededLeanVerificationLimit()) {
            return $this->createManualVerificationRedirectResponse(__('تم تجاوز الحد الأقصى لمحاولات التحقق اليومية'));
        }

        try {
            $response = IbanVerificationAction::make($request->all())->run();
        } catch (\Exception $e) {
            return ajax()->error(__('حدث خطأ أثناء التحقق من الأيبان'))->setData([
                'type' => 'EXCEPTION',
                'show_confirm' => false,
            ]);
        }

        $this->logIbanVerification();

        $autoVerified = $this->determineAutoVerified($response);

        if ($this->shouldFailVerification($response)) {
            $this->createOrUpdateBankAccount($request, $response, $autoVerified);
            return $this->handleVerificationFailure($response);
        }

        $this->createOrUpdateBankAccount($request, $response, $autoVerified);

        return $this->handleSuccessResponse($response->getMessage()->verifications->account_holder_name);
    }

    private function logIbanVerification()
    {
        store()->verificationLogs()->create(['type' => 'iban']);
    }

    private function shouldFailVerification($response)
    {
        return !$response->isSuccess() || !$response->getMessage()->verifications;
    }

    private function handleVerificationFailure($status)
    {
        if ($this->hasExceededLeanVerificationLimit()) {
            return $this->createManualVerificationRedirectResponse($status->getMessage());
        }

        return $this->createStandardFailureResponse($status);
    }

    private function hasExceededLeanVerificationLimit(): bool
    {
        $maxAttempts = 3;
        $todayAttempts = $this->getTodayLeanVerificationAttempts();
        
        return $todayAttempts >= $maxAttempts;
    }

    private function getTodayLeanVerificationAttempts(): int
    {
        return store()->verificationLogs()
            ->where('type', 'iban')
            ->whereDate('created_at', today())
            ->count();
    }

    private function createManualVerificationRedirectResponse(string $message)
    {
        return ajax()->alert($message, 'error')->setData([
            'type' => 'REDIRECT_TO_MANUAL',
            'show_confirm' => false
        ]);
    }

    private function createStandardFailureResponse($status)
    {
        return ajax()->alert($status->getMessage(), 'error')->setData([
            'type' => $status->getData('type'),
            'show_confirm' => $status->getData('showConfirm')
        ]);
    }

    private function determineAutoVerified($response)
    {
        $autoVerified = $response->getMessage()->verifications->iban_ownership_verified ?? false;
        $type = $response->getData('type');

        if (in_array($type, ['UNSUPPORTED', 'FAILED'])) {
            $autoVerified = false;
        }

        return $autoVerified;
    }

    private function createOrUpdateBankAccount($request, $response, $autoVerified)
    {
        $accountNumber = $autoVerified ? null : store()->getMainPendingBankAccount()?->account_number;

        $requestData = collect($request->all())
            ->merge([
                'auto_verified' => $autoVerified,
                'swift_code' => $response->getMessage()->verifications->swift_code ?? null,
                'account_name' => $response->getMessage()->verifications->account_holder_name ?? '- -',
                'account_number' => $accountNumber,
            ])
            ->toArray();

        CreateOrUpdatePendingMainBankAccount::make($requestData)->run();
    }

    private function handleSuccessResponse($accountHolderName)
    {
        return ajax()->success('تم التحقق من الأيبان تلقائياً')->setData([
            'account_holder_name' => $accountHolderName,
            'show_confirm' => false
        ]);
    }
}
