<?php

namespace Modules\MarketPlace\Http\Controllers\Dashboard\Cart;

use App\Models\SallaProducts;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Modules\Checkout\Actions\Payment\Traits\HasFriendlyMessage;
use Modules\Expert\ExpertClients\Request\OrderExpertClient;
use Modules\MarketPlace\Actions\Cart\AddItemAction;
use Modules\MarketPlace\Actions\Cart\CartCompleteHandler;
use Modules\MarketPlace\Actions\Cart\CreatePurchases;
use Modules\MarketPlace\Actions\Cart\RemoveCouponAction;
use Modules\MarketPlace\Actions\Cart\RemoveItemAction;
use Modules\MarketPlace\Actions\Cart\SetCartPaymentMethodAction;
use Modules\MarketPlace\Actions\Cart\UpdateItemFeatureAction;
use Modules\MarketPlace\Actions\MarketplaceApp\CompleteInstallAppAction;
use Modules\MarketPlace\Actions\MarketplaceApp\Presenter\InstallAppPresenter;
use Modules\MarketPlace\Actions\Product\AddFreeSallaProductAction;
use Modules\MarketPlace\Contract\SallaCartService;
use Modules\MarketPlace\Entities\InAppSubscriptionIdentifier;
use Modules\MarketPlace\Entities\SallaCart;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaCartItemFeature;
use Modules\MarketPlace\Entities\SallaProductPriceFeature;
use Modules\MarketPlace\Enum\SallaCartSource;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Http\Requests\Dashboard\PaymentRequest;
use Modules\MarketPlace\Http\Requests\Dashboard\CartPaymentRequest;
use Modules\MarketPlace\Http\Requests\Dashboard\UpdateQuantityRequest;
use Modules\MarketPlace\Http\Requests\Dashboard\UpdateFeatureQuantityRequest;
use Modules\MarketPlace\Traits\RedrawCart;
use Modules\MarketPlace\Widgets\Dashboard\SeoThankYouWidget;
use Modules\Product\Enum\ProductStatus;
use Modules\Product\Presenters\ProductTypePresenter;
use Modules\Store\Enum\CustomerEffortScorePageType;
use Modules\Store\Repositories\CustomerEffortScorePageRepository;
use Salla\Core\Enum\Plan;
use Salla\Core\Services\Ajax;
use Salla\DashboardTracking\DashboardTracking;
use Salla\DashboardTracking\Trackers\Salla\Onboarding\CheckoutTracking;
use Salla\Paymetns\Events\PaymentConfirmFailed;
use Salla\Paymetns\Presenters\PaymentPresenter;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Entities\SallaProductPrice;


class CartController
{
    use RedrawCart, HasFriendlyMessage;

    public function __construct(SallaCartService $service)
    {
        $this->cartService = $service;
    }

    /**
     * @return mixed
     */
    public function __invoke(Request $request)
    {
        return $this->redrawCart(true, __t('marketplace::cart.messages.error.cart is empty'));
    }

    public function add(SallaProducts $product)
    {
        //this one case only for live Theme Landing page
        if (!feature('landing-pages')->isHaveFeature() && $product->getKey() == 9884) {
            return ajax()->error('لا يمكنك شراء الثيم، متوفر فقط للباقات برو وسبيشال');
        }

        if ($product->is_sandbox) {
            return ajax()->error(__('marketplace::my_subscription.messages.warning.trial_subscription_cannot_be_bought'));
        }

        try {
            if ($product->canAddToStoreAsFree()) {
                AddFreeSallaProductAction::setProduct($product)->run();
                return ajax()
                    ->success(__t('marketplace::marketplace_themes.install.success', ['theme' => $product->name]))
                    ->runJavascript('setTimeout(function(){window.location.reload()},1000)');
            }

            AddItemAction::make(request()->all())->setProduct($product)->run();


            if (mobileAgent()->isIos() && mobileAgent()->campareVersion('5.0.0')) {
                $productPriceId = app('optimus')->decode(request()->get('product_price_id'));

                $event = [
                    'apple_product_id' => $product->getProductPriceObject($productPriceId)->getInAppSubscriptionId(),
                    'apple_user_id'    => $this->getAppleIdentifier(),
                ];

                return ajax()
                    ->runJavascript('Salla.event.dispatchMobileEvent("market-place:in-app-purchases",' . json_encode($event) . ');');
            }

            return $this->redrawCart(true, null, [
                'googleTags' => \GoogleTagManager::getDataLayer()->toJson(),
            ], true);


        } catch (ValidationException $validation_exception) {
            return ajax()->error($validation_exception->validator->errors()->first());
        } catch (\Exception $exception) {
            sentryCaptureException($exception);
            return ajax()->error('حدث خطأ غير متوقع، يرجى التواصل مع الدعم الفني لسلة!');
        }
    }

    /**
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse|mixed|\Salla\Core\Services\Ajax
     */
    public function setPaymentMethod(SallaCart $cart)
    {
        $this->cartService->setCart($cart);

        SetCartPaymentMethodAction::make(request()->all())->run();

        return $this->redrawCart();
    }

    public function setInstallmentPayment(SallaCart $cart)
    {
        $this->cartService->setCart($cart);

        $parameters = [];
        if ($this->cartService->isInstallmentAllowed() && request()->get('status') === 'active' ) {
            $parameters['is_installment_payment'] =  true;
            RemoveCouponAction::make()->setCart($cart)->run();
            SetCartPaymentMethodAction::make(
                ['payment_method' =>
                    ['loyalty-point' => 'inactive']
                ])->run();
            SetCartPaymentMethodAction::make(
                ['payment_method' =>
                    ['credit' => 'inactive']
                ])->run();
        } else {
            $parameters['is_installment_payment'] = false;
        }

        $this->cartService->setCartParameters($parameters);
        $this->cartService->refreshTotals();

        return $this->redrawCart();
    }

    public function delete(SallaCartItem $cartItem)
    {
        RemoveItemAction::make()->setItem($cartItem)->run();

        return $this->redrawCart();
    }

    public function pay(SallaCart $cart, CartPaymentRequest $request)
    {
        $this->cartService->setCart($cart);
        $check_error_coupon = $this->cartService->checkCouponAvailability();
        if (!empty($check_error_coupon)) {
            return ajax()->error($check_error_coupon);
        }

        $intercomSurveyId = null;
        if (store()->hasPlan(SallaProductPlanType::BASIC) && $this->cartService->getProductPlanFromCart()) {
            $intercomSurveyId = app(CustomerEffortScorePageRepository::class)->getSurveyByPageKey(CustomerEffortScorePageType::SALLA_PLAN_ENROLLMENT);
        }

        $payment = CreatePurchases::make($request->all())
            ->setPaymentRequest($request)
            ->setReference($this->cartService)
            ->run();

        $checkPayment = $this->cartService->checkPayment($payment);
        if ($checkPayment) {
            return $checkPayment;
        }

        if ($request->source == SallaCartSource::ONBOARDING && $plan_product = $this->cartService->getProductPlanFromCart()) {
            DashboardTracking::addCustomTracker(new CheckoutTracking($plan_product->product->type_value, $plan_product->period));
        }

        if ($cart->source == SallaCartSource::ONBOARDING && $payment->isSuccess() && $this->cartService->hasPlan()) {
            // check first product status and update it if necessary
            $firstProduct = store()->products()->whereIn('status', ProductStatus::availableStatues())->first();
            $plan = $this->cartService->getProductPlanFromCart()->product->type_value;
            if ($firstProduct && $firstProduct->status == ProductStatus::HIDDEN && in_array($firstProduct->type, ProductTypePresenter::getValidTypes($plan))) {
                $firstProduct->update([
                    'status' => ProductStatus::SALE
                ]);
            }
        }

        \Widget::run(SeoThankYouWidget::class, [
            'order' => $payment->getOrder()->getOrderModel(),
        ]);

        $javascript = '$(\'.button_show_cart\').hide(); setTimeout(function() { location.href = location.href;}, 2500);';
        if ($cart->source == SallaCartSource::APP) {
            /**
             * @var SallaCartItem $cart_item
             */
            $cart_item = $cart->items->first();

            /**
             * @var InstallAppPresenter $resultInstallApp
             */
            $resultInstallApp = CompleteInstallAppAction::make([
                'app' => $cart_item->product->marketplaceApp,
            ])->run();

            if ($resultInstallApp->getType() == InstallAppPresenter::TYPE_NEED_AUTHORIZE) {
                $javascript = ' setTimeout(() => openHydraModal("' . implode('","', $cart_item->product->marketplaceApp->getInstallData()) . '") , 500);';
            } else {
                $javascript = ' setTimeout(() => redirectTo("' . $resultInstallApp->getResultUrlWithMessage($cart_item->product->marketplaceApp) . '") , 500);';
                if(feature('install-app-method-v2')->isHaveFeature()) {
                    $javascript .= ' $("#modal_install_app").modal("hide")';
                }
            }
        } else if ($cart->source == SallaCartSource::EXPERT) {
            $expertUrl = route('cp.expert.index') . '/orders/r' . optimus()->encode($payment->getOrder()->getOrderId());
            $javascript = '$(\'.button_show_cart\').hide();  setTimeout(() => window.location.replace("' . $expertUrl . '") , 500);';
        } else if ($cart->source == SallaCartSource::INFLUENCER) {
            $expertUrl = route('cp.marketing.influencers') . '/campaigns/r' . optimus()->encode($payment->getOrder()->getOrderId());
            $javascript = '$(\'.button_show_cart\').hide();  setTimeout(() => window.location.replace("' . $expertUrl . '") , 500);';
        }

        if ($intercomSurveyId) {
            $javascript .= ' $("#modal_salla_cart").modal("hide"); Intercom("startSurvey", ' . $intercomSurveyId . ')';
            return ajax()->runJavascript($javascript);
        }

        if ($request->source === SallaCartSource::MOBILE_APP) {
            $mobileAppRoute = 'mobile_app.index';
            $finalAppRequest = store()->getFinalAppRequest();
            if ($finalAppRequest && $finalAppRequest->status == 'new') {
                $mobileAppRoute = 'mobile_app.info';
            }

            return ajax()->redirect(route($mobileAppRoute));
        }

        if ($cart->source == SallaCartSource::MI_CAMPAIGN) {
            $route = route('dashboard.addons', ['sweply', 'thank-you']);
            $javascript = "$('.button_show_cart').hide(); setTimeout(function() { location.href = '$route';}, 1500);";
        }

        return ajax()->showModal('#modal_complete_payment')
            //if there is need to not run location.reload. pass withoutJs in the request,
            ->when(!request()->has('withoutJs'), function (Ajax $ajax) use ($javascript) {
                return $ajax->runJavascript($javascript);
            });
    }

    public function confirm(SallaCart $cart)
    {
        return CartCompleteHandler::make()->setCart($cart)->run();
    }

    public function update(SallaCartItem $cartItem, UpdateQuantityRequest $request)
    {
        $cartItem->update([
            'quantity' => $request->get('value'),
        ]);

        $this->cartService->setSource($cartItem->cart->source);

        return $this->redrawCart();
    }

    public function updateFeature(SallaCartItem $cartItem, SallaProductPriceFeature $priceFeature, UpdateFeatureQuantityRequest $request)
    {
        UpdateItemFeatureAction::make([
            'cartItem'     => $cartItem,
            'priceFeature' => $priceFeature,
            'quantity'     => $request->get('value', 1),
        ])->run();

        $this->cartService->setSource($cartItem->cart->source);

        return $this->redrawCart();
    }

    public function productPriceOptionSelected(SallaProducts $product)
    {
        try {
            AddItemAction::make(request()->all())->setProduct($product)->run();
        } catch (ValidationException $validation_exception) {
            return ajax()->error($validation_exception->validator->errors()->first());
        } catch (\Exception $exception) {
            sentryCaptureException($exception);

            return ajax()->error('حدث خطأ غير متوقع، يرجى التواصل مع الدعم الفني لسلة!');
        }

        return $this->redrawCart();
    }

    private function getAppleIdentifier(): string
    {
        return InAppSubscriptionIdentifier::query()->firstOrCreate(
            ['user_id' => auth()->id()],
            ['uuid' => Str::uuid()]
        )->uuid;
    }
}
