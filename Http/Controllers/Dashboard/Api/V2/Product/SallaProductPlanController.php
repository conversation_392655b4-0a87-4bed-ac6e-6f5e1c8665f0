<?php

namespace Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\Product;

use App\Http\Controllers\Controller;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\MarketPlace\Http\Requests\Dashboard\Api\SallaProductPlanRequest;
use Modules\MarketPlace\Services\SallaProductService;
use Modules\MarketPlace\Services\SallaSubscriptionService;
use Symfony\Component\HttpFoundation\Response;

class SallaProductPlanController extends Controller
{
    public function __invoke(SallaProductPlanRequest $request)
    {
        $user              = $request->user();
        $infoPartner       = null;
        $isValidInvitation = app(SallaSubscriptionService::class)->hasValidInvitation($user);

        if ($isValidInvitation && $user->utmTracker?->utm_source === 'vip_partners') {
            $request->merge(['utm_source' => $user->utmTracker->utm_source]);

            $company = PartnerCompany::query()->find($user->utmTracker->utm_campaign);
            $infoPartner = (object)[
                'name'  => $company?->name,
                'email' => $company?->email,
            ];
        }

        return responder()
            ->success([
                'plans' => resolve(SallaProductService::class)->getPlans(store()->isCharity(), $request),
                'store' => [
                    'has_verified_documents'  => store()->isStoreVerified(),
                    'info_partner'           => $infoPartner,
                    'entity'                 => store()->entity,
                ],
            ])
            ->respond(Response::HTTP_OK);
    }
}
