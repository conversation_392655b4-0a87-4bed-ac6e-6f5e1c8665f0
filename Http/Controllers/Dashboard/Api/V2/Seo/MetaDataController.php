<?php

namespace Modules\Settings\Http\Controllers\Dashboard\Api\V2\Seo;


use Illuminate\Routing\Controller;
use Modules\Store\Actions\GetMetaData;
use Modules\Store\Events\SeoMateDateUpdated;
use Modules\Store\Features\SeoMetaTagFeature;
use Modules\Store\Http\Requests\MetaDataRequest;
use Salla\FeatureRules\Facades\FeaturesManager;

class MetaDataController extends Controller
{
    public function index()
    {
        return responder()->success(GetMetaData::make()->run());
    }

    public function store(MetaDataRequest $request)
    {
        if (!FeaturesManager::accessible('seo-meta-tag') ||
            !in_array(store()->plan, (new SeoMetaTagFeature)->getSupportedPlans())) {
            return ajax()->error(FeaturesManager::getMessage('seo-meta-tag'));
        }

        $this->storeMetaData($request->getReadyData());

        $newUrls = $request->get(key: 'isSeoProductLinkEnabled') == 2;

        switch ($request->get(key: 'isSeoProductLinkEnabled')) {
            case 1:
                store()->setSetting('seo::findlay-links', 1);
                store()->setSetting('seo::new-links', false);

                break;
            case 2:
                store()->setSetting('seo::findlay-links', 1);
                store()->setSetting('seo::new-links', true);

                break;
            default:
                store()->setSetting('seo::findlay-links', 0);
                store()->setSetting('seo::new-links', false);

                break;
        }

        event(new SeoMateDateUpdated(store()->getCurrent()));

        clearStoreCache(store()->getId());

        return responder()->success(GetMetaData::make()->run());
    }

    public function destroy()
    {
        $this->storeMetaData([
            'description' => "",
            'keywords'    => "",
            'title'       => "",
        ]);

        store()->setSetting('seo::findlay-links', false);

        return ajax()->success('تم استعادة البيانات الافتراضية بنجاح');
    }

    private function storeMetaData($meta_data): void
    {
        $storeMetaData = json_decode((string)store()->meta_data, true);

        $meta_data = !empty($storeMetaData['favicon']) && empty($meta_data['favicon'])
            ? array_merge($meta_data, ['favicon' => $storeMetaData['favicon']])
            : $meta_data;

        store()->getCurrent()->update(['meta_data' => json_encode($meta_data)]);

        clearStoreCache(store()->getId());
    }
}