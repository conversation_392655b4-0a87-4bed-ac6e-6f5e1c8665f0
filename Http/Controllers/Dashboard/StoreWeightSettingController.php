<?php


namespace Modules\Settings\Http\Controllers\Dashboard;


use Illuminate\Validation\Rule;
use Salla\Core\Enum\WeightTypeEnum;
use App\Http\Controllers\Controller;
use Salla\Core\Facades\WeightConverter;
use Salla\Core\Jobs\ClearCache;
use Symfony\Component\HttpFoundation\Response;
use Modules\Product\Features\ProductDefaultWeightFeature;

class StoreWeightSettingController extends Controller
{
    /**
     * @return \Salla\Core\Services\Ajax
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function storeValue()
    {
        if(!feature()->isHaveFeature(ProductDefaultWeightFeature::getName())){
            abort(Response::HTTP_FORBIDDEN, feature()->getMessage(ProductDefaultWeightFeature::getName()));
        }

        $weight = request()->get('weight');
        $validator = \Validator::make(
            ['weight' => $weight],
            ['weight' => 'required|min:0|not_in:0|regex:/^\d+(\.\d{1,10})?$/'],
            [],
            ['weight' => 'الوزن الافتراضي']
        );

        if ($validator->fails()) {
            return ajax()->error(implode(' , ', $validator->errors()->all()));
        }

        $this->saveSettingWeight($weight);

        return ajax();
    }

    /**
     * @return \Salla\Core\Services\Ajax
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function storeUnit()
    {
        $unit = request()->get('unit');
        $validator = \Validator::make(
            ['unit' => $unit],
            ['unit' => 'required', Rule::in(WeightTypeEnum::toArray())],
            [],
            ['unit' => 'نوع الوزن الافتراضي']
        );

        if ($validator->fails()) {
            return ajax()->error(implode(' , ', $validator->errors()->all()));
        }

        $this->saveSettingWeight(null, $unit);

        return ajax();
    }

    /**
     * @param null $weight
     * @param null $weight_unit
     */
    private function saveSettingWeight($weight = null, $weight_unit = null)
    {
        $shipment = store()->getSetting('order::shipment');
        if(!empty($weight)) {
            $shipment['real_weight'] = $weight;
        }

        if(!empty($weight_unit)) {
            $shipment['real_unit'] = $weight_unit;
        }

        $shipment['real_weight'] = !empty($shipment['real_weight']) ? $shipment['real_weight'] : 0.5;
        $shipment['real_unit'] = !empty($shipment['real_unit']) ? $shipment['real_unit'] : WeightTypeEnum::DEFAULT;
        $shipment['default_weight'] = WeightConverter::converterToDefault($shipment['real_weight'], $shipment['real_unit']);
        $shipment['default_unit']   = WeightTypeEnum::DEFAULT;

        store()->setSetting('order::shipment', $shipment);

        ClearCache::dispatchSync();
    }
}