<?php

namespace Modules\MarketPlace\Http\Requests\Dashboard\Api\V2;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class LineItemsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'item-id' => [
                'required',
                'integer',
                'min:1',
            ],
            'app' => [
                'required',
                'string',
                'max:255',
            ],
            'period' => [
                'required',
                'array',
            ],
            'period.year' => [
                'required',
                'integer',
                'min:2020',
                'max:2100',
                function ($attribute, $value, $fail) {
                    $allowedYears = $this->getAllowedYears();
                    
                    if (!in_array($value, $allowedYears)) {
                        $fail('The period year is not allowed for the current time. Allowed years: ' . implode(', ', $allowedYears));
                    }
                },
            ],
            'period.month' => [
                'required',
                'integer',
                'min:1',
                'max:12',
                function ($attribute, $value, $fail) {
                    $year = $this->input('period.year');
                    $allowedPeriods = $this->getAllowedPeriods();
                    
                    $periodKey = $year . '-' . str_pad($value, 2, '0', STR_PAD_LEFT);
                    
                    if (!in_array($periodKey, $allowedPeriods)) {
                        $allowedPeriodsFormatted = array_map(function($period) {
                            $parts = explode('-', $period);
                            return $parts[0] . '-' . intval($parts[1]);
                        }, $allowedPeriods);
                        $fail('The period is not allowed for the current time. Allowed periods: ' . implode(', ', $allowedPeriodsFormatted));
                    }
                },
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'quantity' => [
                'required',
                'integer',
                'min:1',
                'max:1000',
            ],
            'currency' => [
                'required',
                'string',
                'size:3',
                Rule::in(['SAR', 'USD', 'EUR', 'AED', 'KWD', 'BHD', 'OMR', 'QAR']),
            ],
            'unit_price' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'tax' => [
                'required',
                'array',
                'min:1',
            ],
            'tax.*.code' => [
                'required',
                'string',
                Rule::in(['S', 'Z', 'E', 'O']),
            ],
            'tax.*.rate' => [
                'required',
                'numeric',
                'min:0',
                'max:100',
            ],
            'tax.*.amount' => [
                'required',
                'numeric',
                'min:0',
            ],
            'discounts' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'reference' => [
                'required',
                'array',
            ],
            'reference.id' => [
                'required',
                'string',
                'max:255',
            ],
            'reference.type' => [
                'required',
                'string',
                'max:255',
            ],
            'reference.transaction_id' => [
                'nullable',
                'string',
                'max:255',
            ],

            'reference.ad_account_id' => [
                'nullable',
                'string',
                'max:255',
            ],
            'reference.platform_campaign_id' => [
                'nullable',
                'string',
                'max:255',
            ],
            'meta' => [
                'nullable',
                'array',
            ],
            'meta.note' => [
                'nullable',
                'string',
                'max:1000',
            ],
        ];
    }

    /**
     * Get the allowed years based on current time
     *
     * @return array
     */
    private function getAllowedYears()
    {
        $currentDate = now();
        $currentYear = $currentDate->year;
        $currentMonth = $currentDate->month;
        $currentHour = $currentDate->hour;
        
        // If it's the first hour of the year (January 1st, 0-1 AM), allow previous year too
        if ($currentMonth === 1 && $currentHour < 1) {
            return [$currentYear - 1, $currentYear];
        }
        
        // Otherwise, only allow current year
        return [$currentYear];
    }

    /**
     * Get the allowed periods based on current time
     * Returns array of period keys in format "YYYY-MM"
     *
     * @return array
     */
    private function getAllowedPeriods()
    {
        $currentDate = now();
        $currentYear = $currentDate->year;
        $currentMonth = $currentDate->month;
        // $currentHour = $currentDate->hour; // Remove this line
        
        $allowedPeriods = [];
        
        // Always allow current period
        $currentPeriod = $currentYear . '-' . str_pad($currentMonth, 2, '0', STR_PAD_LEFT);
        $allowedPeriods[] = $currentPeriod;
        
        // Check if we should allow previous period (grace period logic)
        if ($this->isInGracePeriod($currentDate)) {
            $previousPeriod = $this->getPreviousPeriod($currentYear, $currentMonth);
            $allowedPeriods[] = $previousPeriod;
        }
        
        return $allowedPeriods;
    }

    /**
     * Check if current time is within the grace period
     *
     * @param Carbon $currentDate
     * @return bool
     */
    private function isInGracePeriod($currentDate)
    {
        $currentHour = $currentDate->hour;
        
        // Grace period is the first hour of any month (0-1 AM)
        return $currentHour < 1;
    }

    /**
     * Get the previous period in YYYY-MM format
     *
     * @param int $year
     * @param int $month
     * @return string
     */
    private function getPreviousPeriod($year, $month)
    {
        $previousMonth = $month - 1;
        $previousYear = $year;
        
        // Handle year transition (December to January)
        if ($previousMonth < 1) {
            $previousMonth = 12;
            $previousYear = $year - 1;
        }
        
        return $previousYear . '-' . str_pad($previousMonth, 2, '0', STR_PAD_LEFT);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'item-id.required' => 'The item ID is required.',
            'app.required' => 'The app identifier is required.',
            'period.required' => 'The period information is required.',
            'period.year.required' => 'The period year is required.',
            'period.month.required' => 'The period month is required.',
            'name.required' => 'The item name is required.',
            'quantity.required' => 'The quantity is required.',
            'currency.required' => 'The currency is required.',
            'unit_price.required' => 'The unit price is required.',
            'tax.required' => 'The tax information is required.',
            'reference.required' => 'The reference information is required.',
            'reference.id.required' => 'The reference ID is required.',
            'reference.type.required' => 'The reference type is required.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'item-id' => 'item ID',
            'period.year' => 'period year',
            'period.month' => 'period month',
            'unit_price' => 'unit price',
            'reference.id' => 'reference ID',
            'reference.type' => 'reference type',
            'reference.transaction_id' => 'transaction ID',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Set default value for discounts if not provided
        if (!$this->has('discounts')) {
            $this->merge(['discounts' => 0]);
        }
    }
} 