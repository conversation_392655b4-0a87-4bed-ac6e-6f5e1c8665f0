<?php

namespace Modules\Settings\Http\Requests;

use App\Models\Bank;
use App\Models\StoreBankAccount;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Settings\Rules\StepVaildationRule;
use Modules\Settings\Traits\LockedHelper;
use Salla\Core\Entities\StoreDocument;
use Salla\Core\Enum\StoreDocumentsStatus;
use Salla\Core\Enum\StoreEntityEnum;

class StoreDocumentRequest extends FormRequest
{

    use LockedHelper;

    final public const FULL_NAME_WORDS = 2;

    private ?Bank $bank = null;

    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        //bank account
        $this->request->set(
            'alternate_identifier_enabled',
            $this->has('alternate_identifier_enabled') && 
            $this->get('alternate_identifier_enabled') == true
        );

        $this->request->set('identifier_type', (int)$this->get('identifier_type'));
    }

    public function rules(): array
    {

        $document = StoreDocument::withoutGlobalScope('approved')
            ->where('status', StoreDocumentsStatus::PENDING)
            ->first();

        $entity =
            $this->get('entity') ??
            $document->entity ??
            $document->store->entity;

        if (!$document){ throw new \Exception('No pending document found for IBAN verification.'); }

        $freelance_rules =
            $entity == StoreEntityEnum::PERSON ?
            $this->freelanceRules() :
            (
                $entity == StoreEntityEnum::CHARITY ?
                $this->charityRules() : []
            );

        $customRules = array_merge($freelance_rules,[
            'entity'                    =>  ['required'],
            'bank_id' => [
                'required',
                'max:255',
                'exists:banks,id',
                function ($attribute, $value, $fail) {
                    $this->bank = Bank::find($value);
                }
            ],
            'account_name'              => [
                'nullable',
                'max:255',
                'regex:/^[^(\|\]~`!%^&*=};:?><’)]*$/',
                function ($attribute, $value, $fail) {
                    $isLeanSupportedAndVerified = $this->bank && $this->bank->lean_supported && optional(store()->getMainPendingBankAccount())->auto_verified;
                    $minWords = $isLeanSupportedAndVerified ? 1 : static::FULL_NAME_WORDS;
                    if (count(explode(" ", $value)) < $minWords) {
                        $fail("يجب كتابة الاسم ثنائي او الثلاثي لتوثيق المتجر");
                    }
                }
            ],
            'account_number'            => ['nullable', 'max:255'],
            'iban_number'               => ['required', 'max:255', 'regex:/^[0-9]|[A-Z]+$/', function ($attribute, $value, $fail) {
                if(strlen(str_replace(' ', '', $value)) != 22){
                    $fail('رقم الايبان غير صحيح، الرجاء إعادة المحاولة والتأكد ان الايبان 22 رقم ');
                }
                else if (! verify_iban('SA'.str_replace(' ', '', $value))) {
                    $fail('رقم الايبان غير صحيح، الرجاء إعادة المحاولة');
                }
            }],
            'owner_identity_image'      => ['nullable', 'string', 'url'],
            'manager_identity_image'    => ['nullable', 'string', 'url'],
            'commercial_register_image'    => ['nullable', 'string', 'url'],
            'iban_certificate' => [
                'nullable',
                'url',
                'required_if:certificate_type,iban'
            ],
            'sbc_certificate' => [
                'nullable',
                'url',
                'required_if:certificate_type,sbc'
            ],
            'certificate_type' => [
                'nullable',
                Rule::in(['iban', 'sbc']),
            ],
            'account_type' => [
                Rule::requiredIf(
                    fn () =>
                        $entity === 'person'
                ),
                'nullable',
                Rule::in([StoreBankAccount::PERSONAL, StoreBankAccount::BUSINESS]),
            ],
            'request_validation_box'    => ['required']
        ]);

        $this->addLeanVerificationRules(
            $customRules,
            ($this->bank ? $this->bank->lean_supported : false),
            $entity
        );

        return array_merge($freelance_rules, $customRules);
    }

    public function attributes()
    {
        return [
            'commercial_register_image' => 'صورة السجل التجاري',
            'freelance_number'          => 'رقم شهادة العمل الحر',
            'freelance_certificate'     => 'شهادة العمل الحر',
            'bank_id'                   => 'البنك',
            'iban_certificate'          => 'شهاده الايبان',
            'sbc_certificate'           => 'شهاده منصة الأعمال',
            'charity_certificate_image' => 'صورة شهادة الجمعية',
            'request_validation_box'    => 'إقرار صحة المعلومات',
            'account_type'              => 'نوع الحساب البنكي',
            'certificate_type'          => 'نوع شهادة التوثيق',
        ];
    }

    public function messages(){
        return [
            'account_name.regex'                => 'اسم الحساب  يجب ان لايحتوي على حروف خاصة مثل ـ-@ او سطر جديد',
            'iban_number.regex'                 => 'الرجاء كتابة ارقام وحروف فقط',
            'iban_certificate.required_if'      => 'شهادة الايبان مطلوبة في حالة اختيار نوع الشهادة ايبان',
            'sbc_certificate.required_if'      => 'شهادة منصة الأعمال مطلوبة في حالة اختيار نوع الشهادة منصة الأعمال',
            'request_validation_box.required'   => 'الرجاء الضغط علي اختيار الإقرار بصحة المعلومات الواردة في الصفحة قبل إرسال الطلب'
        ];
    }

    private function freelanceRules()
    {
        return
            [
                'freelance_number' => ['required', 'regex:/^FL-\d{8,9}$/'],
                'freelance_certificate' => ['required', 'url'],
            ];
    }

    private function charityRules()
    {
        return
            [
            'charity_certificate_image' => ['required', 'url', new StepVaildationRule('charity')],
            ];
    }

    private function addLeanVerificationRules(array &$customRules, bool $leanSupported, $entity): void
    {
        //check if the iban is not locked
        $ibanIsNotLocked = !$this->ibanIsLocked();

        if (
            !$ibanIsNotLocked &&
            $leanSupported &&
            !optional(store()->getMainPendingBankAccount())->auto_verified &&
            in_array(
                $entity,
                [
                    'person',
                    'company',
                    'firm'
                ]
            )
        ) {
            $customRules['iban_number'][] = 'required';
            $customRules['iban_number'][] = function ($attribute, $value, $fail) { $fail('يجب التحقق من الأيبان أولاً'); };
        }
    }
}
