<?php

namespace Modules\Settings\Http\Requests;

use App\Models\StoreBankAccount;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Settings\Rules\IdentityNumberSuspendedRule;
use Modules\Settings\Rules\StepVaildationRule;
use Salla\Core\Entities\StoreDocument;
use Salla\Core\Enum\StoreDocumentsStatus;

class IbanVerificationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {

        $document = StoreDocument::withoutGlobalScope('approved')->where('status', StoreDocumentsStatus::PENDING)->first();

        if(!$document){
            throw new \Exception('No pending document found for IBAN verification.');
        }

        $entity = $this->get('entity') ?? $document->entity ?? $document->store->entity;

        $accountTypeRules = [
            Rule::in([
                StoreBankAccount::PERSONAL,
                StoreBankAccount::BUSINESS,
            ]),
        ];

        if ($entity === 'person') {
            array_unshift(
                $accountTypeRules,
                'required'
            );
        } else {
            array_unshift($accountTypeRules, 'nullable');
        }

        return [
            'national_id' => ['required', new StepVaildationRule('iban')],
            'bank_id'                   => ['required', 'max:255', 'exists:banks,id'],
            'iban_number'               => ['required', 'max:255', 'regex:/^[0-9]|[A-Z]+$/', function ($attribute, $value, $fail) {
                if(strlen(str_replace(' ', '', $value)) != 22){
                    $fail(__('settings::messages.invalid_iban_length'));
                }
                else if (! verify_iban('SA'.str_replace(' ', '', $value))) {
                    $fail(__('settings::messages.invalid_iban'));
                }
            }],
            'account_type' => $accountTypeRules,
        ];
    }

    public function attributes()
    {
        return [
            'national_id' => __('settings::messages.national_id'),
            'bank_id' => __('settings::messages.bank_id'),
            'iban_number' => __('settings::messages.iban_number'),
            'account_type' => __('settings::messages.account_type'),
        ];
    }
}
