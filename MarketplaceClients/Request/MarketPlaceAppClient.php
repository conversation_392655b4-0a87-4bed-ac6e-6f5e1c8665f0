<?php


namespace Modules\MarketPlace\MarketplaceClients\Request;

use Modules\MarketPlace\MarketplaceClients\PrepareAccountApiProtalClient;
use Modules\MarketPlace\MarketplaceClients\Response\AuthorizeAppResponse;
use Modules\MarketPlace\MarketplaceClients\Response\ConsentAcceptAppResponse;
use Modules\MarketPlace\MarketplaceClients\Response\MarketPlaceAppScopeResponse;

class MarketPlaceAppClient extends PrepareAccountApiProtalClient
{
    /**
     * Get scopes of app and check some confition related to merchant and plan
     * @param array $parameter
     * @return mixed|MarketPlaceApp|\Salla\ApiResponse\ApiResponse|\Salla\ApiResponse\Base\ResponseModel
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function getScopes($parameter = [])
    {
        $response = $this->api->get("api/accounts/v2/scopes", $parameter);
        if (! $response->isSuccess()) {
            $responseData = MarketPlaceAppScopeResponse::forResponse($response);
            $errorResponse = json_decode($response->getErrorMessage());
            if(!empty($errorResponse->message)) {
                $responseData->setMessage($errorResponse->message);
            }

            return $responseData->setCode($response->getErrorCode());
        }

        return MarketPlaceAppScopeResponse::forResponse($response)
            ->fromResponseObject($response->getResult()->data);
    }

    /**
     * Accept App
     * @param array $parameter
     * @return mixed|ConsentAcceptAppResponse|\Salla\ApiResponse\ApiResponse|\Salla\ApiResponse\Base\ResponseModel
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function consentApp($parameter = [])
    {
        $response = $this->api->get("api/accounts/v2/consent/accept", $parameter);
        if (! $response->isSuccess()) {
            $responseData = ConsentAcceptAppResponse::forResponse($response);
            $errorResponse = json_decode($response->getErrorMessage());
            if(!empty($errorResponse->message)) {
                $responseData->setMessage($errorResponse->message);
            }

            return $responseData;
        }

        return ConsentAcceptAppResponse::forResponse($response)
            ->fromResponseObject($response->getResult()->data);

    }

    /**
     * @param $parameter
     * @return mixed|AuthorizeAppResponse|\Salla\ApiResponse\ApiResponse|\Salla\ApiResponse\Base\ResponseModel
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function authorizeApp($parameter = [])
    {
        $response = $this->api->get("api/accounts/v2/authorize", $parameter);
        if (! $response->isSuccess()) {
            $responseData = AuthorizeAppResponse::forResponse($response);
            $errorResponse = json_decode($response->getErrorMessage());
            if(!empty($errorResponse->message)) {
                $responseData->setMessage($errorResponse->message);
            }

            return $responseData;
        }

        return AuthorizeAppResponse::forResponse($response)
            ->fromResponseObject($response->getResult()->data);
    }
}