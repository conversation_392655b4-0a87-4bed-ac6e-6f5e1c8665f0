<?php

namespace Modules\MarketPlace\Services;

use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Filters\SallaProduct\TypeValueFilter;
use Modules\MarketPlace\Http\Requests\Dashboard\Api\SallaProductPlanRequest;
use Modules\MarketPlace\Presenter\Dashboard\CharityPlanPresenter;
use Modules\MarketPlace\Presenter\Dashboard\PlanPresenter;
use Modules\MarketPlace\Repositories\SallaProductRepository;
use Modules\Store\Enum\SallaProductType;
use Salla\Core\Enum\Plan;
use Exception;

class SallaProductService
{
    protected $lateInstallment = null;

    public function __construct(protected SallaProductRepository $productRepository)
    {
    }

    public function getPlans(bool $isCharity, SallaProductPlanRequest $request): array
    {
        $this->handleStorePlan();
        $customFilters      = $this->buildCustomFilters($request);
        $determinePlanLevel = $this->determinePlanLevel();
        $productRepository  = $this->prepareProductRepository($customFilters, $request, $determinePlanLevel, $isCharity);
        $plans              = $productRepository->withOutProductPriceCampaign()->get();
        $utmData = array_filter([
            'utm_source'   => $request->get('utm_source'),
            'utm_campaign' => $request->get('utm_campaign'),
        ]);

        if (!$isCharity && !empty($utmData)) {
            $planCampaign = $this->prepareProductRepository($customFilters, $request, $determinePlanLevel)
                ->withFilteredProductPrices(utmData: $utmData)
                ->get();

            $plans = $planCampaign->merge($plans)->unique('id');
        }

        if ($isCharity || ($request->get('utm_source') === 'vip_partners' && $plans->count() > 1)) {
            $plans = $plans->sortBy('id')->take(1);
        }

        return $plans
            ->map(fn($plan) => $isCharity
                ? CharityPlanPresenter::make($plan)
                : PlanPresenter::make($plan, $this->lateInstallment)
            )
            ->values()
            ->toArray();
    }

    public function handleStorePlan(): void
    {
        if (is_null(store()->plan)) {
            throw new Exception("The column 'plan' on the store ID: " . store()->getId() . " is null.");
        }

        if (!store()->isPaid()) {
            $this->lateInstallment = store()->installments()
                ->whereHas('latePayments')
                ->with('latePayments')
                ->latest()
                ->first();
        }
    }

    public function determinePlanLevel(): object
    {
        $level = match ($this->lateInstallment ? $this->lateInstallment->subscription->type_value : store()->plan) {
            Plan::BASIC => 1,
            Plan::PLUS  => 2,
            Plan::PRO   => 3,
            default     => 4, // Handling 'special' plan
        };

        if (store()->isCharity() && !store()->isStoreVerified() && SallaProductPlanType::isBasicPlan(store()->plan)) {
            return (object)[
                'level'         => 3,
                'isCurrentPlan' => true,
                'skipSpecial'   => true,
            ];
        }
        return (object)[
            'level'         => $level,
            'isCurrentPlan' => $level !== 1,
            'skipSpecial'   => $level !== 4,
        ];
    }

    private function buildCustomFilters(SallaProductPlanRequest $request): array
    {
        $customFilters = [];
        if ($request->has('type_value')) {
            $customFilters[] = new TypeValueFilter($request->input('type_value'));
        }

        if ($request->has('utm_source') && $request->get('utm_source') == 'vip_partners') {
            $customFilters[] = new TypeValueFilter(Plan::PRO);
        }

        return $customFilters;
    }

    private function applyMarketplaceLogic(SallaProductRepository $productRepository, SallaProductPlanRequest $request, object $planLevel): SallaProductRepository
    {
        if ($request->request_type === 'marketplace') {
            $productRepository = $productRepository->byLevel(
                includeCurrentPlan: $planLevel->isCurrentPlan,
                level: $planLevel->level
            );

            if ($planLevel->level !== 4) {
                $productRepository = $productRepository->byTypeValue([Plan::BASIC, Plan::PLUS, Plan::PRO]);
            }
            return $productRepository;

        }

        return $productRepository->orderBySort();
    }

    private function applyCustomFilters(SallaProductRepository $productRepository, array $customFilters): SallaProductRepository
    {
        foreach ($customFilters as $filter) {
            $filter->apply($productRepository->getModel());
        }

        return $productRepository;
    }

    private function prepareProductRepository(
        array $customFilters,
        SallaProductPlanRequest $request,
        object $planLevel,
        bool $isCharity = false
    ): SallaProductRepository {
        $productRepository = $this->productRepository
            ->withRelations()
            ->byType(SallaProductType::PLAN)
            ->notSandbox()
            ->byHide(0)
            ->byStatus(SallaSubscriptionStatus::ACTIVE)
            ->applyCharityFilter($isCharity)
            ->excludePartners();

        $productRepository = $this->applyMarketplaceLogic($productRepository, $request, $planLevel);

        return $this->applyCustomFilters($productRepository, $customFilters);

    }

}