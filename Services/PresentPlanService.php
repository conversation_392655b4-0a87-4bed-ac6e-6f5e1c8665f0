<?php

namespace Modules\MarketPlace\Services;

use Exception;
use Illuminate\Support\Collection;
use Modules\MarketPlace\Entities\SallaInstallment;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Presenter\Dashboard\{CharityPlanPresenter, PlanPresenter, AddonPresenter, StorePlanPagePresenter};
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Repositories\SallaProductRepository;
use Prettus\Repository\Exceptions\RepositoryException;
use Salla\Core\Enum\Plan;

class PresentPlanService
{
    /**
     * This flag to determine whether to fetch current plan from DB or not.
     */
    protected ?SallaInstallment $lateInstallment = null;

    protected bool $includeCurrentPlan = true;

    protected bool $hasLifetimeSubscription = false;

    protected bool $notVerifiedCharity = false;

    protected int $level = 1;

    /**
     * @throws Exception
     */
    public function data(): StorePlanPagePresenter
    {
        if (is_null(store()->plan)) {
            throw new Exception("The column 'plan' on the store ID: " . store()->getId() . " is null.");
        }

        if (!store()->isPaid() ) {
            $this->lateInstallment = store()->installments()->whereHas('latePayments')->with('latePayments')->latest()->first();
        }

        switch ($this->lateInstallment ? $this->lateInstallment->subscription->type_value : store()->plan) {
            case Plan::BASIC:
                $this->preparePresenterForLevel(1);
                break;
            case Plan::PLUS:
                $this->preparePresenterForLevel(2);
                break;
            case Plan::PRO:
                $this->preparePresenterForLevel(3);
                break;
            default:
                $this->preparePresenterForLevel(4); // handling 'special' plan
        }

        return $this->generatePresenter();
    }

    protected function preparePresenterForLevel(int $level): void
    {
        $this->level = $level;

        // If current plan is 'basic', then it means get all possible upgrades except the current one.
        if ($level === 1) {
            $this->includeCurrentPlan = request()->get('context', false) == 'onboarding';
        }
    }

    protected function generatePresenter(): StorePlanPagePresenter
    {
        $plans   = $this->plans();
        $utmData = array_filter([
            'utm_source'   => request()->get('utm_source'),
            'utm_campaign' => request()->get('utm_campaign'),
        ]);

        if (!store()->isCharity() && !empty($utmData)) {
            $planCampaign = app(SallaProductRepository::class)
                ->withRelations()
                ->byType(SallaProductType::PLAN)
                ->notSandbox()
                ->byHide(0)
                ->byStatus(SallaSubscriptionStatus::ACTIVE)
                ->excludePartners()
                ->withFilteredProductPrices(utmData: $utmData)
                ->get();

            $plans = $planCampaign->merge($plans)->unique('id');
        }

        $plans = $plans->map(fn($plan) =>
                                            $this->hasLifetimeSubscription || $this->notVerifiedCharity ?
                                                CharityPlanPresenter::make($plan) :
                                                PlanPresenter::make($plan, $this->lateInstallment));
        $addons = $this->addons()->map(fn($addon) => AddonPresenter::make($addon));

        return StorePlanPagePresenter::make($this->lateInstallment)
            ->setPlans($plans)
            ->setAddons($addons)
            ->setLifetimeSubscription($this->hasLifetimeSubscription)
            // you can chain multiple alerts like the below and pass it to presenter
            ->addAlert($this->toggleDocumentVerificationAlert());
    }

    protected function toggleDocumentVerificationAlert(): object
    {
        return (object)[
            'key' => 'show_document_verification',
            'value' => store()->isCharity() && ! store()->isStoreVerified(),
        ];
    }

    /**
     * @return Collection<SallaProducts | []>
     */
    protected function plans()
    {
        if (store()->hasLifetimeSubscription()) {
            $this->hasLifetimeSubscription = true;

            return collect([ '0' =>
                store()->planInfo->sallaProduct
            ]);
        }

        // return pro plan for not verified charity entities
        if (store()->isCharity() && !store()->isStoreVerified() && SallaProductPlanType::isBasicPlan(store()->plan)) {
            $this->notVerifiedCharity = true;

            return app(SallaProductRepository::class)
                ->plansForStorePlanPage(true, 3, true);
        }

        $skipSpecialPlan = $this->level !== 4;

        return app(SallaProductRepository::class)
            ->plansForStorePlanPage($this->includeCurrentPlan, $this->level, $skipSpecialPlan);
    }

    /**
     * @return Collection<SallaProducts | []>
     */
    protected function addons()
    {
        // No need to bring addons if the store has basic plan.
        if (store()->hasPlan(Plan::BASIC)) {
            return collect();
        }

        return app(SallaProductRepository::class)->addons()->orderBy('sort')->get();
    }
}