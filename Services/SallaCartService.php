<?php

namespace Modules\MarketPlace\Services;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Modules\Checkout\Actions\Payment\Traits\HasFriendlyMessage;
use Modules\MarketPlace\Entities\Filters\ProductFilter;
use Modules\MarketPlace\Entities\SallaCart;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaCartItemFreeProduct;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Entities\SallaProductPriceDiscount;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaCartSource;
use Modules\MarketPlace\Enum\SallaInstallmentStatus;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Repositories\SallaCartRepository;
use Modules\MarketPlace\Repositories\SallaCouponHistoryRepository;
use Modules\MarketPlace\Repositories\SallaCouponRepository;
use Modules\MarketPlace\Services\Coupon\CouponHandlerService;
use Modules\MarketPlace\Traits\Cart\HasTotal;
use Modules\MarketPlace\Traits\Product\HasPriceDiscounts;
use Modules\Store\Service\StoreSenderRequestService;
use Omnipay\Common\ItemBag;
use Salla\Core\Traits\Helper\HasCachableAttributes;
use Salla\Events\Salla\Product\ProductRemovedFromCart;
use Salla\Paymetns\Contracts\Invoice\InvoiceReference;
use Salla\Paymetns\Data\Item;
use Salla\Paymetns\Models\Invoice;
use Salla\Core\Enum\StoreDocumentStatus;
use Salla\Core\Entities\StoreDocument;
use Salla\Core\Scopes\CurrentStoreScope;
use Modules\MarketPlace\Services\Coupon\ListSallaCouponFilterService;
use Modules\MarketPlace\Transformers\ListSallaCouponsTransformer;

class SallaCartService implements \Modules\MarketPlace\Contract\SallaCartService, InvoiceReference
{
    use HasTotal,
        HasCachableAttributes,
        HasPriceDiscounts,
        HasFriendlyMessage;

    /**
     * @var SallaCartRepository|Model|Collection
     */
    protected $cartRepository;

    /**
     * @var SallaCart
     */
    protected $cart;

    protected $totals;

    protected $uncompletedStores;

    /**
     * @var User $user
     */
    private $user = null;

    private $source = null;

    public function __construct(SallaCartRepository $cartRepository)
    {
        $this->cartRepository = $cartRepository;
    }

    public function findCart($id)
    {
        return $this->cartRepository->find($id);
    }

    public function hasItem(SallaProducts $product)
    {
        return $this->cartRepository->hasItem(
            $this->getUser(),
            $this->getCart(),
            $product,
            $this->getSource()
        );
    }

    public function getUser()
    {
        return !empty($this->user) ? $this->user : ($this->cart->user ?? auth()->user());
    }

    public function setUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @return \Modules\MarketPlace\Entities\SallaCart
     */
    public function getCart($refresh = false)
    {
        if (
            !$refresh &&
            $this->cart &&
            (
                (empty($this->getSource())) ||
                (
                    (!empty($this->getSource())) &&
                    ($this->cart->source = $this->getSource())
                )
            )
        ) {
            return $this->cart;
        }

        return $this->cart = $this->cartRepository->getCart($this->getUser(), $this->getSource());
    }

    /**
     * @return \App\Services\SallaCartService
     */
    public function setCart(SallaCart $cart): self
    {
        $this->cart = $cart;

        $this->setUser($cart->user);
        $this->setSource($cart->source);

        // because sometimes we load the cart for mobile app
        // so we need to make sure we load the current store from cart
        // in case the session is missing for any reason
        if (!app('store')->hasStore()) {
            app('store')->setCurrent($this->cart->user->store);
        }

        return $this;
    }

    public function getSource()
    {
        return request()->get('source', $this->source);
    }

    public function setSource($source)
    {
        $this->source = $source;
        if (!request()->filled('source')) {
            request()->request->add(['source' => $source]);
        }

        return $this;
    }

    public function isNotEmpty(): bool
    {
        return !$this->isEmpty();
    }

    public function isEmpty(): bool
    {
        return $this->getItems()->isEmpty();
    }

    public function getItems()
    {
        $this->getCart()->loadMissing('items.product');

        return $this->getCart()->items->filter(function (SallaCartItem $cartItem) {
            if (
                (
                    ($cartItem->product && !$cartItem->product->isBuyable()) &&
                    (
                        !$cartItem->product->isTheme() ||
                        ($this->source != SallaCartSource::TRANSFER_STORE) //this special state to enable transfer store buying the theme
                    )
                ) ||
                ProductFilter::isHidden($cartItem->product) ||
                $this->checkSameProductExit($cartItem)
            ) {
                $cartItem->setAppends([]);
                $cartItem->delete();

                event(new ProductRemovedFromCart($cartItem));

                return false;
            }

            return $cartItem->product;
        });
    }

    private function checkSameProductExit(SallaCartItem $cartItem)
    {
        return $this->getCart()->items()->where('id', '!=', $cartItem->id)
            ->where('product_id', $cartItem->product_id)
            ->where('product_price_id', $cartItem->product_price_id)
            ->exists();
    }

    /**
     * @return mixed|null
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function getCouponCode()
    {
        return (new CouponHandlerService($this->getCart()))->getCouponCode();
    }

    public function getStoreAvailableCoupons($storeId, $cartSubTotal) {
       $coupons = app(SallaCouponRepository::class)->getStoreAvailableCoupons($storeId);
       return $this->filterAndSortCoupons($coupons, $cartSubTotal);
    }

    /**
     * @param bool $excludeSaleProducts
     * @return mixed
     */
    public function getSubTotal($excludeSaleProducts = false)
    {
        if (!(bool)$excludeSaleProducts) {
            return $this->getTotal();
        }

        return $this->getTotal() - $this->getItems()->sum(function (SallaCartItem $cartItem) {

                /** @var SallaProductPriceDiscount $priceDiscount */
                $priceDiscount = $this->getDiscountedPrice($cartItem);

                // let us recalculate any conditioned discounts to avoid any changes on it amounts
                if ($priceDiscount) {
                    return $cartItem->quantity * $priceDiscount->discounted_price;
                }

                return $cartItem->quantity * $cartItem->sale_price;
            });
    }

    private function getDiscountedPrice($cartItem){

        if (SallaProductType::IsPLAN($cartItem->product->type)) {

            return $this->getCompatiblePriceDiscount($cartItem->priceWithTrashed);
        }

        return null;
    }

    public function getTotal()
    {
        $this->getCart()->loadMissing(['items.price.product', 'items.promotion']);

        return $this->getItems()->sum(function (SallaCartItem $cartItem) {

            /** @var SallaProductPriceDiscount $priceDiscount */
            $priceDiscount = $this->getDiscountedPrice($cartItem);

            // let us recalculate any conditioned discounts to avoid any changes on it amounts
            if ($priceDiscount) {
                return $priceDiscount->getPrice()->getBasePriceAsFloat();
            }

            return $cartItem->getPrice()->getBasePriceAsFloat();
        });
    }

    public function hasOffer()
    {
        return $this->cartRepository->getSaleItems($this->getCart()) > 0;
    }

    public function getUserOfCart()
    {
        return $this->getCart()->user;
    }

    public function checkSallaPlanPending()
    {
        $productPlanId = $this->getItems()->filter(function ($cartProduct) {
            return $cartProduct->product->type === SallaProductType::PLAN;
        })->pluck('product_id');

        if ($productPlanId->isEmpty()) {
            return false;
        }

        return SallaOrderItems::checkIfPlanPending()->exists();
    }

    public function checkSallaProductSenderName()
    {
        $product = $this->getItems()->filter(function ($cartProduct) {
            return $cartProduct->product->type === SallaProductType::SENDER_NAME;
        });

        if (!$product) {
            return false;
        }

        return !app(StoreSenderRequestService::class)->checkCanApplyForSenderName();
    }

    public function checkSallaProductsNeedPlan()
    {
        if (
            $this->getCart()->store->plan === SallaProductPlanType::BASIC &&
            !$this->hasPlan() &&
            $this->getItemsByTypeExcludingPartnerServices(SallaProductType::getNeedsPlanType())->isNotEmpty()
        ) {
            return true;
        }

        return false;
    }

    /**]
     * @param $types
     * @return Collection|SallaCartItem[]
     */
    public function getItemsByType($types)
    {
        $types = (array)$types;
        return $this->rememberAttribute('items_by_type_' . implode($types), function () use ($types) {
            return $this->getItems()->filter(function (SallaCartItem $item) use ($types) {
                return \in_array($item->product->type, $types, true);
            });
        });
    }

    /**
     * Get items by type excluding partner services.
     *
     * This function filters out items whose product's type value matches any
     * of the partner service types.
     *
     * @param array $types An array of product types to filter.
     * @return Collection|SallaCartItem[]
     */
    public function getItemsByTypeExcludingPartnerServices(array $types)
    {
        return $this->getItemsByType($types)->filter(function (SallaCartItem $item) {
            // Exclude items with type value in partner service types
            return !in_array($item->product->type_value, SallaProductAddonType::getPartnerServiceTypes(), true);
        });
    }


    public function hasPlan()
    {
        return $this->getItemsByType(SallaProductType::PLAN)->count() > 0;
    }

    public function getProductPlanFromCart()
    {
        return $this->getItemsByType(SallaProductType::PLAN)->first();
    }

    /**
     * @return mixed
     */
    public function getApps()
    {
        return $this->getItems()->filter(function (SallaCartItem $item) {
            return ($item->product->type == SallaProductType::ADDON) &&
                ($item->product->type_value == SallaProductAddonType::APPS);
        });
    }

    /**
     * @return mixed
     */
    public function getCartFreeProducts()
    {
        if(!$this->getCart() || $this->getCart()->items->isEmpty()) {
            return collect();
        }

        return SallaCartItemFreeProduct::whereIn('cart_item_id', $this->getCart()->items->pluck('id')->toArray())
            ->get();
    }

    public function getCallbackUrl(): string
    {
        return route('cp.marketplace.payment.landing', [
            'cart' => $this->getCart()->getRouteKey(),
        ]);
    }

    public function getModel(): Model
    {
        return $this->getCart();
    }

    public function getInvoice(): Invoice
    {
        return $this->getCart()->invoice;
    }

    public function getItemsBag(): ItemBag
    {
        return new ItemBag($this->getItems()->map(function ($item) {
            /* @var SallaCartItem $item */
            $tax = $this->getTotals()->getTaxPerItems()->get($item->getKey(), 0);

            return new Item([
                'id'          => $item->product->getKey(),
                'name'        => $item->product->name,
                'price'       => $item->getPrice()->getBasePriceAsFloat(),
                'discount'    => $this->getTotals()->getDiscountPerItems()->get($item->getKey(), 0),
                'tax'         => $tax ? $tax->getTax() : 0,
                'quantity'    => $item->quantity,
                'description' => 'normal',
                'risky'       => false,
            ]);
        })->toArray());
    }

    /**
     * @return \Modules\MarketPlace\Presenter\TotalPresenter
     * @throws \Exception
     */
    public function getTotals()
    {
        return $this->totals ?: ($this->totals = $this->calculateTotal());
    }

    public function supportSingleInvoice(): bool
    {
        return false;
    }

    /**
     */
    public function refreshTotals(): self
    {
        $this->totals = $this->calculateTotal();

        return $this;
    }

    public function removeItem($itemId): void
    {
        /** @var \App\Models\SallaCartItem $item */
        $item = SallaCartItem::find($itemId);
        if (empty($item)) {
            return;
        }

        $item->delete();
    }

    /**
     * @return mixed
     */
    public function hasInvoice()
    {

        return $this->getCart()->invoice;
    }

    /**
     * @return string|null
     */
    public function checkCouponAvailability()
    {
        if (empty($this->cart->coupon_code)) {
            return null;
        }

        $coupon_info = app(SallaCouponRepository::class)->checkByCode($this->cart->coupon_code);
        if (empty($coupon_info)) {
            return __t('marketplace::cart.messages.error.coupon code not correct or expired');
        }

        $couponHistoryRepository = app(SallaCouponHistoryRepository::class);

        $usage_limit_count = $couponHistoryRepository->getUsageLimit($coupon_info);
        if (!empty($coupon_info->usage_limit) && ($usage_limit_count >= $coupon_info->usage_limit)) {
            return __t('marketplace::cart.messages.error.you exceed the limit of use coupon');
        }

        $usage_limit_per_user_count = $couponHistoryRepository->getUsageLimitPerStore($coupon_info, store()->id);
        if (!empty($coupon_info->usage_limit_per_user) && ($usage_limit_per_user_count >= $coupon_info->usage_limit_per_user)) {
            return __t('marketplace::cart.messages.error.you exceed number of used');
        }
    }

    /**
     * @param array $parameters
     */
    public function setCartParameters($parameters = [])
    {
        /**
         * @var SallaCart $cart
         */
        $cart = $this->getCart();

        //special State must not happen
        if(empty($cart)) {
            return $this;
        }

        //if now parameter or new parameters added
        $parameters = array_merge((is_array($cart->parameters) ? $cart->parameters : []), $parameters);
        if(empty($parameters) && (count(array_diff($parameters, $this->cart->parameters)) == 0)) {
            return $this;
        }

        $cart->update([
            'parameters' => $parameters,
        ]);

        return $this;
    }

    /**
     * Reset Coupon of cart
     * @return $this
     */
    public function resetCoupon()
    {
        /**
         * @var SallaCart $cart
         */
        $cart = $this->getCart();

        //special State must not happen
        if(empty($cart)) {
            return $this;
        }

        $cart->update([
            'coupon_code' => null,
            'coupon_id'   => null,
        ]);

        return $this;
    }

    public function getAvailablePaymentMethods() {
        /**
         * @var SallaCart $cart
         */
        $cart          = $this->getCart();
        $methods       = collect(['methods' => ['credit_card'], 'default' => 'credit_card']);
        $isBankAllowed = store()->getSetting('sallaStore::is-bank-transform-enabled', true) &&
            SallaCartSource::checkBankPaymentMethod($cart->source);

        if ($isBankAllowed) {
            $methods->put('methods', ['credit_card', 'bank']);
        }

        if (store()->getSetting('features::apple-pay.merchants.enabled', false)) {
            $availableMethods = ['apple_pay', 'credit_card'];

            if ($isBankAllowed) {
                $availableMethods[] = 'bank';
            }

            $methods->put('methods', $availableMethods);
            $methods->put('default', 'apple_pay');
        }

        return $methods;
    }

    /**
     *
     */
    public function resetCartParameters()
    {
        /**
         * @var SallaCart $cart
         */
        $cart = $this->getCart();

        //special State must not happen
        if(empty($cart)) {
            return $this;
        }

        $cart->update([
            'parameters' => [],
        ]);

        return $this;
    }

    public function isInstallmentPayment()
    {
        return $this->getCart()->isInstallmentCart() || ($this->getCart()->isInstallmentPayment() && $this->isInstallmentAllowed());
    }

    public function hasUncompletedInstallment()
    {
        if (!store()->storeDocument) {
            return 0;
        }

        $this->uncompletedStores = $this->uncompletedStores ? $this->uncompletedStores
            : StoreDocument::withoutGlobalScope(CurrentStoreScope::class)
                ->where('owner_identity_number', store()->storeDocument->owner_identity_number)
                ->where('store_id', '!=', store()->getId())
                ->whereHas('store', function ($query) {
                    $query->withoutGlobalScope(CurrentStoreScope::class)
                        ->whereHas('installments', function ($query) {
                            $query->withoutGlobalScope(CurrentStoreScope::class)
                                ->where('status', SallaInstallmentStatus::ACTIVE)
                                ->whereHas('notPaidPayments', function ($query) {
                                    $query->withoutGlobalScope(CurrentStoreScope::class);
                                });
                        });
                })->count();

        return $this->uncompletedStores;
    }

    public function canShowInstallment(): bool
    {
        // this is a temporary condition until we test installment with churned users then we will open it for others
        /*$hasOldSubscription = store()->subscriptions()->where('type', SallaProductType::PLAN)
                ->where('status', SallaSubscriptionStatus::EXPIRED)->count() > 0;*/

        // allow only stores with basic plans is temporary until we release feature for allowing upgrade plans
        $isNotPaid = (!store()->isPaid() && store()->installments()->whereHas('latePayments')->count() == 0);

        return $isNotPaid && $this->getItems()->filter(function (SallaCartItem $item) {
                return $item->product->type == SallaProductType::PLAN &&
                       $item->product->type_value != SallaProductPlanType::SPECIAL &&
                       !$item->product->planBundledProductItems()->exists() &&
                       in_array((int)$item->getPeriod(), [12, 24], true);
            })->count() === 1;
    }

    public function isInstallmentAllowed(): bool
    {
        return $this->canShowInstallment()
            && store()->getStoreDocumentStatus() == StoreDocumentStatus::COMPLETED
            && store()->isDocumentsVerified()
            && !$this->hasUncompletedInstallment();
    }

    public function checkPayment($payment)
    {
        if(! $payment){
            //for any reason the payment fail , so if occurred then return without complete creating order
            return \ajax()->error( __t('marketplace::cart.messages.error.unexpected error'));
        }

        if ($payment->hasRedirectUrl()) {
            return ajax()
                ->when(fromSallaApp('3.0.0'), function ($ajax) use ($payment) {
                    return $ajax->runJavascript('$("#modal_salla_cart").modal("hide"); Salla.event.dispatchMobileEvent("payment-redirect", {' . $payment->getRedirectUrl() . '});');
                })
                ->when(!fromSallaApp('3.0.0'), function ($ajax) use ($payment) {
                    /** @var \Salla\Core\Services\Ajax $ajax */
                    return $ajax->keepModal()->redirect($payment->getRedirectUrl());
                });
        }

        if ($payment->isPending()) {
            return ajax()->setJson(['status' => $payment->getStatus()])->setData($payment->getPayload());
        }

        if ($payment->isFailed()) {
            return $this->prepareErrorMessage($payment->getMessage());
        }

        return null;
    }

    private function prepareErrorMessage($errorMessage)
    {
        $message = ($this->transformerMessage($errorMessage)
            ?: __t('marketplace::messages.error.unexpected error'));

        //Move domain related error
        if (optional($this->getItems()[0]->product)->action_method === "move_domain") {
            $message = Str::contains($errorMessage, "Purchase Price Does Not Match") ? __('domain::api_v2.error.domain_transfer_price_is_not_reasonable') : $errorMessage;
        }

        return ajax()->error($message);
    }

    private function filterAndSortCoupons($coupons, $cartSubTotal)
    {
        $couponFilter = new ListSallaCouponFilterService($this, true);
        return $coupons
            ->filter(function ($coupon) use ($couponFilter) {
                return $couponFilter->isValid($coupon, store()->isPaid());
            })
            ->sortByDesc(function ($coupon) use ($cartSubTotal) {
                
                if(!$cartSubTotal) {
                    return $coupon->amount;
                } 

                if ($coupon->type == 'P') {
                    return ($coupon->amount * ($cartSubTotal / 100));
                }
                return $coupon->amount;
            })
            ->take(3) // <-- Only take 3 coupons as a suggestion.
            ->map(function ($coupon) {
                return (new ListSallaCouponsTransformer())->transform($coupon);
            })
            ->values()->all();
    }
}
