<?php

namespace Modules\MarketPlace\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Notification;
use Modules\MarketPlace\Entities\SallaSubscriptionReminder;
use Modules\MarketPlace\Entities\SallaSubscriptions;
use Modules\MarketPlace\Enum\SallaSubscriptionRenew;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Notifications\SubscriptionReminder\SubscriptionReminderNotification;

class SubscriptionReminderService
{
    /**
     * Create a subscription reminder.
     *
     * @param SallaSubscriptions $subscription
     * @return void
     */
    public function create(SallaSubscriptions $subscription): void
    {
        SallaSubscriptionReminder::query()->create([
            'subscription_id' => $subscription->id,
            'status'          => SallaSubscriptionReminder::ACTIVE,
            'due_date'        => $subscription->end_date,
        ]);
    }

    /**
     * Get expired subscription reminders query.
     *
     * @return Builder
     */
    public function getExpiredSubscriptionsQuery(): Builder
    {
        return SallaSubscriptionReminder::query()
            ->with([
                'subscription' => [
                    'store:id,username,name' => [
                        'owner:id,model_has_roles.store_id,email'
                    ]
                ]
            ])
            ->whereHas('subscription', function ($query) {
                $query->where([
                    'renew' => SallaSubscriptionRenew::MANUALLY,
                    'status' => SallaSubscriptionStatus::ACTIVE,
                ]);
            })
            ->where('due_date', '<=', now()->format('Y-m-d'))
            ->where('status', '=', SallaSubscriptionReminder::ACTIVE);
    }

    /**
     * Create a subscription reminder.
     *
     * @param SallaSubscriptionReminder $subscriptionReminder
     * @return void
     */
    public function deleteSubscriptionReminder(SallaSubscriptionReminder $subscriptionReminder): void
    {
        $subscriptionReminder->delete();
    }

    /**
     * Create a subscription reminder.
     *
     * @param SallaSubscriptionReminder $subscriptionReminder
     * @return void
     */
    public function notifySubscriptionReminder(SallaSubscriptionReminder $subscriptionReminder): void
    {
        $email = optional(optional($subscriptionReminder->subscription->store)->owner)->email;

        if ($email) {
            Notification::route('mail', $email)
                ->notify(new SubscriptionReminderNotification($subscriptionReminder->subscription, false));
        }
    }
}
