<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" backupStaticAttributes="false" bootstrap="vendor/autoload.php" colors="true" convertErrorsToExceptions="true" convertNoticesToExceptions="true" convertWarningsToExceptions="true" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
  <testsuites>
    <testsuite name="marketplace-module-dashboard">
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Partner</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Feature</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Unit</directory>
    </testsuite>
  </testsuites>
  <coverage processUncoveredFiles="false">
    <include>
      <!-- specify which files are being included in the code coverage report here in <directory> -->
      <directory suffix=".php">./vendor/salla/marketplace-module</directory>
    </include>
    <exclude>
      <directory suffix=".php">./vendor/salla/marketplace-module/Tests</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Resources</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Database</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Routes</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Providers</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Http/Controllers/Nasa</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Http/Requests/Nasa</directory>
      <directory suffix=".php">./vendor/salla/marketplace-module/Actions/Nasa</directory>
    </exclude>
  </coverage>
  <php>
    <ini name="memory_limit" value="-1" />
    <server name="APP_ENV" value="testing"/>
    <server name="BCRYPT_ROUNDS" value="4"/>
    <server name="CACHE_DRIVER" value="array"/>
    <server name="MAIL_MAILER" value="array"/>
    <server name="QUEUE_CONNECTION" value="sync"/>
    <server name="SESSION_DRIVER" value="array"/>
    <server name="TELESCOPE_ENABLED" value="false"/>
  </php>
</phpunit>
