<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd" backupGlobals="false" bootstrap="vendor/autoload.php" colors="true" processIsolation="false" stopOnFailure="false">
  <testsuites>
    <!-- specify which tests to be tested in <testsuites> -->
    <testsuite name="Feature">
      <directory suffix="Test.php">./tests/Feature</directory>
    </testsuite>
    <testsuite name="Unit">
      <directory suffix="Test.php">./tests/Unit</directory>
    </testsuite>
    <testsuite name="Services">
      <directory suffix="Test.php">./tests/Services</directory>
    </testsuite>

    <testsuite name="checkout-team">
      <directory suffix="Test.php">./vendor/salla/checkout/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/payment-module/Tests/Dashboard</directory>
    </testsuite>

<!--    <testsuite name="order-team">-->
<!--      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard</directory>-->
<!--      &lt;!&ndash; <directory suffix="Test.php">./vendor/salla/product-module/Tests/Dashboard</directory> &ndash;&gt;-->
<!--      &lt;!&ndash; <directory suffix="Test.php">./vendor/salla/store-branch-module/Tests/Dashboard</directory> &ndash;&gt;-->
<!--    </testsuite>-->

    <!-- Start Order Team Individual Suites For Each Subfolder -->
    <testsuite name="order-general-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Actions</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Bulks</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Unit</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Widgets</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Rules</directory>
    </testsuite>

    <testsuite name="order-console-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Channel</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Composers</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Console</directory>
    </testsuite>

    <testsuite name="order-controllers-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Controllers</directory>
    </testsuite>

    <testsuite name="order-requests-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Requests</directory>
    </testsuite>

    <testsuite name="order-middlewares-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Middlewares</directory>
    </testsuite>

    <testsuite name="order-entities-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Entities</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Elasticsearch</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Emails</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Repositories</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Traits</directory>
    </testsuite>

    <testsuite name="order-helpers-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Enum</directory>
<!--      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Excel</directory>-->
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Features</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Filters</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/StoreSettings</directory>
    </testsuite>

    <testsuite name="order-jobs-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Jobs</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Listeners</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Presenter</directory>
    </testsuite>

    <testsuite name="order-services-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Services</directory>
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Trackers</directory>
    </testsuite>

    <testsuite name="order-transformers-team">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard/Transformers</directory>
    </testsuite>

    <!-- End Order Team Individual Suites For Each Subfolder -->

    <testsuite name="product-team">
      <directory suffix="Test.php">./vendor/salla/product-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/store-branch-module/Tests/Dashboard</directory>
    </testsuite>

    <testsuite name="theme-team">
      <directory suffix="Test.php">./vendor/salla/customer/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/theme-customization-module/Tests/Dashboard</directory>
    </testsuite>

    <testsuite name="marketing-team">
      <directory suffix="Test.php">./vendor/salla/marketing/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/marketing-integration/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/loyalty-system-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/mailchimp/src/Tests/Dashboard</directory>
    </testsuite>

    <!-- Start Shipping Team Tests -->

    <!-- <testsuite name="shipping-team">-->
    <!-- <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard</directory>-->
    <!-- </testsuite>-->

    <testsuite name="shipping-general-team">
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Actions</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Base</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Components</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Observers</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Web</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Widgets</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Entities</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Model</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Repositories</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Webhook</directory>
    </testsuite>

    <testsuite name="shipping-controllers-team">
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Http/Controllers/Dashboard</directory>
    </testsuite>

    <testsuite name="shipping-jobs-team">
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Jobs</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Console</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Listeners</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Notifications</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Excel</directory>
    </testsuite>

    <testsuite name="shipping-utilities-team">
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Services</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Calculators</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Transformers</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Presenters</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Trackers</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Helper</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Enums</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Features</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Filters</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Rules</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Traits</directory>
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard/Requests</directory>
    </testsuite>

    <!-- End Shipping Team Tests -->

    <testsuite name="api-team">
      <directory suffix="Test.php">./vendor/salla/dashboard-api/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Partner</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Feature</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/external-services-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/store-resthook/Tests/Dashboard</directory>
    </testsuite>

    <testsuite name="journey-team">
      <directory suffix="Test.php">./tests/Feature</directory>
      <directory suffix="Test.php">./Modules/*/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/advertisements-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/dashboard-settings-module/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/domain-module/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/laravel-otp/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/changelog-module/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/help-center-module/Tests</directory>
      <directory suffix="Test.php">./vendor/salla/store-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/payment-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/users/Tests/Unit</directory>
      <directory suffix="Test.php">./vendor/salla/users/Tests/Http</directory>
    </testsuite>

    <testsuite name="Modules">
      <directory suffix="Test.php">./Modules/*/Tests</directory>
    </testsuite>
    <testsuite name="checkout">
      <directory suffix="Test.php">./vendor/salla/checkout/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="customer">
      <directory suffix="Test.php">./vendor/salla/customer/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="payment-module">
      <directory suffix="Test.php">./vendor/salla/payment-module/Tests/Dashboard</directory>
      <directory suffix="Test.php">./vendor/salla/store-wallet/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="product-module">
      <directory suffix="Test.php">./vendor/salla/product-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="orders-module">
      <directory suffix="Test.php">./vendor/salla/orders-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="settings-manager">
      <directory suffix="Test.php">./vendor/salla/settings-manager/tests/Dashboard</directory>
    </testsuite>
    <testsuite name="orders-module">
      <directory suffix="Test.php">./vendor/salla/impex/Tests</directory>
    </testsuite>
       <testsuite name="orders-module">
      <directory suffix="Test.php">./vendor/salla/impex/Tests</directory>
    </testsuite>
    <testsuite name="mahlydash">
      <directory suffix="Test.php">./vendor/salla/mahlydash/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="store-branch-module">
      <directory suffix="Test.php">./vendor/salla/store-branch-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="advertisements-module">
      <directory suffix="Test.php">./vendor/salla/advertisements-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="special-offer-module">
      <directory suffix="Test.php">./vendor/salla/special-offer-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="dashboard-api-module">
      <directory suffix="Test.php">./vendor/salla/dashboard-api/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="marketplace-module">
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Partner</directory>
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Feature</directory>
    </testsuite>
    <testsuite name="shipping-module">
      <directory suffix="Test.php">./vendor/salla/shipping/tests/Dashboard</directory>
    </testsuite>
    <testsuite name="marketing-module">
      <directory suffix="Test.php">./vendor/salla/marketing/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="dashboard-settings-module">
      <directory suffix="Test.php">./vendor/salla/dashboard-settings-module/Tests</directory>
    </testsuite>
    <testsuite name="marketplace-module">
      <directory suffix="Test.php">./vendor/salla/marketplace-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="theme-customization-module">
      <directory suffix="Test.php">./vendor/salla/theme-customization-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="domain-module">
      <directory suffix="Test.php">./vendor/salla/domain-module/Tests</directory>
    </testsuite>
    <testsuite name="store-app-module">
      <directory suffix="Test.php">./vendor/salla/store-app-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="store-module">
      <directory suffix="Test.php">./vendor/salla/store-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="core">
      <directory suffix="Test.php">./vendor/salla/core/src/Tests</directory>
    </testsuite>
    <testsuite name="languages-module">
      <directory suffix="Test.php">./vendor/salla/languages-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="complaint">
      <directory suffix="Test.php">./vendor/salla/complaint/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="audit-log">
      <directory suffix="Test.php">./vendor/salla/audit-log/tests</directory>
    </testsuite>
    <testsuite name="violation-module">
      <directory suffix="Test.php">./vendor/salla/violation-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="marketing-integration-module">
      <directory suffix="Test.php">./vendor/salla/marketing-integration/Tests</directory>
    </testsuite>
    <testsuite name="external-services-module">
      <directory suffix="Test.php">./vendor/salla/external-services-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="expert-module">
      <directory suffix="Test.php">./vendor/salla/expert-module/Tests/Unit</directory>
    </testsuite>
    <testsuite name="communication-module">
      <directory suffix="Test.php">./vendor/salla/communication/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="announcement-module">
      <directory suffix="Test.php">./vendor/salla/announcement-module/Tests/Dashboard</directory>
    </testsuite>
    <testsuite name="users-module">
      <directory suffix="Test.php">./vendor/salla/users/Tests/Unit</directory>
      <directory suffix="Test.php">./vendor/salla/users/Tests/Http</directory>
    </testsuite>
    <testsuite name="rutterapi-module">
      <directory suffix="Test.php">./vendor/salla/rutterapi/Tests/Dashboard</directory>
    </testsuite>
  </testsuites>
  <extensions>
    <bootstrap class="Ergebnis\PHPUnit\SlowTestDetector\Extension"/>
  </extensions>
  <!-- <listeners>
    <listener class="JohnKary\PHPUnit\Listener\SpeedTrapListener" />
  </listeners> -->
  <!-- <coverage processUncoveredFiles="true">
    <include>
      specify which files are being included in the code coverage report here in <directory>
      <directory suffix=".php">./app</directory>
      <directory suffix=".php">./Modules</directory>
      <directory suffix=".php">./vendor/salla/checkout</directory>
      <directory suffix=".php">./vendor/salla/customer</directory>
      <directory suffix=".php">./vendor/salla/product-module</directory>
      <directory suffix=".php">./vendor/salla/store-branch-module</directory>
      <directory suffix=".php">./vendor/salla/orders-module</directory>
      <directory suffix=".php">./vendor/salla/advertisements-module</directory>
      <directory suffix=".php">./vendor/salla/special-offer-module</directory>
      <directory suffix=".php">./vendor/salla/dashboard-api</directory>
      <directory suffix=".php">./vendor/salla/domain-module</directory>
    </include>
  </coverage> -->
  <php>
    <ini name="memory_limit" value="-1" />
    <server name="APP_ENV" value="testing"/>
    <server name="BCRYPT_ROUNDS" value="4"/>
    <server name="CACHE_DRIVER" value="array"/>
    <server name="MAIL_MAILER" value="array"/>
    <server name="QUEUE_CONNECTION" value="sync"/>
    <server name="SESSION_DRIVER" value="array"/>
    <server name="TELESCOPE_ENABLED" value="false"/>
  </php>
</phpunit>
