APP_ENV=local
APP_KEY=base64:Kd+sit6vWbH228WJsOuqE7Da1dmi1EnZMLLlhRa+Dc8=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_DOMAIN=dashboard.test
APP_URL=https://dashboard.test
APP_STORES_DOMAIN=https://store.test
DEBUGBAR_ENABLED=true
SALLA_SHORT_URL_REFERRAL=https://mtjr.at
COUPONE_DOMAIN=https://mtjr.at
SALLA_SHORT_URL=https://mtjr.at
APP_HELP_CENTER_DOMAIN=dashboard-help.test
SENTRY_DNS=https://2ea836e5be564f7cb44d0ace1111c93d:<EMAIL>/2
HASHED_SALLA_CONNECTION=ase64:DxhusGofiWO31BN9OHbJmEhh1LTg9MNoSzjjWuWtcjI=
DB_HOST=localhost
DB_DATABASE=salla
DB_USERNAME=root
DB_PASSWORD=root
DB_PORT=3306

CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_CONNECTION=session
QUEUE_DRIVER=redis
SESSION_DOMAIN=dashboard.test

REDIS_HOST=localhost
REDIS_PASSWORD=null
REDIS_PORT=6379

S3_KEY="********************"
S3_SECRET="O74yk/VMB1ph0T1Go04GM4Mk9w/dWRLDcY53begL"
S3_BUCKET=salla-dev
S3_URL=https://s3-eu-central-1.amazonaws.com/salla-dev/

SESSION_SECURE_COOKIE=true

#MAIL_DRIVER=smtp
#MAIL_HOST=mailtrap.io
#MAIL_PORT=2525
#MAIL_USERNAME=null
#MAIL_PASSWORD=null
#MAIL_ENCRYPTION=null
# MAIL_FROM_ADDRESS=null
# MAIL_FROM_NAME="${APP_NAME}"

PUSHER_APP_ID=
PUSHER_KEY=
PUSHER_SECRET=

ONESIGNAL_APP_ID=
ONESIGNAL_REST_API_KEY=

NAMECOM_TYPE=
NAMECOM_USERNAME=
NAMECOM_TOKEN=
DIGITAL_OCEAN_IP=
DIGITAL_OCEAN_TOKEN=
JSONWHOIS_TOKEN=


SERVER_IP=
CLOUDFLARE_EMAIL=
CLOUDFLARE_TOKEN=
CLOUDFLARE_API_TOKEN=
CLOUDFLARE_ZONE_ID=
CLOUDFLARE_SALLA_ZONE_ID=

FORGE_ID=
FORGE_TOKEN=
FORGE_NGINX_ROOT=/home/<USER>/salla.sa/public

WHOISXMLAPI_TOKEN=at_sIT4kzzq57BULjzvQ7zNeP2GIijUZ

FAILED_JOB_SLACK_WEBHOOK_URL=*****************************************************************************

FREE_MONTHLY_SMS=14
FREE_ANNUALLY_SMS=4

BAREMETRICS_LIVE_KEY=
BAREMETRICS_ENV=sandbox
BAREMETRICS_SANDBOX_KEY=*************************
BAREMETRICS_SOURCE_ID=82ad0b44-5df4-47b3-9768-f63b176e52b0

GOOGLETAG_ACCOUNT=GTM-N27W52C
MIXPANEL_TOKEN=

APP_COMPLAINT_DOMAIN=complaint.dashboard.test


PARTNER_CLOUDFLARE_EMAIL=<EMAIL>
PARTNER_CLOUDFLARE_TOKEN=
PARTNER_ZONE_ID=

GOOGLE_TRANSLATE_API_KEY=AIzaSyC_l5mHolHPuzfjoKEJlt_vYFnNGD_R5Vs
GOOGLE_MAPS_API_KEY=

GOOGLE_APPLICATION_CREDENTIALS=google-client-credentials.json

# sandbox keys
SALES_MACHINE_API_TOKEN=7FarSLM4YrtqcHQLWwr1Og
SALES_MACHINE_API_SECRET=ORUADLINHBV74e125PGfDw

SCOUT_QUEUE=false
SCOUT_DRIVER=elasticsearch

DB_AUDIT_HOST=127.0.0.1
DB_AUDIT_PORT=3306
DB_AUDIT_DATABASE=salla_audit
DB_AUDIT_USERNAME=root
DB_AUDIT_PASSWORD=root

CLOCKWORK_ENABLE=true
GITHUB_TOKEN=
LOCAL_PACKAGES_PATH=

SIFT_ACCOUNT_ID=
SIFT_API_KEY=
SIFT_BEACON_KEY=

MAILCHIMP_CLIENT_ID=
MAILCHIMP_CLIENT_SECRET=
MAILCHIMP_CALLBACK=

PLANHAT_API_TOKEN=
PLANHAT_TENANT_UUID=

AHA_IDEAS_SECRET=
AHA_IDEAS_TOKEN=

CACHE_FALLBACK_DRIVER=redis

# websocket service
CENTRIFUGO_SECRET=46b38493-147e-4e3f-86e0-dc5ec54f5133
CENTRIFUGO_APIKEY=aaaf202f-b5f8-4b34-bf88-f6c03a1ecda6
CENTRIFUGO_URL=https://ws.salla.group

# AIRDROP_NPM_ENABLED=
AIRDROP_REMOTE_DIR=
S3_AIRDROP_KEY=
S3_AIRDROP_SECRET=
S3_AIRDROP_ENDPOINT=
S3_AIRDROP_REGION=

APPSTORE_PASSWORD=
# swoole
#APP_API_DOMAIN=127.0.0.1
# we need it to use http instead of https
#APP_API_URL=http://127.0.0.1:5200
# it's very important locally to active it, to debug leak data in two stores
#LARAVELS_WORKER_NUM=1
# have no Idea we we need it, but in live it's true
#LARAVELS_HANDLE_STATIC=true
# if you have Linux enjoy😭
#LARAVELS_INOTIFY_RELOAD=true

AWS_DYNAMODB_ACCESS_KEY_ID=
AWS_DYNAMODB_ACCESS_SECRET_ACCESS_KEY=

MOKAFAA_ALRAJHI_CLIENT_ID=c58903cfb54795d0c81ad65dc73aad5b
MOKAFAA_ALRAJHI_CLIENT_SECRET=c71d08c5017d8a77c91beef6974269aa
