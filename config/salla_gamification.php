<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Salla Gamification API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration settings for the Salla Gamification API.
    | You can publish this file using the following command:
    |
    | php artisan vendor:publish --tag=salla-gamification-config
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API Key
    |--------------------------------------------------------------------------
    |
    | Your Salla Gamification API key. This should be kept secure and never
    | committed to version control.
    |
    */
    'api_key' => env('SALLA_GAMIFICATION_API_KEY', 'DgC03TSiUtQyFc2DJydQOXXKVWlYBN8Z'),

    /*
    |--------------------------------------------------------------------------
    | API Secret
    |--------------------------------------------------------------------------
    |
    | Your Salla Gamification API secret. This should be kept secure and never
    | committed to version control.
    |
    */
    'api_secret' => env('SALLA_GAMIFICATION_API_SECRET', 'WoPDFdnRabtjeojjtUN3V6lxlrxgaQMI'),

    /*
    |--------------------------------------------------------------------------
    | API Base URL
    |--------------------------------------------------------------------------
    |
    | The base URL for the Salla Gamification API.
    |
    */
    'base_url' => env('SALLA_GAMIFICATION_BASE_URL', 'https://salla-gamification.a-alathamneh.workers.dev'),

    /*
    |--------------------------------------------------------------------------
    | API Version
    |--------------------------------------------------------------------------
    |
    | The version of the Salla Gamification API to use.
    |
    */
    'version' => env('SALLA_GAMIFICATION_VERSION', 'v1'),

    /*
    |--------------------------------------------------------------------------
    | Request Timeout
    |--------------------------------------------------------------------------
    |
    | The timeout in seconds for API requests.
    |
    */
    'timeout' => env('SALLA_GAMIFICATION_TIMEOUT', 30),

    /*
    |--------------------------------------------------------------------------
    | Retry Attempts
    |--------------------------------------------------------------------------
    |
    | The number of times to retry failed requests.
    |
    */
    'retries' => env('SALLA_GAMIFICATION_RETRIES', 3),

    /*
    |--------------------------------------------------------------------------
    | Token Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for JWT token generation and caching.
    |
    */
    'token' => [
        /*
        |--------------------------------------------------------------------------
        | Token Expiry
        |--------------------------------------------------------------------------
        |
        | The default expiry time in minutes for generated tokens.
        |
        */
        'expiry_minutes' => env('SALLA_GAMIFICATION_TOKEN_EXPIRY', 60),

        /*
        |--------------------------------------------------------------------------
        | Cache Configuration
        |--------------------------------------------------------------------------
        |
        | Configuration for token caching.
        |
        */
        'cache' => [
            /*
            |--------------------------------------------------------------------------
            | Cache Key Prefix
            |--------------------------------------------------------------------------
            |
            | The prefix used for token cache keys.
            |
            */
            'key_prefix' => 'salla_gamification_token_',

            /*
            |--------------------------------------------------------------------------
            | Cache TTL Buffer
            |--------------------------------------------------------------------------
            |
            | The time in seconds to subtract from token expiry for cache TTL.
            | This ensures tokens are refreshed before they expire.
            |
            */
            'ttl_buffer' => env('SALLA_GAMIFICATION_TOKEN_CACHE_BUFFER', 60),
        ],
    ],
];
