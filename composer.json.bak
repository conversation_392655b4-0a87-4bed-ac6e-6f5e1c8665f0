{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.3", "salla/security-core": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 4.2.5", "salla/searchable": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 0.1.0", "tymon/jwt-auth": "^2.0", "laravel/framework": "^10.0", "salla/pclzip": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 2.8.3", "socialiteproviders/instagram": "^4.0", "torann/geoip": "^3.0.5", "anam/phantomjs-linux-x86-binary": "^2.1", "predis/predis": "^2.0", "laravel/passport": "^11.0", "berkayk/onesignal-laravel": "^1.0.6", "barryvdh/laravel-snappy": "^1.0.0", "phpoffice/phpspreadsheet": "1.18.0", "giggsey/libphonenumber-for-php": "^8.9", "salla/core": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.4", "salla/shipping": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.6", "salla/shipping-services": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/announcement-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/domain-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/paymetns": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "spatie/laravel-http-logger": "^1.10", "salla/mailchimp": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "laravel-validation-rules/credit-card": "^1.5.0", "salla/laravel-otp": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 0.1.0", "salla/component": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/error": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/impex": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/special-offer-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/orders-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.2", "salla/changelog-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/marketing": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/loyalty-system-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/report-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "globalcitizen/php-iban": "^2.6", "salla/external-services-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 2.0", "salla/store-resthook": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 2.0", "salla/dashboard-tracking": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 2.0", "salla/dashboard-settings-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/stores-cart": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/store-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/store-menu-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/instagram-api": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "laravel/horizon": "^5.0", "salla/payment-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/aws-domain-verify": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "morrislaptop/laravel-queue-clear": "~1.0", "salla/dashboard-api": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/advertisements-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/theme-customization-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "kodeine/laravel-meta": "^2.2.1", "salla/complaint": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/store-app-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.2", "salla/customer": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.1", "milon/barcode": "^10.0", "salla/breadcrumbs": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "beyondcode/laravel-mailbox": "^3.1", "salla/store-branch-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/sitemap": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/google-tags": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 0.1.3", "salla/money": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/product-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.1", "salla/checkout": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/onesignal-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/laravel-geo": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 2.0.3", "salla/laravel-schedule-monitor": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "vitalybaev/google-merchant-feed": "^2.0", "symfony/css-selector": "^6.0", "spatie/laravel-web-tinker": "^1.8.4", "salla/stc": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/violation-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/marketplace-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/omnipay-stc": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/store-wallet": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "spatie/laravel-image-optimizer": "^1.7.1", "salla/graphql-client": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "mailjet/laravel-mailjet": "^3.0.4", "lcobucci/jwt": "^4.0", "bepsvpt/secure-headers": "^7.4", "biscolab/laravel-recaptcha": "^6.0", "hhxsv5/laravel-s": "~3.7.0", "laravel/ui": "^4.4", "laravel/legacy-factories": "^1.4", "salla/rutterapi": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/help-center-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "rap2hpoutre/fast-excel": "^5.3", "salla/planhat-api": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/languages-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.1", "salla/blog-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "symfony/translation": "^5.3.10", "madorin/matex": "^1.0", "spatie/laravel-webhook-server": "^3.8", "spatie/laravel-webhook-client": "^3.2", "salla/aha-ideas": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/form-builder-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "mavinoo/laravel-batch": "2.3.6", "salla/mahlydash": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.9", "salla/hydra-client": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/qoyod-service": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/accounting-services": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/daftra-service": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/laravel-s3-browser-based-uploads": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/omnipay-bank-transfer": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/zoho-books": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0.0", "salla/expert-module": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/encryption": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 0.0.32", "imdhemy/laravel-purchases": "^1.9.1", "salla/dal-shipping": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "alkhwlani/xss-middleware": "^4.0", "salla/omnipay-tap": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/careem-shipping": "~1.0.1", "salla/laravel-duplicate": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/ups-shipping": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "salla/migrations": "dev-feature/PLAT-630-upgrade-to-laravel-ten as 1.0", "zschuessler/laravel-route-to-class": "^2.0", "async-aws/illuminate-cache": "dev-master"}, "repositories": [{"type": "composer", "url": "https://core-repo-package.salla.group"}, {"type": "vcs", "url": "https://github.com/mostafaaminflakes/laravel-auto-presenter", "no-api": true}, {"type": "vcs", "url": "https://github.com/mostafaaminflakes/laravel-helper-functions", "no-api": true}, {"type": "vcs", "url": "https://github.com/mostafaaminflakes/rulerz", "no-api": true}, {"type": "vcs", "url": "https://github.com/mostafaaminflakes/illuminate-cache", "no-api": true}], "require-dev": {"filp/whoops": "^2.0", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "friendsofphp/php-cs-fixer": "^3.0", "ergebnis/phpunit-slow-test-detector": "^2.0", "beyondcode/laravel-dump-server": "^1.0", "orchestra/testbench": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "salla/devmate": "^1.0", "hammerstone/airdrop": "^0.2.3", "timacdonald/log-fake": "^2.1"}, "autoload": {"files": ["app/Functions/helpers.php"], "psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/", "Salla\\": "vendor/salla/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Salla\\LaravelOtp\\Tests\\": "vendor/salla/laravel-otp/tests/"}}, "scripts": {"salla:assets-publish": ["@composer salla-assets-build", "npm run prod"], "salla:clear-cache": ["rm -rf bootstrap/cache/routes/* && rm -rf bootstrap/cache/*.php && rm -rf storage/framework/cache/* && mkdir -p storage/framework/cache/data/ && touch storage/framework/cache/data/.gitignore"], "salla:update": ["@composer salla:clear-cache", "@composer update", "@php artisan migrate", "@php artisan clear-compiled && php artisan cache:clear && php artisan config:clear && php artisan route:clear && php artisan view:clear"], "php-cs:issues": "vendor/bin/php-cs-fixer fix --diff --dry-run", "php-cs:fix": "vendor/bin/php-cs-fixer fix", "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "@composer salla-assets-build"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@composer salla-assets-build"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "salla-assets-build": ["php artisan salla:publish-assets --provider=\"dashboard\"", "php artisan module:publish orders", "php artisan module:publish expert"]}, "config": {"preferred-install": "dist", "allow-plugins": {"ocramius/package-versions": false, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"laravel": {"dont-discover": ["swayok/alternative-laravel-cache", "monospice/laravel-redis-sentinel-drivers", "nwidart/laravel-modules"]}}}