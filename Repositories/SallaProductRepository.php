<?php

namespace Modules\MarketPlace\Repositories;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\MarketPlace\Entities\SallaProducts;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductFeedbacksEnum;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Features\AIProductFeature;
use Modules\Store\Enum\SallaProductType;
use Prettus\Repository\Eloquent\BaseRepository;
use Salla\Core\Enum\Plan;
use Illuminate\Support\Facades\DB;

class SallaProductRepository extends BaseRepository
{
    /**
     * Specify Model class name.
     *
     * @return string
     */
    public function model()
    {
        return SallaProducts::class;
    }

    /**
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection|mixed
     */
    public function getSupportProducts()
    {
        return $this->findWhere([
            'type' => SallaProductType::PLAN,
        ]);
    }

    public function getSimilarProductsOfProduct(array $tagsId = [], $type = null)
    {
        return SallaProducts::query()
            ->when(!empty($tagsId), function ($query) use ($tagsId) {
                $query->whereIn('id', function ($subQuery) use ($tagsId) {
                    $subQuery->select('salla_product_tag.product_id')
                        ->from('salla_product_tag')
                        ->whereIn('salla_product_tag.tag_id', $tagsId);
                });
            }, function ($query) use ($type) {
                $query->when(!empty($type), function ($query) use ($type) {
                    $query->where('type', $type);
                });
            })
            ->where('type_value', '!=', SallaProductAddonType::APPS)
            ->where('hide', false)
            ->forPartner()
            ->limit(4)
            ->inRandomOrder()
            ->get();
    }

    public function getProductsByTypes($types)
    {
        return SallaProducts::query()->whereIn('type', Arr::wrap($types))->get();
    }

    public function getThemeProducts($parameters = [], $as_count = false)
    {
        $limit       = $parameters['limit'] ?? null;
        $sort_by     = $parameters['sort_by'] ?? 'latest';
        $tags        = Arr::wrap($parameters['tags'] ?? []);
        $keyword     = $parameters['keyword'] ?? null;
        $min_price   = $parameters['min_price'] ?? null;
        $max_price   = $parameters['max_price'] ?? null;
        $per_page    = $parameters['per_page'] ?? null;
        $partners    = $parameters['partners'] ?? null;
        $recommended = $parameters['recommended'] ?? null;
        $except      = $parameters['except'] ?? [];
        $selected    = $parameters['selected'] ?? [];
        $rating      = $parameters['rating'] ?? null;
        $tag         = $parameters['tag'] ?? null;
        $free        = $parameters['free'] ?? null;
        $salla       = $parameters['salla'] ?? null;

        //=> when salla taxes applied, the products prices is changed, so will add the taxes to the prices that filtering by, to get right results
        $taxes = 1.15;
        if ($min_price > 0 && $taxes) {
            $min_price = $min_price / $taxes;
        }
        if ($max_price > 0 && $taxes) {
            $max_price = $max_price / $taxes;
        }
        if ($selected) {
            $per_page = count($selected);
        }

        $themes = SallaProducts::query()
            ->selectRaw("salla_products.*,themes.id as theme_id, IFNULL(salla_products.created_at, themes.created_at) as published_at, JSON_EXTRACT(change_log, CONCAT('$[', JSON_LENGTH(change_log) - 1, '].date')) as 'updated_date'")
            ->havingNotNull('published_at')
            ->where('salla_products.type', SallaProductType::THEME)
            ->leftJoin('themes', 'salla_products.type_value','=','themes.id')
            ->where('salla_products.hide', false)
            ->when($keyword, fn($query) => $query->selectRaw("CASE
                    WHEN salla_products.name = ? THEN 200
                    WHEN salla_products.name LIKE ? THEN 190
                    WHEN salla_products.name LIKE ? THEN 180
                    ELSE 0
                END AS 'rank'", [
                    $keyword,
                    "{$keyword}%",
                    "%{$keyword}%",
                ])->having('rank', '>', 0)->orderByDesc('rank')
            )->when($tags, fn ($query) =>
                $query->whereHas('theme.previewDemoStores', fn($subQuery) => $subQuery->whereIn('category_id', $tags))
            )->when($tag, fn ($query) =>
                $query->whereHas('theme.previewDemoStores', fn($subQuery) => $subQuery->where('category_id', $tag))
            )
            ->when($min_price !== null || $max_price !== null, function ($query) use ($min_price, $max_price) {
                return $query->whereHas('productPrices', fn($subQuery) =>
                    $subQuery->where(function ($subSubQuery) use ($min_price, $max_price) {
                        $subSubQuery->whereNull('salla_products_pricing.sale_price')
                            ->whereBetween('salla_products_pricing.price', [$min_price, $max_price]);
                    })->orWhere(function($subSubQuery) use ($min_price, $max_price) {
                        return $subSubQuery->whereNotNull('salla_products_pricing.sale_price')
                            ->where(
                                DB::raw("DATE(IFNULL(salla_products_pricing.sale_end, '". now()->addDay()->toDateString()."'))"),
                                '>',
                                DB::raw("DATE('" . now()->toDateString() . "')")
                            )
                            ->whereBetween('salla_products_pricing.sale_price', [$min_price, $max_price]);
                    })
                );
            })
            ->when($salla, fn($query) => $query->where('themes.is_salla_theme', 1))
            ->when($free, fn($query) => $query->whereHas('productPrices', fn($subQuery) =>
                $subQuery->where(
                    fn($subQuery) => $subQuery->whereNull('sale_price')
                        ->where('price', 0)
                )->orWhere(
                    fn($subQuery) => $subQuery
                        ->where(fn($query) => $query->whereRaw("DATE(salla_products_pricing.sale_end) > DATE('" . now()->toDateString() . "')")->orWhereNull('sale_end'))
                        ->where('sale_price', 0)
                )->when(store()->plan === Plan::SPECIAL, fn($q) => $q->orWhere('themes.is_salla_theme', 1)))
            )
            ->with([
                'tags',
                'single_image',
                'theme',
                'theme.previewDemoStores',
                'theme.previewDemoStores.category',
                'translations',
                'productPrices.priceDiscounts',
                'productPrices' => fn($subQuery) => $subQuery->when($sort_by === 'discounted', fn($subQuery) => $subQuery->orderBy('salla_products_pricing.sale_price')),
                //we need it, to know that if current store rated this theme or not
                'storeFeedback',
                //we need it, to know current rating & counts
                'feedbacks',
                //we need it, to know current installed theme version
                'themeCustomizations:id,settings,theme',
                //we need it, to know that if current store installed it or not
                'subscriptions', // => fn(HasMany $hasMany) => $hasMany->where('store_id', store()->getId())
            ])
            ->when($selected, fn($query) => $query->whereIn('salla_products.id', $selected))
            ->when($partners, fn($query) => $query->where('themes.developer', '!=', 'سلة'))
            ->when($recommended, fn($query) => $query->whereHas('theme.previewDemoStores', fn($q) =>
                $q->whereIn('category_id', store()->activities->pluck('parent_id')->filter()->toArray())))
            ->when($except, fn($query) => $query->whereNotIn('salla_products.id', $except))
            ->when($limit, fn($query) => $query->limit($limit))
            ->when($rating, function($query) use ($rating) {
                $rating = (int) $rating;

                return $query->withCount([
                    'feedbacks as rating_average' => function ($sub_query) {
                        $sub_query->select(DB::raw('ROUND(AVG(rating), 1)'))->where('status', SallaProductFeedbacksEnum::PUBLISHED);
                    }
                ])
                    ->havingRaw('rating_average >= ? AND rating_average < ?', [$rating, $rating + 1]);
            })
            ->when($sort_by !== 'latest', function ($query) use ($sort_by, $recommended) {
                if ($sort_by == 'special') {
                    return $query->whereIn('salla_products.id', store()->getSetting('themes::special_ids', []));
                }
                if ($sort_by == 'oldest') {
                    return $query->orderBy('published_at');
                }
                if ($sort_by == 'popular') {
                    //=> its mean top seller theme, not top rated
                    return $query->withCount(['subscriptions as subscriptions_count' => function ($sub_query) {
                            $sub_query
                                ->where('salla_subscriptions.amount', '>', 0)//count only paid themes
                                ->where('salla_subscriptions.created_at', '>', now()->subDays(30))
                                ->where('salla_subscriptions.status', SallaSubscriptionStatus::ACTIVE)
                                ->withRealThemes();
                        }])
                        ->orderByDesc('subscriptions_count');
                }
                if ($sort_by === 'discounted') {
                    return $query->whereHas('productPrices', fn($subQuery) => $subQuery
                        ->whereNotNull('salla_products_pricing.price')
                        ->whereNotNull('salla_products_pricing.sale_price')
                    )
                    ->orderByRaw('(
                        SELECT MIN(salla_products_pricing.sale_price)
                        FROM salla_products_pricing
                        WHERE salla_products_pricing.product_id = salla_products.id
                    ) ASC');
                }
                if($sort_by == 'random') {
                    return $query->orderByRaw('RAND()');
                }

                if ($sort_by == 'updated') {
                    return $query->orderByRaw('updated_date DESC, published_at DESC');
                }

                return $query->orderByDesc('published_at');
            })
            ->when($sort_by == 'latest', function ($query) use ($sort_by, $partners, $recommended) {
                if ($recommended) {
                    return $query->orderByRaw('updated_date DESC, published_at DESC');
                }

                return $partners
                    ? $query->orderByRaw('RAND()')
                    : $query->orderByDesc('published_at');
            })
            ->when($sort_by === 'installs', fn($query) =>
                $query->withCount(['subscriptions as subscriptions_count' => function ($sub_query) {
                    $sub_query
                        ->where('salla_subscriptions.amount', '>', 0)//count only paid themes
                        ->where('salla_subscriptions.created_at', '>', now()->subDays(30))
                        ->where('salla_subscriptions.status', SallaSubscriptionStatus::ACTIVE)
                        ->withRealThemes();
                }])
                    ->orderByDesc('subscriptions_count')
                    ->orderByDesc('published_at')
            )
            ->when($sort_by === 'rating', function ($query) use ($rating) {
                return $rating
                    ? $query->orderByDesc('rating_average')
                    : $query->withCount(['feedbacks as rating_average' => fn ($sub_query) =>
                        $sub_query->select(DB::raw('ROUND(AVG(rating), 1)'))->where('status', SallaProductFeedbacksEnum::PUBLISHED)
                    ])->orderByDesc('rating_average')->orderByDesc('published_at');
            });

        if($per_page) {
            return $themes->paginate($per_page);
        }

        return $themes->get();
    }

    public function getThemesCount(): int
    {
        return SallaProducts::query()->where('type', SallaProductType::THEME)->count();
    }

    public function sandboxPlanByTypeValue(string $typeValue): ?SallaProducts
    {
        return $this->model
            ->where('is_sandbox', 1)
            ->where('type', 'plan')
            ->where('type_value', $typeValue)
            ->where('status', 'active')
            ->first();
    }

    public function isSandbox(): self
    {
        $this->model = $this->model
            ->where('is_sandbox', 1);

        return $this;
    }

    public function notSandbox(): self
    {
        $this->model = $this->model
            ->where('is_sandbox', 0);

        return $this;
    }

    /**
     * Return the common (most used) plans. Basic, Plus, and Pro/Team only.
     */
    public function commonPlans(): self
    {
        $this->model = $this->model
            ->where('hide', 0)
            ->whereNull('partner_id');

        return $this
            ->notSandbox()
            ->byTypeValue([Plan::BASIC, Plan::PLUS, Plan::PRO])
            ->byType(SallaProductType::PLAN)
            ->byStatus(SallaSubscriptionStatus::ACTIVE)
            ->withOutProductPriceCampaign();
    }

    public function allPlans(): self
    {
        $this->model = $this->model
            ->whereNull('partner_id');

        return $this
            ->notSandbox()
            ->byTypeValue([Plan::BASIC, Plan::PLUS, Plan::PRO, Plan::SPECIAL])
            ->byType(SallaProductType::PLAN)
            ->byStatus(SallaSubscriptionStatus::ACTIVE)
            ->withOutProductPriceCampaign();
    }

    public function addons(): self
    {
        $this->model = $this->model
            ->with(['productPrices'])
            ->where('status', 'active')
            ->where(function($q) {
                // regular addons
                $q->where(function($q) {
                    $q->where('type_value', 'StoreBranchesLimitAddon')
                        ->orWhere('type_value', 'StoreUsersLimitAddon');
                })
                // special addons
                ->orWhere('type', 'translation')
                ->orWhere('type', 'whatsapp_notifications')
                ->when(feature(AIProductFeature::getName())->isHaveFeature(), function($q) {
                    $q->orWhere('type', 'ai_description');
                });
            });

        return $this;
    }

    public function storeBranchAddon(): SallaProducts
    {
        return $this->addons()->where('type_value', 'StoreBranchesLimitAddon')->first();
    }

    public function storeUserAddon(): SallaProducts
    {
        return $this->addons()->where('type_value', 'StoreUsersLimitAddon')->first();
    }
    
    public function aiDescriptionAddon(): SallaProducts
    {
        return $this->model->query()
            ->where('type', \Modules\MarketPlace\Enum\SallaProductType::AI_DESCRIPTION)
            ->with(['productPrices'])
            ->first();
    }

    /**
     * Return all SMS packages/addons from "salla_products" table
     */
    public function smsPackages(): Builder
    {
        return $this->model->query()
            ->where('type', 'sms')
            ->with(['productPrices'])
            ->orderBy('price');
    }

    public function createMobileApp(): Builder
    {
        return $this->model->query()
            ->where('type', SallaProductType::MOBILE_APPS)
            ->where('action_method', 'create_mobile_app_request')
            ->where('status', 'active');
    }

    public function updateMobileApp(): Builder
    {
        return $this->model->query()
            ->where('type', SallaProductType::MOBILE_APPS)
            ->where('action_method', 'update_mobile_app_request')
            ->where('status', 'active');
    }

    /**
     * @param string|array $typeValue
     * @throws Exception
     */
    public function byTypeValue($typeValue): self
    {
        if (is_string($typeValue)) {
            $this->model = $this->model->where('type_value', $typeValue);
        } elseif (is_array($typeValue)) {
            $this->model = $this->model->whereIn('type_value', $typeValue);
        } else {
            throw new Exception('The value of passed argument is not allowed');
        }

        return $this;
    }

    public function byType(string $type): self
    {
        $this->model = $this->model->where('type', $type);

        return $this;
    }

    public function byStatus($status): self
    {
        if (is_string($status)) {
            $this->model = $this->model->where('status', $status);
        } elseif (is_array($status)) {
            $this->model = $this->model->whereIn('status', $status);
        } else {
            throw new Exception('The value of passed argument is not allowed');
        }

        return $this;
    }

    public function getUpgradePlan(int $currentLevel, int $subscriptionPeriod): ?SallaProducts
    {
        $this->byStatus('active');

        return $this->model
            ->where('level', $currentLevel + 1)
            ->with(['productPrices' => fn($q) => $q->where('period','>=', $subscriptionPeriod)])
            ->first();
    }

    public function plansForStorePlanPage(bool $includeCurrentPlan, int $level, bool $skipSpecial = true): Collection
    {
        if ($skipSpecial) {
            $this->commonPlans();
        } else {
            $this->allPlans();
        }

        return $this->model
            ->with([
                'page',
                'productPrices' => fn($q) => $q->orderByDesc('salla_products_pricing.period'),
            ])
            ->where('level', ($includeCurrentPlan ? '>=' : '>'), $level)
            ->orderBy('salla_products.level')
            ->get();
    }

    public function domainActions(): self
    {
        $this->byStatus('active');

        $this->model = $this->model
            ->where('action_method', 'LIKE', '%domain%');

        return $this;
    }

    public function forDomainPage(): Collection
    {
        return $this
            ->domainActions()
            ->with(['productPrices:id,product_id,price,taxable'])
            ->get();
    }

    public function byHide($hide): self
    {
        if (is_int($hide)) {
            $this->model = $this->model->where('hide', '=', $hide);
        } elseif (is_array($hide)) {
            $this->model = $this->model->whereIn('hide', $hide);
        } else {
            throw new Exception('The value of passed argument is not allowed');
        }

        return $this;
    }

    public function withRelations(): self
    {
        $this->model = $this->model->with([
            'page:id,product_id,features',
            'features:id,product_id,title,description,icon,icon_background,icon_color',
        ]);

        return $this;
    }


    public function applyCharityFilter(bool $isCharity): self
    {
        if ($isCharity) {
            $this->model = $this->model->where('type_value', '=', SallaProductPlanType::TEAM);
        }

        return $this;
    }

    public function excludePartners(): self
    {
        $this->model = $this->model->whereNull('partner_id');

        return $this;
    }

    public function orderBySort(): self
    {
        $this->model = $this->model->orderBy('sort');

        return $this;
    }

    public function byLevel($includeCurrentPlan,$level): self
    {
        if (is_int($level)) {
            $this->model = $this->model->where('level', ($includeCurrentPlan ? '>=' : '>'), $level)
                ->orderBy('salla_products.level');
        } elseif (is_array($level)) {
            $this->model = $this->model->whereIn('hide', $level);
        } else {
            throw new Exception('The value of passed argument is not allowed');
        }

        return $this;
    }

    public function shouldApplyPeriodFilter($currentSubscriptionPlan, $plan): bool
    {
        return !empty($currentSubscriptionPlan)
            && !$currentSubscriptionPlan->canRenew()
            && $currentSubscriptionPlan->product_id === $plan->id;
    }

    public function getBundledProducts($productId) 
    {
        return $this->model->planBundledProductItems() 
            ->where('product_id', $productId);
    }

    public function withFilteredProductPrices(array $utmData = []): self
    {
        $utmSource    = $utmData['utm_source'] ?? null;
        $utmCampaign  = $utmData['utm_campaign'] ?? null;
        $shouldFilter = ($utmSource || $utmCampaign) && $utmSource !== 'vip_partners';

        $this->model = $this->model->when($shouldFilter, function ($query) use ($utmSource, $utmCampaign) {
            $query->whereHas('productPrices.utm', function ($subQuery) use ($utmSource, $utmCampaign) {
                $subQuery->where('utm_expire_at', '>=', now()->format('Y-m-d'))
                    ->where(function ($q) use ($utmSource, $utmCampaign) {
                        if ($utmSource && $utmCampaign) {
                            $q->where('utm_source', $utmSource)
                                ->orWhere('utm_campaign', $utmCampaign);
                        } elseif ($utmSource) {
                            $q->where('utm_source', $utmSource);
                        } elseif ($utmCampaign) {
                            $q->where('utm_campaign', $utmCampaign);
                        }
                    });
            });
        });

        return $this;
    }

    public function withOutProductPriceCampaign(): self
    {
        $this->model = $this->model->doesntHave('productPrices.utm');

        return $this;
    }
}
