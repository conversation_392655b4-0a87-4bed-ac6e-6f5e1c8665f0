<?php


namespace Modules\MarketPlace\Repositories;


use Illuminate\Support\Facades\DB;
use Modules\Store\Enum\StorePattern;
use Salla\Settings\Facades\Settings;
use Salla\Core\Entities\PartnerModel;
use Modules\MarketPlace\Enum\SallaProductType;
use Prettus\Repository\Eloquent\BaseRepository;
use Modules\MarketPlace\Entities\SallaOrderItems;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Entities\Partner\PartnerSetting;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\Payment\Presenters\Partner\PaymentPayoutPartnerPresenter;

class SallaOrderItemRepository extends BaseRepository
{
    /**
     * Specify Model class name.
     *
     * @return string
     */
    public function model()
    {
        return SallaOrderItems::class;
    }

    /**
     * @param null $developer_id
     * @return mixed
     */
    public function getUnPaidDeveloperOrderItemsQuery($developer_id = null, $type, $type_value = null, $without_partner_payment = true)
    {
        return SallaOrderItems::PartnerItemsHasNotPayments($type, $type_value, $without_partner_payment)
            ->join('stores', 'salla_orders.store_id', '=', 'stores.id')
            ->where('stores.is_test', false)
            ->where('stores.pattern', StorePattern::NORMAL)
            ->whereNotExists(function ($query) {
                $table = with(new PartnerSetting)->getTable();

                $query->select(DB::raw(1))
                    ->from($table)
                    ->whereRaw("$table.bag = salla_order_items.developer_user_id")
                    ->where("$table.key", "is_frozen")
                    ->where("$table.value", "1");
            })
            ->when(!empty($developer_id), function ($query) use ($developer_id) {
                return $query->where('salla_order_items.developer_user_id', $developer_id);
            });
    }

    /**
     * @param $developer_id
     * @return PaymentPayoutPartnerPresenter
     */
    public function getUnPaidDeveloperOrderItemsDueAmount(PartnerCompany $company)
    {
        $total = 0;
        $tax_amount = 0;
        $fees = 0;
        $due_amount = 0;

        foreach (SallaProductType::getPartnerTypes() as $type) {
            if ($type == SallaProductType::ADDON) {
                foreach (SallaProductAddonType::getPartnerTypes() as $type_value) {
                    $temp = $this->calculateTotalUnPaidDeveloperOrderItems($company, $type, $type_value);

                    $total += $temp['total'];
                    $tax_amount += $temp['tax_amount'];
                    $fees += $temp['fees'];
                    $due_amount += $temp['due_amount'];
                }

                continue;
            }

            $temp = $this->calculateTotalUnPaidDeveloperOrderItems($company, $type);

            $total += $temp['total'];
            $tax_amount += $temp['tax_amount'];
            $fees += $temp['fees'];
            $due_amount += $temp['due_amount'];
        }

        $commission_amount = $this->getCommissionAmount($company->id);

        //total, $tax_amount, $fees, $due_amount
        return new PaymentPayoutPartnerPresenter(
            $company,
            $total,
            $tax_amount,
            $fees,
            $due_amount,
            $commission_amount
        );
    }

    /**
     * @param $company_id
     * @param $type
     * @param $type_value
     * @return array
     */
    private function calculateTotalUnPaidDeveloperOrderItems(PartnerCompany $company, $type, $type_value = null)
    {
        $total_item = $this->getUnPaidDeveloperOrderItemsQuery($company->id, $type, $type_value, false)
            ->select(
                DB::raw('IFNULL(SUM(salla_order_items.total), 0) as total'),
                DB::raw('IFNULL(SUM(salla_order_items.product_price), 0) as amount'),
                DB::raw('IFNULL(SUM(salla_order_items.tax_value), 0) as tax_amount'),
                DB::raw('IFNULL(SUM(salla_order_items.refund_amount), 0) as refund_amount')
            )
            ->first();

        $refund_amount = !empty($total_item->refund_amount) ? $total_item->refund_amount : 0;

        $tax = Settings::get('payment::salla-partner-tax', 15);

        $total = $total_item->total;
        $tax_amount = $total_item->tax_amount;
        if ($refund_amount > 0) {
            $total = $total - $refund_amount;
            $tax_amount = round(($total * $tax) / ($tax + 100), 3);
        }
        $fees = ($total - $tax_amount) * ($company->getCommissionPercentage($type, $type_value) / 100);
        $due_amount = $total - $tax_amount - $fees;

        return [
            'total'      => $total,
            'tax_amount' => $tax_amount,
            'fees'       => $fees,
            'due_amount' => $due_amount,
        ];
    }

    /**
     * @param $companyId
     * @param $productPriceId
     * @param $related_type
     * @return int
     */
    public function getCommissionAmount($companyId, $productPriceId = null, $related_type = null)
    {
        return SallaOrderItems::PartnerPaymentItems(
            $companyId,
            null,
            $productPriceId,
            $related_type
        )
            ->join('partner_order_details', 'salla_order_items.id', '=', 'partner_order_details.order_item_id')
            ->whereNull('partner_order_details.deleted_at')
            ->sum('partner_order_details.commission_amount') ?: 0;
    }
}