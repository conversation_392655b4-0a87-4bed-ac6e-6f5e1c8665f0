<?php

namespace Modules\Payment\Repositories;

use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Settings\Facades\Settings;
use Prettus\Repository\Eloquent\BaseRepository;
use Modules\Payment\Enums\PartnerPaymentLogDetailStatus;
use Modules\Payment\Entities\PartnerPaymentLogDetail;

class PartnerPaymentLogDetailRepository extends BaseRepository
{

    /**
     * Specify Model class name.
     *
     * @return string
     */
    public function model()
    {
        return PartnerPaymentLogDetail::class;
    }

    /**
     * @param $appId
     * @return mixed
     */
    public function getPaymentLogsDetailOfApp($appId)
    {
        return PartnerPaymentLogDetail::where('app_id', $appId)
            ->when(!empty(request()->get('reference_id')), function ($query) {
                return $query->where('order_id', request()->get('reference_id'));
            })
            ->latest('id')
            ->paginate(15);
    }

    /**
     * @param $partner_id
     * @param $status
     * @return mixed
     */
    public function getCountOfOrders($partner_id, $status = null)
    {
        $status = $status ?: PartnerPaymentLogDetailStatus::READY;
        return PartnerPaymentLogDetail::where('partner_company_id', $partner_id)
            ->where('status', $status)
            ->count('id');
    }

    /**
     * @param $partner_id
     * @param $status
     * @return mixed
     */
    public function getTotalOfOrders($partner_id, $status = null)
    {
        $status = $status ?: PartnerPaymentLogDetailStatus::READY;
        $total = PartnerPaymentLogDetail::select(DB::raw('SUM(ABS(total)) as total_holding'))
                ->join('epayment_log', 'epayment_log.id', '=', 'partner_epayment_log_detail.epayment_log_id')
                ->where('partner_epayment_log_detail.partner_company_id', $partner_id)
                ->where('partner_epayment_log_detail.status', $status)
                ->first()?->total_holding ?: 0;

        $result = PartnerPaymentLogDetail::getPayoutAmounts($total,$partner_id);

        return round($result['due_amount'], 2);
    }

    /**
     * @param $partner_id
     * @param $status
     * @return mixed
     */
    public function getOrders($partner_id, $status = null)
    {
        $status = $status ?: PartnerPaymentLogDetailStatus::READY;
        return PartnerPaymentLogDetail::where('partner_company_id', $partner_id)
            ->where('status', $status)
            ->with('order')
            ->simplePaginate(15);
    }
}