<?php

namespace Modules\MarketPlace\Traits;

use App\Widgets\SeoCartWidget;
use Illuminate\Database\Eloquent\Collection;
use Modules\MarketPlace\Contract\SallaCartService;
use Modules\MarketPlace\Entities\SallaBanks;
use Modules\MarketPlace\Entities\SallaCartItem;
use Modules\MarketPlace\Entities\SallaCartItemFreeProduct;
use Modules\MarketPlace\Enum\SallaCartSource;
use Modules\MarketPlace\Enum\SallaCouponCodeEnum;
use Modules\MarketPlace\Presenter\Cart\CartItemPriceOptionsPresenter;
use Modules\MarketPlace\Presenter\SummaryItems\ChangePlanDiscount;
use Modules\MarketPlace\Presenter\SummaryItems\Coupon;
use Modules\MarketPlace\Presenter\SummaryItems\Credit;
use Modules\MarketPlace\Presenter\SummaryItems\GiftCard;
use Modules\MarketPlace\Presenter\SummaryItems\LoyaltyPoint;
use Modules\MarketPlace\Presenter\SummaryItems\SubTotal;
use Modules\MarketPlace\Presenter\SummaryItems\Tax;
use Modules\MarketPlace\Presenter\SummaryItems\Total;
use Modules\MarketPlace\Services\SallaInstallmentService;
use Salla\Core\Enum\StoreDocumentStatus;

trait RedrawCart
{
    /**
     * @var \Modules\MarketPlace\Contract\SallaCartService
     */
    protected $cartService;

    /**
     * @param false $openCart
     * @param null $errorMessage
     * @param array $extraData
     * @param false $addNewItem
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse|mixed|\Salla\Core\Services\Ajax
     */
    private function redrawCart($openCart = false, $errorMessage = null, $extraData = [], $addNewItem = false, $extraJs = null)
    {
        $this->cartService = $this->cartService ?: app(SallaCartService::class);

        $cart = $this->cartService->getCart(request()->get('source', null) === SallaCartSource::EXPERT);

        /** @var SallaCartItem[]|Collection $cartItems */
        $cartItems = $this->cartService->getItems();

        if ($cartItems->count() === 0) {
            return ajax()->when($errorMessage, function ($ajax, $errorMessage) {
                return $ajax->error($errorMessage);
            })->runJavascript('$(\'.button_show_cart\').hide()');
        }

        //to save some extra quires, lets use this same product
        if ($product = optional(request())->route('product')) {
            $cartItems->map(fn( $item) => $item->product_id == $product->id && $item->setRelation('product', $product));
        }
        $totals = $this->cartService->getTotals();

        if ($totals->hasError()) {
            return ajax()->error(implode(', ', $totals->getErrors()));
        }

        $gifts = $this->cartService->getCartFreeProducts()->transform(function ($item, $key) {
            /**
             * @var SallaCartItemFreeProduct $item
             */
            $item->product_name = $item->product->name;
            $item->product_price = $item->productPrice->getPrice();

            return $item;
        });

        $gifts_data = $gifts->transform(function ($item, $key) {
            /**
             * @var SallaCartItemFreeProduct $item
             */
            $item->product_name = $item->product->name;
            $item->product_price = $item->productPrice->getPrice();
            $item->countryCode = 'SA';
            $item->currencyCode = 'SA';

            return $item;
        })->toJson();

        if (request()->filled('open')) {
            $openCart = true;
        }

        $cartItems->loadMissing('product.single_image');
        $source = request()->get('source', null);
        $isUpdate = request()->get('is_update', 0);

        $isInstallmentPayment = $this->cartService->isInstallmentPayment();
        $installmentService = new SallaInstallmentService($this->cartService);
        $couponCode = $this->cartService->getCouponCode();

        $cartData = [
            'cart'                     => $this->cartService->getCart(),
            'source'                   => $source,
            'from'                     => request()->get('from', null),
            'is_update'                => $isUpdate,
            'cartItems'                => $cartItems,
            'cart_item_price_options'  => CartItemPriceOptionsPresenter::make($cartItems),
            'gifts'                    => $gifts,
            'gifts_data'               => $gifts_data,
            'totals'                   => $totals,
            'show_bank_payment_method' => store()->getSetting('sallaStore::is-bank-transform-enabled', true) &&
                                          (new SallaCartSource)->checkBankPaymentMethod($cart->source),
            'payment_methods'          => $this->cartService->getAvailablePaymentMethods(),
            'banks'                    => SallaBanks::all(),
            'coupon_code'              => $couponCode,
            'total_summery'            => [
                'tax'              => $totals->getSummeryByType(Tax::TITLE),
                'chnage_plan_cost' => $totals->getSummeryByType(ChangePlanDiscount::TITLE),
                'loyalty_point'    => $totals->getSummeryByType(LoyaltyPoint::TITLE),
                'credit'           => $totals->getSummeryByType(Credit::TITLE),
                'coupon_discount'  => $totals->getSummeryByType(Coupon::TITLE),
                'gift_card'        => $totals->getSummeryByType(GiftCard::TITLE),
                'sub_total'        => $totals->getSummeryByType(SubTotal::TITLE),
                'total'            => $totals->getSummeryByType(Total::TITLE),
            ],
            'with_header'          => !$this->getNewAppCart() || $source === \Modules\MarketPlace\Enum\SallaCartSource::MOBILE_APP,
            'is_new_app_cart'      => $this->getNewAppCart(),
            'installment'          => [
                'uncompleted_stores'    => $this->cartService->hasUncompletedInstallment(),
                'show'                  => $this->cartService->canShowInstallment(),
                'allowed'               => $this->cartService->isInstallmentAllowed(),
                'store_documented'      => store()->getStoreDocumentStatus() == StoreDocumentStatus::COMPLETED,
                'active'                => $isInstallmentPayment,
                'data'                  => $isInstallmentPayment ? $installmentService->getInstallmentData() : null,
                'first_installment'     => $isInstallmentPayment ? $installmentService->getFirstInstallmentData() : null
            ],
            'is_create_mobile_app' => $source === \Modules\MarketPlace\Enum\SallaCartSource::MOBILE_APP && !$isUpdate,
            'with_coupon'          => SallaCartSource::checkShowCouponInCart(),
            'is_apply_coupon'      => !empty($couponCode),
            'hasBundledProduct'    => $this->cartService->getCart()->items()->has('price.bundledProducts')->exists(),
        ];

        \Widget::run(SeoCartWidget::class, [
            'cartService' => $this->cartService,
            'items'       => $cartItems,
        ]);

        if ($cartItems->count() > 1 && $addNewItem && \is_null($source)) {
            $openCart = false;
        }

        return ajax()
            ->when($extraData, function ($ajax, $extraData) {
                return $ajax->setDatums($extraData);
            })
            ->keepModal()
            ->when($source != SallaCartSource::MOBILE_APP,
                function ($ajax) {
                    return $ajax->hideModal('#modal_salla_product');
                })
            ->when($openCart || (empty($source) && empty($this->cartService->getCart()->source)),
                function ($ajax) use ($openCart, $addNewItem, $cartItems, $extraJs) {

                    $javascript = '';
                    if(!$this->getNewAppCart()) {
                        $javascript .= ' $("#modal_salla_cart").modal("show"); setTimeout(() => $("body").addClass("modal-open"), 500);';
                    }

                    $javascript .= ' ' . $extraJs;

                    return $ajax->runJavascript($javascript);
                })
            ->redrawView($this->getNewAppCart() ? 'div_app_payment_content' : 'div_modal_salla_cart_content')
            ->view('marketplace::dashboard.cart.index', $cartData);
    }

    /**
     * @return string
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    private function getNewAppCart()
    {
        if(
            feature('install-app-method-v2')->isHaveFeature() &&
            request()->get('source') == SallaCartSource::APP
        ) {
            return true;
        }

        return false;
    }
}
