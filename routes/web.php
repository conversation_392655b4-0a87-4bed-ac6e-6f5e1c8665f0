<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON>\LaravelImageOptimizer\Middlewares\OptimizeImages;

Route::get('/update-app', [
    'as' => 'update.app',
    'uses' => 'CP\UpdateAppController',
]);

Route::get('/update-skip', [
    'as' => 'update.skip',
    'uses' => 'CP\UpdateAppController@skipUpdate',
]);

Route::post('background/progress', [
    'as' => 'cp.queue.progress.toast',
    'uses' => 'CP\ProgressToastController',
])->middleware('auth');

Route::get('operations-logs/latest', [
    \Modules\Settings\Actions\Graylog\GetOperationLogsAction::class,
    'asController'
])->name('cp.operations-logs.latest')
    ->middleware('auth');

Route::get('/email/verify', [
    'as' => 'verification.notice',
    'uses' => 'CP\StoreOwnerEmailVerificationController',
])->middleware('auth');

Route::get('/email/verify/{encrypted}', [
    'as' => 'verification.verify',
    'uses' => 'CP\StoreOwnerEmailVerifiedController',
])->middleware('auth:user');

Route::get('/email/user/verify/{encrypted}', [
    'as' => 'employee.verification.verify',
    'uses' => 'CP\StoreEmployeeVerifyController',
]);

Route::post('/banner/close_share_store', [
    'as' => 'banner.close_share_store',
    'uses' => 'CP\CloseShareStoreBannerController',
])->middleware('auth');

/**
 * this is important for route cache, check app/RouteServerProvider
 */
if (request()->getHost() === config('salla.app.domain')) {
    Route::get('readiness', 'ReadinessController')->name('readiness');
} else {
    Route::domain(request()->getHost())->get('readiness', 'ReadinessController')->name('readiness');
}
Route::get('users/{token}/activate_user', [
    'as'   => 'cp.users.activate_user',
    'uses' => 'CP\UserController@activateUser',
]);
Route::post('users/{token}/activate_user', [
    'as'   => 'cp.users.activate_user.store',
    'uses' => 'CP\UserController@doActivateUser',
]);
Route::get('users/need_activation', [
    'as'   => 'cp.users.need_activation',
    'uses' => 'CP\UserController@needActivation',
]);

Route::get('/detail', [
    'as'   => 'cp.partner.profile',
    'uses' => 'CP\PartnerController',
]);

Route::group(['middleware' => ['auth:user,api', 'check_user']], function () {
    Route::group(['middleware' => ['auth.cp']], function () {
        Route::get('/', ['as'   => 'cp.dashboard', 'uses' => 'CP\DashboardController@index',]);
        Route::get('/search', [
            'middleware' => ['escapeEmoji'],
            'as' => 'cp.dashboard.search',
            'uses' => 'CP\DashboardController@search',
        ]);

        Route::get('rating', [
            'as'   => 'cp.rating',
            'uses' => 'CP\StoreController@showRating',
        ]);
        Route::get('demo/redirect', [
            'as'   => 'demo.redirect',
            'uses' => 'CP\DemoController@redirect',
        ]);
        Route::group(['prefix' => 'dashboard'], function () {
            Route::post('save_store_target', [
                'as'   => 'cp.dashboard.save_store_target',
                'uses' => 'CP\DashboardController@saveStoreTarget',
            ]);

            Route::post('show_summery', [
                'as'   => 'cp.dashboard.summery',
                'uses' => 'CP\DashboardController@showSummery',
            ]);

            Route::post('show_orders', [
                'as'   => 'cp.dashboard.orders',
                'uses' => 'CP\DashboardController@showOrders',
            ]);

            Route::post('show_products', [
                'as'   => 'cp.dashboard.products',
                'uses' => 'CP\DashboardController@showProducts',
            ]);
            Route::get('show_products/load-more/{next?}', [
                'as'   => 'cp.dashboard.products.load-more',
                'uses' => 'CP\DashboardController@showProducts',
            ]);

        });

        Route::group(['prefix' => 'products', 'middleware' => ['cors']], function () {
            Route::post('uploadphoto', [
                'as'   => 'cp.uploadphoto',
                'uses' => 'CP\UploaderController@uploadPhoto',
            ]);

            /******************** product sorting routes **************************/
            Route::group(['prefix' => 'sorting'], function () {

                // todo move them to product module
                Route::post('/index', 'Api\ProductSorting\ProductSortingController@save')->name('products.sorting.save');
                Route::get('categories', 'Api\ProductSorting\ProductSortingController@getCategories')->name('products.sorting.get_categories');
                Route::get('/categories/{category}/product', 'Api\ProductSorting\ProductSortingController@index')->name('products.sorting.index');
            });
            /******************************************************************************************/
        }); // end products group

        Route::group(['prefix' => 'orders'], function () {
            /*
             * @deprecated
             */

            Route::get('/order/check_suspicious_popup', [
                'as' => 'cp.order.check_suspicious_popup',
                'uses' => 'CP\OrderController@checkSuspiciousPopup'
            ]);

            Route::any('{status?}', [
                'as'   => 'cp.orders.index',
                'uses' => 'CP\OrderController@index',
            ]);
            Route::get('order/{order}', [
                'as'   => 'cp.order',
                'uses' => 'CP\OrderController@info',
            ]);
            Route::post('order/status', [
                'as'   => 'cp.order.status',
                'uses' => 'CP\OrderController@status',
            ])->middleware('locker');

            Route::post('order/hide-help-panel', [
                'as'   => 'cp.order.hide-help-panel',
                'uses' => 'CP\OrderController@hideHelpPanel',
            ]);

            Route::post('order/status/modal', [
                'as'   => 'cp.order.change-status-modal',
                'uses' => 'CP\OrderController@multiOrdersChangeStatusModal',
            ]);

            Route::get('order/receipt/{order}', [
                'as'   => 'cp.order.receipt',
                'uses' => 'CP\OrderController@receiptInfo',
            ]);
            Route::get('shipping/{order}', [
                'as'   => 'cp.order.shipping',
                'uses' => 'CP\OrderController@orderShipping',
            ]);
            Route::post('export/orders', [
                'as'   => 'cp.orders.export',
                'uses' => 'CP\OrderController@export',
            ]);

            Route::post('order/private_status/{order}/{orderShipmentBranch?}', [
                'as'   => 'cp.order.private_status',
                'uses' => 'CP\OrderController@getPrivateStatus',
            ]);

            Route::post('order/handle_suspicious_alert/{order}', [
                'as'   => 'cp.order.handle_suspicious_alert',
                'uses' => 'CP\OrderController@handleSuspiciousAlert',
            ]);

            Route::put('order/mark_orders_as_read', [
                'as'   => 'cp.order.mark_orders_as_read',
                'uses' => 'CP\OrderController@markOrdersAsRead',
            ]);

            Route::group(['prefix' => 'company_options'], function () {
                Route::post(
                    'custom_company_options/{order}/{orderShipmentBranch?}',
                    'CP\OrderController@companyAdvanceOption'
                )
                    ->name('cp.order.company_options');
            });
        }); // end orders perfix


        Route::group(['prefix' => 'order'], function () {
            Route::get('new', [
                'as'   => 'cp.order.new',
                'uses' => 'CP\CreateOrderController@new',
            ]);
            Route::get('reorder/{order_id}', [
                'as'   => 'cp.order.reorder',
                'uses' => '\Modules\Orders\Actions\Order\Reorder',
            ])->middleware('optimusids:order_id');
            Route::get('edit/{order}', [
                'as'   => 'cp.order.edit',
                'uses' => 'CP\CreateOrderController@index',
            ]);
            Route::get('block/{order}/{tmpl}', [
                'as'   => 'cp.order.block',
                'uses' => 'CP\CreateOrderController@loadTemplate',
            ]);
            Route::get('customer/{order}', [
                'as'   => 'cp.order.customer',
                'uses' => 'CP\CreateOrderController@showCustomerForm',
            ]);
            Route::post('customer_info', [
                'as'   => 'cp.order.customer.info',
                'uses' => 'CP\CreateOrderController@showCustomerInfo',
            ]);
            Route::post('customer/{order}', [
                'as'   => 'cp.order.customer.store',
                'uses' => 'CP\CreateOrderController@storeCustomerForm',
            ])->middleware('escapeEmoji');
            Route::get('customer_search', [
                'middleware' => ['escapeEmoji'],
                'as'   => 'cp.order.customer.search',
                'uses' => 'CP\CreateOrderController@searchCustomer',
            ]);
            Route::get('shipping/{order}', [
                'as'   => 'cp.order.shipping.form',
                'uses' => 'CP\CreateOrderController@showShippingForm',
            ]);
            Route::get('shipping_address/{order}', [
                'as'   => 'cp.order.shipping.address.form',
                'uses' => 'CP\CreateOrderController@showShippingAddressForm',
            ]);
            Route::get('shipping/{order}/{orderShipmentBranch}', [
                'as'   => 'cp.order.shipping.branch.shipping.details.form',
                'uses' => 'CP\CreateOrderController@showBranchShippingDetailsForm',
            ]);
            Route::post('shipping_list/{order}', [
                'as'   => 'cp.order.shipping.list',
                'uses' => 'CP\CreateOrderController@showShippingList',
            ]);
            Route::post('shipping_save/{order}', [
                'as'   => 'cp.order.shipping.save',
                'uses' => 'CP\CreateOrderController@saveShipping',
            ])->middleware('escapeEmoji');
            Route::post('shipping_address_save/{order}', [
                'as'   => 'cp.order.shipping.address.save',
                'uses' => 'CP\CreateOrderController@saveShippingAddress',
            ])->middleware('escapeEmoji');
            Route::post('shipping_branch_save/{order}/{orderShipmentBranch}', [
                'as'   => 'cp.order.shipping.branch.shipping.details.save',
                'uses' => 'CP\CreateOrderController@saveBranchShippingDetails',
            ])->middleware('escapeEmoji');

            Route::post('payment/{order}', [
                'as'   => 'cp.order.payment.save',
                'uses' => 'CP\CreateOrderController@savePayment',
            ]);

            Route::post('payment/photo/{order}', [
                'as'   => 'cp.order.payment.photo',
                'uses' => 'CP\CreateOrderController@uploadPhoto',
            ])->middleware(OptimizeImages::class);

            Route::get('payment/remove_photo/{order}', [
                'as'   => 'cp.order.payment.deletephoto',
                'uses' => 'CP\CreateOrderController@removePhoto',
            ]);

            Route::get('product_search', [
                'middleware' => ['escapeEmoji'],
                'as'   => 'cp.order.product.search',
                'uses' => 'CP\CreateOrderController@searchProduct',
            ]);
            Route::post('product_save/{order}', [
                'as'   => 'cp.order.product.save',
                'uses' => 'CP\CreateOrderController@saveProduct',
                'middleware' => [
                    'escapeEmoji',
                    'optimusids:branch_id'
                ],
            ]);

            Route::post('save_order_options/{order}', [
                'as'   => 'cp.order.products.save_order_options',
                'uses' => 'CP\CreateOrderController@saveOrderOptions',
            ]);
            Route::post('upload_option_image', [
                'as'   => 'cp.order.products.upload_option_image',
                'uses' => 'CP\CreateOrderController@uploadOptionImage',
            ])->middleware(OptimizeImages::class);

            Route::post('activate/{order}', [
                'as'   => 'cp.order.activate',
                'uses' => 'CP\CreateOrderController@activateOrder',
            ]);
            Route::post('draft/{order}', [
                'as'   => 'cp.order.draft',
                'uses' => 'CP\CreateOrderController@draftOrder',
            ]);

            /*  Route::post('delete/{order_id}', [
                    'as'   => 'cp.order.delete',
                    'uses' => 'CP\CreateOrderController@deleteOrder',
                ]); */

            Route::post('get-shipping-address', [
                'as'   => 'cp.order.get-shipping-address',
                'uses' => 'CP\CreateOrderController@getShippingAddress',
            ]);

            Route::group(['middleware' => 'canEditOrder:{order_id}'], function () {
                Route::get('payment/{order}', [
                    'as'   => 'cp.order.payment.form',
                    'uses' => 'CP\CreateOrderController@showPaymentForm',
                ]);

                Route::get('products/{order}', [
                    'as'   => 'cp.order.product.form',
                    'uses' => 'CP\CreateOrderController@showproductForm',
                ]);

                Route::group(['middleware' => 'CanAccessOrderOptions'], function () {

                    Route::get('order-options/{order}/{order_option?}', [
                        'as' => 'cp.order.order_options.form',
                        'uses' => 'CP\CreateOrderController@showOrderOptionsForm',
                    ]);

                    Route::post('order-options/update/{order}', [
                        'as' => 'cp.order.order_options.update',
                        'uses' => 'CP\CreateOrderController@updateOrderOptionsForm',
                    ])->middleware('escapeEmoji');

                    Route::post('order-options/{order}/{order_option?}', [
                        'as' => 'cp.order.order_options.store',
                        'uses' => 'CP\CreateOrderController@storeOrderOptionsForm',
                    ])->middleware('escapeEmoji');

                    Route::get('has-order-options/{order}/{order_item_id?}', [
                        'as' => 'cp.order.product.has_order_options',
                        'uses' => 'CP\CreateOrderController@orderItemHasOrderOptions',
                    ])->middleware('escapeEmoji');
                });

                Route::post('remove_product/{order}', [
                    'as'   => 'cp.order.products.remove_product',
                    'uses' => 'CP\CreateOrderController@removeProduct',
                ]);

                Route::post('remove_order_option/{order}', [
                    'as'   => 'cp.order.order_options.remove',
                    'uses' => 'CP\CreateOrderController@removeOrderOption',
                ]);

                Route::post('update_item_price/{order}', [
                    'as'   => 'cp.order.products.update_item_price',
                    'uses' => 'CP\CreateOrderController@updateItemPrice',
                ]);

                Route::post('show_options/{order}', [
                    'as'   => 'cp.order.products.show_options',
                    'uses' => 'CP\CreateOrderController@showProductOptions',
                ]);

                Route::post('show_order_options/{order}', [
                    'as'   => 'cp.order.products.show_order_options',
                    'uses' => 'CP\CreateOrderController@showOrderOptions',
                ]);

                Route::post('apply_coupon/{order}', [
                    'as'   => 'cp.order.coupon.apply',
                    'uses' => 'CP\CreateOrderController@applyCoupon',
                ]);

                Route::post('delete_coupon/{order}', [
                    'as'   => 'cp.order.coupon.delete',
                    'uses' => 'CP\CreateOrderController@deleteCoupon',
                ]);

                Route::post('update_product/{order}', [
                    'as'   => 'cp.order.products.update_product',
                    'uses' => 'CP\CreateOrderController@updateProduct',
                    'middleware' => 'optimusids:branch_id,id,item_id',
                ]);

                Route::get('reset_wait_payment/{order}', 'CP\OrderController@reset_wait_payment')
                    ->name('cp.order.reset_wait_payment');

                Route::post('remove_loyalty_point/{order}', [
                    'as'   => 'cp.order.loyalty_points.remove',
                    'uses' => 'CP\CreateOrderController@removeLoyaltyPrize',
                ]);
            });
        }); // end order perfix
        Route::group(['prefix' => 'coupons'], function () {
            Route::get('/', [
                'as'   => 'cp.coupons',
                'uses' => 'CP\CouponController@index',
            ]);
            Route::get('coupon/{id?}', [
                'as'   => 'cp.coupon.create',
                'uses' => 'CP\CouponController@create',
            ]);
            Route::post('load_coupons', [
                'as'   => 'cp.load_coupons',
                'uses' => 'CP\CouponController@loadCoupons',
            ]);
            Route::get('coupon/browse/{coupon}/orders', [
                'as'   => 'cp.coupon.browse_orders',
                'uses' => 'CP\CouponController@browse',
            ]);
            Route::post('store', [
                'as'   => 'cp.coupon.store',
                'uses' => 'CP\CouponController@store',
            ])->middleware('trimmer');

            Route::post('status', [
                'as'   => 'cp.coupon.disable',
                'uses' => 'CP\CouponController@disable',
            ]);
            Route::post('delcoupon/{id}', [
                'as'   => 'cp.coupon.delete',
                'uses' => 'CP\CouponController@delete',
            ]);
            Route::get('coupon_list', [
                'as'   => 'cp.coupon.list',
                'uses' => 'CP\CouponController@list',
            ]);
            Route::post('report/{id}', [
                'as'   => 'cp.coupon.report',
                'uses' => 'CP\CouponController@report',
            ]);
            Route::post('export_report/{id}', [
                'as'   => 'cp.coupon.export_report',
                'uses' => 'CP\CouponController@export_report',
            ]);
            Route::get('marketing-coupon/{id}', [
                'as'   => 'cp.coupon.marketing_coupon',
                'uses' => 'CP\CouponController@getMarketingCoupon',
            ]);
            Route::get('group/{coupon}', [
                'as'   => 'cp.coupon.group_coupons_list',
                'uses' => '\Modules\Marketing\Http\Controllers\Dashboard\CouponController@group_coupons_list',
                'middleware' => 'feature:coupon-group',
            ]);
            Route::post('export/coupons', [
                'as'   => 'cp.coupon.export_coupons',
                'uses' => 'CP\CouponController@export_coupons',
                'middleware' => 'feature:coupon-export',
            ]);
            Route::get('export/group/coupons/{coupon}', [
                'as'   => 'cp.coupon.export_group_coupons',
                'uses' => 'CP\CouponController@export_group_coupons',
                'middleware' => ['feature:coupon-group', 'feature:coupon-export'],
            ]);
            Route::post('upload', [
                'as'   => 'cp.coupon.upload',
                'uses' => '\Modules\Marketing\Http\Controllers\Dashboard\CouponController@upload',
            ]);
        });
        Route::group(['prefix' => 'profile'], function () {
            Route::get('/', [
                'as'   => 'cp.profile',
                'uses' => 'CP\UserController@profile',
            ]);
            Route::post('/', [
                'as'         => 'cp.profile.store',
                'uses'       => 'CP\UserController@updateProfile',
            ])->middleware(['trimmer']);
            Route::post('updatepassword', [
                'as'   => 'cp.profile.updatepassword',
                'uses' => 'CP\UserController@updatePassword',
            ]);
            Route::post('edit_mobile', [
                'as'   => 'cp.profile.edit_mobile',
                'uses' => 'CP\UserController@editMobile',
            ]);
            Route::post('update_mobile', [
                'as'   => 'cp.profile.update_mobile',
                'uses' => 'CP\UserController@updateMobile',
            ]);
            Route::post('activate_mobile', [
                'as'   => 'cp.profile.activate_mobile',
                'uses' => 'CP\UserController@activateMobile',
            ]);
            Route::post('resend_activation_code', [
                'as'   => 'cp.profile.resend_activation_code',
                'uses' => 'CP\UserController@resendActivationCode',
            ]);
        });
        Route::group(['prefix' => 'settings'], function () {
            Route::get('refresh_settings', [
                'as'   => 'cp.settings.refresh',
                'uses' => 'CP\StoreController@refreshSettings',
            ]);
            Route::post('/', [
                'as'   => 'cp.settings.store',
                'uses' => 'CP\StoreController@updateAll',
            ]);
            Route::post('/settings/social', [
                'as'   => 'cp.settings.social',
                'uses' => 'CP\StoreController@postSocial',
            ]);
            Route::post('/settings/social_connect', [
                'as'   => 'cp.settings.social_connect',
                'uses' => 'CP\StoreController@postSocialConnect',
            ]);
            Route::post('layout', [
                'as'   => 'cp.settings.layout',
                'uses' => 'CP\StoreController@postLayout',
            ]);
            Route::post('updatebio', [
                'as'   => 'cp.settings.updatebio',
                'uses' => 'CP\StoreController@updatebio',
            ]);
            Route::get('disable_price_alert', [
                'as'   => 'cp.settings.pricing_alert',
                'uses' => 'CP\UserController@disablePricingalert',
            ]);
            Route::get('mystore', [
                'as'   => 'cp.settings.mystore',
                'uses' => 'CP\StoreController@getStoreContent',
            ]);
            Route::post('order_settings', [
                'as'   => 'cp.settings.order',
                'uses' => 'CP\StoreController@saveOrderSettings',
            ]);
            Route::get('avatar', [
                'as'   => 'cp.settings.avatar',
                'uses' => 'CP\StoreController@editAvatar',
            ]);
            Route::post('upload_avatar', [
                'as'   => 'cp.settings.upload_avatar',
                'uses' => 'CP\StoreController@upload_avatar',
            ])->middleware(OptimizeImages::class);
            Route::post('delete_avatar', [
                'as'   => 'cp.settings.delete_avatar',
                'uses' => 'CP\StoreController@delete_avatar',
            ]);
            Route::post('uploadFavicon', [
                'as'   => 'cp.settings.uploadFavicon',
                'uses' => 'CP\StoreController@uploadFavicon',
            ])->middleware(OptimizeImages::class);
            Route::post('deleteFavicon', [
                'as'   => 'cp.settings.deleteFavicon',
                'uses' => 'CP\StoreController@deleteFavicon',
            ]);
            Route::post('save_pattern', [
                'as'   => 'cp.settings.pattern',
                'uses' => 'CP\StoreController@savePattern',
            ]);
            Route::post('disconnect_knawat', [
                'as'   => 'cp.settings.disconnect_knawat',
                'uses' => 'CP\StoreController@disconnectKnawat',
            ]);
            Route::post('save_store_tax_setting', [
                'as'             => 'cp.settings.save_store_tax_setting',
                'middleware' => 'feature:tax',
                'uses'       => 'CP\StoreController@saveStoreTaxSetting',
            ]);
            Route::post('upload_tax_image', [
                'as'             => 'cp.settings.upload_tax_image',
                'middleware' => 'feature:tax',
                'uses'       => 'CP\StoreController@uploadTaxImage',
            ]);
            Route::post('update_store_tax_setting', [
                'as'             => 'cp.settings.update_store_tax_setting',
                'middleware' => 'feature:tax',
                'uses'       => 'CP\StoreController@UpdateStoreTaxSetting',
            ]);
            Route::post('show_agreement', [
                'as'   => 'cp.settings.show_agreement',
                'uses' => 'CP\StoreController@showAgreement',
            ])->middleware('escapeEmoji');
            Route::post('save_agreement', [
                'as'   => 'cp.settings.save_agreement',
                'uses' => 'CP\StoreController@saveAgreement',
            ])->middleware('check-plan');

            Route::post('show_order_instructions', [
                'as' => 'cp.settings.ShowOrderInstructions',
                'uses' => 'CP\StoreController@ShowOrderInstructions',
            ]);

            Route::post('save_order_instructions', [
                'as' => 'cp.settings.saveOrderInstructions',
                'uses' => 'CP\StoreController@saveOrderInstructions',
            ]);
        });

        Route::group(['prefix' => 'feedback'], function () {
            Route::get('/', [
                'as'   => 'cp.feedback',
                'uses' => 'CP\FeedbackController@list',
            ]);
            Route::post('change_status', [
                'as'   => 'cp.feedback.status',
                'uses' => 'CP\FeedbackController@updateStatus',
            ]);
            Route::post('reply', [
                'as'   => 'cp.feedback.reply',
                'uses' => 'CP\FeedbackController@reply',
            ]);

            Route::get('{type}', [
                'as'   => 'cp.feedback.ftech',
                'uses' => 'CP\FetchFeedbackController',
            ]);

            Route::delete('delete/{id}', [
                'as'   => 'cp.feedback.delete',
                'uses' => 'CP\FeedbackController@delete',
            ]);
        });
        // Route::group(['prefix' => 'testimonial'], function () {
        //     Route::get('/', [
        //         'as'   => 'cp.testimonial',
        //         'uses' => 'CP\TestimonialController@list',
        //     ]);
        //     Route::post('change_status', [
        //         'as'   => 'cp.testimonial.status',
        //         'uses' => 'CP\TestimonialController@updateStatus',
        //     ]);
        // });
        Route::group(['prefix' => 'campaign'], function () {
            Route::get('/', [
                'as'   => 'cp.campaign',
                'uses' => 'CP\CampaignController@index',
            ]);
            Route::get('stats/{id}', [
                'as'   => 'cp.campaign.stats',
                'uses' => 'CP\CampaignController@stats',
            ]);
            Route::get('create', [
                'as'   => 'cp.campaign.create',
                'uses' => 'CP\CampaignController@create',
            ]);
            Route::get('filters/{id}', [
                'as'   => 'cp.campaign.filters',
                'uses' => 'CP\CampaignController@showFilters',
            ]);
            Route::post('filters/{id}', [
                'as'   => 'cp.campaign.filters.store',
                'uses' => 'CP\CampaignController@storeFilters',
            ]);
            Route::post('filters_delete/{id}', [
                'as'   => 'cp.campaign.filters.delete',
                'uses' => 'CP\CampaignController@deleteFilters',
            ]);
            Route::post('filters_condition/{id}', [
                'as'   => 'cp.campaign.filters.condition',
                'uses' => 'CP\CampaignController@updateFiltersCondition',
            ]);
            Route::get('edit/{id}', [
                'as'   => 'cp.campaign.edit',
                'uses' => 'CP\CampaignController@edit',
            ]);
            Route::post('edit/{id}', [
                'as'   => 'cp.campaign.update',
                'uses' => 'CP\CampaignController@update',
            ]);
            Route::get('customers/{id}', [
                'as'   => 'cp.campaign.customers',
                'uses' => 'CP\CampaignController@showCustomers',
            ]);
            Route::post('save/{id}', [
                'as'   => 'cp.campaign.save',
                'uses' => 'CP\CampaignController@save',
            ]);
        });
        Route::group(['prefix' => 'polls'], function () {
            Route::post('show', [
                'as'   => 'cp.polls.show',
                'uses' => 'CP\PollsController@show',
            ]);
            Route::post('save', [
                'as'   => 'cp.polls.save',
                'uses' => 'CP\PollsController@save',
            ]);
        });
        Route::group(['prefix' => 'message'], function () {
            Route::post('send', [
                'as'   => 'cp.message.send',
                'uses' => 'CP\MessageController@send',
            ]);
            Route::get('get_sms_balance', [
                'as'   => 'cp.message.get_sms_balance',
                'uses' => 'CP\MessageController@getSMSBalance',
            ]);
        });
        Route::group([
            'prefix' => 'app',
            'middleware' => [\App\Http\Middleware\AppMiddleware::class],
        ], function () {
            Route::get('stream', [
                'as'   => 'app.stream',
                'uses' => 'CP\AppController@getFeeds',
            ]);

            Route::get('more', [
                'as'   => 'cp.app.more',
                'uses' => 'CP\AppController@more',
            ]);
            Route::get('alerts', [
                'as'   => 'cp.app.alerts',
                'uses' => 'CP\AppController@alerts',
            ]);
            Route::get('contact-us', [
                'as'   => 'cp.app.contact-us',
                'uses' => 'CP\AppController@contactUs',
            ]);
            Route::post('updatealerts', [
                'as'   => 'cp.updatealerts',
                'uses' => 'CP\AppController@postAlerts',
            ]);
            Route::post('updateactivity', [
                'as'   => 'cp.updateactivity',
                'uses' => 'CP\AppController@updateActivity',
            ]);
            Route::get('store/location/{location?}/{city?}/{country?}', [
                'as'   => 'cp.app.store.location',
                'uses' => 'CP\AppController@storeLocation',
            ]);
            Route::get('suggestions', [
                'as'   => 'cp.app.suggestions',
                'uses' => 'CP\AppController@suggestions',
            ]);
            Route::group(['prefix' => 'salla_store_tax'], function () {
                Route::get('store_tax/{id?}', [
                    'as'   => 'cp.store_taxes.create',
                    'uses' => 'CP\StoreTaxController@create',
                ]);
                Route::post('store_tax', [
                    'as'   => 'cp.store_taxes.store',
                    'uses' => 'CP\StoreTaxController@store',
                ]);
                Route::post('disable_store_tax', [
                    'as'   => 'cp.store_taxes.disable',
                    'uses' => 'CP\StoreTaxController@disable',
                ]);
                Route::post('delete_store_tax/{id}', [
                    'as'   => 'cp.store_taxes.delete',
                    'uses' => 'CP\StoreTaxController@delete',
                ]);
            });
        });
    });
    Route::get('/url/{id}/{type}', [
        'as'   => 'cp.short_url',
        'uses' => 'CP\ShortUrlController',
    ]);
    Route::group(['prefix' => 'pages'], function () {
        Route::get('/', [
            'as'   => 'cp.pages',
            'uses' => 'CP\PageController@index',
        ]);
        Route::post('load_pages', [
            'as'   => 'cp.load_pages',
            'uses' => 'CP\PageController@loadPages',
        ]);
        Route::post('get_pages', [
            'as'   => 'cp.get_pages',
            'uses' => 'CP\PageController@getPages',
        ]);
        Route::get('page/{id?}', [
            'as'   => 'cp.pages.create',
            'uses' => 'CP\PageController@create',
        ]);
        Route::post('store', [
            'as'   => 'cp.pages.store',
            'uses' => 'CP\PageController@store',
        ])->middleware('escapeEmoji');
        Route::post('sort', [
            'as'   => 'cp.pages.sort',
            'uses' => 'CP\PageController@sort',
        ]);

        Route::post('disable_page', [
            'as'   => 'cp.pages.disable',
            'uses' => 'CP\PageController@disable',
        ]);
        Route::post('delete_page/{id}', [
            'as'   => 'cp.pages.delete',
            'uses' => 'CP\PageController@delete',
        ]);
    });
    Route::group(['prefix' => 'salla_store', 'middleware' => [\App\Http\Middleware\DisableSallaStoreOnIOS::class]], function () {
    });
    Route::group(['prefix' => 'salla_store_tax'], function () {
        Route::post('load_taxs', [
            'as'   => 'cp.store_taxes.load_taxs',
            'uses' => 'CP\StoreTaxController@loadTaxs',
        ]);
    });

    Route::group(['prefix' => 'help_center'], function () {
        Route::get('/', [
            'as'   => 'cp.help_center.temp',
            'uses' => 'CP\HelpCenterController@index',
        ]);

        Route::get('{id}', [
            'as'   => 'cp.help_center.show_article',
            'uses' => 'CP\HelpCenterController@showArticle',
        ]);
    });
    Route::get('cities/{country}', [
        'as'   => 'cp.cities',
        'uses' => 'CP\CityController',
    ])->middleware('escapeEmoji');
    Route::any('selectize', [
        'as'   => 'selectize.entity',
        'uses' => 'SelectizeController',
    ]);
    Route::group(['prefix' => 'activities'], function () {
        Route::get('/', 'CP\MainActivityController')->name('store_activities.main');
        Route::get('{activity}', 'CP\SubActivityController')->name('store_activities.sub');
        Route::post('/', 'CP\StoreActivityController')->name('store_activities.store');
    });
});

Route::any('quick/access', [
    'as'   => 'quick.access.entity',
    'uses' => 'CP\QuickAccessController',
]);

Route::any('robots.txt', 'CP\RobotsTxtController')->name('cp.robots');
Route::any('new-dashboard', 'CP\NewDashboardController')->name('cp.new-dashboard');

Route::webhooks('admin/v2/marketplace/webhook', 'apps-marketplace');
Route::webhooks('admin/v2/marketplace/themes/webhook', 'themes-marketplace');
Route::webhooks('admin/v2/marketplace/stores/webhook', 'marketplace-store');
Route::webhooks('admin/v2/marketplace/expert/webhook', 'expert-marketplace');
Route::webhooks('admin/v2/marketplace/expert/zetca', 'zatca-invoices');
Route::webhooks('admin/v2/marketplace/expert/credit-note/zetca', 'zatca-credit-note-invoices');

// ¯\_(ツ)_/¯  - needed to safely generate rating url ..
Route::get('check_rating', [
    'as'   => 'check_feedback',
    'uses' => 'CheckFeedbackController',
]);

// ¯\_(ツ)_/¯  - needed to safely generate order rating url ..
Route::get('rating/{order_id}', [
    'as'   => 'feedback.order',
    'uses' => 'CheckFeedbackController',
]);

Route::put('darkModeEnabled', [
    'as'   => 'general.darkModeEnabled',
    'uses' => 'DarkModeToggleController',
]);

Route::get('orders', [
    'as'   => 'store.myorders',
    'uses' => 'OrderController@customerOrders',
]);
