<?php

namespace Modules\Settings\Rules;

use App\Models\StoreBankAccount;
use Illuminate\Contracts\Validation\Rule;
use Salla\Core\Entities\StoreDocument;
use Modules\Settings\Traits\LockedHelper;
use Salla\Core\Enum\StoreDocumentsStatus;

class StepVaildationRule implements Rule
{
    use LockedHelper;

    protected $message;
    /**
     * @var \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|StoreDocument
     */
    protected $document;

    protected $entity;

    public function __construct(protected $currentStep)
    {
        $this->document = StoreDocument::withoutGlobalScope('approved')->firstOrCreate([
            'store_id' => store()->getId(),
            'status'   => StoreDocumentsStatus::PENDING
        ]);

        $this->entity = $this->document->entity ?? $this->document->store->entity;

    }

    public function passes($attribute, $value)
    {
        if (in_array($this->currentStep, ['charity', 'commercial', 'vat'])) {
            // we have to complete owner & manager
            if (!$this->document->owner_identity_is_verified) {
                return $this->fail('يرجى اكمال خطوة مالك المتجر اولاً');
            }

            if (!$this->document->manager_identity_is_verified) {
                return $this->fail('يرجى اكمال خطوة مدير المتجر اولاً');
            }
        }

        if (in_array($this->currentStep, ['manager', 'iban']) && !$this->document->owner_identity_is_verified) {
            return $this->fail('يرجى اكمال خطوة مالك المتجر اولاً');
        }

        if ($this->currentStep == 'iban') {

            // check if the entity is personal and the request has key 'account_type' and the type is commercial and the document->freelance_number is null then return fail
            if (
                $this->entity === 'person' &&
                request()->get('account_type') === StoreBankAccount::BUSINESS &&
                !$this->document->freelance_number
            ) {
                return $this->fail('يرجى اكمال خطوة العمل الحر اولاً');
            }

            // commercial if the entity is not personal
            if (
                in_array($this->entity, ['company', 'firm']) &&
                (!$this->document->commercial_register_is_verified ||
                    (is_null($this->document->commercial_register_unified_number) && is_null($this->document->commercial_number))
                )
            ) {
                return $this->fail('يرجى اكمال خطوة السجل التجاري اولاً');
            }
        }

        if ($this->currentStep === 'vat') {
            // commercial if the entity is not personal
            if (in_array(store()->entity, ['company', 'firm']) && !$this->document->commercial_register_is_verified) {
                return $this->fail('يرجى اكمال خطوة السجل التجاري اولاً');
            }
            if (store()->entity === 'charity' && !$this->document->charity_certificate_image) {
                return $this->fail('يرجى اكمال خطوة شهادة الجمعية اولاً');
            }
        }

        if ($this->currentStep === 'manager' && $this->managerIdentityIsLocked($this->document, true)) {
            return $this->fail('يمكنك معاودة المحاولة بعد 24 ساعة');
        }

        if ($this->currentStep === 'personal' && ($this->managerIdentityIsLocked($this->document, true) || $this->ownerIdentityIsLocked($this->document, true))) {
            return $this->fail('يمكنك معاودة المحاولة بعد 24 ساعة');
        }

        if ($this->currentStep === 'owner' && $this->ownerIdentityIsLocked($this->document, true)) {
            return $this->fail('يمكنك معاودة المحاولة بعد 24 ساعة');
        }

        if ($this->currentStep === 'commercial' && $this->commercialRegisterIsLocked($this->document, true)) {
            return $this->fail('يمكنك معاودة المحاولة بعد 24 ساعة');
        }

        if ($this->currentStep === 'vat' && $this->taxNumberIsLocked($this->document, true)) {
            return $this->fail('يمكنك معاودة المحاولة بعد 24 ساعة');
        }


        return true;
    }

    protected function fail(string $message)
    {
        $this->message = $message;

        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->message;
    }
}