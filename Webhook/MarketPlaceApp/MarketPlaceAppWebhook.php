<?php

namespace Modules\MarketPlace\Webhook\MarketPlaceApp;

use Modules\MarketPlace\Actions\MarketplaceApp\Webhooks\InstalledAppExtraAction;
use Modules\MarketPlace\Jobs\App\ExpireFreeAppSubscriptionsJob;
use Spatie\WebhookClient\Models\WebhookCall;
// use Spatie\WebhookClient\ProcessWebhookJob; // v.2.7
use Spatie\WebhookClient\Jobs\ProcessWebhookJob; // v.3.0
use Modules\MarketPlace\Enum\MarketPlaceAppRequestType;
use Modules\MarketPlace\Enum\MarketplaceAppWebhookEvent;
use Modules\MarketPlace\Exceptions\AppMarketplaceException;
use Modules\MarketPlace\Entities\SallaProductMarketplaceApp;
use Modules\MarketPlace\Enum\MarketPlaceAppAccessRequestType;
use Modules\MarketPlace\Actions\MarketplaceApp\UnInstallAppAction;
use Modules\MarketPlace\Actions\MarketplaceApp\SyncCategoryFromPartnerAction;
use Modules\MarketPlace\Actions\MarketplaceApp\Portal\SyncMarketPlaceAppProductAction;
use Modules\MarketPlace\Actions\MarketplaceApp\Privilage\MarketPlaceAppAcceptRequestAction;
use Modules\MarketPlace\Actions\MarketplaceApp\Privilage\MarketPlaceAppAccessRequestAction;
use Modules\MarketPlace\Actions\MarketplaceApp\Privilage\MarketPlaceAppRejectRequestAction;
use Modules\MarketPlace\Actions\MarketplaceApp\Privilage\MarketPlaceAppDeleteRequestAction;


class MarketPlaceAppWebhook extends ProcessWebhookJob
{
    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    public function __construct(WebhookCall $webhookCall)
    {
        parent::__construct($webhookCall);
        $this->onQueue('webhook');
    }

    public function handle()
    {
        $payload = $this->webhookCall->payload;

        if($payload['event_name'] == MarketplaceAppWebhookEvent::CATEGORY_SYNC) {
            SyncCategoryFromPartnerAction::make()->run();
            return;
        }

        $app = SallaProductMarketplaceApp::where('app_id', $payload['app'])->firstOrFail();

        if (!empty($payload['store'])) {
            store()->loadFromId(optimus()->decode($payload['store']));
        }

        /** @var integer|null $planId */
        $planId = !empty($payload['plan']['id']) ? (int)$payload['plan']['id'] : null;

        /** @var array|null $note */
        $note = !empty($payload['update_note']) ? $payload['update_note'] : null;

        $id = !empty($payload['id']) ? $payload['id'] : null;
        $requestType = MarketPlaceAppRequestType::getMapRequestType(!empty($payload['type']) ? $payload['type'] : null);

        switch ($payload['event_name']) {
            case MarketplaceAppWebhookEvent::APP_INSTALL:
                MarketPlaceAppAcceptRequestAction::make([
                    'app'                => $app,
                    'request_type'       => $requestType,
                    'partner_request_id' => $id,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_UNINSTALL:

                //if delete app from partner
                UnInstallAppAction::make([
                    'app'            => $app,
                    'from_dashboard' => false,
                    'is_expired'     => false,
                    'is_removed'     => true,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_ACCESS_REQUEST:
                MarketPlaceAppAccessRequestAction::make([
                    'app'                => $app,
                    'type'               => MarketPlaceAppAccessRequestType::CREATE,
                    'plan_id'            => $planId,
                    'note'               => $note,
                    'request_type'       => $requestType,
                    'partner_request_id' => $id,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_ACCEPT_REQUEST:

                //no need because partner send APP_INSTALL event
//                MarketPlaceAppAcceptRequestAction::make([
//                    'app' => $app,
//                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_REMOVE_REQUEST:
                MarketPlaceAppDeleteRequestAction::make([
                    'app'                => $app,
                    'request_type'       => $requestType,
                    'partner_request_id' => $id,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_REJECT_REQUEST:
                MarketPlaceAppRejectRequestAction::make([
                    'app'                => $app,
                    'partner_request_id' => $id,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_ACCESS_REQUEST_UPDATE_PRIVILAGE:
                MarketPlaceAppAccessRequestAction::make([
                    'app'                => $app,
                    'type'               => MarketPlaceAppAccessRequestType::UPDATE,
                    'plan_id'            => $planId,
                    'note'               => $note,
                    'request_type'       => $requestType,
                    'partner_request_id' => $id,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_PUBLISHED:
                try {
                    SyncMarketPlaceAppProductAction::make([
                        'app'             => $app,
                        'save_categories' => true,
                    ])->run();

                    //dispatch(new ExpireFreeAppSubscriptionsJob($app));
                } catch (AppMarketplaceException $e) {
                    //
                } catch (\Exception $ex) {
                    sentryCaptureException($ex);
                }

                break;
            case MarketplaceAppWebhookEvent::APP_MARKETPLACE_UNINSTALL:
                \Modules\MarketPlace\Actions\MarketplaceApp\Webhooks\UnInstallAppAction::make([
                    'app'           => $app,
                    'installed_app' => !empty($payload['data']['installed_app']) ? $payload['data']['installed_app'] : null,
                ])->run();

                break;
            case MarketplaceAppWebhookEvent::APP_MARKETPLACE_APP_INSTALLED:
                InstalledAppExtraAction::make([
                    'app'           => $app,
                    'installed_app' => !empty($payload['data']['installed_app']) ? $payload['data']['installed_app'] : null,
                ])->run();

                break;
        }
    }
}
