version: "3"

services:
  web:
    build:
      context: .
      args:
        - GITHUB_TOKEN=${GITHUB_TOKEN:-docker}
        - PACKAGES_USERNAME=${PACKAGES_USERNAME:-docker}
        - PACKAGES_PASSWORD=${PACKAGES_PASSWORD:-docker}
    #        links:
    #            - database
    #            - redis
    expose:
      - 80
      - 443
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - .:/app
    environment:
      GITHUB_TOKEN: "xxxx"
      PACKAGES_USERNAME: "salla"
      PACKAGES_PASSWORD: "xxxxx"
      APP_USER_LOCAL: "true"
      START_QUEUE: "false"
      RUN_LARAVEL_CRON: "false"
      START_HORIZON: "false"
      DEVELOPMENT_MODE: "false"
#    database:
#        image: quay.io/continuouspipe/mysql8.0:stable
#        environment:
#            MYSQL_ROOT_PASSWORD: salla
#            MYSQL_DATABASE: salla
#            MYSQL_USER: salla
#            MYSQL_PASSWORD: salla
#        expose:
#            - 3306
#        ports:
#            - "3306:3306"

#    redis:
#        image: quay.io/continuouspipe/redis3:stable
#        expose:
#            - 6380
#        ports:
#            - "6380:6379"
