/node_modules
/public/hot
/public/storage
/storage/*.key
/storage/translations
/vendor
/.idea
/.vscode
/.vagrant
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.env
.env.testing
.phpunit.result.cache
/nbproject
.php_cs.cache
.php-cs-fixer.cache
shipping0.blade.php
composer.lock
public/vendor/salla
_ide_helper.php
/Modules/CodeEditor
public/web.config
public/vendor/marketing/.gitkeep
public/vendor/marketing/images/dic-header.png
public/vendor/marketing/js/app.js
public/vendor/marketing/sass/app.scss
Modules/Settings
Modules/Marketing
Modules/ExternalService
Modules/DashboardApi
public/vendor/emails/app.css
public/vendor/emails/app.css.map
public/vendor/emails/app.js
public/vendor/emails/app.js.map
public/vendor/emails/img/.gitkeep
public/vendor/emails/img/arr-down.png
public/vendor/emails/img/arr.png
public/vendor/emails/img/goal.png
public/vendor/emails/img/logo.png
public/vendor/emails/img/orders.png
public/vendor/emails/img/sales.png
public/vendor/emails/img/social-i.png
public/vendor/emails/img/social-t.png
public/vendor/emails/img/social-y.png
public/vendor/emails/img/users.png
public/vendor/emails/img/archive/.gitkeep
public/vendor/payment/moyasar_fees_alrajhi.pdf
!storage/responses/*
/public/modules/
public/vendor/complaint/*
public/vendor/store-resthook/*
public/vendor/marketing/*
public/vendor/store_app
public/vendor/external-services
public/vendor/special-offer-module/*
public/vendor/changelog/*
package-lock.json
storage/sitemaps/*
.styleci.yml
public/vendor/orders
# google-client-credentials.json
.DS_Store
storage/clockwork/*

# web mix files
public/css
public/css/uploader.css
public/css/product.css
public/cp/assets/css/re-custom.css
public/cp/assets/css/invoices.css
public/cp/assets/css/dashboardapi.css
public/cp/assets/css/product-sorting.css
public/cp/assets/css/semantic.css
public/cp/assets/js/product-sorting.js
public/cp/assets/js/categories.js
public/cp/assets/js/plugins/intl-tel-input/noneQIntlTell.js
public/cp/assets/js/semantic.js
public/js/dashboard.js
public/js/product.js
public/js/filepond.js
storage/clockwork/*
.github_prs
.pr_body
storage/laravels.conf
storage/laravels.pid
storage/laravels-timer-process.pid
storage/laravels-custom-processes.pid
storage/clockwork/*
storage/csrs/*
public/cp/assets/js/product_quantity_log.js
public/cp/assets/js/bulk_editor.js
public/cp/assets/js/products-view.js
public/cp/assets/js/quantity-management.js
public/cp/assets/js/quantity_editor.js
public/cp/assets/js/single_product_view.js
public/cp/assets/js/entity.js
public/cp/assets/js/entity.js.LICENSE.txt
public/cp/assets/js/shipping-role.js
public/cp/assets/js/shipping-role.js.LICENSE.txt
public/vendor/customer

public/css/docs-api.css
public/js/payments/apple-pay.js
public/js/settings.js
public/css/email.css
public/cp/assets/css/register.css
public/js/docs-api.js
public/cp/assets/js/product-sorting.js.LICENSE.txt

/public/cp/assets/js/bulk_editor.js
/public/cp/assets/js/bulk_editor.js.LICENSE.txt
/public/cp/assets/js/categories.js
/public/cp/assets/js/categories.js.LICENSE.txt
/public/cp/assets/js/entity.js
/public/cp/assets/js/entity.js.LICENSE.txt
/public/cp/assets/js/plugins/intl-tel-input/noneQIntlTell.js
/public/cp/assets/js/product_quantity_log.js
/public/cp/assets/js/product_quantity_log.js.LICENSE.txt
/public/cp/assets/js/product-sorting.js
/public/cp/assets/js/product-sorting.js.LICENSE.txt
/public/cp/assets/js/products-view.js
/public/cp/assets/js/products-view.js.LICENSE.txt
/public/cp/assets/js/quantity_editor.js
/public/cp/assets/js/quantity_editor.js.LICENSE.txt
/public/cp/assets/js/quantity-management.js
/public/cp/assets/js/quantity-management.js.LICENSE.txt
/public/cp/assets/js/shipping-role.js
/public/cp/assets/js/shipping-role.js.LICENSE.txt
/public/cp/assets/js/single_product_view.js
/public/cp/assets/js/single_product_view.js.LICENSE.txt
/public/js/dashboard.js
/public/js/filepond.js
/public/js/filepond.js.LICENSE.txt
/public/vendor/settings

public/cp/assets/js/bulk_pricing.js
public/cp/assets/css/re-custom.css
public/cp/assets/css/register.css
public/cp/assets/js/entity.js
public/cp/assets/js/entity.js.LICENSE.txt
public/cp/assets/js/inventory-view.js
public/cp/assets/js/inventory-view.js.LICENSE.txt
public/cp/assets/js/payments_rules.js
public/cp/assets/js/payments_rules.js.LICENSE.txt
public/js/docs-api.js
public/js/payments/apple-pay.js
public/js/product.js
public/js/settings.js
public/cp/assets/js/bulk_pricing.js.LICENSE.txt
public/vendor/dist
public/vendor/whatsapp-business/*
public/vendor/whatsapp-business/js/whatsapp_chat.js
public/vendor/external-services/services.js
public/vendor/form-builder
public/vendor/store-menu-module
public/vendor/audit
public/vendor/theme-dashboard
resources/views/vendor/email-templates
public/vendor/product-module
public/vendor/wallet
public/vendor/whatsapp-business
public/vendor/marketplace
public/vendor/domain
public/vendor/marketing
public/vendor/quick-donation
test-log.html

public/**
!public/mix-manifest.json

reports/*
coverage/*