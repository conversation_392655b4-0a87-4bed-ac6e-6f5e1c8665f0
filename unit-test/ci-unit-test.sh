REDIS="redis-unit"
REDIS_PORT="6378"
MYSQL="mysql-unit"
MYSQL_TAG="latest"
REDIS="redis-unit"
ELASTICSEARCH="elasticsearch-unit"
MYSQL_PORT="3307"
ELASTICSEARCH_PORT="9201"
REDIS_PORT="6378"


function run_test {
  echo "▶️ Start ruuning test for $2 ⏳"
  printf "======================= ▶️ Start ruuning test for $2. =======================\n" >> test-log.txt
  touch failed-temp.txt
  echo "Running $1";
  output=$($1)
  echo "1" > result.txt
  echo "${output}" | while read line || [[ -n $line ]];
  do
    echo "$line" >> test-log.txt
    if echo "$line" | grep "FAIL"; then
      echo "- $line" | sed 's/\x1B\[[0-9;]\{1,\}[A-Za-z]//g' >> failed-temp.txt
      echo "0" > result.txt
    fi
    if echo "$line" | grep "⨯"; then
      printf "> %s\n" "$line"  | sed 's/\x1B\[[0-9;]\{1,\}[A-Za-z]//g' >> failed-temp.txt
      echo "0" > result.txt
    fi
  done

  result=$(cat result.txt)
  if [ "$result" -eq 1 ]; then 
   echo "> - Test ( $2 ) " >> successed-logs.txt
   else
    printf "\n**Test ( %s ) failed ❌**\n%s" "$2" "$(cat failed-temp.txt)" > failed-temp.txt
    log=$(cat failed-temp.txt)
    printf "%s\n\n" "$log" >> failed-logs.txt
    rm failed-temp.txt 
  fi
}

echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin

if ! docker ps --format '{{.Names}}' | grep -Eq "^${MYSQL}\$"  ; then
  docker run --name $MYSQL -e MYSQL_ALLOW_EMPTY_PASSWORD=yes -d -p $MYSQL_PORT:3306 -d sallasa/unit-test-db:$MYSQL_TAG
fi

if ! docker ps --format '{{.Names}}' | grep -Eq "^${REDIS}\$" ; then
  docker run --name $REDIS -p $REDIS_PORT:6379 -d redis
fi

if ! docker ps --format '{{.Names}}' | grep -Eq "^${ELASTICSEARCH}\$" ; then
  docker run --name $ELASTICSEARCH -p $ELASTICSEARCH_PORT:9200 -d elasticsearch:8.5.3
fi 


TIMEOUT=0
MAX_TIMEOUT=120

while docker exec $MYSQL sh -c "mysqladmin -uroott --password= ping --silent" &> /dev/null; do  
  echo "Waiting for database connection...⌛️"
  TIMEOUT=$((TIMEOUT + 1))
  sleep 1

  if [ $TIMEOUT -ge $MAX_TIMEOUT ]; then 
    echo "Connection to DB failed...❌"
    exit 1 
  fi 
done

# Additional sleep if the connection is established before the timeout
sleep 6
echo "MySQL Connection is ready ✅"

php artisan --env=testing migrate --force

touch failed-logs.txt
touch successed-logs.txt
touch test-log.txt
touch report.txt
printf "## Failed Tests ❌\n" > failed-logs.txt
printf "## Successful Tests ✅\n\n" > successed-logs.txt

var=$1
echo "Testing $var"
if [[ $var == "dashboard-coverage-team" ]]; then
  run_test "php artisan test -c ./phpunit-dashboard.xml --testsuite dashboard  --coverage-clover=clover.xml -d memory_limit=-1" "$var"
  echo "running -s report -r "clover.xml" --api-token "$CODACY_API_TOKEN" --organization-provider gh --username SallaApp --project-name "Dashboard" --language php --commit-uuid "$COMMIT_UUID""
  curl -Ls "https://coverage.codacy.com/get.sh" | bash -s report -r "clover.xml" --api-token "$CODACY_API_TOKEN" --organization-provider gh --username SallaApp --project-name "Dashboard" --language php --commit-uuid "$COMMIT_UUID"
else
  run_test "php -d memory_limit=-1 artisan test --testsuite=$var --stop-on-failure" "$var"
fi


result=$(cat successed-logs.txt | wc -l | awk '{print $1}' )
if  [ "$result" -gt 1 ]; then
  cat successed-logs.txt >> report.txt
fi;


result=$(cat failed-logs.txt | wc -l | awk '{print $1}' )
if  [ "$result" -gt 1 ]; then
  cat failed-logs.txt >> report.txt
fi;

docker ps 
