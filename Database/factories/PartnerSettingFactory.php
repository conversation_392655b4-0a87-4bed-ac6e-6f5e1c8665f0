<?php

namespace Modules\MarketPlace\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\MarketPlace\Entities\Partner\PartnerSetting;

class PartnerSettingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PartnerSetting::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'bag'        => 'general',
            'key'        => fake()->text(10),
            'value'      => fake()->randomDigit(),
        ];
    }

}