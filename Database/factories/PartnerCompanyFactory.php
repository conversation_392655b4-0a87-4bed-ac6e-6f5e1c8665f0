<?php


namespace Modules\MarketPlace\Database\factories;


use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\MarketPlace\Entities\Partner\PartnerCompany>
 */
class PartnerCompanyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PartnerCompany::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name'          => fake()->name(),
            'email'         => fake()->email(),
        ];
    }

    public function withAppsCommissionPercentage($value)
    {
        return $this->state(function () use ($value) {
            return [
                'commissions' => [
                    'apps' => [
                        'percentage' => $value,
                    ],
                ],
            ];
        });
    }

}
