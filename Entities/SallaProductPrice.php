<?php

namespace Modules\MarketPlace\Entities;

use App\Scopes\ActiveScope;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use CommerceGuys\Tax\TaxableInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Models\SallaProductPriceFreeProduct;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\MarketPlace\Actions\Subscription\GetNextRenewalDateAction;
use Modules\MarketPlace\Enum\SallaProductPriceType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\MarketPlace\Traits\Product\HasPriceDiscounts;
use Modules\MarketPlace\Scopes\SallaProductPriceCheckStoreScope;
use Modules\MarketPlace\Entities\Traits\SallaProductPriceHtmlTrait;
use Modules\MarketPlace\Database\factories\SallaProductPriceFactory;
use Modules\MarketPlace\Entities\Traits\HasTranslatedSallaProductPrice;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Salla\Core\Entities\Store;

/**
 * Class SallaProductPrice.
 *
 * @property int id
 * @property int product_id
 * @property float price
 * @property float|null sale_price
 * @property string|null sale_end
 * @property string currency
 * @property int|null period
 * @property string|null subtitle
 * @property string|null gifts
 * @property int|bool|null
 * @property Carbon|null created_at
 * @property Carbon|null updated_at
 * @property string|null deleted_at
 * @property array|null price_by_plan
 * @property int|bool taxable
 * @property int|bool tax_included
 * @property int|string uuid
 * @property int|float first_time_cost
 * @property string|null plan_name_ar
 * @property string|null plan_name_en
 *
 * @property float|null  $balance        Plan balance.
 * @property string|null $type           Plan type.
 * @property string|null $on_demand_type on demand type.
 *
 * @property \Modules\MarketPlace\Entities\SallaProductPriceDiscount|\Illuminate\Database\Eloquent\Relations\HasMany|null priceDiscounts    Relation NULL
 * @property integer|null $store_id
 */
class SallaProductPrice extends Model implements TaxableInterface
{
    use SoftDeletes,
        HasFactory,
        HasPriceDiscounts,
        SallaProductPriceHtmlTrait,
        HasTranslatedSallaProductPrice;

    protected $table = 'salla_products_pricing';

    protected $guarded = ['id'];

    protected $casts = [
        'price_by_plan' => 'array',
        'deleted_at'    => 'string',
        'balance'       => 'float',
    ];

    // protected $appends = [
    //     'percent_of_price',
    // ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope(new SallaProductPriceCheckStoreScope());
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return SallaProductPriceFactory::new();
    }

    public function product()
    {
        return $this->belongsTo(SallaProducts::class, 'product_id');
    }

    public function priceDiscounts()
    {
        return $this->hasMany(SallaProductPriceDiscount::class, 'product_price_id')
            ->latest('id');
    }

    public function freeProducts()
    {
        return $this->hasMany(SallaProductPriceFreeProduct::class, 'product_price_id');
    }

    public function freeProductsOnlyTrashed()
    {
        return $this->hasMany(SallaProductPriceFreeProduct::class, 'product_price_id')->onlyTrashed();
    }

    public function priceFeatures()
    {
        return $this->hasMany(SallaProductPriceFeature::class, 'product_price_id');
    }

    public function bundledProducts()
    {
        return $this->hasMany(SallaPlanBundleProductItems::class, 'product_price_id', 'id');
    }

    public function customPriceStores()
    {
        return $this->belongsToMany(Store::class, 'salla_product_pricing_stores', 'salla_products_pricing_id', 'store_id')
            ->withTimestamps()
            ->as('salla_product_pricing_stores')
            ->wherePivotNull('deleted_at');
    }

    /**
     * @param $value
     */
    public function getSalePriceAttribute($value): ?float
    {
        return $this->price_by_plan[app('store')->plan] ?? $value;
    }

    public function getHumanCurrency()
    {
        return trans('global.currency.' . $this->currency);
    }

    /**
     * @return \Salla\Money\Data\MoneyTaxable
     */
    public function getPrice()
    {
        return $this->rememberAttribute('price', function () {
            if(mobileAgent()->isIos() && $this->ios_price !== null ){
                return money($this->ios_price, null, $this, withTaxIncluded: false);
            }

            if( $this->product->type === SallaProductType::PLAN
                && $this->product->type_value === SallaProductPlanType::SPECIAL
                && store()->latestPlanInfo
                && store()->latestPlanInfo->type_value ===  SallaProductPlanType::SPECIAL
                && store()->latestPlanInfo->product_price_id === $this->id  // refer to the same product price that has initial different amount as an offer from salla
            )
            {
                $this->price = max(store()->latestPlanInfo->amount,$this->product->price);
            }

            if ($this->hasSalePrice()) {
                return $this->getSalePrice();
            }

            // TODO :: support it
            // if (! empty($this->percent_of_price)) {
            //     $price = round((($price * $this->percent_of_price) / 100), 2);
            // }

            return money($this->price, null, $this, withTaxIncluded: false);
        });
    }

        /**
     * @return \Salla\Money\Data\MoneyTaxable
     */
    public function getFirstTimeCost()
    {
        return $this->rememberAttribute('first_time_cost', function () {
            return money($this->first_time_cost, null, $this, withTaxIncluded: false);
        });
    }

    /**
     * Get the sale price , this sale price may be a normal discount OR Conditioned discount
     * @return mixed
     */
    public function getSalePrice()
    {
        if (isNasa() || !$this->product->isSupportDiscountedPrice()) {
            return money($this->sale_price, null, $this, withTaxIncluded: false);
        }

        // but when Is plan and Not from Nasa(Dashboard , Portal in future)
        //  then check if has Conditioned Discount
        /**@var  SallaProductPriceDiscount $discountedPrice */
        if ($discountedPrice = $this->getConditionedPriceDiscount()) {
            return money($discountedPrice->discounted_price, null, $this, withTaxIncluded: false);
        }

        //when from Nasa Or when no compatible discounted prices
        return money($this->sale_price, null, $this, withTaxIncluded: false);
    }


    public function getPriceWithCurrency()
    {
        return $this->getPrice()->getTaxedPriceAsFloat() . ' ' . $this->getHumanCurrency();
    }

    public function hasSalePrice(): bool
    {
        if (isNasa()) {
            return $this->hasNormalSalePrice();
        }

        // when from dashboard may be the product price not have normal sale_price, But has Conditions for this discount
        // So in nasa the sale_price differ on the conditioned discount price
        // but in dashboard the sale_price may be sale_price Or Conditioned discounted price
        return $this->hasNormalSalePrice() || $this->hasConditionedPriceDiscount();
    }

    public function hasNormalSalePrice(): bool
    {
        return $this->price > 0 && (
            ($this->sale_price || !empty($this->price_by_plan[app('store')->plan])) &&
            (
                empty($this->sale_end) ||
                $this->sale_end >= Carbon::now()->toDateString() ||
                $this->sale_end === '0000-00-00'
            )
        );
    }

    /**
     * @return float|int|null
     */
    public function percentSalePrice()
    {
        if($this->price <= 0 || !$this->hasNormalSalePrice()) {
            return null;
        }

        return round(100 * $this->sale_price / $this->price, 2);
    }

    /**
     * @return \Salla\Money\Data\MoneyTaxable
     */
    public function getPriceUntilNextRenewal()
    {
        if (
            $this->product->type !== SallaProductType::ADDON ||
            $this->product->type_value == SallaProductAddonType::APPS
        ) {
            return $this->getPrice();
        }

        $next_renewal_date = GetNextRenewalDateAction::make()->run();

        // when we don't have a date that mean the client doesn't has a plan in store & cart
        // or when the expire plan date grater than the current add-on
        // so lets use main price
        $addon_expire_at = Carbon::now()->addMonths($this->period);
        if (!$next_renewal_date || $next_renewal_date->greaterThanOrEqualTo($addon_expire_at)) {
            return $this->getPrice();
        }

        $total_days = Carbon::now()->diffInDays($addon_expire_at);
        $remain_days = Carbon::now()->diffInDays($next_renewal_date, false);

        if ($remain_days > 0 && $total_days > $remain_days) {
            $percent_of_price = round((100 * $remain_days) / $total_days);

            return money(round((($this->getPrice()->getBasePriceAsFloat() * $percent_of_price) / 100), 2), null, $this, withTaxIncluded: false);
        }

        return $this->getPrice();
    }

    /**
     * @return \Salla\Money\Contacts\MoneyTaxable|\Salla\Money\Data\MoneyTaxable
     */
    public function getFinalPrice(array $features_by_slug = [])
    {
        $product_price = $this->getPriceUntilNextRenewal();
        if (
            !$this->product->isApp() ||
            empty($features_by_slug)
        ) {
            return $product_price;
        }

        $price_features = $this->getPriceFeaturesBySlug(
            array_keys($features_by_slug)
        );

        $price = 0;
        foreach ($price_features as $feature) {
            if (!empty($features_by_slug[$feature->slug])) {
                $price += $features_by_slug[$feature->slug] * $feature->price; //quantity * price
            } else {
                $price += $feature->price;
            }
        }

        return $product_price->add(money($price, withTaxIncluded: false));
    }

    /**
     * todo:: refactor
     * $except_old: for transfer store we need to return theme again
     */
    public function getFreeProducts($as_array = false, $except_old = false)
    {
        $result = [];
        $result_as_array = collect();
        if ($this->freeProducts->count() > 0) {
            foreach ($this->freeProducts->groupBy('type') as $type => $list_free_product) {
                $all_product = $list_free_product->filter(function ($value, $key) {
                    return $value->value == null;
                })->first();

                $list_product_id = [];
                if (empty($all_product)) {
                    $list_product_id = $list_free_product->pluck('value')->toArray();
                }

                if (SallaProductType::IsSENDER_NAME('sender_name') && store()->sms_sender_name) {
                    continue;
                }

                //prevent development stores to get free domain as a gift!
                if(store()->isTemplate() && $type === SallaProductType::DOMAIN) {
                   continue;
                }

                $temp = SallaProducts::where('type', $type)
                    ->with('productPrices')
                    ->when($list_product_id, fn(Builder $q) => $q->whereIn('salla_products.id', $list_product_id))
                    ->when(!SallaProductType::IsTHEME($type), fn(Builder $q) => $q->withoutGlobalScope(ActiveScope::class))
                    ->when(SallaProductType::IsTHEME($type), function ($query) use ($except_old) {
                        $query
                            //->whereNull('salla_products.partner_id')
                            //todo:: support it using flag
                            ->whereIn('salla_products.id', store()->getSetting('giftable_salla_products', []))
                            ->when(empty($except_old), function ($query) {
                                return $query->whereNotExists(function ($query) {
                                    //todo:: enhance this query
                                    $query->select(DB::raw(1))
                                        ->from('salla_subscriptions')
                                        ->whereRaw('salla_subscriptions.product_id = salla_products.id')
                                        ->where('salla_subscriptions.store_id', store()->getId())
                                        ->where('salla_subscriptions.type', 'theme')
                                        ->where('salla_subscriptions.status', 'active');
                                });
                            });
                    })
                    ->get();
                $result[$type] = $temp->map(function (SallaProducts $item, $key) use ($list_free_product, $all_product) {
                    $item->free_id = $all_product->id ?? null;
                    $item->free_product_price_id = null;
                    if (empty($all_product)) {
                        $free_product = $list_free_product->where('value', $item->id)->first();
                        if (!empty($free_product)) {
                            $item->free_id = $free_product->id;
                            $item->free_product_price_id = $free_product->sub_product_price_id;
                        }
                    } else {
                        $product_price_object = $item->getProductPriceObject();
                        if (!empty($product_price_object)) {
                            $item->free_product_price_id = $product_price_object->id;
                        }
                    }

                    return $item;
                });

                $result_as_array = $result_as_array->merge($result[$type]);
            }
        }

        return $as_array ? $result_as_array : $result;
    }

    public function getSelectedFreeProduct($selected_product = [], $as_object = true)
    {
        //TODO:: needed
        //throw new \InvalidArgumentException('this should be removed');

        $result = [];
        if ($this->freeProducts->count() > 0) {
            foreach ($this->freeProducts->groupBy('type') as $type => $list_price_free_product) {
                $list_price_free_product_id = [];
                $temp = $list_price_free_product->filter(function ($value, $key) {
                    return $value->value == null;
                })->first();
                if (empty($temp)) {
                    $list_price_free_product_id = $list_price_free_product->pluck('value')->toArray();
                }

                if (
                    ($type == 'sender_name') &&
                    (!empty(auth()->user()->store->sms_sender_name))
                ) {
                    continue;
                }

                $list_free_product = SallaProducts::where('type', $type)
                    ->withoutGlobalScope(ActiveScope::class)
                    ->where(function ($query) use ($list_price_free_product_id, $selected_product) {
                        if (!empty($list_price_free_product_id)) {
                            $query->whereIn('salla_products.id', $list_price_free_product_id);
                        }
                        if (!empty($selected_product)) {
                            $query->whereIn('salla_products.id', $selected_product);
                        }
                    })
                    ->get();
                $result[$type] = $list_free_product;
                if (!$as_object) {
                    if ($type == 'sms') {
                        $result[$type] = $list_free_product->sum('type_value');
                    } else { //temp
                        $result[$type] = null;
                    }
                    //                    else if($type == 'theme') {
                    //                        $result[$type] = $list_free_product->pluck('id')->toArray();
                    //                    } else if($type == 'shipping') {
                    //                        $result[$type] = 0;
                    //                        foreach ($list_price_free_product as $val) {
                    //                            $result[$type] = $val->productPriceFree->price;
                    //                        }
                    //                    } else if($type == 'domain') {
                    //                        //TODO
                    //                    }
                }
            }
        }

        return $result;
    }

    public function getProductPrice($endDate = null): SallaProductPrice
    {
        throw new \InvalidArgumentException('this should be removed');
    }

    public function isPhysical()
    {
        return $this->taxable;
    }

    public function getInAppSubscriptionId()
    {
        //It must be pro for the identifier in apple store (pro is equal to team)
        $type_value = $this->product->type_value == SallaProductPlanType::TEAM ? "pro" : $this->product->type_value;
        // ex: plan_team_12
        if (is_null($this->period)) {
            return $this->product->type . '_' . $type_value;
        }

        if ($this->period == 1) {
            return $this->product->type . '_' . $type_value . '_salla_OneMonth';
        }

        return $this->product->type . '_' . $type_value . '_salla_' . $this->period;
    }

    /**
     * @return bool
     */
    public function hasFirstTimeCost()
    {
        return !empty($this->first_time_cost);
    }

    /**
     * @param $slugs
     * @return mixed
     */
    public function getPriceFeaturesBySlug($slugs)
    {
        return $this->priceFeatures->whereIn('slug', Arr::wrap($slugs));
    }

    public function hasConditionedPriceDiscount(): bool
    {
        return (bool) $this->getCompatiblePriceDiscount($this);
    }

    public function getConditionedPriceDiscount() : ?SallaProductPriceDiscount
    {
        return $this->getCompatiblePriceDiscount($this);
    }

    public function isAppCustomPlan(): bool
    {
        if (
            empty($this->store_id) ||
            !$this->product->isApp() ||
            $this->product->app->is_salla_app
        ) {
            return false;
        }

        return true;
    }

    /**
     * @return bool
     */
    public function checkAppCustomPlanOfCurrentStore()
    {
        if(!$this->isAppCustomPlan()) {
            return false;
        }

        return $this->store_id == store()->id;
    }

    public function isOnDemandPlan(): bool
    {
        if ($this->type === SallaProductPriceType::OnDemand) {
            return true;
        }

        return false;
    }

    /**
     * @return array|mixed
     */
    public function getPlanFeatures()
    {
        return !empty($this->plan_feature) ? json_decode($this->plan_feature, true) : [];
    }

    /**
     * @return string|null
     */
    public function getPlanName()
    {
        if(app()->getLocale() == 'en') {
            return $this->name ?: $this->plan_name_en ?: $this->subtitle;
        }

        return $this->name ?: $this->plan_name_ar ?: $this->subtitle;
    }

    /**
     * this function for special state for map price with new price for app application
     * @return mixed
     */
    public function getMapProductPrice($product = null, $return_same = true)
    {
        $product = $product ?: $this->product;
        return $product->productPrices->first(function ($val_price) {
            if (
                ($val_price->id == $this->id) ||
                (!empty($val_price->uuid) && $val_price->uuid == $this->uuid)
                //|| (!empty($val_price->slug) && $val_price->slug == $this->slug)
            ) {
                return true;
            }

            if (
                ($val_price->period == $this->period) &&
                ($val_price->price == $this->price) &&
                ($val_price->plan_name_ar == $this->plan_name_ar)
            ) {
                return true;
            }
        }) ?: ($return_same ? $this : null);
    }

    /**
     * @return mixed
     */
    public function hasFeatures()
    {
        return $this->priceFeatures->isNotEmpty();
    }

    public function utm()
    {
        return $this->morphOne(SallaUtmTracker::class, 'model');
    }
}
