<?php

namespace Modules\MarketPlace\Entities\Partner;


use Salla\Core\Entities\PartnerModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\MarketPlace\Entities\Partner\PartnerBank;
use Modules\Setting\Enums\VerificationIDRequestStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\MarketPlace\Database\factories\PartnerCompanyFactory;
use Modules\MarketPlace\Enum\SallaProductAddonType;
class PartnerCompany extends PartnerModel
{
    use HasFactory;
    use Notifiable;
    use PartnerLocationTrait;

    protected $table = 'companies';
    protected $casts = [
        'commissions' => 'json',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function users()
    {
        return $this->hasMany(PartnerUser::class, 'company_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function adminUser()
    {
        return $this->hasOne(PartnerUser::class, 'company_id')
            ->where('users.is_owner', true);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function banks()
    {
        return $this->hasMany(PartnerBank::class, 'company_id')
            ->latest('id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function defaultBank()
    {
        return $this->hasOne(PartnerBank::class, 'company_id')
            ->where('default', 1)
            ->orderBy('id', 'DESC');
    }

    public function approvedPersonalID(): HasOne
    {
        return $this->hasOne(PartnerVerificationIDRequest::class, 'company_id')->where('status', '=', 1)->latest();
    }

    /**
     * @return mixed
     */
    public function getBank()
    {
        $status_approved = 2;

        // or get the first user bank
        return $this->banks->firstWhere('status', $status_approved);
    }

    /**
     * @return mixed
     */
    public function getDisplayName()
    {
        if ($this->approvedPersonalID && $this->approvedPersonalID->full_name) {
            return $this->approvedPersonalID->full_name;
        }

        if(!empty($this->name)) {
            return $this->name;
        }

        return $this->adminUser->type == 'company' && !empty($this->adminUser->company_name) ?
            $this->adminUser->company_name :
            $this->adminUser->full_name;
    }

    /**
     * @return string
     */
    public function getMobile()
    {
        return $this->mobile_code . $this->mobile;
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return PartnerCompanyFactory::new();
    }

    /**
     * @return bool
     */
    public function hasTaxNumber()
    {
        return !empty($this->tax_number);
    }

    /**
     * @return mixed
     */
    public function getTaxNumber()
    {
        return $this->tax_number;
    }

    /**
     * @return mixed|null
     */
    public function getTaxCertificate()
    {
        $file = PartnerFile::query()->find($this->certificate_id);
        return !empty($file->url) ? config('marketplace.partners.s3partners') . $file->url : null;
    }

    /**
     * @return bool
     */
    public function canGeneratePayout()
    {
        if(config()->get('app.env') == 'testing') {
            return true;
        }

        return !($this->is_test || $this->frozen_payout) && $this->id_verified;
    }

    public function getCommissionPercentage($type = null, $type_value = null)
    {
        if ($type_value === SallaProductAddonType::APPS && !empty($this->commissions['apps']['percentage'])) {
            return $this->commissions['apps']['percentage'];
        }

        return getPartnerSallaFee($type);
    }
}