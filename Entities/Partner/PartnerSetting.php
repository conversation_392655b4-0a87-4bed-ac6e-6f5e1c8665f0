<?php


namespace Modules\MarketPlace\Entities\Partner;


use Salla\Core\Entities\PartnerModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\MarketPlace\Database\factories\PartnerSettingFactory;

class PartnerSetting extends PartnerModel
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'settings';

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return PartnerSettingFactory::new();
    }
}