<?php

namespace Modules\MarketPlace\Entities;

use Carbon\Carbon;
use Salla\Core\Enum\Plan;
use App\Scopes\ActiveScope;
use Illuminate\Support\Str;
use \Illuminate\Support\Facades\URL;
use CommerceGuys\Tax\TaxableInterface;
use Illuminate\Database\Eloquent\Model;
use Modules\MarketPlace\Enum\SallaCartSource;
use Modules\MarketPlace\Traits\HasOrderItem;
use Illuminate\Database\Eloquent\SoftDeletes;
use Salla\Core\Scopes\CurrentStoreScope;
use Salla\GoogleTags\Traits\IsTaggingProduct;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Traits\Product\Scopes;
use Salla\Core\Traits\Eloquent\ModelQueueable;
use Salla\GoogleTags\Contracts\TaggingProduct;
use Modules\StoreApp\Traits\HasMobileAppProduct;
use Modules\MarketPlace\Traits\Product\Relations;
use Salla\Core\Traits\Eloquent\RoutesWithOptimus;
use Modules\MarketPlace\Enum\SallaProductPlanType;
use Modules\MarketPlace\Traits\Product\Attributes;
use Modules\MarketPlace\Traits\Product\Conditions;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Html\SallaProductPriceHtml;
use Salla\Core\Traits\Eloquent\HasCachableAttributes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\MarketPlace\Entities\Traits\GetSallaIconTrait;
use Modules\MarketPlace\Entities\Traits\HasSallaProductTheme;
use Modules\MarketPlace\Database\factories\SallaProductsFactory;
use Modules\MarketPlace\Entities\Traits\HasTranslatedSallaProduct;
use Modules\MarketPlace\Entities\SallaPlanBundleProductItems;

/**
 * @property int id
 * @property null|string name
 * @property null|float price
 * @property null|float annual_price
 * @property string currency
 * @property null|string description
 * @property null|string|SallaProductType type
 * @property null|string type_value
 * @property null|float sale_price
 * @property null|float sale_end
 * @property null|float annual_sale_price
 * @property null|string annual_sale_end
 * @property bool|int require_shipping
 * @property int sort
 * @property null|Carbon created_at
 * @property null|Carbon updated_at
 * @property null|string|SallaProductActionMethod action_method
 * @property int level
 * @property null|string status
 * @property null|int partner_id
 * @property bool partner_public
 * @property bool partner_one_time
 * @property bool messaging
 * @property null|string avatar
 * @property null|string color
 * @property bool hide
 * @property null|string short_description
 * @property null|string icon
 * @property null|string form
 * @property null|string promotional
 * @property bool has_quantity
 * @property null|string quantity_label
 * @property null|int max_quantity
 * @property null|string medium_description
 * @property bool is_special
 * @property null|string special_text
 * @property bool taxable
 * @property bool tax_included
 * @property bool loyaltyable
 * @property null|int rating
 * @property null|string deleted_at
 * @property null|bool temporary_display
 */
class SallaProducts extends Model implements TaggingProduct, TaxableInterface
{
    use RoutesWithOptimus,
        Relations,
        Scopes,
        SallaProductPriceHtml,
        Conditions,
        Attributes,
        HasOrderItem,
        HasMobileAppProduct,
        IsTaggingProduct,
        HasFactory,
        ModelQueueable,
        HasTranslatedSallaProduct,
        HasSallaProductTheme,
        SoftDeletes,
        GetSallaIconTrait;

    protected $table = 'salla_products';

    protected $guarded = ['id'];

    protected $appends = [
        'short_description_view',
    ];

    protected $casts = [
        'has_quantity' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        if (config('app.name') === 'Dashboard' && !request()->runningInQueue()) {
            self::addGlobalScope(new ActiveScope());
        }
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return SallaProductsFactory::new();
    }

    public function getHumanCurrency()
    {
        return trans('global.currency.' . $this->currency);
    }

    public function getExtraProducts($product_price_id, $type = null, $as_array = false, $except_old = false)
    {
        // TODO :: refacting
        /** @var SallaProductPrice $product_price */
        $product_price = $this->productPrices->where('id', $product_price_id)->first();
        if (empty($product_price)) {
            return collect();
        }

        if (empty($type)) {
            return $product_price->getFreeProducts($as_array, $except_old);
        }

        $temp = $product_price->getFreeProducts(false, $except_old);
        return array_key_exists($type, $temp) ? $temp[$type] : collect();
    }

    public function getShortDescriptionViewAttribute()
    {
        return !empty($this->short_description) ? $this->short_description
            : Str::words(strip_tags($this->description), 30, ' ...');
    }

    public function findBytype($type)
    {
        if (\is_array($type)) {
            $query = $this->whereIn('type', $type);
        } else {
            $query = $this->where('type', $type);
        }

        return $query->pluck('id')->toArray();
    }

    public function findOne($id)
    {
        return $this->where('id', $id)->with('list_images')->first();
    }

    // public function getProductPriceByPeriod($period)
    // {
    //     $product_price = $this->productPrices->where('period', $period)->first();
    //
    //     return $this->getProductPrice($product_price->id);
    // }

    /**
     * @param null $product_price_id
     *
     * @return int
     * @deprecated
     */
    public function getProductPrice($product_price_id = null)
    {
        throw new \InvalidArgumentException('this should be removed');
    }

    /**
     * @param null $product_price_id
     *
     * @return \Modules\MarketPlace\Entities\SallaProductPrice
     */
    public function getProductPriceObject($product_price_id = null)
    {
        return $this->rememberAttribute('price_' . ($product_price_id ?: 'default'), function () use ($product_price_id
        ) {
            if (!empty($product_price_id)) {
                return $this->productPrices->where('id', $product_price_id)->first();
            }

            return $this->productPrices->where('default_price', '1')->first() ?: $this->productPrices()->first();
        });
    }

    public function getGoogleTagManagerVariant(): string
    {
        return implode(':', [$this->type, $this->type_value]);
    }

    public function getGoogleTagManagerCategories(): array
    {
        return [];
    }

    public function isPhysical()
    {
        return (bool)$this->taxable;
    }

    /**
     * @return \Salla\Money\Data\MoneyTaxable
     */
    public function getPrice()
    {
        return $this->getProductPriceObject()->getPrice();
    }

    /**
     * @param null $product_price_id
     *
     * @return \Salla\Money\Data\MoneyTaxable
     */
    public function getPriceFor($product_price_id = null)
    {
        return $this->getProductPriceObject($product_price_id)->getPrice();
    }

    /**
     * get product as gift not like normal product
     *
     * @return \Illuminate\Support\Collection|\Tightenco\Collect\Support\Collection
     */
    public function getGiftsAsProduct()
    {
        $products = collect();

        if (
            ($this->type != SallaProductType::PLAN) ||
            ($this->type_value != SallaProductPlanType::SPECIAL)
        ) {
            return $products;
        }

        //Mobile App
        $product = \Modules\MarketPlace\Entities\SallaProducts::where('type', SallaProductType::MOBILE_APPS)
            ->where('action_method', 'create_mobile_app_request')
            ->first();
        if (!empty($product) && !$product->canShowMobileApp()) {
            $products->push($product);
        }

        return $products;
    }

    /**
     * @return mixed
     */
    public function hasSettingFeatures()
    {
        return $this->settingFeatures->isNotEmpty();
    }

    /**
     * Determines whether the current product is an App.
     */
    public function isApp(): bool
    {
        return ($this->type == SallaProductType::ADDON) &&
            ($this->type_value == SallaProductAddonType::APPS);
    }

    /**
     * Determines whether the current product is an Store.
     */
    public function isStore(): bool
    {
        return ($this->type == SallaProductType::STORE);
    }

    /**
     * Determines whether the product price is changed.
     */
    public function isAppPriceChanged(SallaProductPrice $new_price, $price = null): bool
    {
        if (!$this->isApp()) {
            return false;
        }

        /**
         * @var SallaSubscriptions $subscription
         */
        $subscription = $this->app->getActiveSubscription();

        return !empty($subscription) && $subscription->isPriceChanged($new_price, $price);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|SallaProductPrice[]
     */
    public function getPrices()
    {
        return $this->productPrices->whereNull('store_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|SallaProductPrice[]
     */
    public function getAllNotFreePrices()
    {
        return $this->productPrices->filter(function (SallaProductPrice $price) {
            return $price->price > 0;
        });
    }

    /**
     * @return bool
     */
    public function hasMultiPrice()
    {
        return $this->getPrices()->count() > 1;
    }

    /**
     * Determines whether the current product is an Expert Service.
     */
    public function isExpertService(): bool
    {
        return ($this->type == SallaProductType::ADDON) &&
            (in_array($this->type_value, [SallaProductAddonType::SERVICE, SallaProductAddonType::INFLUENCER_SERVICE]));
    }

    /**
     * @return bool
     */
    public function isMerhcantServiceType(): bool
    {
        return $this->isExpertService() &&
            ($this->type_value == SallaProductAddonType::SERVICE);
    }

    /**
     * @return bool
     */
    public function isInfluencerServiceType(): bool
    {
        return $this->isExpertService() &&
            ($this->type_value == SallaProductAddonType::INFLUENCER_SERVICE);
    }

    /**
     * @return bool
     */
    public function canAddToStoreAsFree()
    {
        //we need to add order item for template store to konw themes that exist
        if (store()->isTemplate()) {
            return false;
        }

        //install free themes without checkout process
        //enable merchant from Special plan only => to install (Salla) paid themes Freely
        if (
            $this->type == SallaProductType::THEME && ((round($this->latestProductPrice->price, 0, 2) === 0.0) ||
            (store()->plan === Plan::SPECIAL && ($this->theme->developer ?? 'سلة') == 'سلة'))
        ) {
            return true;
        }

        return false;
    }

    /**
     * @return string|void
     */
    public function getExternalId($externalDecode = true)
    {
        if ($this->isApp()) {
            return $externalDecode ? optimusPortal()->decode($this->app->app_id) : $this->app->app_id;
        }

        if ($this->isExpertService()) {
            return $externalDecode ? optimusPortal()->decode($this->expretService->service_id) :
                $this->expretService->service_id;
        }

        if ($this->isTheme()) {
            return $externalDecode ? optimusPortal()->decode($this->theme->marketplace_theme_id) :
                $this->theme->marketplace_theme_id;
        }

        return optimus()->encode($this->id);
    }

    public function isFree()
    {
        return round($this->latestProductPrice->price, 0, 2) === 0.0;
    }

    public function planBundledProductItems()
    {
        return $this->hasMany(SallaPlanBundleProductItems::class, 'product_id', 'id');
    }
}
