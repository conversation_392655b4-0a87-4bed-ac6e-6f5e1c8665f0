<?php

namespace Modules\MarketPlace\Entities;

use App\Models\SallaSubscriptions;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Modules\Domain\Entities\DomainMoveRequest;
use Modules\MarketPlace\Database\factories\SallaOrderItemsFactory;
use Modules\MarketPlace\Entities\Expert\SallaProductExpertService;
use Modules\MarketPlace\Entities\Partner\PartnerCompany;
use Modules\MarketPlace\Entities\Partner\PartnerSetting;
use Modules\MarketPlace\Entities\Partner\PartnerUser;
use Modules\MarketPlace\Enum\PartnerOrderTypeEnum;
use Modules\MarketPlace\Enum\SallaOrderItemStatus;
use Modules\MarketPlace\Enum\SallaOrderStatus;
use Modules\MarketPlace\Enum\SallaProductAddonType;
use Modules\MarketPlace\Enum\SallaProductMarketplaceAppType;
use Modules\MarketPlace\Enum\SallaProductType;
use Modules\MarketPlace\Enum\SallaSubscriptionStatus;
use Modules\MarketPlace\Traits\HasUtmTracker;
use Modules\MarketPlace\Traits\Order\OrderItemCalculation;
use Modules\Payment\Enums\PaymentPartnerRelatedType;
use Modules\ThemeCustomization\Entities\Theme;
use Salla\Core\Base\BaseModel;
use Salla\Core\Traits\Eloquent\SallaStore\DiscountHelper;
use Salla\GoogleTags\Contracts\TaggingOrderItem;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class SallaOrderItems
 *
 * @package Modules\MarketPlace\Entities
 * @method \Illuminate\Database\Eloquent\Builder checkIfPlanPending()
 */
class SallaOrderItems extends BaseModel implements TaggingOrderItem
{
    use OrderItemCalculation,
        DiscountHelper,
        HasFactory,
        SoftDeletes,
        HasUtmTracker;

    protected $table = 'salla_order_items';

    protected $guarded = ['id'];

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return SallaOrderItemsFactory::new();
    }

    public function product()
    {
        return $this->belongsTo(SallaProducts::class, 'product_id', 'id');
    }

    public function sallaProductPrice()
    {
        return $this->productPrice();
    }

    public function productPrice()
    {
        return $this->belongsTo(SallaProductPrice::class, 'product_price_id');
    }

    /**
     *  Get productPrice relation with trashed data.
     *
     * @return mixed
     */
    public function productPriceWithTrashed()
    {
        return $this->productPrice()->withTrashed();
    }

    public function productWithTrashed()
    {
        return $this->product()->withTrashed();
    }

    public function sallaOrder()
    {
        return $this->order();
    }

    public function order()
    {
        return $this->belongsTo(SallaOrders::class, 'order_id');
    }

    public function freeProducts()
    {
        return $this->hasMany(SallaOrderItemFreeProduct::class, 'order_item_id');
    }

    public function domainMoveRequest()
    {
        return $this->hasOne(DomainMoveRequest::class, 'order_item_id');
    }

    public function creditNote()
    {
        return $this->hasOne(SallaOrderCreditNote::class, 'order_item_id');
    }

    public function activeSubscription()
    {
        return $this->hasOne(SallaSubscriptions::class, 'order_item_id')
            ->where('status', SallaSubscriptionStatus::ACTIVE);
    }

    public function subscription()
    {
        return $this->hasOne(SallaSubscriptions::class, 'order_item_id')->withTrashed();
    }

    public function priceFeatures()
    {
        return $this->hasMany(SallaOrderItemFeature::class, 'order_item_id');
    }

    public function partnerUser()
    {
        return $this->belongsTo(PartnerUser::class, 'developer_user_id');
    }

    public function partnerCompany()
    {
        return $this->belongsTo(PartnerCompany::class, 'developer_user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function app()
    {
        return $this->hasOne(SallaProductMarketplaceApp::class, 'product_id', 'product_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function theme()
    {
        return $this->hasOne(Theme::class, 'product_id', 'product_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function expretService()
    {
        return $this->hasOne(SallaProductExpertService::class, 'product_id', 'product_id');
    }

    public function hasCreditNoteOrInvalidSubsciption()
    {
        return $this->creditNote || !$this->activeSubscription;
    }

    public function getExtraProducts()
    {
        return $this->freeProducts->pluck('product');
    }

    public function getSubTotal($order, $exclude_sale_products = 0)
    {
        $amount = $order->amount;

        if ($exclude_sale_products == 1) {
            $now = Carbon::now();
            $cart_offer = $this->select(\DB::raw('SUM(salla_order_items.product_price*salla_order_items.quantity) as amount'))
                ->join('salla_products', function ($join) {
                    $join->on('salla_products.id', '=', 'salla_order_items.product_id');
                })
                ->where(function ($q) use ($now) {
                    $q->where('sale_end', '>=', $now->toDateString());
                    $q->orWhere('sale_end', '=', '0000-00-00');
                    $q->orWhereNull('sale_end');
                })
                ->where('sale_price', '>', 0)
                ->where('salla_order_items.order_id', $order->id)
                ->groupBy('salla_order_items.order_id')
                ->first();

            if ($cart_offer) {
                $amount_by_offers = $cart_offer->amount;
                if ($amount > 0) {
                    $amount -= $amount_by_offers;
                }
            }
        }

        return $amount;
    }

    public function getHumanCurrency()
    {
        return trans('global.currency.' . $this->currency);
    }

    public function scopeCheckIfPlanPending($query)
    {
        return $query->whereHas('sallaOrder', function ($query) {
            $query->where('status', 'pending_review')->where('store_id', app('store')->getId());
        })->whereHas('product', function ($query) {
            $query->where('type', SallaProductType::PLAN);
        });
    }

    public function scopeByProductId($query, $product_id)
    {
        return $query->where('product_id', $product_id);
    }

    /**
     * Get Order Items of Theme and app
     * @param $query
     * @param bool $without_partner_payment
     * @return mixed
     */
    public function scopePartnerItemsHasNotPayments(
        $query,
        $type = null,
        $type_value = null,
        $without_partner_payment = true
    ) {
        $type = $type ?: SallaProductType::ADDON;
        $type_value = $type_value ?: SallaProductAddonType::APPS;

        return $query->join('salla_orders', 'salla_order_items.order_id', '=', 'salla_orders.id')
            ->join('salla_products_pricing', 'salla_order_items.product_price_id', '=', 'salla_products_pricing.id')
            ->join('salla_products', 'salla_order_items.product_id', '=', 'salla_products.id')
            ->whereNull('salla_order_items.payment_id')
            ->where('salla_orders.is_test', false)
            ->when($without_partner_payment, function ($subQuery) {
                return $subQuery->whereNull('salla_order_items.salla_partner_payment_id');
            })
            ->when($type == SallaProductType::ADDON, function ($query) use ($type_value) {
                return $query->when($type_value == SallaProductAddonType::APPS, function ($query) {
                    return $query->join('salla_product_marketplace_app', 'salla_product_marketplace_app.product_id', '=', 'salla_products.id')
                        ->where('salla_product_marketplace_app.is_salla_app', 0)
                        ->where('salla_product_marketplace_app.developer_user_id', '!=', 4)
                        ->where('salla_product_marketplace_app.type', SallaProductMarketplaceAppType::PUBLIC);
                })->when(in_array($type_value, SallaProductAddonType::getPartnerServiceTypes()), function ($query) {
                    return $query->join('salla_product_expert_services', 'salla_product_expert_services.product_id', '=', 'salla_products.id')
                        ->where('salla_product_expert_services.partner_company_id', '!=', 4)
                        ->where('salla_order_items.status', SallaOrderItemStatus::COMPLETED);
                })
                    ->where('salla_products.type', SallaProductType::ADDON)
                    ->where('salla_products.type_value', $type_value);
            })
            ->when($type == SallaProductType::THEME, function ($query) {
                return $query->join('themes', 'themes.product_id', '=', 'salla_products.id')
                    ->where('salla_products.type', SallaProductType::THEME)
                    ->where('themes.is_salla_theme', 0)
                    ->where('themes.developer_user_id', '!=', 4);
            })
            ->where('payment_method', '!=', 'free')
            ->whereNotNull('salla_order_items.developer_user_id')
            ->where('salla_orders.status', 'paid')
            ->where('salla_order_items.returned', false)
            ->where('salla_order_items.updated_at', '<', Carbon::now()->subDays(7)->startOfDay())
            ->whereNotIn('salla_order_items.product_price_id', store()->getSetting('payment::except_plans', []));
    }

    public function scopePartnerAffiliateItemsNotHavePayments($query)
    {
        return $query->join('partner_order_details', function ($join) {
            return $join->on('salla_order_items.id', '=', 'partner_order_details.order_item_id')
                ->whereIn(
                    'partner_order_details.order_type',
                    // we need to support both customer and merchant orders
                    [PartnerOrderTypeEnum::CUSTOMER_ORDER, PartnerOrderTypeEnum::MERCHANT_ORDER]
                );
        })
            ->join('salla_products', 'salla_order_items.product_id', '=', 'salla_products.id')
            ->join('salla_orders', 'salla_order_items.order_id', '=', 'salla_orders.id')
            ->where('salla_orders.status', SallaOrderStatus::PAID)
            ->whereNull('partner_order_details.salla_partner_payment_id') //payment not generate affiliater
            ->whereNull('partner_order_details.payment_id') //payment not generate affiliater
            ->whereNull('partner_order_details.deleted_at')
            ->where(function ($query) {
                return $query->whereNotNull('salla_order_items.payment_id') //payment generate to partner
                    ->orWhere('salla_products.type', SallaProductType::AFFILIATE)
                    ->orWhere(function ($query) {
                        //enable plan for affiliater after 7 days
                        return $query->where('salla_products.type', SallaProductType::PLAN)
                            ->where('partner_order_details.created_at', '<', Carbon::now()->subDays(7)->startOfDay());
                });
            })
            ->where('partner_order_details.created_at', '<', now()->toDateString());
    }

    /**
     * get order items that will set payout for them
     * @param $query
     * @return mixed
     */
    public function scopePartnerPaymentItems(
        $query,
        $developer_user_id,
        $product_id = null,
        $product_price_id = null,
        $related_type = null
    )
    {
        return $query->join('salla_orders', 'salla_order_items.order_id', '=', 'salla_orders.id')
            ->where('salla_orders.is_test', false)
            ->when(!empty($product_price_id), function ($query) use ($product_price_id) {
                return $query->where('salla_order_items.product_price_id', $product_price_id);
            })
            ->when(!empty($product_id), function ($query) use ($product_id) {
                return $query->where('salla_order_items.product_id', $product_id);
            })
            ->where('salla_orders.payment_method', '!=', 'free')
            ->where('salla_order_items.developer_user_id', $developer_user_id)
            ->where('salla_order_items.returned', false)
            ->where('salla_orders.status', 'paid')
            ->when(!empty($related_type) && PaymentPartnerRelatedType::isService($related_type), function ($query) {
                return $query->where('salla_order_items.status', SallaOrderItemStatus::COMPLETED);
            })
            ->where('salla_order_items.updated_at', '<', \Carbon\Carbon::now()->subDays(7)->startOfDay())
            ->whereNull('salla_order_items.salla_partner_payment_id')
            ->whereNotIn('salla_order_items.product_price_id', store()->getSetting('payment::except_plans', []));
    }

    public function getGoogleTagManagerData(): array
    {
        if (!$this->product) {
            return [];
        }

        return array_merge($this->product->getGoogleTagManagerData(), [
            'quantity' => $this->quantity,
            'currency' => $this->currency,
            'price'    => $this->total,
        ]);
    }

    public function getItemvalue()
    {
        return $this->domain ?? $this->value;
    }

    public function getSubTotalAfterDiscount()
    {
        return round($this->sub_total - $this->discount);
    }


    public function getSubTotalWithTax()
    {
        return $this->sub_total + round($this->tax_value / $this->quantity, 2);
    }

    public function hasQuantity(): bool
    {
        return !empty($this->product->has_quantity);
    }

    /**
     * @return float|int
     */
    public function getFess()
    {
        if (empty($this->developer_user_id)) {
            return 0;
        }
        return round((($this->total - $this->tax_value) * ($this->partnerCompany->getCommissionPercentage($this->product->type,$this->product->type_value) / 100)), 2);
    }

    /**
     * @return mixed
     */
    public function cartItem()
    {
        return $this->order->cart->items->firstWhere('product_id', $this->product_id);
    }

    public function bundle() {
        return $this->hasOne(SallaPlanBundleProductItems::class, 'bundle_id', 'id');
    }
}
