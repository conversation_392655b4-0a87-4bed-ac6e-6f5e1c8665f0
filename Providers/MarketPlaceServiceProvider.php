<?php

namespace Modules\MarketPlace\Providers;

use App\Models\User;
use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Modules\MarketPlace\{Components\Apps\Component,
    Contract\SallaCartService,
    Entities\SallaCartItem,
    Entities\SallaOrders,
    Entities\ThemePreviewRequest,
    Features\EnableSpecialPlanPurchaseFeature,
    Features\InstallAppMethodV2Feature,
    Features\InstallNewHydraMethodFeature,
    Features\MerchantAppStoreLoginFeature,
    Features\PartnerAffiliateFeature,
    Features\PartnerMerchantAffiliateFeature,
    Features\PartnersChatFeature,
    Features\PosAppFeature,
    Features\ReadyStoreThemePurchaseFeature,
    Features\SallaPortalMarketplaceFeature,
    Features\VendorAppFeature,
    Features\SallaGPTAppFeature,
    Features\AIProductFeature,
    Features\SallaChatFeature,
    Features\PrivateAffiliateLinkFeature,
    Features\GeneralManagerTokenFeature,
    Features\NewAppsFeature,
    Features\ApplePayMethodFeature,
    Policies\SallaOrderPolicy,
    Services\SallaAppManager,
    Widgets\Dashboard\MenuWidget};
use Modules\MarketPlace\Console\{AddPriceToStoreTemplateCommand,
    AutoPaidInvoiceCommand,
    CheckAppChangePriceCommand,
    CheckSallaSubscriptionPendingCommand,
    CheckSubscriptionsCommand,
    CleanSallaMarketplaceRequestCommand,
    CreateInvoiceCommand,
    CreateSubscriptionReminderCommand,
    CreditCardExpirationAlertCommand,
    DeleteBulkSallaInvoiceCommand,
    DeleteCreditCardCommand,
    DeleteSallaInvoiceCommand,
    FixAppSubscriptionDatesCommand,
    FixAutoRenewAppSubscription,
    FixDeleteSubscriptionsAppCommand,
    FixFreeAppInsallSubscription,
    FixScripts\FixAffiliateOrdersCommand,
    FixScripts\CreateAffiliateOrdersCommand,
    FixScripts\FixAppSubscriptionNotHaveInstalledAppCommand,
    FixScripts\FixPartnerReviewingCommand,
    FixScripts\FixSallaAppVersionCommand,
    FixScripts\FixShippingCompanyApiCommand,
    FixUninstallAppCommand,
    FixUpdateVersionAccessRequestAppCommand,
    GenerateMarketplaceAppInvoiceCommand,
    GenerateMarketplaceAppInvoiceItemOrderCommand,
    MarketplaceApp\ChangeStatusOfHoldingPaymentAppsCommand,
    MarketplaceApp\CheckCanReviewMarketplaceAppsCommand,
    MarketplaceApp\CheckNeedRenewSubscriptionCommand,
    MarketplaceApp\CleanMarketplaceAppRequestCommand,
    MarketplaceApp\ExpireFreeSubscriptionsOfAppsCommand,
    MarketplaceApp\NotifyAppsNeedRenewCommand,
    MarketplaceApp\ExpireAppCommand,
    MarketplaceApp\SetCanReviewMarketplaceAppsCommand,
    MarketplaceApp\InstallLocalAppCommand,
    MergeSallaPackagesCommand,
    MigrateOrderShipmentHistoryToOrderShipmentBranchCommand,
    MigrateSallaOrderReceiptImageDataCommand,
    MigrateSallaOrders,
    MigrateSallaProductCommand,
    MigrateSallaProductPricingCommand,
    MigrateSallaStoreData,
    NotifyMarketplaceAppInvoiceCommand,
    NotifyUnpaidSallaInvoiceCommand,
    SallaSubscriptionRevenueChangeStatusCommand,
    SendInstallmentPayPreReminderCommand,
    SetDeveloperUserIdSallaOrderCommand,
    AddAppToPartnerManuallyCommand,
    AddAffiliateProductCommand,
    CreditCardFinishAlertCommand,
    CreditNoteAppleOrdersCommand,
    SubscriptionRenewWebhookCommand,
    SyncMarketplaceAppCategoriesCommand,
    FixZeroCreditNoteCommand,
    ChangePlanWithoutSubscriptionCommand,
    Themes\CalculatePartnerThemeStatisticsCommand};
use Modules\MarketPlace\Entities\InAppSubscription;
use Modules\MarketPlace\Policies\InAppSubscriptionPolicy;
use Modules\Settings\Facades\SectionManager;
use Salla\FeatureRules\Facades\FeaturesManager;
use Illuminate\Database\Eloquent\Relations\Relation;

class MarketPlaceServiceProvider extends ServiceProvider
{
    protected $commands = [];

    protected $policies = [
        InAppSubscription::class => InAppSubscriptionPolicy::class,
        SallaOrders::class       => SallaOrderPolicy::class,
    ];

    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        if (isDashboard() && $this->app->runningInConsole()) {
            $this->commands([
                AutoPaidInvoiceCommand::class,
                CheckSubscriptionsCommand::class,
                CreateInvoiceCommand::class,
                CreateSubscriptionReminderCommand::class,
                CreditCardExpirationAlertCommand::class,
                DeleteCreditCardCommand::class,
                DeleteSallaInvoiceCommand::class,
                SallaSubscriptionRevenueChangeStatusCommand::class,
                NotifyUnpaidSallaInvoiceCommand::class,
                MigrateSallaStoreData::class,
                MigrateSallaOrders::class,
                FixUninstallAppCommand::class,
                CleanSallaMarketplaceRequestCommand::class,
                SetDeveloperUserIdSallaOrderCommand::class,
                FixAutoRenewAppSubscription::class,
                FixDeleteSubscriptionsAppCommand::class,
                AddAppToPartnerManuallyCommand::class,
                AddAffiliateProductCommand::class,
                FixFreeAppInsallSubscription::class,
                MigrateSallaProductPricingCommand::class,
                MigrateSallaProductCommand::class,
                CheckSallaSubscriptionPendingCommand::class,
                GenerateMarketplaceAppInvoiceCommand::class,
                GenerateMarketplaceAppInvoiceItemOrderCommand::class,
                NotifyMarketplaceAppInvoiceCommand::class,
                MergeSallaPackagesCommand::class,
                MigrateOrderShipmentHistoryToOrderShipmentBranchCommand::class,
                MigrateSallaOrderReceiptImageDataCommand::class,
                SyncMarketplaceAppCategoriesCommand::class,
                FixUpdateVersionAccessRequestAppCommand::class,
                SetCanReviewMarketplaceAppsCommand::class,
                InstallLocalAppCommand::class,
                CheckCanReviewMarketplaceAppsCommand::class,
                CheckNeedRenewSubscriptionCommand::class,
                FixAppSubscriptionDatesCommand::class,
                AddPriceToStoreTemplateCommand::class,
                CreditNoteAppleOrdersCommand::class,
                DeleteBulkSallaInvoiceCommand::class,
                FixZeroCreditNoteCommand::class,
                ChangePlanWithoutSubscriptionCommand::class,
                FixPartnerReviewingCommand::class,
                CleanMarketplaceAppRequestCommand::class,
                ChangeStatusOfHoldingPaymentAppsCommand::class,
                FixAffiliateOrdersCommand::class,
                CreateAffiliateOrdersCommand::class,
                CalculatePartnerThemeStatisticsCommand::class,
                NotifyAppsNeedRenewCommand::class,
                ExpireAppCommand::class,
                SubscriptionRenewWebhookCommand::class,
                FixSallaAppVersionCommand::class,
                SendInstallmentPayPreReminderCommand::class,
                FixAppSubscriptionNotHaveInstalledAppCommand::class,
                ExpireFreeSubscriptionsOfAppsCommand::class,
                FixShippingCompanyApiCommand::class,
            ]);
        } else if (config('app.name') === 'Nasa') {
            $this->commands([
                SetDeveloperUserIdSallaOrderCommand::class,
            ]);
        }

        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerAssets();
        $this->registerFactories();
        $this->registerPolicies();

        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');

        app('arrilot.widget-namespaces')->registerNamespace('marketplace', '\Modules\MarketPlace\Widgets');
        $this->registerCommands();

        FeaturesManager::addFeature(MerchantAppStoreLoginFeature::class);
        FeaturesManager::addFeature(EnableSpecialPlanPurchaseFeature::class);
        FeaturesManager::addFeature(SallaPortalMarketplaceFeature::class);
        FeaturesManager::addFeature(InstallNewHydraMethodFeature::class);
        FeaturesManager::addFeature(InstallAppMethodV2Feature::class);
        FeaturesManager::addFeature(VendorAppFeature::class);
        FeaturesManager::addFeature(SallaGPTAppFeature::class);
        FeaturesManager::addFeature(AIProductFeature::class);
        FeaturesManager::addFeature(SallaChatFeature::class);
        FeaturesManager::addFeature(PrivateAffiliateLinkFeature::class);
        FeaturesManager::addFeature(PosAppFeature::class);
        FeaturesManager::addFeature(PartnerAffiliateFeature::class);
        FeaturesManager::addFeature(PartnerMerchantAffiliateFeature::class);
        FeaturesManager::addFeature(GeneralManagerTokenFeature::class);
        FeaturesManager::addFeature(NewAppsFeature::class);
        FeaturesManager::addFeature(ApplePayMethodFeature::class);
        FeaturesManager::addFeature(PartnersChatFeature::class);
        FeaturesManager::addFeature(ReadyStoreThemePurchaseFeature::class);

        if(isDashboard()) {
            SectionManager::addComponent('advanced', 'apps', Component::class);
            $this->setMenuLinks();
        }

       Relation::morphMap([
           'theme_preview_request'     => ThemePreviewRequest::class,
           'salla_cart_item'           => SallaCartItem::class,
       ]);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/marketplace');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'marketplace');
        } else {
            $this->loadTranslationsFrom(__DIR__ . '/../Resources/lang', 'marketplace');
        }
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__ . '/../Config/config.php' => config_path('marketplace.php'),
            __DIR__ . '/../Config/in-app-purchase.php' => config_path('marketplace.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/config.php',
            'marketplace'
        );
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/in-app-purchase.php',
            'in-app-purchase'
       );
    }
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/marketplace');

        $sourcePath = __DIR__ . '/../Resources/views';

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $this->loadViewsFrom($sourcePath, 'marketplace');
    }

    public function registerFactories()
    {
        if (! app()->environment('production')) {
            app(Factory::class)->load(__DIR__ . '/../Database/factories');
        }
    }

    public function registerAssets()
    {
        if (!isDashboard() or !$this->app->runningInConsole()) {
            return;
        }

        $this->publishes([__DIR__ . '/../dist' => public_path('vendor/marketplace')], 'public');
    }

    public function registerCommands()
    {
        if ($this->app->runningInConsole()) {
            $this->commands($this->commands);
        }
    }

        /**
     * Register the application's policies.
     *
     * @return void
     */
    public function registerPolicies()
    {
        foreach ($this->policies as $key => $value) {
            Gate::policy($key, $value);
        }
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        $this->app->register(EventServiceProvider::class);

        $this->registerFactories();

        $this->app->scoped(SallaCartService::class, function ($app) {
            return $app->make(\Modules\MarketPlace\Services\SallaCartService::class);
        });

        $this->app->singleton('salla-app-manager', function ($app) {
            return $app->make(SallaAppManager::class);
        });

        if (config('app.name') === 'Nasa') {
            $this->app->register(NasaServiceProvider::class);
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [
            \Prettus\Repository\Providers\RepositoryServiceProvider::class,
        ];
    }

    protected function setMenuLinks()
    {
        app('hooker')->registerGlobal(function () {
            \Widget::group('dashboard::menu')->position(0)->addWidget(MenuWidget::class);
        });
    }
}
