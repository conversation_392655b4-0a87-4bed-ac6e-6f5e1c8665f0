<?php

namespace Modules\MarketPlace\Providers;

use App\Events\StoreUpdateInfoEvent;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use Modules\MarketPlace\Entities\SallaCategories;
use Modules\MarketPlace\Entities\SallaInvoiceItem;
use Modules\MarketPlace\Entities\SallaTag;
use Modules\MarketPlace\Listeners\StoreUpdateInfoListener;
use Modules\MarketPlace\Observers\SallaCategoryObserver;
use Modules\MarketPlace\Observers\InvoiceItemObserver;
use Modules\MarketPlace\Observers\SallaTagObserver;

class NasaServiceProvider extends ServiceProvider
{
    public function boot()
    {
        SallaTag::observe(SallaTagObserver::class);
        SallaCategories::observe(SallaCategoryObserver::class);
        SallaInvoiceItem::observe(InvoiceItemObserver::class);

        Event::listen(StoreUpdateInfoEvent::class, StoreUpdateInfoListener::class);

    }

    public function register()
    {

    }
}
