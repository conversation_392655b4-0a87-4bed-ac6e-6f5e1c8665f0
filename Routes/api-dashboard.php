<?php

use App\Http\Middleware\DeveloperPortalMiddleware;
use Illuminate\Support\Facades\Route;
use Modules\DashboardApi\Http\Middleware\ScopeMiddleware;
use Modules\DashboardApi\Http\Middleware\TokenableMiddleware;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\BulkSyncPublishedAppController;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\BankTransferController;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\StoreReferralsController;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\Subscriptions\PartnerPlanController;
use Modules\MarketPlace\Http\Middleware\PrepareMoneyMiddleware;
use Modules\ExternalService\Http\Middleware\AppAccessMiddleware;
use Modules\ExternalService\Http\Middleware\InstalledSallaAppMiddleware;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\InAppSubscriptionsController;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\Apps\CompleteInstallAppController;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\Product\SallaProductPlanController;
use Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\Product\SallaProductBulkRatingController;
use  Modules\MarketPlace\Http\Controllers\Dashboard\Api\V2\AvailableSallaCouponController;
/*
 * The Endpoint is for dashboard api v2
 */
Route::group([
    'namespace' => 'V2',
    'prefix' => 'v2/marketplace',
        'as' => 'api.v2.marketplace.',
    'middleware' => [
        DeveloperPortalMiddleware::class,
        \App\Http\Middleware\TranslatableMiddleware::class,
        \App\Http\Middleware\LanguageHeaderMiddleware::class,
    ],
], function () {
    Route::prefix('apps')->name('apps.')->group(function () {
        Route::post('', 'DeveloperSallaProductController@store')->middleware('escapeEmoji')->name('store');
        Route::put('{app_id}', 'DeveloperSallaProductController@update')->middleware('escapeEmoji')->name('update');
        Route::delete('{app_id}', 'DeveloperSallaProductController@delete')->name('delete');
        Route::post('{app_id}/payment-status', 'MarketPlaceAppPaymentStatusController')->name('payment-status');
        Route::post('{app_id}/install-request', 'MarketPlaceAppInstallRequestController')->name('install-request');
        Route::put('{app_id}/transfer', 'MarketplaceAppTransferController')->name('transfer');
        Route::post('bulk-sync', [BulkSyncPublishedAppController::class, '__invoke'])->name('bulk-sync-apps');

        Route::get('coupons/{coupon}/history', 'DeveloperSallaCouponHistoryController');
        Route::apiResource('coupons', 'DeveloperSallaCouponController')->only(['index', 'store', 'destroy', 'update']);

        Route::get('/{app_id}/delete-reports/statistics', 'MarketPlaceAppReports\DeleteStatisticsController')->name('apps.delete-statistics');
        Route::get('/{app_id}/delete-reports', 'MarketPlaceAppReports\DeleteReportsController')->name('apps.delete-reports');

        //this api if we want to bring subscription as rows
        Route::get('{developer_company_id}/subscriptions', 'Subscriptions\MarketPlaceSubscriptionsController')
            ->name('subscriptions');
        Route::get('{developer_company_id}/subscriptions/export', 'Subscriptions\MarketPlaceSubscriptionsExportController')
            ->name('subscriptions.export');
        //bring install app and subscriptions under it
        Route::get('{developer_company_id}/stores', 'Subscriptions\MarketPlaceStoresOfSubscriptionController')
            ->name('stores');

        Route::apiResource('recommendations', 'Recommendations\MarketPlaceAppRecommendationController')
            ->only(['index', 'show', 'store', 'update', 'destroy']);
    });

    Route::get('users/{id}', 'StoreController@user')->name('store.users.details');
    Route::post('check-store', 'StoreController@check')->name('apps.check-store');

    Route::get('invoices', 'MarketPlaceAppInvoices\MarketPlaceAppInvoiceController')->name('invoices');
    Route::get('invoices/unpaid', 'MarketPlaceAppInvoices\MarketPlaceAppInvoiceUnPaidController')->name('invoices.unpaid');
    Route::post('invoices/{app_invoice}', 'MarketPlaceAppInvoices\MarketPlaceAppInvoiceReceiptController')->name('invoices.update');

    Route::apiResource('coupons', 'DeveloperSallaCouponController')->only(['index', 'store', 'destroy', 'update']);
    Route::put('bulk-coupons', 'DeveloperBulkSallaCouponController@update')->name('bulk_coupons.update');

    Route::group(['namespace' => 'Themes', 'prefix' => 'themes', 'as' => 'themes.'], function () {
        Route::post('preview/{theme_id}', 'ThemePreviewController')->name('preview');
    });

    Route::get('/{developer_id}/statistics', 'MarketPlaceStatisticsController')->name('statistics');
});

Route::group([
    'namespace' => 'V2',
    'prefix' => 'v2/marketplace',
    'as' => 'api.v2.marketplace.',
    'middleware' => [
        TokenableMiddleware::class,
        \App\Http\Middleware\TranslatableMiddleware::class,
        \App\Http\Middleware\LanguageHeaderMiddleware::class,
    ],
], function () {
    Route::get('/salla-coupons/available', [AvailableSallaCouponController::class, 'index'])->name('salla-coupons.available');
});

Route::middleware([
    TokenableMiddleware::class,
    ScopeMiddleware::class,
    AppAccessMiddleware::class,
])
    ->namespace('V2')
    ->prefix('/v2/apps')
    ->name('api.v2.apps.')
    ->group(function () {
        Route::get('/{app}/subscriptions', 'MarketPlaceAppStoreSubscriptionController')->name('subscriptions.index');
        Route::post('install', 'MarketPlaceAppInstallController')->name('install');
        Route::post('update', 'MarketPlaceAppUpdateController')->name('update');
    });

Route::middleware([
    TokenableMiddleware::class,
    InstalledSallaAppMiddleware::class,
])
    ->namespace('V2')
    ->prefix('/v2/salla-apps')
    ->name('api.v2.salla_apps')
    ->group(function () {
        Route::post('{installedApp}/complete-install', 'Apps\CompleteInstallSallaAppController')->name('complete-install');
    });


Route::group([
    'namespace' => 'V2\Product',
    'prefix' => 'v2/marketplace/product',
    'as' => 'api.v2.marketplace.product.',
    'middleware' => [TokenableMiddleware::class],
], function () {
    Route::post('/bulk-rating', [SallaProductBulkRatingController::class, '__invoke'])->name('bulkRating');
    Route::post('/{product}/rating', 'SallaProductRatingController')->name('rating');
    Route::delete('feedback/{feedback_id}', 'SallaProductRatingController@delete')->name('rating.delete');
    Route::group(['middleware' => 'feature:ai-product'], function () {
        Route::get('ai-description', 'AIDescriptionInfoController')->name('ai-description');
        Route::post('ai-description/deduction', 'AIDescriptionDeductionController')->name('ai-description.deduction');
    });
});

require __DIR__ . '/themes/api-dash.php';

Route::middleware(TokenableMiddleware::class)
    ->namespace('V2')
    ->prefix('/v2')
    ->group(function () {
        Route::post('in-app-subscriptions', [InAppSubscriptionsController::class, 'store'])->name('api.v2.subscriptions.in_app');
        Route::get('subscription/cancel_subscription/{subscription}', 'Subscriptions\CancelSubscriptionReasonsController')->name('api.v2.subscription.cancellation_reasons');
        Route::get('marketplace/plans',SallaProductPlanController::class)
               ->middleware([PrepareMoneyMiddleware::class])
               ->name('api.v2.marketplace.plans');
        Route::get('marketplace/bank-transfer/status', BankTransferController::class)->name('api.v2.marketplace.bank-transfer.status');
        Route::get('marketplace/partner/plan/purchase', PartnerPlanController::class)->name('api.v2.marketplace.partner.plan.purchase.free');
        Route::post('/apps/complete-install', CompleteInstallAppController::class)
            ->name('api.v2.apps.complete-install');
        Route::get('marketplace/store/referrals', StoreReferralsController::class)->name('api.v2.marketplace.store.referrals');
    });

require __DIR__ . '/themes/api-dash.php';
require __DIR__ . '/api-partner-affiliate.php';

Route::group([
    'namespace'  => 'V2',
    'prefix'     => 'v2/marketplace/embedded-apps',
    'as'         => 'api.v2.marketplace.embedded-apps.',
    'middleware' => [TokenableMiddleware::class],
], function () {
    Route::get('/{slug}', 'EmbeddedAppController')->name('show');
});

require __DIR__ . '/cart/api-dash.php';

Route::middleware(\Salla\Core\Http\Middleware\VerifyTooljetRequest::class)
     ->namespace('V2')
     ->prefix('/v2/marketplace')
     ->name('api.v2.marketplace.')
     ->group(function () {

        Route::name('tooljet.')
            ->prefix('/tooljet')
            ->group(function () {

                Route::get('/theme-feedbacks', [
                    'as'   => 'theme_feedbacks.index',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@index'
                ]);

                Route::get('/theme-feedbacks/feedback-notification-employees', [
                    'as'   => 'theme_feedbacks.feedback_notification_employees',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@feedbackNotificationEmployees'
                ]);

                Route::post('/theme-feedbacks/save-admins-notify', [
                    'as'   => 'theme_feedbacks.save_admins_notify',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@saveAdminsNotify'
                ]);

                Route::post('/theme-feedbacks/{feedbackId}/update-status', [
                    'as'   => 'theme_feedbacks.update_status',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@updateStatus'
                ]);

                Route::post('/theme-feedbacks/{feedbackId}/feature-feedback', [
                    'as'   => 'theme_feedbacks.feature_feedback',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@featureFeedback'
                ]);

                Route::post('/theme-feedbacks/{feedbackId}/reply-feedback', [
                    'as'   => 'theme_feedbacks.reply_feedback',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@replyFeedback'
                ]);

                Route::delete('/theme-feedbacks/{feedbackId}/delete', [
                    'as'   => 'theme_feedbacks.delete',
                    'uses' => 'Tooljet\SallaThemeProductFeedbackController@delete'
                ]);
            });
});

Route::group([
    'prefix' => 'v2/marketplace/wallets',
    'namespace' => 'V2',
    'as' => 'api.v2.wallets.',
], function () {
    Route::group(['middleware' => ['check_user', 'auth.cp', TokenableMiddleware::class]], function () {
        Route::get('/', 'WalletController@index')->name('index');
        Route::get('/{slug}', 'WalletController@show')->name('show');
        Route::post('/transaction', 'WalletController@withdrawTransaction')->name('withdraw');

        Route::group(['prefix' => 'transactions', 'as' => 'transactions.'], function () {
            Route::get('/{transaction_id}', 'WalletController@getTransaction')->name('show');
        });
    });

    Route::group(['prefix' => 'transactions', 'as' => 'transactions.'], function () {
        Route::post('/refund', 'WalletController@refundTransaction')->name('refund');
    });
});

Route::group([
    'prefix' => 'v2/marketplace',
    'namespace' => 'V2',
    'as' => 'api.v2.wallets.line-items.',
    'middleware' => [
        'check_user',
        'auth.cp',
        TokenableMiddleware::class
    ],
], function () {
        Route::post('/line-items', 'LineItemsController')->name('insert');
});


Route::group([
    'prefix' => 'v2/marketplace/chat',
    'namespace' => 'V2',
    'as' => 'api.v2.chat.',
], function () {
        Route::post('/webhook', 'ChatWebhookController')->name('webhook');
});
